exports.ids = [21];
exports.modules = {

/***/ 1241:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1321);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("194819e0", content, true, context)
};

/***/ }),

/***/ 1319:
/***/ (function(module, exports, __webpack_require__) {

var map = {
	"./arrow-left.svg": 522,
	"./arrow-right.svg": 623,
	"./corner-resize-marker.svg": 513,
	"./cursor-student-down.svg": 596,
	"./cursor-student-right.svg": 597,
	"./cursor-teacher-down.svg": 598,
	"./cursor-teacher-right.svg": 599,
	"./student-arrow-2.svg": 602,
	"./student-arrow.svg": 603,
	"./student-beforeGrab.svg": 604,
	"./student-cursor-link.svg": 605,
	"./student-dragging.svg": 606,
	"./student-eraser.svg": 607,
	"./student-pencil.svg": 608,
	"./student-pointer.svg": 609,
	"./student-text-cursor.svg": 610,
	"./teacher-arrow-2.svg": 611,
	"./teacher-arrow.svg": 612,
	"./teacher-beforeGrab.svg": 613,
	"./teacher-cursor-link.svg": 614,
	"./teacher-dragging.svg": 615,
	"./teacher-eraser.svg": 616,
	"./teacher-pencil.svg": 617,
	"./teacher-pointer.svg": 618,
	"./teacher-text-cursor.svg": 619,
	"./volume-high.svg": 578
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = 1319;

/***/ }),

/***/ 1320:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_OtherCursor_vue_vue_type_style_index_0_id_b743e11e_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1241);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_OtherCursor_vue_vue_type_style_index_0_id_b743e11e_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_OtherCursor_vue_vue_type_style_index_0_id_b743e11e_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_OtherCursor_vue_vue_type_style_index_0_id_b743e11e_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_OtherCursor_vue_vue_type_style_index_0_id_b743e11e_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1321:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".other_cursor[data-v-b743e11e]{width:100%;position:absolute;z-index:99998;background-size:contain!important;background-position:50%!important;background-repeat:no-repeat!important}.other_cursor.pointer[data-v-b743e11e]{width:22px;height:23px}.other_cursor.pencil[data-v-b743e11e]{width:23px;height:23px;margin:-22px 0 0}.other_cursor.eraser[data-v-b743e11e]{width:25px;height:23px;margin:-20px 0 0 -10px}.other_cursor .cursor-name[data-v-b743e11e]{position:absolute;left:35px;bottom:-20px;max-width:180px;height:20px;padding:0 8px;white-space:nowrap;text-overflow:ellipsis;color:#fff;font-size:13px;line-height:20px;border-radius:3px;font-weight:600;overflow:hidden}@media only screen and (max-width:767px){.other_cursor .cursor-name[data-v-b743e11e]{left:25px;bottom:-15px;max-width:80px;height:16px;font-weight:400;font-size:11px;line-height:16px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1401:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/OtherCursor.vue?vue&type=template&id=b743e11e&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:"show",rawName:"v-show",value:(_vm.isVisible),expression:"isVisible"}],ref:"other_cursor",class:['other_cursor', _vm.otherCursor.cursor],style:(_vm.styles),attrs:{"id":"other_cursor"}},[_vm._ssrNode("<div class=\"cursor-name\""+(_vm._ssrStyle(null,{ backgroundColor: _vm.otherCursor.bgColorTooltip }, null))+" data-v-b743e11e>"+_vm._ssrEscape("\n    "+_vm._s(_vm.otherCursor.username)+"\n  ")+"</div>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/classroom/OtherCursor.vue?vue&type=template&id=b743e11e&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/OtherCursor.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var OtherCursorvue_type_script_lang_js_ = ({
  name: 'OtherCursor',

  data() {
    return {
      left: 0,
      top: 0
    };
  },

  computed: {
    otherCursor() {
      return this.$store.getters['classroom/otherCursor'];
    },

    otherUserRole() {
      return this.$store.getters['classroom/otherUserRole'];
    },

    isVisible() {
      return !!this.otherCursor.username;
    },

    zoom() {
      var _this$$store$getters$;

      return (_this$$store$getters$ = this.$store.getters['classroom/zoomAsset']) === null || _this$$store$getters$ === void 0 ? void 0 : _this$$store$getters$.asset;
    },

    styles() {
      return {
        top: `${this.top}px`,
        left: `${this.left}px`,
        backgroundImage: 'url(' + __webpack_require__(1319)(`./${this.otherUserRole}-${this.otherCursor.cursor}.svg`) + ')'
      };
    }

  },
  watch: {
    otherCursor: {
      handler(data) {
        this.left = (data.coords.x - this.zoom.x) * this.zoom.zoomIndex;
        this.top = (data.coords.y - this.zoom.y) * this.zoom.zoomIndex;
      }

    }
  }
});
// CONCATENATED MODULE: ./components/classroom/OtherCursor.vue?vue&type=script&lang=js&
 /* harmony default export */ var classroom_OtherCursorvue_type_script_lang_js_ = (OtherCursorvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/classroom/OtherCursor.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1320)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  classroom_OtherCursorvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "b743e11e",
  "5063d7d6"
  
)

/* harmony default export */ var OtherCursor = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=classroom-other-cursor.js.map