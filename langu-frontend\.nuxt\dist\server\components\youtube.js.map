{"version": 3, "file": "components/youtube.js", "sources": ["webpack:///./node_modules/vuetify/src/components/VSkeletonLoader/VSkeletonLoader.sass?044b", "webpack:///./node_modules/vuetify/src/components/VSkeletonLoader/VSkeletonLoader.sass", "webpack:///../../../src/components/VSkeletonLoader/VSkeletonLoader.ts", "webpack:///./components/Youtube.vue?a44f", "webpack:///./components/Youtube.vue?e0f4", "webpack:///./components/Youtube.vue?26b1", "webpack:///./components/Youtube.vue", "webpack:///./components/Youtube.vue?b8b0", "webpack:///./components/Youtube.vue?f6b3", "webpack:///./components/Youtube.vue?e043", "webpack:///./components/Youtube.vue?15b1", "webpack:///./components/Youtube.vue?fd86", "webpack:///./components/Youtube.vue?1df7"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./VSkeletonLoader.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"5f757930\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".theme--light.v-skeleton-loader .v-skeleton-loader__bone:after{background:linear-gradient(90deg,hsla(0,0%,100%,0),hsla(0,0%,100%,.3),hsla(0,0%,100%,0))}.theme--light.v-skeleton-loader .v-skeleton-loader__avatar,.theme--light.v-skeleton-loader .v-skeleton-loader__button,.theme--light.v-skeleton-loader .v-skeleton-loader__chip,.theme--light.v-skeleton-loader .v-skeleton-loader__divider,.theme--light.v-skeleton-loader .v-skeleton-loader__heading,.theme--light.v-skeleton-loader .v-skeleton-loader__image,.theme--light.v-skeleton-loader .v-skeleton-loader__text{background:rgba(0,0,0,.12)}.theme--light.v-skeleton-loader .v-skeleton-loader__actions,.theme--light.v-skeleton-loader .v-skeleton-loader__article,.theme--light.v-skeleton-loader .v-skeleton-loader__card-heading,.theme--light.v-skeleton-loader .v-skeleton-loader__card-text,.theme--light.v-skeleton-loader .v-skeleton-loader__date-picker,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-avatar,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-avatar-three-line,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-avatar-two-line,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-text,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-three-line,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-two-line,.theme--light.v-skeleton-loader .v-skeleton-loader__table-heading,.theme--light.v-skeleton-loader .v-skeleton-loader__table-tbody,.theme--light.v-skeleton-loader .v-skeleton-loader__table-tfoot,.theme--light.v-skeleton-loader .v-skeleton-loader__table-thead{background:#fff}.theme--dark.v-skeleton-loader .v-skeleton-loader__bone:after{background:linear-gradient(90deg,hsla(0,0%,100%,0),hsla(0,0%,100%,.05),hsla(0,0%,100%,0))}.theme--dark.v-skeleton-loader .v-skeleton-loader__avatar,.theme--dark.v-skeleton-loader .v-skeleton-loader__button,.theme--dark.v-skeleton-loader .v-skeleton-loader__chip,.theme--dark.v-skeleton-loader .v-skeleton-loader__divider,.theme--dark.v-skeleton-loader .v-skeleton-loader__heading,.theme--dark.v-skeleton-loader .v-skeleton-loader__image,.theme--dark.v-skeleton-loader .v-skeleton-loader__text{background:hsla(0,0%,100%,.12)}.theme--dark.v-skeleton-loader .v-skeleton-loader__actions,.theme--dark.v-skeleton-loader .v-skeleton-loader__article,.theme--dark.v-skeleton-loader .v-skeleton-loader__card-heading,.theme--dark.v-skeleton-loader .v-skeleton-loader__card-text,.theme--dark.v-skeleton-loader .v-skeleton-loader__date-picker,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-avatar,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-avatar-three-line,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-avatar-two-line,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-text,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-three-line,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-two-line,.theme--dark.v-skeleton-loader .v-skeleton-loader__table-heading,.theme--dark.v-skeleton-loader .v-skeleton-loader__table-tbody,.theme--dark.v-skeleton-loader .v-skeleton-loader__table-tfoot,.theme--dark.v-skeleton-loader .v-skeleton-loader__table-thead{background:#1e1e1e}.v-skeleton-loader{border-radius:8px;position:relative;vertical-align:top}.v-skeleton-loader__actions{padding:16px 16px 8px;text-align:right}.v-skeleton-loader__actions .v-skeleton-loader__button{display:inline-block}.v-application--is-ltr .v-skeleton-loader__actions .v-skeleton-loader__button:first-child{margin-right:12px}.v-application--is-rtl .v-skeleton-loader__actions .v-skeleton-loader__button:first-child{margin-left:12px}.v-skeleton-loader .v-skeleton-loader__list-item,.v-skeleton-loader .v-skeleton-loader__list-item-avatar,.v-skeleton-loader .v-skeleton-loader__list-item-avatar-three-line,.v-skeleton-loader .v-skeleton-loader__list-item-avatar-two-line,.v-skeleton-loader .v-skeleton-loader__list-item-text,.v-skeleton-loader .v-skeleton-loader__list-item-three-line,.v-skeleton-loader .v-skeleton-loader__list-item-two-line{border-radius:8px}.v-skeleton-loader .v-skeleton-loader__actions:after,.v-skeleton-loader .v-skeleton-loader__article:after,.v-skeleton-loader .v-skeleton-loader__card-avatar:after,.v-skeleton-loader .v-skeleton-loader__card-heading:after,.v-skeleton-loader .v-skeleton-loader__card-text:after,.v-skeleton-loader .v-skeleton-loader__card:after,.v-skeleton-loader .v-skeleton-loader__date-picker-days:after,.v-skeleton-loader .v-skeleton-loader__date-picker-options:after,.v-skeleton-loader .v-skeleton-loader__date-picker:after,.v-skeleton-loader .v-skeleton-loader__list-item-avatar-three-line:after,.v-skeleton-loader .v-skeleton-loader__list-item-avatar-two-line:after,.v-skeleton-loader .v-skeleton-loader__list-item-avatar:after,.v-skeleton-loader .v-skeleton-loader__list-item-text:after,.v-skeleton-loader .v-skeleton-loader__list-item-three-line:after,.v-skeleton-loader .v-skeleton-loader__list-item-two-line:after,.v-skeleton-loader .v-skeleton-loader__list-item:after,.v-skeleton-loader .v-skeleton-loader__paragraph:after,.v-skeleton-loader .v-skeleton-loader__sentences:after,.v-skeleton-loader .v-skeleton-loader__table-cell:after,.v-skeleton-loader .v-skeleton-loader__table-heading:after,.v-skeleton-loader .v-skeleton-loader__table-row-divider:after,.v-skeleton-loader .v-skeleton-loader__table-row:after,.v-skeleton-loader .v-skeleton-loader__table-tbody:after,.v-skeleton-loader .v-skeleton-loader__table-tfoot:after,.v-skeleton-loader .v-skeleton-loader__table-thead:after,.v-skeleton-loader .v-skeleton-loader__table:after{display:none}.v-application--is-ltr .v-skeleton-loader__article .v-skeleton-loader__heading{margin:16px 0 16px 16px}.v-application--is-rtl .v-skeleton-loader__article .v-skeleton-loader__heading{margin:16px 16px 0}.v-skeleton-loader__article .v-skeleton-loader__paragraph{padding:16px}.v-skeleton-loader__bone{border-radius:inherit;overflow:hidden;position:relative}.v-skeleton-loader__bone:after{-webkit-animation:loading 1.5s infinite;animation:loading 1.5s infinite;content:\\\"\\\";height:100%;left:0;position:absolute;right:0;top:0;transform:translateX(-100%);z-index:1}.v-skeleton-loader__avatar{border-radius:50%;height:48px;width:48px}.v-skeleton-loader__button{border-radius:4px;height:36px;width:64px}.v-skeleton-loader__card .v-skeleton-loader__image{border-radius:0}.v-skeleton-loader__card-heading .v-skeleton-loader__heading{margin:16px}.v-skeleton-loader__card-text{padding:16px}.v-skeleton-loader__chip{border-radius:16px;height:32px;width:96px}.v-skeleton-loader__date-picker{border-radius:inherit}.v-skeleton-loader__date-picker .v-skeleton-loader__list-item:first-child .v-skeleton-loader__text{max-width:88px;width:20%}.v-skeleton-loader__date-picker .v-skeleton-loader__heading{max-width:256px;width:40%}.v-skeleton-loader__date-picker-days{display:flex;flex-wrap:wrap;padding:0 12px;margin:0 auto}.v-skeleton-loader__date-picker-days .v-skeleton-loader__avatar{border-radius:8px;flex:1 1 auto;margin:4px;height:40px;width:40px}.v-skeleton-loader__date-picker-options{align-items:center;display:flex;padding:16px}.v-skeleton-loader__date-picker-options .v-skeleton-loader__avatar{height:40px;width:40px}.v-skeleton-loader__date-picker-options .v-skeleton-loader__avatar:nth-child(2){margin-left:auto}.v-application--is-ltr .v-skeleton-loader__date-picker-options .v-skeleton-loader__avatar:nth-child(2){margin-right:8px}.v-application--is-rtl .v-skeleton-loader__date-picker-options .v-skeleton-loader__avatar:nth-child(2){margin-left:8px}.v-skeleton-loader__date-picker-options .v-skeleton-loader__text.v-skeleton-loader__bone:first-child{margin-bottom:0;max-width:50%;width:456px}.v-skeleton-loader__divider{border-radius:1px;height:2px}.v-skeleton-loader__heading{border-radius:12px;height:24px;width:45%}.v-skeleton-loader__image{height:200px;border-radius:0}.v-skeleton-loader__image~.v-skeleton-loader__card-heading{border-radius:0}.v-skeleton-loader__image::first-child,.v-skeleton-loader__image::last-child{border-radius:inherit}.v-skeleton-loader__list-item{height:48px}.v-skeleton-loader__list-item-three-line{flex-wrap:wrap}.v-skeleton-loader__list-item-three-line>*{flex:1 0 100%;width:100%}.v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__avatar,.v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__avatar,.v-skeleton-loader__list-item-avatar .v-skeleton-loader__avatar{height:40px;width:40px}.v-skeleton-loader__list-item-avatar{height:48px}.v-skeleton-loader__list-item-avatar-two-line,.v-skeleton-loader__list-item-two-line{height:72px}.v-skeleton-loader__list-item-avatar-three-line,.v-skeleton-loader__list-item-three-line{height:88px}.v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__avatar{align-self:flex-start}.v-skeleton-loader__list-item,.v-skeleton-loader__list-item-avatar,.v-skeleton-loader__list-item-avatar-three-line,.v-skeleton-loader__list-item-avatar-two-line,.v-skeleton-loader__list-item-three-line,.v-skeleton-loader__list-item-two-line{align-content:center;align-items:center;display:flex;flex-wrap:wrap;padding:0 16px}.v-application--is-ltr .v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item-avatar .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item-three-line .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item-two-line .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item .v-skeleton-loader__avatar{margin-right:16px}.v-application--is-rtl .v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item-avatar .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item-three-line .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item-two-line .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item .v-skeleton-loader__avatar{margin-left:16px}.v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item-avatar .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-avatar .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item-three-line .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-three-line .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item-two-line .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-two-line .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item .v-skeleton-loader__text:only-child{margin-bottom:0}.v-skeleton-loader__paragraph,.v-skeleton-loader__sentences{flex:1 0 auto}.v-skeleton-loader__paragraph:not(:last-child){margin-bottom:6px}.v-skeleton-loader__paragraph .v-skeleton-loader__text:first-child{max-width:100%}.v-skeleton-loader__paragraph .v-skeleton-loader__text:nth-child(2){max-width:50%}.v-skeleton-loader__paragraph .v-skeleton-loader__text:nth-child(3),.v-skeleton-loader__sentences .v-skeleton-loader__text:nth-child(2){max-width:70%}.v-skeleton-loader__sentences:not(:last-child){margin-bottom:6px}.v-skeleton-loader__table-heading{align-items:center;display:flex;justify-content:space-between;padding:16px}.v-skeleton-loader__table-heading .v-skeleton-loader__heading{max-width:15%}.v-skeleton-loader__table-heading .v-skeleton-loader__text{max-width:40%}.v-skeleton-loader__table-thead{display:flex;justify-content:space-between;padding:16px}.v-skeleton-loader__table-thead .v-skeleton-loader__heading{max-width:5%}.v-skeleton-loader__table-tbody{padding:16px 16px 0}.v-skeleton-loader__table-tfoot{align-items:center;display:flex;justify-content:flex-end;padding:16px}.v-application--is-ltr .v-skeleton-loader__table-tfoot>*{margin-left:8px}.v-application--is-rtl .v-skeleton-loader__table-tfoot>*{margin-right:8px}.v-skeleton-loader__table-tfoot .v-skeleton-loader__avatar{height:40px;width:40px}.v-skeleton-loader__table-tfoot .v-skeleton-loader__text{margin-bottom:0}.v-skeleton-loader__table-tfoot .v-skeleton-loader__text:first-child{max-width:128px}.v-skeleton-loader__table-tfoot .v-skeleton-loader__text:nth-child(2){max-width:64px}.v-skeleton-loader__table-row{display:flex;justify-content:space-between}.v-skeleton-loader__table-cell{align-items:center;display:flex;height:48px;width:88px}.v-skeleton-loader__table-cell .v-skeleton-loader__text{margin-bottom:0}.v-skeleton-loader__text{border-radius:6px;flex:1 0 auto;height:12px;margin-bottom:6px}.v-skeleton-loader--boilerplate .v-skeleton-loader__bone:after{display:none}.v-skeleton-loader--is-loading{overflow:hidden}.v-skeleton-loader--tile,.v-skeleton-loader--tile .v-skeleton-loader__bone{border-radius:0}@-webkit-keyframes loading{to{transform:translateX(100%)}}@keyframes loading{to{transform:translateX(100%)}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// Styles\nimport './VSkeletonLoader.sass'\n\n// Mixins\nimport Elevatable from '../../mixins/elevatable'\nimport Measurable from '../../mixins/measurable'\nimport Themeable from '../../mixins/themeable'\n\n// Utilities\nimport mixins from '../../util/mixins'\n\n// Types\nimport { VNode } from 'vue'\nimport { getSlot } from '../../util/helpers'\nimport { PropValidator } from 'vue/types/options'\n\nexport interface HTMLSkeletonLoaderElement extends HTMLElement {\n  _initialStyle?: {\n    display: string | null\n    transition: string\n  }\n}\n\n/* @vue/component */\nexport default mixins(\n  Elevatable,\n  Measurable,\n  Themeable,\n).extend({\n  name: 'VSkeletonLoader',\n\n  props: {\n    boilerplate: Boolean,\n    loading: Boolean,\n    tile: Boolean,\n    transition: String,\n    type: String,\n    types: {\n      type: Object,\n      default: () => ({}),\n    } as PropValidator<Record<string, string>>,\n  },\n\n  computed: {\n    attrs (): object {\n      if (!this.isLoading) return this.$attrs\n\n      return !this.boilerplate ? {\n        'aria-busy': true,\n        'aria-live': 'polite',\n        role: 'alert',\n        ...this.$attrs,\n      } : {}\n    },\n    classes (): object {\n      return {\n        'v-skeleton-loader--boilerplate': this.boilerplate,\n        'v-skeleton-loader--is-loading': this.isLoading,\n        'v-skeleton-loader--tile': this.tile,\n        ...this.themeClasses,\n        ...this.elevationClasses,\n      }\n    },\n    isLoading (): boolean {\n      return !('default' in this.$scopedSlots) || this.loading\n    },\n    rootTypes (): Record<string, string> {\n      return {\n        actions: 'button@2',\n        article: 'heading, paragraph',\n        avatar: 'avatar',\n        button: 'button',\n        card: 'image, card-heading',\n        'card-avatar': 'image, list-item-avatar',\n        'card-heading': 'heading',\n        chip: 'chip',\n        'date-picker': 'list-item, card-heading, divider, date-picker-options, date-picker-days, actions',\n        'date-picker-options': 'text, avatar@2',\n        'date-picker-days': 'avatar@28',\n        heading: 'heading',\n        image: 'image',\n        'list-item': 'text',\n        'list-item-avatar': 'avatar, text',\n        'list-item-two-line': 'sentences',\n        'list-item-avatar-two-line': 'avatar, sentences',\n        'list-item-three-line': 'paragraph',\n        'list-item-avatar-three-line': 'avatar, paragraph',\n        paragraph: 'text@3',\n        sentences: 'text@2',\n        table: 'table-heading, table-thead, table-tbody, table-tfoot',\n        'table-heading': 'heading, text',\n        'table-thead': 'heading@6',\n        'table-tbody': 'table-row-divider@6',\n        'table-row-divider': 'table-row, divider',\n        'table-row': 'table-cell@6',\n        'table-cell': 'text',\n        'table-tfoot': 'text@2, avatar@2',\n        text: 'text',\n        ...this.types,\n      }\n    },\n  },\n\n  methods: {\n    genBone (text: string, children: VNode[]) {\n      return this.$createElement('div', {\n        staticClass: `v-skeleton-loader__${text} v-skeleton-loader__bone`,\n      }, children)\n    },\n    genBones (bone: string): VNode[] {\n      // e.g. 'text@3'\n      const [type, length] = bone.split('@') as [string, number]\n      const generator = () => this.genStructure(type)\n\n      // Generate a length array based upon\n      // value after @ in the bone string\n      return Array.from({ length }).map(generator)\n    },\n    // Fix type when this is merged\n    // https://github.com/microsoft/TypeScript/pull/33050\n    genStructure (type?: string): any {\n      let children = []\n      type = type || this.type || ''\n      const bone = this.rootTypes[type] || ''\n\n      // End of recursion, do nothing\n      /* eslint-disable-next-line no-empty, brace-style */\n      if (type === bone) {}\n      // Array of values - e.g. 'heading, paragraph, text@2'\n      else if (type.indexOf(',') > -1) return this.mapBones(type)\n      // Array of values - e.g. 'paragraph@4'\n      else if (type.indexOf('@') > -1) return this.genBones(type)\n      // Array of values - e.g. 'card@2'\n      else if (bone.indexOf(',') > -1) children = this.mapBones(bone)\n      // Array of values - e.g. 'list-item@2'\n      else if (bone.indexOf('@') > -1) children = this.genBones(bone)\n      // Single value - e.g. 'card-heading'\n      else if (bone) children.push(this.genStructure(bone))\n\n      return [this.genBone(type, children)]\n    },\n    genSkeleton () {\n      const children = []\n\n      if (!this.isLoading) children.push(getSlot(this))\n      else children.push(this.genStructure())\n\n      /* istanbul ignore else */\n      if (!this.transition) return children\n\n      /* istanbul ignore next */\n      return this.$createElement('transition', {\n        props: {\n          name: this.transition,\n        },\n        // Only show transition when\n        // content has been loaded\n        on: {\n          afterEnter: this.resetStyles,\n          beforeEnter: this.onBeforeEnter,\n          beforeLeave: this.onBeforeLeave,\n          leaveCancelled: this.resetStyles,\n        },\n      }, children)\n    },\n    mapBones (bones: string) {\n      // Remove spaces and return array of structures\n      return bones.replace(/\\s/g, '').split(',').map(this.genStructure)\n    },\n    onBeforeEnter (el: HTMLSkeletonLoaderElement) {\n      this.resetStyles(el)\n\n      if (!this.isLoading) return\n\n      el._initialStyle = {\n        display: el.style.display,\n        transition: el.style.transition,\n      }\n\n      el.style.setProperty('transition', 'none', 'important')\n    },\n    onBeforeLeave (el: HTMLSkeletonLoaderElement) {\n      el.style.setProperty('display', 'none', 'important')\n    },\n    resetStyles (el: HTMLSkeletonLoaderElement) {\n      if (!el._initialStyle) return\n\n      el.style.display = el._initialStyle.display || ''\n      el.style.transition = el._initialStyle.transition\n\n      delete el._initialStyle\n    },\n  },\n\n  render (h): VNode {\n    return h('div', {\n      staticClass: 'v-skeleton-loader',\n      attrs: this.attrs,\n      on: this.$listeners,\n      class: this.classes,\n      style: this.isLoading ? this.measurableStyles : undefined,\n    }, [this.genSkeleton()])\n  },\n})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Youtube.vue?vue&type=style&index=0&id=8df477bc&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"37444f06\", content, true, context)\n};", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Youtube.vue?vue&type=style&index=1&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"1d225ef2\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.videoId)?_c('div',{staticClass:\"video\"},[(!_vm.isLoaded)?_c('v-skeleton-loader',{staticClass:\"video-v-skeleton-loader\",attrs:{\"type\":\"image\"}}):_vm._e(),_vm._ssrNode(\" \"),_c('client-only',[_c('div',{staticClass:\"video-helper\"},[_c('iframe',{attrs:{\"src\":(\"https://www.youtube.com/embed/\" + _vm.videoId),\"title\":\"YouTube video player\",\"frameborder\":\"0\",\"allow\":\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\"allowfullscreen\":\"\"},on:{\"load\":function($event){_vm.isLoaded = true}}})])])],2):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'LYoutube',\n  props: {\n    videoLink: {\n      type: String,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      isLoaded: false,\n    }\n  },\n  computed: {\n    videoId() {\n      let videoId = null\n\n      if (this.videoLink?.includes('v=')) {\n        videoId = this.videoLink.split('v=')[1]\n\n        const ampersandPosition = videoId.indexOf('&')\n\n        if (ampersandPosition !== -1) {\n          videoId = videoId.substring(0, ampersandPosition)\n        }\n      }\n\n      return videoId\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Youtube.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Youtube.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Youtube.vue?vue&type=template&id=8df477bc&scoped=true&\"\nimport script from \"./Youtube.vue?vue&type=script&lang=js&\"\nexport * from \"./Youtube.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./Youtube.vue?vue&type=style&index=0&id=8df477bc&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\nvar style1 = require(\"./Youtube.vue?vue&type=style&index=1&lang=scss&\")\nif (style1.__inject__) style1.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"8df477bc\",\n  \"d63acc8a\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VSkeletonLoader } from 'vuetify/lib/components/VSkeletonLoader';\ninstallComponents(component, {VSkeletonLoader})\n", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Youtube.vue?vue&type=style&index=0&id=8df477bc&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".video[data-v-8df477bc]{position:relative;height:0;padding-bottom:56%;border-radius:8px;overflow:hidden}.video-helper[data-v-8df477bc],.video-v-skeleton-loader[data-v-8df477bc]{position:absolute;top:0;left:0;width:100%;height:100%}.video-helper[data-v-8df477bc]{background-color:var(--v-greyLight-lighten2);z-index:2}.video-helper iframe[data-v-8df477bc]{width:100%;height:100%}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Youtube.vue?vue&type=style&index=1&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".video .video-v-skeleton-loader>div{height:100%!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAIA;AAUA;AACA;AAAA;AAKA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AANA;AAYA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAJA;AAJA;AACA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAZA;AACA;AAmBA;AACA;AArBA;AACA;AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA;AAiCA;AACA;AA1DA;AA4DA;AACA;AACA;AACA;AADA;AAFA;AACA;AAKA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AADA;AAAA;AAAA;AAbA;AACA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAEA;AAEA;AAEA;AAEA;AACA;AAGA;AApCA;AACA;AAqCA;AACA;AAEA;AAGA;AACA;AAAA;AAEA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AANA;AAhDA;AACA;AA6DA;AACA;AACA;AAhEA;AACA;AAiEA;AACA;AAEA;AAEA;AACA;AACA;AAFA;AAKA;AA5EA;AACA;AA6EA;AACA;AA/EA;AACA;AAgFA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAzFA;AACA;AA0FA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AA/KA;;;;;;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA;AAbA;;ACvBA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}