(window.webpackJsonp=window.webpackJsonp||[]).push([[202],{1350:function(e,t,n){"use strict";n.r(t),n.d(t,"Workbox",(function(){return h})),n.d(t,"messageSW",(function(){return r}));try{self["workbox:window:5.1.4"]&&_()}catch(r){}function r(e,t){return new Promise((function(n){var r=new MessageChannel;r.port1.onmessage=function(e){n(e.data)},e.postMessage(t,[r.port2])}))}function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function v(e,t){var n;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var i=0;return function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}return(n=e[Symbol.iterator]()).next.bind(n)}try{self["workbox:core:5.1.4"]&&_()}catch(r){}var i=function(){var e=this;this.promise=new Promise((function(t,n){e.resolve=t,e.reject=n}))};function f(e,t){var n=location.href;return new URL(e,n).href===new URL(t,n).href}var u=function(e,t){this.type=e,Object.assign(this,t)};function a(e,t,n){return n?t?t(e):e:(e&&e.then||(e=Promise.resolve(e)),t?e.then(t):e)}function l(){}var h=function(e){var t,n;function c(t,n){var r,o;return void 0===n&&(n={}),(r=e.call(this)||this).t={},r.i=0,r.o=new i,r.u=new i,r.s=new i,r.v=0,r.h=new Set,r.l=function(){var e=r.m,t=e.installing;r.i>0||!f(t.scriptURL,r.g)||performance.now()>r.v+6e4?(r.p=t,e.removeEventListener("updatefound",r.l)):(r.P=t,r.h.add(t),r.o.resolve(t)),++r.i,t.addEventListener("statechange",r.S)},r.S=function(e){var t=r.m,n=e.target,i=n.state,o=n===r.p,a=o?"external":"",c={sw:n,originalEvent:e};!o&&r.j&&(c.isUpdate=!0),r.dispatchEvent(new u(a+i,c)),"installed"===i?r.A=self.setTimeout((function(){"installed"===i&&t.waiting===n&&r.dispatchEvent(new u(a+"waiting",c))}),200):"activating"===i&&(clearTimeout(r.A),o||r.u.resolve(n))},r.O=function(e){var t=r.P;t===navigator.serviceWorker.controller&&(r.dispatchEvent(new u("controlling",{sw:t,originalEvent:e,isUpdate:r.j})),r.s.resolve(t))},r.U=(o=function(e){var t=e.data,n=e.source;return a(r.getSW(),(function(){r.h.has(n)&&r.dispatchEvent(new u("message",{data:t,sw:n,originalEvent:e}))}))},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return Promise.resolve(o.apply(this,e))}catch(e){return Promise.reject(e)}}),r.g=t,r.t=n,navigator.serviceWorker.addEventListener("message",r.U),r}n=e,(t=c).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n;var v,l,h=c.prototype;return h.register=function(e){var t=(void 0===e?{}:e).immediate,n=void 0!==t&&t;try{var r=this;return function(e,t){var n=e();return n&&n.then?n.then(t):t()}((function(){if(!n&&"complete"!==document.readyState)return s(new Promise((function(e){return window.addEventListener("load",e)})))}),(function(){return r.j=Boolean(navigator.serviceWorker.controller),r.I=r.M(),a(r.R(),(function(e){r.m=e,r.I&&(r.P=r.I,r.u.resolve(r.I),r.s.resolve(r.I),r.I.addEventListener("statechange",r.S,{once:!0}));var t=r.m.waiting;return t&&f(t.scriptURL,r.g)&&(r.P=t,Promise.resolve().then((function(){r.dispatchEvent(new u("waiting",{sw:t,wasWaitingBeforeRegister:!0}))})).then((function(){}))),r.P&&(r.o.resolve(r.P),r.h.add(r.P)),r.m.addEventListener("updatefound",r.l),navigator.serviceWorker.addEventListener("controllerchange",r.O,{once:!0}),r.m}))}))}catch(e){return Promise.reject(e)}},h.update=function(){try{return this.m?s(this.m.update()):void 0}catch(e){return Promise.reject(e)}},h.getSW=function(){try{return void 0!==this.P?this.P:this.o.promise}catch(e){return Promise.reject(e)}},h.messageSW=function(e){try{return a(this.getSW(),(function(t){return r(t,e)}))}catch(e){return Promise.reject(e)}},h.M=function(){var e=navigator.serviceWorker.controller;return e&&f(e.scriptURL,this.g)?e:void 0},h.R=function(){try{var e=this;return function(e,t){try{var n=e()}catch(e){return t(e)}return n&&n.then?n.then(void 0,t):n}((function(){return a(navigator.serviceWorker.register(e.g,e.t),(function(t){return e.v=performance.now(),t}))}),(function(e){throw e}))}catch(e){return Promise.reject(e)}},v=c,(l=[{key:"active",get:function(){return this.u.promise}},{key:"controlling",get:function(){return this.s.promise}}])&&o(v.prototype,l),c}(function(){function e(){this.k=new Map}var t=e.prototype;return t.addEventListener=function(e,t){this.B(e).add(t)},t.removeEventListener=function(e,t){this.B(e).delete(t)},t.dispatchEvent=function(e){e.target=this;for(var t,n=v(this.B(e.type));!(t=n()).done;)(0,t.value)(e)},t.B=function(e){return this.k.has(e)||this.k.set(e,new Set),this.k.get(e)},e}());function s(e,t){if(!t)return e&&e.then?e.then(l):Promise.resolve()}}}]);