{"version": 3, "file": "components/user-settings-calendar-notification-info.js", "sources": ["webpack:///./node_modules/vuetify/src/components/VSwitch/VSwitch.sass?ce72", "webpack:///./node_modules/vuetify/src/components/VSwitch/VSwitch.sass", "webpack:///../../../src/components/VSwitch/VSwitch.ts", "webpack:///./components/user-settings/CalendarNotificationInfo.vue?5c2c", "webpack:///./components/user-settings/CalendarNotificationInfo.vue", "webpack:///./components/user-settings/CalendarNotificationInfo.vue?3728", "webpack:///./components/user-settings/CalendarNotificationInfo.vue?0af8", "webpack:///../../../src/mixins/comparable/index.ts", "webpack:///./components/user-settings/UserSettingTemplate.vue?b3b0", "webpack:///./components/user-settings/UserSettingTemplate.vue", "webpack:///./components/user-settings/UserSettingTemplate.vue?fdf8", "webpack:///./components/user-settings/UserSettingTemplate.vue?54d6", "webpack:///../../../src/mixins/rippleable/index.ts", "webpack:///./node_modules/vuetify/src/styles/components/_selection-controls.sass?2a30", "webpack:///../../../src/mixins/selectable/index.ts", "webpack:///./components/user-settings/UserSettingTemplate.vue?9dd4", "webpack:///./node_modules/vuetify/src/styles/components/_selection-controls.sass", "webpack:///./components/user-settings/UserSettingTemplate.vue?a8f9", "webpack:///./components/user-settings/UserSettingTemplate.vue?0b4b"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./VSwitch.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"beda1088\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".theme--light.v-input--switch .v-input--switch__thumb{color:#fff}.theme--light.v-input--switch .v-input--switch__track{color:rgba(0,0,0,.38)}.theme--light.v-input--switch.v-input--is-disabled:not(.v-input--is-dirty) .v-input--switch__thumb{color:#fafafa!important}.theme--light.v-input--switch.v-input--is-disabled:not(.v-input--is-dirty) .v-input--switch__track{color:rgba(0,0,0,.12)!important}.theme--dark.v-input--switch .v-input--switch__thumb{color:#bdbdbd}.theme--dark.v-input--switch .v-input--switch__track{color:hsla(0,0%,100%,.3)}.theme--dark.v-input--switch.v-input--is-disabled:not(.v-input--is-dirty) .v-input--switch__thumb{color:#424242!important}.theme--dark.v-input--switch.v-input--is-disabled:not(.v-input--is-dirty) .v-input--switch__track{color:hsla(0,0%,100%,.1)!important}.v-input--switch__thumb,.v-input--switch__track{background-color:currentColor;pointer-events:none;transition:inherit}.v-input--switch__track{border-radius:8px;width:36px;height:14px;left:2px;position:absolute;opacity:.6;right:2px;top:calc(50% - 7px)}.v-input--switch__thumb{border-radius:50%;top:calc(50% - 10px);height:20px;position:relative;width:20px;display:flex;justify-content:center;align-items:center;transition:.3s cubic-bezier(.25,.8,.5,1)}.v-input--switch .v-input--selection-controls__input{width:38px}.v-input--switch .v-input--selection-controls__ripple{top:calc(50% - 24px)}.v-input--switch.v-input--dense .v-input--switch__thumb{width:18px;height:18px}.v-input--switch.v-input--dense .v-input--switch__track{height:12px;width:32px}.v-input--switch.v-input--dense.v-input--switch--inset .v-input--switch__track{height:22px;width:44px;top:calc(50% - 12px);left:-3px}.v-input--switch.v-input--dense .v-input--selection-controls__ripple{top:calc(50% - 22px)}.v-input--switch.v-input--is-dirty.v-input--is-disabled{opacity:.6}.v-application--is-ltr .v-input--switch .v-input--selection-controls__ripple{left:-14px}.v-application--is-ltr .v-input--switch.v-input--dense .v-input--selection-controls__ripple{left:-12px}.v-application--is-ltr .v-input--switch.v-input--is-dirty .v-input--selection-controls__ripple,.v-application--is-ltr .v-input--switch.v-input--is-dirty .v-input--switch__thumb{transform:translate(20px)}.v-application--is-rtl .v-input--switch .v-input--selection-controls__ripple{right:-14px}.v-application--is-rtl .v-input--switch.v-input--dense .v-input--selection-controls__ripple{right:-12px}.v-application--is-rtl .v-input--switch.v-input--is-dirty .v-input--selection-controls__ripple,.v-application--is-rtl .v-input--switch.v-input--is-dirty .v-input--switch__thumb{transform:translate(-20px)}.v-input--switch:not(.v-input--switch--flat):not(.v-input--switch--inset) .v-input--switch__thumb{box-shadow:0 2px 4px -1px rgba(0,0,0,.2),0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12)}.v-input--switch--inset .v-input--selection-controls__input,.v-input--switch--inset .v-input--switch__track{width:48px}.v-input--switch--inset .v-input--switch__track{border-radius:14px;height:28px;left:-4px;opacity:.32;top:calc(50% - 14px)}.v-application--is-ltr .v-input--switch--inset .v-input--selection-controls__ripple,.v-application--is-ltr .v-input--switch--inset .v-input--switch__thumb{transform:translate(0)!important}.v-application--is-rtl .v-input--switch--inset .v-input--selection-controls__ripple,.v-application--is-rtl .v-input--switch--inset .v-input--switch__thumb{transform:translate(-6px)!important}.v-application--is-ltr .v-input--switch--inset.v-input--is-dirty .v-input--selection-controls__ripple,.v-application--is-ltr .v-input--switch--inset.v-input--is-dirty .v-input--switch__thumb{transform:translate(20px)!important}.v-application--is-rtl .v-input--switch--inset.v-input--is-dirty .v-input--selection-controls__ripple,.v-application--is-rtl .v-input--switch--inset.v-input--is-dirty .v-input--switch__thumb{transform:translate(-26px)!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// Styles\nimport '../../styles/components/_selection-controls.sass'\nimport './VSwitch.sass'\n\n// Mixins\nimport Selectable from '../../mixins/selectable'\nimport VInput from '../VInput'\n\n// Directives\nimport Touch from '../../directives/touch'\n\n// Components\nimport { VFabTransition } from '../transitions'\nimport VProgressCircular from '../VProgressCircular/VProgressCircular'\n\n// Helpers\nimport { keyCodes } from '../../util/helpers'\n\n// Types\nimport { VNode, VNodeData } from 'vue'\n\n/* @vue/component */\nexport default Selectable.extend({\n  name: 'v-switch',\n\n  directives: { Touch },\n\n  props: {\n    inset: Boolean,\n    loading: {\n      type: [Boolean, String],\n      default: false,\n    },\n    flat: {\n      type: Boolean,\n      default: false,\n    },\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        ...VInput.options.computed.classes.call(this),\n        'v-input--selection-controls v-input--switch': true,\n        'v-input--switch--flat': this.flat,\n        'v-input--switch--inset': this.inset,\n      }\n    },\n    attrs (): object {\n      return {\n        'aria-checked': String(this.isActive),\n        'aria-disabled': String(this.isDisabled),\n        role: 'switch',\n      }\n    },\n    // Do not return undefined if disabled,\n    // according to spec, should still show\n    // a color when disabled and active\n    validationState (): string | undefined {\n      if (this.hasError && this.shouldValidate) return 'error'\n      if (this.hasSuccess) return 'success'\n      if (this.hasColor !== null) return this.computedColor\n      return undefined\n    },\n    switchData (): VNodeData {\n      return this.setTextColor(this.loading ? undefined : this.validationState, {\n        class: this.themeClasses,\n      })\n    },\n  },\n\n  methods: {\n    genDefaultSlot (): (VNode | null)[] {\n      return [\n        this.genSwitch(),\n        this.genLabel(),\n      ]\n    },\n    genSwitch (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-input--selection-controls__input',\n      }, [\n        this.genInput('checkbox', {\n          ...this.attrs,\n          ...this.attrs$,\n        }),\n        this.genRipple(this.setTextColor(this.validationState, {\n          directives: [{\n            name: 'touch',\n            value: {\n              left: this.onSwipeLeft,\n              right: this.onSwipeRight,\n            },\n          }],\n        })),\n        this.$createElement('div', {\n          staticClass: 'v-input--switch__track',\n          ...this.switchData,\n        }),\n        this.$createElement('div', {\n          staticClass: 'v-input--switch__thumb',\n          ...this.switchData,\n        }, [this.genProgress()]),\n      ])\n    },\n    genProgress (): VNode {\n      return this.$createElement(VFabTransition, {}, [\n        this.loading === false\n          ? null\n          : this.$slots.progress || this.$createElement(VProgressCircular, {\n            props: {\n              color: (this.loading === true || this.loading === '')\n                ? (this.color || 'primary')\n                : this.loading,\n              size: 16,\n              width: 2,\n              indeterminate: true,\n            },\n          }),\n      ])\n    },\n    onSwipeLeft () {\n      if (this.isActive) this.onChange()\n    },\n    onSwipeRight () {\n      if (!this.isActive) this.onChange()\n    },\n    onKeydown (e: KeyboardEvent) {\n      if (\n        (e.keyCode === keyCodes.left && this.isActive) ||\n        (e.keyCode === keyCodes.right && !this.isActive)\n      ) this.onChange()\n    },\n  },\n})\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.item)?_c('user-setting-template',{attrs:{\"title\":_vm.$t('calendar'),\"submit-func\":_vm.submitData}},[_c('v-row',[_c('v-col',{staticClass:\"col-12 col-md-8\"},[_c('div',{staticClass:\"input-wrap mb-3 mb-md-0\"},[_c('div',{staticClass:\"input-wrap-title body-1 font-weight-medium\"},[_vm._v(\"\\n          \"+_vm._s(_vm.$t(\n              'do_you_want_to_automatically_add_lesson_bookings_to_your_calendar'\n            ))+\"\\n        \")]),_vm._v(\" \"),(_vm.$vuetify.breakpoint.mdAndUp)?_c('div',{staticClass:\"mt-4 d-none d-md-block\",style:(_vm.styles)},[_c('text-input',{attrs:{\"type-class\":\"border-gradient\",\"height\":\"44\",\"rules\":_vm.emailRules,\"hide-details\":\"\",\"placeholder\":_vm.$t('calendar_email_address')},model:{value:(_vm.email),callback:function ($$v) {_vm.email=$$v},expression:\"email\"}})],1):_vm._e()])]),_vm._v(\" \"),_c('v-col',{staticClass:\"col-12 col-md-auto\",attrs:{\"offset-md\":\"1\"}},[_c('v-row',{attrs:{\"align\":\"center\",\"justify\":\"space-between\"}},[(_vm.$vuetify.breakpoint.smAndDown)?_c('v-col',{staticClass:\"col-9 col-md-12 d-md-none\"},[_c('div',{style:(_vm.styles)},[_c('text-input',{attrs:{\"type-class\":\"border-gradient\",\"height\":\"44\",\"rules\":_vm.emailRules,\"hide-details\":\"\",\"placeholder\":_vm.$t('calendar_email_address')},model:{value:(_vm.email),callback:function ($$v) {_vm.email=$$v},expression:\"email\"}})],1)]):_vm._e(),_vm._v(\" \"),_c('v-col',{staticClass:\"col-auto\"},[_c('v-switch',{staticClass:\"pt-0 mt-0 mt-md-3\",attrs:{\"inset\":\"\",\"ripple\":false,\"color\":\"success\",\"dense\":\"\",\"hide-details\":\"\"},model:{value:(_vm.enabled),callback:function ($$v) {_vm.enabled=$$v},expression:\"enabled\"}})],1)],1)],1)],1),_vm._v(\" \"),(_vm.isTeacher)?_c('v-row',{staticClass:\"pt-3 pt-md-4\"},[_c('v-col',{staticClass:\"col-12 col-md-8\"},[_c('div',{staticClass:\"input-wrap mb-3 mb-md-0\"},[_c('div',{staticClass:\"input-wrap-title body-1 font-weight-medium d-flex align-center\"},[_c('img',{staticStyle:{\"height\":\"24px\",\"width\":\"24px\",\"margin-right\":\"8px\"},attrs:{\"src\":\"https://www.gstatic.com/marketing-cms/assets/images/cf/3c/0d56042f479fac9ad22d06855578/calender.webp=s96-fcrop64=1,00000000ffffffff-rw\",\"alt\":\"Google Calendar\"}}),_vm._v(\"\\n          Connect to Google Calendar\\n        \")]),_vm._v(\" \"),_c('span',{staticStyle:{\"font-size\":\"14px\"},domProps:{\"innerHTML\":_vm._s(_vm.$t('advanced_integration_google_calendar_only'))}})])]),_vm._v(\" \"),_c('v-col',{staticClass:\"col-12 col-md-auto d-flex justify-end\",attrs:{\"offset-md\":\"1\"}},[_c('v-switch',{staticClass:\"pt-0 mt-0 mt-md-3\",attrs:{\"inset\":\"\",\"ripple\":false,\"color\":\"success\",\"dense\":\"\",\"hide-details\":\"\"},on:{\"change\":_vm.toggleAdvancedIntegration},model:{value:(_vm.syncCalendarWithSlots),callback:function ($$v) {_vm.syncCalendarWithSlots=$$v},expression:\"syncCalendarWithSlots\"}})],1)],1):_vm._e(),_vm._v(\" \"),(_vm.isTeacher)?_c('v-row',{staticClass:\"pt-0 pb-2\"},[_c('v-col',{staticClass:\"col-12\"},[_c('div',{staticClass:\"mt-2\",staticStyle:{\"font-size\":\"13px\",\"color\":\"#757575\"}},[_vm._v(\"\\n        This app's use and transfer to any other app of information received\\n        from Google APIs will adhere to the Google API Services User Data\\n        Policy, including the Limited Use requirements.\\n        \"),_c('a',{staticStyle:{\"color\":\"#1a73e8\",\"text-decoration\":\"underline\"},attrs:{\"href\":\"https://developers.google.com/terms/api-services-user-data-policy\",\"target\":\"_blank\",\"rel\":\"noopener\"}},[_vm._v(\"\\n          Read more\\n        \")])])])],1):_vm._e()],1):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'\nimport TextInput from '@/components/form/TextInput'\n\nexport default {\n  name: 'CalendarNotificationInfo',\n  components: { UserSettingTemplate, TextInput },\n  data() {\n    return {\n      syncCalendarWithSlots: undefined,\n    }\n  },\n  computed: {\n    isTeacher() {\n      return this.$store.getters['user/isTeacher']\n    },\n    item() {\n      return this.$store.state.settings.notificationCalendarItem\n    },\n    emailRules() {\n      return this.enabled ? [(v) => !!v, (v) => /.+@.+\\..+/.test(v)] : []\n    },\n    email: {\n      get() {\n        return this.item.email\n      },\n      set(value) {\n        this.$store.commit('settings/UPDATE_NOTIFICATION_CALENDAR_ITEM', {\n          email: value,\n        })\n      },\n    },\n    enabled: {\n      get() {\n        return this.item.enabled\n      },\n      set(value) {\n        this.$store.commit('settings/UPDATE_NOTIFICATION_CALENDAR_ITEM', {\n          enabled: value,\n        })\n      },\n    },\n    // syncCalendarWithSlots() {\n    //   console.log(this.$store.state.user.item.syncCalendarWithSlots)\n    //   return this.$store.state.user.item.syncCalendarWithSlots\n    // },\n    styles() {\n      let obj = {}\n\n      if (!this.enabled) {\n        obj = {\n          opacity: 0,\n          visibility: 'hidden',\n          zIndex: -1,\n        }\n      }\n\n      return obj\n    },\n  },\n  beforeCreate() {\n    this.$store.dispatch('settings/getNotificationCalendar')\n  },\n  beforeMount() {\n    this.syncCalendarWithSlots = this.$store.state.user.item.syncCalendarWithSlots\n  },\n  methods: {\n    submitData() {\n      this.$store.dispatch('settings/updateNotificationCalendar')\n    },\n    toggleAdvancedIntegration(event) {\n      window.location = event\n        ? '/user/login/google-calendar'\n        : '/google/calendar-notification-webhook-deactivate'\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CalendarNotificationInfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./CalendarNotificationInfo.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./CalendarNotificationInfo.vue?vue&type=template&id=0780ed20&scoped=true&\"\nimport script from \"./CalendarNotificationInfo.vue?vue&type=script&lang=js&\"\nexport * from \"./CalendarNotificationInfo.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  \n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"0780ed20\",\n  \"764cb474\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {UserSettingTemplate: require('D:/languworks/langu-frontend/components/user-settings/UserSettingTemplate.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VRow } from 'vuetify/lib/components/VGrid';\nimport { VSwitch } from 'vuetify/lib/components/VSwitch';\ninstallComponents(component, {VCol,VRow,VSwitch})\n", "import Vue from 'vue'\nimport { PropValidator } from 'vue/types/options'\nimport { deepEqual } from '../../util/helpers'\n\nexport default Vue.extend({\n  name: 'comparable',\n  props: {\n    valueComparator: {\n      type: Function,\n      default: deepEqual,\n    } as PropValidator<typeof deepEqual>,\n  },\n})\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-form',{ref:\"form\",attrs:{\"value\":_vm.formValid},on:{\"validate\":_vm.validate,\"submit\":function($event){$event.preventDefault();return _vm.submit.apply(null, arguments)},\"input\":function($event){_vm.formValid = $event}}},[_c('div',{staticClass:\"user-settings-panel\"},[_c('div',{staticClass:\"panel\"},[(_vm.$vuetify.breakpoint.smAndUp)?_c('div',{staticClass:\"panel-head d-none d-sm-block\"},[_c('div',{staticClass:\"panel-head-title font-weight-medium mb-2 mb-sm-3 mb-md-5\"},[_vm._v(\"\\n          \"+_vm._s(_vm.title)+\"\\n        \")])]):_vm._e(),_vm._v(\" \"),_c('div',{staticClass:\"panel-body\"},[_vm._t(\"default\")],2),_vm._v(\" \"),(!_vm.hideFooter)?_c('div',{staticClass:\"panel-footer d-flex justify-center justify-sm-end\"},[_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"primary\",\"type\":\"submit\",\"disabled\":!_vm.valid}},[_c('svg',{staticClass:\"mr-1\",attrs:{\"width\":\"18\",\"height\":\"18\",\"viewBox\":\"0 0 18 18\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#save-icon\")}})]),_vm._v(\"\\n          \"+_vm._s(_vm.$t('save_changes'))+\"\\n        \")])],1):_vm._e()])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'UserSettingTemplate',\n  props: {\n    title: {\n      type: String,\n      required: true,\n    },\n    hideFooter: {\n      type: <PERSON>olean,\n      default: false,\n    },\n    customValid: {\n      type: Boolean,\n      default: true,\n    },\n    submitFunc: {\n      type: Function,\n      default: () => {},\n    },\n  },\n  data() {\n    return {\n      formValid: true,\n    }\n  },\n  computed: {\n    valid() {\n      return this.formValid && this.customValid\n    },\n  },\n  mounted() {\n    this.validate()\n  },\n  methods: {\n    validate() {\n      this.$refs.form.validate()\n    },\n    submit() {\n      if (!this.valid) return\n\n      this.submitFunc()\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserSettingTemplate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserSettingTemplate.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./UserSettingTemplate.vue?vue&type=template&id=6326778e&\"\nimport script from \"./UserSettingTemplate.vue?vue&type=script&lang=js&\"\nexport * from \"./UserSettingTemplate.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./UserSettingTemplate.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"ed2bb580\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VForm } from 'vuetify/lib/components/VForm';\ninstallComponents(component, {VBtn,VForm})\n", "// Directives\nimport ripple from '../../directives/ripple'\n\n// Types\nimport Vue, { VNode, VNodeData, VNodeDirective } from 'vue'\n\nexport default Vue.extend({\n  name: 'rippleable',\n\n  directives: { ripple },\n\n  props: {\n    ripple: {\n      type: [Boolean, Object],\n      default: true,\n    },\n  },\n\n  methods: {\n    genRipple (data: VNodeData = {}): VNode | null {\n      if (!this.ripple) return null\n\n      data.staticClass = 'v-input--selection-controls__ripple'\n\n      data.directives = data.directives || []\n      data.directives.push({\n        name: 'ripple',\n        value: { center: true },\n      } as VNodeDirective)\n\n      return this.$createElement('div', data)\n    },\n  },\n})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./_selection-controls.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"2e2bc7da\", content, true)", "// Components\nimport VInput from '../../components/VInput'\n\n// Mixins\nimport Rippleable from '../rippleable'\nimport Comparable from '../comparable'\n\n// Utilities\nimport mixins from '../../util/mixins'\n\nexport function prevent (e: Event) {\n  e.preventDefault()\n}\n\n/* @vue/component */\nexport default mixins(\n  VInput,\n  Rippleable,\n  Comparable\n).extend({\n  name: 'selectable',\n\n  model: {\n    prop: 'inputValue',\n    event: 'change',\n  },\n\n  props: {\n    id: String,\n    inputValue: null as any,\n    falseValue: null as any,\n    trueValue: null as any,\n    multiple: {\n      type: Boolean,\n      default: null,\n    },\n    label: String,\n  },\n\n  data () {\n    return {\n      hasColor: this.inputValue,\n      lazyValue: this.inputValue,\n    }\n  },\n\n  computed: {\n    computedColor (): string | undefined {\n      if (!this.isActive) return undefined\n      if (this.color) return this.color\n      if (this.isDark && !this.appIsDark) return 'white'\n      return 'primary'\n    },\n    isMultiple (): boolean {\n      return this.multiple === true || (this.multiple === null && Array.isArray(this.internalValue))\n    },\n    isActive (): boolean {\n      const value = this.value\n      const input = this.internalValue\n\n      if (this.isMultiple) {\n        if (!Array.isArray(input)) return false\n\n        return input.some(item => this.valueComparator(item, value))\n      }\n\n      if (this.trueValue === undefined || this.falseValue === undefined) {\n        return value\n          ? this.valueComparator(value, input)\n          : Boolean(input)\n      }\n\n      return this.valueComparator(input, this.trueValue)\n    },\n    isDirty (): boolean {\n      return this.isActive\n    },\n    rippleState (): string | undefined {\n      return !this.isDisabled && !this.validationState\n        ? undefined\n        : this.validationState\n    },\n  },\n\n  watch: {\n    inputValue (val) {\n      this.lazyValue = val\n      this.hasColor = val\n    },\n  },\n\n  methods: {\n    genLabel () {\n      const label = VInput.options.methods.genLabel.call(this)\n\n      if (!label) return label\n\n      label!.data!.on = {\n        // Label shouldn't cause the input to focus\n        click: prevent,\n      }\n\n      return label\n    },\n    genInput (type: string, attrs: object) {\n      return this.$createElement('input', {\n        attrs: Object.assign({\n          'aria-checked': this.isActive.toString(),\n          disabled: this.isDisabled,\n          id: this.computedId,\n          role: type,\n          type,\n        }, attrs),\n        domProps: {\n          value: this.value,\n          checked: this.isActive,\n        },\n        on: {\n          blur: this.onBlur,\n          change: this.onChange,\n          focus: this.onFocus,\n          keydown: this.onKeydown,\n          click: prevent,\n        },\n        ref: 'input',\n      })\n    },\n    onBlur () {\n      this.isFocused = false\n    },\n    onClick (e: Event) {\n      this.onChange()\n      this.$emit('click', e)\n    },\n    onChange () {\n      if (!this.isInteractive) return\n\n      const value = this.value\n      let input = this.internalValue\n\n      if (this.isMultiple) {\n        if (!Array.isArray(input)) {\n          input = []\n        }\n\n        const length = input.length\n\n        input = input.filter((item: any) => !this.valueComparator(item, value))\n\n        if (input.length === length) {\n          input.push(value)\n        }\n      } else if (this.trueValue !== undefined && this.falseValue !== undefined) {\n        input = this.valueComparator(input, this.trueValue) ? this.falseValue : this.trueValue\n      } else if (value) {\n        input = this.valueComparator(input, value) ? null : value\n      } else {\n        input = !input\n      }\n\n      this.validate(true, input)\n      this.internalValue = input\n      this.hasColor = input\n    },\n    onFocus () {\n      this.isFocused = true\n    },\n    /** @abstract */\n    onKeydown (e: Event) {},\n  },\n})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserSettingTemplate.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"419d3f06\", content, true, context)\n};", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".theme--light.v-input--selection-controls.v-input--is-disabled:not(.v-input--indeterminate) .v-icon{color:rgba(0,0,0,.26)!important}.theme--dark.v-input--selection-controls.v-input--is-disabled:not(.v-input--indeterminate) .v-icon{color:hsla(0,0%,100%,.3)!important}.v-input--selection-controls{margin-top:16px;padding-top:4px}.v-input--selection-controls>.v-input__append-outer,.v-input--selection-controls>.v-input__prepend-outer{margin-top:0;margin-bottom:0}.v-input--selection-controls:not(.v-input--hide-details)>.v-input__slot{margin-bottom:12px}.v-input--selection-controls .v-input__slot,.v-input--selection-controls .v-radio{cursor:pointer}.v-input--selection-controls .v-input__slot>.v-label,.v-input--selection-controls .v-radio>.v-label{align-items:center;display:inline-flex;flex:1 1 auto;height:auto}.v-input--selection-controls__input{color:inherit;display:inline-flex;flex:0 0 auto;height:24px;position:relative;transition:.3s cubic-bezier(.25,.8,.5,1);transition-property:transform;width:24px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__input .v-icon{width:100%}.v-application--is-ltr .v-input--selection-controls__input{margin-right:8px}.v-application--is-rtl .v-input--selection-controls__input{margin-left:8px}.v-input--selection-controls__input input[role=checkbox],.v-input--selection-controls__input input[role=radio],.v-input--selection-controls__input input[role=switch]{position:absolute;opacity:0;width:100%;height:100%;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__input+.v-label{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__ripple{border-radius:50%;cursor:pointer;height:34px;position:absolute;transition:inherit;width:34px;left:-12px;top:calc(50% - 24px);margin:7px}.v-input--selection-controls__ripple:before{border-radius:inherit;bottom:0;content:\\\"\\\";position:absolute;opacity:.2;left:0;right:0;top:0;transform-origin:center center;transform:scale(.2);transition:inherit}.v-input--selection-controls__ripple>.v-ripple__container{transform:scale(1.2)}.v-input--selection-controls.v-input--dense .v-input--selection-controls__ripple{width:28px;height:28px;left:-9px}.v-input--selection-controls.v-input--dense:not(.v-input--switch) .v-input--selection-controls__ripple{top:calc(50% - 21px)}.v-input--selection-controls.v-input{flex:0 1 auto}.v-input--selection-controls.v-input--is-focused .v-input--selection-controls__ripple:before,.v-input--selection-controls .v-radio--is-focused .v-input--selection-controls__ripple:before{background:currentColor;transform:scale(1.2)}.v-input--selection-controls__input:hover .v-input--selection-controls__ripple:before{background:currentColor;transform:scale(1.2);transition:none}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserSettingTemplate.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".user-settings-panel{padding:44px;border-radius:20px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1)}@media only screen and (max-width:1439px){.user-settings-panel{padding:24px}}@media only screen and (max-width:767px){.user-settings-panel{padding:0;box-shadow:none}}.user-settings-panel .row{margin:0 -14px!important}.user-settings-panel .col{padding:0 14px!important}.user-settings-panel .panel{color:var(--v-greyDark-base)}.user-settings-panel .panel-head-title{font-size:24px;line-height:1.333;color:var(--v-darkLight-base)}@media only screen and (max-width:1439px){.user-settings-panel .panel-head-title{font-size:20px}}.user-settings-panel .panel-body .chips>div{margin-top:6px}.user-settings-panel .panel-body .price-input .v-text-field--outlined.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot{min-height:32px!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr>th{color:var(--v-dark-base)}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr:last-child>th{border:none!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:hover:not(.v-data-table__expanded__content):not(.v-data-table__empty-wrapper){background:none}.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tbody>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tfoot>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>thead>tr>td{height:38px;color:var(--v-greyDark-base)}.user-settings-panel .panel-footer{margin-top:115px}@media only screen and (max-width:1439px){.user-settings-panel .panel-footer{margin-top:56px}}.user-settings-panel .panel-footer .v-btn{letter-spacing:.1px}@media only screen and (max-width:479px){.user-settings-panel .panel-footer .v-btn{width:100%!important}}.user-settings-panel .l-checkbox .v-input--selection-controls__input{width:16px;height:16px;margin-top:2px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AAKA;AACA;AAAA;AACA;AAEA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AANA;AAYA;AACA;AACA;AAEA;AACA;AACA;AAJA;AAFA;AACA;AAQA;AACA;AACA;AACA;AACA;AAHA;AAVA;AACA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAvBA;AACA;AAwBA;AACA;AACA;AADA;AAGA;AACA;AA9BA;AAgCA;AACA;AACA;AAFA;AACA;AAMA;AACA;AACA;AADA;AAKA;AAFA;AAKA;AACA;AACA;AACA;AACA;AAFA;AAFA;AADA;AAUA;AACA;AAFA;AAKA;AACA;AAFA;AA5BA;AACA;AAiCA;AACA;AAIA;AACA;AAGA;AACA;AACA;AANA;AADA;AAtCA;AACA;AAiDA;AACA;AAnDA;AACA;AAoDA;AACA;AAtDA;AACA;AAuDA;AACA;AAIA;AACA;AA9DA;AAjDA;;;;;;;;;;;;ACtBA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AATA;AAUA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AATA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AACA;AA/CA;AACA;AA+CA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AATA;AA9DA;;ACvHA;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AChCA;AAAA;AAAA;AAAA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AADA;AAFA;;;;;;;;;;;;ACJA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AACA;AAiBA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AACA;AAIA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AATA;AAjCA;;AC7CA;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AC7BA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAFA;AADA;AAOA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAFA;AAKA;AACA;AACA;AAdA;AAZA;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACPA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAKA;AAEA;AACA;AACA;AAFA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AATA;AACA;AAWA;AACA;AACA;AACA;AAFA;AArBA;AACA;AA0BA;AACA;AACA;AACA;AACA;AACA;AALA;AACA;AAMA;AACA;AARA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AA1BA;AACA;AA2BA;AACA;AA7BA;AACA;AA8BA;AACA;AAGA;AACA;AApCA;AAsCA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAFA;AAKA;AAXA;AACA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AAnBA;AAdA;AACA;AAmCA;AACA;AArCA;AACA;AAsCA;AACA;AACA;AAzCA;AACA;AA0CA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAXA;AAaA;AADA;AAGA;AADA;AAGA;AACA;AACA;AACA;AACA;AACA;AAvEA;AACA;AAwEA;AACA;AA1EA;AACA;AA2EA;AACA;AACA;AA9EA;AAxEA;;;;;;;ACnBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}