exports.ids = [69,68];
exports.modules = {

/***/ 1011:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1084);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("fd0dd7ee", content, true, context)
};

/***/ }),

/***/ 1023:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1109);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("20cd0fe8", content, true, context)
};

/***/ }),

/***/ 1083:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentItem_vue_vue_type_style_index_0_id_995c1e74_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1011);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentItem_vue_vue_type_style_index_0_id_995c1e74_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentItem_vue_vue_type_style_index_0_id_995c1e74_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentItem_vue_vue_type_style_index_0_id_995c1e74_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentItem_vue_vue_type_style_index_0_id_995c1e74_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1084:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".payment-item[data-v-995c1e74]{display:flex;background:#fff;border-radius:14px;margin-bottom:12px;overflow:hidden;box-shadow:0 4px 12px rgba(0,0,0,.1)}.payment-item[data-v-995c1e74]:hover{box-shadow:0 4px 14px rgba(217,225,236,.47)}.payment-item-date[data-v-995c1e74]{min-width:100px;padding:11px;display:flex;flex-direction:column;align-items:center;width:142px;border-radius:16px;justify-content:center;background:linear-gradient(126.15deg,rgba(128,182,34,.74),rgba(60,135,248,.74) 102.93%);color:#fff;box-shadow:4px 0 8px rgba(0,0,0,.1);position:relative;z-index:1}.payment-item-date .weekday[data-v-995c1e74]{font-size:13px;font-weight:700;line-height:1;text-transform:capitalize;text-align:center}.payment-item-date .date[data-v-995c1e74]{font-size:24px;font-weight:700;line-height:1.2;margin-bottom:2px}.payment-item-date .time[data-v-995c1e74]{font-size:13px;line-height:1;font-weight:700;margin-bottom:18px;text-align:center}.payment-item-date .duration-icon[data-v-995c1e74]{color:var(--v-dark-lighten3)}.payment-item-date .duration[data-v-995c1e74]{display:flex;align-items:center;font-size:16px}.payment-item-date .duration span[data-v-995c1e74]{color:#e8f1f7}.payment-item-date .duration-icon[data-v-995c1e74]{margin-right:4px;display:flex;align-items:center}.payment-item-content[data-v-995c1e74]{flex:1;padding:16px 24px}.payment-item-content .payment-info .student-name[data-v-995c1e74]{font-size:24px;font-weight:500;color:#333;margin-bottom:12px}.payment-item-content .payment-info .details[data-v-995c1e74]{display:flex;align-items:center;grid-gap:24px;gap:24px;font-size:14px}.payment-item-content .payment-info .details .detail-group[data-v-995c1e74]{align-items:center;grid-gap:6px;gap:6px}.payment-item-content .payment-info .details .detail-group p[data-v-995c1e74]{margin:0}.payment-item-content .payment-info .details .detail-group .label[data-v-995c1e74]{color:#666;font-size:14px}.payment-item-content .payment-info .details .detail-group .value[data-v-995c1e74]{color:#333}.payment-item-content .payment-info .details .detail-group .value.gradient-text[data-v-995c1e74]{background:linear-gradient(126.15deg,#80b622,#3c87f8 102.93%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;font-weight:500;font-size:16px;line-height:18px}.payment-item-content .payment-info .details .detail-group .pdf-download-link[data-v-995c1e74]{cursor:pointer}.payment-item-content .payment-info .details .detail-group .pdf-download-link[data-v-995c1e74]:hover{text-decoration:underline}.d-none[data-v-995c1e74]{display:none}@media screen and (min-width:768px){.d-sm-none[data-v-995c1e74]{display:none}}@media screen and (min-width:768px){.d-sm-block[data-v-995c1e74]{display:block}}@media screen and (max-width:768px){.payment-item[data-v-995c1e74]{flex-direction:column;margin-bottom:16px;box-shadow:none;background:transparent}.payment-item[data-v-995c1e74],.payment-item-date[data-v-995c1e74]{box-shadow:4px 0 8px rgba(0,0,0,.1)}.payment-item-date[data-v-995c1e74]{width:auto;min-height:auto;padding:8px 16px;flex-direction:row;justify-content:flex-start;border-radius:24px;margin-bottom:8px}.payment-item-date .date[data-v-995c1e74]{margin-right:8px;margin-bottom:0}.payment-item-date .time[data-v-995c1e74]{margin-left:0;opacity:1;margin-bottom:0}.payment-item-content[data-v-995c1e74]{background:#fff;border-radius:12px;padding:16px;box-shadow:0 4px 12px rgba(0,0,0,.1)}.payment-item-content .payment-info .student-name[data-v-995c1e74]{font-size:20px;margin-bottom:4px;padding-bottom:12px;border-bottom:1px solid rgba(0,0,0,.1)}.payment-item-content .payment-info .details[data-v-995c1e74]{flex-direction:column;grid-gap:8px;gap:8px}.payment-item-content .payment-info .details .detail-group[data-v-995c1e74]{display:flex;justify-content:space-between;width:100%}.payment-item-content .payment-info .details .detail-group .value[data-v-995c1e74]{font-size:16px;font-weight:500}.payment-item-content .payment-info .details .detail-group[data-v-995c1e74]:first-child{margin-bottom:4px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1102:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/payments/PaymentItem.vue?vue&type=template&id=995c1e74&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"payment-item"},[_vm._ssrNode("<div class=\"payment-item-date\" data-v-995c1e74><div data-v-995c1e74><div class=\"weekday d-none d-sm-block\" data-v-995c1e74>"+_vm._ssrEscape("\n        "+_vm._s(_vm.formatWeekday(_vm.item.date))+"\n      ")+"</div> <div class=\"date d-none d-sm-block\" data-v-995c1e74>"+_vm._ssrEscape("\n        "+_vm._s(_vm.formatDate(_vm.item.date))+"\n      ")+"</div> <div class=\"time d-none d-sm-block\" data-v-995c1e74>"+_vm._ssrEscape(_vm._s(_vm.formatTime(_vm.item.time)))+"</div> <div class=\"d-sm-none\" data-v-995c1e74>"+_vm._ssrEscape("\n        "+_vm._s(_vm.formatWeekday(_vm.item.date))+", "+_vm._s(_vm.formatDate(_vm.item.date))+" -\n        "+_vm._s(_vm.formatTime(_vm.item.time))+"\n      ")+"</div></div> <div class=\"duration d-none d-sm-block\" data-v-995c1e74><div class=\"duration-icon\" data-v-995c1e74><svg width=\"18\" height=\"18\" viewBox=\"0 0 18 18\" data-v-995c1e74><use"+(_vm._ssrAttr("xlink:href",__webpack_require__(14) + "#clock-thin"))+" data-v-995c1e74></use></svg> <span class=\"ml-1\" data-v-995c1e74>"+_vm._ssrEscape(_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.lessonLength })))+"</span></div></div> <div class=\"duration d-sm-none\" data-v-995c1e74>"+_vm._ssrEscape("\n       ("+_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.lessonLength }))+")\n    ")+"</div></div> <div class=\"payment-item-content\" data-v-995c1e74><div class=\"payment-info\" data-v-995c1e74><div class=\"student-name\" data-v-995c1e74>"+_vm._ssrEscape(_vm._s(_vm.item.student))+"</div> <div class=\"details\" data-v-995c1e74><div class=\"detail-group\" data-v-995c1e74><p class=\"label\" data-v-995c1e74>Lesson:</p> <p class=\"value gradient-text\" data-v-995c1e74>"+_vm._ssrEscape(_vm._s(_vm.item.lessonType))+"</p></div> <div class=\"detail-group\" data-v-995c1e74><p class=\"label\" data-v-995c1e74>Finished:</p> <p class=\"value gradient-text\" data-v-995c1e74>"+_vm._ssrEscape("\n            "+_vm._s(_vm.formatFinishedAt(_vm.item.finishedAt))+"\n          ")+"</p></div> <div class=\"detail-group\" data-v-995c1e74><p class=\"label\" data-v-995c1e74>Invoice no.</p> <p class=\"value gradient-text\" data-v-995c1e74>"+_vm._ssrEscape(_vm._s(_vm.item.invoiceNo))+"</p></div> <div class=\"detail-group\" data-v-995c1e74><p class=\"label\" data-v-995c1e74>Lesson no.</p> <p class=\"value gradient-text\" data-v-995c1e74>"+_vm._ssrEscape(_vm._s(_vm.item.lessonNo))+"</p></div> <div class=\"detail-group\" data-v-995c1e74><p class=\"label\" data-v-995c1e74>Value</p> <p class=\"value gradient-text\" data-v-995c1e74>"+_vm._ssrEscape("\n            "+_vm._s(_vm.formatCurrencyValue(_vm.item.value))+"\n          ")+"</p></div> "+((_vm.item.transactionId && _vm.item.invoiceNumber)?("<div class=\"detail-group\" data-v-995c1e74><p class=\"label\" data-v-995c1e74>PDF</p> <p class=\"value gradient-text\" data-v-995c1e74><a href=\"#\" class=\"pdf-download-link\" data-v-995c1e74>\n              Download\n            </a></p></div>"):"<!---->")+"</div></div></div>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/payments/PaymentItem.vue?vue&type=template&id=995c1e74&scoped=true&

// EXTERNAL MODULE: ./helpers/index.js
var helpers = __webpack_require__(501);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/payments/PaymentItem.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

const DEFAULT_PAYMENT_ITEM = {
  date: '2023-11-18',
  time: '9:00 AM',
  student: 'Kathrin Donaldson',
  lessonType: 'Trial',
  status: 'Finished',
  completedAt: '18 Nov, 10:02 AM',
  invoiceNo: '8395',
  lessonNo: '295032',
  value: '12.50',
  lessonLength: 30 // Default lesson length in minutes

};
/* harmony default export */ var PaymentItemvue_type_script_lang_js_ = ({
  name: 'PaymentItem',
  props: {
    item: {
      type: Object,
      required: true,
      default: () => ({ ...DEFAULT_PAYMENT_ITEM
      }),

      validator(value) {
        return ['date', 'time', 'student', 'lessonType', 'status', 'completedAt', 'invoiceNo', 'lessonNo', 'value'].every(key => key in value);
      }

    }
  },
  computed: {
    lessonLength() {
      // If lessonLength is available in the item, use it, otherwise default to 30 minutes
      return this.item.lessonLength || 30;
    },

    userLocale() {
      var _this$$store$state$us;

      // Get user's UI language/locale, fallback to browser locale or 'en'
      return this.$store.getters['user/isUserLogged'] ? ((_this$$store$state$us = this.$store.state.user.item) === null || _this$$store$state$us === void 0 ? void 0 : _this$$store$state$us.uiLanguage) || this.$i18n.locale : this.$i18n.locale || 'en';
    },

    timeZone() {
      // Get user's timezone, fallback to browser timezone
      return this.$store.getters['user/timeZone'];
    },

    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    },

    currentCurrency() {
      // Get current currency info from store
      return this.$store.state.currency.item;
    }

  },
  methods: {
    formatDate(date) {
      try {
        return this.$dayjs(date).tz(this.timeZone).format('DD MMM');
      } catch (e) {
        // Fallback to default formatting if there's an error
        return date;
      }
    },

    formatWeekday(date) {
      // Format weekday using user's locale and timezone
      try {
        // Use dayjs with timezone support and locale formatting
        return this.$dayjs(date).tz(this.timeZone).format('dddd');
      } catch (e) {
        // Fallback using Intl.DateTimeFormat with user's locale
        return new Intl.DateTimeFormat(this.userLocale, {
          weekday: 'long'
        }).format(new Date(date));
      }
    },

    formatTime(time) {
      // Format time using user's locale and timezone
      try {
        // If time is already in a good format, we can try to parse it with the date
        // and format it according to user's locale
        if (time && this.item.date) {
          // Combine date and time for proper timezone conversion
          const dateTimeString = `${this.item.date} ${time}`;
          const dateTime = this.$dayjs(dateTimeString).tz(this.timeZone); // Format time using locale-aware format (LT = localized time)

          return dateTime.format('LT');
        } // Fallback: return the original time if we can't parse it


        return time;
      } catch (e) {
        // Fallback to original time if there's an error
        return time;
      }
    },

    formatFinishedAt(finishedAt) {
      // Format finished date/time using user's locale and timezone
      try {
        if (!finishedAt) return '-'; // Use dayjs with timezone support and locale formatting
        // Format as "DD MMM, LT" (e.g., "18 Nov, 10:02 AM")

        return this.$dayjs(finishedAt).tz(this.timeZone).format('DD MMM, LT');
      } catch (e) {
        // Fallback to original value if there's an error
        return finishedAt || '-';
      }
    },

    formatValue(value) {
      // Format the value with exactly 2 decimal places
      return Number(value).toFixed(2);
    },

    formatCurrencyValue(value) {
      var _this$currentCurrency;

      // Format currency value according to user's locale
      const currencyCode = ((_this$currentCurrency = this.currentCurrency) === null || _this$currentCurrency === void 0 ? void 0 : _this$currentCurrency.isoCode) || 'EUR';
      return Object(helpers["formatCurrencyLocale"])(value, currencyCode, this.userLocale, true);
    },

    openPdf() {
      try {
        this.$store.dispatch('payments/openInvoicePdf', {
          transactionId: this.item.transactionId,
          invoiceNumber: this.item.invoiceNumber
        });
      } catch (error) {
        // Handle error - show user-friendly message
        if (this.$store.dispatch) {
          this.$store.dispatch('snackbar/error', {
            errorMessage: 'Failed to open invoice PDF. Please try again.'
          });
        }
      }
    }

  }
});
// CONCATENATED MODULE: ./components/payments/PaymentItem.vue?vue&type=script&lang=js&
 /* harmony default export */ var payments_PaymentItemvue_type_script_lang_js_ = (PaymentItemvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/payments/PaymentItem.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1083)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  payments_PaymentItemvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "995c1e74",
  "067c283c"
  
)

/* harmony default export */ var PaymentItem = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1108:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentLesson_vue_vue_type_style_index_0_id_ec37933a_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1023);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentLesson_vue_vue_type_style_index_0_id_ec37933a_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentLesson_vue_vue_type_style_index_0_id_ec37933a_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentLesson_vue_vue_type_style_index_0_id_ec37933a_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentLesson_vue_vue_type_style_index_0_id_ec37933a_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1109:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".payment-item[data-v-ec37933a]:hover{box-shadow:0 4px 14px rgba(217,225,236,.47)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1117:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/payments/PaymentLesson.vue?vue&type=template&id=ec37933a&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('payment-item',{attrs:{"item":_vm.paymentData},scopedSlots:_vm._u([{key:"additionalActionsTop",fn:function(){return [_c('div',{staticClass:"d-flex align-center"},[_c('v-chip',{staticClass:"mr-2",attrs:{"small":"","label":"","color":_vm.item.status === 'completed' ? 'success' : 'warning'}},[_vm._v("\n        "+_vm._s(_vm.item.status)+"\n      ")]),_vm._v(" "),_c('span',{staticClass:"caption grey--text"},[_vm._v("\n        "+_vm._s(_vm.$t('invoice_no'))+": "+_vm._s(_vm.item.invoiceNo)+"\n      ")])],1)]},proxy:true},{key:"additionalActionsBottom",fn:function(){return [_c('div',{staticClass:"d-flex align-center justify-space-between w-100"},[_c('div',{staticClass:"caption grey--text"},[_vm._v("\n        "+_vm._s(_vm.$t('lesson_no'))+": "+_vm._s(_vm.item.lessonNo)+"\n      ")]),_vm._v(" "),_c('div',{staticClass:"text-h6 primary--text"},[_vm._v("\n        "+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.formatValue(_vm.item.value))+"\n      ")])])]},proxy:true}])})}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/payments/PaymentLesson.vue?vue&type=template&id=ec37933a&scoped=true&

// EXTERNAL MODULE: ./components/payments/PaymentItem.vue + 4 modules
var PaymentItem = __webpack_require__(1102);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/payments/PaymentLesson.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var PaymentLessonvue_type_script_lang_js_ = ({
  name: 'PaymentLesson',
  components: {
    PaymentItem: PaymentItem["default"]
  },
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  computed: {
    paymentData() {
      return {
        day: this.item.day,
        date: this.item.date,
        time: this.item.time,
        student: this.item.student,
        lessonType: this.item.lessonType,
        status: this.item.status,
        invoiceNo: this.item.invoiceNo,
        lessonNo: this.item.lessonNo,
        value: this.item.value,
        finishedAt: this.item.finishedAt,
        transactionId: this.item.transactionId,
        invoiceNumber: this.item.invoiceNumber,
        lessonLength: this.item.lessonLength
      };
    },

    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    }

  },
  methods: {
    formatValue(value) {
      // Format the value with exactly 2 decimal places
      return Number(value).toFixed(2);
    }

  }
});
// CONCATENATED MODULE: ./components/payments/PaymentLesson.vue?vue&type=script&lang=js&
 /* harmony default export */ var payments_PaymentLessonvue_type_script_lang_js_ = (PaymentLessonvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VChip/VChip.js
var VChip = __webpack_require__(901);

// CONCATENATED MODULE: ./components/payments/PaymentLesson.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1108)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  payments_PaymentLessonvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "ec37933a",
  "5ae071c7"
  
)

/* harmony default export */ var PaymentLesson = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */


installComponents_default()(component, {VChip: VChip["a" /* default */]})


/***/ }),

/***/ 901:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(909);
/* harmony import */ var _src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
/* harmony import */ var _transitions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(67);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(66);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9);
/* harmony import */ var _mixins_groupable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(47);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(7);
/* harmony import */ var _mixins_toggleable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(10);
/* harmony import */ var _mixins_routable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(18);
/* harmony import */ var _mixins_sizeable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(49);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(3);
// Styles

 // Components


 // Mixins






 // Utilities


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(_mixins_colorable__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"], _mixins_sizeable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"], _mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], _mixins_themeable__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"], Object(_mixins_groupable__WEBPACK_IMPORTED_MODULE_5__[/* factory */ "a"])('chipGroup'), Object(_mixins_toggleable__WEBPACK_IMPORTED_MODULE_7__[/* factory */ "b"])('inputValue')).extend({
  name: 'v-chip',
  props: {
    active: {
      type: Boolean,
      default: true
    },
    activeClass: {
      type: String,

      default() {
        if (!this.chipGroup) return '';
        return this.chipGroup.activeClass;
      }

    },
    close: Boolean,
    closeIcon: {
      type: String,
      default: '$delete'
    },
    closeLabel: {
      type: String,
      default: '$vuetify.close'
    },
    disabled: Boolean,
    draggable: Boolean,
    filter: Boolean,
    filterIcon: {
      type: String,
      default: '$complete'
    },
    label: Boolean,
    link: Boolean,
    outlined: Boolean,
    pill: Boolean,
    tag: {
      type: String,
      default: 'span'
    },
    textColor: String,
    value: null
  },
  data: () => ({
    proxyClass: 'v-chip--active'
  }),
  computed: {
    classes() {
      return {
        'v-chip': true,
        ..._mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].options.computed.classes.call(this),
        'v-chip--clickable': this.isClickable,
        'v-chip--disabled': this.disabled,
        'v-chip--draggable': this.draggable,
        'v-chip--label': this.label,
        'v-chip--link': this.isLink,
        'v-chip--no-color': !this.color,
        'v-chip--outlined': this.outlined,
        'v-chip--pill': this.pill,
        'v-chip--removable': this.hasClose,
        ...this.themeClasses,
        ...this.sizeableClasses,
        ...this.groupClasses
      };
    },

    hasClose() {
      return Boolean(this.close);
    },

    isClickable() {
      return Boolean(_mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].options.computed.isClickable.call(this) || this.chipGroup);
    }

  },

  created() {
    const breakingProps = [['outline', 'outlined'], ['selected', 'input-value'], ['value', 'active'], ['@input', '@active.sync']];
    /* istanbul ignore next */

    breakingProps.forEach(([original, replacement]) => {
      if (this.$attrs.hasOwnProperty(original)) Object(_util_console__WEBPACK_IMPORTED_MODULE_10__[/* breaking */ "a"])(original, replacement, this);
    });
  },

  methods: {
    click(e) {
      this.$emit('click', e);
      this.chipGroup && this.toggle();
    },

    genFilter() {
      const children = [];

      if (this.isActive) {
        children.push(this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"], {
          staticClass: 'v-chip__filter',
          props: {
            left: true
          }
        }, this.filterIcon));
      }

      return this.$createElement(_transitions__WEBPACK_IMPORTED_MODULE_2__[/* VExpandXTransition */ "b"], children);
    },

    genClose() {
      return this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"], {
        staticClass: 'v-chip__close',
        props: {
          right: true,
          size: 18
        },
        attrs: {
          'aria-label': this.$vuetify.lang.t(this.closeLabel)
        },
        on: {
          click: e => {
            e.stopPropagation();
            e.preventDefault();
            this.$emit('click:close');
            this.$emit('update:active', false);
          }
        }
      }, this.closeIcon);
    },

    genContent() {
      return this.$createElement('span', {
        staticClass: 'v-chip__content'
      }, [this.filter && this.genFilter(), this.$slots.default, this.hasClose && this.genClose()]);
    }

  },

  render(h) {
    const children = [this.genContent()];
    let {
      tag,
      data
    } = this.generateRouteLink();
    data.attrs = { ...data.attrs,
      draggable: this.draggable ? 'true' : undefined,
      tabindex: this.chipGroup && !this.disabled ? 0 : data.attrs.tabindex
    };
    data.directives.push({
      name: 'show',
      value: this.active
    });
    data = this.setBackgroundColor(this.color, data);
    const color = this.textColor || this.outlined && this.color;
    return h(tag, this.setTextColor(color, data), children);
  }

}));

/***/ }),

/***/ 909:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(910);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("197fcea4", content, true)

/***/ }),

/***/ 910:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-chip:not(.v-chip--outlined).accent,.v-chip:not(.v-chip--outlined).error,.v-chip:not(.v-chip--outlined).info,.v-chip:not(.v-chip--outlined).primary,.v-chip:not(.v-chip--outlined).secondary,.v-chip:not(.v-chip--outlined).success,.v-chip:not(.v-chip--outlined).warning{color:#fff}.theme--light.v-chip{border-color:rgba(0,0,0,.12);color:rgba(0,0,0,.87)}.theme--light.v-chip:not(.v-chip--active){background:#e0e0e0}.theme--light.v-chip:hover:before{opacity:.04}.theme--light.v-chip--active:before,.theme--light.v-chip--active:hover:before,.theme--light.v-chip:focus:before{opacity:.12}.theme--light.v-chip--active:focus:before{opacity:.16}.theme--dark.v-chip{border-color:hsla(0,0%,100%,.12);color:#fff}.theme--dark.v-chip:not(.v-chip--active){background:#555}.theme--dark.v-chip:hover:before{opacity:.08}.theme--dark.v-chip--active:before,.theme--dark.v-chip--active:hover:before,.theme--dark.v-chip:focus:before{opacity:.24}.theme--dark.v-chip--active:focus:before{opacity:.32}.v-chip{align-items:center;cursor:default;display:inline-flex;line-height:20px;max-width:100%;outline:none;overflow:hidden;padding:0 12px;position:relative;text-decoration:none;transition-duration:.28s;transition-property:box-shadow,opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);vertical-align:middle;white-space:nowrap}.v-chip:before{background-color:currentColor;bottom:0;border-radius:inherit;content:\"\";left:0;opacity:0;position:absolute;pointer-events:none;right:0;top:0}.v-chip .v-avatar{height:24px!important;min-width:24px!important;width:24px!important}.v-chip .v-icon{font-size:24px}.v-application--is-ltr .v-chip .v-avatar--left,.v-application--is-ltr .v-chip .v-icon--left{margin-left:-6px;margin-right:6px}.v-application--is-ltr .v-chip .v-avatar--right,.v-application--is-ltr .v-chip .v-icon--right,.v-application--is-rtl .v-chip .v-avatar--left,.v-application--is-rtl .v-chip .v-icon--left{margin-left:6px;margin-right:-6px}.v-application--is-rtl .v-chip .v-avatar--right,.v-application--is-rtl .v-chip .v-icon--right{margin-left:-6px;margin-right:6px}.v-chip:not(.v-chip--no-color) .v-icon{color:inherit}.v-chip .v-chip__close.v-icon{font-size:18px;max-height:18px;max-width:18px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right{margin-right:-4px}.v-application--is-rtl .v-chip .v-chip__close.v-icon.v-icon--right{margin-left:-4px}.v-chip .v-chip__close.v-icon:active,.v-chip .v-chip__close.v-icon:focus,.v-chip .v-chip__close.v-icon:hover{opacity:.72}.v-chip .v-chip__content{align-items:center;display:inline-flex;height:100%;max-width:100%}.v-chip--active .v-icon{color:inherit}.v-chip--link:before{transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-chip--link:focus:before{opacity:.32}.v-chip--clickable{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip--clickable:active{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-chip--disabled{opacity:.4;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip__filter{max-width:24px}.v-chip__filter.v-icon{color:inherit}.v-chip__filter.expand-x-transition-enter,.v-chip__filter.expand-x-transition-leave-active{margin:0}.v-chip--pill .v-chip__filter{margin-right:0 16px 0 0}.v-chip--pill .v-avatar{height:32px!important;width:32px!important}.v-application--is-ltr .v-chip--pill .v-avatar--left{margin-left:-12px}.v-application--is-ltr .v-chip--pill .v-avatar--right,.v-application--is-rtl .v-chip--pill .v-avatar--left{margin-right:-12px}.v-application--is-rtl .v-chip--pill .v-avatar--right{margin-left:-12px}.v-chip--label{border-radius:4px!important}.v-chip.v-chip--outlined{border-width:thin;border-style:solid}.v-chip.v-chip--outlined.v-chip--active:before{opacity:.08}.v-chip.v-chip--outlined .v-icon{color:inherit}.v-chip.v-chip--outlined.v-chip.v-chip{background-color:transparent!important}.v-chip.v-chip--selected{background:transparent}.v-chip.v-chip--selected:after{opacity:.28}.v-chip.v-size--x-small{border-radius:8px;font-size:10px;height:16px}.v-chip.v-size--small{border-radius:12px;font-size:12px;height:24px}.v-chip.v-size--default{border-radius:16px;font-size:14px;height:32px}.v-chip.v-size--large{border-radius:27px;font-size:16px;height:54px}.v-chip.v-size--x-large{border-radius:33px;font-size:18px;height:66px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

};;
//# sourceMappingURL=payments-payment-lesson.js.map