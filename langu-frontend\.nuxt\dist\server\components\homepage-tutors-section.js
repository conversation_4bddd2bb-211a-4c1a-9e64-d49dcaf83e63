exports.ids = [48];
exports.modules = {

/***/ 1071:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1105);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("20c2c1c7", content, true)

/***/ }),

/***/ 1105:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".slick-track[data-v-e4caeaf8]{position:relative;top:0;left:0;display:block;transform:translateZ(0)}.slick-track.slick-center[data-v-e4caeaf8]{margin-left:auto;margin-right:auto}.slick-track[data-v-e4caeaf8]:after,.slick-track[data-v-e4caeaf8]:before{display:table;content:\"\"}.slick-track[data-v-e4caeaf8]:after{clear:both}.slick-loading .slick-track[data-v-e4caeaf8]{visibility:hidden}.slick-slide[data-v-e4caeaf8]{display:none;float:left;height:100%;min-height:1px}[dir=rtl] .slick-slide[data-v-e4caeaf8]{float:right}.slick-slide img[data-v-e4caeaf8]{display:block}.slick-slide.slick-loading img[data-v-e4caeaf8]{display:none}.slick-slide.dragging img[data-v-e4caeaf8]{pointer-events:none}.slick-initialized .slick-slide[data-v-e4caeaf8]{display:block}.slick-loading .slick-slide[data-v-e4caeaf8]{visibility:hidden}.slick-vertical .slick-slide[data-v-e4caeaf8]{display:block;height:auto;border:1px solid transparent}.slick-arrow.slick-hidden[data-v-21137603]{display:none}.slick-slider[data-v-3d1a4f76]{position:relative;display:block;box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;touch-action:pan-y;-webkit-tap-highlight-color:transparent}.slick-list[data-v-3d1a4f76]{position:relative;display:block;overflow:hidden;margin:0;padding:0;transform:translateZ(0)}.slick-list[data-v-3d1a4f76]:focus{outline:none}.slick-list.dragging[data-v-3d1a4f76]{cursor:pointer;cursor:hand}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1259:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1351);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("6e05f7b8", content, true, context)
};

/***/ }),

/***/ 1350:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TutorsSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1259);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TutorsSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TutorsSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TutorsSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TutorsSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1351:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(68);
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(622);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".tutors{position:relative;padding:220px 0 132px}@media only screen and (max-width:1439px){.tutors{padding:150px 0 110px}}@media only screen and (max-width:991px){.tutors{padding:150px 0 80px}}@media only screen and (max-width:767px){.tutors{padding:90px 0 80px}}.tutors-decoration--after,.tutors-decoration--before{position:absolute;width:100%}.tutors-decoration--before{top:0;left:0;width:100%;max-width:874px;height:1015px;max-height:1085px}@media only screen and (max-width:1643px){.tutors-decoration--before{top:120px;max-width:690px;height:820px}}@media only screen and (max-width:1439px){.tutors-decoration--before{top:55px;width:calc(100% - 45px);max-width:480px;height:600px}}@media only screen and (max-width:991px){.tutors-decoration--before{top:0;height:400px}}@media only screen and (max-width:767px){.tutors-decoration--before{height:400px}}.tutors-decoration--before .v-image__image{background-position:0}.tutors-decoration--after{right:0;top:145px;max-width:437px;height:100%;max-height:679px}@media only screen and (max-width:1643px){.tutors-decoration--after{top:185px;max-width:305px;max-height:479px}}@media only screen and (max-width:1439px){.tutors-decoration--after{top:240px;max-width:190px;max-height:295px}}@media only screen and (max-width:991px){.tutors-decoration--after{top:auto;bottom:50px}}.tutors-decoration--after .v-image__image{background-position:100%}.tutors-carousel{position:relative;margin-top:96px;z-index:2}@media only screen and (max-width:1643px){.tutors-carousel{margin-top:50px}}@media only screen and (min-width:1440px){.tutors-carousel{margin-left:-48px;max-width:1262px}}@media only screen and (max-width:1439px){.tutors-carousel{width:calc(100% + 20px);padding-bottom:100px}}@media only screen and (max-width:991px){.tutors-carousel{width:calc(100% + 15px)}}.tutors-carousel .slick-slide{padding:0 37px}@media only screen and (max-width:1439px){.tutors-carousel .slick-slide{padding:0 20px 0 0}}@media only screen and (max-width:991px){.tutors-carousel .slick-slide{padding:0 15px 0 0}.tutors-carousel .slick-slide>div{display:flex;justify-content:center}}.tutors-carousel-item{display:inline-flex!important;color:#fff;background:linear-gradient(126.15deg,var(--v-green-base),var(--v-primary-base) 102.93%),#c4c4c4;border-radius:24px}@media only screen and (min-width:1440px){.tutors-carousel-item{max-width:594px}}@media only screen and (max-width:991px){.tutors-carousel-item{position:relative;flex-direction:column;max-width:640px}}.tutors-carousel-item-left{width:238px;padding:12px 32px 18px;border-right:1px solid hsla(0,0%,100%,.1)}@media only screen and (max-width:1439px){.tutors-carousel-item-left{width:215px;padding:20px 24px}}@media only screen and (max-width:991px){.tutors-carousel-item-left{width:100%;padding:20px 24px 6px;border-bottom:1px solid hsla(0,0%,100%,.1);border-right:none}.tutors-carousel-item-left>div{display:flex;justify-content:space-between}}@media only screen and (max-width:479px){.tutors-carousel-item-left{padding:20px 15px 6px}}.tutors-carousel-item-right{position:relative;width:calc(100% - 238px);padding:32px 32px 82px}@media only screen and (max-width:1439px){.tutors-carousel-item-right{padding:20px 24px 80px;width:calc(100% - 215px)}}@media only screen and (max-width:991px){.tutors-carousel-item-right{width:100%}}@media only screen and (max-width:479px){.tutors-carousel-item-right{padding:32px 15px 95px}}.tutors-carousel-item-image{position:relative;width:163px;height:163px;margin:0 auto 4px}@media only screen and (min-width:992px)and (max-width:1439px){.tutors-carousel-item-image{width:140px;height:140px}.tutors-carousel-item-image .v-avatar{width:140px!important;height:140px!important}}@media only screen and (max-width:991px){.tutors-carousel-item-image{margin:0 38px 0 0}}@media only screen and (max-width:479px){.tutors-carousel-item-image{width:90px;height:90px;margin:0 25px 0 0}.tutors-carousel-item-image .v-avatar{width:90px!important;height:90px!important}}.tutors-carousel-item-image .flags{position:absolute;bottom:5px;right:-24px}@media only screen and (max-width:479px){.tutors-carousel-item-image .flags{width:38px;bottom:0;right:-20px}}.tutors-carousel-item-image .flags-item{margin-top:2px;border-radius:8px;overflow:hidden}.tutors-carousel-item-name{font-size:24px;font-weight:700}@media only screen and (max-width:991px){.tutors-carousel-item-name{text-align:right}}@media only screen and (max-width:639px){.tutors-carousel-item-name{font-size:18px}}.tutors-carousel-item-name a{color:#fff!important;text-decoration:none}.tutors-carousel-item-rating{display:flex;align-items:center;margin-top:8px}@media only screen and (max-width:991px){.tutors-carousel-item-rating{flex-direction:column;align-items:flex-end}}.tutors-carousel-item-rating span{display:inline-block;margin-left:8px;font-size:13px;font-weight:700;line-height:.8;color:var(--v-orange-base)}@media only screen and (max-width:991px){.tutors-carousel-item-rating span{display:block;margin:10px 0 0}}.tutors-carousel-item-list{margin-top:12px;padding-left:0!important;list-style-type:none}@media only screen and (max-width:991px){.tutors-carousel-item-list{display:flex;flex-wrap:wrap;margin-top:18px;margin-left:-20px}}.tutors-carousel-item-list>li{position:relative;margin-top:2px;padding-left:19px;font-size:18px;line-height:1.55}@media only screen and (min-width:992px)and (max-width:1439px){.tutors-carousel-item-list>li{margin-top:4px;font-size:16px;line-height:1.2}}@media only screen and (max-width:991px){.tutors-carousel-item-list>li{margin:0 0 10px 24px}}.tutors-carousel-item-list>li:before{content:\"\";position:absolute;top:7px;left:0;width:11px;height:9px;background-size:contain;background-repeat:no-repeat;background-position:50%;background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ")}.tutors-carousel-item-text{font-size:18px;line-height:1.33}.tutors-carousel-item-text span{display:block;margin:25px 0 8px;font-weight:400}.tutors-carousel-item-languages{font-weight:300}.tutors-carousel-item-button{position:absolute;width:100%;left:0;bottom:20px}@media only screen and (max-width:479px){.tutors-carousel-item-button{padding:0 24px}}.tutors-carousel-item-button .v-btn{min-width:222px!important}@media only screen and (max-width:1439px){.tutors-carousel-item-button .v-btn{min-width:180px!important}}@media only screen and (max-width:479px){.tutors-carousel-item-button .v-btn{min-width:100%!important;width:100%!important}}.tutors .slick-arrow{position:absolute;top:50%;transform:translateY(-50%)}@media only screen and (max-width:1439px){.tutors .slick-arrow{top:auto;bottom:-100px;transform:translateX(-50%)}}.tutors .slick-prev{left:calc(50vw + 626px)}@media only screen and (max-width:1643px){.tutors .slick-prev{left:auto;right:-82px}}@media only screen and (max-width:1439px){.tutors .slick-prev{left:calc(50% - 65px);right:auto}}@media only screen and (max-width:991px){.tutors .slick-prev{left:calc(50% - 60px)}}.tutors .slick-next{left:calc(50vw + 706px)}@media only screen and (max-width:1643px){.tutors .slick-next{left:auto;right:-162px}}@media only screen and (max-width:1439px){.tutors .slick-next{left:calc(50% + 35px);right:auto}}@media only screen and (max-width:991px){.tutors .slick-next{left:calc(50% + 40px)}}.tutors-button{display:flex;justify-content:center;margin-top:90px}@media only screen and (max-width:1643px){.tutors-button{margin-top:80px}}@media only screen and (max-width:1439px){.tutors-button{margin-top:50px}}.tutors-button .v-btn{min-width:285px!important}@media only screen and (max-width:479px){.tutors-button .v-btn{min-width:100%!important;width:100%!important}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1415:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/TutorsSection.vue?vue&type=template&id=0135b7d4&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"tutors"},[_vm._ssrNode("<div class=\"tutors-decoration--before\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(135),"contain":"","options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{staticClass:"section-head-title",staticStyle:{"color":"#262626","-webkit-text-fill-color":"#262626"}},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.tutors_section_title'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"section-head-subtitle text--gradient"},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.tutors_section_subtitle'))+"\n          ")])])])],1)],1),_vm._ssrNode(" "),_c('v-container',{staticClass:"py-0",attrs:{"fluid":""}},[_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"tutors-carousel"},[_c('client-only',[_c('VueSlickCarousel',_vm._b({},'VueSlickCarousel',_vm.tutorsCarouselSettings,false),_vm._l((_vm.teachers),function(t,idx){return _c('div',{key:idx,ref:"tutorsCarouselItem",refInFor:true,staticClass:"tutors-carousel-item"},[_c('div',{staticClass:"tutors-carousel-item-left"},[_c('div',[_c('nuxt-link',{attrs:{"to":t.profileLink}},[_c('div',{staticClass:"tutors-carousel-item-image"},[_c('v-avatar',{attrs:{"width":"163","height":"163"}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(
                                t.avatarsResized,
                                'user_thumb_163x163'
                              ),"srcset":_vm.getSrcSetAvatar(
                                t.avatarsResized,
                                'user_thumb_163x163',
                                'user_thumb_326x326'
                              ),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),(t.languagesTaught.length)?_c('div',{staticClass:"flags"},_vm._l((t.languagesTaught),function(languageTaught){return _c('div',{key:languageTaught.isoCode,staticClass:"flags-item"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (languageTaught.isoCode) + ".svg"),"width":"52","height":"36","contain":"","options":{ rootMargin: '50%' }}})],1)}),0):_vm._e()],1)]),_vm._v(" "),_c('div',[_c('div',{staticClass:"tutors-carousel-item-name text-uppercase"},[_c('nuxt-link',{attrs:{"to":t.profileLink}},[_vm._v("\n                          "+_vm._s(t.firstName)+"\n                        ")])],1),_vm._v(" "),_c('div',{staticClass:"tutors-carousel-item-rating"},[_c('div',[_c('v-img',{attrs:{"src":__webpack_require__(119),"width":"68","contain":"","options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('span',[_vm._v("("+_vm._s(_vm.$tc('review', t.countFeedbacks))+")")])])])],1),_vm._v(" "),(t.languagesTaught.length)?_c('ul',{staticClass:"tutors-carousel-item-list"},_vm._l((t.languagesTaught),function(lt,index){return _c('li',{key:index},[_vm._v("\n                      "+_vm._s(lt.name)+" "+_vm._s(_vm.$t('teacher'))+"\n                    ")])}),0):_vm._e()]),_vm._v(" "),_c('div',{staticClass:"tutors-carousel-item-right"},[_c('div',{staticClass:"tutors-carousel-item-text"},[_vm._v("\n                    “"+_vm._s(t.description)+"”\n                    "),(t.specialities && t.specialities.length)?[_c('span',[_vm._v(_vm._s(_vm.$t('top_specialities'))+":")]),_vm._v(" "),_c('div',{staticClass:"tutors-carousel-item-languages"},[_vm._v("\n                        "+_vm._s(_vm._f("specialitiesStr")(t.specialities))+"\n                      ")])]:_vm._e()],2),_vm._v(" "),(_vm.$vuetify.breakpoint.mdAndUp)?_c('div',{staticClass:"tutors-carousel-item-button text-center d-none d-md-block"},[_c('v-btn',{attrs:{"to":t.profileLink,"large":"","color":"greyDark"}},[(t.bookLesson.freeTrial)?[_vm._v("\n                        "+_vm._s(_vm.$t('free_trial'))+"\n                      ")]:[_vm._v("\n                        "+_vm._s(_vm.$t('trial'))+":  "+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.getPrice(t.bookLesson.price))+"\n                      ")]],2)],1):_vm._e()]),_vm._v(" "),(_vm.$vuetify.breakpoint.smAndDown)?_c('div',{staticClass:"tutors-carousel-item-button text-center d-md-none"},[_c('v-btn',{attrs:{"to":t.profileLink,"large":"","color":"greyDark"}},[(t.bookLesson.freeTrial)?[_vm._v("\n                      "+_vm._s(_vm.$t('free_trial'))+"\n                    ")]:[_vm._v("\n                      "+_vm._s(_vm.$t('trial'))+":  "+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.getPrice(t.bookLesson.price))+"\n                    ")]],2)],1):_vm._e()])}),0)],1)],1)])],1)],1),_vm._ssrNode(" "),_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"tutors-button"},[_c('v-btn',{attrs:{"to":_vm.localePath({
                name: 'teacher-listing',
                params: {
                  utm_source: 'homepage',
                  utm_medium: 'teacher-cards',
                },
              }),"large":"","color":"primary"}},[_vm._v("\n            "+_vm._s(_vm.$t('meet_all_our_teachers'))+"\n          ")])],1)])],1)],1),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"tutors-decoration--after\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(136),"contain":"","options":{ rootMargin: '50%' }}})],1)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/TutorsSection.vue?vue&type=template&id=0135b7d4&

// EXTERNAL MODULE: external "vue-slick-carousel"
var external_vue_slick_carousel_ = __webpack_require__(859);
var external_vue_slick_carousel_default = /*#__PURE__*/__webpack_require__.n(external_vue_slick_carousel_);

// EXTERNAL MODULE: ./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css
var vue_slick_carousel = __webpack_require__(1071);

// EXTERNAL MODULE: ./mixins/Avatars.vue + 2 modules
var Avatars = __webpack_require__(932);

// EXTERNAL MODULE: ./helpers/index.js
var helpers = __webpack_require__(501);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/TutorsSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var TutorsSectionvue_type_script_lang_js_ = ({
  name: 'TutorsSection',
  components: {
    VueSlickCarousel: external_vue_slick_carousel_default.a
  },
  filters: {
    specialitiesStr(arr) {
      let str = '';

      for (let i = 0; i < 3; i++) {
        var _arr$i$speciality$nam, _arr$i, _arr$i$speciality;

        str += (_arr$i$speciality$nam = (_arr$i = arr[i]) === null || _arr$i === void 0 ? void 0 : (_arr$i$speciality = _arr$i.speciality) === null || _arr$i$speciality === void 0 ? void 0 : _arr$i$speciality.name) !== null && _arr$i$speciality$nam !== void 0 ? _arr$i$speciality$nam : '';

        if (i < 3 - 1) {
          str += ', ';
        }
      }

      return str;
    }

  },
  mixins: [Avatars["a" /* default */]],
  props: {
    teachers: {
      type: Array,
      required: true
    }
  },

  data() {
    return {
      getPrice: helpers["getPrice"],
      tutorsCarouselSettings: {
        dots: false,
        focusOnSelect: true,
        infinite: true,
        speed: 800,
        slidesToShow: 2,
        slidesToScroll: 1,
        responsive: [{
          breakpoint: 992,
          settings: {
            slidesToShow: 1
          }
        }]
      }
    };
  },

  computed: {
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    }

  }
});
// CONCATENATED MODULE: ./components/homepage/TutorsSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_TutorsSectionvue_type_script_lang_js_ = (TutorsSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/VAvatar.js
var VAvatar = __webpack_require__(830);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/TutorsSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1350)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_TutorsSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "f3947edc"
  
)

/* harmony default export */ var TutorsSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */







installComponents_default()(component, {VAvatar: VAvatar["a" /* default */],VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 932:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/Avatars.vue?vue&type=script&lang=js&
/* harmony default export */ var Avatarsvue_type_script_lang_js_ = ({
  methods: {
    getSrcAvatar(images, property, defaultImage = 'avatar.png') {
      return images !== null && images !== void 0 && images[property] ? images[property] : __webpack_require__(511)(`./${defaultImage}`);
    },

    getSrcSetAvatar(images, property1, property2) {
      return images !== null && images !== void 0 && images[property1] && images !== null && images !== void 0 && images[property2] ? `
            ${images[property1]} 1x,
            ${images[property2]} 2x,
          ` : '';
    }

  }
});
// CONCATENATED MODULE: ./mixins/Avatars.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_Avatarsvue_type_script_lang_js_ = (Avatarsvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/Avatars.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_Avatarsvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "0af9ff4e"
  
)

/* harmony default export */ var Avatars = __webpack_exports__["a"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=homepage-tutors-section.js.map