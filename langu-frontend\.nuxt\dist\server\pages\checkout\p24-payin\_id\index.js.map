{"version": 3, "file": "pages/checkout/p24-payin/_id/index.js", "sources": ["webpack:///./pages/checkout/p24-payin/_id/index.vue?7c03", "webpack:///./pages/checkout/p24-payin/_id/index.vue?5fe6", "webpack:///./pages/checkout/p24-payin/_id/index.vue?f5ee", "webpack:///./pages/checkout/p24-payin/_id/index.vue?d32d", "webpack:///./pages/checkout/p24-payin/_id/index.vue", "webpack:///./pages/checkout/p24-payin/_id/index.vue?11e3", "webpack:///./pages/checkout/p24-payin/_id/index.vue?5541"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"352751b0\", content, true, context)\n};", "export * from \"-!../../../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".p24-payin{max-width:620px;min-height:410px;padding:56px 0 80px}@media only screen and (max-width:991px){.p24-payin{padding:38px 0 64px}}@media only screen and (max-width:479px){.p24-payin{padding:28px 0 45px}}.p24-payin--error{max-width:800px}.p24-payin a{color:var(--v-success-base)!important;text-decoration:none}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-col',{staticClass:\"col-12 pa-0\"},[_c('div',{class:['p24-payin mx-auto', { 'p24-payin--error': _vm.isFailure }]},[_c('v-container',{staticClass:\"py-0\",attrs:{\"fluid\":\"\"}},[_c('v-row',[_c('v-col',{staticClass:\"col-12\"},[(_vm.isFailure)?[_c('div',{staticClass:\"text-center\"},[_c('div',{domProps:{\"innerHTML\":_vm._s(\n                  _vm.$t(\n                    'przelewy24_payment_failure_try_again_or_contact_langu_customer_service'\n                  )\n                )}}),_vm._v(\" \"),(_vm.username)?_c('div',{staticClass:\"mt-6\"},[_c('v-btn',{staticClass:\"gradient font-weight-medium\",attrs:{\"to\":(\"/teacher/\" + _vm.username)}},[_c('div',{staticClass:\"text--gradient\"},[_vm._v(\"\\n                    \"+_vm._s(_vm.buttonText)+\"\\n                  \")])])],1):_vm._e()])]:[_c('div',{staticClass:\"text-center\"},[_c('div',{domProps:{\"innerHTML\":_vm._s(_vm.$t('confirming_payment_p24'))}}),_vm._v(\" \"),_c('p',[_c('a',{staticClass:\"text--gradient\",attrs:{\"href\":\"https://instagram.com/heylangu\",\"target\":\"_blank\"}},[_vm._v(\"\\n                  https://instagram.com/heylangu\\n                \")])]),_vm._v(\" \"),_c('div',{staticClass:\"spinner mt-6\"},[_c('v-img',{staticClass:\"mx-auto\",attrs:{\"src\":require('~/assets/images/ajax-loader.gif'),\"width\":\"48\",\"height\":\"48\"}})],1)])]],2)],1)],1)],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nconst CALL_LIMIT = 100\n\nexport default {\n  name: 'P24Payin',\n  data() {\n    return {\n      called: 0,\n      intervalId: null,\n      username: null,\n      isFailure: false,\n    }\n  },\n  computed: {\n    buttonText() {\n      return `${process.env.NUXT_ENV_URL}/teacher/${this.username}`\n    },\n  },\n  beforeMount() {\n    this.username = window.localStorage.getItem('teacher-username')\n\n    this.intervalId = window.setInterval(() => {\n      this.called++\n\n      if (this.called === CALL_LIMIT) {\n        this.clearInterval()\n\n        this.isFailure = true\n      }\n\n      this.$store.dispatch('loadingAllow', false)\n      this.$store\n        .dispatch('purchase/p24Paying', this.$route.params.id)\n        .then(async (data) => {\n          if (+data?.confirmed === 1) {\n            this.clearInterval()\n            this.clearLocalStorage()\n\n            if (this.$store.getters['teacher_profile/isSelectedTrial']) {\n              await this.$cookiz.set('confirmation_page_allowed', 1, {\n                domain: process.env.NUXT_ENV_COOKIE_DOMAIN,\n                path: '/',\n              })\n\n              return this.$router.push('/user/lessons/confirmation')\n            } else {\n              await this.$cookiz.set('thank_you_page_allowed', 1, {\n                domain: process.env.NUXT_ENV_COOKIE_DOMAIN,\n                path: '/',\n              })\n\n              return this.$router.push('/user/lessons/thank-you')\n            }\n          }\n        })\n        .catch((e) => {\n          this.isFailure = true\n        })\n        .finally(() => {\n          this.$store.dispatch('loadingAllow', true)\n        })\n    }, 5000)\n  },\n  beforeDestroy() {\n    this.clearInterval()\n    this.clearLocalStorage()\n  },\n  methods: {\n    clearInterval() {\n      clearInterval(this.intervalId)\n    },\n    clearLocalStorage() {\n      window.localStorage.removeItem('teacher-username')\n    },\n  },\n}\n", "import mod from \"-!../../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=cbef3c2a&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./index.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"77fb9346\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VContainer } from 'vuetify/lib/components/VGrid';\nimport { VImg } from 'vuetify/lib/components/VImg';\nimport { VRow } from 'vuetify/lib/components/VGrid';\ninstallComponents(component, {VBtn,VCol,VContainer,VImg,VRow})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AACA;AAIA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAKA;AACA;AACA;AACA;AACA;AAFA;AAKA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;AAhEA;;AC3DA;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}