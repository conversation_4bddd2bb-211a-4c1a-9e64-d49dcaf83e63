{"version": 3, "file": "components/star-rating.js", "sources": ["webpack:///./components/StarRating.vue?8931", "webpack:///./components/StarRating.vue?f5be", "webpack:///./components/StarRating.vue?a800", "webpack:///./components/StarRating.vue?5f4c", "webpack:///./components/StarRating.vue", "webpack:///./components/StarRating.vue?9bca", "webpack:///./components/StarRating.vue?bad1"], "sourcesContent": ["export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StarRating.vue?vue&type=style&index=0&id=1645fb89&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".score[data-v-1645fb89]{display:flex;align-items:center;height:18px;font-size:12px;line-height:.8;font-weight:700;letter-spacing:.1px;color:var(--v-orange-base)}@media only screen and (max-width:1215px){.score[data-v-1645fb89]{justify-content:flex-end}}.score>div[data-v-1645fb89]{width:65px;display:flex;margin-left:2px}@media only screen and (max-width:1215px){.score>div[data-v-1645fb89]{width:auto}}.score svg[data-v-1645fb89]:not(:first-child){margin-left:1px}.score--large[data-v-1645fb89]{font-size:18px}@media only screen and (max-width:1215px){.score--large[data-v-1645fb89]{font-size:16px}}.score--large>div[data-v-1645fb89]{width:112px;margin-left:8px}@media only screen and (max-width:1215px){.score--large>div[data-v-1645fb89]{width:84px}}.score--large svg[data-v-1645fb89]:not(:first-child){margin-left:3px}@media only screen and (max-width:1215px){.score--large svg[data-v-1645fb89]:not(:first-child){margin-left:1px}}@media only screen and (max-width:1215px){.score--large svg[data-v-1645fb89]{width:16px!important;height:16px!important}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StarRating.vue?vue&type=style&index=0&id=1645fb89&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"1f907d7b\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:['score', { 'score--large': _vm.large }]},[_vm._ssrNode(\"<span data-v-1645fb89>\"+_vm._ssrEscape(_vm._s(_vm.value_.toFixed(1)))+\"</span> <div data-v-1645fb89>\"+(_vm._ssrList((_vm.stars),function(i){return (\"<svg\"+(_vm._ssrAttr(\"width\",_vm.width))+(_vm._ssrAttr(\"height\",_vm.height))+\" viewBox=\\\"0 0 12 12\\\" data-v-1645fb89><use\"+(_vm._ssrAttr(\"xlink:href\",_vm.iconFilledStar))+\" data-v-1645fb89></use></svg>\")}))+\" \"+((_vm.isHasHalf)?(\"<svg\"+(_vm._ssrAttr(\"width\",_vm.width))+(_vm._ssrAttr(\"height\",_vm.height))+\" viewBox=\\\"0 0 12 12\\\" data-v-1645fb89><use\"+(_vm._ssrAttr(\"xlink:href\",_vm.iconFilledHalfStar))+\" data-v-1645fb89></use></svg>\"):\"<!---->\")+\"</div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'StarRating',\n  props: {\n    value: {\n      type: Number,\n      required: true,\n    },\n    large: {\n      type: Boolean,\n      required: false,\n    },\n  },\n  data() {\n    return {\n      iconFilledStar: `${require('~/assets/images/icon-sprite.svg')}#filledStar`,\n      iconFilledHalfStar: `${require('~/assets/images/icon-sprite.svg')}#filledHalfStar`,\n    }\n  },\n  computed: {\n    width() {\n      return this.large ? 20 : 12\n    },\n    height() {\n      return this.large ? 20 : 12\n    },\n    value_() {\n      return Math.round(this.value * 10) / 10\n    },\n    isRoundToLess() {\n      const rest = Math.round((this.value_ % 1) * 10)\n\n      return rest <= 5 && rest !== 0\n    },\n    roundToLessHalf() {\n      return this.isRoundToLess\n        ? Math.floor(this.value_ * 2) / 2\n        : Math.ceil(this.value_ * 2) / 2\n    },\n    stars() {\n      return this.isRoundToLess\n        ? Math.floor(this.roundToLessHalf)\n        : Math.ceil(this.roundToLessHalf)\n    },\n    isHasHalf() {\n      return (this.isRoundToLess && this.value_ !== 5) || this.value_ < 0.5\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StarRating.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StarRating.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./StarRating.vue?vue&type=template&id=1645fb89&scoped=true&\"\nimport script from \"./StarRating.vue?vue&type=script&lang=js&\"\nexport * from \"./StarRating.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./StarRating.vue?vue&type=style&index=0&id=1645fb89&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"1645fb89\",\n  \"743e07b2\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AACA;AASA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AA5BA;AAlBA;;ACtBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}