{"version": 3, "file": "components/user-settings-qualification-success-dialog.js", "sources": ["webpack:///./components/user-settings/QualificationSuccessDialog.vue?159f", "webpack:///./components/user-settings/QualificationSuccessDialog.vue", "webpack:///./components/user-settings/QualificationSuccessDialog.vue?e4d7", "webpack:///./components/user-settings/QualificationSuccessDialog.vue?ce6a"], "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{\"dialog\":_vm.isShownDialog,\"max-width\":\"418\",\"custom-class\":\"qualification-added text-center\"}},_vm.$listeners),[_c('div',[_c('v-img',{staticClass:\"mx-auto mb-3\",attrs:{\"src\":require('~/assets/images/success-icon-gradient.svg'),\"width\":\"56\",\"height\":\"56\"}}),_vm._v(\" \"),_c('div',{staticClass:\"qualification-added-text\"},[_vm._v(\"\\n      \"+_vm._s(_vm.$t(\n          'thank_you_for_saving_your_qualification_it_will_shortly_be_verified_by_langu_admin'\n        ))+\"\\n    \")])],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'QualificationSuccessDialog',\n  props: {\n    isShownDialog: {\n      type: Boolean,\n      required: true,\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./QualificationSuccessDialog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./QualificationSuccessDialog.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./QualificationSuccessDialog.vue?vue&type=template&id=1fa2a67e&\"\nimport script from \"./QualificationSuccessDialog.vue?vue&type=script&lang=js&\"\nexport * from \"./QualificationSuccessDialog.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"40ac936b\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {LDialog: require('D:/languworks/langu-frontend/components/LDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VImg})\n"], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AADA;AAFA;;AC1BA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}