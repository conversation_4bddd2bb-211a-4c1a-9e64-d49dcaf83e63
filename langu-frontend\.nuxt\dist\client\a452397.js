(window.webpackJsonp=window.webpackJsonp||[]).push([[46,16,39,40,43,56],{1372:function(t,e,o){var n=o(43);t.exports=function(t){return n(Set.prototype.values,t)}},1384:function(t,e,o){"use strict";var n=o(43),r=o(79),h=o(32);t.exports=function(){for(var t=h(this),e=r(t.add),o=0,l=arguments.length;o<l;o++)n(e,t,arguments[o]);return t}},1389:function(t,e,o){"use strict";o.r(e);o(31),o(35),o(60);var n=o(1528),r=o(266),h=o(1428),l=(o(1449),{components:{VueDraggableResizable:n.default},mixins:[h.a],props:{asset:{type:Object,required:!0},childHeaderHeight:{type:Number,default:0},lockAspectRatio:{type:Boolean,default:!0},scalable:{type:Boolean,default:!0},hoverEnabled:{type:Boolean,default:!0},isDraggableProp:{type:Boolean,default:!0},hideResizeIcon:{type:Boolean,default:!1},handles:{type:Array,default:function(){return["tl","tm","tr","mr","br","bm","bl","ml"]}}},data:function(){return{width:r.j,height:r.i,top:50,left:500,isHovered:!1,isHoveredByAsset:!1,index:5,eventBodyClass:null,allowIndexChange:!0,resizable:!0,draggable:!0,synchronizeable:!0,viewportWidth:window.innerWidth,viewportHeight:window.innerHeight,offset:5}},computed:{isCanvasOversizeX:function(){return r.n>this.viewportWidth},isCanvasOversizeY:function(){return r.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return r.k*this.zoomIndex>this.viewportHeight},getRoleHoverColor:function(){return"teacher"===this.role?"var(--v-teacherColor-base)":"var(--v-studentColor-base)"},enabledResizeable:function(){return this.resizable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},enabledDraggable:function(){return this.isDraggableProp&&this.draggable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},maxIndex:function(){return this.$store.state.classroom.maxIndex},zoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},zoomIndex:function(){var t,e;return null!==(t=null===(e=this.zoom)||void 0===e?void 0:e.zoomIndex)&&void 0!==t?t:1},type:function(){var t,e;return null===(t=this.asset)||void 0===t||null===(e=t.asset)||void 0===e?void 0:e.type},topScale:function(){return"toolbar"===this.type?this.top<0||this.top+this.height>this.viewportHeight?this.viewportHeight-this.height-70:this.top:this.synchronizeable?this.top-this.zoom.y:this.top},leftScale:function(){return"toolbar"===this.type?this.left+this.width>this.viewportWidth||this.left<0?this.viewportWidth-this.width-15:this.left:this.synchronizeable?this.left-this.zoom.x:this.left},isLockedForStudent:function(){return this.$store.getters["classroom/isLocked"]&&"student"===this.role},isSocketConnected:function(){return this.$store.state.socket.isConnected}},watch:{"asset.asset":function(t){this.move(t)}},mounted:function(){this.move(this.asset.asset)},methods:{mouseenter:function(){var t,e;(this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!0,this.socketAssetMoved({isHovered:!0})),"twilio"===this.type||"tokbox"===this.type||"editor"===this.type)&&(this.$store.commit("classroom/setCursorNameBeforeChange",(null===(t=this.$store.getters["classroom/userParams"])||void 0===t?void 0:t.cursor)||"cursor-pointer"),this.$store.commit("classroom/setToolNameBeforeChange",(null===(e=this.$store.getters["classroom/userParams"])||void 0===e?void 0:e.tool)||"pointer"),this.setTool("pointer","cursor-pointer"))},mouseleave:function(){this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!1,this.socketAssetMoved({isHovered:!1})),"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||this.setTool(this.$store.state.classroom.toolNameBeforeChange,this.$store.state.classroom.cursorNameBeforeChange)},onIndex:function(){this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{index:this.index}}),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{index:this.index}})},updateAsset:function(t,e){this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,index:this.index}})},onDrag:function(t,e){if(this.synchronizeable){if(this.left=t+this.zoom.x,this.top=e+this.zoom.y,this.allowIndexChange){var o=document.body;o.classList.contains(this.eventBodyClass)||(this.eventBodyClass="dragging",o.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var n={left:this.left,top:this.top,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:n}),this.socketAssetMoved(n)}},onDragStop:function(t,e){var o=document.body;o.classList.contains(this.eventBodyClass)&&(o.classList.remove(this.eventBodyClass),this.eventBodyClass=null),this.allowIndexChange=!0,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,index:this.index}})},onResize:function(t,e,o,n,r){if(this.synchronizeable){if(this.left=t+this.zoom.x,this.top=e+this.zoom.y,this.width=o,this.height=n-this.childHeaderHeight,this.allowIndexChange){var h=document.body;h.classList.contains(this.eventBodyClass)||(this.eventBodyClass=r.split(" ")[1],h.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var l={left:this.left,top:this.top,width:this.width,height:this.height,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:l}),this.socketAssetMoved(l)}},onResizeStop:function(t,e,o,n){this.eventBodyClass&&document.body.classList.remove(this.eventBodyClass),this.allowIndexChange=!0,this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,width:o,height:n-this.childHeaderHeight,index:this.index}})},socketAssetMoved:function(t){this.isSocketConnected&&this.$socket.emit("asset-moved",{id:this.asset.id,lessonId:this.asset.lessonId,asset:t})},move:function(t){if(void 0!==t.width?this.width=t.width:"editor"===this.type&&(this.width=.66*(this.isCanvasOversizeX?this.viewportWidth:r.n)),void 0!==t.height)this.height=t.height;else{if("editor"===this.type){var e=.8*(this.isCanvasOversizeY?this.viewportHeight:r.k);e>1200&&(e=1200),e<400&&(e=400),this.height=e-2*this.offset}"audio"===this.type&&(this.height=0)}void 0!==t.top?this.top=t.top:"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||(this.top=this.offset),void 0!==t.left?this.left=t.left:("twilio"!==this.type&&"tokbox"!==this.type||(this.left=(this.isCanvasOversizeX?this.viewportWidth:r.n)-this.width-this.offset),"editor"===this.type&&(this.left=this.offset)),void 0!==t.index&&(this.index=t.index),void 0!==t.resizable&&(this.resizable=t.resizable),void 0!==t.draggable&&(this.draggable=t.draggable),void 0!==t.synchronizeable&&(this.synchronizeable=t.synchronizeable),void 0!==t.isHovered&&(this.isHoveredByAsset=t.isHovered)}}}),d=o(22),component=Object(d.a)(l,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{on:{mouseenter:t.mouseenter,mouseleave:t.mouseleave}},[o("vue-draggable-resizable",{ref:"vueDraggableResizable",class:{student:"student"===t.role,teacher:"teacher"===t.role,"hide-resize-icon":t.hideResizeIcon},style:{outline:t.isHoveredByAsset||t.isHovered?"3px solid "+t.getRoleHoverColor:"none"},attrs:{draggable:t.enabledDraggable,resizable:t.enabledResizeable,w:t.width,h:t.height+t.childHeaderHeight,x:t.leftScale,y:t.topScale,z:t.index,"zoom-index":t.scalable?t.zoom.zoomIndex:1,"zoom-x":t.zoom.x,"zoom-y":t.zoom.y,"child-header-height":t.childHeaderHeight,"lock-aspect-ratio":t.lockAspectRatio,handles:t.handles},on:{dragging:t.onDrag,resizing:t.onResize,dragstop:t.onDragStop,resizestop:t.onResizeStop,"update-asset":t.updateAsset},nativeOn:{click:function(e){return t.onIndex.apply(null,arguments)}}},[t._t("default",null,{onIndex:t.onIndex})],2)],1)}),[],!1,null,null,null);e.default=component.exports},1390:function(t,e,o){"use strict";o(872)("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),o(873))},1391:function(t,e,o){"use strict";o(11)({target:"Set",proto:!0,real:!0,forced:!0},{addAll:o(1384)})},1392:function(t,e,o){"use strict";o(11)({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:o(874)})},1393:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(43),l=o(79),d=o(32),c=o(125),v=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{difference:function(t){var e=d(this),o=new(c(e,r("Set")))(e),n=l(o.delete);return v(t,(function(t){h(n,o,t)})),o}})},1394:function(t,e,o){"use strict";var n=o(11),r=o(32),h=o(69),l=o(1372),d=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{every:function(t){var e=r(this),o=l(e),n=h(t,arguments.length>1?arguments[1]:void 0);return!d(o,(function(t,o){if(!n(t,t,e))return o()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1395:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(43),l=o(79),d=o(32),c=o(69),v=o(125),f=o(1372),m=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{filter:function(t){var e=d(this),o=f(e),n=c(t,arguments.length>1?arguments[1]:void 0),y=new(v(e,r("Set"))),x=l(y.add);return m(o,(function(t){n(t,t,e)&&h(x,y,t)}),{IS_ITERATOR:!0}),y}})},1396:function(t,e,o){"use strict";var n=o(11),r=o(32),h=o(69),l=o(1372),d=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{find:function(t){var e=r(this),o=l(e),n=h(t,arguments.length>1?arguments[1]:void 0);return d(o,(function(t,o){if(n(t,t,e))return o(t)}),{IS_ITERATOR:!0,INTERRUPTED:!0}).result}})},1397:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(43),l=o(79),d=o(32),c=o(125),v=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function(t){var e=d(this),o=new(c(e,r("Set"))),n=l(e.has),f=l(o.add);return v(t,(function(t){h(n,e,t)&&h(f,o,t)})),o}})},1398:function(t,e,o){"use strict";var n=o(11),r=o(43),h=o(79),l=o(32),d=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function(t){var e=l(this),o=h(e.has);return!d(t,(function(t,n){if(!0===r(o,e,t))return n()}),{INTERRUPTED:!0}).stopped}})},1399:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(43),l=o(79),d=o(45),c=o(32),v=o(209),f=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function(t){var e=v(this),o=c(t),n=o.has;return d(n)||(o=new(r("Set"))(t),n=l(o.has)),!f(e,(function(t,e){if(!1===h(n,o,t))return e()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1400:function(t,e,o){"use strict";var n=o(11),r=o(43),h=o(79),l=o(32),d=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function(t){var e=l(this),o=h(e.has);return!d(t,(function(t,n){if(!1===r(o,e,t))return n()}),{INTERRUPTED:!0}).stopped}})},1401:function(t,e,o){"use strict";var n=o(11),r=o(17),h=o(32),l=o(61),d=o(1372),c=o(86),v=r([].join),f=[].push;n({target:"Set",proto:!0,real:!0,forced:!0},{join:function(t){var e=h(this),o=d(e),n=void 0===t?",":l(t),r=[];return c(o,f,{that:r,IS_ITERATOR:!0}),v(r,n)}})},1402:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(69),l=o(43),d=o(79),c=o(32),v=o(125),f=o(1372),m=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{map:function(t){var e=c(this),o=f(e),n=h(t,arguments.length>1?arguments[1]:void 0),y=new(v(e,r("Set"))),x=d(y.add);return m(o,(function(t){l(x,y,n(t,t,e))}),{IS_ITERATOR:!0}),y}})},1403:function(t,e,o){"use strict";var n=o(11),r=o(5),h=o(79),l=o(32),d=o(1372),c=o(86),v=r.TypeError;n({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function(t){var e=l(this),o=d(e),n=arguments.length<2,r=n?void 0:arguments[1];if(h(t),c(o,(function(o){n?(n=!1,r=o):r=t(r,o,o,e)}),{IS_ITERATOR:!0}),n)throw v("Reduce of empty set with no initial value");return r}})},1404:function(t,e,o){"use strict";var n=o(11),r=o(32),h=o(69),l=o(1372),d=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{some:function(t){var e=r(this),o=l(e),n=h(t,arguments.length>1?arguments[1]:void 0);return d(o,(function(t,o){if(n(t,t,e))return o()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1405:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(43),l=o(79),d=o(32),c=o(125),v=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function(t){var e=d(this),o=new(c(e,r("Set")))(e),n=l(o.delete),f=l(o.add);return v(t,(function(t){h(n,o,t)||h(f,o,t)})),o}})},1406:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(79),l=o(32),d=o(125),c=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{union:function(t){var e=l(this),o=new(d(e,r("Set")))(e);return c(t,h(o.add),{that:o}),o}})},1428:function(t,e,o){"use strict";o(35),o(81),o(23),o(6),o(24),o(38);var n={computed:{role:function(){return this.$store.getters["classroom/role"]}},methods:{setTool:function(t,e){this.$store.commit("classroom/enableContainerComponent","pointer"===t),this.$socket.emit("cursor-moved",{tool:t,cursor:e.replace(/(-cursor|cursor-)/i,""),lessonId:this.$store.state.classroom.lessonId}),this.$store.commit("classroom/setUserTool",t),this.$store.commit("classroom/setUserCursor",e);var o=document.body,n=o.classList;this.removeCursors(n),o.classList.add("".concat(this.role,"-").concat(e)),this.classList=o.classList},removeCursors:function(t){t.forEach((function(t){t.includes("cursor")&&document.body.classList.remove(t)}))}}},r=o(22),component=Object(r.a)(n,undefined,undefined,!1,null,null,null);e.a=component.exports},1449:function(t,e,o){var content=o(1477);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("07a1f444",content,!0,{sourceMap:!1})},1459:function(t,e,o){"use strict";o.r(e);o(31),o(35),o(60);var n=o(1528),r=o(266),h=o(1428),l=(o(1449),{components:{VueDraggableResizable:n.default},mixins:[h.a],props:{asset:{type:Object,required:!0},childHeaderHeight:{type:Number,default:0},lockAspectRatio:{type:Boolean,default:!0},scalable:{type:Boolean,default:!0},hoverEnabled:{type:Boolean,default:!0},isDraggableProp:{type:Boolean,default:!0},hideResizeIcon:{type:Boolean,default:!1},handles:{type:Array,default:function(){return["tl","tm","tr","mr","br","bm","bl","ml"]}}},data:function(){return{width:r.j,height:r.i,top:50,left:500,isHovered:!1,isHoveredByAsset:!1,index:5,eventBodyClass:null,allowIndexChange:!0,resizable:!0,draggable:!0,synchronizeable:!0,viewportWidth:window.innerWidth,viewportHeight:window.innerHeight,offset:5}},computed:{isCanvasOversizeX:function(){return r.n>this.viewportWidth},isCanvasOversizeY:function(){return r.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return r.k*this.zoomIndex>this.viewportHeight},getRoleHoverColor:function(){return"teacher"===this.role?"var(--v-teacherColor-base)":"var(--v-studentColor-base)"},enabledResizeable:function(){return this.resizable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},enabledDraggable:function(){return this.isDraggableProp&&this.draggable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},maxIndex:function(){return this.$store.state.classroom.maxIndex},zoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},zoomIndex:function(){var t,e;return null!==(t=null===(e=this.zoom)||void 0===e?void 0:e.zoomIndex)&&void 0!==t?t:1},type:function(){var t,e;return null===(t=this.asset)||void 0===t||null===(e=t.asset)||void 0===e?void 0:e.type},topScale:function(){return"toolbar"===this.type?this.top<0||this.top+this.height>this.viewportHeight?this.viewportHeight-this.height-70:this.top:this.synchronizeable?this.top-this.zoom.y:this.top},leftScale:function(){return"toolbar"===this.type?this.left+this.width>this.viewportWidth||this.left<0?this.viewportWidth-this.width-15:this.left:this.synchronizeable?this.left-this.zoom.x:this.left},isLockedForStudent:function(){return this.$store.getters["classroom/isLocked"]&&"student"===this.role},isSocketConnected:function(){return this.$store.state.socket.isConnected}},watch:{"asset.asset":function(t){this.move(t)}},mounted:function(){this.move(this.asset.asset)},methods:{mouseenter:function(){var t,e;(this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!0,this.socketAssetMoved({isHovered:!0})),"twilio"===this.type||"tokbox"===this.type||"editor"===this.type)&&(this.$store.commit("classroom/setCursorNameBeforeChange",(null===(t=this.$store.getters["classroom/userParams"])||void 0===t?void 0:t.cursor)||"cursor-pointer"),this.$store.commit("classroom/setToolNameBeforeChange",(null===(e=this.$store.getters["classroom/userParams"])||void 0===e?void 0:e.tool)||"pointer"),this.setTool("pointer","cursor-pointer"))},mouseleave:function(){this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!1,this.socketAssetMoved({isHovered:!1})),"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||this.setTool(this.$store.state.classroom.toolNameBeforeChange,this.$store.state.classroom.cursorNameBeforeChange)},onIndex:function(){this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{index:this.index}}),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{index:this.index}})},updateAsset:function(t,e){this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,index:this.index}})},onDrag:function(t,e){if(this.synchronizeable){if(this.left=t+this.zoom.x,this.top=e+this.zoom.y,this.allowIndexChange){var o=document.body;o.classList.contains(this.eventBodyClass)||(this.eventBodyClass="dragging",o.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var n={left:this.left,top:this.top,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:n}),this.socketAssetMoved(n)}},onDragStop:function(t,e){var o=document.body;o.classList.contains(this.eventBodyClass)&&(o.classList.remove(this.eventBodyClass),this.eventBodyClass=null),this.allowIndexChange=!0,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,index:this.index}})},onResize:function(t,e,o,n,r){if(this.synchronizeable){if(this.left=t+this.zoom.x,this.top=e+this.zoom.y,this.width=o,this.height=n-this.childHeaderHeight,this.allowIndexChange){var h=document.body;h.classList.contains(this.eventBodyClass)||(this.eventBodyClass=r.split(" ")[1],h.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var l={left:this.left,top:this.top,width:this.width,height:this.height,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:l}),this.socketAssetMoved(l)}},onResizeStop:function(t,e,o,n){this.eventBodyClass&&document.body.classList.remove(this.eventBodyClass),this.allowIndexChange=!0,this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,width:o,height:n-this.childHeaderHeight,index:this.index}})},socketAssetMoved:function(t){this.isSocketConnected&&this.$socket.emit("asset-moved",{id:this.asset.id,lessonId:this.asset.lessonId,asset:t})},move:function(t){if(void 0!==t.width?this.width=t.width:"editor"===this.type&&(this.width=.66*(this.isCanvasOversizeX?this.viewportWidth:r.n)),void 0!==t.height)this.height=t.height;else{if("editor"===this.type){var e=.8*(this.isCanvasOversizeY?this.viewportHeight:r.k);e>1200&&(e=1200),e<400&&(e=400),this.height=e-2*this.offset}"audio"===this.type&&(this.height=0)}void 0!==t.top?this.top=t.top:"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||(this.top=this.offset),void 0!==t.left?this.left=t.left:("twilio"!==this.type&&"tokbox"!==this.type||(this.left=(this.isCanvasOversizeX?this.viewportWidth:r.n)-this.width-this.offset),"editor"===this.type&&(this.left=this.offset)),void 0!==t.index&&(this.index=t.index),void 0!==t.resizable&&(this.resizable=t.resizable),void 0!==t.draggable&&(this.draggable=t.draggable),void 0!==t.synchronizeable&&(this.synchronizeable=t.synchronizeable),void 0!==t.isHovered&&(this.isHoveredByAsset=t.isHovered)}}}),d=o(22),component=Object(d.a)(l,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{on:{mouseenter:t.mouseenter,mouseleave:t.mouseleave}},[o("vue-draggable-resizable",{ref:"vueDraggableResizable",class:{student:"student"===t.role,teacher:"teacher"===t.role,"hide-resize-icon":t.hideResizeIcon},style:{outline:t.isHoveredByAsset||t.isHovered?"3px solid "+t.getRoleHoverColor:"none"},attrs:{draggable:t.enabledDraggable,resizable:t.enabledResizeable,w:t.width,h:t.height+t.childHeaderHeight,x:t.leftScale,y:t.topScale,z:t.index,"zoom-index":t.scalable?t.zoom.zoomIndex:1,"zoom-x":t.zoom.x,"zoom-y":t.zoom.y,"child-header-height":t.childHeaderHeight,"lock-aspect-ratio":t.lockAspectRatio,handles:t.handles},on:{dragging:t.onDrag,resizing:t.onResize,dragstop:t.onDragStop,resizestop:t.onResizeStop,"update-asset":t.updateAsset},nativeOn:{click:function(e){return t.onIndex.apply(null,arguments)}}},[t._t("default",null,{onIndex:t.onIndex})],2)],1)}),[],!1,null,null,null);e.default=component.exports},1477:function(t,e,o){var n=o(18),r=o(265),h=o(870),l=n(!1),d=r(h);l.push([t.i,'.vdr{touch-action:none;position:absolute;box-sizing:border-box;border:none}.handle{display:block!important}.resizable.vdr:not(.hide-resize-icon):after{content:"";position:absolute;bottom:4px;right:2px;width:8px;height:8px;background-image:url('+d+');background-size:contain;background-repeat:no-repeat;background-position:50%;opacity:.9;z-index:99}.vdr .handle{box-sizing:border-box;position:absolute;width:12px;height:12px;border:none;background-color:transparent;z-index:10}.vdr .handle-tl{top:-7px;left:-7px}.vdr .handle-tm{top:-7px;margin-left:-5px}.vdr .handle-tr{top:-7px;right:-7px}.vdr .handle-ml{margin-top:-5px;left:-7px}.vdr .handle-mr{margin-top:-5px;right:-7px}.vdr .handle-bl{bottom:-7px;left:-7px}.vdr .handle-bm{bottom:-7px;margin-left:-5px}.vdr .handle-br{bottom:-7px;right:-7px}@media only screen and (max-width:768px){[class*=handle-]:before{content:"";left:-10px;right:-10px;bottom:-10px;top:-10px;position:absolute}}.vdr .handle-bm,.vdr .handle-tm{width:100%;left:5px}.vdr .handle-ml,.vdr .handle-mr{height:100%;top:5px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:14px;height:14px;z-index:11}@media only screen and (max-width:1199px){.vdr .handle-bm,.vdr .handle-tm{height:15px}.vdr .handle-ml,.vdr .handle-mr{width:15px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:15px;height:15px}.vdr .handle-tl{top:-10px;left:-10px}.vdr .handle-tm{top:-10px;margin-left:-5px}.vdr .handle-tr{top:-10px;right:-10px}.vdr .handle-ml{margin-top:-5px;left:-10px}.vdr .handle-mr{margin-top:-5px;right:-10px}.vdr .handle-bl{bottom:-10px;left:-10px}.vdr .handle-bm{bottom:-10px;margin-left:-5px}.vdr .handle-br{bottom:-10px;right:-10px}}',""]),t.exports=l},1555:function(t,e,o){"use strict";o.r(e);o(7),o(8),o(9),o(14),o(15);var n=o(2),r=o(28),h=(o(31),o(6),o(23),o(1018)),l=o.n(h),d=o(266);function c(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var v={name:"Konva",props:{file:{type:Object,required:!0},width:{type:Number,required:!0},height:{type:Number,required:!0},scale:{type:Number,default:1},currentTime:{type:Number,default:null},currentPage:{type:Number,default:null},isMainKonva:{type:Boolean,default:!1}},data:function(){return{shapeData:null,beginPoint:null,konvaEl:null,konvaOverlayREl:null,konvaOverlayBEl:null}},computed:{isCanvasOversizeX:function(){return d.n>this.width},isScaledCanvasOversizeX:function(){return d.n*this.scale>this.width},isCanvasOversizeY:function(){return d.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return d.k*this.scale>this.height},assetType:function(){return this.file.asset.type},userParams:function(){return this.$store.getters["classroom/userParams"]},assetShapes:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.shapes)&&void 0!==t?t:[]},shapes:function(){var t=this,e=Object(r.a)(this.assetShapes),o=[];return this.shapeData&&e.push(this.shapeData),e.forEach((function(e){var r,h=function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?c(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):c(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},e);h.x=e.x-t.zoomX,h.y=e.y-t.zoomY,null!==(r=t.file.asset)&&void 0!==r&&r.originalWidth&&(h.strokeWidth=e.strokeWidth*t.file.asset.originalWidth/t.file.asset.width),o.push(h)})),o},zoomX:function(){return this.isMainKonva?this.zoom.x:0},zoomY:function(){return this.isMainKonva?this.zoom.y:0},zoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},config:function(){return{scale:{x:this.scale,y:this.scale},width:this.width,height:this.height,draggable:!1}}},watch:{width:function(t){this.stage.setWidth(t),this.isMainKonva&&(this.konvaOverlayREl&&this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayBEl&&this.setStyleForVerticalMainKonvaOverlays())},height:function(t){this.stage.setHeight(t),this.isMainKonva&&(this.konvaOverlayREl&&this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayBEl&&this.setStyleForVerticalMainKonvaOverlays())},scale:function(t){this.stage.setScale({x:t,y:t}),this.isMainKonva&&(this.konvaOverlayREl&&this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayBEl&&this.setStyleForVerticalMainKonvaOverlays())},isScaledCanvasOversizeX:function(t){this.isMainKonva&&(t?(this.konvaEl.removeChild(this.konvaOverlayREl),this.konvaOverlayREl=null):this.addHorizontalMainKonvaOverlays())},isScaledCanvasOversizeY:function(t){this.isMainKonva&&(t?(this.konvaEl.removeChild(this.konvaOverlayBEl),this.konvaOverlayBEl=null):this.addVerticalMainKonvaOverlays())}},mounted:function(){var t=this;this.stage=this.$refs.stage.getStage(),this.$nextTick((function(){t.move()})),this.konvaEl=document.getElementById("konva"),this.isMainKonva&&(this.isScaledCanvasOversizeX||this.addHorizontalMainKonvaOverlays(),this.isScaledCanvasOversizeY||this.addVerticalMainKonvaOverlays())},methods:{addHorizontalMainKonvaOverlays:function(){this.konvaOverlayREl||(this.konvaOverlayREl=document.createElement("div"),this.konvaOverlayREl.classList.add("konva-overlay-r"),this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayREl.addEventListener("mouseenter",this.mouseup),this.konvaEl.appendChild(this.konvaOverlayREl))},addVerticalMainKonvaOverlays:function(){this.konvaOverlayBEl||(this.konvaOverlayBEl=document.createElement("div"),this.konvaOverlayBEl.classList.add("konva-overlay-b"),this.setStyleForVerticalMainKonvaOverlays(),this.konvaOverlayBEl.addEventListener("mouseenter",this.mouseup),this.konvaEl.appendChild(this.konvaOverlayBEl))},setStyleForHorizontalMainKonvaOverlays:function(){this.konvaOverlayREl.style.position="absolute",this.konvaOverlayREl.style.top="0",this.konvaOverlayREl.style.right="0",this.konvaOverlayREl.style.width="".concat(this.width-d.n*this.scale,"px"),this.konvaOverlayREl.style.height="".concat(this.height,"px"),this.konvaOverlayREl.style.backgroundColor="#DCDCDD"},setStyleForVerticalMainKonvaOverlays:function(){this.konvaOverlayBEl.style.position="absolute",this.konvaOverlayBEl.style.bottom="0",this.konvaOverlayBEl.style.left="0",this.konvaOverlayBEl.style.width="".concat(this.width,"px"),this.konvaOverlayBEl.style.height="".concat(this.height-d.k*this.scale,"px"),this.konvaOverlayBEl.style.backgroundColor="#DCDCDD"},mousedown:function(t){var e=this.$refs.globalLayer.getNode(),o=t.target.getStage().getPointerPosition();switch(this.beginPoint={x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY},this.userParams.tool){case d.f:var n=new l.a.Circle({x:o.x/this.scale,y:o.y/this.scale,radius:2,stroke:this.userParams.color,strokeWidth:1});return e.add(n),void new l.a.Tween({node:n,duration:1,radius:24,onFinish:function(){return n.destroy()}}).play();case d.e:this.shapeData={type:"v-line",stroke:this.userParams.color,strokeWidth:5,x:0,y:0,points:[o.x/this.scale+this.zoomX,o.y/this.scale+this.zoomY],lineCap:"round",lineJoin:"round",tension:0,bezier:!0,perfectDrawEnabled:!1};break;case d.g:this.shapeData={type:"v-rect",x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY,width:1,height:1,stroke:this.userParams.color,strokeWidth:5};break;case d.b:this.shapeData={type:"v-circle",x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY,radius:1,stroke:this.userParams.color,strokeWidth:5};break;case d.h:this.shapeData={type:"v-line",stroke:this.userParams.color,strokeWidth:5,x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY,points:[0,0,0,0,0,0],tension:0,closed:!0};break;case d.d:this.shapeData={type:"v-line",stroke:this.userParams.color,strokeWidth:5,x:0,y:0,points:[o.x/this.scale+this.zoomX,o.y/this.scale+this.zoomY]};break;case d.c:this.shapeData={type:"v-line",stroke:"#f2f2f2",strokeWidth:30,x:0,y:0,points:[o.x/this.scale+this.zoomX,o.y/this.scale+this.zoomY],globalCompositeOperation:"destination-out"};break;default:console.warn("Requested action doesnt found for selected cursor - "+this.userParams.tool)}this.userParams.tool!==d.f&&("video"===this.assetType&&(this.shapeData.time=this.currentTime),"pdf"===this.assetType&&(this.shapeData.page=this.currentPage))},mouseup:function(){if(this.shapeData&&this.shapeData.type){var t={shapes:[].concat(Object(r.a)(this.assetShapes),[this.shapeData])};this.$store.commit("classroom/moveAsset",{id:this.file.id,asset:t}),this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.file.lessonId,asset:t}),this.beginPoint=null,this.shapeData=null}},move:function(t){if(this.shapeData){var e=t.target.getStage().getPointerPosition();this.drawing(e)}},drawing:function(t){var e,o;if(this.shapeData)switch(this.userParams.tool){case d.e:case d.c:this.shapeData.points=[].concat(Object(r.a)(this.shapeData.points),[t.x/this.scale+this.zoomX,t.y/this.scale+this.zoomY]);break;case d.g:this.shapeData.width=t.x/this.scale+this.zoomX-this.beginPoint.x,this.shapeData.height=t.y/this.scale+this.zoomY-this.beginPoint.y;break;case d.b:e=Math.abs(t.x/this.scale+this.zoomX-this.beginPoint.x),o=Math.abs(t.y/this.scale+this.zoomY-this.beginPoint.y),this.shapeData.radius=Math.max(e,o);break;case d.h:this.shapeData.points=[-(t.x/this.scale+this.zoomX-this.beginPoint.x),t.y/this.scale+this.zoomY-this.beginPoint.y,0,0,t.x/this.scale+this.zoomX-this.beginPoint.x,t.y/this.scale+this.zoomY-this.beginPoint.y];break;case d.d:this.shapeData.points=[this.beginPoint.x,this.beginPoint.y,t.x/this.scale+this.zoomX,t.y/this.scale+this.zoomY];break;default:console.warn("Requested action doesnt found for selected cursor")}}}},f=o(22),component=Object(f.a)(v,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-stage",{ref:"stage",attrs:{config:t.config},on:{mousedown:t.mousedown,touchstart:t.mousedown,touchmove:t.move,touchend:t.mouseup,mouseup:t.mouseup,mousemove:t.move}},[o("v-layer",{ref:"globalLayer"},[t._l(t.shapes,(function(e,n){return[!e.hasOwnProperty("time")&&!e.hasOwnProperty("page")||e.hasOwnProperty("time")&&t.currentTime+1>=e.time&&e.time>=t.currentTime-1||e.hasOwnProperty("page")&&t.currentPage===e.page?o(e.type,{key:n,tag:"component",attrs:{config:e}}):t._e()]}))],2)],1)}),[],!1,null,null,null);e.default=component.exports},1664:function(t,e,o){var content=o(1784);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("6cc1002b",content,!0,{sourceMap:!1})},1779:function(t,e){},1780:function(t,e){},1781:function(t,e){},1782:function(t,e){},1783:function(t,e,o){"use strict";o(1664)},1784:function(t,e,o){var n=o(18)(!1);n.push([t.i,".pdf-controls{display:flex;justify-content:space-between;align-items:center;box-shadow:0 -1px 3px rgba(0,0,0,.25);position:relative;padding:5px 12px;background:#fff}.pdf-controls button{border:none;background:none}.pdf-controls button,.pdf-controls button *{cursor:pointer!important}.pdf-controls button.btn-next .v-image{transform:rotate(180deg)}.pdf-controls button:disabled,.pdf-controls button[disabled]{opacity:0;visibility:hidden}",""]),t.exports=n},1941:function(t,e,o){"use strict";o.r(e);var n=o(10),r=(o(62),o(859)),h=o(1389),l=o(1459),d=o(266),c=o(1555),v=o(1428),f={name:"PdfItem",components:{ClassroomContainer:h.default,ClassroomContainerHeader:l.default,Konva:c.default},mixins:[v.a],props:{file:{type:Object,required:!0}},data:function(){return{pdfjsLib:{},renderInProgress:!1,viewport:null,page:1,pageTotal:1,pdfDoc:null,pdfPage:null}},computed:{scale:function(){return this.width?this.width/d.j:1},canvas:function(){return document.querySelector("#pdf-render--".concat(this.file.id))},zoomIndex:function(){var t,e,o;return null!==(t=null===(e=this.$store.getters["classroom/zoomAsset"])||void 0===e||null===(o=e.asset)||void 0===o?void 0:o.zoomIndex)&&void 0!==t?t:1},width:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.width)&&void 0!==t?t:0},height:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.height)&&void 0!==t?t:0},isPrevPageEnabled:function(){return this.pageTotal>1&&this.page>1},isNextPageEnabled:function(){return this.pageTotal>1&&this.page<this.pageTotal}},watch:{"file.asset.page":function(t){this.page=t,this.renderPage()},zoomIndex:function(){this.renderPage()}},mounted:function(){var t=this;return Object(n.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o(1691);case 2:return t.pdfjsLib=e.sent,e.next=5,o(1692);case 5:t.pdfjsLib.GlobalWorkerOptions.workerSrc=e.sent,t.file.asset.page&&(t.page=t.file.asset.page),t.pdfjsLib.getDocument(t.file.asset.path).promise.then((function(e){t.pdfDoc=e,t.pageTotal=t.pdfDoc.numPages,t.renderPage();var o={originalWidth:d.j};t.$store.commit("classroom/moveAsset",{id:t.file.id,asset:o}),t.$store.dispatch("classroom/moveAsset",{id:t.file.id,lessonId:t.file.lessonId,asset:o})})).catch((function(t){alert(t.message)})),new ResizeObserver(Object(r.debounce)(t.resize,200)).observe(t.$refs.childComponent);case 9:case"end":return e.stop()}}),e)})))()},methods:{showPage:function(){this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.file.lessonId,asset:{page:this.page}})},showPrevPage:function(){this.page<=1||(this.page-=1,this.renderPage(),this.showPage())},showNextPage:function(){this.page>=this.pageTotal||(this.page+=1,this.renderPage(),this.showPage())},resize:function(){var t=this;if(this.pdfPage){if(this.renderInProgress)return;this.renderInProgress=!0;var e=this.canvas.getContext("2d"),o=(window.devicePixelRatio||1)*this.zoomIndex;if(this.$refs.childComponent){var n=this.$refs.childComponent.getBoundingClientRect().width,r=this.pdfPage.getViewport({scale:1}).width,h=n/this.zoomIndex/r;this.viewport=this.pdfPage.getViewport({scale:o*h}),this.canvas.height=this.viewport.height,this.canvas.width=this.viewport.width,this.canvas.style.width="100%",this.pdfPage.render({canvasContext:e,viewport:this.viewport}).promise.then((function(){t.$store.commit("classroom/moveAsset",{id:t.file.id,asset:{width:t.canvas.width/o,height:t.canvas.height/o}}),t.renderInProgress=!1}))}}},renderPage:function(){var t=this;this.pdfDoc.getPage(this.page).then((function(e){t.pdfPage=e,t.resize()}))},mouseenterHandler:function(){var t,e,o,n;this.$store.commit("classroom/setCursorNameBeforeChange",(null===(t=this.$store.state)||void 0===t||null===(e=t.userParams)||void 0===e?void 0:e.cursor)||"cursor-pointer"),this.$store.commit("classroom/setToolNameBeforeChange",(null===(o=this.$store.state)||void 0===o||null===(n=o.userParams)||void 0===n?void 0:n.tool)||"pointer"),this.setTool("pointer","cursor-pointer")},mouseleaveHandler:function(){this.setTool(this.$store.state.classroom.toolNameBeforeChange,this.$store.state.classroom.cursorNameBeforeChange)}}},m=(o(1783),o(22)),y=o(42),x=o.n(y),z=o(1327),w=o(261),component=Object(m.a)(f,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("classroom-container",{attrs:{asset:t.file,"child-header-height":92}},[n("div",{ref:"childComponent",staticClass:"image-wrap-classroom"},[n("classroom-container-header",{attrs:{file:t.file,title:t.file.asset.displayName}},[n("canvas",{attrs:{id:"pdf-render--"+t.file.id}}),t._v(" "),n("div",{staticClass:"transparent"},[t.file&&t.width&&t.height?n("konva",{style:{marginTop:"-"+t.height+"px"},attrs:{file:t.file,width:t.width,height:t.height,scale:t.scale,"current-page":t.page}}):t._e()],1),t._v(" "),n("div",{staticClass:"pdf-controls",on:{mouseenter:function(e){return e.preventDefault(),t.mouseenterHandler.apply(null,arguments)},mouseleave:function(e){return e.preventDefault(),t.mouseleaveHandler.apply(null,arguments)}}},[n("v-btn",{attrs:{icon:"",width:"36",height:"36",disabled:!t.isPrevPageEnabled},on:{click:t.showPrevPage}},[n("v-img",{attrs:{src:o(885),height:"14",contain:""}})],1),t._v(" "),n("span",{staticClass:"page-info"},[t._v("\n          "+t._s(t.$t("page"))+" "),n("span",[t._v(t._s(t.page))]),t._v(" of "+t._s(t.pageTotal)),n("span")]),t._v(" "),n("v-btn",{staticClass:"btn-next",attrs:{icon:"",small:"",width:"36",height:"36",disabled:!t.isNextPageEnabled},on:{click:t.showNextPage}},[n("v-img",{attrs:{src:o(885),height:"14",contain:""}})],1)],1)])],1)])}),[],!1,null,null,null);e.default=component.exports;x()(component,{ClassroomContainerHeader:o(1459).default,ClassroomContainer:o(1389).default}),x()(component,{VBtn:z.a,VImg:w.a})}}]);