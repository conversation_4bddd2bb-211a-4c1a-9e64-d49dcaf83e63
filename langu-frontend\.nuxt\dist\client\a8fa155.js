(window.webpackJsonp=window.webpackJsonp||[]).push([[127,57,58,119,120,128,131],{1375:function(t,e,n){"use strict";n.r(e);var r={name:"UserSettingTemplate",props:{title:{type:String,required:!0},hideFooter:{type:Boolean,default:!1},customValid:{type:Boolean,default:!0},submitFunc:{type:Function,default:function(){}}},data:function(){return{formValid:!0}},computed:{valid:function(){return this.formValid&&this.customValid}},mounted:function(){this.validate()},methods:{validate:function(){this.$refs.form.validate()},submit:function(){this.valid&&this.submitFunc()}}},o=(n(1419),n(22)),l=n(42),c=n.n(l),d=n(1327),h=n(1363),component=Object(o.a)(r,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("v-form",{ref:"form",attrs:{value:t.formValid},on:{validate:t.validate,submit:function(e){return e.preventDefault(),t.submit.apply(null,arguments)},input:function(e){t.formValid=e}}},[r("div",{staticClass:"user-settings-panel"},[r("div",{staticClass:"panel"},[t.$vuetify.breakpoint.smAndUp?r("div",{staticClass:"panel-head d-none d-sm-block"},[r("div",{staticClass:"panel-head-title font-weight-medium mb-2 mb-sm-3 mb-md-5"},[t._v("\n          "+t._s(t.title)+"\n        ")])]):t._e(),t._v(" "),r("div",{staticClass:"panel-body"},[t._t("default")],2),t._v(" "),t.hideFooter?t._e():r("div",{staticClass:"panel-footer d-flex justify-center justify-sm-end"},[r("v-btn",{staticClass:"font-weight-medium",attrs:{color:"primary",type:"submit",disabled:!t.valid}},[r("svg",{staticClass:"mr-1",attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[r("use",{attrs:{"xlink:href":n(91)+"#save-icon"}})]),t._v("\n          "+t._s(t.$t("save_changes"))+"\n        ")])],1)])])])}),[],!1,null,null,null);e.default=component.exports;c()(component,{VBtn:d.a,VForm:h.a})},1380:function(t,e,n){var content=n(1381);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("73707fd0",content,!0,{sourceMap:!1})},1381:function(t,e,n){var r=n(18)(!1);r.push([t.i,".v-item-group{flex:0 1 auto;position:relative;max-width:100%;transition:.3s cubic-bezier(.25,.8,.5,1)}",""]),t.exports=r},1385:function(t,e,n){var content=n(1420);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("419d3f06",content,!0,{sourceMap:!1})},1411:function(t,e,n){"use strict";n.d(e,"a",(function(){return v}));n(7),n(8),n(14),n(15);var r=n(2),o=(n(31),n(9),n(24),n(38),n(126),n(6),n(55),n(71),n(371),n(1380),n(372)),l=n(36),c=n(12),d=n(16);function h(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var v=Object(c.a)(o.a,l.a).extend({name:"base-item-group",props:{activeClass:{type:String,default:"v-item--active"},mandatory:Boolean,max:{type:[Number,String],default:null},multiple:Boolean,tag:{type:String,default:"div"}},data:function(){return{internalLazyValue:void 0!==this.value?this.value:this.multiple?[]:void 0,items:[]}},computed:{classes:function(){return function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?h(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):h(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({"v-item-group":!0},this.themeClasses)},selectedIndex:function(){return this.selectedItem&&this.items.indexOf(this.selectedItem)||-1},selectedItem:function(){if(!this.multiple)return this.selectedItems[0]},selectedItems:function(){var t=this;return this.items.filter((function(e,n){return t.toggleMethod(t.getValue(e,n))}))},selectedValues:function(){return null==this.internalValue?[]:Array.isArray(this.internalValue)?this.internalValue:[this.internalValue]},toggleMethod:function(){var t=this;if(!this.multiple)return function(e){return t.internalValue===e};var e=this.internalValue;return Array.isArray(e)?function(t){return e.includes(t)}:function(){return!1}}},watch:{internalValue:"updateItemsState",items:"updateItemsState"},created:function(){this.multiple&&!Array.isArray(this.internalValue)&&Object(d.c)("Model must be bound to an array if the multiple property is true.",this)},methods:{genData:function(){return{class:this.classes}},getValue:function(t,i){return null==t.value||""===t.value?i:t.value},onClick:function(t){this.updateInternalValue(this.getValue(t,this.items.indexOf(t)))},register:function(t){var e=this,n=this.items.push(t)-1;t.$on("change",(function(){return e.onClick(t)})),this.mandatory&&!this.selectedValues.length&&this.updateMandatory(),this.updateItem(t,n)},unregister:function(t){if(!this._isDestroyed){var e=this.items.indexOf(t),n=this.getValue(t,e);if(this.items.splice(e,1),!(this.selectedValues.indexOf(n)<0)){if(!this.mandatory)return this.updateInternalValue(n);this.multiple&&Array.isArray(this.internalValue)?this.internalValue=this.internalValue.filter((function(t){return t!==n})):this.internalValue=void 0,this.selectedItems.length||this.updateMandatory(!0)}}},updateItem:function(t,e){var n=this.getValue(t,e);t.isActive=this.toggleMethod(n)},updateItemsState:function(){var t=this;this.$nextTick((function(){if(t.mandatory&&!t.selectedItems.length)return t.updateMandatory();t.items.forEach(t.updateItem)}))},updateInternalValue:function(t){this.multiple?this.updateMultiple(t):this.updateSingle(t)},updateMandatory:function(t){if(this.items.length){var e=this.items.slice();t&&e.reverse();var n=e.find((function(t){return!t.disabled}));if(n){var r=this.items.indexOf(n);this.updateInternalValue(this.getValue(n,r))}}},updateMultiple:function(t){var e=(Array.isArray(this.internalValue)?this.internalValue:[]).slice(),n=e.findIndex((function(e){return e===t}));this.mandatory&&n>-1&&e.length-1<1||null!=this.max&&n<0&&e.length+1>this.max||(n>-1?e.splice(n,1):e.push(t),this.internalValue=e)},updateSingle:function(t){var e=t===this.internalValue;this.mandatory&&e||(this.internalValue=e?void 0:t)}},render:function(t){return t(this.tag,this.genData(),this.$slots.default)}});v.extend({name:"v-item-group",provide:function(){return{itemGroup:this}}})},1419:function(t,e,n){"use strict";n(1385)},1420:function(t,e,n){var r=n(18)(!1);r.push([t.i,".user-settings-panel{padding:44px;border-radius:20px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1)}@media only screen and (max-width:1439px){.user-settings-panel{padding:24px}}@media only screen and (max-width:767px){.user-settings-panel{padding:0;box-shadow:none}}.user-settings-panel .row{margin:0 -14px!important}.user-settings-panel .col{padding:0 14px!important}.user-settings-panel .panel{color:var(--v-greyDark-base)}.user-settings-panel .panel-head-title{font-size:24px;line-height:1.333;color:var(--v-darkLight-base)}@media only screen and (max-width:1439px){.user-settings-panel .panel-head-title{font-size:20px}}.user-settings-panel .panel-body .chips>div{margin-top:6px}.user-settings-panel .panel-body .price-input .v-text-field--outlined.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot{min-height:32px!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr>th{color:var(--v-dark-base)}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr:last-child>th{border:none!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:hover:not(.v-data-table__expanded__content):not(.v-data-table__empty-wrapper){background:none}.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tbody>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tfoot>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>thead>tr>td{height:38px;color:var(--v-greyDark-base)}.user-settings-panel .panel-footer{margin-top:115px}@media only screen and (max-width:1439px){.user-settings-panel .panel-footer{margin-top:56px}}.user-settings-panel .panel-footer .v-btn{letter-spacing:.1px}@media only screen and (max-width:479px){.user-settings-panel .panel-footer .v-btn{width:100%!important}}.user-settings-panel .l-checkbox .v-input--selection-controls__input{width:16px;height:16px;margin-top:2px}",""]),t.exports=r},1429:function(t,e,n){"use strict";var r=n(3),o=n(1);e.a=r.default.extend({name:"comparable",props:{valueComparator:{type:Function,default:o.h}}})},1434:function(t,e,n){var content=n(1491);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("a98bb618",content,!0,{sourceMap:!1})},1440:function(t,e,n){"use strict";n.r(e);n(31);var r={name:"UserSettingSelect",props:{value:{type:Object,required:!0},items:{type:Array,required:!0},attachId:{type:String,required:!0},itemValue:{type:String,default:"id"},itemText:{type:String,default:"name"},hideDetails:{type:Boolean,default:!0},hideSelected:{type:Boolean,default:!0},rules:{type:Array,default:function(){return[]}},validateOnBlur:{type:Boolean,default:!1},maxHeight:{type:Number,default:162},placeholder:[Boolean,String]},data:function(){return{chevronIcon:"".concat(n(91),"#chevron-down")}},computed:{_placeholder:function(){return this.placeholder?this.$t(this.placeholder):""}}},o=(n(1519),n(22)),l=n(42),c=n.n(l),d=n(261),h=n(1610),component=Object(o.a)(r,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"user-setting-select",attrs:{id:t.attachId}},[r("v-select",t._g({attrs:{value:t.value,items:t.items,height:"44","full-width":"",outlined:"",dense:"","hide-selected":t.hideSelected,"hide-details":t.hideDetails,"return-object":"",rules:t.rules,placeholder:t._placeholder,"item-value":t.itemValue,"item-text":t.itemText,attach:"#"+t.attachId,"validate-on-blur":t.validateOnBlur,"menu-props":{bottom:!0,offsetY:!0,minWidth:200,maxHeight:t.maxHeight,nudgeBottom:8,contentClass:"select-list l-scroll"}},scopedSlots:t._u([{key:"append",fn:function(){return[r("svg",{attrs:{width:"12",height:"12",viewBox:"0 0 12 12"}},[r("use",{attrs:{"xlink:href":t.chevronIcon}})])]},proxy:!0},{key:"item",fn:function(e){var o=e.item;return[o.isoCode?r("div",{staticClass:"icon"},[r("v-img",{attrs:{src:n(369)("./"+o.isoCode+".svg"),height:"24",width:"24",eager:""}})],1):t._e(),t._v(" "),r("div",{staticClass:"text",style:{color:"all"===o.id?"#888":"inherit"}},[t._v("\n        "+t._s(o[t.itemText])+"\n      ")])]}}])},t.$listeners))],1)}),[],!1,null,"322ff0d6",null);e.default=component.exports;c()(component,{VImg:d.a,VSelect:h.a})},1445:function(t,e,n){var content=n(1512);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("f203485e",content,!0,{sourceMap:!1})},1448:function(t,e,n){var content=n(1520);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("5bc00c5d",content,!0,{sourceMap:!1})},1450:function(t,e,n){"use strict";n.r(e);var r={name:"ConfirmDialog",props:{isShownConfirmDialog:{type:Boolean,required:!0},cancelTextButton:{type:String,default:"close"},confirmTextButton:{type:String,default:"confirm"}}},o=(n(1511),n(22)),l=n(42),c=n.n(l),d=n(1327),component=Object(o.a)(r,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.isShownConfirmDialog?n("l-dialog",t._g({attrs:{dialog:t.isShownConfirmDialog,"hide-close-button":"","max-width":"418","custom-class":"remove-illustration text-center"}},t.$listeners),[n("div",[n("div",{staticClass:"remove-illustration-title font-weight-medium"},[t._v("\n      "+t._s(t.$t("are_you_sure"))+"\n    ")]),t._v(" "),n("div",{staticClass:"mt-2"},[t._t("default")],2),t._v(" "),n("div",{staticClass:"d-flex justify-space-around justify-sm-space-between flex-wrap mt-2"},[n("v-btn",{staticClass:"gradient font-weight-medium my-1",on:{click:function(e){return t.$emit("close-dialog")}}},[n("div",{staticClass:"text--gradient"},[t._v("\n          "+t._s(t.$t(t.cancelTextButton))+"\n        ")])]),t._v(" "),n("v-btn",{staticClass:"font-weight-medium my-1",attrs:{color:"primary"},on:{click:function(e){return t.$emit("confirm")}}},[t._v("\n        "+t._s(t.$t(t.confirmTextButton))+"\n      ")])],1)])]):t._e()}),[],!1,null,null,null);e.default=component.exports;c()(component,{LDialog:n(149).default}),c()(component,{VBtn:d.a})},1479:function(t,e,n){var content=n(1564);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("2e2bc7da",content,!0,{sourceMap:!1})},1480:function(t,e,n){"use strict";n.d(e,"b",(function(){return d}));n(20),n(80),n(9);var r=n(117),o=n(1556),l=n(1429),c=n(12);function d(t){t.preventDefault()}e.a=Object(c.a)(r.a,o.a,l.a).extend({name:"selectable",model:{prop:"inputValue",event:"change"},props:{id:String,inputValue:null,falseValue:null,trueValue:null,multiple:{type:Boolean,default:null},label:String},data:function(){return{hasColor:this.inputValue,lazyValue:this.inputValue}},computed:{computedColor:function(){if(this.isActive)return this.color?this.color:this.isDark&&!this.appIsDark?"white":"primary"},isMultiple:function(){return!0===this.multiple||null===this.multiple&&Array.isArray(this.internalValue)},isActive:function(){var t=this,e=this.value,input=this.internalValue;return this.isMultiple?!!Array.isArray(input)&&input.some((function(n){return t.valueComparator(n,e)})):void 0===this.trueValue||void 0===this.falseValue?e?this.valueComparator(e,input):Boolean(input):this.valueComparator(input,this.trueValue)},isDirty:function(){return this.isActive},rippleState:function(){return this.isDisabled||this.validationState?this.validationState:void 0}},watch:{inputValue:function(t){this.lazyValue=t,this.hasColor=t}},methods:{genLabel:function(){var label=r.a.options.methods.genLabel.call(this);return label?(label.data.on={click:d},label):label},genInput:function(t,e){return this.$createElement("input",{attrs:Object.assign({"aria-checked":this.isActive.toString(),disabled:this.isDisabled,id:this.computedId,role:t,type:t},e),domProps:{value:this.value,checked:this.isActive},on:{blur:this.onBlur,change:this.onChange,focus:this.onFocus,keydown:this.onKeydown,click:d},ref:"input"})},onBlur:function(){this.isFocused=!1},onClick:function(t){this.onChange(),this.$emit("click",t)},onChange:function(){var t=this;if(this.isInteractive){var e=this.value,input=this.internalValue;if(this.isMultiple){Array.isArray(input)||(input=[]);var n=input.length;(input=input.filter((function(n){return!t.valueComparator(n,e)}))).length===n&&input.push(e)}else input=void 0!==this.trueValue&&void 0!==this.falseValue?this.valueComparator(input,this.trueValue)?this.falseValue:this.trueValue:e?this.valueComparator(input,e)?null:e:!input;this.validate(!0,input),this.internalValue=input,this.hasColor=input}},onFocus:function(){this.isFocused=!0},onKeydown:function(t){}}})},1481:function(t,e,n){var map={"./illustration-1.svg":909,"./illustration-10.svg":910,"./illustration-11.svg":911,"./illustration-12.svg":912,"./illustration-13.svg":913,"./illustration-14.svg":914,"./illustration-15.svg":915,"./illustration-16.svg":916,"./illustration-17.svg":917,"./illustration-18.svg":918,"./illustration-19.svg":919,"./illustration-2.svg":920,"./illustration-20.svg":921,"./illustration-21.svg":922,"./illustration-22.svg":923,"./illustration-3.svg":924,"./illustration-4.svg":925,"./illustration-5.svg":926,"./illustration-6.svg":927,"./illustration-7.svg":928,"./illustration-8.svg":929,"./illustration-9.svg":930};function r(t){var e=o(t);return n(e)}function o(t){if(!n.o(map,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return map[t]}r.keys=function(){return Object.keys(map)},r.resolve=o,t.exports=r,r.id=1481},1490:function(t,e,n){"use strict";n(1434)},1491:function(t,e,n){var r=n(18)(!1);r.push([t.i,'.text-editor{position:relative}.text-editor-buttons{position:absolute;right:18px;top:8px;z-index:2}.text-editor-buttons button{display:inline-flex;justify-content:center;align-items:center;width:24px;height:24px;margin-left:8px;border-radius:2px;border:1px solid transparent}.text-editor-buttons button.is-active{background-color:var(--v-greyBg-base);border-color:var(--v-greyLight-base)}.text-editor .ProseMirror{min-height:280px;margin-bottom:4px;padding:40px 12px 12px;border:1px solid #bebebe;font-size:13px;border-radius:16px;line-height:1.23}.text-editor .ProseMirror>*{position:relative}.text-editor .ProseMirror p{margin-bottom:0}.text-editor .ProseMirror ul{padding-left:28px}.text-editor .ProseMirror ul>li p{margin-bottom:0}.text-editor .ProseMirror strong{font-weight:700!important}.text-editor .ProseMirror.focus-visible,.text-editor .ProseMirror:focus,.text-editor .ProseMirror:focus-visible{outline:none!important}.text-editor .ProseMirror-focused:before{content:"";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:16px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}.text-editor .v-text-field__details{padding:0 14px}',""]),t.exports=r},1503:function(t,e,n){"use strict";n.r(e);n(31),n(96);var r=n(1586),o=n(1587),l=n(1577),c=n(1589),d={name:"Editor",components:{EditorContent:r.b},props:{value:{type:String,required:!0},counter:{type:Boolean,default:!1},autoLink:{type:Boolean,default:!1},limit:{type:Number,default:null}},data:function(){return{editor:null,text:"",isValid:!0,editorEl:null,keysPressed:{},isDirty:!1}},watch:{value:function(t){this.editor.getHTML()===t||this.editor.commands.setContent(t,!1)}},mounted:function(){var t=this;this.editor=new r.a({content:this.value,extensions:[o.a,l.a.configure({limit:this.limit}),c.a.configure({autolink:!0})]}),this.editor.on("create",(function(e){var n=e.editor;t.text=n.getText(),t.$nextTick((function(){t.editorEl=document.getElementsByClassName("ProseMirror")[0],t.editorEl&&(t.editorEl.addEventListener("keydown",t.keydownHandler),t.editorEl.addEventListener("keyup",t.keyupHandler))})),t.validation()})),this.editor.on("update",(function(e){var n=e.editor;t.isDirty=!0,t.text=n.getText(),t.validation(),t.$emit("update",t.text?n.getHTML():"")}))},beforeDestroy:function(){this.editorEl&&(this.editorEl.removeEventListener("keydown",this.keydownHandler),this.editorEl.removeEventListener("keyup",this.keyupHandler)),this.editor.destroy()},methods:{keydownHandler:function(t){this.keysPressed[t.keyCode]=!0,(t.ctrlKey||this.keysPressed[17]||this.keysPressed[91]||this.keysPressed[93]||this.keysPressed[224])&&this.keysPressed[13]?(t.preventDefault(),this.$emit("submit"),this.keysPressed={}):13!==t.keyCode||t.shiftKey||(t.preventDefault(),this.editor.commands.enter())},keyupHandler:function(t){delete this.keysPressed[t.keyCode]},validation:function(){var t=this.text.trim().length;this.isValid=!!t,t&&this.limit&&(this.isValid=t<=this.limit),this.$emit("validation",this.isValid)}}},h=(n(1490),n(22)),component=Object(h.a)(d,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"text-editor"},[t.editor?r("div",{staticClass:"text-editor-buttons"},[r("button",{class:{"is-active":t.editor.isActive("bold")},on:{click:function(e){e.stopPropagation(),e.preventDefault(),t.editor.chain().focus().toggleBold().run()}}},[r("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16"}},[r("use",{attrs:{"xlink:href":n(91)+"#editor-bold-icon"}})])]),t._v(" "),r("button",{class:{"is-active":t.editor.isActive("bulletList")},on:{click:function(e){e.stopPropagation(),e.preventDefault(),t.editor.chain().focus().toggleBulletList().run()}}},[r("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16"}},[r("use",{attrs:{"xlink:href":n(91)+"#editor-list-icon"}})])])]):t._e(),t._v(" "),r("editor-content",{attrs:{editor:t.editor}}),t._v(" "),t.counter?r("div",{staticClass:"v-text-field__details"},[t._m(0),t._v(" "),r("div",{class:["v-counter theme--light",{"error--text":!t.isValid&&t.isDirty}]},[t._v("\n      "+t._s(t.text.length)+" / "+t._s(t.limit)+"\n    ")])]):t._e()],1)}),[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"v-messages theme--light"},[e("div",{staticClass:"v-messages__wrapper"})])}],!1,null,null,null);e.default=component.exports},1511:function(t,e,n){"use strict";n(1445)},1512:function(t,e,n){var r=n(18)(!1);r.push([t.i,".remove-illustration-title{font-size:20px}",""]),t.exports=r},1519:function(t,e,n){"use strict";n(1448)},1520:function(t,e,n){var r=n(18)(!1);r.push([t.i,".user-setting-select[data-v-322ff0d6]{position:relative}",""]),t.exports=r},1523:function(t,e,n){"use strict";n.r(e);n(31);var r=n(370),o=/^\d+\.?\d{0,2}$/,l={name:"LessonPrice",components:{TextInput:r.default},props:{value:{type:[String,Number],required:!0},rules:{type:Array,default:function(){return[]}},length:{type:Number,required:!0,default:30},freeTrial:{type:Boolean,required:!1,default:!1}},data:function(){return{key:1,keyCode:null}},computed:{currentCurrencySymbol:function(){return this.$store.getters["currency/currentCurrencySymbol"]},currencyCode:function(){return{$:"USD","€":"EUR","£":"GBP","zł":"PLN",A$:"AUD",C$:"CAD"}[this.currentCurrencySymbol]||"USD"},value_:function(){return this.value||null},minimumPrice:function(){var t;return(null===(t={EUR:{30:7,60:11,90:16,120:21},GBP:{30:6,60:10,90:15,120:20},PLN:{30:30,60:50,90:70,120:85},USD:{30:8,60:12,90:17,120:22},AUD:{30:12,60:20,90:28,120:36},CAD:{30:11,60:18,90:25,120:32}}[this.currencyCode])||void 0===t?void 0:t[this.length])||10},minimumValidation:function(t){return 60===Number(length)||Number(t)>0?this.minimumPrice:0}},mounted:function(){var t;this.validation(null!==(t=this.value)&&void 0!==t?t:0,this.freeTrial)},methods:{updateValue:function(t){var e,n=this;o.test(t)||"Backspace"===this.keyCode||"Delete"===this.keyCode?e=t:(e=this.value,this.key++,this.$nextTick((function(){n.$refs.priceInput.focus()}))),this.keyCode=null,this.validation(e),this.$emit("input",e)},validation:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.minimumPrice;this.$emit("validation",!(!e&&(60===Number(length)&&Number(t)<n||Number(t)>0&&Number(t)<n)))},validatePrice:function(t){var e=this.minimumPrice;return!!this.$props.freeTrial||(60===Number(length)&&Number(t)<e?"Error: Minimum price is ".concat(e):!(Number(t)>0&&Number(t)<e)||"Error: Minimum price is ".concat(e))}}},c=n(22),component=Object(c.a)(l,(function(){var t=this,e=t.$createElement;return(t._self._c||e)("text-input",{key:t.key,ref:"priceInput",attrs:{value:t.value_,"type-class":"border-gradient",height:"32","hide-details":"",placeholder:"0.00",rules:t.rules.concat([t.validatePrice]),prefix:t.currentCurrencySymbol},on:{keydown:function(e){t.keyCode=e.code},input:function(e){return t.updateValue(e)}}})}),[],!1,null,null,null);e.default=component.exports},1551:function(t,e,n){var content=n(1603);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("65b3defe",content,!0,{sourceMap:!1})},1556:function(t,e,n){"use strict";var r=n(127),o=n(3);e.a=o.default.extend({name:"rippleable",directives:{ripple:r.a},props:{ripple:{type:[Boolean,Object],default:!0}},methods:{genRipple:function(){var data=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.ripple?(data.staticClass="v-input--selection-controls__ripple",data.directives=data.directives||[],data.directives.push({name:"ripple",value:{center:!0}}),this.$createElement("div",data)):null}}})},1564:function(t,e,n){var r=n(18)(!1);r.push([t.i,'.theme--light.v-input--selection-controls.v-input--is-disabled:not(.v-input--indeterminate) .v-icon{color:rgba(0,0,0,.26)!important}.theme--dark.v-input--selection-controls.v-input--is-disabled:not(.v-input--indeterminate) .v-icon{color:hsla(0,0%,100%,.3)!important}.v-input--selection-controls{margin-top:16px;padding-top:4px}.v-input--selection-controls>.v-input__append-outer,.v-input--selection-controls>.v-input__prepend-outer{margin-top:0;margin-bottom:0}.v-input--selection-controls:not(.v-input--hide-details)>.v-input__slot{margin-bottom:12px}.v-input--selection-controls .v-input__slot,.v-input--selection-controls .v-radio{cursor:pointer}.v-input--selection-controls .v-input__slot>.v-label,.v-input--selection-controls .v-radio>.v-label{align-items:center;display:inline-flex;flex:1 1 auto;height:auto}.v-input--selection-controls__input{color:inherit;display:inline-flex;flex:0 0 auto;height:24px;position:relative;transition:.3s cubic-bezier(.25,.8,.5,1);transition-property:transform;width:24px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__input .v-icon{width:100%}.v-application--is-ltr .v-input--selection-controls__input{margin-right:8px}.v-application--is-rtl .v-input--selection-controls__input{margin-left:8px}.v-input--selection-controls__input input[role=checkbox],.v-input--selection-controls__input input[role=radio],.v-input--selection-controls__input input[role=switch]{position:absolute;opacity:0;width:100%;height:100%;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__input+.v-label{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__ripple{border-radius:50%;cursor:pointer;height:34px;position:absolute;transition:inherit;width:34px;left:-12px;top:calc(50% - 24px);margin:7px}.v-input--selection-controls__ripple:before{border-radius:inherit;bottom:0;content:"";position:absolute;opacity:.2;left:0;right:0;top:0;transform-origin:center center;transform:scale(.2);transition:inherit}.v-input--selection-controls__ripple>.v-ripple__container{transform:scale(1.2)}.v-input--selection-controls.v-input--dense .v-input--selection-controls__ripple{width:28px;height:28px;left:-9px}.v-input--selection-controls.v-input--dense:not(.v-input--switch) .v-input--selection-controls__ripple{top:calc(50% - 21px)}.v-input--selection-controls.v-input{flex:0 1 auto}.v-input--selection-controls.v-input--is-focused .v-input--selection-controls__ripple:before,.v-input--selection-controls .v-radio--is-focused .v-input--selection-controls__ripple:before{background:currentColor;transform:scale(1.2)}.v-input--selection-controls__input:hover .v-input--selection-controls__ripple:before{background:currentColor;transform:scale(1.2);transition:none}',""]),t.exports=r},1580:function(t,e,n){var content=n(1644);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("26b71cad",content,!0,{sourceMap:!1})},1602:function(t,e,n){"use strict";n(1551)},1603:function(t,e,n){var r=n(18)(!1);r.push([t.i,'.v-application .v-dialog.illustration-picker{height:100%}@media only screen and (min-height:1080px){.v-application .v-dialog.illustration-picker{max-height:975px!important}}.v-application .v-dialog.illustration-picker .dialog-content{display:flex;flex-direction:column;height:100%}.v-application .v-dialog.illustration-picker .dialog-footer{position:absolute;bottom:0;left:0;display:flex;justify-content:flex-end;align-items:center;width:100%;height:104px;margin-top:0;padding:0 28px}@media only screen and (max-width:991px){.v-application .v-dialog.illustration-picker .dialog-footer{height:74px}}.v-application .v-dialog.illustration-picker>.v-card{height:100%;padding:88px 28px 104px!important}@media only screen and (max-width:991px){.v-application .v-dialog.illustration-picker>.v-card{padding:50px 18px 74px!important}}.v-application .v-dialog.illustration-picker .illustration-picker-header{position:absolute;top:0;left:0;width:100%;height:88px;display:flex;align-items:flex-end;padding:0 60px 24px 28px;font-size:20px;font-weight:700;line-height:1.1}@media only screen and (max-width:991px){.v-application .v-dialog.illustration-picker .illustration-picker-header{align-items:center;height:50px;padding-left:18px;padding-bottom:0;font-size:18px}}.v-application .v-dialog.illustration-picker .illustration-picker-body{height:100%;flex-grow:1;overflow-y:auto;overflow-x:hidden;padding:0 2px}.v-application .v-dialog.illustration-picker .illustration-picker-list{flex-wrap:wrap;width:calc(100% + 16px)}@media only screen and (max-width:991px){.v-application .v-dialog.illustration-picker .illustration-picker-list{width:calc(100% + 12px)}}.v-application .v-dialog.illustration-picker .illustration-picker-list .item{width:20%;flex:0 0 20%;padding:8px 16px 8px 0}@media only screen and (max-width:767px){.v-application .v-dialog.illustration-picker .illustration-picker-list .item{width:25%;flex:0 0 25%;padding:6px 12px 6px 0}}.v-application .v-dialog.illustration-picker .illustration-picker-list .item-helper{position:relative;border-radius:16px;background-clip:padding-box}.v-application .v-dialog.illustration-picker .illustration-picker-list .item-helper:not(.item-helper--selected){cursor:pointer}.v-application .v-dialog.illustration-picker .illustration-picker-list .item-helper:not(.item-helper--selected):hover:after{content:"";position:absolute;border-radius:inherit;top:0;left:0;bottom:0;right:0;border:1px solid rgba(45,45,45,.31)}.v-application .v-dialog.illustration-picker .illustration-picker-list .item-helper .v-image{border-radius:inherit;background:linear-gradient(95.18deg,#f2f8e9 15.34%,#ebf3fe 66.75%),#c4c4c4}.v-application .v-dialog.illustration-picker .illustration-picker-list .item-helper--selected:before{content:"";position:absolute;border-radius:18px;top:0;left:0;bottom:0;right:0;margin:-2px;background:linear-gradient(95.18deg,var(--v-green-base) 15.34%,var(--v-primary-base) 66.75%),#c4c4c4}',""]),t.exports=r},1620:function(t,e,n){"use strict";n.r(e);var r={name:"IllustrationDialog",components:{LDialog:n(149).default},props:{isShownIllustrationDialog:{type:Boolean,required:!0},currentIllustration:{type:Object,required:!0}},data:function(){return{newSelectedImage:null}},computed:{items:function(){return this.$store.state.settings.illustrationItems},selectedImage:function(){var t;return this.newSelectedImage||(null===(t=this.currentIllustration)||void 0===t?void 0:t.image)}}},o=(n(1602),n(22)),l=n(42),c=n.n(l),d=n(1327),h=n(261),component=Object(o.a)(r,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("l-dialog",t._g({attrs:{dialog:t.isShownIllustrationDialog,"max-width":"844","custom-class":"illustration-picker",fullscreen:t.$vuetify.breakpoint.smAndDown},scopedSlots:t._u([{key:"footer",fn:function(){return[r("div",{staticClass:"d-flex justify-end"},[r("v-btn",{staticClass:"font-weight-medium",attrs:{color:"primary"},on:{click:function(e){return t.$emit("update-image",t.selectedImage)}}},[r("svg",{staticClass:"mr-1",attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[r("use",{attrs:{"xlink:href":n(91)+"#save-icon"}})]),t._v("\n        "+t._s(t.$t("save_changes"))+"\n      ")])],1)]},proxy:!0}])},t.$listeners),[r("div",{staticClass:"illustration-picker-header"},[t._v("\n    "+t._s(t.$t("choose_illustration"))+":\n  ")]),t._v(" "),r("div",{staticClass:"illustration-picker-body l-scroll l-scroll--grey l-scroll--large"},[r("div",{staticClass:"illustration-picker-list d-flex"},t._l(t.items,(function(e){return r("div",{key:e.id,staticClass:"item"},[r("div",{class:["item-helper",{"item-helper--selected":t.selectedImage===e.image}],on:{click:function(n){t.newSelectedImage=e.image}}},[r("v-img",{attrs:{src:n(1481)("./"+e.image+".svg")}})],1)])})),0)])])}),[],!1,null,null,null);e.default=component.exports;c()(component,{LDialog:n(149).default}),c()(component,{VBtn:d.a,VImg:h.a})},1643:function(t,e,n){"use strict";n(1580)},1644:function(t,e,n){var r=n(18)(!1);r.push([t.i,".course-panel .v-expansion-panel-header{min-height:75px;padding:24px 0;font-size:20px;font-weight:600}@media only screen and (max-width:1439px){.course-panel .v-expansion-panel-header{padding:20px 0}}@media only screen and (max-width:767px){.course-panel .v-expansion-panel-header{min-height:48px;padding:10px 0;font-size:16px}}.course-panel .v-expansion-panel-content__wrap{padding:16px 0 40px}@media only screen and (max-width:1439px){.course-panel .v-expansion-panel-content__wrap{padding:16px 0 32px}}@media only screen and (max-width:767px){.course-panel .v-expansion-panel-content__wrap{padding:16px 0 24px}}.course-panel .input-wrap--image .image-select{color:var(--v-grey-base)}.course-panel .input-wrap--image .image-select-preview{width:80px;height:80px;border-radius:16px;background:linear-gradient(95.18deg,#f2f8e9 15.34%,#ebf3fe 66.75%),#c4c4c4;overflow:hidden}.course-panel .input-wrap--image .image-select-name{position:relative;margin-left:12px;padding-right:32px;font-size:14px}.course-panel .input-wrap--image .image-select-name .v-btn{position:absolute;right:0;top:1px}",""]),t.exports=r},1645:function(t,e,n){var content=n(1749);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("f170caf6",content,!0,{sourceMap:!1})},1697:function(t,e,n){"use strict";n.r(e);var r=n(10),o=n(2),l=n(13),c=(n(62),n(31),n(9),n(24),n(38),n(39),n(40),n(23),n(1641),n(7),n(8),n(14),n(6),n(15),n(370)),d=n(1523),h=n(1440),v=n(1503),m=n(1620),f=n(1450),_=n(208);function x(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function y(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?x(Object(source),!0).forEach((function(e){Object(o.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):x(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var w={name:"CourseItem",components:{TextInput:c.default,LessonPrice:d.default,UserSettingSelect:h.default,Editor:v.default,IllustrationDialog:m.default,ConfirmDialog:f.default},props:{item:{type:Object,required:!0},languages:{type:Array,required:!0},index:{type:Number,required:!0},isActive:{type:Boolean,required:!0}},data:function(){var t=this;return{mdiMinus:_.h,title:"",key:1,formValid:!0,priceValid:!0,courseStructureValid:!0,rules:{name:[function(t){return t&&t.length<=50},function(){return t.titleIsUnique||t.$t("name_should_be_unique")}],shortDescription:[function(t){return t&&t.length<=250}],description:[function(t){return t&&t.length<=500}]},isShownIllustrationDialog:!1,isShownIllustrationConfirmDialog:!1,isShownCourseConfirmDialog:!1,currentSelectedDuration:30}},computed:{lessonCountItems:function(){return this.$store.state.settings.lessonCountItems},lessonLengthItems:function(){return this.$store.state.settings.lessonLengthItems},totalPackagePrice:function(){return this.item.price*this.item.lessons},illustrationItems:function(){return this.$store.state.settings.illustrationItems},currentIllustration:function(){var t=this;if(!this.item.image)return{};var e=this.illustrationItems.filter((function(e){return e.image===t.item.image}));return Object(l.a)(e,1)[0]},isPublish:{get:function(){return this.item.isPublish},set:function(t){this.updateValue(t,"isPublish")}},currentCurrencySymbol:function(){return this.$store.getters["currency/currentCurrencySymbol"]},courses:function(){return this.$store.state.settings.courseItems},titleIsUnique:function(){var t=this;return!this.courses.filter((function(e){return e.id&&e.id!==t.item.id||e.uid&&e.uid!==t.item.uid})).map((function(t){return t.name})).includes(this.item.name)},valid:function(){var t;return this.titleIsUnique&&this.formValid&&this.priceValid&&!(null===(t=this.currentIllustration)||void 0===t||!t.id)&&this.courseStructureValid}},watch:{titleIsUnique:function(t,e){var n;null===(n=this.$refs["form-".concat(this.index)])||void 0===n||n.validate()}},mounted:function(){this.setTitle()},methods:{setTitle:function(){this.title=this.item.name||"".concat(this.$t("course")," ").concat(this.index+1)},updateImage:function(t){this.isShownIllustrationDialog=!1,this.updateValue(t,"image")},updatePriceTrialLesson:function(t){this.updateValue(t?Number.parseFloat(t):0,"price")},removeImage:function(){this.isShownIllustrationConfirmDialog=!1,this.updateValue(null,"image"),this.key++},updateCourseStructure:function(t){this.updateValue(t,"courseStructure")},updateValue:function(t,e){this.$store.commit("settings/UPDATE_COURSE_ITEM",y(y({},this.item),{},Object(o.a)({},e,t)))},removeCourse:function(){var t=this;return Object(r.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.item.id){e.next=3;break}return e.next=3,t.$store.dispatch("settings/removeCourse",t.item.id);case 3:t.$store.commit("settings/REMOVE_COURSE_ITEM",t.item),t.isShownCourseConfirmDialog=!1,t.$emit("scroll-to-top");case 6:case"end":return e.stop()}}),e)})))()},saveCourse:function(){var t=this,e=y({},this.item);delete e.slug,this.$store.dispatch("settings/".concat(this.item.uid?"addCourse":"updateCourse"),e).then((function(){return t.setTitle()}))}}},C=(n(1643),n(22)),k=n(42),V=n.n(k),S=n(1327),I=n(1360),$=n(1573),O=n(1574),D=n(1575),P=n(1363),j=n(339),E=n(261),M=n(1361),T=n(1747),B=n(1366),component=Object(C.a)(w,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("v-expansion-panel",{staticClass:"course-panel"},[r("v-expansion-panel-header",{attrs:{"disable-icon-rotate":""},scopedSlots:t._u([{key:"actions",fn:function(){return[t.isActive?[r("v-icon",{attrs:{color:"darkLight"}},[t._v("\n          "+t._s(t.mdiMinus)+"\n        ")])]:[r("v-img",{attrs:{src:n(880),width:"24",height:"24"}})]]},proxy:!0}])},[t._v("\n    "+t._s(t.title)+"\n    ")]),t._v(" "),r("v-expansion-panel-content",{attrs:{eager:""}},[r("v-form",{ref:"form-"+t.index,attrs:{value:t.valid},on:{input:function(e){t.formValid=e},submit:function(e){return e.preventDefault(),t.saveCourse.apply(null,arguments)}}},[r("div",{staticClass:"mb-md-2"},[r("v-row",[r("v-col",{staticClass:"col-12 col-sm-6 d-flex align-end"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-label"},[t._v("\n                "+t._s(t.$t("please_enter_name_of_course"))+"\n              ")]),t._v(" "),r("text-input",{attrs:{value:t.item.name,"type-class":"border-gradient",height:"44",counter:"50","hide-details":!1,rules:t.rules.name,placeholder:t.$t("name_of_course")},on:{input:function(e){return t.updateValue(e,"name")}}})],1)]),t._v(" "),r("v-col",{staticClass:"col-12 col-sm-6 d-flex align-end"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-label"},[t._v("\n                "+t._s(t.$t("number_of_lessons_in_course"))+"\n              ")]),t._v(" "),r("user-setting-select",{attrs:{value:{name:t.item.lessons},items:t.lessonCountItems,"hide-details":!1,"item-value":"name","attach-id":"course-lessons-"+(t.item.id||t.item.uid)},on:{change:function(e){return t.updateValue(e.name,"lessons")}}})],1)])],1)],1),t._v(" "),r("div",{staticClass:"mb-sm-2"},[r("v-row",[r("v-col",{staticClass:"col-12 col-sm-6 d-flex mb-2 mb-sm-0"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-label"},[t._v("\n                "+t._s(t.$t("duration_of_lessons"))+"\n              ")]),t._v(" "),r("user-setting-select",{attrs:{value:{name:t.item.length},items:t.lessonLengthItems,"item-value":"name","attach-id":"course-length-"+(t.item.id||t.item.uid)},on:{change:function(e){t.updateValue(e.name,"length"),t.currentSelectedDuration=e.name}}})],1)]),t._v(" "),r("v-col",{staticClass:"col-12 col-sm-6 d-flex mb-2 mb-sm-0"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-label"},[t._v("\n                "+t._s(t.$t("price_per_lesson"))+"\n              ")]),t._v(" "),r("lesson-price",{attrs:{value:t.item.price,length:t.currentSelectedDuration},on:{validation:function(e){t.priceValid=e},input:t.updatePriceTrialLesson}}),t._v(" "),t.totalPackagePrice?r("div",{staticClass:"input-wrap-label mt-1 mb-0"},[t._v("\n                "+t._s(t.$t("Total package price"))+": "+t._s(t.currentCurrencySymbol)+t._s(t.totalPackagePrice.toFixed(2))+"\n              ")]):t._e()],1)])],1)],1),t._v(" "),r("div",{staticClass:"mb-2 mb-md-4"},[r("v-row",[r("v-col",{staticClass:"col-12 col-sm-6 d-flex"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-label"},[t._v("\n                "+t._s(t.$t("language_of_course"))+"\n              ")]),t._v(" "),r("user-setting-select",{attrs:{value:{id:t.item.languageId},items:t.languages,"hide-selected":!1,"attach-id":"course-language-"+(t.item.id||t.item.uid)},on:{change:function(e){return t.updateValue(e.id,"languageId")}}})],1)])],1)],1),t._v(" "),r("div",{staticClass:"mb-md-2"},[r("v-row",[r("v-col",{staticClass:"col-12 col-sm-6 d-flex"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-label"},[t._v("\n                "+t._s(t.$t("please_enter_short_description_of_course"))+":\n              ")]),t._v(" "),r("div",[r("v-textarea",{staticClass:"l-textarea",attrs:{value:t.item.shortDescription,"no-resize":"",height:"234",solo:"",dense:"",counter:"250",rules:t.rules.shortDescription},on:{input:function(e){return t.updateValue(e,"shortDescription")}}})],1)])]),t._v(" "),r("v-col",{staticClass:"col-12 col-sm-6 d-flex"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-label"},[t._v("\n                "+t._s(t.$t("please_enter_intro_to_course"))+":\n              ")]),t._v(" "),r("div",[r("v-textarea",{staticClass:"l-textarea",attrs:{value:t.item.introductionToCourse,"no-resize":"",height:"234",solo:"",dense:"",counter:"500",rules:t.rules.description},on:{input:function(e){return t.updateValue(e,"introductionToCourse")}}})],1)])])],1)],1),t._v(" "),r("div",{staticClass:"mb-2 mb-md-3"},[r("v-row",[r("v-col",{staticClass:"col-12 d-flex"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-label"},[t._v("\n                "+t._s(t.$t("please_enter_structure_of_course"))+":\n              ")]),t._v(" "),r("div",[r("editor",{attrs:{value:t.item.courseStructure,limit:1500,counter:""},on:{validation:function(e){t.courseStructureValid=e},update:t.updateCourseStructure}})],1)])])],1)],1),t._v(" "),r("div",[r("v-row",[r("v-col",{staticClass:"col-12 col-sm-6 d-flex mb-2 mb-sm-0"},[r("div",{staticClass:"input-wrap input-wrap--image"},[r("div",{staticClass:"input-wrap-label"},[t._v("\n                "+t._s(t.$t("please_choose_illustration_that_best_suits_to_course"))+":\n              ")]),t._v(" "),r("div",{staticClass:"image-select"},[t.item.image?[r("div",{staticClass:"d-flex align-center"},[r("div",{staticClass:"image-select-preview"},[r("v-img",{attrs:{eager:"",src:n(1481)("./"+t.item.image+".svg")}})],1),t._v(" "),r("div",{staticClass:"image-select-name"},[t._v("\n                      "+t._s(t.currentIllustration.name)+"\n                      "),r("v-btn",{attrs:{width:"18",height:"18",icon:""},on:{click:function(e){t.isShownIllustrationConfirmDialog=!0}}},[r("v-img",{attrs:{src:n(374),width:"15",height:"15"}})],1)],1)])]:[r("div",{staticClass:"image-select--no-data body-2"},[t._v("\n                    "+t._s(t.$t("no_illustration_chosen"))+"\n                  ")])]],2),t._v(" "),r("v-btn",{staticClass:"gradient font-weight-medium mt-3",on:{click:function(e){t.isShownIllustrationDialog=!0}}},[r("div",{staticClass:"text--gradient"},[t._v("\n                  "+t._s(t.$t("choose_illustration"))+"\n                ")])])],1)]),t._v(" "),r("v-col",{staticClass:"col-12 col-sm-6"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-label"},[t._v("\n                "+t._s(t.$t("course_intro_video_please_enter_youtube_link"))+"\n              ")]),t._v(" "),r("text-input",{attrs:{value:t.item.youtube,"type-class":"border-gradient",height:"44","hide-details":"",placeholder:t.$t("youtube_link")},on:{input:function(e){return t.updateValue(e,"youtube")}}})],1),t._v(" "),r("div",{staticClass:"mt-3"},[r("div",{staticClass:"input-wrap-label mb-0"},[t._v("\n                "+t._s(t.$t("are_you_ready_to_publish_this_course"))+" "),r("br"),t._v("\n                "+t._s(t.$t("select_this_option_when_you_are_ready_to_begin_selling_this_course"))+"\n              ")]),t._v(" "),r("div",{staticClass:"d-flex justify-end"},[r("v-switch",{attrs:{inset:"",ripple:!1,color:"success",dense:"","hide-details":""},model:{value:t.isPublish,callback:function(e){t.isPublish=e},expression:"isPublish"}})],1)])])],1)],1),t._v(" "),r("div",[r("v-row",[r("v-col",{staticClass:"col-12"},[r("div",{staticClass:"d-flex justify-space-between justify-sm-end mt-3"},[r("v-btn",{staticClass:"font-weight-medium mt-1 mx-1",attrs:{color:"error"},on:{click:function(e){t.isShownCourseConfirmDialog=!0}}},[t._v("\n                "+t._s(t.$t("delete_course"))+"\n              ")]),t._v(" "),r("v-btn",{staticClass:"font-weight-medium mt-1 mx-1",attrs:{color:"primary",disabled:!t.valid,type:"submit"}},[r("svg",{staticClass:"mr-1",attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[r("use",{attrs:{"xlink:href":n(91)+"#save-icon"}})]),t._v("\n                "+t._s(t.$t("save_changes"))+"\n              ")])],1)])],1)],1)]),t._v(" "),r("illustration-dialog",{key:t.key,attrs:{"is-shown-illustration-dialog":t.isShownIllustrationDialog,"current-illustration":t.currentIllustration},on:{"update-image":t.updateImage,"close-dialog":function(e){t.isShownIllustrationDialog=!1}}}),t._v(" "),r("confirm-dialog",{attrs:{"is-shown-confirm-dialog":t.isShownIllustrationConfirmDialog},on:{confirm:t.removeImage,"close-dialog":function(e){t.isShownIllustrationConfirmDialog=!1}}},[t._v("\n      "+t._s(t.$t("illustration_will_be_deleted_from_course_you_will_have_to_choose_another_one"))+"\n      "),r("br"),t._v("\n      "+t._s(t.$t("do_you_confirm_that"))+"\n    ")]),t._v(" "),r("confirm-dialog",{attrs:{"is-shown-confirm-dialog":t.isShownCourseConfirmDialog,"cancel-text-button":"no","confirm-text-button":"yes"},on:{confirm:t.removeCourse,"close-dialog":function(e){t.isShownCourseConfirmDialog=!1}}},[t._v("\n      "+t._s(t.$t("are_you_sure_you_want_to_delete_this_course_permanently"))+"\n    ")])],1)],1)}),[],!1,null,null,null);e.default=component.exports;V()(component,{UserSettingSelect:n(1440).default,ConfirmDialog:n(1450).default}),V()(component,{VBtn:S.a,VCol:I.a,VExpansionPanel:$.a,VExpansionPanelContent:O.a,VExpansionPanelHeader:D.a,VForm:P.a,VIcon:j.a,VImg:E.a,VRow:M.a,VSwitch:T.a,VTextarea:B.a})},1748:function(t,e,n){"use strict";n(1645)},1749:function(t,e,n){var r=n(18)(!1);r.push([t.i,".border-top[data-v-e39f7fc0]{border-top:1px solid #ccc}.v-expansion-panel[data-v-e39f7fc0]:before{box-shadow:none}",""]),t.exports=r},1929:function(t,e,n){"use strict";n.r(e);n(7),n(8),n(9),n(14),n(6),n(15);var r=n(2),o=(n(39),n(40),n(63),n(725)),l=n.n(o),c=n(1375),d=n(1697),h=n(859);function v(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function m(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?v(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):v(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var f={name:"CoursesInfo",components:{UserSettingTemplate:c.default,CourseItem:d.default},data:function(){return{getSlugByString:h.getSlugByString,panel:void 0}},computed:{items:function(){var t=this;return this.$store.state.settings.courseItems.map((function(e){return m(m({},e),{},{slug:e.name?t.getSlugByString(e.name):e.uid})}))},languagesTaught:function(){var t,e;return null!==(t=null===(e=this.$store.state.settings.languagesItem)||void 0===e?void 0:e.languagesTaught)&&void 0!==t?t:[]}},watch:{panel:function(){var t,e=this,n=document.getElementById(null===(t=this.items[this.panel])||void 0===t?void 0:t.slug);n&&window.setTimeout((function(){e.goTo(n)}),300)}},beforeCreate:function(){this.$store.dispatch("settings/getCourses"),this.$store.dispatch("settings/getLanguages")},methods:{addCourse:function(){var t=this,e=l()();this.$store.commit("settings/ADD_COURSE_ITEM",e),this.$nextTick((function(){t.panel=t.items.length-1}))},scrollToTop:function(){this.panel=void 0,this.goTo()},goTo:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.$vuetify.goTo(t,{duration:0,offset:0,easing:"linear"})}}},_=(n(1748),n(22)),x=n(42),y=n.n(x),w=n(1327),C=n(1360),k=n(1592),V=n(261),S=n(1361),component=Object(_.a)(f,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("user-setting-template",{attrs:{title:t.$t("courses"),"hide-footer":""}},[t.items.length?r("v-row",[r("v-col",{staticClass:"col-12"},[r("v-expansion-panels",{attrs:{accordion:""},model:{value:t.panel,callback:function(e){t.panel=e},expression:"panel"}},t._l(t.items,(function(e,n){return r("course-item",{key:n,attrs:{id:e.slug,item:e,index:n,"is-active":n===t.panel,languages:t.languagesTaught},on:{"scroll-to-top":t.scrollToTop}})})),1)],1)],1):t._e(),t._v(" "),r("v-row",[r("v-col",{staticClass:"col-12"},[r("div",{class:{"border-top":t.items.length}},[r("v-btn",{staticClass:"gradient font-weight-medium mt-4",on:{click:t.addCourse}},[r("div",{staticClass:"mr-1"},[r("v-img",{attrs:{src:n(880),width:"20",height:"20"}})],1),t._v(" "),r("div",{staticClass:"text--gradient"},[t._v("\n            "+t._s(t.$t("add_course"))+"\n          ")])])],1)])],1)],1)}),[],!1,null,"e39f7fc0",null);e.default=component.exports;y()(component,{UserSettingTemplate:n(1375).default}),y()(component,{VBtn:w.a,VCol:C.a,VExpansionPanels:k.a,VImg:V.a,VRow:S.a})}}]);