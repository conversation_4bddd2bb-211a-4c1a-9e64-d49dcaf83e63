(window.webpackJsonp=window.webpackJsonp||[]).push([[147,188],{1960:function(e,n,t){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}t.r(n),t.d(n,"loadStripe",(function(){return E}));var o,c="https://js.stripe.com/v3",l=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,d="loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used",f=function(e){var n=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",script=document.createElement("script");script.src="".concat(c).concat(n);var t=document.head||document.body;if(!t)throw new Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return t.appendChild(script),script},v=null,m=null,w=null,h=function(e){return null!==v?v:(v=new Promise((function(n,t){if("undefined"!=typeof window&&"undefined"!=typeof document)if(window.Stripe&&e&&console.warn(d),window.Stripe)n(window.Stripe);else try{var script=function(){for(var e=document.querySelectorAll('script[src^="'.concat(c,'"]')),i=0;i<e.length;i++){var script=e[i];if(l.test(script.src))return script}return null}();if(script&&e)console.warn(d);else if(script){if(script&&null!==w&&null!==m){var r;script.removeEventListener("load",w),script.removeEventListener("error",m),null===(r=script.parentNode)||void 0===r||r.removeChild(script),script=f(e)}}else script=f(e);w=function(e,n){return function(){window.Stripe?e(window.Stripe):n(new Error("Stripe.js not available"))}}(n,t),m=function(e){return function(){e(new Error("Failed to load Stripe.js"))}}(t),script.addEventListener("load",w),script.addEventListener("error",m)}catch(e){return void t(e)}else n(null)}))).catch((function(e){return v=null,Promise.reject(e)}))},y=function(e,n,t){if(null===e)return null;var r=e.apply(void 0,n);return function(e,n){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"3.4.0",startTime:n})}(r,t),r},S=function(e){var n="invalid load parameters; expected object of shape\n\n    {advancedFraudSignals: boolean}\n\nbut received\n\n    ".concat(JSON.stringify(e),"\n");if(null===e||"object"!==r(e))throw new Error(n);if(1===Object.keys(e).length&&"boolean"==typeof e.advancedFraudSignals)return e;throw new Error(n)},j=!1,E=function(){for(var e=arguments.length,n=new Array(e),t=0;t<e;t++)n[t]=arguments[t];j=!0;var r=Date.now();return h(o).then((function(e){return y(e,n,r)}))};E.setLoadParameters=function(e){if(j&&o){var n=S(e);if(Object.keys(n).reduce((function(n,t){var r;return n&&e[t]===(null===(r=o)||void 0===r?void 0:r[t])}),!0))return}if(j)throw new Error("You cannot change load parameters after calling loadStripe");o=S(e)}},2219:function(e,n,t){"use strict";t.r(n);var r=t(10),o=(t(62),t(1960)),c={name:"StripeForm",beforeMount:function(){var e=this;return Object(r.a)(regeneratorRuntime.mark((function n(){var t;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(t=e.$route.params.id,e.$stripe){n.next=5;break}return n.next=4,Object(o.loadStripe)("pk_test_MQIviMptDCcknTZ25dojL7Z400OHkru9Qw");case 4:e.$stripe=n.sent;case 5:return n.next=7,e.$cookiz.set("thank_you_page_allowed",1,{domain:".langu.loc",path:"/"});case 7:e.$stripe?e.$stripe.redirectToCheckout({sessionId:t}).then((function(){console.log("success")})).catch((function(e){console.log("error",e)})):console.log("stripe not found");case 8:case"end":return n.stop()}}),n)})))()}},l=t(22),component=Object(l.a)(c,(function(){var e=this.$createElement;return(this._self._c||e)("div",{staticStyle:{"min-height":"calc(100vh - 300px)"}})}),[],!1,null,null,null);n.default=component.exports}}]);