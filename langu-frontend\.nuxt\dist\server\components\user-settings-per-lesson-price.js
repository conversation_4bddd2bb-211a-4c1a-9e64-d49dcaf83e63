exports.ids = [116,115];
exports.modules = {

/***/ 1114:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/LessonPrice.vue?vue&type=template&id=1ecf865e&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('text-input',{key:_vm.key,ref:"priceInput",attrs:{"value":_vm.value_,"type-class":"border-gradient","height":"32","hide-details":"","placeholder":"0.00","rules":_vm.rules.concat( [_vm.validatePrice]),"prefix":_vm.currentCurrencySymbol},on:{"keydown":function($event){_vm.keyCode = $event.code},"input":function($event){return _vm.updateValue($event)}}})}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/LessonPrice.vue?vue&type=template&id=1ecf865e&

// EXTERNAL MODULE: ./components/form/TextInput.vue + 4 modules
var TextInput = __webpack_require__(102);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/LessonPrice.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

const regex = /^\d+\.?\d{0,2}$/;
/* harmony default export */ var LessonPricevue_type_script_lang_js_ = ({
  name: 'LessonPrice',
  components: {
    TextInput: TextInput["default"]
  },
  props: {
    value: {
      type: [String, Number],
      required: true
    },
    rules: {
      type: Array,
      default: () => []
    },
    length: {
      type: Number,
      required: true,
      default: 30
    },
    freeTrial: {
      type: Boolean,
      required: false,
      default: false
    }
  },

  data() {
    return {
      key: 1,
      keyCode: null
    };
  },

  computed: {
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    },

    currencyCode() {
      const currencyMap = {
        $: 'USD',
        '€': 'EUR',
        '£': 'GBP',
        zł: 'PLN',
        A$: 'AUD',
        C$: 'CAD'
      };
      return currencyMap[this.currentCurrencySymbol] || 'USD';
    },

    value_() {
      return this.value || null;
    },

    minimumPrice() {
      var _pricingMap$this$curr;

      const pricingMap = {
        EUR: {
          30: 7,
          60: 11,
          90: 16,
          120: 21
        },
        GBP: {
          30: 6,
          60: 10,
          90: 15,
          120: 20
        },
        PLN: {
          30: 30,
          60: 50,
          90: 70,
          120: 85
        },
        USD: {
          30: 8,
          60: 12,
          90: 17,
          120: 22
        },
        AUD: {
          30: 12,
          60: 20,
          90: 28,
          120: 36
        },
        CAD: {
          30: 11,
          60: 18,
          90: 25,
          120: 32
        }
      };
      return ((_pricingMap$this$curr = pricingMap[this.currencyCode]) === null || _pricingMap$this$curr === void 0 ? void 0 : _pricingMap$this$curr[this.length]) || 10;
    },

    minimumValidation(value) {
      if (Number(length) === 60 || Number(value) > 0) {
        return this.minimumPrice;
      } else {
        return 0;
      }
    }

  },

  mounted() {
    var _this$value;

    this.validation((_this$value = this.value) !== null && _this$value !== void 0 ? _this$value : 0, this.freeTrial);
  },

  methods: {
    updateValue(event) {
      let value;

      if (regex.test(event) || this.keyCode === 'Backspace' || this.keyCode === 'Delete') {
        value = event;
      } else {
        value = this.value;
        this.key++;
        this.$nextTick(() => {
          this.$refs.priceInput.focus();
        });
      }

      this.keyCode = null;
      this.validation(value);
      this.$emit('input', value);
    },

    validation(value, isFreeTrial = false) {
      const minPrice = this.minimumPrice;
      this.$emit('validation', function () {
        if (isFreeTrial) {
          return true;
        } else if (Number(length) === 60 && Number(value) < minPrice) {
          return false;
        } else if (Number(value) > 0 && Number(value) < minPrice) {
          return false;
        }

        return true;
      }());
    },

    validatePrice(value) {
      const minPrice = this.minimumPrice;

      if (this.$props.freeTrial) {
        return true;
      } else if (Number(length) === 60 && Number(value) < minPrice) {
        return `Error: Minimum price is ${minPrice}`;
      } else if (Number(value) > 0 && Number(value) < minPrice) {
        return `Error: Minimum price is ${minPrice}`;
      }

      return true;
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/LessonPrice.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_LessonPricevue_type_script_lang_js_ = (LessonPricevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/user-settings/LessonPrice.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_LessonPricevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "fa29dedc"
  
)

/* harmony default export */ var LessonPrice = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1271:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/PerLessonPrice.vue?vue&type=template&id=b047eada&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('lesson-price',{attrs:{"value":_vm.value,"rules":_vm.rules,"length":_vm.length},on:{"input":_vm.updateValue}})}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/PerLessonPrice.vue?vue&type=template&id=b047eada&

// EXTERNAL MODULE: ./components/user-settings/LessonPrice.vue + 4 modules
var LessonPrice = __webpack_require__(1114);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/PerLessonPrice.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//

/* harmony default export */ var PerLessonPricevue_type_script_lang_js_ = ({
  name: 'PerLessonPrice',
  components: {
    LessonPrice: LessonPrice["default"]
  },
  props: {
    items: {
      type: Array,
      required: true
    },
    length: {
      type: Number,
      required: true
    },
    lessons: {
      type: Number,
      required: true
    },
    rules: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      key: this.length + this.lessons,
      keyCode: null
    };
  },

  computed: {
    value() {
      var _this$items$find;

      return (_this$items$find = this.items.find(item => item.length === this.length && item.lessons === this.lessons)) === null || _this$items$find === void 0 ? void 0 : _this$items$find.price;
    }

  },
  methods: {
    updateValue(value) {
      this.$store.commit('settings/UPDATE_LESSON_PRICE', {
        value,
        length: this.length,
        lessons: this.lessons
      });
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/PerLessonPrice.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_PerLessonPricevue_type_script_lang_js_ = (PerLessonPricevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/user-settings/PerLessonPrice.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_PerLessonPricevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "50089bfa"
  
)

/* harmony default export */ var PerLessonPrice = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=user-settings-per-lesson-price.js.map