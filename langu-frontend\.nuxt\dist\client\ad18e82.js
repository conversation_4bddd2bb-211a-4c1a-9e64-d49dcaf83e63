(window.webpackJsonp=window.webpackJsonp||[]).push([[57],{1445:function(t,e,n){var content=n(1512);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("f203485e",content,!0,{sourceMap:!1})},1450:function(t,e,n){"use strict";n.r(e);var o={name:"ConfirmDialog",props:{isShownConfirmDialog:{type:Boolean,required:!0},cancelTextButton:{type:String,default:"close"},confirmTextButton:{type:String,default:"confirm"}}},l=(n(1511),n(22)),r=n(42),c=n.n(r),f=n(1327),component=Object(l.a)(o,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.isShownConfirmDialog?n("l-dialog",t._g({attrs:{dialog:t.isShownConfirmDialog,"hide-close-button":"","max-width":"418","custom-class":"remove-illustration text-center"}},t.$listeners),[n("div",[n("div",{staticClass:"remove-illustration-title font-weight-medium"},[t._v("\n      "+t._s(t.$t("are_you_sure"))+"\n    ")]),t._v(" "),n("div",{staticClass:"mt-2"},[t._t("default")],2),t._v(" "),n("div",{staticClass:"d-flex justify-space-around justify-sm-space-between flex-wrap mt-2"},[n("v-btn",{staticClass:"gradient font-weight-medium my-1",on:{click:function(e){return t.$emit("close-dialog")}}},[n("div",{staticClass:"text--gradient"},[t._v("\n          "+t._s(t.$t(t.cancelTextButton))+"\n        ")])]),t._v(" "),n("v-btn",{staticClass:"font-weight-medium my-1",attrs:{color:"primary"},on:{click:function(e){return t.$emit("confirm")}}},[t._v("\n        "+t._s(t.$t(t.confirmTextButton))+"\n      ")])],1)])]):t._e()}),[],!1,null,null,null);e.default=component.exports;c()(component,{LDialog:n(149).default}),c()(component,{VBtn:f.a})},1511:function(t,e,n){"use strict";n(1445)},1512:function(t,e,n){var o=n(18)(!1);o.push([t.i,".remove-illustration-title{font-size:20px}",""]),t.exports=o}}]);