exports.ids = [25];
exports.modules = {

/***/ 1171:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1265);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("77e2f3f6", content, true, context)
};

/***/ }),

/***/ 1264:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_VideoActions_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1171);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_VideoActions_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_VideoActions_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_VideoActions_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_VideoActions_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1265:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".stream-controls{position:absolute;left:50%;bottom:-38px;z-index:10;transform:translateX(-50%)}.stream-controls .toolbar-button-wrapper{width:40px;height:40px}.stream-controls .toolbar-button-wrapper button svg{margin:0 auto}.stream-controls .toolbar-button-wrapper button:focus{outline:none}.stream-controls .toolbar-button-wrapper-full-screen button{padding:9px}.stream-controls .hover-btn-info{left:50%;right:auto;top:auto;bottom:-36px;transform:translateX(-50%)}.stream-controls .hover-btn-info:after{left:50%;top:-9px;right:auto;border:5px solid transparent;border-bottom-color:#444;transform:translateX(-50%)}.stream-controls .stream-controls-switch{display:flex;justify-content:center;align-items:center;margin-top:-6px;padding:10px 6px 3px;background:#fff;border:none;border-radius:0 0 6px 6px;font-size:13px;line-height:1;outline:none;box-shadow:0 2px 10px rgba(0,0,0,.25)}.stream-controls .stream-controls-switch .toolbar-button-wrapper{width:18px;height:18px}.stream-controls .stream-controls-switch .toolbar-button-wrapper:not(:last-child){margin-right:4px}.stream-controls .stream-controls-switch .toolbar-button-item{min-width:18px!important;font-size:13px!important;border-radius:50%!important;overflow:hidden}.stream-controls .stream-controls-switch .toolbar-button-item--selected{color:#fff;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%)!important}.toolbar-button-icon.hand-raised{fill:#ffc107!important}.toolbar-button-icon.chat-enabled,.toolbar-button-icon.participants-enabled{fill:#2196f3!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1278:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/video/VideoActions.vue?vue&type=template&id=bff1bf3c&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"stream-controls"},[_vm._ssrNode("<div id=\"video-window-buttons\" class=\"video-window-buttons-wrap\"><div class=\"stream-controls-wrapper cursor-auto\"><div class=\"toolbar-button-wrapper\"><button data-stream-toggle-video type=\"button\""+(_vm._ssrAttr("disabled",!_vm.isJoined))+" class=\"toolbar-button-item cursor-pointer\">"+((_vm.settings.isVideoEnabled)?("<svg width=\"51\" height=\"33\" viewBox=\"0 0 51 33\" class=\"toolbar-button-icon\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(548)) + "#videocam")))+"></use></svg>"):(!_vm.settings.isVideoEnabled)?("<svg width=\"39\" height=\"33\" viewBox=\"0 0 39 33\" class=\"toolbar-button-icon\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(548)) + "#videocam_off")))+"></use></svg>"):"<!---->")+"</button> <div class=\"hover-btn-info\">"+((_vm.settings.isVideoEnabled)?(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('turn_off_camera'))+"\n          ")):(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('turn_on_camera'))+"\n          ")))+"</div></div> <div class=\"toolbar-button-wrapper\"><button data-stream-toggle-audio type=\"button\""+(_vm._ssrAttr("disabled",!_vm.isJoined))+" class=\"toolbar-button-item cursor-pointer\">"+((!_vm.settings.isMuted)?("<svg width=\"23\" height=\"33\" viewBox=\"0 0 23 33\" class=\"toolbar-button-icon\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(547)) + "#microphone")))+"></use></svg>"):"<!---->")+" "+((_vm.settings.isMuted)?("<svg width=\"31\" height=\"34\" viewBox=\"0 0 31 34\" class=\"toolbar-button-icon\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(547)) + "#microphone-off")))+"></use></svg>"):"<!---->")+"</button> <div class=\"hover-btn-info\">"+((!_vm.settings.isMuted)?(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('mute_microphone'))+"\n          ")):(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('unmute_microphone'))+"\n          ")))+"</div></div> <div class=\"toolbar-button-wrapper toolbar-button-wrapper-full-screen\"><button data-stream-toggle-full-screen type=\"button\""+(_vm._ssrAttr("disabled",!_vm.isJoined))+" class=\"toolbar-button-item cursor-pointer\">"+((!_vm.settings.isFullscreenEnabled)?("<svg width=\"31\" height=\"31\" viewBox=\"0 0 31 31\" class=\"toolbar-button-icon\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(546)) + "#full_screen")))+"></use></svg>"):"<!---->")+" "+((_vm.settings.isFullscreenEnabled)?("<svg width=\"31\" height=\"31\" viewBox=\"0 0 31 31\" class=\"toolbar-button-icon\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(546)) + "#window_screen")))+"></use></svg>"):"<!---->")+"</button> <div class=\"hover-btn-info\">"+((!_vm.settings.isFullscreenEnabled)?(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('full_screen_video'))+"\n          ")):(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('leave_full_screen_video'))+"\n          ")))+"</div></div> <div class=\"toolbar-button-wrapper\"><button data-stream-toggle-screen-share type=\"button\""+(_vm._ssrAttr("disabled",_vm.isScreenShareDisabled || (!_vm.isJoined && _vm.type === 'twilio')))+" class=\"toolbar-button-item cursor-pointer\">"+((_vm.settings.isScreenShareEnabled)?("<svg width=\"38\" height=\"35\" viewBox=\"0 0 38 35\" class=\"toolbar-button-icon\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(521)) + "#not_share")))+"></use></svg>"):"<!---->")+" "+((!_vm.settings.isScreenShareEnabled)?("<svg width=\"37\" height=\"28\" viewBox=\"0 0 37 28\" class=\"toolbar-button-icon\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(521)) + "#share")))+"></use></svg>"):"<!---->")+"</button> <div class=\"hover-btn-info\">"+((_vm.isScreenShareDisabled)?(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('share_is_disabled'))+"\n          ")):(((!_vm.settings.isScreenShareEnabled)?(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('share_my_screen'))+"\n            ")):(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('stop_screenshare'))+"\n            ")))))+"</div></div> "+((_vm.type === 'whereby')?("<div class=\"toolbar-button-wrapper\"><button data-stream-toggle-hand-raise type=\"button\""+(_vm._ssrAttr("disabled",!_vm.isJoined))+" class=\"toolbar-button-item cursor-pointer\"><svg width=\"24\" height=\"33\" viewBox=\"0 0 24 33\""+(_vm._ssrClass(null,[
              'toolbar-button-icon',
              { 'hand-raised': _vm.settings.isHandRaised } ]))+"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(600)) + "#hand")))+"></use></svg></button> <div class=\"hover-btn-info\">"+((_vm.settings.isHandRaised)?(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('lower_hand'))+"\n          ")):(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('raise_hand'))+"\n          ")))+"</div></div>"):"<!---->")+" "+((_vm.type === 'whereby')?("<div class=\"toolbar-button-wrapper\"><button data-stream-toggle-chat type=\"button\""+(_vm._ssrAttr("disabled",!_vm.isJoined))+" class=\"toolbar-button-item cursor-pointer\"><svg width=\"28\" height=\"28\" viewBox=\"0 0 28 28\""+(_vm._ssrClass(null,[
              'toolbar-button-icon',
              { 'chat-enabled': _vm.settings.isChatEnabled } ]))+"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(595)) + "#chat")))+"></use></svg></button> <div class=\"hover-btn-info\">"+((_vm.settings.isChatEnabled)?(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('hide_chat'))+"\n          ")):(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('show_chat'))+"\n          ")))+"</div></div>"):"<!---->")+" "+((_vm.type === 'whereby')?("<div class=\"toolbar-button-wrapper\"><button data-stream-toggle-participants type=\"button\""+(_vm._ssrAttr("disabled",!_vm.isJoined))+" class=\"toolbar-button-item cursor-pointer\"><svg width=\"32\" height=\"28\" viewBox=\"0 0 32 28\""+(_vm._ssrClass(null,[
              'toolbar-button-icon',
              { 'participants-enabled': _vm.settings.isParticipantsEnabled } ]))+"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(601)) + "#participants")))+"></use></svg></button> <div class=\"hover-btn-info\">"+((_vm.settings.isParticipantsEnabled)?(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('hide_participants'))+"\n          ")):(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('show_participants'))+"\n          ")))+"</div></div>"):"<!---->")+"</div></div>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/classroom/video/VideoActions.vue?vue&type=template&id=bff1bf3c&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/video/VideoActions.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var VideoActionsvue_type_script_lang_js_ = ({
  name: 'VideoActions',
  props: {
    settings: {
      type: Object,
      required: true
    },
    isJoined: {
      type: Boolean,
      required: true
    },
    isScreenShareDisabled: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      required: true
    }
  },
  computed: {
    role() {
      return this.$store.getters['classroom/role'];
    }

  }
});
// CONCATENATED MODULE: ./components/classroom/video/VideoActions.vue?vue&type=script&lang=js&
 /* harmony default export */ var video_VideoActionsvue_type_script_lang_js_ = (VideoActionsvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/classroom/video/VideoActions.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1264)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  video_VideoActionsvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "168b8df8"
  
)

/* harmony default export */ var VideoActions = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=classroom-video-actions.js.map