(window.webpackJsonp=window.webpackJsonp||[]).push([[164],{2225:function(e,t,n){"use strict";n.r(t);var r=n(10),o=(n(62),n(35),n(173),n(71),{name:"UserMessagesView",components:{MessagesPage:n(1918).default},middleware:"authenticated",asyncData:function(e){return Object(r.a)(regeneratorRuntime.mark((function t(){var n,o,c,l,d,m;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.store,o=e.route,c=e.query,l=null==c?void 0:c.search,d=+o.params.threadId,t.next=5,n.dispatch("message/getItems",{page:1,searchQuery:l}).then(function(){var e=Object(r.a)(regeneratorRuntime.mark((function e(data){var t,r,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(m=null!==(t=null==data?void 0:data.count)&&void 0!==t?t:0,null==data||null===(r=data.threads)||void 0===r||!r.length){e.next=6;break}return o=data.threads.find((function(e){return e.id===d})),n.commit("message/SET_ITEM",o||{id:d}),e.next=6,n.dispatch("message/getConversation",{threadId:d});case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 5:return t.abrupt("return",{count:m});case 6:case"end":return t.stop()}}),t)})))()},head:function(){return{title:this.$t("user_messages_page.seo_title"),meta:[{hid:"description",name:"description",content:this.$t("user_messages_page.seo_description")},{hid:"og:title",name:"og:title",property:"og:title",content:this.$t("user_messages_page.seo_title")},{property:"og:description",content:this.$t("user_messages_page.seo_description")}],bodyAttrs:{class:"".concat(this.locale," user-messages-page")}}},computed:{locale:function(){return this.$i18n.locale}},watchQuery:!0}),c=n(22),component=Object(c.a)(o,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("messages-page",{attrs:{"total-quantity":e.count,"additional-user":""}})}),[],!1,null,null,null);t.default=component.exports}}]);