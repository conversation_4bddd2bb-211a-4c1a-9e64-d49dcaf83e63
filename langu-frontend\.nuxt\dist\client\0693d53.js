(window.webpackJsonp=window.webpackJsonp||[]).push([[152],{1326:function(e,t,n){"use strict";n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return h}));var o=n(1329),r=n(1),c=Object(r.g)("v-card__actions"),l=Object(r.g)("v-card__subtitle"),d=Object(r.g)("v-card__text"),h=Object(r.g)("v-card__title");o.a},1486:function(e,t,n){var content=n(1487);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("197fcea4",content,!0,{sourceMap:!1})},1487:function(e,t,n){var o=n(18)(!1);o.push([e.i,'.v-chip:not(.v-chip--outlined).accent,.v-chip:not(.v-chip--outlined).error,.v-chip:not(.v-chip--outlined).info,.v-chip:not(.v-chip--outlined).primary,.v-chip:not(.v-chip--outlined).secondary,.v-chip:not(.v-chip--outlined).success,.v-chip:not(.v-chip--outlined).warning{color:#fff}.theme--light.v-chip{border-color:rgba(0,0,0,.12);color:rgba(0,0,0,.87)}.theme--light.v-chip:not(.v-chip--active){background:#e0e0e0}.theme--light.v-chip:hover:before{opacity:.04}.theme--light.v-chip--active:before,.theme--light.v-chip--active:hover:before,.theme--light.v-chip:focus:before{opacity:.12}.theme--light.v-chip--active:focus:before{opacity:.16}.theme--dark.v-chip{border-color:hsla(0,0%,100%,.12);color:#fff}.theme--dark.v-chip:not(.v-chip--active){background:#555}.theme--dark.v-chip:hover:before{opacity:.08}.theme--dark.v-chip--active:before,.theme--dark.v-chip--active:hover:before,.theme--dark.v-chip:focus:before{opacity:.24}.theme--dark.v-chip--active:focus:before{opacity:.32}.v-chip{align-items:center;cursor:default;display:inline-flex;line-height:20px;max-width:100%;outline:none;overflow:hidden;padding:0 12px;position:relative;text-decoration:none;transition-duration:.28s;transition-property:box-shadow,opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);vertical-align:middle;white-space:nowrap}.v-chip:before{background-color:currentColor;bottom:0;border-radius:inherit;content:"";left:0;opacity:0;position:absolute;pointer-events:none;right:0;top:0}.v-chip .v-avatar{height:24px!important;min-width:24px!important;width:24px!important}.v-chip .v-icon{font-size:24px}.v-application--is-ltr .v-chip .v-avatar--left,.v-application--is-ltr .v-chip .v-icon--left{margin-left:-6px;margin-right:6px}.v-application--is-ltr .v-chip .v-avatar--right,.v-application--is-ltr .v-chip .v-icon--right,.v-application--is-rtl .v-chip .v-avatar--left,.v-application--is-rtl .v-chip .v-icon--left{margin-left:6px;margin-right:-6px}.v-application--is-rtl .v-chip .v-avatar--right,.v-application--is-rtl .v-chip .v-icon--right{margin-left:-6px;margin-right:6px}.v-chip:not(.v-chip--no-color) .v-icon{color:inherit}.v-chip .v-chip__close.v-icon{font-size:18px;max-height:18px;max-width:18px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right{margin-right:-4px}.v-application--is-rtl .v-chip .v-chip__close.v-icon.v-icon--right{margin-left:-4px}.v-chip .v-chip__close.v-icon:active,.v-chip .v-chip__close.v-icon:focus,.v-chip .v-chip__close.v-icon:hover{opacity:.72}.v-chip .v-chip__content{align-items:center;display:inline-flex;height:100%;max-width:100%}.v-chip--active .v-icon{color:inherit}.v-chip--link:before{transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-chip--link:focus:before{opacity:.32}.v-chip--clickable{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip--clickable:active{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-chip--disabled{opacity:.4;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip__filter{max-width:24px}.v-chip__filter.v-icon{color:inherit}.v-chip__filter.expand-x-transition-enter,.v-chip__filter.expand-x-transition-leave-active{margin:0}.v-chip--pill .v-chip__filter{margin-right:0 16px 0 0}.v-chip--pill .v-avatar{height:32px!important;width:32px!important}.v-application--is-ltr .v-chip--pill .v-avatar--left{margin-left:-12px}.v-application--is-ltr .v-chip--pill .v-avatar--right,.v-application--is-rtl .v-chip--pill .v-avatar--left{margin-right:-12px}.v-application--is-rtl .v-chip--pill .v-avatar--right{margin-left:-12px}.v-chip--label{border-radius:4px!important}.v-chip.v-chip--outlined{border-width:thin;border-style:solid}.v-chip.v-chip--outlined.v-chip--active:before{opacity:.08}.v-chip.v-chip--outlined .v-icon{color:inherit}.v-chip.v-chip--outlined.v-chip.v-chip{background-color:transparent!important}.v-chip.v-chip--selected{background:transparent}.v-chip.v-chip--selected:after{opacity:.28}.v-chip.v-size--x-small{border-radius:8px;font-size:10px;height:16px}.v-chip.v-size--small{border-radius:12px;font-size:12px;height:24px}.v-chip.v-size--default{border-radius:16px;font-size:14px;height:32px}.v-chip.v-size--large{border-radius:27px;font-size:16px;height:54px}.v-chip.v-size--x-large{border-radius:33px;font-size:18px;height:66px}',""]),e.exports=o},1563:function(e,t,n){"use strict";n(7),n(8),n(14),n(6),n(15);var o=n(13),r=n(2),c=(n(9),n(1486),n(12)),l=n(267),d=n(263),h=n(51),v=n(210),m=n(36),f=n(72),x=n(108),y=n(213),_=n(16);function C(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,n)}return t}function w(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?C(Object(source),!0).forEach((function(t){Object(r.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):C(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}t.a=Object(c.a)(h.a,y.a,x.a,m.a,Object(v.a)("chipGroup"),Object(f.b)("inputValue")).extend({name:"v-chip",props:{active:{type:Boolean,default:!0},activeClass:{type:String,default:function(){return this.chipGroup?this.chipGroup.activeClass:""}},close:Boolean,closeIcon:{type:String,default:"$delete"},closeLabel:{type:String,default:"$vuetify.close"},disabled:Boolean,draggable:Boolean,filter:Boolean,filterIcon:{type:String,default:"$complete"},label:Boolean,link:Boolean,outlined:Boolean,pill:Boolean,tag:{type:String,default:"span"},textColor:String,value:null},data:function(){return{proxyClass:"v-chip--active"}},computed:{classes:function(){return w(w(w(w({"v-chip":!0},x.a.options.computed.classes.call(this)),{},{"v-chip--clickable":this.isClickable,"v-chip--disabled":this.disabled,"v-chip--draggable":this.draggable,"v-chip--label":this.label,"v-chip--link":this.isLink,"v-chip--no-color":!this.color,"v-chip--outlined":this.outlined,"v-chip--pill":this.pill,"v-chip--removable":this.hasClose},this.themeClasses),this.sizeableClasses),this.groupClasses)},hasClose:function(){return Boolean(this.close)},isClickable:function(){return Boolean(x.a.options.computed.isClickable.call(this)||this.chipGroup)}},created:function(){var e=this;[["outline","outlined"],["selected","input-value"],["value","active"],["@input","@active.sync"]].forEach((function(t){var n=Object(o.a)(t,2),r=n[0],c=n[1];e.$attrs.hasOwnProperty(r)&&Object(_.a)(r,c,e)}))},methods:{click:function(e){this.$emit("click",e),this.chipGroup&&this.toggle()},genFilter:function(){var e=[];return this.isActive&&e.push(this.$createElement(d.a,{staticClass:"v-chip__filter",props:{left:!0}},this.filterIcon)),this.$createElement(l.b,e)},genClose:function(){var e=this;return this.$createElement(d.a,{staticClass:"v-chip__close",props:{right:!0,size:18},attrs:{"aria-label":this.$vuetify.lang.t(this.closeLabel)},on:{click:function(t){t.stopPropagation(),t.preventDefault(),e.$emit("click:close"),e.$emit("update:active",!1)}}},this.closeIcon)},genContent:function(){return this.$createElement("span",{staticClass:"v-chip__content"},[this.filter&&this.genFilter(),this.$slots.default,this.hasClose&&this.genClose()])}},render:function(e){var t=[this.genContent()],n=this.generateRouteLink(),o=n.tag,data=n.data;data.attrs=w(w({},data.attrs),{},{draggable:this.draggable?"true":void 0,tabindex:this.chipGroup&&!this.disabled?0:data.attrs.tabindex}),data.directives.push({name:"show",value:this.active}),data=this.setBackgroundColor(this.color,data);var r=this.textColor||this.outlined&&this.color;return e(o,this.setTextColor(r,data),t)}})},1625:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));n(2);var o=n(10);n(62),n(20),n(37),n(44),n(151),n(24),n(38),n(23),n(80),n(7),n(8),n(9),n(14),n(6),n(15);function r(e){return c.apply(this,arguments)}function c(){return(c=Object(o.a)(regeneratorRuntime.mark((function e(t){var n,o,r,c,l,d,h,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.lessonId,o=t.teacherId,r=t.studentId,c=t.isRecurring,l=void 0!==c&&c,e.prev=1,e.next=4,fetch("/_nuxt/api/whereby/create-room",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({lessonId:n,teacherId:o,studentId:r,isRecurring:l})});case 4:if((d=e.sent).ok){e.next=10;break}return e.next=8,d.json();case 8:throw h=e.sent,new Error(h.error||"Failed to create room");case 10:return e.next=12,d.json();case 12:return data=e.sent,e.abrupt("return",data.room);case 16:throw e.prev=16,e.t0=e.catch(1),console.error("Error creating Whereby room:",e.t0),e.t0;case 20:case"end":return e.stop()}}),e,null,[[1,16]])})))).apply(this,arguments)}},1834:function(e,t,n){var content=n(1985);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("cdff56e4",content,!0,{sourceMap:!1})},1984:function(e,t,n){"use strict";n(1834)},1985:function(e,t,n){var o=n(18)(!1);o.push([e.i,".meet-page[data-v-510802b6]{min-height:100vh;background:#f5f5f5}.meet-header[data-v-510802b6]{text-align:center}.meet-video-card .video-container[data-v-510802b6]{background:#000;border-radius:8px;overflow:hidden}.meet-video-card .whereby-video-container[data-v-510802b6]{background:#000}.video-provider-buttons .v-btn[data-v-510802b6]{min-width:auto}.video-controls .v-btn[data-v-510802b6]{color:#fff!important}",""]),e.exports=o},2201:function(e,t,n){"use strict";n.r(t);var o=n(10),r=(n(62),n(20),n(37),n(44),n(151),n(24),n(38),n(23),n(80),n(63),n(9),n(1625)),c={name:"MeetPage",data:function(){return{currentProvider:"whereby",isConnected:!1,isVideoEnabled:!0,isMuted:!1,roomName:"test-meet-room",wherebyClient:null,localStreamContainer:null,currentRole:"participant",wherebyRoom:null,isCreatingRoom:!1}},head:function(){return{title:"Video Call Test - Langu",meta:[{hid:"description",name:"description",content:"Test video call functionality with Whereby integration"}]}},mounted:function(){var e=this;this.$nextTick((function(){e.localStreamContainer=document.getElementById("whereby-video-container"),"whereby"===e.currentProvider&&e.initializeWhereby()})),window.addEventListener("beforeunload",this.cleanup)},beforeDestroy:function(){this.cleanup()},methods:{switchProvider:function(e){var t=this;this.cleanup(),this.currentProvider=e,this.isConnected=!1,"whereby"===e&&this.$nextTick((function(){t.localStreamContainer=document.getElementById("whereby-video-container"),t.initializeWhereby()}))},initializeWhereby:function(){var e=this;try{var t=this.localStreamContainer;if(t){t.innerHTML='\n            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center;">\n              <div style="margin-bottom: 20px;">\n                <h3 style="color: #333; margin-bottom: 10px;">Whereby Video Call</h3>\n                <p style="color: #666; margin-bottom: 20px;">Choose your role to join the embedded video room</p>\n              </div>\n\n              <div style="display: flex; gap: 10px; margin-bottom: 15px;">\n                <button\n                  id="whereby-host-btn"\n                  style="\n                    background: #28a745;\n                    color: white;\n                    border: none;\n                    padding: 12px 20px;\n                    border-radius: 6px;\n                    font-size: 14px;\n                    cursor: pointer;\n                    font-weight: 600;\n                  "\n                  onmouseover="this.style.background=\'#218838\'"\n                  onmouseout="this.style.background=\'#28a745\'"\n                >\n                  Join as Host\n                </button>\n\n                <button\n                  id="whereby-participant-btn"\n                  style="\n                    background: #5E72E4;\n                    color: white;\n                    border: none;\n                    padding: 12px 20px;\n                    border-radius: 6px;\n                    font-size: 14px;\n                    cursor: pointer;\n                    font-weight: 600;\n                  "\n                  onmouseover="this.style.background=\'#4C63D2\'"\n                  onmouseout="this.style.background=\'#5E72E4\'"\n                >\n                  Join as Participant\n                </button>\n              </div>\n\n              <p style="color: #888; font-size: 14px;">\n                '.concat("Dynamic room will be created",'\n              </p>\n              <p style="color: #999; font-size: 12px;">Embedded interface with all paid features</p>\n            </div>\n          ');var n=t.querySelector("#whereby-host-btn"),o=t.querySelector("#whereby-participant-btn");n&&o&&(n.addEventListener("click",(function(){e.currentRole="host",e.createEmbeddedWhereby("host")})),o.addEventListener("click",(function(){e.currentRole="participant",e.createEmbeddedWhereby("participant")})))}}catch(e){console.error("Failed to initialize Whereby:",e),this.isConnected=!1}},createEmbeddedWhereby:function(e){var t=this;return Object(o.a)(regeneratorRuntime.mark((function n(){var o,c,l,iframe,d;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(n.prev=0,o=t.localStreamContainer){n.next=4;break}return n.abrupt("return");case 4:if(t.isCreatingRoom=!0,o.innerHTML='\n          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">\n            <div style="margin-bottom: 20px;">\n              <h3 style="color: #333; margin-bottom: 10px;">Creating Whereby Room...</h3>\n              <p style="color: #666; margin-bottom: 20px;">Please wait while we set up your video call</p>\n            </div>\n            <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #5E72E4; border-radius: 50%; animation: spin 1s linear infinite;"></div>\n            <style>\n              @keyframes spin {\n                0% { transform: rotate(0deg); }\n                100% { transform: rotate(360deg); }\n              }\n            </style>\n          </div>\n        ',t.wherebyRoom){n.next=15;break}n.next=12;break;case 12:return n.next=14,Object(r.a)({lessonId:"meet-test-".concat(Date.now()),teacherId:"meet-teacher",studentId:"meet-student",isRecurring:!1});case 14:t.wherebyRoom=n.sent;case 15:t.isCreatingRoom=!1,c="host"===e?t.wherebyRoom.hostRoomUrl:t.wherebyRoom.roomUrl,l=new URLSearchParams({embed:"",displayName:"host"===e?"Test Host":"Test Participant",audio:t.isMuted?"off":"on",video:t.isVideoEnabled?"on":"off",chat:"on",people:"on",screenshare:"on",reactions:"on",handRaise:"on",leaveButton:"on",background:"on",recording:"on",breakoutRooms:"on",whiteboard:"on",minimal:"false",skipMediaPermissionPrompt:"true",autoJoin:"true"}),iframe=document.createElement("iframe"),d=c.includes("?")?"&":"?",iframe.src="".concat(c).concat(d).concat(l.toString()),iframe.style.width="100%",iframe.style.height="100%",iframe.style.border="none",iframe.style.borderRadius="8px",iframe.allow="camera; microphone; fullscreen; display-capture; autoplay",iframe.allowFullscreen=!0,o.innerHTML="",o.appendChild(iframe),t.isConnected=!0,console.log("Whereby room created for meet page:",t.wherebyRoom),n.next=39;break;case 33:n.prev=33,n.t0=n.catch(0),console.error("Failed to create embedded Whereby:",n.t0),t.isCreatingRoom=!1,t.isConnected=!1,t.localStreamContainer&&(t.localStreamContainer.innerHTML='\n            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">\n              <div style="margin-bottom: 20px;">\n                <h3 style="color: #d32f2f; margin-bottom: 10px;">Failed to Create Room</h3>\n                <p style="color: #666; margin-bottom: 20px;">Unable to create Whereby room. Please try again.</p>\n              </div>\n              <button\n                onclick="location.reload()"\n                style="\n                  background: #5E72E4;\n                  color: white;\n                  border: none;\n                  padding: 12px 20px;\n                  border-radius: 6px;\n                  font-size: 14px;\n                  cursor: pointer;\n                  font-weight: 600;\n                "\n              >\n                Retry\n              </button>\n            </div>\n          ');case 39:case"end":return n.stop()}}),n,null,[[0,33]])})))()},toggleScreenShare:function(){this.isScreenShareEnabled=!this.isScreenShareEnabled,console.log("Screen share toggled:",this.isScreenShareEnabled)},toggleHandRaise:function(){this.isHandRaised=!this.isHandRaised,console.log("Hand raise toggled:",this.isHandRaised)},toggleChat:function(){this.isChatEnabled=!this.isChatEnabled,console.log("Chat toggled:",this.isChatEnabled)},toggleParticipants:function(){this.isParticipantsEnabled=!this.isParticipantsEnabled,console.log("Participants panel toggled:",this.isParticipantsEnabled)},sendReaction:function(e){var t=this;this.reactions.push({emoji:e,timestamp:Date.now()}),console.log("Reaction sent:",e),setTimeout((function(){t.reactions=t.reactions.filter((function(e){return Date.now()-e.timestamp<3e3}))}),3e3)},toggleVideo:function(){this.isVideoEnabled=!this.isVideoEnabled,console.log("Video toggled:",this.isVideoEnabled)},toggleAudio:function(){this.isMuted=!this.isMuted,console.log("Audio toggled:",!this.isMuted)},toggleFullScreen:function(){this.localStreamContainer&&(document.fullscreenElement?document.exitFullscreen():this.localStreamContainer.requestFullscreen())},cleanup:function(){this.localStreamContainer&&(this.localStreamContainer.innerHTML=""),this.isConnected=!1}}},l=(n(1984),n(22)),d=n(42),h=n.n(d),v=n(1327),m=n(1329),f=n(1326),x=n(1563),y=n(1360),_=n(1370),C=n(339),w=n(1361),component=Object(l.a)(c,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"meet-page"},[n("v-container",{staticClass:"pa-4",attrs:{fluid:""}},[n("v-row",[n("v-col",{attrs:{cols:"12"}},[n("div",{staticClass:"meet-header mb-4"},[n("h1",{staticClass:"text-h4 font-weight-bold mb-2"},[e._v("Video Call Test")]),e._v(" "),n("p",{staticClass:"text-subtitle-1 grey--text"},[e._v("\n            Test the Whereby video call integration\n          ")])])])],1),e._v(" "),n("v-row",[n("v-col",{attrs:{cols:"12",md:"8"}},[n("v-card",{staticClass:"meet-video-card",attrs:{elevation:"2"}},[n("v-card-title",{staticClass:"d-flex align-center justify-space-between"},[n("span",[e._v("Video Call")]),e._v(" "),n("div",{staticClass:"video-provider-buttons"},[n("v-btn",{class:[{primary:"whereby"===e.currentProvider}],attrs:{small:""},on:{click:function(t){return e.switchProvider("whereby")}}},[e._v("\n                C - Whereby\n              ")])],1)]),e._v(" "),n("v-card-text",{staticClass:"pa-0"},[n("div",{staticClass:"video-container",staticStyle:{height:"500px",position:"relative"}},["whereby"===e.currentProvider?n("div",{staticClass:"whereby-video-container",staticStyle:{width:"100%",height:"100%"},attrs:{id:"whereby-video-container"}}):e._e(),e._v(" "),"whereby"===e.currentProvider?n("div",{staticClass:"video-controls",staticStyle:{position:"absolute",bottom:"16px",left:"16px",right:"16px"}},[n("v-card",{staticClass:"pa-2",staticStyle:{background:"rgba(0, 0, 0, 0.8)"}},[n("div",{staticClass:"d-flex align-center justify-space-between"},[n("div",{staticClass:"d-flex align-center"},[n("v-btn",{attrs:{color:e.isVideoEnabled?"success":"error",icon:"",small:""},on:{click:e.toggleVideo}},[n("v-icon",[e._v(e._s(e.isVideoEnabled?"mdi-video":"mdi-video-off"))])],1),e._v(" "),n("v-btn",{staticClass:"mx-1",attrs:{color:e.isMuted?"error":"success",icon:"",small:""},on:{click:e.toggleAudio}},[n("v-icon",[e._v(e._s(e.isMuted?"mdi-microphone-off":"mdi-microphone"))])],1),e._v(" "),n("v-btn",{staticClass:"mx-1",attrs:{color:e.isScreenShareEnabled?"primary":"default",icon:"",small:""},on:{click:e.toggleScreenShare}},[n("v-icon",[e._v("mdi-monitor-share")])],1),e._v(" "),n("v-btn",{staticClass:"mx-1",attrs:{color:e.isHandRaised?"warning":"default",icon:"",small:""},on:{click:e.toggleHandRaise}},[n("v-icon",[e._v("mdi-hand-back-right")])],1)],1),e._v(" "),n("div",{staticClass:"d-flex align-center"},[n("v-btn",{staticClass:"mx-1",attrs:{icon:"",small:""},on:{click:function(t){return e.sendReaction("👍")}}},[n("span",{staticStyle:{"font-size":"16px"}},[e._v("👍")])]),e._v(" "),n("v-btn",{staticClass:"mx-1",attrs:{icon:"",small:""},on:{click:function(t){return e.sendReaction("👏")}}},[n("span",{staticStyle:{"font-size":"16px"}},[e._v("👏")])]),e._v(" "),n("v-btn",{staticClass:"mx-1",attrs:{icon:"",small:""},on:{click:function(t){return e.sendReaction("❤️")}}},[n("span",{staticStyle:{"font-size":"16px"}},[e._v("❤️")])]),e._v(" "),n("v-btn",{staticClass:"mx-1",attrs:{icon:"",small:""},on:{click:function(t){return e.sendReaction("😂")}}},[n("span",{staticStyle:{"font-size":"16px"}},[e._v("😂")])])],1),e._v(" "),n("div",{staticClass:"d-flex align-center"},[n("v-btn",{staticClass:"mx-1",attrs:{color:e.isChatEnabled?"primary":"default",icon:"",small:""},on:{click:e.toggleChat}},[n("v-icon",[e._v("mdi-chat")])],1),e._v(" "),n("v-btn",{staticClass:"mx-1",attrs:{color:e.isParticipantsEnabled?"primary":"default",icon:"",small:""},on:{click:e.toggleParticipants}},[n("v-icon",[e._v("mdi-account-group")])],1),e._v(" "),n("v-btn",{staticClass:"mx-1",attrs:{color:"primary",icon:"",small:""},on:{click:e.toggleFullScreen}},[n("v-icon",[e._v("mdi-fullscreen")])],1),e._v(" "),n("v-chip",{staticClass:"ml-2",attrs:{color:"host"===e.currentRole?"success":"primary",small:"","text-color":"white"}},[e._v("\n                        "+e._s("host"===e.currentRole?"Host":"Participant")+"\n                      ")])],1)])])],1):e._e()])])],1)],1),e._v(" "),n("v-col",{attrs:{cols:"12",md:"4"}},[n("v-card",{attrs:{elevation:"2"}},[n("v-card-title",[e._v("Connection Info")]),e._v(" "),n("v-card-text",[n("div",{staticClass:"mb-3"},[n("strong",[e._v("Provider:")]),e._v(" "+e._s(e.currentProvider.toUpperCase())+"\n            ")]),e._v(" "),n("div",{staticClass:"mb-3"},[n("strong",[e._v("Status:")]),e._v(" "),n("v-chip",{attrs:{color:e.isConnected?"success":"warning",small:"","text-color":"white"}},[e._v("\n                "+e._s(e.isConnected?"Connected":"Connecting...")+"\n              ")])],1),e._v(" "),n("div",{staticClass:"mb-3"},[n("strong",[e._v("Role:")]),e._v(" "),n("v-chip",{attrs:{color:"host"===e.currentRole?"success":"primary",small:"","text-color":"white"}},[e._v("\n                "+e._s("host"===e.currentRole?"Host":"Participant")+"\n              ")])],1),e._v(" "),n("div",{staticClass:"mb-3"},[n("strong",[e._v("Room:")]),e._v(" "),e.wherebyRoom?n("span",[e._v(e._s(e.wherebyRoom.roomName))]):n("span",[e._v("Creating room...")])]),e._v(" "),e.wherebyRoom?n("div",{staticClass:"mb-3"},[n("strong",[e._v("Meeting ID:")]),e._v(" "+e._s(e.wherebyRoom.meetingId)+"\n            ")]):e._e(),e._v(" "),n("div",{staticClass:"mb-3"},[n("strong",[e._v("Video:")]),e._v(" "+e._s(e.isVideoEnabled?"On":"Off")+"\n            ")]),e._v(" "),n("div",{staticClass:"mb-3"},[n("strong",[e._v("Audio:")]),e._v(" "+e._s(e.isMuted?"Off":"On")+"\n            ")]),e._v(" "),n("div",{staticClass:"mb-3"},[n("strong",[e._v("Features:")]),e._v(" All controls available in Whereby\n              interface\n            ")])])],1),e._v(" "),n("v-card",{staticClass:"mt-4",attrs:{elevation:"2"}},[n("v-card-title",[e._v("Instructions")]),e._v(" "),n("v-card-text",[n("ol",{staticClass:"pl-4"},[n("li",[e._v("\n                Click on provider buttons (A, B, C) to switch between video\n                providers\n              ")]),e._v(" "),n("li",[e._v("\n                Whereby (C) creates dynamic rooms with all paid features:\n                reactions, hand raising, screen sharing, chat, etc.\n              ")]),e._v(" "),n("li",[e._v('\n                Choose "Host" for full control or "Participant" for standard\n                access\n              ')]),e._v(" "),n("li",[e._v("Each session creates a unique room automatically")]),e._v(" "),n("li",[e._v("\n                All controls are built into the Whereby interface - no\n                external controls needed\n              ")]),e._v(" "),n("li",[e._v("Multiple users can join the same room simultaneously")]),e._v(" "),n("li",[e._v("Host has additional controls and moderator features")])])])],1)],1)],1)],1)],1)}),[],!1,null,"510802b6",null);t.default=component.exports;h()(component,{VBtn:v.a,VCard:m.a,VCardText:f.a,VCardTitle:f.b,VChip:x.a,VCol:y.a,VContainer:_.a,VIcon:C.a,VRow:w.a})}}]);