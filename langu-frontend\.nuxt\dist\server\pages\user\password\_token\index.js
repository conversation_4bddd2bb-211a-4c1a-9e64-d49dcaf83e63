exports.ids = [150];
exports.modules = {

/***/ 1493:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/password/_token/index.vue?vue&type=template&id=6ec54c0a&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{"min-height":"calc(100vh - 300px)"}},[])}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/user/password/_token/index.vue?vue&type=template&id=6ec54c0a&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/password/_token/index.vue?vue&type=script&lang=js&
//
//
//
//
/* harmony default export */ var _tokenvue_type_script_lang_js_ = ({
  name: 'UserPasswordToken',

  beforeMount() {
    const token = this.$route.params.token;
    const redirectUrl = this.$route.query.redirectUrl; // If redirectUrl is present in the query parameters, save it to the store

    if (redirectUrl) {
      this.$store.dispatch('user/setRedirectUrl', redirectUrl);
    }

    this.$store.dispatch('auth/checkPasswordToken', token).finally(() => {
      this.$router.push('/teacher-listing');
    });
  }

});
// CONCATENATED MODULE: ./pages/user/password/_token/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var password_tokenvue_type_script_lang_js_ = (_tokenvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./pages/user/password/_token/index.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  password_tokenvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "73690256"
  
)

/* harmony default export */ var _token = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=index.js.map