exports.ids = [136];
exports.modules = {

/***/ 1213:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(URLSearchParams) {/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return createWherebyRoom; });
/* unused harmony export getWherebyRoom */
/* unused harmony export endWherebyRoom */
/* unused harmony export generateWherebyUrls */
/* unused harmony export openWherebyWindow */
/* unused harmony export isRoomActive */
/* unused harmony export getRoomTimeRemaining */
/**
 * Whereby API helper functions for classroom integration
 */

/**
 * Create a new Whereby room for a classroom session
 * @param {Object} params - Room creation parameters
 * @param {string} params.lessonId - The lesson ID
 * @param {string} params.teacherId - The teacher ID
 * @param {string} params.studentId - The student ID
 * @param {boolean} params.isRecurring - Whether this is a recurring lesson
 * @returns {Promise<Object>} Room information
 */
async function createWherebyRoom({
  lessonId,
  teacherId,
  studentId,
  isRecurring = false
}) {
  try {
    const response = await fetch('/_nuxt/api/whereby/create-room', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        lessonId,
        teacherId,
        studentId,
        isRecurring
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create room');
    }

    const data = await response.json();
    return data.room;
  } catch (error) {
    console.error('Error creating Whereby room:', error);
    throw error;
  }
}
/**
 * Get room information for a lesson
 * @param {string} lessonId - The lesson ID
 * @returns {Promise<Object>} Room information
 */

async function getWherebyRoom(lessonId) {
  try {
    const response = await fetch(`/api/whereby/room/${lessonId}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get room info');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching Whereby room:', error);
    throw error;
  }
}
/**
 * End a Whereby room
 * @param {string} meetingId - The Whereby meeting ID
 * @returns {Promise<Object>} Success response
 */

async function endWherebyRoom(meetingId) {
  try {
    const response = await fetch(`/api/whereby/room/${meetingId}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to end room');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error ending Whereby room:', error);
    throw error;
  }
}
/**
 * Generate room URLs with parameters for embedding
 * @param {Object} room - Room information from createWherebyRoom
 * @param {string} role - 'host' or 'participant'
 * @param {Object} options - Additional options
 * @returns {Object} URLs and parameters
 */

function generateWherebyUrls(room, role = 'participant', options = {}) {
  const {
    displayName = 'User',
    audio = 'on',
    video = 'on',
    chat = 'off',
    // Explicitly disabled
    people = 'off',
    // Explicitly disabled
    screenshare = 'on',
    reactions = 'on',
    handRaise = 'on',
    leaveButton = 'off',
    // Explicitly disabled
    background = 'on',
    recording = 'off',
    // Explicitly disabled
    breakoutRooms = 'on',
    whiteboard = 'on',
    minimal = 'false'
  } = options; // Use appropriate URL based on role

  const baseUrl = role === 'host' ? room.hostRoomUrl : room.roomUrl; // Build embed parameters - explicitly disable requested features

  const embedParams = new URLSearchParams({
    displayName,
    audio,
    video,
    chat,
    people,
    screenshare,
    reactions,
    handRaise,
    leaveButton,
    background,
    recording,
    breakoutRooms,
    whiteboard,
    minimal
  }); // Determine if we need to add & or ? for parameters

  const separator = baseUrl.includes('?') ? '&' : '?';
  const fullUrl = `${baseUrl}${separator}${embedParams.toString()}`;
  return {
    baseUrl,
    fullUrl,
    embedParams: embedParams.toString(),
    role,
    meetingId: room.meetingId
  };
}
/**
 * Open Whereby room in a new window
 * @param {Object} room - Room information
 * @param {string} role - 'host' or 'participant'
 * @param {Object} options - Display options
 * @returns {Window|null} Reference to opened window
 */

function openWherebyWindow(room, role = 'participant', options = {}) {
  const {
    displayName = 'User',
    windowFeatures = 'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no'
  } = options;

  try {
    const {
      fullUrl
    } = generateWherebyUrls(room, role, {
      displayName,
      ...options
    });
    const windowName = `whereby-${room.lessonId}-${role}`;
    const newWindow = window.open(fullUrl, windowName, windowFeatures);

    if (newWindow) {
      newWindow.focus();
      return newWindow;
    } else {
      throw new Error('Failed to open window - popup blocked?');
    }
  } catch (error) {
    console.error('Error opening Whereby window:', error);
    throw error;
  }
}
/**
 * Check if a room is still active
 * @param {Object} room - Room information
 * @returns {boolean} Whether the room is still active
 */

function isRoomActive(room) {
  if (!room || !room.endDate) return false;
  const now = new Date();
  const endDate = new Date(room.endDate);
  return now < endDate;
}
/**
 * Get time remaining for a room
 * @param {Object} room - Room information
 * @returns {number} Minutes remaining (0 if expired)
 */

function getRoomTimeRemaining(room) {
  if (!room || !room.endDate) return 0;
  const now = new Date();
  const endDate = new Date(room.endDate);
  if (now >= endDate) return 0;
  const diffMs = endDate - now;
  return Math.floor(diffMs / (1000 * 60)); // Convert to minutes
}
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(427)["URLSearchParams"]))

/***/ }),

/***/ 1283:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(URLSearchParams) {/* harmony import */ var _helpers_whereby_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1213);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ __webpack_exports__["a"] = ({
  name: 'MeetPage',

  data() {
    return {
      currentProvider: 'whereby',
      isConnected: false,
      isVideoEnabled: true,
      isMuted: false,
      roomName: 'test-meet-room',
      wherebyClient: null,
      localStreamContainer: null,
      currentRole: 'participant',
      // 'host' or 'participant'
      wherebyRoom: null,
      // Store the created room information
      isCreatingRoom: false // Loading state for room creation

    };
  },

  head() {
    return {
      title: 'Video Call Test - Langu',
      meta: [{
        hid: 'description',
        name: 'description',
        content: 'Test video call functionality with Whereby integration'
      }]
    };
  },

  mounted() {
    this.$nextTick(() => {
      this.localStreamContainer = document.getElementById('whereby-video-container');

      if (this.currentProvider === 'whereby') {
        this.initializeWhereby();
      }
    });
    window.addEventListener('beforeunload', this.cleanup);
  },

  beforeDestroy() {
    this.cleanup();
  },

  methods: {
    switchProvider(provider) {
      this.cleanup();
      this.currentProvider = provider;
      this.isConnected = false;

      if (provider === 'whereby') {
        this.$nextTick(() => {
          this.localStreamContainer = document.getElementById('whereby-video-container');
          this.initializeWhereby();
        });
      }
    },

    initializeWhereby() {
      try {
        // For CSP compliance, we need to open Whereby in a new window instead of iframe
        // The CSP error indicates that Whereby doesn't allow embedding from localhost
        // Create embedded iframe interface like existing Whereby component
        const container = this.localStreamContainer;

        if (container) {
          // Create role selection interface first
          container.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center;">
              <div style="margin-bottom: 20px;">
                <h3 style="color: #333; margin-bottom: 10px;">Whereby Video Call</h3>
                <p style="color: #666; margin-bottom: 20px;">Choose your role to join the embedded video room</p>
              </div>

              <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                <button
                  id="whereby-host-btn"
                  style="
                    background: #28a745;
                    color: white;
                    border: none;
                    padding: 12px 20px;
                    border-radius: 6px;
                    font-size: 14px;
                    cursor: pointer;
                    font-weight: 600;
                  "
                  onmouseover="this.style.background='#218838'"
                  onmouseout="this.style.background='#28a745'"
                >
                  Join as Host
                </button>

                <button
                  id="whereby-participant-btn"
                  style="
                    background: #5E72E4;
                    color: white;
                    border: none;
                    padding: 12px 20px;
                    border-radius: 6px;
                    font-size: 14px;
                    cursor: pointer;
                    font-weight: 600;
                  "
                  onmouseover="this.style.background='#4C63D2'"
                  onmouseout="this.style.background='#5E72E4'"
                >
                  Join as Participant
                </button>
              </div>

              <p style="color: #888; font-size: 14px;">
                ${ false ? undefined : 'Dynamic room will be created'}
              </p>
              <p style="color: #999; font-size: 12px;">Embedded interface with all paid features</p>
            </div>
          `; // Add click handlers for role selection

          const hostBtn = container.querySelector('#whereby-host-btn');
          const participantBtn = container.querySelector('#whereby-participant-btn');

          if (hostBtn && participantBtn) {
            // Host button click handler
            hostBtn.addEventListener('click', () => {
              this.currentRole = 'host';
              this.createEmbeddedWhereby('host');
            }); // Participant button click handler

            participantBtn.addEventListener('click', () => {
              this.currentRole = 'participant';
              this.createEmbeddedWhereby('participant');
            });
          }
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to initialize Whereby:', error);
        this.isConnected = false;
      }
    },

    async createEmbeddedWhereby(role) {
      try {
        const container = this.localStreamContainer;
        if (!container) return; // Show loading state

        this.isCreatingRoom = true;
        container.innerHTML = `
          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">
            <div style="margin-bottom: 20px;">
              <h3 style="color: #333; margin-bottom: 10px;">Creating Whereby Room...</h3>
              <p style="color: #666; margin-bottom: 20px;">Please wait while we set up your video call</p>
            </div>
            <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #5E72E4; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            <style>
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            </style>
          </div>
        `; // Create or get existing room

        if (!this.wherebyRoom) {
          // Use static room for staging environment
          if (false) {} else {
            // Dynamic room creation for other environments
            this.wherebyRoom = await Object(_helpers_whereby_api__WEBPACK_IMPORTED_MODULE_0__[/* createWherebyRoom */ "a"])({
              lessonId: `meet-test-${Date.now()}`,
              // Generate unique ID for meet page
              teacherId: 'meet-teacher',
              studentId: 'meet-student',
              isRecurring: false
            });
          }
        } // Hide loading state


        this.isCreatingRoom = false; // Determine the URL based on role

        const roomUrl = role === 'host' ? this.wherebyRoom.hostRoomUrl : this.wherebyRoom.roomUrl; // Build embed parameters with all paid features enabled

        const embedParams = new URLSearchParams({
          embed: '',
          displayName: role === 'host' ? 'Test Host' : 'Test Participant',
          audio: !this.isMuted ? 'on' : 'off',
          video: this.isVideoEnabled ? 'on' : 'off',
          // Enable all paid features
          chat: 'on',
          // Enable chat
          people: 'on',
          // Enable participants panel
          screenshare: 'on',
          // Enable screen sharing
          reactions: 'on',
          // Enable reactions
          handRaise: 'on',
          // Enable hand raising
          leaveButton: 'on',
          // Show leave button
          background: 'on',
          // Enable background effects
          recording: 'on',
          // Enable recording (if available)
          breakoutRooms: 'on',
          // Enable breakout rooms (if available)
          whiteboard: 'on',
          // Enable whiteboard (if available)
          minimal: 'false',
          // Full interface
          // Direct entry without knocking
          skipMediaPermissionPrompt: 'true',
          autoJoin: 'true'
        }); // Create iframe with all features

        const iframe = document.createElement('iframe'); // Determine if we need to add & or ? for parameters

        const separator = roomUrl.includes('?') ? '&' : '?';
        iframe.src = `${roomUrl}${separator}${embedParams.toString()}`;
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.border = 'none';
        iframe.style.borderRadius = '8px';
        iframe.allow = 'camera; microphone; fullscreen; display-capture; autoplay';
        iframe.allowFullscreen = true; // Clear container and add iframe

        container.innerHTML = '';
        container.appendChild(iframe); // Mark as connected

        this.isConnected = true; // eslint-disable-next-line no-console

        console.log('Whereby room created for meet page:', this.wherebyRoom);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to create embedded Whereby:', error);
        this.isCreatingRoom = false;
        this.isConnected = false; // Show error message

        if (this.localStreamContainer) {
          this.localStreamContainer.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">
              <div style="margin-bottom: 20px;">
                <h3 style="color: #d32f2f; margin-bottom: 10px;">Failed to Create Room</h3>
                <p style="color: #666; margin-bottom: 20px;">Unable to create Whereby room. Please try again.</p>
              </div>
              <button
                onclick="location.reload()"
                style="
                  background: #5E72E4;
                  color: white;
                  border: none;
                  padding: 12px 20px;
                  border-radius: 6px;
                  font-size: 14px;
                  cursor: pointer;
                  font-weight: 600;
                "
              >
                Retry
              </button>
            </div>
          `;
        }
      }
    },

    toggleScreenShare() {
      this.isScreenShareEnabled = !this.isScreenShareEnabled; // eslint-disable-next-line no-console

      console.log('Screen share toggled:', this.isScreenShareEnabled); // Note: Actual screen sharing is handled by Whereby iframe
    },

    toggleHandRaise() {
      this.isHandRaised = !this.isHandRaised; // eslint-disable-next-line no-console

      console.log('Hand raise toggled:', this.isHandRaised); // Note: Actual hand raising is handled by Whereby iframe
    },

    toggleChat() {
      this.isChatEnabled = !this.isChatEnabled; // eslint-disable-next-line no-console

      console.log('Chat toggled:', this.isChatEnabled); // Note: Actual chat toggle is handled by Whereby iframe
    },

    toggleParticipants() {
      this.isParticipantsEnabled = !this.isParticipantsEnabled; // eslint-disable-next-line no-console

      console.log('Participants panel toggled:', this.isParticipantsEnabled); // Note: Actual participants panel is handled by Whereby iframe
    },

    sendReaction(emoji) {
      this.reactions.push({
        emoji,
        timestamp: Date.now()
      }); // eslint-disable-next-line no-console

      console.log('Reaction sent:', emoji); // Note: Actual reactions are handled by Whereby iframe
      // Remove reaction after 3 seconds

      setTimeout(() => {
        this.reactions = this.reactions.filter(r => Date.now() - r.timestamp < 3000);
      }, 3000);
    },

    toggleVideo() {
      this.isVideoEnabled = !this.isVideoEnabled; // Note: With iframe approach, video controls are handled within Whereby interface
      // eslint-disable-next-line no-console

      console.log('Video toggled:', this.isVideoEnabled);
    },

    toggleAudio() {
      this.isMuted = !this.isMuted; // Note: With iframe approach, audio controls are handled within Whereby interface
      // eslint-disable-next-line no-console

      console.log('Audio toggled:', !this.isMuted);
    },

    toggleFullScreen() {
      if (this.localStreamContainer) {
        if (document.fullscreenElement) {
          document.exitFullscreen();
        } else {
          this.localStreamContainer.requestFullscreen();
        }
      }
    },

    cleanup() {
      if (this.localStreamContainer) {
        this.localStreamContainer.innerHTML = '';
      }

      this.isConnected = false;
    }

  }
});
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(427)["URLSearchParams"]))

/***/ }),

/***/ 1367:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1434);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("cdff56e4", content, true, context)
};

/***/ }),

/***/ 1433:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_510802b6_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1367);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_510802b6_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_510802b6_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_510802b6_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_510802b6_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1434:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".meet-page[data-v-510802b6]{min-height:100vh;background:#f5f5f5}.meet-header[data-v-510802b6]{text-align:center}.meet-video-card .video-container[data-v-510802b6]{background:#000;border-radius:8px;overflow:hidden}.meet-video-card .whereby-video-container[data-v-510802b6]{background:#000}.video-provider-buttons .v-btn[data-v-510802b6]{min-width:auto}.video-controls .v-btn[data-v-510802b6]{color:#fff!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1507:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/meet/index.vue?vue&type=template&id=510802b6&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"meet-page"},[_c('v-container',{staticClass:"pa-4",attrs:{"fluid":""}},[_c('v-row',[_c('v-col',{attrs:{"cols":"12"}},[_c('div',{staticClass:"meet-header mb-4"},[_c('h1',{staticClass:"text-h4 font-weight-bold mb-2"},[_vm._v("Video Call Test")]),_vm._v(" "),_c('p',{staticClass:"text-subtitle-1 grey--text"},[_vm._v("\n            Test the Whereby video call integration\n          ")])])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{attrs:{"cols":"12","md":"8"}},[_c('v-card',{staticClass:"meet-video-card",attrs:{"elevation":"2"}},[_c('v-card-title',{staticClass:"d-flex align-center justify-space-between"},[_c('span',[_vm._v("Video Call")]),_vm._v(" "),_c('div',{staticClass:"video-provider-buttons"},[_c('v-btn',{class:[{ primary: _vm.currentProvider === 'whereby' }],attrs:{"small":""},on:{"click":function($event){return _vm.switchProvider('whereby')}}},[_vm._v("\n                C - Whereby\n              ")])],1)]),_vm._v(" "),_c('v-card-text',{staticClass:"pa-0"},[_c('div',{staticClass:"video-container",staticStyle:{"height":"500px","position":"relative"}},[(_vm.currentProvider === 'whereby')?_c('div',{staticClass:"whereby-video-container",staticStyle:{"width":"100%","height":"100%"},attrs:{"id":"whereby-video-container"}}):_vm._e(),_vm._v(" "),(_vm.currentProvider === 'whereby')?_c('div',{staticClass:"video-controls",staticStyle:{"position":"absolute","bottom":"16px","left":"16px","right":"16px"}},[_c('v-card',{staticClass:"pa-2",staticStyle:{"background":"rgba(0, 0, 0, 0.8)"}},[_c('div',{staticClass:"d-flex align-center justify-space-between"},[_c('div',{staticClass:"d-flex align-center"},[_c('v-btn',{attrs:{"color":_vm.isVideoEnabled ? 'success' : 'error',"icon":"","small":""},on:{"click":_vm.toggleVideo}},[_c('v-icon',[_vm._v(_vm._s(_vm.isVideoEnabled ? 'mdi-video' : 'mdi-video-off'))])],1),_vm._v(" "),_c('v-btn',{staticClass:"mx-1",attrs:{"color":!_vm.isMuted ? 'success' : 'error',"icon":"","small":""},on:{"click":_vm.toggleAudio}},[_c('v-icon',[_vm._v(_vm._s(!_vm.isMuted ? 'mdi-microphone' : 'mdi-microphone-off'))])],1),_vm._v(" "),_c('v-btn',{staticClass:"mx-1",attrs:{"color":_vm.isScreenShareEnabled ? 'primary' : 'default',"icon":"","small":""},on:{"click":_vm.toggleScreenShare}},[_c('v-icon',[_vm._v("mdi-monitor-share")])],1),_vm._v(" "),_c('v-btn',{staticClass:"mx-1",attrs:{"color":_vm.isHandRaised ? 'warning' : 'default',"icon":"","small":""},on:{"click":_vm.toggleHandRaise}},[_c('v-icon',[_vm._v("mdi-hand-back-right")])],1)],1),_vm._v(" "),_c('div',{staticClass:"d-flex align-center"},[_c('v-btn',{staticClass:"mx-1",attrs:{"icon":"","small":""},on:{"click":function($event){return _vm.sendReaction('👍')}}},[_c('span',{staticStyle:{"font-size":"16px"}},[_vm._v("👍")])]),_vm._v(" "),_c('v-btn',{staticClass:"mx-1",attrs:{"icon":"","small":""},on:{"click":function($event){return _vm.sendReaction('👏')}}},[_c('span',{staticStyle:{"font-size":"16px"}},[_vm._v("👏")])]),_vm._v(" "),_c('v-btn',{staticClass:"mx-1",attrs:{"icon":"","small":""},on:{"click":function($event){return _vm.sendReaction('❤️')}}},[_c('span',{staticStyle:{"font-size":"16px"}},[_vm._v("❤️")])]),_vm._v(" "),_c('v-btn',{staticClass:"mx-1",attrs:{"icon":"","small":""},on:{"click":function($event){return _vm.sendReaction('😂')}}},[_c('span',{staticStyle:{"font-size":"16px"}},[_vm._v("😂")])])],1),_vm._v(" "),_c('div',{staticClass:"d-flex align-center"},[_c('v-btn',{staticClass:"mx-1",attrs:{"color":_vm.isChatEnabled ? 'primary' : 'default',"icon":"","small":""},on:{"click":_vm.toggleChat}},[_c('v-icon',[_vm._v("mdi-chat")])],1),_vm._v(" "),_c('v-btn',{staticClass:"mx-1",attrs:{"color":_vm.isParticipantsEnabled ? 'primary' : 'default',"icon":"","small":""},on:{"click":_vm.toggleParticipants}},[_c('v-icon',[_vm._v("mdi-account-group")])],1),_vm._v(" "),_c('v-btn',{staticClass:"mx-1",attrs:{"color":"primary","icon":"","small":""},on:{"click":_vm.toggleFullScreen}},[_c('v-icon',[_vm._v("mdi-fullscreen")])],1),_vm._v(" "),_c('v-chip',{staticClass:"ml-2",attrs:{"color":_vm.currentRole === 'host' ? 'success' : 'primary',"small":"","text-color":"white"}},[_vm._v("\n                        "+_vm._s(_vm.currentRole === 'host' ? 'Host' : 'Participant')+"\n                      ")])],1)])])],1):_vm._e()])])],1)],1),_vm._v(" "),_c('v-col',{attrs:{"cols":"12","md":"4"}},[_c('v-card',{attrs:{"elevation":"2"}},[_c('v-card-title',[_vm._v("Connection Info")]),_vm._v(" "),_c('v-card-text',[_c('div',{staticClass:"mb-3"},[_c('strong',[_vm._v("Provider:")]),_vm._v(" "+_vm._s(_vm.currentProvider.toUpperCase())+"\n            ")]),_vm._v(" "),_c('div',{staticClass:"mb-3"},[_c('strong',[_vm._v("Status:")]),_vm._v(" "),_c('v-chip',{attrs:{"color":_vm.isConnected ? 'success' : 'warning',"small":"","text-color":"white"}},[_vm._v("\n                "+_vm._s(_vm.isConnected ? 'Connected' : 'Connecting...')+"\n              ")])],1),_vm._v(" "),_c('div',{staticClass:"mb-3"},[_c('strong',[_vm._v("Role:")]),_vm._v(" "),_c('v-chip',{attrs:{"color":_vm.currentRole === 'host' ? 'success' : 'primary',"small":"","text-color":"white"}},[_vm._v("\n                "+_vm._s(_vm.currentRole === 'host' ? 'Host' : 'Participant')+"\n              ")])],1),_vm._v(" "),_c('div',{staticClass:"mb-3"},[_c('strong',[_vm._v("Room:")]),_vm._v(" "),(_vm.wherebyRoom)?_c('span',[_vm._v(_vm._s(_vm.wherebyRoom.roomName))]):_c('span',[_vm._v("Creating room...")])]),_vm._v(" "),(_vm.wherebyRoom)?_c('div',{staticClass:"mb-3"},[_c('strong',[_vm._v("Meeting ID:")]),_vm._v(" "+_vm._s(_vm.wherebyRoom.meetingId)+"\n            ")]):_vm._e(),_vm._v(" "),_c('div',{staticClass:"mb-3"},[_c('strong',[_vm._v("Video:")]),_vm._v(" "+_vm._s(_vm.isVideoEnabled ? 'On' : 'Off')+"\n            ")]),_vm._v(" "),_c('div',{staticClass:"mb-3"},[_c('strong',[_vm._v("Audio:")]),_vm._v(" "+_vm._s(!_vm.isMuted ? 'On' : 'Off')+"\n            ")]),_vm._v(" "),_c('div',{staticClass:"mb-3"},[_c('strong',[_vm._v("Features:")]),_vm._v(" All controls available in Whereby\n              interface\n            ")])])],1),_vm._v(" "),_c('v-card',{staticClass:"mt-4",attrs:{"elevation":"2"}},[_c('v-card-title',[_vm._v("Instructions")]),_vm._v(" "),_c('v-card-text',[_c('ol',{staticClass:"pl-4"},[_c('li',[_vm._v("\n                Click on provider buttons (A, B, C) to switch between video\n                providers\n              ")]),_vm._v(" "),_c('li',[_vm._v("\n                Whereby (C) creates dynamic rooms with all paid features:\n                reactions, hand raising, screen sharing, chat, etc.\n              ")]),_vm._v(" "),_c('li',[_vm._v("\n                Choose \"Host\" for full control or \"Participant\" for standard\n                access\n              ")]),_vm._v(" "),_c('li',[_vm._v("Each session creates a unique room automatically")]),_vm._v(" "),_c('li',[_vm._v("\n                All controls are built into the Whereby interface - no\n                external controls needed\n              ")]),_vm._v(" "),_c('li',[_vm._v("Multiple users can join the same room simultaneously")]),_vm._v(" "),_c('li',[_vm._v("Host has additional controls and moderator features")])])])],1)],1)],1)],1)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/meet/index.vue?vue&type=template&id=510802b6&scoped=true&

// EXTERNAL MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/meet/index.vue?vue&type=script&lang=js&
var meetvue_type_script_lang_js_ = __webpack_require__(1283);

// CONCATENATED MODULE: ./pages/meet/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var pages_meetvue_type_script_lang_js_ = (meetvue_type_script_lang_js_["a" /* default */]); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(862);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(834);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VChip/VChip.js
var VChip = __webpack_require__(901);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(87);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./pages/meet/index.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1433)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  pages_meetvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "510802b6",
  "1181a1da"
  
)

/* harmony default export */ var meet = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */










installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCard: VCard["a" /* default */],VCardText: components_VCard["a" /* VCardText */],VCardTitle: components_VCard["b" /* VCardTitle */],VChip: VChip["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VIcon: VIcon["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 834:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* unused harmony export VCardActions */
/* unused harmony export VCardSubtitle */
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return VCardText; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "b", function() { return VCardTitle; });
/* harmony import */ var _VCard__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(862);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(0);


const VCardActions = Object(_util_helpers__WEBPACK_IMPORTED_MODULE_1__[/* createSimpleFunctional */ "g"])('v-card__actions');
const VCardSubtitle = Object(_util_helpers__WEBPACK_IMPORTED_MODULE_1__[/* createSimpleFunctional */ "g"])('v-card__subtitle');
const VCardText = Object(_util_helpers__WEBPACK_IMPORTED_MODULE_1__[/* createSimpleFunctional */ "g"])('v-card__text');
const VCardTitle = Object(_util_helpers__WEBPACK_IMPORTED_MODULE_1__[/* createSimpleFunctional */ "g"])('v-card__title');

/* unused harmony default export */ var _unused_webpack_default_export = ({
  $_vuetify_subcomponents: {
    VCard: _VCard__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"],
    VCardActions,
    VCardSubtitle,
    VCardText,
    VCardTitle
  }
});

/***/ }),

/***/ 901:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(909);
/* harmony import */ var _src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
/* harmony import */ var _transitions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(67);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(66);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9);
/* harmony import */ var _mixins_groupable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(47);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(7);
/* harmony import */ var _mixins_toggleable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(10);
/* harmony import */ var _mixins_routable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(18);
/* harmony import */ var _mixins_sizeable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(49);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(3);
// Styles

 // Components


 // Mixins






 // Utilities


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(_mixins_colorable__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"], _mixins_sizeable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"], _mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], _mixins_themeable__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"], Object(_mixins_groupable__WEBPACK_IMPORTED_MODULE_5__[/* factory */ "a"])('chipGroup'), Object(_mixins_toggleable__WEBPACK_IMPORTED_MODULE_7__[/* factory */ "b"])('inputValue')).extend({
  name: 'v-chip',
  props: {
    active: {
      type: Boolean,
      default: true
    },
    activeClass: {
      type: String,

      default() {
        if (!this.chipGroup) return '';
        return this.chipGroup.activeClass;
      }

    },
    close: Boolean,
    closeIcon: {
      type: String,
      default: '$delete'
    },
    closeLabel: {
      type: String,
      default: '$vuetify.close'
    },
    disabled: Boolean,
    draggable: Boolean,
    filter: Boolean,
    filterIcon: {
      type: String,
      default: '$complete'
    },
    label: Boolean,
    link: Boolean,
    outlined: Boolean,
    pill: Boolean,
    tag: {
      type: String,
      default: 'span'
    },
    textColor: String,
    value: null
  },
  data: () => ({
    proxyClass: 'v-chip--active'
  }),
  computed: {
    classes() {
      return {
        'v-chip': true,
        ..._mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].options.computed.classes.call(this),
        'v-chip--clickable': this.isClickable,
        'v-chip--disabled': this.disabled,
        'v-chip--draggable': this.draggable,
        'v-chip--label': this.label,
        'v-chip--link': this.isLink,
        'v-chip--no-color': !this.color,
        'v-chip--outlined': this.outlined,
        'v-chip--pill': this.pill,
        'v-chip--removable': this.hasClose,
        ...this.themeClasses,
        ...this.sizeableClasses,
        ...this.groupClasses
      };
    },

    hasClose() {
      return Boolean(this.close);
    },

    isClickable() {
      return Boolean(_mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].options.computed.isClickable.call(this) || this.chipGroup);
    }

  },

  created() {
    const breakingProps = [['outline', 'outlined'], ['selected', 'input-value'], ['value', 'active'], ['@input', '@active.sync']];
    /* istanbul ignore next */

    breakingProps.forEach(([original, replacement]) => {
      if (this.$attrs.hasOwnProperty(original)) Object(_util_console__WEBPACK_IMPORTED_MODULE_10__[/* breaking */ "a"])(original, replacement, this);
    });
  },

  methods: {
    click(e) {
      this.$emit('click', e);
      this.chipGroup && this.toggle();
    },

    genFilter() {
      const children = [];

      if (this.isActive) {
        children.push(this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"], {
          staticClass: 'v-chip__filter',
          props: {
            left: true
          }
        }, this.filterIcon));
      }

      return this.$createElement(_transitions__WEBPACK_IMPORTED_MODULE_2__[/* VExpandXTransition */ "b"], children);
    },

    genClose() {
      return this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"], {
        staticClass: 'v-chip__close',
        props: {
          right: true,
          size: 18
        },
        attrs: {
          'aria-label': this.$vuetify.lang.t(this.closeLabel)
        },
        on: {
          click: e => {
            e.stopPropagation();
            e.preventDefault();
            this.$emit('click:close');
            this.$emit('update:active', false);
          }
        }
      }, this.closeIcon);
    },

    genContent() {
      return this.$createElement('span', {
        staticClass: 'v-chip__content'
      }, [this.filter && this.genFilter(), this.$slots.default, this.hasClose && this.genClose()]);
    }

  },

  render(h) {
    const children = [this.genContent()];
    let {
      tag,
      data
    } = this.generateRouteLink();
    data.attrs = { ...data.attrs,
      draggable: this.draggable ? 'true' : undefined,
      tabindex: this.chipGroup && !this.disabled ? 0 : data.attrs.tabindex
    };
    data.directives.push({
      name: 'show',
      value: this.active
    });
    data = this.setBackgroundColor(this.color, data);
    const color = this.textColor || this.outlined && this.color;
    return h(tag, this.setTextColor(color, data), children);
  }

}));

/***/ }),

/***/ 909:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(910);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("197fcea4", content, true)

/***/ }),

/***/ 910:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-chip:not(.v-chip--outlined).accent,.v-chip:not(.v-chip--outlined).error,.v-chip:not(.v-chip--outlined).info,.v-chip:not(.v-chip--outlined).primary,.v-chip:not(.v-chip--outlined).secondary,.v-chip:not(.v-chip--outlined).success,.v-chip:not(.v-chip--outlined).warning{color:#fff}.theme--light.v-chip{border-color:rgba(0,0,0,.12);color:rgba(0,0,0,.87)}.theme--light.v-chip:not(.v-chip--active){background:#e0e0e0}.theme--light.v-chip:hover:before{opacity:.04}.theme--light.v-chip--active:before,.theme--light.v-chip--active:hover:before,.theme--light.v-chip:focus:before{opacity:.12}.theme--light.v-chip--active:focus:before{opacity:.16}.theme--dark.v-chip{border-color:hsla(0,0%,100%,.12);color:#fff}.theme--dark.v-chip:not(.v-chip--active){background:#555}.theme--dark.v-chip:hover:before{opacity:.08}.theme--dark.v-chip--active:before,.theme--dark.v-chip--active:hover:before,.theme--dark.v-chip:focus:before{opacity:.24}.theme--dark.v-chip--active:focus:before{opacity:.32}.v-chip{align-items:center;cursor:default;display:inline-flex;line-height:20px;max-width:100%;outline:none;overflow:hidden;padding:0 12px;position:relative;text-decoration:none;transition-duration:.28s;transition-property:box-shadow,opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);vertical-align:middle;white-space:nowrap}.v-chip:before{background-color:currentColor;bottom:0;border-radius:inherit;content:\"\";left:0;opacity:0;position:absolute;pointer-events:none;right:0;top:0}.v-chip .v-avatar{height:24px!important;min-width:24px!important;width:24px!important}.v-chip .v-icon{font-size:24px}.v-application--is-ltr .v-chip .v-avatar--left,.v-application--is-ltr .v-chip .v-icon--left{margin-left:-6px;margin-right:6px}.v-application--is-ltr .v-chip .v-avatar--right,.v-application--is-ltr .v-chip .v-icon--right,.v-application--is-rtl .v-chip .v-avatar--left,.v-application--is-rtl .v-chip .v-icon--left{margin-left:6px;margin-right:-6px}.v-application--is-rtl .v-chip .v-avatar--right,.v-application--is-rtl .v-chip .v-icon--right{margin-left:-6px;margin-right:6px}.v-chip:not(.v-chip--no-color) .v-icon{color:inherit}.v-chip .v-chip__close.v-icon{font-size:18px;max-height:18px;max-width:18px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right{margin-right:-4px}.v-application--is-rtl .v-chip .v-chip__close.v-icon.v-icon--right{margin-left:-4px}.v-chip .v-chip__close.v-icon:active,.v-chip .v-chip__close.v-icon:focus,.v-chip .v-chip__close.v-icon:hover{opacity:.72}.v-chip .v-chip__content{align-items:center;display:inline-flex;height:100%;max-width:100%}.v-chip--active .v-icon{color:inherit}.v-chip--link:before{transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-chip--link:focus:before{opacity:.32}.v-chip--clickable{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip--clickable:active{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-chip--disabled{opacity:.4;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip__filter{max-width:24px}.v-chip__filter.v-icon{color:inherit}.v-chip__filter.expand-x-transition-enter,.v-chip__filter.expand-x-transition-leave-active{margin:0}.v-chip--pill .v-chip__filter{margin-right:0 16px 0 0}.v-chip--pill .v-avatar{height:32px!important;width:32px!important}.v-application--is-ltr .v-chip--pill .v-avatar--left{margin-left:-12px}.v-application--is-ltr .v-chip--pill .v-avatar--right,.v-application--is-rtl .v-chip--pill .v-avatar--left{margin-right:-12px}.v-application--is-rtl .v-chip--pill .v-avatar--right{margin-left:-12px}.v-chip--label{border-radius:4px!important}.v-chip.v-chip--outlined{border-width:thin;border-style:solid}.v-chip.v-chip--outlined.v-chip--active:before{opacity:.08}.v-chip.v-chip--outlined .v-icon{color:inherit}.v-chip.v-chip--outlined.v-chip.v-chip{background-color:transparent!important}.v-chip.v-chip--selected{background:transparent}.v-chip.v-chip--selected:after{opacity:.28}.v-chip.v-size--x-small{border-radius:8px;font-size:10px;height:16px}.v-chip.v-size--small{border-radius:12px;font-size:12px;height:24px}.v-chip.v-size--default{border-radius:16px;font-size:14px;height:32px}.v-chip.v-size--large{border-radius:27px;font-size:16px;height:54px}.v-chip.v-size--x-large{border-radius:33px;font-size:18px;height:66px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

};;
//# sourceMappingURL=index.js.map