exports.ids = [78];
exports.modules = {

/***/ 1003:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1004);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("12a190a6", content, true)

/***/ }),

/***/ 1004:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-input--checkbox.v-input--indeterminate.v-input--is-disabled{opacity:.6}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1093:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VRadioGroup_VRadio_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(971);
/* harmony import */ var _src_components_VRadioGroup_VRadio_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VRadioGroup_VRadio_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _VLabel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(50);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(66);
/* harmony import */ var _VInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(20);
/* harmony import */ var _mixins_binds_attrs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(23);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(9);
/* harmony import */ var _mixins_groupable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(47);
/* harmony import */ var _mixins_rippleable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(934);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(7);
/* harmony import */ var _mixins_selectable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(936);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(0);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(2);
/* harmony import */ var _util_mergeData__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(15);
// Styles



 // Mixins






 // Utilities




const baseMixins = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"])(_mixins_binds_attrs__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"], _mixins_colorable__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"], _mixins_rippleable__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"], Object(_mixins_groupable__WEBPACK_IMPORTED_MODULE_6__[/* factory */ "a"])('radioGroup'), _mixins_themeable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"]);
/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (baseMixins.extend().extend({
  name: 'v-radio',
  inheritAttrs: false,
  props: {
    disabled: Boolean,
    id: String,
    label: String,
    name: String,
    offIcon: {
      type: String,
      default: '$radioOff'
    },
    onIcon: {
      type: String,
      default: '$radioOn'
    },
    readonly: Boolean,
    value: {
      default: null
    }
  },
  data: () => ({
    isFocused: false
  }),
  computed: {
    classes() {
      return {
        'v-radio--is-disabled': this.isDisabled,
        'v-radio--is-focused': this.isFocused,
        ...this.themeClasses,
        ...this.groupClasses
      };
    },

    computedColor() {
      return _mixins_selectable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].options.computed.computedColor.call(this);
    },

    computedIcon() {
      return this.isActive ? this.onIcon : this.offIcon;
    },

    computedId() {
      return _VInput__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].options.computed.computedId.call(this);
    },

    hasLabel: _VInput__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].options.computed.hasLabel,

    hasState() {
      return (this.radioGroup || {}).hasState;
    },

    isDisabled() {
      return this.disabled || !!this.radioGroup && this.radioGroup.isDisabled;
    },

    isReadonly() {
      return this.readonly || !!this.radioGroup && this.radioGroup.isReadonly;
    },

    computedName() {
      if (this.name || !this.radioGroup) {
        return this.name;
      }

      return this.radioGroup.name || `radio-${this.radioGroup._uid}`;
    },

    rippleState() {
      return _mixins_selectable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].options.computed.rippleState.call(this);
    },

    validationState() {
      return (this.radioGroup || {}).validationState || this.computedColor;
    }

  },
  methods: {
    genInput(args) {
      // We can't actually use the mixin directly because
      // it's made for standalone components, but its
      // genInput method is exactly what we need
      return _mixins_selectable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].options.methods.genInput.call(this, 'radio', args);
    },

    genLabel() {
      if (!this.hasLabel) return null;
      return this.$createElement(_VLabel__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], {
        on: {
          // Label shouldn't cause the input to focus
          click: _mixins_selectable__WEBPACK_IMPORTED_MODULE_9__[/* prevent */ "b"]
        },
        attrs: {
          for: this.computedId
        },
        props: {
          color: this.validationState,
          focused: this.hasState
        }
      }, Object(_util_helpers__WEBPACK_IMPORTED_MODULE_10__[/* getSlot */ "n"])(this, 'label') || this.label);
    },

    genRadio() {
      return this.$createElement('div', {
        staticClass: 'v-input--selection-controls__input'
      }, [this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"], this.setTextColor(this.validationState, {
        props: {
          dense: this.radioGroup && this.radioGroup.dense
        }
      }), this.computedIcon), this.genInput({
        name: this.computedName,
        value: this.value,
        ...this.attrs$
      }), this.genRipple(this.setTextColor(this.rippleState))]);
    },

    onFocus(e) {
      this.isFocused = true;
      this.$emit('focus', e);
    },

    onBlur(e) {
      this.isFocused = false;
      this.$emit('blur', e);
    },

    onChange() {
      if (this.isDisabled || this.isReadonly || this.isActive) return;
      this.toggle();
    },

    onKeydown: () => {}
  },

  render(h) {
    const data = {
      staticClass: 'v-radio',
      class: this.classes,
      on: Object(_util_mergeData__WEBPACK_IMPORTED_MODULE_12__[/* mergeListeners */ "b"])({
        click: this.onChange
      }, this.listeners$)
    };
    return h('div', data, [this.genRadio(), this.genLabel()]);
  }

}));

/***/ }),

/***/ 1094:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(935);
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _src_components_VRadioGroup_VRadioGroup_sass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(973);
/* harmony import */ var _src_components_VRadioGroup_VRadioGroup_sass__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_src_components_VRadioGroup_VRadioGroup_sass__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _VInput__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(20);
/* harmony import */ var _VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(902);
/* harmony import */ var _mixins_comparable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(903);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(2);
// Styles

 // Extensions


 // Mixins

 // Types


const baseMixins = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(_mixins_comparable__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"], _VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_3__[/* BaseItemGroup */ "a"], _VInput__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"]);
/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (baseMixins.extend({
  name: 'v-radio-group',

  provide() {
    return {
      radioGroup: this
    };
  },

  props: {
    column: {
      type: Boolean,
      default: true
    },
    height: {
      type: [Number, String],
      default: 'auto'
    },
    name: String,
    row: Boolean,
    // If no value set on VRadio
    // will match valueComparator
    // force default to null
    value: null
  },
  computed: {
    classes() {
      return { ..._VInput__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"].options.computed.classes.call(this),
        'v-input--selection-controls v-input--radio-group': true,
        'v-input--radio-group--column': this.column && !this.row,
        'v-input--radio-group--row': this.row
      };
    }

  },
  methods: {
    genDefaultSlot() {
      return this.$createElement('div', {
        staticClass: 'v-input--radio-group__input',
        attrs: {
          id: this.id,
          role: 'radiogroup',
          'aria-labelledby': this.computedId
        }
      }, _VInput__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"].options.methods.genDefaultSlot.call(this));
    },

    genInputSlot() {
      const render = _VInput__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"].options.methods.genInputSlot.call(this);
      delete render.data.on.click;
      return render;
    },

    genLabel() {
      const label = _VInput__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"].options.methods.genLabel.call(this);
      if (!label) return null;
      label.data.attrs.id = this.computedId; // WAI considers this an orphaned label

      delete label.data.attrs.for;
      label.tag = 'legend';
      return label;
    },

    onClick: _VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_3__[/* BaseItemGroup */ "a"].options.methods.onClick
  }
}));

/***/ }),

/***/ 1128:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VCheckbox_VCheckbox_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1003);
/* harmony import */ var _src_components_VCheckbox_VCheckbox_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VCheckbox_VCheckbox_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(935);
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(66);
/* harmony import */ var _VInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(20);
/* harmony import */ var _mixins_selectable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(936);
// Styles

 // Components


 // Mixins


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (_mixins_selectable__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"].extend({
  name: 'v-checkbox',
  props: {
    indeterminate: Boolean,
    indeterminateIcon: {
      type: String,
      default: '$checkboxIndeterminate'
    },
    offIcon: {
      type: String,
      default: '$checkboxOff'
    },
    onIcon: {
      type: String,
      default: '$checkboxOn'
    }
  },

  data() {
    return {
      inputIndeterminate: this.indeterminate
    };
  },

  computed: {
    classes() {
      return { ..._VInput__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].options.computed.classes.call(this),
        'v-input--selection-controls': true,
        'v-input--checkbox': true,
        'v-input--indeterminate': this.inputIndeterminate
      };
    },

    computedIcon() {
      if (this.inputIndeterminate) {
        return this.indeterminateIcon;
      } else if (this.isActive) {
        return this.onIcon;
      } else {
        return this.offIcon;
      }
    },

    // Do not return undefined if disabled,
    // according to spec, should still show
    // a color when disabled and active
    validationState() {
      if (this.isDisabled && !this.inputIndeterminate) return undefined;
      if (this.hasError && this.shouldValidate) return 'error';
      if (this.hasSuccess) return 'success';
      if (this.hasColor !== null) return this.computedColor;
      return undefined;
    }

  },
  watch: {
    indeterminate(val) {
      // https://github.com/vuetifyjs/vuetify/issues/8270
      this.$nextTick(() => this.inputIndeterminate = val);
    },

    inputIndeterminate(val) {
      this.$emit('update:indeterminate', val);
    },

    isActive() {
      if (!this.indeterminate) return;
      this.inputIndeterminate = false;
    }

  },
  methods: {
    genCheckbox() {
      return this.$createElement('div', {
        staticClass: 'v-input--selection-controls__input'
      }, [this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"], this.setTextColor(this.validationState, {
        props: {
          dense: this.dense,
          dark: this.dark,
          light: this.light
        }
      }), this.computedIcon), this.genInput('checkbox', { ...this.attrs$,
        'aria-checked': this.inputIndeterminate ? 'mixed' : this.isActive.toString()
      }), this.genRipple(this.setTextColor(this.rippleState))]);
    },

    genDefaultSlot() {
      return [this.genCheckbox(), this.genLabel()];
    }

  }
}));

/***/ }),

/***/ 1238:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1314);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("a55f04de", content, true, context)
};

/***/ }),

/***/ 1313:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SummaryLessonDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1238);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SummaryLessonDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SummaryLessonDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SummaryLessonDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SummaryLessonDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1314:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-application .v-dialog.schedule-lesson-dialog>.v-card{padding:32px 40px!important}@media only screen and (max-width:991px){.v-application .v-dialog.schedule-lesson-dialog>.v-card{padding:50px 18px 74px!important}.v-application .v-dialog.schedule-lesson-dialog>.v-card .dialog-content,.v-application .v-dialog.schedule-lesson-dialog>.v-card .schedule-lesson-dialog-body,.v-application .v-dialog.schedule-lesson-dialog>.v-card .v-form{height:100%}.v-application .v-dialog.schedule-lesson-dialog>.v-card .schedule-lesson-dialog-body{overflow-y:auto}}@media only screen and (min-width:768px){.v-application .v-dialog.schedule-lesson-dialog .details{padding-right:15px}}.v-application .v-dialog.schedule-lesson-dialog .details-row{display:flex;margin-top:16px}@media only screen and (max-width:767px){.v-application .v-dialog.schedule-lesson-dialog .details-row{margin-top:8px}}.v-application .v-dialog.schedule-lesson-dialog .details-row:first-child{margin-top:0}.v-application .v-dialog.schedule-lesson-dialog .details-row .property{width:115px;line-height:1.2!important}.v-application .v-dialog.schedule-lesson-dialog .details-row .value{padding-left:5px}.v-application .v-dialog.schedule-lesson-dialog .details-row .value--icon{position:relative;padding-left:26px}.v-application .v-dialog.schedule-lesson-dialog .details-row .value--icon .v-image{position:absolute;left:0;top:3px;border-radius:50%;overflow:hidden}.v-application .v-dialog.schedule-lesson-dialog .notice{position:relative;margin-top:4px;color:#a4a4a4}.v-application .v-dialog.schedule-lesson-dialog .notice p{margin:10px 0}.v-application .v-dialog.schedule-lesson-dialog .notice a{color:inherit;text-decoration:none}.v-application .v-dialog.schedule-lesson-dialog .notice a:hover{color:var(--v-orange-base)}.v-application .v-dialog.schedule-lesson-dialog .notice .spinner{position:absolute;bottom:-70px;left:50%;transform:translateX(-50%)}.v-application .v-dialog.schedule-lesson-dialog .details-notice{color:#969696}.v-application .v-dialog.schedule-lesson-dialog .l-checkbox .v-label{font-size:12px!important}.v-application .v-dialog.schedule-lesson-dialog .l-checkbox .v-input--selection-controls__input{margin-top:3px}.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-header{display:inline-block;padding-right:60px;font-size:20px;font-weight:700;line-height:1.1}@media only screen and (max-width:991px){.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-header{position:absolute;top:0;left:0;width:100%;height:50px;display:flex;align-items:center;padding-left:18px;font-size:18px}}.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-body .row .col:first-child{padding-right:20px}.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-body .row .col:last-child{padding-left:20px}@media only screen and (min-width:992px){.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-footer{margin-top:28px}}@media only screen and (max-width:991px){.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-footer{position:absolute;bottom:0;left:0;width:100%;height:74px;padding:0 18px}}.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-footer .prev-button{color:var(--v-orange-base);cursor:pointer}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1398:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/SummaryLessonDialog.vue?vue&type=template&id=7ba56d06&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{"dialog":_vm.isShownSummaryLessonDialog,"max-width":"725","custom-class":"schedule-lesson-dialog","persistent":"","fullscreen":_vm.$vuetify.breakpoint.smAndDown}},_vm.$listeners),[_c('v-form',{on:{"submit":function($event){$event.preventDefault();return _vm.scheduleLessons.apply(null, arguments)}},model:{value:(_vm.valid),callback:function ($$v) {_vm.valid=$$v},expression:"valid"}},[_c('div',{staticClass:"schedule-lesson-dialog-header text--gradient"},[_vm._v("\n      "+_vm._s(_vm.$t('lesson_summary'))+":\n    ")]),_vm._v(" "),_c('div',{class:[
        'schedule-lesson-dialog-body pt-2 pt-sm-4',
        {
          'l-scroll l-scroll--grey l-scroll--large':
            _vm.$vuetify.breakpoint.xsOnly,
        } ]},[_c('v-row',{attrs:{"no-gutters":""}},[_c('v-col',{staticClass:"col-12 col-sm-6"},[_c('div',{staticClass:"details"},[_c('div',{staticClass:"details-row"},[_c('div',{staticClass:"subtitle-2 property font-weight-medium"},[_vm._v("\n                "+_vm._s(_vm.$t('language'))+":\n              ")]),_vm._v(" "),_c('div',{staticClass:"body-1 value value--icon"},[(_vm.selectedLanguage.isoCode)?_c('div',{staticClass:"icon mr-1"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (_vm.selectedLanguage.isoCode) + ".svg"),"width":"18","height":"18"}})],1):_vm._e(),_vm._v("\n                "+_vm._s(_vm.selectedLanguage.name)+"\n              ")])]),_vm._v(" "),_c('div',{staticClass:"details-row"},[_c('div',{staticClass:"subtitle-2 property font-weight-medium"},[_vm._v("\n                "+_vm._s(_vm.$t('teacher_capitalize'))+":\n              ")]),_vm._v(" "),_c('div',{staticClass:"body-1 value"},[_vm._v("\n                "+_vm._s(_vm.teacher.firstName)+" "+_vm._s(_vm.teacher.lastName)+"\n              ")])]),_vm._v(" "),(_vm.selectedCourse.isCourse)?_c('div',{staticClass:"details-row"},[_c('div',{staticClass:"subtitle-2 property font-weight-medium"},[_vm._v("\n                "+_vm._s(_vm.$t('course'))+":\n              ")]),_vm._v(" "),_c('div',{staticClass:"body-1 value"},[_vm._v("\n                "+_vm._s(_vm.selectedCourse.name)+"\n                "),_c('span',{staticClass:"body-2 greyDark--text"},[_vm._v("("+_vm._s(_vm.$tc('lessons_count', _vm.selectedCourse.lessons))+")")])])]):_c('div',{staticClass:"details-row"},[_c('div',{staticClass:"subtitle-2 property font-weight-medium"},[_vm._v("\n                "+_vm._s(_vm.$t('package'))+":\n              ")]),_vm._v(" "),_c('div',{staticClass:"body-1 value"},[(_vm.isSelectedTrial)?[_vm._v("\n                  "+_vm._s(_vm.$t('trial'))+"\n                ")]:[_vm._v("\n                  "+_vm._s(_vm.$tc('lessons_count', _vm.selectedCourse.lessons))+"\n                ")]],2)]),_vm._v(" "),_c('div',{staticClass:"details-row"},[_c('div',{staticClass:"subtitle-2 property font-weight-medium"},[_vm._v("\n                "+_vm._s(_vm.$t('length'))+":\n              ")]),_vm._v(" "),_c('div',{staticClass:"body-1 value"},[_vm._v("\n                "+_vm._s(_vm.$tc('minutes_count', _vm.selectedCourse.length))+"\n              ")])]),_vm._v(" "),(_vm.selectedSlots.length)?[_c('div',{staticClass:"details-row"},[_c('div',{staticClass:"subtitle-2 property font-weight-medium"},[_vm._v("\n                  "+_vm._s(_vm.$t('lesson_time'))+":\n                ")]),_vm._v(" "),_c('div',{staticClass:"body-1 value"},_vm._l((_vm.selectedSlots),function(date,idx){return _c('div',{key:idx},[_vm._v("\n                    "+_vm._s(_vm.$dayjs(date)
                        .add(_vm.$dayjs(date).tz(_vm.timezone).utcOffset(), 'minute')
                        .format('ll, LT'))+"\n                  ")])}),0)])]:_vm._e(),_vm._v(" "),_c('div',{staticClass:"details-notice notice caption mt-2"},[_vm._v("\n              "+_vm._s(_vm.$t('time_listed_are_in_timezone', { timezone: _vm.timezone }))+"\n            ")]),_vm._v(" "),(!_vm.isFreeTrialPackage && _vm.lessonsLeft > 0)?_c('div',{staticClass:"details-notice notice caption mt-2"},[_vm._v("\n              "+_vm._s(_vm.$t('you_can_schedule_your_remaining_lessons', {
                  count: _vm.$tc('remaining_lessons_count', _vm.lessonsLeft),
                }))+"\n            ")]):_vm._e()],2)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 mt-3 mt-sm-0"},[(_vm.isSelectedTrial)?_c('div',{staticClass:"message mb-4"},[_c('div',{staticClass:"subtitle-2 font-weight-medium"},[_vm._v("\n              "+_vm._s(_vm.$t('write_message_to_your_teacher'))+":\n            ")]),_vm._v(" "),_c('div',{staticClass:"mt-1"},[_c('v-textarea',{staticClass:"l-textarea",attrs:{"no-resize":"","height":"100","counter":_vm.messageCounter,"solo":"","dense":"","rules":_vm.messageRules,"hint":_vm.messageHint,"persistent-hint":"","placeholder":_vm.$t(
                    'briefly_introduce_yourself_write_your_teacher_few_words'
                  )},scopedSlots:_vm._u([{key:"counter",fn:function(ref){
                  var props = ref.props;
return [_c('div',{directives:[{name:"show",rawName:"v-show",value:(props.value > 0),expression:"props.value > 0"}],staticClass:"v-counter theme--light"},[_vm._v("\n                    "+_vm._s(props.value)+"\n                  ")])]}}],null,false,3865712920),model:{value:(_vm.message),callback:function ($$v) {_vm.message=$$v},expression:"message"}})],1),_vm._v(" "),(_vm.isFreeTrialPackage)?_c('div',{staticClass:"mt-3"},[_c('v-checkbox',{staticClass:"l-checkbox caption",attrs:{"value":_vm.isAgree,"label":_vm.$t(
                    'i_understand_that_my_teacher_is_making_time_for_this_trial'
                  ),"ripple":false,"rules":_vm.agreeRules},on:{"change":function($event){_vm.isAgree = true}}})],1):_vm._e()]):_vm._e(),_vm._v(" "),(!_vm.isFreeTrialPackage && !_vm.isEnoughCredits)?_c('div',{staticClass:"payment"},[_c('div',{staticClass:"subtitle-2 font-weight-medium"},[_vm._v("\n              "+_vm._s(_vm.$t('choose_payment_method'))+":\n            ")]),_vm._v(" "),_c('div',{staticClass:"mt-1 mt-sm-2"},[_c('v-radio-group',{staticClass:"mt-0 pt-0",attrs:{"hide-details":""},model:{value:(_vm.selectedPaymentMethod),callback:function ($$v) {_vm.selectedPaymentMethod=$$v},expression:"selectedPaymentMethod"}},_vm._l((_vm.paymentMethods),function(paymentMethod){return _c('v-radio',{key:paymentMethod.id,staticClass:"l-radio-button",attrs:{"label":_vm.getLabelPayment(paymentMethod),"ripple":false,"value":paymentMethod.id}})}),1)],1)]):_vm._e(),_vm._v(" "),_c('div',{staticClass:"details-row mt-3"},[_c('div',{staticClass:"subtitle-2 property font-weight-medium"},[_vm._v("\n              "+_vm._s(_vm.$t('total_price'))+":\n            ")]),_vm._v(" "),_c('div',{staticClass:"body-1 value"},[(_vm.isFreeTrialPackage)?[_vm._v("\n                "+_vm._s(_vm.$t('free'))+"\n              ")]:[_vm._v("\n                "+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.totalPrice)+"\n              ")]],2)]),_vm._v(" "),(!_vm.isFreeTrialPackage && _vm.additionalCredits)?[_c('div',{staticClass:"details-row mt-1"},[_c('div',{staticClass:"subtitle-2 property font-weight-medium"},[_vm._v("\n                "+_vm._s(_vm.$t('langu_credit'))+":\n              ")]),_vm._v(" "),_c('div',{staticClass:"body-1 value"},[_vm._v("\n                -"+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.isEnoughCredits ? _vm.totalPrice : _vm.additionalCredits)+"\n              ")])]),_vm._v(" "),_c('div',{staticClass:"details-row mt-1"},[_c('div',{staticClass:"subtitle-2 property font-weight-medium"},[_vm._v("\n                "+_vm._s(_vm.$t('total_due'))+":\n              ")]),_vm._v(" "),_c('div',{staticClass:"body-1 value"},[_vm._v("\n                "+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.totalDuePrice)+"\n              ")])])]:_vm._e(),_vm._v(" "),(!_vm.isFreeTrialPackage)?_c('div',{staticClass:"details-notice notice caption mt-2"},[_vm._v("\n            "+_vm._s(_vm.$t(
                'your_teacher_will_receive_your_payment_once_lesson_has_successfully_concluded'
              ))+"\n          ")]):_vm._e(),_vm._v(" "),(!_vm.isFreeTrialPackage && _vm.additionalCredits)?_c('div',{staticClass:"details-notice notice caption mt-1"},[_vm._v("\n            "+_vm._s(_vm.$t('after_this_purchase_you_will_have_credit_remaining', {
                value: _vm.additionalCreditsLeft,
              }))+"\n          ")]):_vm._e()],2)],1)],1),_vm._v(" "),_c('div',{staticClass:"schedule-lesson-dialog-footer d-flex justify-space-between align-center"},[_c('div',{staticClass:"prev-button body-1",on:{"click":_vm.prevStep}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"17","height":"12","viewBox":"0 0 17 12"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#arrow-prev")}})]),_vm._v("\n        "+_vm._s(_vm.$t('go_back'))+"\n      ")]),_vm._v(" "),_c('div',[(_vm.isStudent)?_c('v-btn',{attrs:{"id":"continue_trialOrPurchase","small":"","color":"primary","type":"submit","disabled":!_vm.valid}},[(_vm.isSelectedTrial && _vm.isFreeTrialPackage)?[_vm._v("\n            "+_vm._s(_vm.$t('book_trial'))+"!\n          ")]:(_vm.isEnoughCredits)?[_vm._v("\n            "+_vm._s(_vm.$t('complete_purchase'))+"\n          ")]:[_vm._v("\n            "+_vm._s(_vm.$t('continue_to_purchase'))+"\n          ")]],2):_vm._e()],1)])])],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/SummaryLessonDialog.vue?vue&type=template&id=7ba56d06&

// EXTERNAL MODULE: ./components/LDialog.vue + 5 modules
var LDialog = __webpack_require__(28);

// EXTERNAL MODULE: ./utils/hash.js
var hash = __webpack_require__(655);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/SummaryLessonDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


const MESSAGE_MIN_LENGTH = 100;
/* harmony default export */ var SummaryLessonDialogvue_type_script_lang_js_ = ({
  name: 'ScheduleLessonDialog',
  components: {
    LDialog: LDialog["default"]
  },
  props: {
    isShownSummaryLessonDialog: {
      type: Boolean,
      required: true
    },
    username: {
      type: String,
      required: true
    },
    query: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      valid: true,
      isAgree: false,
      messageCounter: MESSAGE_MIN_LENGTH,
      messageRules: [v => (v === null || v === void 0 ? void 0 : v.length) >= MESSAGE_MIN_LENGTH || this.messageHint],
      agreeRules: [v => !!v || this.$t('field_required')]
    };
  },

  computed: {
    timezone() {
      return this.$store.getters['user/timeZone'];
    },

    isStudent() {
      return this.$store.getters['user/isStudent'];
    },

    teacher() {
      return this.$store.state.teacher_profile.item;
    },

    paymentMethods() {
      return this.$store.state.purchase.paymentMethods;
    },

    trialPackage() {
      return this.$store.getters['teacher_profile/trialPackage'];
    },

    isSelectedTrial() {
      return this.$store.getters['teacher_profile/isSelectedTrial'];
    },

    isFreeTrialPackage() {
      return this.isSelectedTrial && this.trialPackage.isFreeTrialLesson;
    },

    selectedCourse() {
      return this.$store.state.teacher_profile.selectedCourse;
    },

    selectedLanguage() {
      return this.$store.state.teacher_profile.selectedLanguage;
    },

    selectedSlots() {
      return this.$store.state.teacher_profile.selectedSlots.map(item => item[0].date).sort((a, b) => new Date(a) - new Date(b));
    },

    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    },

    totalPrice() {
      return this.$store.getters['teacher_profile/totalPrice'];
    },

    lessonsLeft() {
      return this.selectedCourse.lessons - this.selectedSlots.length;
    },

    message: {
      get() {
        return this.$store.state.purchase.message;
      },

      set(value) {
        this.$store.commit('purchase/SET_MESSAGE', value);
      }

    },
    selectedPaymentMethod: {
      get() {
        return this.$store.state.purchase.selectedPaymentMethod;
      },

      set(value) {
        this.$store.commit('purchase/SET_SELECTED_PAYMENT_METHOD', value);
      }

    },

    additionalCredits() {
      return this.$store.getters['purchase/additionalCredits'];
    },

    isEnoughCredits() {
      return this.additionalCredits >= this.totalPrice;
    },

    totalDuePrice() {
      return (this.isEnoughCredits ? 0 : this.totalPrice - this.additionalCredits).toFixed(2);
    },

    additionalCreditsLeft() {
      return this.currentCurrencySymbol + (this.isEnoughCredits ? this.additionalCredits - this.totalPrice : '0.00');
    },

    messageHint() {
      return this.$t('please_write_at_least_characters', {
        value: MESSAGE_MIN_LENGTH
      });
    },

    userCurrency() {
      return this.$store.getters['user/currency'];
    },

    getFormattedDate() {
      const date = new Date();
      const options = {
        year: 'numeric',
        month: 'short',
        day: '2-digit',
        hour: 'numeric',
        minute: 'numeric',
        hour12: true
      };
      const formattedDate = date.toLocaleString('en-US', options);
      return formattedDate || 'Jan 01, 2000, 12:00 AM';
    }

  },
  methods: {
    prevStep() {
      this.$router.push({
        path: this.$route.path,
        query: { ...this.query,
          step: 'schedule-lessons'
        }
      });
      this.$emit('prev-step');
    },

    getLabelPayment(payment) {
      var _this$userCurrency;

      let label;

      switch (payment.id) {
        case 1:
          label = ((_this$userCurrency = this.userCurrency) === null || _this$userCurrency === void 0 ? void 0 : _this$userCurrency.id) !== 4 ? this.$t('debit_or_credit_card') : this.$t('debit_or_credit_card_pl_version');
          break;

        case 2:
          label = 'Przelewy24/BLIK';
          break;

        default:
          label = payment.name;
      }

      return label;
    },

    async scheduleLessons() {
      const tidioData = this.$store.state.user.tidioData || {}; // Try to get user data from the API for the most up-to-date information

      let userData = null;

      try {
        userData = await this.$store.dispatch('payments/fetchUserData');
      } catch (error) {
        console.error('Error fetching user data from API:', error);
      } // If API call fails, fall back to store state


      if (!userData || !userData.email) {
        userData = this.$store.state.user.item || {};
      }

      const userEmail = tidioData.email || '';
      const userName = `${userData.firstName || ''} ${userData.lastName || ''}`.trim(); // Hash the user data

      const hashedEmail = Object(hash["hashUserData"])(userEmail);
      const hashedName = Object(hash["hashUserData"])(userName); // Create or update event data with hashed values

      let eventData = null; // Create free trial event if applicable

      if (this.isSelectedTrial && this.trialPackage.isFreeTrialLesson) {
        eventData = {
          event: 'purchase_free_trial',
          ecommerce: {
            transaction_id_free_trial: 'T_12345',
            items: [{
              item_id_free_trial: this.$store.state.teacher_profile.item.id || '1234',
              teacher_name_free_trial: `${this.$store.state.teacher_profile.item.firstName.trim()} ${this.$store.state.teacher_profile.item.lastName.trim()}`,
              language_free_trial: this.$store.state.teacher_profile.selectedLanguage.name || 'English',
              lesson_length_free_trial: `${this.selectedCourse.length} minutes` || '30 minutes',
              lesson_time_free_trial: this.getFormattedDate,
              package_type_free_trial: 'free_trial',
              package_free_trial: `${this.selectedCourse.lessons} Lesson` || '1 Lesson',
              user_name: hashedName,
              email_id: hashedEmail
            }]
          }
        };
      } // Create paid trial event if applicable
      else if (this.isSelectedTrial && !this.trialPackage.isFreeTrialLesson) {
        eventData = {
          event: 'purchase_paid_trial',
          ecommerce: {
            transaction_id_paid_trial: 'T_12345',
            value_paid_trial: this.$store.getters['teacher_profile/totalPrice'] || 0,
            tax_paid_trial: null,
            currency_paid_trial: this.$store.getters['user/currency'].isoCode || 'USD',
            items: [{
              item_id_paid_trial: this.$store.state.teacher_profile.item.id || '1234',
              teacher_name_paid_trial: `${this.$store.state.teacher_profile.item.firstName.trim()} ${this.$store.state.teacher_profile.item.lastName.trim()}`,
              total_price_paid_trial: this.$store.getters['teacher_profile/totalPrice'] || 0,
              currency_paid_trial: this.$store.getters['user/currency'].isoCode || 'USD',
              language_paid_trial: this.$store.state.teacher_profile.selectedLanguage.name || 'English',
              lesson_length_paid_trial: `${this.selectedCourse.length} minutes` || '30 minutes',
              lesson_time_paid_trial: this.getFormattedDate,
              package_type_paid_trial: 'paid trial',
              package_paid_trial: `${this.selectedCourse.lessons} Lesson` || '1 Lesson',
              payment_type_paid_trial: 'credit',
              user_name: hashedName,
              email_id: hashedEmail
            }]
          }
        };
      } // Create standard purchase event for regular lessons
      else {
        eventData = {
          event: 'purchase',
          ecommerce: {
            transaction_id: 'T_12345',
            value: this.$store.getters['teacher_profile/totalPrice'] || 0,
            tax: null,
            currency: this.$store.getters['user/currency'].isoCode || 'USD',
            user_id: this.$store.getters['user/getUserId'] || '0',
            items: [{
              item_id: this.$store.state.teacher_profile.item.id || '1234',
              teacher_name: `${this.$store.state.teacher_profile.item.firstName.trim()} ${this.$store.state.teacher_profile.item.lastName.trim()}`,
              total_price: this.$store.getters['teacher_profile/totalPrice'] || 120,
              currency: this.$store.getters['user/currency'].isoCode || 'USD',
              language: this.$store.state.teacher_profile.selectedLanguage.name || 'English',
              lesson_length: `${this.selectedCourse.length} minutes` || '30 minutes',
              lesson_time: this.getFormattedDate,
              package_type: 'Paid',
              package: `${this.selectedCourse.lessons} Lesson` || '1 Lesson',
              payment_type: 'credit',
              user_name: hashedName,
              email_id: hashedEmail
            }]
          }
        };
      }

      localStorage.setItem('event_data', JSON.stringify(eventData)); // Print initial event data
      // eslint-disable-next-line no-console

      if (this.selectedPaymentMethod === 2) {
        window.localStorage.setItem('teacher-username', this.username);
      }

      this.$store.dispatch('loadingStart');
      this.$store.dispatch('purchase/scheduleLessons', this.username);
    }

  }
});
// CONCATENATED MODULE: ./components/SummaryLessonDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_SummaryLessonDialogvue_type_script_lang_js_ = (SummaryLessonDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCheckbox/VCheckbox.js
var VCheckbox = __webpack_require__(1128);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(896);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VRadioGroup/VRadio.js
var VRadio = __webpack_require__(1093);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VRadioGroup/VRadioGroup.js
var VRadioGroup = __webpack_require__(1094);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(897);

// CONCATENATED MODULE: ./components/SummaryLessonDialog.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1313)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_SummaryLessonDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "42c5d47e"
  
)

/* harmony default export */ var SummaryLessonDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LDialog: __webpack_require__(28).default})


/* vuetify-loader */










installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCheckbox: VCheckbox["a" /* default */],VCol: VCol["a" /* default */],VForm: VForm["a" /* default */],VImg: VImg["a" /* default */],VRadio: VRadio["a" /* default */],VRadioGroup: VRadioGroup["a" /* default */],VRow: VRow["a" /* default */],VTextarea: VTextarea["a" /* default */]})


/***/ }),

/***/ 902:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return BaseItemGroup; });
/* harmony import */ var _src_components_VItemGroup_VItemGroup_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(906);
/* harmony import */ var _src_components_VItemGroup_VItemGroup_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VItemGroup_VItemGroup_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _mixins_proxyable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(104);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3);
// Styles


 // Utilities



const BaseItemGroup = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(_mixins_proxyable__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], _mixins_themeable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"]).extend({
  name: 'base-item-group',
  props: {
    activeClass: {
      type: String,
      default: 'v-item--active'
    },
    mandatory: Boolean,
    max: {
      type: [Number, String],
      default: null
    },
    multiple: Boolean,
    tag: {
      type: String,
      default: 'div'
    }
  },

  data() {
    return {
      // As long as a value is defined, show it
      // Otherwise, check if multiple
      // to determine which default to provide
      internalLazyValue: this.value !== undefined ? this.value : this.multiple ? [] : undefined,
      items: []
    };
  },

  computed: {
    classes() {
      return {
        'v-item-group': true,
        ...this.themeClasses
      };
    },

    selectedIndex() {
      return this.selectedItem && this.items.indexOf(this.selectedItem) || -1;
    },

    selectedItem() {
      if (this.multiple) return undefined;
      return this.selectedItems[0];
    },

    selectedItems() {
      return this.items.filter((item, index) => {
        return this.toggleMethod(this.getValue(item, index));
      });
    },

    selectedValues() {
      if (this.internalValue == null) return [];
      return Array.isArray(this.internalValue) ? this.internalValue : [this.internalValue];
    },

    toggleMethod() {
      if (!this.multiple) {
        return v => this.internalValue === v;
      }

      const internalValue = this.internalValue;

      if (Array.isArray(internalValue)) {
        return v => internalValue.includes(v);
      }

      return () => false;
    }

  },
  watch: {
    internalValue: 'updateItemsState',
    items: 'updateItemsState'
  },

  created() {
    if (this.multiple && !Array.isArray(this.internalValue)) {
      Object(_util_console__WEBPACK_IMPORTED_MODULE_4__[/* consoleWarn */ "c"])('Model must be bound to an array if the multiple property is true.', this);
    }
  },

  methods: {
    genData() {
      return {
        class: this.classes
      };
    },

    getValue(item, i) {
      return item.value == null || item.value === '' ? i : item.value;
    },

    onClick(item) {
      this.updateInternalValue(this.getValue(item, this.items.indexOf(item)));
    },

    register(item) {
      const index = this.items.push(item) - 1;
      item.$on('change', () => this.onClick(item)); // If no value provided and mandatory,
      // assign first registered item

      if (this.mandatory && !this.selectedValues.length) {
        this.updateMandatory();
      }

      this.updateItem(item, index);
    },

    unregister(item) {
      if (this._isDestroyed) return;
      const index = this.items.indexOf(item);
      const value = this.getValue(item, index);
      this.items.splice(index, 1);
      const valueIndex = this.selectedValues.indexOf(value); // Items is not selected, do nothing

      if (valueIndex < 0) return; // If not mandatory, use regular update process

      if (!this.mandatory) {
        return this.updateInternalValue(value);
      } // Remove the value


      if (this.multiple && Array.isArray(this.internalValue)) {
        this.internalValue = this.internalValue.filter(v => v !== value);
      } else {
        this.internalValue = undefined;
      } // If mandatory and we have no selection
      // add the last item as value

      /* istanbul ignore else */


      if (!this.selectedItems.length) {
        this.updateMandatory(true);
      }
    },

    updateItem(item, index) {
      const value = this.getValue(item, index);
      item.isActive = this.toggleMethod(value);
    },

    // https://github.com/vuetifyjs/vuetify/issues/5352
    updateItemsState() {
      this.$nextTick(() => {
        if (this.mandatory && !this.selectedItems.length) {
          return this.updateMandatory();
        } // TODO: Make this smarter so it
        // doesn't have to iterate every
        // child in an update


        this.items.forEach(this.updateItem);
      });
    },

    updateInternalValue(value) {
      this.multiple ? this.updateMultiple(value) : this.updateSingle(value);
    },

    updateMandatory(last) {
      if (!this.items.length) return;
      const items = this.items.slice();
      if (last) items.reverse();
      const item = items.find(item => !item.disabled); // If no tabs are available
      // aborts mandatory value

      if (!item) return;
      const index = this.items.indexOf(item);
      this.updateInternalValue(this.getValue(item, index));
    },

    updateMultiple(value) {
      const defaultValue = Array.isArray(this.internalValue) ? this.internalValue : [];
      const internalValue = defaultValue.slice();
      const index = internalValue.findIndex(val => val === value);
      if (this.mandatory && // Item already exists
      index > -1 && // value would be reduced below min
      internalValue.length - 1 < 1) return;
      if ( // Max is set
      this.max != null && // Item doesn't exist
      index < 0 && // value would be increased above max
      internalValue.length + 1 > this.max) return;
      index > -1 ? internalValue.splice(index, 1) : internalValue.push(value);
      this.internalValue = internalValue;
    },

    updateSingle(value) {
      const isSame = value === this.internalValue;
      if (this.mandatory && isSame) return;
      this.internalValue = isSame ? undefined : value;
    }

  },

  render(h) {
    return h(this.tag, this.genData(), this.$slots.default);
  }

});
/* unused harmony default export */ var _unused_webpack_default_export = (BaseItemGroup.extend({
  name: 'v-item-group',

  provide() {
    return {
      itemGroup: this
    };
  }

}));

/***/ }),

/***/ 903:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(0);


/* harmony default export */ __webpack_exports__["a"] = (vue__WEBPACK_IMPORTED_MODULE_0___default.a.extend({
  name: 'comparable',
  props: {
    valueComparator: {
      type: Function,
      default: _util_helpers__WEBPACK_IMPORTED_MODULE_1__[/* deepEqual */ "h"]
    }
  }
}));

/***/ }),

/***/ 906:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(907);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("73707fd0", content, true)

/***/ }),

/***/ 907:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-item-group{flex:0 1 auto;position:relative;max-width:100%;transition:.3s cubic-bezier(.25,.8,.5,1)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 934:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _directives_ripple__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(22);
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(1);
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_1__);
// Directives
 // Types


/* harmony default export */ __webpack_exports__["a"] = (vue__WEBPACK_IMPORTED_MODULE_1___default.a.extend({
  name: 'rippleable',
  directives: {
    ripple: _directives_ripple__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]
  },
  props: {
    ripple: {
      type: [Boolean, Object],
      default: true
    }
  },
  methods: {
    genRipple(data = {}) {
      if (!this.ripple) return null;
      data.staticClass = 'v-input--selection-controls__ripple';
      data.directives = data.directives || [];
      data.directives.push({
        name: 'ripple',
        value: {
          center: true
        }
      });
      return this.$createElement('div', data);
    }

  }
}));

/***/ }),

/***/ 935:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(957);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("2e2bc7da", content, true)

/***/ }),

/***/ 936:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "b", function() { return prevent; });
/* harmony import */ var _components_VInput__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20);
/* harmony import */ var _rippleable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(934);
/* harmony import */ var _comparable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(903);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2);
// Components
 // Mixins


 // Utilities


function prevent(e) {
  e.preventDefault();
}
/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(_components_VInput__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"], _rippleable__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], _comparable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"]).extend({
  name: 'selectable',
  model: {
    prop: 'inputValue',
    event: 'change'
  },
  props: {
    id: String,
    inputValue: null,
    falseValue: null,
    trueValue: null,
    multiple: {
      type: Boolean,
      default: null
    },
    label: String
  },

  data() {
    return {
      hasColor: this.inputValue,
      lazyValue: this.inputValue
    };
  },

  computed: {
    computedColor() {
      if (!this.isActive) return undefined;
      if (this.color) return this.color;
      if (this.isDark && !this.appIsDark) return 'white';
      return 'primary';
    },

    isMultiple() {
      return this.multiple === true || this.multiple === null && Array.isArray(this.internalValue);
    },

    isActive() {
      const value = this.value;
      const input = this.internalValue;

      if (this.isMultiple) {
        if (!Array.isArray(input)) return false;
        return input.some(item => this.valueComparator(item, value));
      }

      if (this.trueValue === undefined || this.falseValue === undefined) {
        return value ? this.valueComparator(value, input) : Boolean(input);
      }

      return this.valueComparator(input, this.trueValue);
    },

    isDirty() {
      return this.isActive;
    },

    rippleState() {
      return !this.isDisabled && !this.validationState ? undefined : this.validationState;
    }

  },
  watch: {
    inputValue(val) {
      this.lazyValue = val;
      this.hasColor = val;
    }

  },
  methods: {
    genLabel() {
      const label = _components_VInput__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"].options.methods.genLabel.call(this);
      if (!label) return label;
      label.data.on = {
        // Label shouldn't cause the input to focus
        click: prevent
      };
      return label;
    },

    genInput(type, attrs) {
      return this.$createElement('input', {
        attrs: Object.assign({
          'aria-checked': this.isActive.toString(),
          disabled: this.isDisabled,
          id: this.computedId,
          role: type,
          type
        }, attrs),
        domProps: {
          value: this.value,
          checked: this.isActive
        },
        on: {
          blur: this.onBlur,
          change: this.onChange,
          focus: this.onFocus,
          keydown: this.onKeydown,
          click: prevent
        },
        ref: 'input'
      });
    },

    onBlur() {
      this.isFocused = false;
    },

    onClick(e) {
      this.onChange();
      this.$emit('click', e);
    },

    onChange() {
      if (!this.isInteractive) return;
      const value = this.value;
      let input = this.internalValue;

      if (this.isMultiple) {
        if (!Array.isArray(input)) {
          input = [];
        }

        const length = input.length;
        input = input.filter(item => !this.valueComparator(item, value));

        if (input.length === length) {
          input.push(value);
        }
      } else if (this.trueValue !== undefined && this.falseValue !== undefined) {
        input = this.valueComparator(input, this.trueValue) ? this.falseValue : this.trueValue;
      } else if (value) {
        input = this.valueComparator(input, value) ? null : value;
      } else {
        input = !input;
      }

      this.validate(true, input);
      this.internalValue = input;
      this.hasColor = input;
    },

    onFocus() {
      this.isFocused = true;
    },

    /** @abstract */
    onKeydown(e) {}

  }
}));

/***/ }),

/***/ 957:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-input--selection-controls.v-input--is-disabled:not(.v-input--indeterminate) .v-icon{color:rgba(0,0,0,.26)!important}.theme--dark.v-input--selection-controls.v-input--is-disabled:not(.v-input--indeterminate) .v-icon{color:hsla(0,0%,100%,.3)!important}.v-input--selection-controls{margin-top:16px;padding-top:4px}.v-input--selection-controls>.v-input__append-outer,.v-input--selection-controls>.v-input__prepend-outer{margin-top:0;margin-bottom:0}.v-input--selection-controls:not(.v-input--hide-details)>.v-input__slot{margin-bottom:12px}.v-input--selection-controls .v-input__slot,.v-input--selection-controls .v-radio{cursor:pointer}.v-input--selection-controls .v-input__slot>.v-label,.v-input--selection-controls .v-radio>.v-label{align-items:center;display:inline-flex;flex:1 1 auto;height:auto}.v-input--selection-controls__input{color:inherit;display:inline-flex;flex:0 0 auto;height:24px;position:relative;transition:.3s cubic-bezier(.25,.8,.5,1);transition-property:transform;width:24px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__input .v-icon{width:100%}.v-application--is-ltr .v-input--selection-controls__input{margin-right:8px}.v-application--is-rtl .v-input--selection-controls__input{margin-left:8px}.v-input--selection-controls__input input[role=checkbox],.v-input--selection-controls__input input[role=radio],.v-input--selection-controls__input input[role=switch]{position:absolute;opacity:0;width:100%;height:100%;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__input+.v-label{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__ripple{border-radius:50%;cursor:pointer;height:34px;position:absolute;transition:inherit;width:34px;left:-12px;top:calc(50% - 24px);margin:7px}.v-input--selection-controls__ripple:before{border-radius:inherit;bottom:0;content:\"\";position:absolute;opacity:.2;left:0;right:0;top:0;transform-origin:center center;transform:scale(.2);transition:inherit}.v-input--selection-controls__ripple>.v-ripple__container{transform:scale(1.2)}.v-input--selection-controls.v-input--dense .v-input--selection-controls__ripple{width:28px;height:28px;left:-9px}.v-input--selection-controls.v-input--dense:not(.v-input--switch) .v-input--selection-controls__ripple{top:calc(50% - 21px)}.v-input--selection-controls.v-input{flex:0 1 auto}.v-input--selection-controls.v-input--is-focused .v-input--selection-controls__ripple:before,.v-input--selection-controls .v-radio--is-focused .v-input--selection-controls__ripple:before{background:currentColor;transform:scale(1.2)}.v-input--selection-controls__input:hover .v-input--selection-controls__ripple:before{background:currentColor;transform:scale(1.2);transition:none}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 971:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(972);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("5e62c9d0", content, true)

/***/ }),

/***/ 972:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-radio--is-disabled label{color:rgba(0,0,0,.38)}.theme--light.v-radio--is-disabled .v-icon{color:rgba(0,0,0,.26)!important}.theme--dark.v-radio--is-disabled label{color:hsla(0,0%,100%,.5)}.theme--dark.v-radio--is-disabled .v-icon{color:hsla(0,0%,100%,.3)!important}.v-radio{align-items:center;display:flex;height:auto;outline:none}.v-radio--is-disabled{pointer-events:none;cursor:default}.v-input--radio-group.v-input--radio-group--row .v-radio{margin-right:16px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 973:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(974);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("999cb8a8", content, true)

/***/ }),

/***/ 974:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-input--radio-group legend.v-label{cursor:text;font-size:14px;height:auto}.v-input--radio-group__input{border:none;cursor:default;display:flex;width:100%}.v-input--radio-group--column .v-input--radio-group__input>.v-label{padding-bottom:8px}.v-input--radio-group--row .v-input--radio-group__input>.v-label{padding-right:8px}.v-input--radio-group--row legend{align-self:center;display:inline-block}.v-input--radio-group--row .v-input--radio-group__input{flex-direction:row;flex-wrap:wrap}.v-input--radio-group--column legend{padding-bottom:8px}.v-input--radio-group--column .v-radio:not(:last-child):not(:only-child){margin-bottom:8px}.v-input--radio-group--column .v-input--radio-group__input{flex-direction:column}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

};;
//# sourceMappingURL=summary-lesson-dialog.js.map