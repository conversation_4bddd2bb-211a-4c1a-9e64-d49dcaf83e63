(window.webpackJsonp=window.webpackJsonp||[]).push([[47,39,56],{1372:function(t,e,o){var n=o(43);t.exports=function(t){return n(Set.prototype.values,t)}},1384:function(t,e,o){"use strict";var n=o(43),r=o(79),d=o(32);t.exports=function(){for(var t=d(this),e=r(t.add),o=0,l=arguments.length;o<l;o++)n(e,t,arguments[o]);return t}},1389:function(t,e,o){"use strict";o.r(e);o(31),o(35),o(60);var n=o(1528),r=o(266),d=o(1428),l=(o(1449),{components:{VueDraggableResizable:n.default},mixins:[d.a],props:{asset:{type:Object,required:!0},childHeaderHeight:{type:Number,default:0},lockAspectRatio:{type:Boolean,default:!0},scalable:{type:Boolean,default:!0},hoverEnabled:{type:Boolean,default:!0},isDraggableProp:{type:Boolean,default:!0},hideResizeIcon:{type:Boolean,default:!1},handles:{type:Array,default:function(){return["tl","tm","tr","mr","br","bm","bl","ml"]}}},data:function(){return{width:r.j,height:r.i,top:50,left:500,isHovered:!1,isHoveredByAsset:!1,index:5,eventBodyClass:null,allowIndexChange:!0,resizable:!0,draggable:!0,synchronizeable:!0,viewportWidth:window.innerWidth,viewportHeight:window.innerHeight,offset:5}},computed:{isCanvasOversizeX:function(){return r.n>this.viewportWidth},isCanvasOversizeY:function(){return r.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return r.k*this.zoomIndex>this.viewportHeight},getRoleHoverColor:function(){return"teacher"===this.role?"var(--v-teacherColor-base)":"var(--v-studentColor-base)"},enabledResizeable:function(){return this.resizable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},enabledDraggable:function(){return this.isDraggableProp&&this.draggable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},maxIndex:function(){return this.$store.state.classroom.maxIndex},zoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},zoomIndex:function(){var t,e;return null!==(t=null===(e=this.zoom)||void 0===e?void 0:e.zoomIndex)&&void 0!==t?t:1},type:function(){var t,e;return null===(t=this.asset)||void 0===t||null===(e=t.asset)||void 0===e?void 0:e.type},topScale:function(){return"toolbar"===this.type?this.top<0||this.top+this.height>this.viewportHeight?this.viewportHeight-this.height-70:this.top:this.synchronizeable?this.top-this.zoom.y:this.top},leftScale:function(){return"toolbar"===this.type?this.left+this.width>this.viewportWidth||this.left<0?this.viewportWidth-this.width-15:this.left:this.synchronizeable?this.left-this.zoom.x:this.left},isLockedForStudent:function(){return this.$store.getters["classroom/isLocked"]&&"student"===this.role},isSocketConnected:function(){return this.$store.state.socket.isConnected}},watch:{"asset.asset":function(t){this.move(t)}},mounted:function(){this.move(this.asset.asset)},methods:{mouseenter:function(){var t,e;(this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!0,this.socketAssetMoved({isHovered:!0})),"twilio"===this.type||"tokbox"===this.type||"editor"===this.type)&&(this.$store.commit("classroom/setCursorNameBeforeChange",(null===(t=this.$store.getters["classroom/userParams"])||void 0===t?void 0:t.cursor)||"cursor-pointer"),this.$store.commit("classroom/setToolNameBeforeChange",(null===(e=this.$store.getters["classroom/userParams"])||void 0===e?void 0:e.tool)||"pointer"),this.setTool("pointer","cursor-pointer"))},mouseleave:function(){this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!1,this.socketAssetMoved({isHovered:!1})),"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||this.setTool(this.$store.state.classroom.toolNameBeforeChange,this.$store.state.classroom.cursorNameBeforeChange)},onIndex:function(){this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{index:this.index}}),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{index:this.index}})},updateAsset:function(t,e){this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,index:this.index}})},onDrag:function(t,e){if(this.synchronizeable){if(this.left=t+this.zoom.x,this.top=e+this.zoom.y,this.allowIndexChange){var o=document.body;o.classList.contains(this.eventBodyClass)||(this.eventBodyClass="dragging",o.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var n={left:this.left,top:this.top,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:n}),this.socketAssetMoved(n)}},onDragStop:function(t,e){var o=document.body;o.classList.contains(this.eventBodyClass)&&(o.classList.remove(this.eventBodyClass),this.eventBodyClass=null),this.allowIndexChange=!0,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,index:this.index}})},onResize:function(t,e,o,n,r){if(this.synchronizeable){if(this.left=t+this.zoom.x,this.top=e+this.zoom.y,this.width=o,this.height=n-this.childHeaderHeight,this.allowIndexChange){var d=document.body;d.classList.contains(this.eventBodyClass)||(this.eventBodyClass=r.split(" ")[1],d.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var l={left:this.left,top:this.top,width:this.width,height:this.height,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:l}),this.socketAssetMoved(l)}},onResizeStop:function(t,e,o,n){this.eventBodyClass&&document.body.classList.remove(this.eventBodyClass),this.allowIndexChange=!0,this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,width:o,height:n-this.childHeaderHeight,index:this.index}})},socketAssetMoved:function(t){this.isSocketConnected&&this.$socket.emit("asset-moved",{id:this.asset.id,lessonId:this.asset.lessonId,asset:t})},move:function(t){if(void 0!==t.width?this.width=t.width:"editor"===this.type&&(this.width=.66*(this.isCanvasOversizeX?this.viewportWidth:r.n)),void 0!==t.height)this.height=t.height;else{if("editor"===this.type){var e=.8*(this.isCanvasOversizeY?this.viewportHeight:r.k);e>1200&&(e=1200),e<400&&(e=400),this.height=e-2*this.offset}"audio"===this.type&&(this.height=0)}void 0!==t.top?this.top=t.top:"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||(this.top=this.offset),void 0!==t.left?this.left=t.left:("twilio"!==this.type&&"tokbox"!==this.type||(this.left=(this.isCanvasOversizeX?this.viewportWidth:r.n)-this.width-this.offset),"editor"===this.type&&(this.left=this.offset)),void 0!==t.index&&(this.index=t.index),void 0!==t.resizable&&(this.resizable=t.resizable),void 0!==t.draggable&&(this.draggable=t.draggable),void 0!==t.synchronizeable&&(this.synchronizeable=t.synchronizeable),void 0!==t.isHovered&&(this.isHoveredByAsset=t.isHovered)}}}),c=o(22),component=Object(c.a)(l,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{on:{mouseenter:t.mouseenter,mouseleave:t.mouseleave}},[o("vue-draggable-resizable",{ref:"vueDraggableResizable",class:{student:"student"===t.role,teacher:"teacher"===t.role,"hide-resize-icon":t.hideResizeIcon},style:{outline:t.isHoveredByAsset||t.isHovered?"3px solid "+t.getRoleHoverColor:"none"},attrs:{draggable:t.enabledDraggable,resizable:t.enabledResizeable,w:t.width,h:t.height+t.childHeaderHeight,x:t.leftScale,y:t.topScale,z:t.index,"zoom-index":t.scalable?t.zoom.zoomIndex:1,"zoom-x":t.zoom.x,"zoom-y":t.zoom.y,"child-header-height":t.childHeaderHeight,"lock-aspect-ratio":t.lockAspectRatio,handles:t.handles},on:{dragging:t.onDrag,resizing:t.onResize,dragstop:t.onDragStop,resizestop:t.onResizeStop,"update-asset":t.updateAsset},nativeOn:{click:function(e){return t.onIndex.apply(null,arguments)}}},[t._t("default",null,{onIndex:t.onIndex})],2)],1)}),[],!1,null,null,null);e.default=component.exports},1390:function(t,e,o){"use strict";o(872)("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),o(873))},1391:function(t,e,o){"use strict";o(11)({target:"Set",proto:!0,real:!0,forced:!0},{addAll:o(1384)})},1392:function(t,e,o){"use strict";o(11)({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:o(874)})},1393:function(t,e,o){"use strict";var n=o(11),r=o(87),d=o(43),l=o(79),c=o(32),h=o(125),f=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{difference:function(t){var e=c(this),o=new(h(e,r("Set")))(e),n=l(o.delete);return f(t,(function(t){d(n,o,t)})),o}})},1394:function(t,e,o){"use strict";var n=o(11),r=o(32),d=o(69),l=o(1372),c=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{every:function(t){var e=r(this),o=l(e),n=d(t,arguments.length>1?arguments[1]:void 0);return!c(o,(function(t,o){if(!n(t,t,e))return o()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1395:function(t,e,o){"use strict";var n=o(11),r=o(87),d=o(43),l=o(79),c=o(32),h=o(69),f=o(125),m=o(1372),v=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{filter:function(t){var e=c(this),o=m(e),n=h(t,arguments.length>1?arguments[1]:void 0),x=new(f(e,r("Set"))),y=l(x.add);return v(o,(function(t){n(t,t,e)&&d(y,x,t)}),{IS_ITERATOR:!0}),x}})},1396:function(t,e,o){"use strict";var n=o(11),r=o(32),d=o(69),l=o(1372),c=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{find:function(t){var e=r(this),o=l(e),n=d(t,arguments.length>1?arguments[1]:void 0);return c(o,(function(t,o){if(n(t,t,e))return o(t)}),{IS_ITERATOR:!0,INTERRUPTED:!0}).result}})},1397:function(t,e,o){"use strict";var n=o(11),r=o(87),d=o(43),l=o(79),c=o(32),h=o(125),f=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function(t){var e=c(this),o=new(h(e,r("Set"))),n=l(e.has),m=l(o.add);return f(t,(function(t){d(n,e,t)&&d(m,o,t)})),o}})},1398:function(t,e,o){"use strict";var n=o(11),r=o(43),d=o(79),l=o(32),c=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function(t){var e=l(this),o=d(e.has);return!c(t,(function(t,n){if(!0===r(o,e,t))return n()}),{INTERRUPTED:!0}).stopped}})},1399:function(t,e,o){"use strict";var n=o(11),r=o(87),d=o(43),l=o(79),c=o(45),h=o(32),f=o(209),m=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function(t){var e=f(this),o=h(t),n=o.has;return c(n)||(o=new(r("Set"))(t),n=l(o.has)),!m(e,(function(t,e){if(!1===d(n,o,t))return e()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1400:function(t,e,o){"use strict";var n=o(11),r=o(43),d=o(79),l=o(32),c=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function(t){var e=l(this),o=d(e.has);return!c(t,(function(t,n){if(!1===r(o,e,t))return n()}),{INTERRUPTED:!0}).stopped}})},1401:function(t,e,o){"use strict";var n=o(11),r=o(17),d=o(32),l=o(61),c=o(1372),h=o(86),f=r([].join),m=[].push;n({target:"Set",proto:!0,real:!0,forced:!0},{join:function(t){var e=d(this),o=c(e),n=void 0===t?",":l(t),r=[];return h(o,m,{that:r,IS_ITERATOR:!0}),f(r,n)}})},1402:function(t,e,o){"use strict";var n=o(11),r=o(87),d=o(69),l=o(43),c=o(79),h=o(32),f=o(125),m=o(1372),v=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{map:function(t){var e=h(this),o=m(e),n=d(t,arguments.length>1?arguments[1]:void 0),x=new(f(e,r("Set"))),y=c(x.add);return v(o,(function(t){l(y,x,n(t,t,e))}),{IS_ITERATOR:!0}),x}})},1403:function(t,e,o){"use strict";var n=o(11),r=o(5),d=o(79),l=o(32),c=o(1372),h=o(86),f=r.TypeError;n({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function(t){var e=l(this),o=c(e),n=arguments.length<2,r=n?void 0:arguments[1];if(d(t),h(o,(function(o){n?(n=!1,r=o):r=t(r,o,o,e)}),{IS_ITERATOR:!0}),n)throw f("Reduce of empty set with no initial value");return r}})},1404:function(t,e,o){"use strict";var n=o(11),r=o(32),d=o(69),l=o(1372),c=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{some:function(t){var e=r(this),o=l(e),n=d(t,arguments.length>1?arguments[1]:void 0);return c(o,(function(t,o){if(n(t,t,e))return o()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1405:function(t,e,o){"use strict";var n=o(11),r=o(87),d=o(43),l=o(79),c=o(32),h=o(125),f=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function(t){var e=c(this),o=new(h(e,r("Set")))(e),n=l(o.delete),m=l(o.add);return f(t,(function(t){d(n,o,t)||d(m,o,t)})),o}})},1406:function(t,e,o){"use strict";var n=o(11),r=o(87),d=o(79),l=o(32),c=o(125),h=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{union:function(t){var e=l(this),o=new(c(e,r("Set")))(e);return h(t,d(o.add),{that:o}),o}})},1428:function(t,e,o){"use strict";o(35),o(81),o(23),o(6),o(24),o(38);var n={computed:{role:function(){return this.$store.getters["classroom/role"]}},methods:{setTool:function(t,e){this.$store.commit("classroom/enableContainerComponent","pointer"===t),this.$socket.emit("cursor-moved",{tool:t,cursor:e.replace(/(-cursor|cursor-)/i,""),lessonId:this.$store.state.classroom.lessonId}),this.$store.commit("classroom/setUserTool",t),this.$store.commit("classroom/setUserCursor",e);var o=document.body,n=o.classList;this.removeCursors(n),o.classList.add("".concat(this.role,"-").concat(e)),this.classList=o.classList},removeCursors:function(t){t.forEach((function(t){t.includes("cursor")&&document.body.classList.remove(t)}))}}},r=o(22),component=Object(r.a)(n,undefined,undefined,!1,null,null,null);e.a=component.exports},1449:function(t,e,o){var content=o(1477);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("07a1f444",content,!0,{sourceMap:!1})},1477:function(t,e,o){var n=o(18),r=o(265),d=o(870),l=n(!1),c=r(d);l.push([t.i,'.vdr{touch-action:none;position:absolute;box-sizing:border-box;border:none}.handle{display:block!important}.resizable.vdr:not(.hide-resize-icon):after{content:"";position:absolute;bottom:4px;right:2px;width:8px;height:8px;background-image:url('+c+');background-size:contain;background-repeat:no-repeat;background-position:50%;opacity:.9;z-index:99}.vdr .handle{box-sizing:border-box;position:absolute;width:12px;height:12px;border:none;background-color:transparent;z-index:10}.vdr .handle-tl{top:-7px;left:-7px}.vdr .handle-tm{top:-7px;margin-left:-5px}.vdr .handle-tr{top:-7px;right:-7px}.vdr .handle-ml{margin-top:-5px;left:-7px}.vdr .handle-mr{margin-top:-5px;right:-7px}.vdr .handle-bl{bottom:-7px;left:-7px}.vdr .handle-bm{bottom:-7px;margin-left:-5px}.vdr .handle-br{bottom:-7px;right:-7px}@media only screen and (max-width:768px){[class*=handle-]:before{content:"";left:-10px;right:-10px;bottom:-10px;top:-10px;position:absolute}}.vdr .handle-bm,.vdr .handle-tm{width:100%;left:5px}.vdr .handle-ml,.vdr .handle-mr{height:100%;top:5px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:14px;height:14px;z-index:11}@media only screen and (max-width:1199px){.vdr .handle-bm,.vdr .handle-tm{height:15px}.vdr .handle-ml,.vdr .handle-mr{width:15px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:15px;height:15px}.vdr .handle-tl{top:-10px;left:-10px}.vdr .handle-tm{top:-10px;margin-left:-5px}.vdr .handle-tr{top:-10px;right:-10px}.vdr .handle-ml{margin-top:-5px;left:-10px}.vdr .handle-mr{margin-top:-5px;right:-10px}.vdr .handle-bl{bottom:-10px;left:-10px}.vdr .handle-bm{bottom:-10px;margin-left:-5px}.vdr .handle-br{bottom:-10px;right:-10px}}',""]),t.exports=l},1661:function(t,e,o){var content=o(1774);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("dcaa6c5e",content,!0,{sourceMap:!1})},1772:function(t,e,o){"use strict";(function(t){o.d(e,"a",(function(){return r}));var n=function(){return"undefined"!=typeof window?window:t},r=function(){var t=n();return t&&t.tinymce?t.tinymce:null}}).call(this,o(56))},1773:function(t,e,o){"use strict";o(1661)},1774:function(t,e,o){var n=o(18)(!1);n.push([t.i,'body.handle-bl .tox-sidebar-wrap:after,body.handle-bm .tox-sidebar-wrap:after,body.handle-br .tox-sidebar-wrap:after,body.handle-ml .tox-sidebar-wrap:after,body.handle-mr .tox-sidebar-wrap:after,body.handle-tl .tox-sidebar-wrap:after,body.handle-tm .tox-sidebar-wrap:after,body.handle-tr .tox-sidebar-wrap:after{content:"";position:absolute;top:0;left:0;width:100%;height:100%}body .tox-tinymce-aux{z-index:100001!important}body .tox{font-family:"Lato",sans-serif!important}body .tox .tox-dialog-wrap__backdrop{background-color:rgba(0,0,0,.4)}body .tox .tox-dialog{box-shadow:0 8px 17px rgba(17,46,90,.1);border-radius:20px;border:none}@media only screen and (max-width:991px){body .tox .tox-dialog{border-radius:15px}}body .tox .tox-dialog__header{padding:16px 30px 0}@media only screen and (max-width:991px){body .tox .tox-dialog__header{padding:12px 15px 0}}body .tox .tox-dialog__title{font-size:20px;font-weight:700;font-family:inherit;color:var(--v-success-base);background:linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));background:-webkit-linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));-webkit-background-clip:text;-webkit-text-fill-color:transparent}body .tox .tox-dialog__body-content{padding:16px 30px}@media only screen and (max-width:991px){body .tox .tox-dialog__body-content{padding:16px 15px}}body .tox .tox-dialog__footer{padding:8px 30px 24px;border-top:none}@media only screen and (max-width:991px){body .tox .tox-dialog__footer{padding:8px 15px 15px}}body .tox .tox-dialog__footer .tox-button{min-width:162px;height:38px;border-radius:20px;font-weight:600;font-size:15px}@media only screen and (max-width:767px){body .tox .tox-dialog__footer .tox-button{min-width:120px}}body .tox .tox-button{font-family:inherit}body .tox .tox-button,body .tox .tox-button *{cursor:pointer!important}body .tox .tox-button:not(.tox-button--secondary):not(.tox-button--naked){background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);border:none!important}body .tox .tox-button:not(.tox-button--secondary):not(.tox-button--naked):hover{background:linear-gradient(305.26deg,var(--v-success-base) 8.94%,var(--v-primary-base) 110.83%)!important}body .tox label{font-family:inherit!important}body .tox .tox-listboxfield .tox-listbox--select,body .tox .tox-textarea,body .tox .tox-textfield{font-family:inherit;cursor:auto!important}body.teacher-role .tox .tox-listboxfield .tox-listbox--select:focus,body.teacher-role .tox .tox-textarea:focus,body.teacher-role .tox .tox-textfield:focus{border-color:var(--v-teacherColor-base)}body.student-role .tox .tox-listboxfield .tox-listbox--select:focus,body.student-role .tox .tox-textarea:focus,body.student-role .tox .tox-textfield:focus{border-color:var(--v-studentColor-base)}.no-scroll{pointer-events:none}.classroom-editor{display:flex;flex-direction:column;height:100%;background-color:#fff;box-shadow:0 2px 30px rgba(0,0,0,.25)}.classroom-editor button,.classroom-editor button *,.classroom-editor div[role=button],.classroom-editor div[role=button] *{cursor:auto!important}.classroom-editor .tox-sidebar-wrap{position:relative}.classroom-editor .tox-edit-area *{cursor:auto!important}.classroom-editor .tox-edit-area a{cursor:pointer!important}.classroom-editor .tox-statusbar__path{display:none!important}',""]),t.exports=n},1912:function(t,e,o){"use strict";o.r(e);o(7),o(8),o(9),o(14),o(6),o(15);var n,r=o(2),d=o(10),l=(o(62),o(88),o(20),o(24),o(80),["onActivate","onAddUndo","onBeforeAddUndo","onBeforeExecCommand","onBeforeGetContent","onBeforeRenderUI","onBeforeSetContent","onBeforePaste","onBlur","onChange","onClearUndos","onClick","onContextMenu","onCopy","onCut","onDblclick","onDeactivate","onDirty","onDrag","onDragDrop","onDragEnd","onDragGesture","onDragOver","onDrop","onExecCommand","onFocus","onFocusIn","onFocusOut","onGetContent","onHide","onInit","onKeyDown","onKeyPress","onKeyUp","onLoadContent","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onNodeChange","onObjectResizeStart","onObjectResized","onObjectSelected","onPaste","onPostProcess","onPostRender","onPreProcess","onProgressState","onRedo","onRemove","onReset","onSaveContent","onSelectionChange","onSetAttrib","onSetContent","onShow","onSubmit","onUndo","onVisualAid"]),c=function(t){return-1!==l.map((function(t){return t.toLowerCase()})).indexOf(t.toLowerCase())},h=function(t,e,o){var n=e.$props.value?e.$props.value:"",r=e.$props.initialValue?e.$props.initialValue:"";o.setContent(n||(e.initialized?e.cache:r)),e.$watch("value",(function(t,n){o&&"string"==typeof t&&t!==n&&t!==o.getContent({format:e.$props.outputFormat})&&o.setContent(t)})),e.$listeners.input&&function(t,e){var o=t.$props.modelEvents?t.$props.modelEvents:null,n=Array.isArray(o)?o.join(" "):o;e.on(n||"change input undo redo",(function(){t.$emit("input",e.getContent({format:t.$props.outputFormat}))}))}(e,o),function(t,e,o){Object.keys(e).filter(c).forEach((function(n){var r=e[n];"function"==typeof r&&("onInit"===n?r(t,o):o.on(n.substring(2),(function(t){return r(t,o)})))}))}(t,e.$listeners,o),e.initialized=!0},f=0,m=function(t){var time=Date.now();return t+"_"+Math.floor(1e9*Math.random())+ ++f+String(time)},v=function(t){return void 0===t||""===t?[]:Array.isArray(t)?t:t.split(" ")},x=function(){return{listeners:[],scriptId:m("tiny-script"),scriptLoaded:!1}},y=(n=x(),{load:function(t,e,o){n.scriptLoaded?o():(n.listeners.push(o),t.getElementById(n.scriptId)||function(t,e,o,n){var r=e.createElement("script");r.referrerPolicy="origin",r.type="application/javascript",r.id=t,r.src=o;var d=function(){r.removeEventListener("load",d),n()};r.addEventListener("load",d),e.head&&e.head.appendChild(r)}(n.scriptId,t,e,(function(){n.listeners.forEach((function(t){return t()})),n.scriptLoaded=!0})))},reinitialize:function(){n=x()}}),w=o(1772),C={apiKey:String,cloudChannel:String,id:String,init:Object,initialValue:String,inline:Boolean,modelEvents:[String,Array],plugins:[String,Array],tagName:String,toolbar:[String,Array],value:String,disabled:Boolean,tinymceScriptSrc:String,outputFormat:{type:String,validator:function(t){return"html"===t||"text"===t}}},E=function(){return(E=Object.assign||function(t){for(var s,i=1,e=arguments.length;i<e;i++)for(var p in s=arguments[i])Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p]);return t}).apply(this,arguments)},S=function(t){return function(){var e,o,element,n=E(E({},t.$props.init),{readonly:t.$props.disabled,selector:"#"+t.elementId,plugins:(e=t.$props.init&&t.$props.init.plugins,o=t.$props.plugins,v(e).concat(v(o))),toolbar:t.$props.toolbar||t.$props.init&&t.$props.init.toolbar,inline:t.inlineEditor,setup:function(e){t.editor=e,e.on("init",(function(o){return h(o,t,e)})),t.$props.init&&"function"==typeof t.$props.init.setup&&t.$props.init.setup(e)}});null!==(element=t.element)&&"textarea"===element.tagName.toLowerCase()&&(t.element.style.visibility="",t.element.style.display=""),Object(w.a)().init(n)}},z={props:C,created:function(){this.elementId=this.$props.id||m("tiny-vue"),this.inlineEditor=this.$props.init&&this.$props.init.inline||this.$props.inline,this.initialized=!1},watch:{disabled:function(){this.editor.setMode(this.disabled?"readonly":"design")}},mounted:function(){if(this.element=this.$el,null!==Object(w.a)())S(this)();else if(this.element&&this.element.ownerDocument){var t=this.$props.cloudChannel?this.$props.cloudChannel:"5",e=this.$props.apiKey?this.$props.apiKey:"no-api-key",o=null==this.$props.tinymceScriptSrc?"https://cdn.tiny.cloud/1/"+e+"/tinymce/"+t+"/tinymce.min.js":this.$props.tinymceScriptSrc;y.load(this.element.ownerDocument,o,S(this))}},beforeDestroy:function(){null!==Object(w.a)()&&Object(w.a)().remove(this.editor)},deactivated:function(){var t;this.inlineEditor||(this.cache=this.editor.getContent(),null===(t=Object(w.a)())||void 0===t||t.remove(this.editor))},activated:function(){!this.inlineEditor&&this.initialized&&S(this)()},render:function(t){return this.inlineEditor?function(t,e,o){return t(o||"div",{attrs:{id:e}})}(t,this.elementId,this.$props.tagName):function(t,e){return t("textarea",{attrs:{id:e},style:{visibility:"hidden"}})}(t,this.elementId)}},k=o(859);function I(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}function P(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?I(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):I(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var $={name:"TinymceVue",components:{ClassroomContainer:o(1389).default,Editor:z},props:{file:{type:Object,required:!0}},data:function(){return{frameElId:"tinymce_editor",apiKey:"myt0tdi0h4brlcvgco6woa4zkus1ouk8l90uxuzlk6lunjn2",editor:null,text:"",toolbar:["bold italic strikethrough underline","forecolor backcolor","link hr table","alignleft aligncenter alignright alignjustify","bullist numlist outdent indent","exportButton codesample"].join(" | "),content_style:"\n        html {\n          height: 100%;\n          cursor: text;\n        }\n\n        body {\n          height: calc(100% - 30px);\n          margin: 15px;\n        }\n\n        .mce-content-body[data-mce-placeholder]::before {\n          cursor: text;\n        }\n\n        body p {\n          margin: 15px 0;\n        }\n      ",frameEl:null,frameEditAreaEl:null,frameDoc:null,changedBySocket:!1,changedByDatabase:!0,startCursorPos:null,endCursorPos:null,pressButtonKey:null,currentNode:null,previousNode:null,offset:null,isCaretPositionFound:!1,isDraggableProp:!0}},computed:{locale:function(){return this.$i18n.locale},isSocketConnected:function(){return this.$store.state.socket.isConnected},isCtrlKeyDown:function(){return this.$store.state.classroom.isCtrlKeyDown},zoom:function(){var t;return null===(t=this.$store.getters["classroom/zoomAsset"])||void 0===t?void 0:t.asset},assetText:function(){return this.file.asset.text}},watch:{text:function(){this.changeHandler(this.text)},isCtrlKeyDown:function(t,e){this.frameEl&&(t?this.frameEl.classList.add("no-scroll"):this.frameEl.classList.remove("no-scroll"))},assetText:function(t,e){this.setText(t)}},beforeDestroy:function(){var t=this;this.frameEditAreaEl&&(["pointerdown","pointerup","pointerout","pointerleave"].forEach((function(e){return t.frameEditAreaEl.removeEventListener(e,t.pointerUpHandler,!1)})),this.frameEditAreaEl.removeEventListener("pointerdown",this.pointerDownHandler,!1),this.frameEditAreaEl.removeEventListener("pointermove",this.pointerMoveHandler,!1))},methods:{initHandler:function(){var t=this;return Object(d.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.setText(t.assetText);case 2:t.frameEl=document.getElementById("".concat(t.frameElId,"_ifr")),t.frameDocument=t.frameEl.contentDocument||t.frameEl.contentWindow.document,t.frameEl.setAttribute("data-hj-allow-iframe",!0),t.frameEditAreaEl=t.frameDocument.getElementById("tinymce"),t.frameEditAreaEl&&(["pointerdown","pointerup","pointerout","pointerleave"].forEach((function(e){return t.frameEditAreaEl.addEventListener(e,t.pointerUpHandler,!1)})),t.frameEditAreaEl.addEventListener("pointerdown",t.pointerDownHandler,!1),t.frameEditAreaEl.addEventListener("pointermove",t.pointerMoveHandler,!1));case 7:case"end":return e.stop()}}),e)})))()},setText:function(t){var e=this;return new Promise((function(o){e.text=t,o()}))},mouseOverHandler:function(t){this.$emit("mouse-move",P(P({},t),{},{clientX:(this.file.asset.left-this.zoom.x+t.clientX)*this.zoom.zoomIndex,clientY:(this.file.asset.top-this.zoom.y+t.clientY+40)*this.zoom.zoomIndex}))},pointerDownHandler:function(t){this.$emit("pointer-down",t)},pointerMoveHandler:function(t){this.$emit("pointer-move",t)},pointerUpHandler:function(t){this.$emit("pointer-up",t)},clickHandler:function(){this.assetText||this.setStartCursorPos()},focusHandler:function(t){t.onIndex()},dragOverHandler:function(t){t.preventDefault(),this.$store.commit("classroom/isDraggingTrigger",!0)},keyDownHandler:function(t){this.$store.commit("classroom/SET_IS_CTRL_KEY_DOWN",t.ctrlKey),this.pressButtonKey=t.keyCode},keyUpHandler:function(t){this.$store.commit("classroom/SET_IS_CTRL_KEY_DOWN",!1),[37,38,39,40].includes(t.keyCode)&&this.setStartCursorPos()},setStartCursorPos:function(){this.startCursorPos=this.getCaretCharacterOffsetWithin()},changeHandler:function(text){this.changedBySocket||this.changedByDatabase?(this.changedBySocket=!1,this.changedByDatabase=!1):(this.endCursorPos=this.getCaretCharacterOffsetWithin(),this.$socket.emit("text-editor-updated",{id:this.file.id,lessonId:this.file.lessonId,asset:{text:text,startPos:this.startCursorPos,endPos:this.endCursorPos,pressButtonKey:this.pressButtonKey,offsetTop:this.getCurrentNodeOffsetTop()}}),this.startCursorPos=this.endCursorPos,this.updateAsset(text))},updateAsset:Object(k.debounce)((function(text){this.$store.dispatch("classroom/updateAssetWithoutSync",{id:this.file.id,lessonId:this.file.lessonId,asset:{text:text}})}),500),getCaretCharacterOffsetWithin:function(){var t,e,element=null===(t=this.frameEditAreaEl)||void 0===t||null===(e=t.childNodes)||void 0===e?void 0:e[0];if(element){var o,n=element.ownerDocument||element.document,r=n.defaultView||n.parentWindow,d=0;if(void 0!==(null==r?void 0:r.getSelection)){if((o=r.getSelection()).rangeCount>0){var l=o.getRangeAt(0),c=l.cloneRange();c.selectNodeContents(element),c.setEnd(l.endContainer,l.endOffset),d=c.toString().length}}else if((o=n.selection)&&"Control"!==o.type){var h=o.createRange(),f=n.body.createTextRange();f.moveToElementText(element),f.setEndPoint("EndToEnd",h),d=f.text.length}return d}return 0},getCurrentNodeOffsetTop:function(){var t,e,element=null===(t=this.frameEditAreaEl)||void 0===t||null===(e=t.childNodes)||void 0===e?void 0:e[0];if(element){var o,n,r,d,l=element.ownerDocument||element.document,c=l.defaultView||l.parentWindow,h=0;if(void 0!==(null==c?void 0:c.getSelection))h=null!==(o=null===(n=c.getSelection())||void 0===n||null===(r=n.anchorNode)||void 0===r||null===(d=r.parentElement)||void 0===d?void 0:d.offsetTop)&&void 0!==o?o:0;return h}return 0},getCurrentNodeWithPosition:function(t){var e=this;return new Promise((function(o){for(var i=0;i<t.length;i++){var n,r,d;if((null===(n=t[i])||void 0===n||null===(r=n.childNodes)||void 0===r?void 0:r.length)>0)e.getCurrentNodeWithPosition(null===(d=t[i])||void 0===d?void 0:d.childNodes);else if(!e.isCaretPositionFound){var l,c,h;if(e.previousNode=e.currentNode,e.currentNode=t[i],null!==(l=e.previousNode)&&void 0!==l&&l.textContent.length)e.offset-=null===(h=e.previousNode)||void 0===h?void 0:h.textContent.length;if(e.offset<=(null===(c=e.currentNode)||void 0===c?void 0:c.textContent.length)){e.isCaretPositionFound=!0;break}}}o()}))},setCaretPosition:function(t,e){var o=this;return Object(d.a)(regeneratorRuntime.mark((function n(){var r,d;return regeneratorRuntime.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r=o.frameDocument.getSelection(),d=r.getRangeAt(0),o.offset=e,o.currentNode=null,o.previousNode=null,o.isCaretPositionFound=!1,n.next=8,o.getCurrentNodeWithPosition(t);case 8:null!=o.currentNode&&o.currentNode.length>=o.offset&&(d.setStart(o.currentNode,o.offset),d.collapse(!0),r.removeAllRanges(),r.addRange(d));case 9:case"end":return n.stop()}}),n)})))()},generatePdf:function(){this.$store.dispatch("lesson/generatePdf",this.file.lessonId)}},sockets:{"text-editor-updated":function(data){var t=this;return Object(d.a)(regeneratorRuntime.mark((function e(){var o,n,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.changedBySocket=!0,data.asset.text===(null===(o=t.frameEditAreaEl)||void 0===o?void 0:o.innerHTML)){e.next=10;break}return e.next=4,t.setText(data.asset.text);case 4:if(data.asset.offsetTop>0&&null!==(n=t.frameEl)&&void 0!==n&&n.contentWindow&&(t.frameEl.contentWindow.document.body.offsetHeight<data.asset.offsetTop||t.frameEl.contentWindow.scrollY>data.asset.offsetTop)&&t.frameEl.contentWindow.scrollTo({top:data.asset.offsetTop-15,behavior:"smooth"}),null==t.startCursorPos){e.next=10;break}return(data.asset.endPos<data.asset.startPos&&t.startCursorPos>=data.asset.startPos||data.asset.endPos>data.asset.startPos&&t.startCursorPos>data.asset.startPos)&&(t.startCursorPos=t.startCursorPos+data.asset.endPos-data.asset.startPos),t.startCursorPos>0&&t.startCursorPos>data.asset.startPos&&data.asset.endPos===data.asset.startPos&&13!==data.asset.pressButtonKey&&(t.startCursorPos=t.startCursorPos-1),e.next=10,t.setCaretPosition(null===(r=t.frameEditAreaEl)||void 0===r?void 0:r.childNodes,t.startCursorPos);case 10:case"end":return e.stop()}}),e)})))()}}},O=(o(1773),o(22)),component=Object(O.a)($,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("classroom-container",{attrs:{asset:t.file,"lock-aspect-ratio":!1,"is-draggable-prop":t.isDraggableProp,"hide-resize-icon":""},scopedSlots:t._u([{key:"default",fn:function(e){return[o("div",{staticClass:"classroom-editor"},[o("editor",{attrs:{id:t.frameElId,"api-key":t.apiKey,init:{language:t.locale,height:"100%",placeholder:t.$t("welcome_to_new_langu_classroom"),plugins:["lists link hr table codesample autolink"],extended_valid_elements:"a[href|target=_blank]",menubar:"",branding:!1,contextmenu:!1,browser_spellcheck:!0,toolbar_drawer:"wrap",mobile:{toolbar_drawer:"wrap"},toolbar:t.toolbar,setup:function(e){e.ui.registry.addMenuButton("exportButton",{icon:"export",tooltip:"Export",fetch:function(e){e([{type:"menuitem",text:"PDF",tooltip:"PDF",onAction:function(){t.generatePdf()}}])}})},content_style:t.content_style},disabled:!t.isSocketConnected},on:{onInit:t.initHandler,onClick:t.setStartCursorPos,onFocus:function(o){return t.focusHandler(e)},onKeyUp:t.keyUpHandler,onKeyDown:t.keyDownHandler,onMouseMove:t.mouseOverHandler,onDragOver:t.dragOverHandler},model:{value:t.text,callback:function(e){t.text=e},expression:"text"}})],1)]}}])})}),[],!1,null,null,null);e.default=component.exports;installComponents(component,{ClassroomContainer:o(1389).default})}}]);