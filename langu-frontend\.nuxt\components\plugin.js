import Vue from 'vue'
import { wrapFunctional } from './utils'

const components = {
  BuzzDialog: () => import('../..\\components\\BuzzDialog.vue' /* webpackChunkName: "components/buzz-dialog" */).then(c => wrapFunctional(c.default || c)),
  Calendar: () => import('../..\\components\\Calendar.vue' /* webpackChunkName: "components/calendar" */).then(c => wrapFunctional(c.default || c)),
  CalendarDate: () => import('../..\\components\\CalendarDate.vue' /* webpackChunkName: "components/calendar-date" */).then(c => wrapFunctional(c.default || c)),
  CheckEmailDialog: () => import('../..\\components\\CheckEmailDialog.vue' /* webpackChunkName: "components/check-email-dialog" */).then(c => wrapFunctional(c.default || c)),
  ConfirmDialog: () => import('../..\\components\\ConfirmDialog.vue' /* webpackChunkName: "components/confirm-dialog" */).then(c => wrapFunctional(c.default || c)),
  CookiePopup: () => import('../..\\components\\CookiePopup.vue' /* webpackChunkName: "components/cookie-popup" */).then(c => wrapFunctional(c.default || c)),
  Footer: () => import('../..\\components\\Footer.vue' /* webpackChunkName: "components/footer" */).then(c => wrapFunctional(c.default || c)),
  FreeSlots: () => import('../..\\components\\FreeSlots.vue' /* webpackChunkName: "components/free-slots" */).then(c => wrapFunctional(c.default || c)),
  GoogleSignInButton: () => import('../..\\components\\GoogleSignInButton.vue' /* webpackChunkName: "components/google-sign-in-button" */).then(c => wrapFunctional(c.default || c)),
  Header: () => import('../..\\components\\Header.vue' /* webpackChunkName: "components/header" */).then(c => wrapFunctional(c.default || c)),
  LAvatar: () => import('../..\\components\\LAvatar.vue' /* webpackChunkName: "components/l-avatar" */).then(c => wrapFunctional(c.default || c)),
  LChip: () => import('../..\\components\\LChip.vue' /* webpackChunkName: "components/l-chip" */).then(c => wrapFunctional(c.default || c)),
  LDialog: () => import('../..\\components\\LDialog.vue' /* webpackChunkName: "components/l-dialog" */).then(c => wrapFunctional(c.default || c)),
  LessonTimeNotice: () => import('../..\\components\\LessonTimeNotice.vue' /* webpackChunkName: "components/lesson-time-notice" */).then(c => wrapFunctional(c.default || c)),
  LExpansionPanels: () => import('../..\\components\\LExpansionPanels.vue' /* webpackChunkName: "components/l-expansion-panels" */).then(c => wrapFunctional(c.default || c)),
  Loader: () => import('../..\\components\\Loader.vue' /* webpackChunkName: "components/loader" */).then(c => wrapFunctional(c.default || c)),
  LoadMoreBtn: () => import('../..\\components\\LoadMoreBtn.vue' /* webpackChunkName: "components/load-more-btn" */).then(c => wrapFunctional(c.default || c)),
  LoginSidebar: () => import('../..\\components\\LoginSidebar.vue' /* webpackChunkName: "components/login-sidebar" */).then(c => wrapFunctional(c.default || c)),
  MessageDialog: () => import('../..\\components\\MessageDialog.vue' /* webpackChunkName: "components/message-dialog" */).then(c => wrapFunctional(c.default || c)),
  Pagination: () => import('../..\\components\\Pagination.vue' /* webpackChunkName: "components/pagination" */).then(c => wrapFunctional(c.default || c)),
  SetPasswordDialog: () => import('../..\\components\\SetPasswordDialog.vue' /* webpackChunkName: "components/set-password-dialog" */).then(c => wrapFunctional(c.default || c)),
  Snackbar: () => import('../..\\components\\Snackbar.vue' /* webpackChunkName: "components/snackbar" */).then(c => wrapFunctional(c.default || c)),
  StarRating: () => import('../..\\components\\StarRating.vue' /* webpackChunkName: "components/star-rating" */).then(c => wrapFunctional(c.default || c)),
  Steps: () => import('../..\\components\\Steps.vue' /* webpackChunkName: "components/steps" */).then(c => wrapFunctional(c.default || c)),
  SummaryLessonDialog: () => import('../..\\components\\SummaryLessonDialog.vue' /* webpackChunkName: "components/summary-lesson-dialog" */).then(c => wrapFunctional(c.default || c)),
  TeacherCard: () => import('../..\\components\\TeacherCard.vue' /* webpackChunkName: "components/teacher-card" */).then(c => wrapFunctional(c.default || c)),
  TeacherFilter: () => import('../..\\components\\TeacherFilter.vue' /* webpackChunkName: "components/teacher-filter" */).then(c => wrapFunctional(c.default || c)),
  TeacherFilterNew: () => import('../..\\components\\TeacherFilterNew.vue' /* webpackChunkName: "components/teacher-filter-new" */).then(c => wrapFunctional(c.default || c)),
  TimePicker: () => import('../..\\components\\TimePicker.vue' /* webpackChunkName: "components/time-picker" */).then(c => wrapFunctional(c.default || c)),
  TimePickerItem: () => import('../..\\components\\TimePickerItem.vue' /* webpackChunkName: "components/time-picker-item" */).then(c => wrapFunctional(c.default || c)),
  UserStatus: () => import('../..\\components\\UserStatus.vue' /* webpackChunkName: "components/user-status" */).then(c => wrapFunctional(c.default || c)),
  Youtube: () => import('../..\\components\\Youtube.vue' /* webpackChunkName: "components/youtube" */).then(c => wrapFunctional(c.default || c)),
  BusinessPage: () => import('../..\\components\\business-page\\BusinessPage.vue' /* webpackChunkName: "components/business-page" */).then(c => wrapFunctional(c.default || c)),
  ClassroomAudioItem: () => import('../..\\components\\classroom\\AudioItem.vue' /* webpackChunkName: "components/classroom-audio-item" */).then(c => wrapFunctional(c.default || c)),
  ClassroomContainer: () => import('../..\\components\\classroom\\ClassroomContainer.vue' /* webpackChunkName: "components/classroom-container" */).then(c => wrapFunctional(c.default || c)),
  ClassroomContainerHeader: () => import('../..\\components\\classroom\\ClassroomContainerHeader.vue' /* webpackChunkName: "components/classroom-container-header" */).then(c => wrapFunctional(c.default || c)),
  ClassroomDropFileArea: () => import('../..\\components\\classroom\\DropFileArea.vue' /* webpackChunkName: "components/classroom-drop-file-area" */).then(c => wrapFunctional(c.default || c)),
  ClassroomImageItem: () => import('../..\\components\\classroom\\ImageItem.vue' /* webpackChunkName: "components/classroom-image-item" */).then(c => wrapFunctional(c.default || c)),
  ClassroomKonva: () => import('../..\\components\\classroom\\Konva.vue' /* webpackChunkName: "components/classroom-konva" */).then(c => wrapFunctional(c.default || c)),
  ClassroomLibrary: () => import('../..\\components\\classroom\\Library.vue' /* webpackChunkName: "components/classroom-library" */).then(c => wrapFunctional(c.default || c)),
  ClassroomOtherCursor: () => import('../..\\components\\classroom\\OtherCursor.vue' /* webpackChunkName: "components/classroom-other-cursor" */).then(c => wrapFunctional(c.default || c)),
  ClassroomPdfItem: () => import('../..\\components\\classroom\\PdfItem.vue' /* webpackChunkName: "components/classroom-pdf-item" */).then(c => wrapFunctional(c.default || c)),
  ClassroomTinymceVue: () => import('../..\\components\\classroom\\TinymceVue.vue' /* webpackChunkName: "components/classroom-tinymce-vue" */).then(c => wrapFunctional(c.default || c)),
  ClassroomToolbar: () => import('../..\\components\\classroom\\Toolbar.vue' /* webpackChunkName: "components/classroom-toolbar" */).then(c => wrapFunctional(c.default || c)),
  ClassroomVideoInput: () => import('../..\\components\\classroom\\VideoInput.vue' /* webpackChunkName: "components/classroom-video-input" */).then(c => wrapFunctional(c.default || c)),
  ClassroomVideoItem: () => import('../..\\components\\classroom\\VideoItem.vue' /* webpackChunkName: "components/classroom-video-item" */).then(c => wrapFunctional(c.default || c)),
  ClassroomViewport: () => import('../..\\components\\classroom\\Viewport.vue' /* webpackChunkName: "components/classroom-viewport" */).then(c => wrapFunctional(c.default || c)),
  FormEditor: () => import('../..\\components\\form\\Editor.vue' /* webpackChunkName: "components/form-editor" */).then(c => wrapFunctional(c.default || c)),
  FormRate: () => import('../..\\components\\form\\FormRate.vue' /* webpackChunkName: "components/form-rate" */).then(c => wrapFunctional(c.default || c)),
  FormSearchInput: () => import('../..\\components\\form\\SearchInput.vue' /* webpackChunkName: "components/form-search-input" */).then(c => wrapFunctional(c.default || c)),
  FormSelectInput: () => import('../..\\components\\form\\SelectInput.vue' /* webpackChunkName: "components/form-select-input" */).then(c => wrapFunctional(c.default || c)),
  FormSelectInputNew: () => import('../..\\components\\form\\SelectInputNew.vue' /* webpackChunkName: "components/form-select-input-new" */).then(c => wrapFunctional(c.default || c)),
  FormTextInput: () => import('../..\\components\\form\\TextInput.vue' /* webpackChunkName: "components/form-text-input" */).then(c => wrapFunctional(c.default || c)),
  HomepageAboutSection: () => import('../..\\components\\homepage\\AboutSection.vue' /* webpackChunkName: "components/homepage-about-section" */).then(c => wrapFunctional(c.default || c)),
  HomepageFaqSection: () => import('../..\\components\\homepage\\FaqSection.vue' /* webpackChunkName: "components/homepage-faq-section" */).then(c => wrapFunctional(c.default || c)),
  HomepageHowWorksSection: () => import('../..\\components\\homepage\\HowWorksSection.vue' /* webpackChunkName: "components/homepage-how-works-section" */).then(c => wrapFunctional(c.default || c)),
  HomepageIntroSection: () => import('../..\\components\\homepage\\IntroSection.vue' /* webpackChunkName: "components/homepage-intro-section" */).then(c => wrapFunctional(c.default || c)),
  HomepageLanguagesSection: () => import('../..\\components\\homepage\\LanguagesSection.vue' /* webpackChunkName: "components/homepage-languages-section" */).then(c => wrapFunctional(c.default || c)),
  HomepageReviewSection: () => import('../..\\components\\homepage\\ReviewSection.vue' /* webpackChunkName: "components/homepage-review-section" */).then(c => wrapFunctional(c.default || c)),
  HomepageSelectLanguage: () => import('../..\\components\\homepage\\SelectLanguage.vue' /* webpackChunkName: "components/homepage-select-language" */).then(c => wrapFunctional(c.default || c)),
  HomepageStatSection: () => import('../..\\components\\homepage\\StatSection.vue' /* webpackChunkName: "components/homepage-stat-section" */).then(c => wrapFunctional(c.default || c)),
  HomepageThinkingSection: () => import('../..\\components\\homepage\\ThinkingSection.vue' /* webpackChunkName: "components/homepage-thinking-section" */).then(c => wrapFunctional(c.default || c)),
  HomepageTutorsSection: () => import('../..\\components\\homepage\\TutorsSection.vue' /* webpackChunkName: "components/homepage-tutors-section" */).then(c => wrapFunctional(c.default || c)),
  ImagesAlarmGradientIcon: () => import('../..\\components\\images\\AlarmGradientIcon.vue' /* webpackChunkName: "components/images-alarm-gradient-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesBusinessPageIntroImage: () => import('../..\\components\\images\\BusinessPageIntroImage.vue' /* webpackChunkName: "components/images-business-page-intro-image" */).then(c => wrapFunctional(c.default || c)),
  ImagesBusinessPageIntroMobileImage: () => import('../..\\components\\images\\BusinessPageIntroMobileImage.vue' /* webpackChunkName: "components/images-business-page-intro-mobile-image" */).then(c => wrapFunctional(c.default || c)),
  ImagesCareerGradientIcon: () => import('../..\\components\\images\\CareerGradientIcon.vue' /* webpackChunkName: "components/images-career-gradient-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesCheckedGradientIcon: () => import('../..\\components\\images\\CheckedGradientIcon.vue' /* webpackChunkName: "components/images-checked-gradient-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesEducationGradientIcon: () => import('../..\\components\\images\\EducationGradientIcon.vue' /* webpackChunkName: "components/images-education-gradient-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesEmailIcon: () => import('../..\\components\\images\\EmailIcon.vue' /* webpackChunkName: "components/images-email-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesEnFlagIcon: () => import('../..\\components\\images\\EnFlagIcon.vue' /* webpackChunkName: "components/images-en-flag-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesEsFlagIcon: () => import('../..\\components\\images\\EsFlagIcon.vue' /* webpackChunkName: "components/images-es-flag-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesGoogleIcon: () => import('../..\\components\\images\\GoogleIcon.vue' /* webpackChunkName: "components/images-google-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesHomePageIntroImage: () => import('../..\\components\\images\\HomePageIntroImage.vue' /* webpackChunkName: "components/images-home-page-intro-image" */).then(c => wrapFunctional(c.default || c)),
  ImagesLifeGradientIcon: () => import('../..\\components\\images\\LifeGradientIcon.vue' /* webpackChunkName: "components/images-life-gradient-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesMoonGradientIcon: () => import('../..\\components\\images\\MoonGradientIcon.vue' /* webpackChunkName: "components/images-moon-gradient-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesPlFlagIcon: () => import('../..\\components\\images\\PlFlagIcon.vue' /* webpackChunkName: "components/images-pl-flag-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesSearchIcon: () => import('../..\\components\\images\\SearchIcon.vue' /* webpackChunkName: "components/images-search-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesSunGradientIcon: () => import('../..\\components\\images\\SunGradientIcon.vue' /* webpackChunkName: "components/images-sun-gradient-icon" */).then(c => wrapFunctional(c.default || c)),
  ImagesSunsetGradientIcon: () => import('../..\\components\\images\\SunsetGradientIcon.vue' /* webpackChunkName: "components/images-sunset-gradient-icon" */).then(c => wrapFunctional(c.default || c)),
  LandingPageTeachersSlider: () => import('../..\\components\\landing-page\\TeachersSlider.vue' /* webpackChunkName: "components/landing-page-teachers-slider" */).then(c => wrapFunctional(c.default || c)),
  LandingPageTestimonialsSlider: () => import('../..\\components\\landing-page\\TestimonialsSlider.vue' /* webpackChunkName: "components/landing-page-testimonials-slider" */).then(c => wrapFunctional(c.default || c)),
  PaymentsCountries: () => import('../..\\components\\payments\\countries.js' /* webpackChunkName: "components/payments-countries" */).then(c => wrapFunctional(c.default || c)),
  PaymentsPaymentDetailsModal: () => import('../..\\components\\payments\\PaymentDetailsModal.vue' /* webpackChunkName: "components/payments-payment-details-modal" */).then(c => wrapFunctional(c.default || c)),
  PaymentsPaymentItem: () => import('../..\\components\\payments\\PaymentItem.vue' /* webpackChunkName: "components/payments-payment-item" */).then(c => wrapFunctional(c.default || c)),
  PaymentsPaymentLesson: () => import('../..\\components\\payments\\PaymentLesson.vue' /* webpackChunkName: "components/payments-payment-lesson" */).then(c => wrapFunctional(c.default || c)),
  PaymentsPaymentPayout: () => import('../..\\components\\payments\\PaymentPayout.vue' /* webpackChunkName: "components/payments-payment-payout" */).then(c => wrapFunctional(c.default || c)),
  PaymentsPage: () => import('../..\\components\\payments\\PaymentsPage.vue' /* webpackChunkName: "components/payments-page" */).then(c => wrapFunctional(c.default || c)),
  PaymentsPayoutItem: () => import('../..\\components\\payments\\PayoutItem.vue' /* webpackChunkName: "components/payments-payout-item" */).then(c => wrapFunctional(c.default || c)),
  PaymentsPayoutModal: () => import('../..\\components\\payments\\PayoutModal.vue' /* webpackChunkName: "components/payments-payout-modal" */).then(c => wrapFunctional(c.default || c)),
  PaymentsSavedAccountsModal: () => import('../..\\components\\payments\\SavedAccountsModal.vue' /* webpackChunkName: "components/payments-saved-accounts-modal" */).then(c => wrapFunctional(c.default || c)),
  PaymentsWiseTransferModal: () => import('../..\\components\\payments\\WiseTransferModal.vue' /* webpackChunkName: "components/payments-wise-transfer-modal" */).then(c => wrapFunctional(c.default || c)),
  TeacherListing: () => import('../..\\components\\teacher-listing\\TeacherListing.vue' /* webpackChunkName: "components/teacher-listing" */).then(c => wrapFunctional(c.default || c)),
  TeacherListingBanner: () => import('../..\\components\\teacher-listing\\TeacherListingBanner.vue' /* webpackChunkName: "components/teacher-listing-banner" */).then(c => wrapFunctional(c.default || c)),
  TeacherListingHeader: () => import('../..\\components\\teacher-listing\\TeacherListingHeader.vue' /* webpackChunkName: "components/teacher-listing-header" */).then(c => wrapFunctional(c.default || c)),
  TeacherProfileCourseItem: () => import('../..\\components\\teacher-profile\\CourseItem.vue' /* webpackChunkName: "components/teacher-profile-course-item" */).then(c => wrapFunctional(c.default || c)),
  TeacherProfileFeedbackTags: () => import('../..\\components\\teacher-profile\\FeedbackTags.vue' /* webpackChunkName: "components/teacher-profile-feedback-tags" */).then(c => wrapFunctional(c.default || c)),
  TeacherProfileFindMoreTeachersButton: () => import('../..\\components\\teacher-profile\\FindMoreTeachersButton.vue' /* webpackChunkName: "components/teacher-profile-find-more-teachers-button" */).then(c => wrapFunctional(c.default || c)),
  TeacherProfilePricePerLesson: () => import('../..\\components\\teacher-profile\\PricePerLesson.vue' /* webpackChunkName: "components/teacher-profile-price-per-lesson" */).then(c => wrapFunctional(c.default || c)),
  TeacherProfileSidebar: () => import('../..\\components\\teacher-profile\\TeacherProfileSidebar.vue' /* webpackChunkName: "components/teacher-profile-sidebar" */).then(c => wrapFunctional(c.default || c)),
  TeacherProfileTimePickerDialog: () => import('../..\\components\\teacher-profile\\TimePickerDialog.vue' /* webpackChunkName: "components/teacher-profile-time-picker-dialog" */).then(c => wrapFunctional(c.default || c)),
  UserMessagesConversation: () => import('../..\\components\\user-messages\\Conversation.vue' /* webpackChunkName: "components/user-messages-conversation" */).then(c => wrapFunctional(c.default || c)),
  UserMessagesConversationItem: () => import('../..\\components\\user-messages\\ConversationItem.vue' /* webpackChunkName: "components/user-messages-conversation-item" */).then(c => wrapFunctional(c.default || c)),
  UserMessagesEmptyContent: () => import('../..\\components\\user-messages\\EmptyContent.vue' /* webpackChunkName: "components/user-messages-empty-content" */).then(c => wrapFunctional(c.default || c)),
  UserMessagesPage: () => import('../..\\components\\user-messages\\MessagesPage.vue' /* webpackChunkName: "components/user-messages-page" */).then(c => wrapFunctional(c.default || c)),
  UserLessonsLessonEvaluationDialog: () => import('../..\\components\\user-lessons\\LessonEvaluationDialog.vue' /* webpackChunkName: "components/user-lessons-lesson-evaluation-dialog" */).then(c => wrapFunctional(c.default || c)),
  UserLessonsLessonItem: () => import('../..\\components\\user-lessons\\LessonItem.vue' /* webpackChunkName: "components/user-lessons-lesson-item" */).then(c => wrapFunctional(c.default || c)),
  UserLessonsPage: () => import('../..\\components\\user-lessons\\LessonsPage.vue' /* webpackChunkName: "components/user-lessons-page" */).then(c => wrapFunctional(c.default || c)),
  UserLessonsPastLesson: () => import('../..\\components\\user-lessons\\PastLesson.vue' /* webpackChunkName: "components/user-lessons-past-lesson" */).then(c => wrapFunctional(c.default || c)),
  UserLessonsTimePickerDialog: () => import('../..\\components\\user-lessons\\TimePickerDialog.vue' /* webpackChunkName: "components/user-lessons-time-picker-dialog" */).then(c => wrapFunctional(c.default || c)),
  UserLessonsUnscheduledLesson: () => import('../..\\components\\user-lessons\\UnscheduledLesson.vue' /* webpackChunkName: "components/user-lessons-unscheduled-lesson" */).then(c => wrapFunctional(c.default || c)),
  UserLessonsUpcomingLesson: () => import('../..\\components\\user-lessons\\UpcomingLesson.vue' /* webpackChunkName: "components/user-lessons-upcoming-lesson" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsAboutMeInfo: () => import('../..\\components\\user-settings\\AboutMeInfo.vue' /* webpackChunkName: "components/user-settings-about-me-info" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsAddQualificationDialog: () => import('../..\\components\\user-settings\\AddQualificationDialog.vue' /* webpackChunkName: "components/user-settings-add-qualification-dialog" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsBackgroundInfo: () => import('../..\\components\\user-settings\\BackgroundInfo.vue' /* webpackChunkName: "components/user-settings-background-info" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsBasicInfo: () => import('../..\\components\\user-settings\\BasicInfo.vue' /* webpackChunkName: "components/user-settings-basic-info" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsCalendarNotificationInfo: () => import('../..\\components\\user-settings\\CalendarNotificationInfo.vue' /* webpackChunkName: "components/user-settings-calendar-notification-info" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsCourseItem: () => import('../..\\components\\user-settings\\CourseItem.vue' /* webpackChunkName: "components/user-settings-course-item" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsCoursesInfo: () => import('../..\\components\\user-settings\\CoursesInfo.vue' /* webpackChunkName: "components/user-settings-courses-info" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsIllustrationDialog: () => import('../..\\components\\user-settings\\IllustrationDialog.vue' /* webpackChunkName: "components/user-settings-illustration-dialog" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsLanguagesInfo: () => import('../..\\components\\user-settings\\LanguagesInfo.vue' /* webpackChunkName: "components/user-settings-languages-info" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsLearningPreferencesInfo: () => import('../..\\components\\user-settings\\LearningPreferencesInfo.vue' /* webpackChunkName: "components/user-settings-learning-preferences-info" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsLessonPrice: () => import('../..\\components\\user-settings\\LessonPrice.vue' /* webpackChunkName: "components/user-settings-lesson-price" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsPerLessonPrice: () => import('../..\\components\\user-settings\\PerLessonPrice.vue' /* webpackChunkName: "components/user-settings-per-lesson-price" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsPricingTableInfo: () => import('../..\\components\\user-settings\\PricingTableInfo.vue' /* webpackChunkName: "components/user-settings-pricing-table-info" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsQualificationSuccessDialog: () => import('../..\\components\\user-settings\\QualificationSuccessDialog.vue' /* webpackChunkName: "components/user-settings-qualification-success-dialog" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsReceiptInfo: () => import('../..\\components\\user-settings\\ReceiptInfo.vue' /* webpackChunkName: "components/user-settings-receipt-info" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsSpecialityDialog: () => import('../..\\components\\user-settings\\SpecialityDialog.vue' /* webpackChunkName: "components/user-settings-speciality-dialog" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsSummaryInfo: () => import('../..\\components\\user-settings\\SummaryInfo.vue' /* webpackChunkName: "components/user-settings-summary-info" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsTeachingPreferencesInfo: () => import('../..\\components\\user-settings\\TeachingPreferencesInfo.vue' /* webpackChunkName: "components/user-settings-teaching-preferences-info" */).then(c => wrapFunctional(c.default || c)),
  UserSettingsTeachingQualificationsInfo: () => import('../..\\components\\user-settings\\TeachingQualificationsInfo.vue' /* webpackChunkName: "components/user-settings-teaching-qualifications-info" */).then(c => wrapFunctional(c.default || c)),
  UserSettingAutocomplete: () => import('../..\\components\\user-settings\\UserSettingAutocomplete.vue' /* webpackChunkName: "components/user-setting-autocomplete" */).then(c => wrapFunctional(c.default || c)),
  UserSettingSelect: () => import('../..\\components\\user-settings\\UserSettingSelect.vue' /* webpackChunkName: "components/user-setting-select" */).then(c => wrapFunctional(c.default || c)),
  UserSettingTemplate: () => import('../..\\components\\user-settings\\UserSettingTemplate.vue' /* webpackChunkName: "components/user-setting-template" */).then(c => wrapFunctional(c.default || c)),
  BusinessPageIconsDotsIcon: () => import('../..\\components\\business-page\\icons\\DotsIcon.vue' /* webpackChunkName: "components/business-page-icons-dots-icon" */).then(c => wrapFunctional(c.default || c)),
  ClassroomVideoTokbox: () => import('../..\\components\\classroom\\video\\Tokbox.vue' /* webpackChunkName: "components/classroom-video-tokbox" */).then(c => wrapFunctional(c.default || c)),
  ClassroomVideoTwilio: () => import('../..\\components\\classroom\\video\\Twilio.vue' /* webpackChunkName: "components/classroom-video-twilio" */).then(c => wrapFunctional(c.default || c)),
  ClassroomVideoActions: () => import('../..\\components\\classroom\\video\\VideoActions.vue' /* webpackChunkName: "components/classroom-video-actions" */).then(c => wrapFunctional(c.default || c)),
  ClassroomVideoWhereby: () => import('../..\\components\\classroom\\video\\Whereby.vue' /* webpackChunkName: "components/classroom-video-whereby" */).then(c => wrapFunctional(c.default || c)),
  ClassroomVueDraggableResizable: () => import('../..\\components\\classroom\\vue-draggable-resizable\\VueDraggableResizable.vue' /* webpackChunkName: "components/classroom-vue-draggable-resizable" */).then(c => wrapFunctional(c.default || c))
}

for (const name in components) {
  Vue.component(name, components[name])
  Vue.component('Lazy' + name, components[name])
}
