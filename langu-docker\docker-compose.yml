version: "3.4"

services:

  webserver:
    build: nginx
    container_name: langu-webserver
    restart: always
    ports:
      - ${NGINX_LISTEN_PORT}:80
      - ${NGINX_LISTEN_PORT2}:443
    depends_on:
      - php
    command: "/bin/sh -c 'while :; do sleep 6h & wait $${!}; nginx -s reload; done & nginx -g \"daemon off;\"'"
    volumes:
#      - ../langu:/var/www/langu
#      - ../langu:/var/www/langu
      - ../langu-new:/var/www/langu
      - ../langu/web/uploads:/var/www/langu/public/uploads
    env_file:
      - .env
    tty: true
    environment:
      - DOMAIN_NAME=${DOMAIN_NAME}
      - DOMAINS_NAME=${DOMAINS_NAME}
      - DOMAINS_NAME_FOR_CERTBOT=${DOMAINS_NAME_FOR_CERTBOT}
      - ADMIN_DOMAINS_NAME_FOR_CERTBOT=${ADMIN_DOMAINS_NAME_FOR_CERTBOT}
      - ADMIN_DOMAINS_NAME=${ADMIN_DOMAINS_NAME}
      - NODE_PORT=${NODE_PORT}
      - EMAIL_FOR_CERTBOT=${EMAIL_FOR_CERTBOT}
      - APP_ENV=${APP_ENV}

  # mysql:
  #   image: mariadb:${MARIA_DB_VERSION}
  #   restart: always
  #   container_name: langu-mysql
  #   command: mysqld --character-set-server=${CHARSET_DB} --collation-server=${COLLATION_DB}
  #   working_dir: /application
  #   volumes:
  #     - .:/application
  #     - ../langu-docker/mysql/init:/docker-entrypoint-initdb.d
  #   environment:
  #     - MYSQL_ROOT_PASSWORD=${DATABASE_PASSWORD}
  #     - MYSQL_USER=${DATABASE_USER}
  #     - MYSQL_PASSWORD=${DATABASE_PASSWORD}
  #     - MYSQL_HOST=${DATABASE_HOST}
  #   ports:
  #     - ${DATABASE_PORT}:3306

  # php:
  #   build:
  #     context: php-new
  #     args:
  #       - USERID
  #       - GROUPID
  #   container_name: langu-php
  #   restart: always
  #   working_dir: /var/www/langu
  #   links:
  #     - mysql:mariadb
  #   volumes:
  #     - ../langu-new:/var/www/langu
  #     - ../langu/web/uploads:/var/www/langu/public/uploads
  #   env_file:
  #     - .env
  #   tty: true
  #   user: www-data
  #   environment:
  #     - COMPOSER_MEMORY_LIMIT=-1


  redis:
    image: redis:alpine
    container_name: langu-redis
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD}

  node:
    build: node
    working_dir: /var/www/l2node
    restart: always
    container_name: langu-node
    environment:
      - NODE_PORT=${NODE_PORT}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    command: tail -f /dev/null
    volumes:
      - ../Langu2-node:/var/www/l2node


  # frontend:
  #   restart: always
  #   image: node:14
  #   user: node
  #   working_dir: /var/www
  #   environment:
  #     NODE_ENV: production
  #     HOST: 0.0.0.0
  #   expose:
  #     - 3000
  #   command: bash -c "npm run start"
  #   volumes:
  #     - ../langu-frontend:/var/www:cached

  # libreoffice:
  #   build: libreoffice
  #   working_dir: /var/www/libreoffice
  #   container_name: langu-libreoffice
  #   volumes:
  #     - ../libreoffice:/var/www/libreoffice
  #   environment:
  #     - PUID=1000
  #     - PGID=1000
  #     - TZ=Europe/London
  #   #    ports:
  #   #      - "6040:6040"
  #   restart: always
