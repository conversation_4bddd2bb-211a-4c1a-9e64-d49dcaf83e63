(window.webpackJsonp=window.webpackJsonp||[]).push([[175],{1440:function(e,t,r){"use strict";r.r(t);r(31);var n={name:"UserSettingSelect",props:{value:{type:Object,required:!0},items:{type:Array,required:!0},attachId:{type:String,required:!0},itemValue:{type:String,default:"id"},itemText:{type:String,default:"name"},hideDetails:{type:Boolean,default:!0},hideSelected:{type:Boolean,default:!0},rules:{type:Array,default:function(){return[]}},validateOnBlur:{type:Boolean,default:!1},maxHeight:{type:Number,default:162},placeholder:[Boolean,String]},data:function(){return{chevronIcon:"".concat(r(91),"#chevron-down")}},computed:{_placeholder:function(){return this.placeholder?this.$t(this.placeholder):""}}},o=(r(1519),r(22)),l=r(42),c=r.n(l),d=r(261),m=r(1610),component=Object(o.a)(n,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"user-setting-select",attrs:{id:e.attachId}},[n("v-select",e._g({attrs:{value:e.value,items:e.items,height:"44","full-width":"",outlined:"",dense:"","hide-selected":e.hideSelected,"hide-details":e.hideDetails,"return-object":"",rules:e.rules,placeholder:e._placeholder,"item-value":e.itemValue,"item-text":e.itemText,attach:"#"+e.attachId,"validate-on-blur":e.validateOnBlur,"menu-props":{bottom:!0,offsetY:!0,minWidth:200,maxHeight:e.maxHeight,nudgeBottom:8,contentClass:"select-list l-scroll"}},scopedSlots:e._u([{key:"append",fn:function(){return[n("svg",{attrs:{width:"12",height:"12",viewBox:"0 0 12 12"}},[n("use",{attrs:{"xlink:href":e.chevronIcon}})])]},proxy:!0},{key:"item",fn:function(t){var o=t.item;return[o.isoCode?n("div",{staticClass:"icon"},[n("v-img",{attrs:{src:r(369)("./"+o.isoCode+".svg"),height:"24",width:"24",eager:""}})],1):e._e(),e._v(" "),n("div",{staticClass:"text",style:{color:"all"===o.id?"#888":"inherit"}},[e._v("\n        "+e._s(o[e.itemText])+"\n      ")])]}}])},e.$listeners))],1)}),[],!1,null,"322ff0d6",null);t.default=component.exports;c()(component,{VImg:d.a,VSelect:m.a})},1448:function(e,t,r){var content=r(1520);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(19).default)("5bc00c5d",content,!0,{sourceMap:!1})},1458:function(e,t,r){"use strict";r.r(t);r(9),r(24),r(38),r(39);var n={name:"UserSettingAutocomplete",props:{value:{type:Object,default:function(){return{}}},items:{type:Array,required:!0},selectedItems:{type:Array,default:function(){return[]}},attachId:{type:String,required:!0},itemText:{type:String,default:"name"},rules:{type:Array,default:function(){return[]}},hideDetails:{type:Boolean,default:!0},placeholder:[Boolean,String]},data:function(){return{key:1,chevronIcon:"".concat(r(91),"#chevron-down")}},computed:{_placeholder:function(){return this.placeholder?this.$t(this.placeholder||"choose_language"):""},_items:function(){var e=this;return this.selectedItems.length?this.items.filter((function(t){var r,n;return!(null!==(r=null===(n=e.selectedItems)||void 0===n?void 0:n.map((function(e){return e.id})))&&void 0!==r?r:[]).includes(t.id)})):this.items}},methods:{clearSelection:function(){var e=this;this.$nextTick((function(){var t,r,input=null===(t=e.$refs.autocomplete)||void 0===t||null===(r=t.$el)||void 0===r?void 0:r.querySelector("input");input&&(input.setSelectionRange(0,0),input.blur())}))}}},o=(r(1549),r(22)),l=r(42),c=r.n(l),d=r(1612),m=r(261),component=Object(o.a)(n,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{key:e.key,staticClass:"user-setting-autocomplete",attrs:{id:e.attachId}},[n("v-autocomplete",e._g({ref:"autocomplete",attrs:{value:e.value,items:e._items,dense:"",filled:"",outlined:"","hide-selected":"","hide-no-data":"","return-object":"","hide-details":e.hideDetails,rules:e.rules,"item-text":e.itemText,placeholder:e._placeholder,attach:"#"+e.attachId,"menu-props":{bottom:!0,offsetY:!0,nudgeBottom:8,contentClass:"select-list l-scroll",maxHeight:205}},on:{focus:e.clearSelection,change:function(t){e.key++}},scopedSlots:e._u([{key:"append",fn:function(){return[n("svg",{attrs:{width:"12",height:"12",viewBox:"0 0 12 12"}},[n("use",{attrs:{"xlink:href":e.chevronIcon}})])]},proxy:!0},{key:"item",fn:function(t){var o=t.item;return[o.isoCode?n("div",{staticClass:"icon"},[n("v-img",{attrs:{src:r(369)("./"+o.isoCode+".svg"),height:"24",width:"24",eager:""}})],1):e._e(),e._v(" "),n("div",{staticClass:"text"},[e._v(e._s(o[e.itemText]))])]}}])},e.$listeners))],1)}),[],!1,null,"53fdd87c",null);t.default=component.exports;c()(component,{VAutocomplete:d.a,VImg:m.a})},1476:function(e,t,r){var content=r(1550);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(19).default)("61200eaa",content,!0,{sourceMap:!1})},1519:function(e,t,r){"use strict";r(1448)},1520:function(e,t,r){var n=r(18)(!1);n.push([e.i,".user-setting-select[data-v-322ff0d6]{position:relative}",""]),e.exports=n},1549:function(e,t,r){"use strict";r(1476)},1550:function(e,t,r){var n=r(18)(!1);n.push([e.i,".user-setting-autocomplete[data-v-53fdd87c]{position:relative}",""]),e.exports=n},1848:function(e,t,r){var content=r(2050);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(19).default)("11da3ed2",content,!0,{sourceMap:!1})},2049:function(e,t,r){"use strict";r(1848)},2050:function(e,t,r){var n=r(18)(!1);n.push([e.i,'.user-register{margin-top:10px}.user-register .row{margin:0 -14px!important}.user-register .col{padding:0 14px!important}.user-register-wrap{padding:44px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1);border-radius:20px}@media only screen and (min-width:992px){.user-register-wrap{max-width:820px;margin:0 auto}}@media only screen and (max-width:991px){.user-register-wrap{margin:0 15px}}@media only screen and (max-width:767px){.user-register-wrap{margin:0 5px;padding:25px 15px 35px}}.user-register-title{font-size:24px;line-height:1.333}@media only screen and (max-width:479px){.user-register-title{font-size:20px}}.user-register-content .checkbox-label{padding-top:2px}.user-register-content .l-checkbox.v-input--is-label-active .v-label{color:var(--v-darkLight-base)!important}.user-register-content .l-checkbox .v-input--selection-controls__input{margin-top:5px}.user-register-content .time-zone-list{width:calc(100% + 16px)}@media only screen and (max-width:991px){.user-register-content .time-zone-list{width:calc(100% + 8px)}}.user-register-content .time-zone-list .item{position:relative;border-radius:16px}.user-register-content .time-zone-list .item:not(.selected){background:linear-gradient(126.15deg,rgba(128,182,34,.08),rgba(60,135,248,.08) 102.93%);cursor:pointer}.user-register-content .time-zone-list .item>div{height:50px;padding:4px 16px;border-radius:inherit}@media only screen and (max-width:991px){.user-register-content .time-zone-list .item>div{height:42px}}@media only screen and (max-width:767px){.user-register-content .time-zone-list .item>div{padding:4px 10px;font-size:12px!important}}.user-register-content .time-zone-list .item.selected,.user-register-content .time-zone-list .item:hover{position:relative;height:50px}@media only screen and (max-width:991px){.user-register-content .time-zone-list .item.selected,.user-register-content .time-zone-list .item:hover{height:42px}}.user-register-content .time-zone-list .item.selected>div,.user-register-content .time-zone-list .item:hover>div{background:linear-gradient(126.15deg,rgba(128,182,34,.28),rgba(60,135,248,.28) 102.93%)}.user-register-content .time-zone-list .item.selected{top:-1px}.user-register-content .time-zone-list .item.selected>div{height:inherit;margin-top:1px}.user-register-content .time-zone-list .item.selected:before{display:block!important;content:"";position:absolute;top:0;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:20px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}@media only screen and (min-width:768px){.user-register-content .alert-wrap{height:44px}}.user-register-footer{margin-top:90px}@media only screen and (max-width:991px){.user-register-footer{margin-top:60px}}@media only screen and (max-width:767px){.user-register-footer{margin-top:30px}}.user-register-footer .v-btn:not(.v-btn--icon).v-size--default{min-width:180px!important}@media only screen and (max-width:479px){.user-register-footer .v-btn:not(.v-btn--icon).v-size--default{width:100%!important}}.user-register button[type=submit] svg{transform:rotate(-90deg)}.user-setting-select[data-selected=all-languages] .v-select__selections .v-select__selection--comma{color:var(--v-greyLight-base)!important}.user-setting-autocomplete .v-autocomplete,.user-setting-autocomplete .v-input__slot,.user-setting-autocomplete .v-select__slot{cursor:pointer!important}.user-setting-autocomplete .v-list-item--active{background-color:transparent!important;color:inherit!important}.user-setting-autocomplete .v-list-item:hover{background-color:rgba(0,0,0,.04)!important}.user-setting-autocomplete .v-list-item--highlighted{background-color:transparent!important}',""]),e.exports=n},2209:function(e,t,r){"use strict";r.r(t);r(7),r(8),r(14),r(6),r(15);var n=r(2),o=r(28),l=r(10),c=(r(9),r(71),r(40),r(23),r(39),r(35),r(60),r(62),r(859)),d=r(370),m=r(1440),h=r(1458),f=r(702);function v(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}function _(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?v(Object(source),!0).forEach((function(t){Object(n.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):v(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var x=function(e){return/.+@.+\..+/.test(e)},y=function(e){return!!e&&e.length>=5&&e.length<=40},w=function(e){return!!e&&/^[a-zA-Z0-9.-]*$/.test(e)},k={name:"UserRegisterPage",components:{TextInput:d.default,UserSettingSelect:m.default,UserSettingAutocomplete:h.default},middleware:function(e){var t=e.store,r=e.redirect;if(t.getters["user/isUserLogged"])return r("/")},asyncData:function(e){return Object(l.a)(regeneratorRuntime.mark((function t(){var r;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.store,t.next=3,r.dispatch("user/getRegistrationPageItem");case 3:case"end":return t.stop()}}),t)})))()},data:function(){var e=this;return{termsLinkEl:null,valid:!0,isAgree:!1,isEmailExist:!1,isUsernameExist:!1,languagesToLearn:[{id:"all",name:this.$t("all_languages")}],referralSources:[{id:"google search",name:this.$t("google_search")},{id:"google ad",name:this.$t("google_ad")},{id:"instagram",name:this.$t("instagram")},{id:"facebook group",name:this.$t("facebook_group")},{id:"facebook ad",name:this.$t("facebook_ad")},{id:"olx",name:this.$t("olx")},{id:"ebay kleinanzeigen",name:this.$t("ebay_kleinanzeigen")},{id:"recommendation_from_teacher",name:this.$t("recommendation_from_teacher")},{id:"recommendation_from_friend_family",name:this.$t("recommendation_from_friend_family")},{id:"provided_by_employer",name:this.$t("provided_by_employer")},{id:"chatgpt",name:this.$t("chatgpt")}],prepareTimeZones:null,registrationByGoogle:!1,rules:{usernameLength:function(t){return y(t)||e.$t("username_length")},usernameChar:function(t){return w(t)||e.$t("username_is_invalid")},required:function(t){return!!t&&t.length>0||e.$t("this_value_should_not_be_left_blank")},selectRequired:function(t){return!(null==t||!t.id)||e.$t("this_value_should_not_be_left_blank")},agree:function(t){return!!t||e.$t("please_check_box_if_you_want_to_proceed")},email:function(t){return x(t)||e.$t("email_must_be_valid")},isEmailExist:function(){return!e.isEmailExist||e.$t("email_is_already_exist")},isUsernameExist:function(){return!e.isUsernameExist||e.$t("username_is_already_exist")}}}},head:function(){return{title:this.$t("user_register_page.seo_title"),meta:[{hid:"description",name:"description",content:this.$t("user_register_page.seo_description")},{hid:"og:title",name:"og:title",property:"og:title",content:this.$t("user_register_page.seo_title")},{property:"og:description",content:this.$t("user_register_page.seo_description")}],bodyAttrs:{class:"".concat(this.locale," user-register-page")}}},computed:{pageItem:function(){return this.$store.state.user.registrationPageItem},locale:function(){return this.$i18n.locale},currencies:function(){var e=this;return this.pageItem.currencies.filter((function(t){return t["isAvailableFor".concat(1===e.userType?"Student":"Teacher")]}))},currency:function(){return this.$store.state.currency.item},item:function(){return this.$store.state.user.newUserItem},userType:function(){return this.item.userType},selectedTimeZone:function(){var e,t=this;return(null===(e=this.pageItem)||void 0===e?void 0:e.timezones.find((function(e){return e.name===t.item.timezone})))||{}},selectedLanguageToLearn:function(){var e=this;return this.item.languageToLearn&&this.languagesToLearn.find((function(t){return t.id===e.item.languageToLearn}))||null},selectedTaxCountry:function(){var e,t=this;return(null===(e=this.pageItem)||void 0===e?void 0:e.taxCountries.find((function(e){return e.id===t.item.taxCountry})))||{}},termsLink:function(){return"/terms/".concat(1===this.userType?"student":"teacher")},role:function(){return this.$t(1===this.userType?"student":"teacher").toLowerCase()},userTypes:function(){return this.pageItem.userType},selectedReferralSource:function(){var e=this;return this.referralSources.find((function(t){return t.id===e.item.referralSource}))||{}}},watch:{isUsernameExist:function(){this.validate()},isEmailExist:function(){this.validate()}},mounted:function(){var e,t,r,n,l=this,c=this.$store.state.locale,d=this.$dayjs.tz.guess();this.languagesToLearn=[].concat(Object(o.a)(this.languagesToLearn),Object(o.a)(this.pageItem.languagesToLearn)),this.prepareTimeZones=null===(e=this.pageItem)||void 0===e?void 0:e.timezones.filter((function(e){return l.$dayjs().tz(e.name).utcOffset()===l.$dayjs().utcOffset()})).map((function(e){return _(_({},e),{},{tz:e.gmt.split(" ")[0]})}));var m,h,f,v,x,y,w,k,C,$=this.prepareTimeZones.find((function(e){return e.name===d})),E={locale:c,uiLanguage:c,currency:this.currency.isoCode,timezone:null!==(t=null==$?void 0:$.name)&&void 0!==t?t:null};null!==(r=this.pageItem)&&void 0!==r&&null!==(n=r.googleData)&&void 0!==n&&n.email&&(this.registrationByGoogle=!0,E=_(_({},E),{},{email:null!==(m=null===(h=this.pageItem)||void 0===h||null===(f=h.googleData)||void 0===f?void 0:f.email)&&void 0!==m?m:null,firstName:null!==(v=null===(x=this.pageItem)||void 0===x||null===(y=x.googleData)||void 0===y?void 0:y.firstName)&&void 0!==v?v:null,lastName:null!==(w=null===(k=this.pageItem)||void 0===k||null===(C=k.googleData)||void 0===C?void 0:C.lastName)&&void 0!==w?w:null}));this.$store.commit("user/UPDATE_NEW_USER_ITEM",E),this.$nextTick((function(){l.termsLinkEl=document.getElementById("terms-link"),l.termsLinkEl&&l.termsLinkEl.addEventListener("click",(function(){window.open(l.termsLink,"_blank")}),null)}))},beforeDestroy:function(){this.isEmailExist=!1,this.isUsernameExist=!1,this.$store.commit("user/RESET_NEW_USER_ITEM")},methods:{updateUsername:function(e){y(e)&&w(e)&&this.checkUsername(e),this.updateValue(e,"username")},checkUsername:Object(c.debounce)((function(e){var t=this;this.$store.dispatch("user/checkUsername",e).then((function(data){t.isUsernameExist=!data.isValid}))}),800),updateEmail:function(e){x(e)&&this.checkEmail(e),this.updateValue(e,"email")},checkEmail:Object(c.debounce)((function(e){var t=this;this.$store.dispatch("user/checkEmail",e).then((function(data){t.isEmailExist=!data.isValid}))}),800),updateValue:function(e,t){if("referralSource"===t){var r=this.referralSources.find((function(source){return source.id===e}));this.$store.commit("user/UPDATE_NEW_USER_ITEM",Object(n.a)({},t,r?r.id:e))}else this.$store.commit("user/UPDATE_NEW_USER_ITEM",Object(n.a)({},t,e))},getUserTypeLabels:function(e){return"student"===e.toLowerCase()?this.$t("student"):"teacher"===e.toLowerCase()?this.$t("teacher_capitalize"):"Default"},submit:function(){var e=this;this.$store.dispatch("user/registration",this.item).then(Object(l.a)(regeneratorRuntime.mark((function t(){var path,r,n,o;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(path="/teacher-listing?checkEmail=1",r=e.$store.getters["user/redirectUrl"],n=2===e.userType,o=1===e.userType,!e.registrationByGoogle){t.next=7;break}return t.next=7,e.$store.dispatch("user/getUserStatus").then((function(){path=e.$store.getters["user/isStudent"]&&!e.$store.getters["user/registrationConfirmed"]?"/teacher-listing/welcome":"/user/lessons"}));case 7:if(!n||e.registrationByGoogle){t.next=10;break}return e.$router.push({path:"/teacher-listing?checkEmail=1"}),t.abrupt("return");case 10:if(!o||e.registrationByGoogle){t.next=13;break}return e.$router.push({path:"/teacher-listing?checkEmail=1"}),t.abrupt("return");case 13:if(!Object(f.a)(e.$router)){t.next=16;break}return t.abrupt("return");case 16:r?(e.$router.push(r),e.$store.dispatch("user/clearRedirectUrl")):e.$router.push({path:path});case 17:case"end":return t.stop()}}),t)})))).catch((function(t){var r=null;t.response&&(403===t.response.status&&(r={errorMessage:"username_is_already_exist"},e.isUsernameExist=!0),404===t.response.status&&(r={errorMessage:"email_is_already_exist"},e.isEmailExist=!0),e.validate()),e.$store.dispatch("snackbar/error",r),e.$vuetify.goTo(0,{duration:300,offset:0,easing:"linear"})}))},validate:function(){this.$refs.form.validate()}}},C=(r(2049),r(22)),$=r(42),E=r.n($),T=r(2190),S=r(1327),z=r(1611),I=r(1360),O=r(1370),L=r(1363),U=r(2192),j=r(2193),V=r(1361),component=Object(C.a)(k,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-col",{staticClass:"col-12 px-0"},[n("div",{staticClass:"user-register"},[n("v-form",{ref:"form",on:{submit:function(t){return t.preventDefault(),e.submit.apply(null,arguments)}},model:{value:e.valid,callback:function(t){e.valid=t},expression:"valid"}},[n("v-container",{staticClass:"pa-0",attrs:{fluid:""}},[n("v-row",{attrs:{"no-gutters":""}},[n("v-col",{staticClass:"col-12"},[n("div",{staticClass:"user-register-wrap"},[n("div",{staticClass:"user-register-title mb-3 mb-md-5"},[e._v("\n                "+e._s(e.$t("welcome_to_langu"))+" 👋\n              ")]),e._v(" "),n("div",{staticClass:"user-register-content"},[n("v-row",[n("v-col",{staticClass:"col-12"},[n("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[e._v("\n                      "+e._s(e.$t("are_you_student_or_teacher"))+"\n                    ")]),e._v(" "),n("div",[n("v-radio-group",{staticClass:"mt-0 pt-0 mb-1",attrs:{value:e.userType,"hide-details":""},on:{change:function(t){return e.updateValue(t,"userType")}}},[n("div",{staticClass:"d-flex flex-wrap mb-2 mb-md-4"},e._l(e.pageItem.userTypes,(function(t,r){return n("div",{key:r,class:["radiobutton",{"mr-5":0===r}]},[n("v-radio",{staticClass:"d-flex align-center l-radio-button",attrs:{color:"success",ripple:!1,value:t.type},scopedSlots:e._u([{key:"label",fn:function(){return[e._v("\n                                "+e._s(e.getUserTypeLabels(t.typeName))+"\n                              ")]},proxy:!0}],null,!0)})],1)})),0)])],1)])],1),e._v(" "),2===e.userType?n("v-row",[n("v-col",{staticClass:"col-12"},[n("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[e._v("\n                      "+e._s(e.$t("choose_your_username"))+"\n                    ")])]),e._v(" "),n("v-col",{staticClass:"col-12 col-sm-6 mb-1 order-2 order-sm-1"},[n("div",{staticClass:"input-wrap-label"},[e._v("\n                      "+e._s(e.$t("this_will_be_visible_in_link_to_your_profile"))+"\n                    ")]),e._v(" "),n("text-input",{attrs:{value:e.item.username,"type-class":"border-gradient",height:"44",rules:[e.rules.usernameLength,e.rules.usernameChar,e.rules.isUsernameExist]},on:{input:e.updateUsername}})],1),e._v(" "),n("v-col",{staticClass:"col-12 col-sm-6 d-flex align-end mb-1 mb-sm-3 order-1 order-sm-2"},[e.item.username?e._e():n("div",{staticClass:"alert-wrap d-flex align-center pb-sm-3"},[n("v-alert",{staticClass:"mb-0",attrs:{text:"",type:"error"},scopedSlots:e._u([{key:"prepend",fn:function(){return[n("div",{staticClass:"d-flex align-center mr-1"},[n("svg",{attrs:{width:"18",height:"18",viewBox:"0 0 12 12"}},[n("use",{attrs:{"xlink:href":r(91)+"#attention"}})])])]},proxy:!0}],null,!1,2560067326)},[e._v("\n                        "+e._s(e.$t("this_value_should_not_be_left_blank"))+"\n                      ")])],1)])],1):e._e(),e._v(" "),n("v-row",[n("v-col",{staticClass:"col-12"},[n("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[e._v("\n                      "+e._s(e.$t("your_name"))+"\n                    ")])]),e._v(" "),n("v-col",{staticClass:"col-12 col-sm-6 mb-sm-1"},[n("text-input",{attrs:{value:e.item.firstName,"type-class":"border-gradient",height:"44",rules:[e.rules.required],placeholder:e.$t("first_name"),autocomplete:"given-name"},on:{input:function(t){return e.updateValue(t,"firstName")}}})],1),e._v(" "),n("v-col",{staticClass:"col-12 col-sm-6 mb-1"},[n("text-input",{attrs:{value:e.item.lastName,"type-class":"border-gradient",height:"44",rules:[e.rules.required],placeholder:e.$t("last_name"),autocomplete:"family-name"},on:{input:function(t){return e.updateValue(t,"lastName")}}})],1)],1),e._v(" "),n("v-row",[n("v-col",{staticClass:"col-12"},[n("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[e._v("\n                      "+e._s(e.$t("email_address"))+"\n                    ")])]),e._v(" "),n("v-col",{staticClass:"col-12 col-sm-6 mb-1"},[n("text-input",{attrs:{value:e.item.email,"type-class":"border-gradient",height:"44",rules:[e.rules.required,e.rules.email,e.rules.isEmailExist],disabled:e.registrationByGoogle,placeholder:e.$t("enter_your_email"),autocomplete:"email"},on:{input:e.updateEmail}})],1)],1),e._v(" "),n("v-row",[n("v-col",{staticClass:"col-12"},[n("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[e._v("\n                      "+e._s(e.$t("your_currency"))+"\n                    ")]),e._v(" "),n("div",[n("v-radio-group",{staticClass:"mt-0 pt-0",attrs:{value:e.item.currency,"hide-details":""},on:{change:function(t){return e.updateValue(t,"currency")}}},[n("div",{staticClass:"d-flex flex-wrap mb-3 mb-md-4"},e._l(e.currencies,(function(t,r){return n("div",{key:r,staticClass:"radiobutton mb-1 mr-3"},[n("v-radio",{staticClass:"d-flex align-center l-radio-button",attrs:{color:"success",ripple:!1,value:t.isoCode},scopedSlots:e._u([{key:"label",fn:function(){return[e._v("\n                                "+e._s(t.isoCode)+"\n                              ")]},proxy:!0}],null,!0)})],1)})),0)])],1)])],1),e._v(" "),n("v-row",[n("v-col",{staticClass:"col-12"},[n("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[e._v("\n                      "+e._s(e.$t("your_time_zone"))+"\n                    ")])]),e._v(" "),n("v-col",{staticClass:"col-12 col-sm-6"},[n("div",{staticClass:"input-wrap-label"},[e._v("\n                      "+e._s(e.$t("select_your_current_local_time_dates_and_times_throughout_this_site_will_be_displayed_using_this_time_zone"))+"\n                    ")]),e._v(" "),n("user-setting-autocomplete",{attrs:{value:e.selectedTimeZone,items:e.pageItem.timezones,"item-text":"gmt","attach-id":"time-zone","hide-details":!1,placeholder:"choose_time_zone"},on:{change:function(t){return e.updateValue(t.name,"timezone")}}})],1),e._v(" "),e.prepareTimeZones?n("v-col",{staticClass:"col-12 mb-2 mb-md-3"},[n("div",{staticClass:"time-zone-list d-flex flex-wrap"},e._l(e.prepareTimeZones,(function(t){return n("div",{key:t.id,class:["item unselected mr-1 mr-md-2 mb-1 mb-md-2",{selected:e.item.timezone===t.name}],on:{click:function(r){return r.stopPropagation(),r.preventDefault(),e.updateValue(t.name,"timezone")}}},[n("div",{staticClass:"d-flex align-center body-2"},[n("span",{staticClass:"font-weight-medium"},[e._v(e._s(t.tz))]),e._v(" \n                          "+e._s(t.name)+"\n                        ")])])})),0)]):e._e()],1),e._v(" "),2===e.userType?n("v-row",[n("v-col",{staticClass:"col-12"},[n("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[e._v("\n                      "+e._s(e.$t("in_which_country_do_you_normally_pay_taxes"))+"\n                    ")])]),e._v(" "),n("v-col",{staticClass:"col-12 col-sm-6 mb-1 mb-md-3"},[n("user-setting-select",{attrs:{value:e.selectedTaxCountry,items:e.pageItem.taxCountries,"attach-id":"country-pay-taxes","hide-details":!1,rules:[e.rules.selectRequired],"validate-on-blur":"",placeholder:"choose_country"},on:{change:function(t){return e.updateValue(t.id,"taxCountry")}}})],1)],1):n("v-row",[n("v-col",{staticClass:"col-12"},[n("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[e._v("\n                      "+e._s(e.$t("what_language_would_you_like_to_learn_on_langu"))+"\n                    ")])]),e._v(" "),n("v-col",{staticClass:"col-12 col-sm-6 mb-1"},[n("user-setting-select",{attrs:{value:e.selectedLanguageToLearn,items:e.languagesToLearn,"attach-id":"learn-language","hide-details":!1,placeholder:e.selectedLanguageToLearn?"":"all_languages","max-height":250},on:{change:function(t){return e.updateValue(t.id,"languageToLearn")}}})],1)],1),e._v(" "),1===e.userType?n("v-row",[n("v-col",{staticClass:"col-12"},[n("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[e._v("\n                      "+e._s(e.$t("how_did_you_find_out_about_langu"))+"\n                      "),n("span",{staticStyle:{"font-size":"14px"}},[e._v("("+e._s(e.$t("optional"))+")")])])]),e._v(" "),n("v-col",{staticClass:"col-12 col-sm-6 mb-1 mb-md-3"},[n("user-setting-select",{attrs:{value:e.selectedReferralSource,items:e.referralSources,"attach-id":"referral-source","hide-details":!1,placeholder:"choose_referral_source","max-height":250},on:{change:function(t){return e.updateValue(t.id,"referralSource")}}})],1)],1):e._e(),e._v(" "),n("v-row",[n("v-col",[n("v-checkbox",{staticClass:"l-checkbox caption",attrs:{value:e.isAgree,ripple:!1,rules:[e.rules.agree]},on:{change:function(t){e.isAgree=t}},scopedSlots:e._u([{key:"label",fn:function(){return[n("span",{staticClass:"checkbox-label body-2",domProps:{innerHTML:e._s(e.$t("i_agree_to_role_terms_and_conditions",{role:e.role,link:e.termsLink}))}})]},proxy:!0}])})],1)],1)],1),e._v(" "),n("div",{staticClass:"user-register-footer"},[n("v-row",[n("v-col",{staticClass:"col-12"},[n("div",{staticClass:"d-flex justify-center justify-sm-end"},[n("v-btn",{staticClass:"font-weight-medium",attrs:{color:"primary",type:"submit",disabled:!e.valid}},[e._v("\n                        "+e._s(e.$t("submit"))+"\n                        "),n("svg",{staticClass:"ml-1",attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[n("use",{attrs:{"xlink:href":r(91)+"#save-icon"}})])])],1)])],1)],1)])])],1)],1)],1)],1)])}),[],!1,null,null,null);t.default=component.exports;E()(component,{UserSettingAutocomplete:r(1458).default,UserSettingSelect:r(1440).default}),E()(component,{VAlert:T.a,VBtn:S.a,VCheckbox:z.a,VCol:I.a,VContainer:O.a,VForm:L.a,VRadio:U.a,VRadioGroup:j.a,VRow:V.a})}}]);