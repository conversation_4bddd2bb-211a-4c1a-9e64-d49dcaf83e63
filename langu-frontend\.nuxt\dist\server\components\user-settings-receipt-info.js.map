{"version": 3, "file": "components/user-settings-receipt-info.js", "sources": ["webpack:///./components/user-settings/ReceiptInfo.vue?c332", "webpack:///./components/user-settings/ReceiptInfo.vue?c03c", "webpack:///./components/user-settings/ReceiptInfo.vue?77b8", "webpack:///./components/user-settings/ReceiptInfo.vue?28ea", "webpack:///./components/user-settings/ReceiptInfo.vue", "webpack:///./components/user-settings/ReceiptInfo.vue?5877", "webpack:///./components/user-settings/ReceiptInfo.vue?61d0", "webpack:///./components/user-settings/UserSettingTemplate.vue?b3b0", "webpack:///./components/user-settings/UserSettingTemplate.vue", "webpack:///./components/user-settings/UserSettingTemplate.vue?fdf8", "webpack:///./components/user-settings/UserSettingTemplate.vue?54d6", "webpack:///./components/user-settings/UserSettingTemplate.vue?9dd4", "webpack:///./components/user-settings/UserSettingTemplate.vue?a8f9", "webpack:///./components/user-settings/UserSettingTemplate.vue?0b4b"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReceiptInfo.vue?vue&type=style&index=0&id=af7ad3a4&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"09966082\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReceiptInfo.vue?vue&type=style&index=0&id=af7ad3a4&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".checkbox[data-v-af7ad3a4]:not(:last-child){margin-bottom:20px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('user-setting-template',{attrs:{\"title\":_vm.$t('receipt_information'),\"submit-func\":_vm.submitData}},[_c('v-row',[_c('v-col',{staticClass:\"col-12\"},[_c('div',{staticClass:\"input-wrap-title body-1 font-weight-medium mb-2\"},[_vm._v(\"\\n        \"+_vm._s(_vm.$t(\n            'provide_purchaser_information_you_would_like_to_appear_in_receipt'\n          ))+\":\\n      \")])]),_vm._v(\" \"),_c('v-col',{staticClass:\"col-12 col-md-10\"},[_c('div',{staticClass:\"input-wrap\"},[_c('div',[_c('v-textarea',{staticClass:\"l-textarea\",attrs:{\"value\":_vm.itemText,\"no-resize\":\"\",\"height\":\"186\",\"solo\":\"\",\"dense\":\"\"},on:{\"input\":function($event){return _vm.updateValue($event, 'text')}}})],1)])])],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'\n\nexport default {\n  name: 'ReceiptInfo',\n  components: { UserSettingTemplate },\n  data() {\n    return {\n      isShownSpecialitiesDialog: false,\n    }\n  },\n  computed: {\n    itemText() {\n      return this.$store.state.settings.invoiceItem?.text ?? ''\n    },\n  },\n  beforeCreate() {\n    this.$store.dispatch('settings/getInvoice')\n  },\n  methods: {\n    updateValue(value, property) {\n      this.$store.commit('settings/UPDATE_INVOICE_ITEM', {\n        [property]: value,\n      })\n    },\n    submitData() {\n      this.$store.dispatch('settings/updateInvoice')\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReceiptInfo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReceiptInfo.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ReceiptInfo.vue?vue&type=template&id=af7ad3a4&scoped=true&\"\nimport script from \"./ReceiptInfo.vue?vue&type=script&lang=js&\"\nexport * from \"./ReceiptInfo.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./ReceiptInfo.vue?vue&type=style&index=0&id=af7ad3a4&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"af7ad3a4\",\n  \"0bce53b2\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {UserSettingTemplate: require('D:/languworks/langu-frontend/components/user-settings/UserSettingTemplate.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VRow } from 'vuetify/lib/components/VGrid';\nimport { VTextarea } from 'vuetify/lib/components/VTextarea';\ninstallComponents(component, {VCol,VRow,VTextarea})\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-form',{ref:\"form\",attrs:{\"value\":_vm.formValid},on:{\"validate\":_vm.validate,\"submit\":function($event){$event.preventDefault();return _vm.submit.apply(null, arguments)},\"input\":function($event){_vm.formValid = $event}}},[_c('div',{staticClass:\"user-settings-panel\"},[_c('div',{staticClass:\"panel\"},[(_vm.$vuetify.breakpoint.smAndUp)?_c('div',{staticClass:\"panel-head d-none d-sm-block\"},[_c('div',{staticClass:\"panel-head-title font-weight-medium mb-2 mb-sm-3 mb-md-5\"},[_vm._v(\"\\n          \"+_vm._s(_vm.title)+\"\\n        \")])]):_vm._e(),_vm._v(\" \"),_c('div',{staticClass:\"panel-body\"},[_vm._t(\"default\")],2),_vm._v(\" \"),(!_vm.hideFooter)?_c('div',{staticClass:\"panel-footer d-flex justify-center justify-sm-end\"},[_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"primary\",\"type\":\"submit\",\"disabled\":!_vm.valid}},[_c('svg',{staticClass:\"mr-1\",attrs:{\"width\":\"18\",\"height\":\"18\",\"viewBox\":\"0 0 18 18\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#save-icon\")}})]),_vm._v(\"\\n          \"+_vm._s(_vm.$t('save_changes'))+\"\\n        \")])],1):_vm._e()])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'UserSettingTemplate',\n  props: {\n    title: {\n      type: String,\n      required: true,\n    },\n    hideFooter: {\n      type: <PERSON>olean,\n      default: false,\n    },\n    customValid: {\n      type: Boolean,\n      default: true,\n    },\n    submitFunc: {\n      type: Function,\n      default: () => {},\n    },\n  },\n  data() {\n    return {\n      formValid: true,\n    }\n  },\n  computed: {\n    valid() {\n      return this.formValid && this.customValid\n    },\n  },\n  mounted() {\n    this.validate()\n  },\n  methods: {\n    validate() {\n      this.$refs.form.validate()\n    },\n    submit() {\n      if (!this.valid) return\n\n      this.submitFunc()\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserSettingTemplate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserSettingTemplate.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./UserSettingTemplate.vue?vue&type=template&id=6326778e&\"\nimport script from \"./UserSettingTemplate.vue?vue&type=script&lang=js&\"\nexport * from \"./UserSettingTemplate.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./UserSettingTemplate.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"ed2bb580\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VForm } from 'vuetify/lib/components/VForm';\ninstallComponents(component, {VBtn,VForm})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserSettingTemplate.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"419d3f06\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserSettingTemplate.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".user-settings-panel{padding:44px;border-radius:20px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1)}@media only screen and (max-width:1439px){.user-settings-panel{padding:24px}}@media only screen and (max-width:767px){.user-settings-panel{padding:0;box-shadow:none}}.user-settings-panel .row{margin:0 -14px!important}.user-settings-panel .col{padding:0 14px!important}.user-settings-panel .panel{color:var(--v-greyDark-base)}.user-settings-panel .panel-head-title{font-size:24px;line-height:1.333;color:var(--v-darkLight-base)}@media only screen and (max-width:1439px){.user-settings-panel .panel-head-title{font-size:20px}}.user-settings-panel .panel-body .chips>div{margin-top:6px}.user-settings-panel .panel-body .price-input .v-text-field--outlined.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot{min-height:32px!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr>th{color:var(--v-dark-base)}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr:last-child>th{border:none!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:hover:not(.v-data-table__expanded__content):not(.v-data-table__empty-wrapper){background:none}.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tbody>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tfoot>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>thead>tr>td{height:38px;color:var(--v-greyDark-base)}.user-settings-panel .panel-footer{margin-top:115px}@media only screen and (max-width:1439px){.user-settings-panel .panel-footer{margin-top:56px}}.user-settings-panel .panel-footer .v-btn{letter-spacing:.1px}@media only screen and (max-width:479px){.user-settings-panel .panel-footer .v-btn{width:100%!important}}.user-settings-panel .l-checkbox .v-input--selection-controls__input{width:16px;height:16px;margin-top:2px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;;;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAJA;AACA;AAIA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AATA;AAhBA;;ACrCA;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AClCA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AACA;AAiBA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AACA;AAIA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AATA;AAjCA;;AC7CA;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}