{"version": 3, "file": "pages/user/messages/index.js", "sources": ["webpack:///./components/ConfirmDialog.vue?8e2d", "webpack:///./components/ConfirmDialog.vue?56aa", "webpack:///./components/LoadMoreBtn.vue?bc61", "webpack:///./components/LoadMoreBtn.vue", "webpack:///./components/LoadMoreBtn.vue?7da0", "webpack:///./components/LoadMoreBtn.vue?ad22", "webpack:///./node_modules/vuetify/src/components/VFileInput/VFileInput.sass?30b3", "webpack:///./node_modules/vuetify/src/components/VFileInput/VFileInput.sass", "webpack:///../../../src/components/VExpansionPanel/VExpansionPanel.ts", "webpack:///../../../src/components/VExpansionPanel/VExpansionPanelContent.ts", "webpack:///../../../src/components/VExpansionPanel/VExpansionPanelHeader.ts", "webpack:///../../../src/components/VExpansionPanel/VExpansionPanels.ts", "webpack:///./components/LoadMoreBtn.vue?1e57", "webpack:///./components/user-messages/ConversationItem.vue?ed4c", "webpack:///./components/user-messages/Conversation.vue?829e", "webpack:///./components/user-messages/EmptyContent.vue?3b81", "webpack:///./components/LoadMoreBtn.vue?08bf", "webpack:///./components/LoadMoreBtn.vue?1d0f", "webpack:///./components/user-messages/ConversationItem.vue?28d4", "webpack:///./components/user-messages/ConversationItem.vue?12d9", "webpack:///../../../src/components/VFileInput/VFileInput.ts", "webpack:///./components/user-messages/MessagesPage.vue?66dd", "webpack:///./components/user-messages/ConversationItem.vue?9a8b", "webpack:///./components/user-messages/ConversationItem.vue", "webpack:///./components/user-messages/ConversationItem.vue?4c10", "webpack:///./components/user-messages/ConversationItem.vue?0923", "webpack:///./components/user-messages/Conversation.vue?d7a5", "webpack:///./components/user-messages/Conversation.vue?7f01", "webpack:///./components/user-messages/EmptyContent.vue?8667", "webpack:///./components/user-messages/EmptyContent.vue?d5b5", "webpack:///./components/user-messages/Conversation.vue?ba58", "webpack:///./components/user-messages/Conversation.vue", "webpack:///./components/user-messages/Conversation.vue?a6a4", "webpack:///./components/user-messages/Conversation.vue?9d8b", "webpack:///./components/user-messages/EmptyContent.vue?975e", "webpack:///./components/user-messages/EmptyContent.vue", "webpack:///./components/user-messages/EmptyContent.vue?b742", "webpack:///./components/user-messages/EmptyContent.vue?a8e9", "webpack:///./components/user-messages/MessagesPage.vue?97e1", "webpack:///./components/user-messages/MessagesPage.vue?b171", "webpack:///./components/user-messages/MessagesPage.vue?c9a5", "webpack:///./components/user-messages/MessagesPage.vue", "webpack:///./components/user-messages/MessagesPage.vue?d601", "webpack:///./components/user-messages/MessagesPage.vue?8253", "webpack:///./pages/user/messages/index.vue?2a86", "webpack:///./pages/user/messages/index.vue", "webpack:///./pages/user/messages/index.vue?3765", "webpack:///./pages/user/messages/index.vue?454f", "webpack:///../../../src/components/VTextField/index.ts", "webpack:///../../../src/components/VChip/VChip.ts", "webpack:///../../../src/components/VItemGroup/VItemGroup.ts", "webpack:///./node_modules/vuetify/src/components/VItemGroup/VItemGroup.sass?7678", "webpack:///./node_modules/vuetify/src/components/VItemGroup/VItemGroup.sass", "webpack:///./node_modules/vuetify/src/components/VChip/VChip.sass?005d", "webpack:///./node_modules/vuetify/src/components/VChip/VChip.sass", "webpack:///./components/form/Editor.vue?b6fd", "webpack:///./components/UserStatus.vue?1220", "webpack:///./components/UserStatus.vue", "webpack:///./components/UserStatus.vue?34a5", "webpack:///./components/UserStatus.vue?d7af", "webpack:///./components/form/SearchInput.vue?0840", "webpack:///./mixins/Avatars.vue", "webpack:///./mixins/Avatars.vue?9044", "webpack:///./mixins/Avatars.vue?7fa3", "webpack:///./components/UserStatus.vue?669a", "webpack:///./components/form/Editor.vue?03ad", "webpack:///./components/form/Editor.vue?6e6d", "webpack:///./components/form/Editor.vue?3107", "webpack:///./components/form/Editor.vue", "webpack:///./components/form/Editor.vue?13f3", "webpack:///./components/form/Editor.vue?9998", "webpack:///./components/form/SearchInput.vue?47cd", "webpack:///./components/form/SearchInput.vue", "webpack:///./components/form/SearchInput.vue?67df", "webpack:///./components/form/SearchInput.vue?7b4d", "webpack:///./components/form/SearchInput.vue?84f4", "webpack:///./components/form/SearchInput.vue?618a", "webpack:///./components/UserStatus.vue?80fc", "webpack:///./components/UserStatus.vue?41dd", "webpack:///./node_modules/vuetify/src/components/VExpansionPanel/VExpansionPanel.sass?e120", "webpack:///./node_modules/vuetify/src/components/VExpansionPanel/VExpansionPanel.sass", "webpack:///./components/ConfirmDialog.vue?48f5", "webpack:///./components/ConfirmDialog.vue?5333", "webpack:///./components/ConfirmDialog.vue", "webpack:///./components/ConfirmDialog.vue?51a0", "webpack:///./components/ConfirmDialog.vue?56f3", "webpack:///./mixins/StatusOnline.vue", "webpack:///./mixins/StatusOnline.vue?c046", "webpack:///./mixins/StatusOnline.vue?62a8"], "sourcesContent": ["export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ConfirmDialog.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".remove-illustration-title{font-size:20px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-btn',{class:['load-more-btn', { 'load-more-btn--large': _vm.large }],attrs:{\"text\":\"\",\"width\":\"100%\"},on:{\"click\":_vm.fetchFunc}},[_c('div',{staticClass:\"load-more-btn-icon mr-1\"},[_c('svg',{attrs:{\"viewBox\":\"0 0 17 12\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#arrow-prev\")}})])]),_vm._v(\"\\n  \"+_vm._s(_vm.textBtn)+\"\\n\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'LoadMoreBtn',\n  props: {\n    large: {\n      type: Boolean,\n      default: false,\n    },\n    textBtn: {\n      type: String,\n      required: true,\n    },\n    fetchFunc: {\n      type: Function,\n      required: true,\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./LoadMoreBtn.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./LoadMoreBtn.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./LoadMoreBtn.vue?vue&type=template&id=34f0bc91&scoped=true&\"\nimport script from \"./LoadMoreBtn.vue?vue&type=script&lang=js&\"\nexport * from \"./LoadMoreBtn.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./LoadMoreBtn.vue?vue&type=style&index=0&id=34f0bc91&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"34f0bc91\",\n  \"59b3c299\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\ninstallComponents(component, {VBtn})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./VFileInput.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"83ff91dc\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".theme--light.v-file-input .v-file-input__text{color:rgba(0,0,0,.87)}.theme--light.v-file-input .v-file-input__text--placeholder{color:rgba(0,0,0,.6)}.theme--light.v-file-input.v-input--is-disabled .v-file-input__text,.theme--light.v-file-input.v-input--is-disabled .v-file-input__text .v-file-input__text--placeholder{color:rgba(0,0,0,.38)}.theme--dark.v-file-input .v-file-input__text{color:#fff}.theme--dark.v-file-input .v-file-input__text--placeholder{color:hsla(0,0%,100%,.7)}.theme--dark.v-file-input.v-input--is-disabled .v-file-input__text,.theme--dark.v-file-input.v-input--is-disabled .v-file-input__text .v-file-input__text--placeholder{color:hsla(0,0%,100%,.5)}.v-file-input input[type=file]{left:0;opacity:0;pointer-events:none;position:absolute;max-width:0;width:0}.v-file-input .v-file-input__text{align-items:center;align-self:stretch;display:flex;flex-wrap:wrap;width:100%}.v-file-input .v-file-input__text.v-file-input__text--chips{flex-wrap:wrap}.v-file-input .v-file-input__text .v-chip{margin:4px}.v-file-input .v-text-field__slot{min-height:32px}.v-file-input.v-input--dense .v-text-field__slot{min-height:26px}.v-file-input.v-text-field--filled:not(.v-text-field--single-line) .v-file-input__text{padding-top:22px}.v-file-input.v-text-field--outlined .v-text-field__slot{padding:6px 0}.v-file-input.v-text-field--outlined.v-input--dense .v-text-field__slot{padding:3px 0}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// Components\nimport VExpansionPanels from './VExpansionPanels'\nimport VExpansionPanelHeader from './VExpansionPanelHeader'\nimport VExpansionPanelContent from './VExpansionPanelContent'\n\n// Mixins\nimport { factory as GroupableFactory } from '../../mixins/groupable'\nimport { provide as RegistrableProvide } from '../../mixins/registrable'\n\n// Utilities\nimport { getSlot } from '../../util/helpers'\nimport mixins from '../../util/mixins'\n\n// Types\nimport { VNode } from 'vue'\n\ntype VExpansionPanelHeaderInstance = InstanceType<typeof VExpansionPanelHeader>\ntype VExpansionPanelContentInstance = InstanceType<typeof VExpansionPanelContent>\n\nexport default mixins(\n  GroupableFactory<'expansionPanels', typeof VExpansionPanels>('expansionPanels', 'v-expansion-panel', 'v-expansion-panels'),\n  RegistrableProvide('expansionPanel', true)\n  /* @vue/component */\n).extend({\n  name: 'v-expansion-panel',\n\n  props: {\n    disabled: <PERSON><PERSON><PERSON>,\n    readonly: <PERSON><PERSON><PERSON>,\n  },\n\n  data () {\n    return {\n      content: null as VExpansionPanelContentInstance | null,\n      header: null as VExpansionPanelHeaderInstance | null,\n      nextIsActive: false,\n    }\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        'v-expansion-panel--active': this.isActive,\n        'v-expansion-panel--next-active': this.nextIsActive,\n        'v-expansion-panel--disabled': this.isDisabled,\n        ...this.groupClasses,\n      }\n    },\n    isDisabled (): boolean {\n      return this.expansionPanels.disabled || this.disabled\n    },\n    isReadonly (): boolean {\n      return this.expansionPanels.readonly || this.readonly\n    },\n  },\n\n  methods: {\n    registerContent (vm: VExpansionPanelContentInstance) {\n      this.content = vm\n    },\n    unregisterContent () {\n      this.content = null\n    },\n    registerHeader (vm: VExpansionPanelHeaderInstance) {\n      this.header = vm\n      vm.$on('click', this.onClick)\n    },\n    unregisterHeader () {\n      this.header = null\n    },\n    onClick (e: MouseEvent) {\n      if (e.detail) this.header!.$el.blur()\n\n      this.$emit('click', e)\n\n      this.isReadonly || this.isDisabled || this.toggle()\n    },\n    toggle () {\n      /* istanbul ignore else */\n      if (this.content) this.content.isBooted = true\n      this.$nextTick(() => this.$emit('change'))\n    },\n  },\n\n  render (h): VNode {\n    return h('div', {\n      staticClass: 'v-expansion-panel',\n      class: this.classes,\n      attrs: {\n        'aria-expanded': String(this.isActive),\n      },\n    }, getSlot(this))\n  },\n})\n", "// Components\nimport VExpansionPanel from './VExpansionPanel'\nimport { VExpandTransition } from '../transitions'\n\n// Mixins\nimport Bootable from '../../mixins/bootable'\nimport Colorable from '../../mixins/colorable'\nimport { inject as RegistrableInject } from '../../mixins/registrable'\n\n// Utilities\nimport { getSlot } from '../../util/helpers'\nimport mixins, { ExtractVue } from '../../util/mixins'\n\n// Types\nimport Vue, { VNode, VueConstructor } from 'vue'\n\nconst baseMixins = mixins(\n  Bootable,\n  Colorable,\n  RegistrableInject<'expansionPanel', VueConstructor<Vue>>('expansionPanel', 'v-expansion-panel-content', 'v-expansion-panel')\n)\n\ninterface options extends ExtractVue<typeof baseMixins> {\n  expansionPanel: InstanceType<typeof VExpansionPanel>\n}\n\n/* @vue/component */\nexport default baseMixins.extend<options>().extend({\n  name: 'v-expansion-panel-content',\n\n  computed: {\n    isActive (): boolean {\n      return this.expansionPanel.isActive\n    },\n  },\n\n  created () {\n    this.expansionPanel.registerContent(this)\n  },\n\n  beforeDestroy () {\n    this.expansionPanel.unregisterContent()\n  },\n\n  render (h): VNode {\n    return h(VExpandTransition, this.showLazyContent(() => [\n      h('div', this.setBackgroundColor(this.color, {\n        staticClass: 'v-expansion-panel-content',\n        directives: [{\n          name: 'show',\n          value: this.isActive,\n        }],\n      }), [\n        h('div', { class: 'v-expansion-panel-content__wrap' }, getSlot(this)),\n      ]),\n    ]))\n  },\n})\n", "// Components\nimport { VFadeTransition } from '../transitions'\nimport VExpansionPanel from './VExpansionPanel'\nimport VIcon from '../VIcon'\n\n// Mixins\nimport Colorable from '../../mixins/colorable'\nimport { inject as RegistrableInject } from '../../mixins/registrable'\n\n// Directives\nimport ripple from '../../directives/ripple'\n\n// Utilities\nimport { getSlot } from '../../util/helpers'\nimport mixins, { ExtractVue } from '../../util/mixins'\n\n// Types\nimport Vue, { VNode, VueConstructor } from 'vue'\n\nconst baseMixins = mixins(\n  Colorable,\n  RegistrableInject<'expansionPanel', VueConstructor<Vue>>('expansionPanel', 'v-expansion-panel-header', 'v-expansion-panel')\n)\n\ninterface options extends ExtractVue<typeof baseMixins> {\n  $el: HTMLElement\n  expansionPanel: InstanceType<typeof VExpansionPanel>\n}\n\nexport default baseMixins.extend<options>().extend({\n  name: 'v-expansion-panel-header',\n\n  directives: { ripple },\n\n  props: {\n    disableIconRotate: Boolean,\n    expandIcon: {\n      type: String,\n      default: '$expand',\n    },\n    hideActions: Boolean,\n    ripple: {\n      type: [Boolean, Object],\n      default: false,\n    },\n  },\n\n  data: () => ({\n    hasMousedown: false,\n  }),\n\n  computed: {\n    classes (): object {\n      return {\n        'v-expansion-panel-header--active': this.isActive,\n        'v-expansion-panel-header--mousedown': this.hasMousedown,\n      }\n    },\n    isActive (): boolean {\n      return this.expansionPanel.isActive\n    },\n    isDisabled (): boolean {\n      return this.expansionPanel.isDisabled\n    },\n    isReadonly (): boolean {\n      return this.expansionPanel.isReadonly\n    },\n  },\n\n  created () {\n    this.expansionPanel.registerHeader(this)\n  },\n\n  beforeDestroy () {\n    this.expansionPanel.unregisterHeader()\n  },\n\n  methods: {\n    onClick (e: MouseEvent) {\n      this.$emit('click', e)\n    },\n    genIcon () {\n      const icon = getSlot(this, 'actions') ||\n        [this.$createElement(VIcon, this.expandIcon)]\n\n      return this.$createElement(VFadeTransition, [\n        this.$createElement('div', {\n          staticClass: 'v-expansion-panel-header__icon',\n          class: {\n            'v-expansion-panel-header__icon--disable-rotate': this.disableIconRotate,\n          },\n          directives: [{\n            name: 'show',\n            value: !this.isDisabled,\n          }],\n        }, icon),\n      ])\n    },\n  },\n\n  render (h): VNode {\n    return h('button', this.setBackgroundColor(this.color, {\n      staticClass: 'v-expansion-panel-header',\n      class: this.classes,\n      attrs: {\n        tabindex: this.isDisabled ? -1 : null,\n        type: 'button',\n      },\n      directives: [{\n        name: 'ripple',\n        value: this.ripple,\n      }],\n      on: {\n        ...this.$listeners,\n        click: this.onClick,\n        mousedown: () => (this.hasMousedown = true),\n        mouseup: () => (this.hasMousedown = false),\n      },\n    }), [\n      getSlot(this, 'default', { open: this.isActive }, true),\n      this.hideActions || this.genIcon(),\n    ])\n  },\n})\n", "// Styles\nimport './VExpansionPanel.sass'\n\n// Components\nimport { BaseItemGroup, GroupableInstance } from '../VItemGroup/VItemGroup'\nimport VExpansionPanel from './VExpansionPanel'\n\n// Utilities\nimport { breaking } from '../../util/console'\n\n// Types\ninterface VExpansionPanelInstance extends InstanceType<typeof VExpansionPanel> {}\n\n/* @vue/component */\nexport default BaseItemGroup.extend({\n  name: 'v-expansion-panels',\n\n  provide (): object {\n    return {\n      expansionPanels: this,\n    }\n  },\n\n  props: {\n    accordion: Boolean,\n    disabled: Boolean,\n    flat: Boolean,\n    hover: Boolean,\n    focusable: Boolean,\n    inset: <PERSON>olean,\n    popout: <PERSON><PERSON><PERSON>,\n    readonly: <PERSON>ole<PERSON>,\n    tile: <PERSON>olean,\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        ...BaseItemGroup.options.computed.classes.call(this),\n        'v-expansion-panels': true,\n        'v-expansion-panels--accordion': this.accordion,\n        'v-expansion-panels--flat': this.flat,\n        'v-expansion-panels--hover': this.hover,\n        'v-expansion-panels--focusable': this.focusable,\n        'v-expansion-panels--inset': this.inset,\n        'v-expansion-panels--popout': this.popout,\n        'v-expansion-panels--tile': this.tile,\n      }\n    },\n  },\n\n  created () {\n    /* istanbul ignore next */\n    if (this.$attrs.hasOwnProperty('expand')) {\n      breaking('expand', 'multiple', this)\n    }\n\n    /* istanbul ignore next */\n    if (\n      Array.isArray(this.value) &&\n      this.value.length > 0 &&\n      typeof this.value[0] === 'boolean'\n    ) {\n      breaking(':value=\"[true, false, true]\"', ':value=\"[0, 2]\"', this)\n    }\n  },\n\n  methods: {\n    updateItem (item: GroupableInstance & VExpansionPanelInstance, index: number) {\n      const value = this.getValue(item, index)\n      const nextValue = this.getValue(item, index + 1)\n\n      item.isActive = this.toggleMethod(value)\n      item.nextIsActive = this.toggleMethod(nextValue)\n    },\n  },\n})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./LoadMoreBtn.vue?vue&type=style&index=0&id=34f0bc91&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"45e472a6\", content, true, context)\n};", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ConversationItem.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"7337ad18\", content, true, context)\n};", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Conversation.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"c2cacb64\", content, true, context)\n};", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./EmptyContent.vue?vue&type=style&index=0&id=58e2b6d0&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"79c53e1f\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./LoadMoreBtn.vue?vue&type=style&index=0&id=34f0bc91&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".load-more-btn[data-v-34f0bc91]{color:var(--v-greyDark-base)}.load-more-btn-icon[data-v-34f0bc91]{display:flex;align-items:center;justify-content:center;width:38px;height:38px;color:#fff;border-radius:50%;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%)}.load-more-btn-icon svg[data-v-34f0bc91]{width:19px;height:14px;transform:rotate(-90deg)}.load-more-btn--large[data-v-34f0bc91]{height:52px!important}.load-more-btn--large .load-more-btn-icon[data-v-34f0bc91]{width:52px;height:52px}.load-more-btn--large .load-more-btn-icon svg[data-v-34f0bc91]{width:26px;height:16px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ConversationItem.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".conversation-item{display:flex;width:100%}.conversation-item>div{width:calc(100% - 60px);max-width:462px}@media only screen and (max-width:639px){.conversation-item>div{width:calc(100% - 45px)}}@media only screen and (max-width:479px){.conversation-item>div{width:calc(100% - 20px)}}.conversation-item-header{display:flex;align-items:flex-end;margin-bottom:2px;font-size:13px;color:var(--v-greyLight-darken2);line-height:1.23}@media only screen and (max-width:479px){.conversation-item-header{font-size:12px}}.conversation-item-header-avatar{position:relative;margin-bottom:2px;filter:drop-shadow(0 4px 5px rgba(0,0,0,.2))}@media only screen and (max-width:479px){.conversation-item-header-avatar .v-avatar{width:42px!important;min-width:42px!important;height:42px!important}}.conversation-item-header>div:not(.conversation-item-header-avatar){margin-left:10px}.conversation-item-body{padding:16px 12px;font-size:14px;line-height:1.4;border-radius:16px;color:var(--v-greyLight-darken4);background:linear-gradient(126.15deg,rgba(128,182,34,.18),rgba(60,135,248,.18) 102.93%)}@media only screen and (max-width:1439px){.conversation-item-body{padding:14px 12px}}.conversation-item-body a{color:var(--v-greyLight-darken4)!important;transition:color .3s}.conversation-item-body a span{display:inline-block;font-size:20px}.conversation-item-body a:hover{color:var(--v-success-base)!important}.conversation-item-body.conversation-item-body--file a{text-decoration:none}.conversation-item-body ul{padding-left:20px}.conversation-item-body ul li{margin-bottom:0}.conversation-item-body ul li p{min-height:16px;margin-bottom:0}.conversation-item-body>div{word-wrap:break-word}.conversation-item-body>div>*{min-height:16px;margin-bottom:0}.conversation-item-body>div>:last-child{min-height:0}.conversation-item-footer{display:flex;justify-content:flex-end;align-items:center;margin-top:2px;padding-right:10px;font-size:13px;color:var(--v-greyLight-darken2);line-height:1.23}@media only screen and (max-width:479px){.conversation-item-footer{font-size:12px}}.conversation-item--mine{justify-content:flex-end}.conversation-item--mine .conversation-item-header{flex-direction:row-reverse}.conversation-item--mine .conversation-item-header-avatar{margin-right:0;margin-left:12px}.conversation-item--mine .conversation-item-footer{padding-right:0}.conversation-item--mine+.conversation-item--mine{margin-top:10px}@media only screen and (max-width:479px){.conversation-item--mine+.conversation-item--mine{margin-top:6px}}.conversation-item--mine+.conversation-item--mine .conversation-item-header-avatar{display:none}.conversation-item--other+.conversation-item--other{margin-top:10px}@media only screen and (max-width:479px){.conversation-item--other+.conversation-item--other{margin-top:6px}}.conversation-item--other+.conversation-item--other .conversation-item-header-avatar{display:none}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// Styles\nimport './VFileInput.sass'\n\n// Extensions\nimport VTextField from '../VTextField'\n\n// Components\nimport { VChip } from '../VChip'\n\n// Types\nimport { PropValidator } from 'vue/types/options'\n\n// Utilities\nimport { deepEqual, humanReadableFileSize, wrapInArray } from '../../util/helpers'\nimport { consoleError } from '../../util/console'\nimport { mergeStyles } from '../../util/mergeData'\n\nexport default VTextField.extend({\n  name: 'v-file-input',\n\n  model: {\n    prop: 'value',\n    event: 'change',\n  },\n\n  props: {\n    chips: Boolean,\n    clearable: {\n      type: Boolean,\n      default: true,\n    },\n    counterSizeString: {\n      type: String,\n      default: '$vuetify.fileInput.counterSize',\n    },\n    counterString: {\n      type: String,\n      default: '$vuetify.fileInput.counter',\n    },\n    hideInput: Boolean,\n    placeholder: String,\n    prependIcon: {\n      type: String,\n      default: '$file',\n    },\n    readonly: {\n      type: Boolean,\n      default: false,\n    },\n    showSize: {\n      type: [Boolean, Number],\n      default: false,\n      validator: (v: boolean | number) => {\n        return (\n          typeof v === 'boolean' ||\n          [1000, 1024].includes(v)\n        )\n      },\n    } as PropValidator<boolean | 1000 | 1024>,\n    smallChips: Boolean,\n    truncateLength: {\n      type: [Number, String],\n      default: 22,\n    },\n    type: {\n      type: String,\n      default: 'file',\n    },\n    value: {\n      default: undefined,\n      validator: val => {\n        return wrapInArray(val).every(v => v != null && typeof v === 'object')\n      },\n    } as PropValidator<File | File[]>,\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        ...VTextField.options.computed.classes.call(this),\n        'v-file-input': true,\n      }\n    },\n    computedCounterValue (): string {\n      const fileCount = (this.isMultiple && this.lazyValue)\n        ? this.lazyValue.length\n        : (this.lazyValue instanceof File) ? 1 : 0\n\n      if (!this.showSize) return this.$vuetify.lang.t(this.counterString, fileCount)\n\n      const bytes = this.internalArrayValue.reduce((bytes: number, { size = 0 }: File) => {\n        return bytes + size\n      }, 0)\n\n      return this.$vuetify.lang.t(\n        this.counterSizeString,\n        fileCount,\n        humanReadableFileSize(bytes, this.base === 1024)\n      )\n    },\n    internalArrayValue (): File[] {\n      return wrapInArray(this.internalValue)\n    },\n    internalValue: {\n      get (): File[] {\n        return this.lazyValue\n      },\n      set (val: File | File[]) {\n        this.lazyValue = val\n        this.$emit('change', this.lazyValue)\n      },\n    },\n    isDirty (): boolean {\n      return this.internalArrayValue.length > 0\n    },\n    isLabelActive (): boolean {\n      return this.isDirty\n    },\n    isMultiple (): boolean {\n      return this.$attrs.hasOwnProperty('multiple')\n    },\n    text (): string[] {\n      if (!this.isDirty && (this.isFocused || !this.hasLabel)) return [this.placeholder]\n\n      return this.internalArrayValue.map((file: File) => {\n        const {\n          name = '',\n          size = 0,\n        } = file\n\n        const truncatedText = this.truncateText(name)\n\n        return !this.showSize\n          ? truncatedText\n          : `${truncatedText} (${humanReadableFileSize(size, this.base === 1024)})`\n      })\n    },\n    base (): 1000 | 1024 | undefined {\n      return typeof this.showSize !== 'boolean' ? this.showSize : undefined\n    },\n    hasChips (): boolean {\n      return this.chips || this.smallChips\n    },\n  },\n\n  watch: {\n    readonly: {\n      handler (v) {\n        if (v === true) consoleError('readonly is not supported on <v-file-input>', this)\n      },\n      immediate: true,\n    },\n    value (v) {\n      const value = this.isMultiple ? v : v ? [v] : []\n      if (!deepEqual(value, this.$refs.input.files)) {\n        // When the input value is changed programatically, clear the\n        // internal input's value so that the `onInput` handler\n        // can be triggered again if the user re-selects the exact\n        // same file(s). Ideally, `input.files` should be\n        // manipulated directly but that property is readonly.\n        this.$refs.input.value = ''\n      }\n    },\n  },\n\n  methods: {\n    clearableCallback () {\n      this.internalValue = this.isMultiple ? [] : null\n      this.$refs.input.value = ''\n    },\n    genChips () {\n      if (!this.isDirty) return []\n\n      return this.text.map((text, index) => this.$createElement(VChip, {\n        props: { small: this.smallChips },\n        on: {\n          'click:close': () => {\n            const internalValue = this.internalValue\n            internalValue.splice(index, 1)\n            this.internalValue = internalValue // Trigger the watcher\n          },\n        },\n      }, [text]))\n    },\n    genControl () {\n      const render = VTextField.options.methods.genControl.call(this)\n\n      if (this.hideInput) {\n        render.data!.style = mergeStyles(\n          render.data!.style,\n          { display: 'none' }\n        )\n      }\n\n      return render\n    },\n    genInput () {\n      const input = VTextField.options.methods.genInput.call(this)\n\n      // We should not be setting value\n      // programmatically on the input\n      // when it is using type=\"file\"\n      delete input.data!.domProps!.value\n\n      // This solves an issue in Safari where\n      // nothing happens when adding a file\n      // do to the input event not firing\n      // https://github.com/vuetifyjs/vuetify/issues/7941\n      delete input.data!.on!.input\n      input.data!.on!.change = this.onInput\n\n      return [this.genSelections(), input]\n    },\n    genPrependSlot () {\n      if (!this.prependIcon) return null\n\n      const icon = this.genIcon('prepend', () => {\n        this.$refs.input.click()\n      })\n\n      return this.genSlot('prepend', 'outer', [icon])\n    },\n    genSelectionText (): string[] {\n      const length = this.text.length\n\n      if (length < 2) return this.text\n      if (this.showSize && !this.counter) return [this.computedCounterValue]\n      return [this.$vuetify.lang.t(this.counterString, length)]\n    },\n    genSelections () {\n      const children = []\n\n      if (this.isDirty && this.$scopedSlots.selection) {\n        this.internalArrayValue.forEach((file: File, index: number) => {\n          if (!this.$scopedSlots.selection) return\n\n          children.push(\n            this.$scopedSlots.selection({\n              text: this.text[index],\n              file,\n              index,\n            })\n          )\n        })\n      } else {\n        children.push(this.hasChips && this.isDirty ? this.genChips() : this.genSelectionText())\n      }\n\n      return this.$createElement('div', {\n        staticClass: 'v-file-input__text',\n        class: {\n          'v-file-input__text--placeholder': this.placeholder && !this.isDirty,\n          'v-file-input__text--chips': this.hasChips && !this.$scopedSlots.selection,\n        },\n      }, children)\n    },\n    genTextFieldSlot () {\n      const node = VTextField.options.methods.genTextFieldSlot.call(this)\n\n      node.data!.on = {\n        ...(node.data!.on || {}),\n        click: () => this.$refs.input.click(),\n      }\n\n      return node\n    },\n    onInput (e: Event) {\n      const files = [...(e.target as HTMLInputElement).files || []]\n\n      this.internalValue = this.isMultiple ? files : files[0]\n\n      // Set initialValue here otherwise isFocused\n      // watcher in VTextField will emit a change\n      // event whenever the component is blurred\n      this.initialValue = this.internalValue\n    },\n    onKeyDown (e: KeyboardEvent) {\n      this.$emit('keydown', e)\n    },\n    truncateText (str: string) {\n      if (str.length < Number(this.truncateLength)) return str\n      const charsKeepOneSide = Math.floor((Number(this.truncateLength) - 1) / 2)\n      return `${str.slice(0, charsKeepOneSide)}…${str.slice(str.length - charsKeepOneSide)}`\n    },\n  },\n})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MessagesPage.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"5aec5f3e\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[\n    'conversation-item',\n    { 'conversation-item--mine': _vm.isCurrentUser },\n    { 'conversation-item--other': !_vm.isCurrentUser } ]},[_vm._ssrNode(\"<div>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"conversation-item-header\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"conversation-item-header-avatar\\\">\",\"</div>\",[_c('v-avatar',{attrs:{\"width\":\"52\",\"height\":\"52\"}},[_c('v-img',{attrs:{\"src\":_vm.getSrcAvatar(_vm.avatars, 'user_thumb_52x52'),\"srcset\":_vm.getSrcSetAvatar(\n                _vm.avatars,\n                'user_thumb_52x52',\n                'user_thumb_104x104'\n              ),\"options\":{ rootMargin: '50%' }}})],1),_vm._ssrNode(\" \"),_c('user-status',{attrs:{\"user-id\":_vm.item.authorId,\"user-statuses\":_vm.userStatuses}})],2),_vm._ssrNode(\" <div>\"+((_vm.isCurrentUser)?(_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('sent_by_me_on'))+\"\\n        \")):(_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('sent_by_somebody_on', {\n              username: _vm.recipientName,\n            }))+\"\\n        \")))+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.$dayjs(_vm.item.createDate).tz(_vm.timeZone).format('ll, LT'))+\"\\n      \")+\"</div>\")],2),_vm._ssrNode(\" <div\"+(_vm._ssrClass(null,[\n        'conversation-item-body',\n        { 'conversation-item-body--file': _vm.item.isFile } ]))+\">\"+((!_vm.item.isFile)?(\"<div>\"+(_vm._s(_vm.item.text))+\"</div>\"):(\"<a\"+(_vm._ssrAttr(\"href\",_vm.fileUrl))+\" download><span class=\\\"mr-1\\\">📎</span>\"+_vm._ssrEscape(_vm._s(_vm.item.text)+\"\\n        \")+\"</a>\"))+\"</div> \"),_vm._ssrNode(\"<div class=\\\"conversation-item-footer\\\">\",\"</div>\",[_vm._ssrNode(((_vm.item.readDate)?(_vm._ssrEscape(_vm._s(_vm.$t('seen'))+\"\\n        \"+_vm._s(_vm.$dayjs(_vm.item.readDate).tz(_vm.timeZone).format('ll, LT')))):(_vm._ssrEscape(_vm._s(_vm.$t('not_yet_seen')))))+\" \"),(_vm.isCurrentUser)?_c('v-btn',{attrs:{\"text\":\"\",\"x-small\":\"\",\"height\":\"16\",\"color\":\"grey\"},on:{\"click\":function($event){_vm.isShownMessageConfirmDialog = true}}},[_vm._v(\"\\n        (\"),_c('span',{staticClass:\"text-decoration-underline\"},[_vm._v(_vm._s(_vm.$t('delete_message')))]),_vm._v(\")\")]):_vm._e()],2)],2),_vm._ssrNode(\" \"),_c('confirm-dialog',{attrs:{\"is-shown-confirm-dialog\":_vm.isShownMessageConfirmDialog,\"cancel-text-button\":\"no\",\"confirm-text-button\":\"yes\"},on:{\"confirm\":_vm.removeMessage,\"close-dialog\":function($event){_vm.isShownMessageConfirmDialog = false}}},[_vm._v(\"\\n    \"+_vm._s(_vm.$t('are_you_sure_you_want_to_delete_this_message_permanently'))+\"\\n  \")])],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport ConfirmDialog from '@/components/ConfirmDialog'\nimport Avatars from '~/mixins/Avatars'\nimport UserStatus from '~/components/UserStatus'\n\nexport default {\n  name: 'ConversationItem',\n  components: { UserStatus, ConfirmDialog },\n  mixins: [Avatars],\n  props: {\n    item: {\n      type: Object,\n      required: true,\n    },\n    threadId: {\n      type: Number,\n      required: true,\n    },\n    recipientName: {\n      type: String,\n      required: true,\n    },\n    recipientAvatars: {\n      type: Object,\n      required: true,\n    },\n    userAvatars: {\n      type: Object,\n      required: true,\n    },\n    userStatuses: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  data() {\n    return {\n      isShownMessageConfirmDialog: false,\n    }\n  },\n  computed: {\n    userId() {\n      return this.$store.state.user.item?.id\n    },\n    isCurrentUser() {\n      return this.userId === this.item.authorId\n    },\n    avatars() {\n      return this.isCurrentUser ? this.userAvatars : this.recipientAvatars\n    },\n    fileUrl() {\n      return `${process.env.NUXT_ENV_URL}/messages/file/${this.item.id}`\n    },\n    timeZone() {\n      return this.$store.getters['user/timeZone']\n    },\n  },\n  methods: {\n    async downloadClickHandler() {\n      await this.$axios({\n        url: this.fileUrl,\n        method: 'GET',\n        responseType: 'blob',\n      })\n        .then((response) => {\n          const url = window.URL.createObjectURL(new Blob([response.data]))\n          const link = document.createElement('a')\n\n          link.href = url\n          link.setAttribute('download', this.item.text)\n          document.body.appendChild(link)\n          link.click()\n        })\n        .catch(() => console.info('Download error'))\n    },\n    removeMessage() {\n      this.isShownMessageConfirmDialog = false\n\n      this.$store.dispatch('message/removeMessage', this.item.id)\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ConversationItem.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ConversationItem.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ConversationItem.vue?vue&type=template&id=1e96ee73&\"\nimport script from \"./ConversationItem.vue?vue&type=script&lang=js&\"\nexport * from \"./ConversationItem.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./ConversationItem.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"a09c4348\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {UserStatus: require('D:/languworks/langu-frontend/components/UserStatus.vue').default,ConfirmDialog: require('D:/languworks/langu-frontend/components/ConfirmDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VAvatar } from 'vuetify/lib/components/VAvatar';\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VAvatar,VBtn,VImg})\n", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Conversation.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".conversation{padding:30px 44px 138px;background-color:#fff;border-radius:20px}@media only screen and (max-width:1439px){.conversation{padding:24px 24px 60px}}@media only screen and (min-width:768px){.conversation{box-shadow:0 8px 17px rgba(17,46,90,.1)}}@media only screen and (max-width:767px){.conversation{padding:0}}.conversation-header{display:flex;justify-content:space-between}.conversation-header .conversation-avatar{filter:drop-shadow(0 4px 5px rgba(0,0,0,.2))}.conversation-header .conversation-avatar>div,.conversation-header .conversation-avatar>div .v-avatar{position:relative}@media only screen and (max-width:991px){.conversation-header .conversation-avatar>div .v-avatar{width:90px!important;height:90px!important}}@media only screen and (max-width:639px){.conversation-header .conversation-avatar>div .v-avatar{width:80px!important;height:80px!important}}@media only screen and (max-width:479px){.conversation-header .conversation-avatar>div .v-avatar{width:70px!important;height:70px!important}}.conversation-header .conversation-avatar>div .v-avatar a{display:block;position:absolute;top:0;left:0;width:100%;height:100%;z-index:2}.conversation-header .conversation-avatar .user-status{right:3px;bottom:3px}@media only screen and (max-width:639px){.conversation-header .conversation-avatar .user-status{right:1px;bottom:1px}}.conversation-header .conversation-title{font-size:24px;line-height:1.333}@media only screen and (max-width:1439px){.conversation-header .conversation-title{font-size:20px}}@media only screen and (max-width:767px){.conversation-header .conversation-title{font-size:18px}}.conversation-header .conversation-details{font-size:13px;line-height:1.23;color:var(--v-greyLight-darken2)}@media only screen and (max-width:767px){.conversation-header .conversation-details{font-size:12px}}@media only screen and (max-width:1439px){.conversation-header .conversation-details>div:not(:last-child){margin-bottom:4px}}.conversation-header .conversation-details a{color:var(--v-grey-base)}.conversation-header .conversation-details a:hover{color:var(--v-green-base);transition:all .3s}.conversation-body .new-message-label{font-size:16px}.conversation-body .new-message .text-editor .ProseMirror{min-height:118px}.conversation-body .new-message .text-editor .text-editor-buttons{left:8px;right:auto}.conversation-body .new-message-notice{margin-bottom:4px;font-size:12px;color:var(--v-greyLight-darken2);line-height:1.2}@media only screen and (max-width:1439px){.conversation-body .new-message-notice{margin-bottom:8px}}.conversation-body .new-message-bottom{display:flex;flex-wrap:wrap;justify-content:space-between;align-items:center}.conversation-body .new-message-bottom .upload-file-name{display:flex;align-items:center}.conversation-body .new-message-bottom-buttons{width:100%;display:flex;flex-wrap:wrap;justify-content:flex-end}@media only screen and (max-width:479px){.conversation-body .new-message-bottom-buttons{justify-content:space-around}}.conversation-body .new-message-bottom-buttons .v-btn .v-btn__content{white-space:nowrap}.conversation-body .messages-list{display:flex;flex-direction:column}.conversation-body .conversation-show-more-btn{max-width:240px;margin-left:auto;margin-right:auto}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./EmptyContent.vue?vue&type=style&index=0&id=58e2b6d0&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".messages-empty-content[data-v-58e2b6d0]{padding:30px 44px 138px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1);border-radius:20px;line-height:1.4}@media only screen and (max-width:1439px){.messages-empty-content[data-v-58e2b6d0]{padding:24px 24px 60px}}@media only screen and (min-width:768px){.messages-empty-content[data-v-58e2b6d0]{min-height:620px}}@media only screen and (max-width:479px){.messages-empty-content[data-v-58e2b6d0]{padding:24px 15px 30px}}.messages-empty-content-title[data-v-58e2b6d0]{font-size:24px}@media only screen and (max-width:991px){.messages-empty-content-title[data-v-58e2b6d0]{font-size:20px}}.messages-empty-content-text[data-v-58e2b6d0]{font-size:18px}@media only screen and (max-width:991px){.messages-empty-content-text[data-v-58e2b6d0]{font-size:16px}}.messages-empty-content-text a[data-v-58e2b6d0]{color:var(--v-success-base);background:linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));background:-webkit-linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));-webkit-background-clip:text;-webkit-text-fill-color:transparent}.messages-empty-content-text ul[data-v-58e2b6d0]{padding-left:32px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"conversation\"},[_vm._ssrNode(\"<div class=\\\"conversation-header mb-1\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"mr-1 mr-sm-2\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"conversation-title font-weight-medium\\\">\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.$t('messages_with'))+\"\\n        \")+((!_vm.item.userIsDeleted)?(_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.item.firstName)+\"\\n          \"+_vm._s(_vm.item.lastName)+\"\\n        \")):(_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('deleted_user'))+\"\\n        \")))+\"</div> \"),(!_vm.item.userIsDeleted)?_vm._ssrNode(\"<div class=\\\"conversation-details mt-2\\\">\",\"</div>\",[_vm._ssrNode(\"<div>\",\"</div>\",[_vm._ssrNode(_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('next_lesson'))+\":\\n          \")),_vm._ssrNode(\"<span class=\\\"text-no-wrap\\\">\",\"</span>\",[(_vm.hasNextLesson)?[_vm._ssrNode(\"<span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.$dayjs(_vm.item.nextLessonDate)\n                    .tz(_vm.userTimezone)\n                    .format('ll, LT'))+\"\\n              \")+\"</span>\\n              (\"),_c('nuxt-link',{attrs:{\"to\":_vm.localePath({ path: '/user/lessons' })}},[_vm._v(_vm._s(_vm.$t('see_lessons')))]),_vm._ssrNode(\")\\n            \")]:_vm._ssrNode(\"<span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.$t('none_scheduled'))+\"\\n              \")+\"</span>\")],2)],2),_vm._ssrNode(\" <div>\"+_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('current_local_time_for'))+\"\\n          \"+_vm._s(_vm.item.firstName)+\":\\n          \")+\"<span class=\\\"text-no-wrap\\\"><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$dayjs().tz(_vm.item.recipientTimeZone).format('LT'))+\"\\n            \")+\"</span>\"+_vm._ssrEscape(\"\\n            (\"+_vm._s(_vm.$dayjs().tz(_vm.item.recipientTimeZone).format('z'))+\")\\n          \")+\"</span></div> <div>\"+_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('last_online'))+\":\\n          \")+\"<span class=\\\"font-weight-medium text-no-wrap\\\">\"+((_vm.status === 'online')?(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('online_now'))+\"\\n            \")):(_vm.status === 'idle')?(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('online_but_idle'))+\"\\n            \")):(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$dayjs(_vm.item.lastLoginDate).tz(_vm.userTimezone).format('ll, LT'))+\"\\n            \")))+\"</span></div>\")],2):_vm._e()],2),_vm._ssrNode(\" \"),(!_vm.item.userIsDeleted)?_vm._ssrNode(\"<div class=\\\"conversation-avatar\\\">\",\"</div>\",[_vm._ssrNode(\"<div>\",\"</div>\",[_c('v-avatar',{attrs:{\"width\":\"118\",\"height\":\"118\"}},[_c('v-img',{attrs:{\"src\":_vm.getSrcAvatar(_vm.item.recipientAvatars, 'user_thumb_118x118'),\"srcset\":_vm.getSrcSetAvatar(\n                _vm.item.recipientAvatars,\n                'user_thumb_118x118',\n                'user_thumb_236x236'\n              ),\"options\":{ rootMargin: '50%' }}}),_vm._v(\" \"),(_vm.recipientLink)?_c('nuxt-link',{attrs:{\"to\":_vm.recipientLink}}):_vm._e()],1),_vm._ssrNode(\" \"),_c('user-status',{attrs:{\"user-id\":_vm.item.userId,\"user-statuses\":_vm.userStatuses,\"large\":\"\"}})],2)]):_vm._e()],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"conversation-body\\\">\",\"</div>\",[(!_vm.item.userIsDeleted)?_vm._ssrNode(\"<div class=\\\"new-message\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"new-message-label font-weight-medium mb-1\\\">\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.$t('write_new_message'))+\" 🖋️\\n      \")+\"</div> \"),_c('editor',{attrs:{\"value\":_vm.newMessage,\"limit\":6000,\"auto-link\":\"\"},on:{\"update\":function($event){_vm.newMessage = $event},\"validation\":function($event){_vm.isMessageValid = $event},\"submit\":_vm.submitNewMessageHandler,\"keydown\":_vm.handleKeydown}}),_vm._ssrNode(\" <div class=\\\"new-message-notice pl-2\\\">\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.$t('press_ctrl_enter_to_send'))+\"\\n      \")+\"</div> \"),_vm._ssrNode(\"<div class=\\\"new-message-bottom\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"new-message-attached-file\\\">\",\"</div>\",[_c('v-file-input',{ref:\"fileMessage\",staticClass:\"d-none\",attrs:{\"rules\":_vm.rules.file,\"prepend-icon\":\"\"},on:{\"change\":_vm.uploadFile}})],1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"new-message-bottom-buttons\\\">\",\"</div>\",[_c('v-btn',{staticClass:\"gradient font-weight-medium my-1 ml-1\",on:{\"click\":function($event){_vm.$refs.fileMessage.$el.querySelector('input').click()}}},[_c('div',{staticClass:\"text--gradient\"},[_vm._v(_vm._s(_vm.$t('attach_document'))+\" 📎\")])]),_vm._ssrNode(\" \"),_c('v-btn',{staticClass:\"font-weight-medium my-1 ml-1\",attrs:{\"color\":\"primary\"},on:{\"click\":_vm.submitNewMessageHandler}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('send'))+\" 📬\\n          \")])],2)],2)],2):_vm._e(),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"messages-list mt-2 mt-md-3\\\">\",\"</div>\",_vm._l((_vm.messages),function(message){return _c('conversation-item',{key:message.id,attrs:{\"item\":message,\"recipient-name\":_vm.recipientName,\"recipient-avatars\":!_vm.item.userIsDeleted ? _vm.item.recipientAvatars : {},\"user-avatars\":_vm.item.userAvatars,\"thread-id\":_vm.threadId,\"user-statuses\":_vm.userStatuses}})}),1),_vm._ssrNode(\" \"),(_vm.isMoreButtonShown)?_vm._ssrNode(\"<div class=\\\"conversation-show-more-btn mt-2 mt-sm-5\\\">\",\"</div>\",[_c('load-more-btn',{attrs:{\"text-btn\":_vm.$t('load_more_messages'),\"fetch-func\":_vm.fetchMessages}})],1):_vm._e()],2)],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Editor from '~/components/form/Editor'\nimport LoadMoreBtn from '~/components/LoadMoreBtn'\nimport ConversationItem from '~/components/user-messages/ConversationItem'\nimport Avatars from '~/mixins/Avatars'\nimport UserStatus from '~/components/UserStatus'\n\nexport default {\n  name: 'Conversation',\n  components: { Editor, LoadMoreBtn, ConversationItem, UserStatus },\n  mixins: [Avatars],\n  props: {\n    item: {\n      type: Object,\n      required: true,\n    },\n    userStatuses: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  data() {\n    return {\n      newMessage: '',\n      messagesPage: 1,\n      isMessageValid: false,\n      rules: {\n        file: [\n          (v) => !!v,\n          (v) =>\n            !v ||\n            v.size < 10485760 ||\n            this.$t('file_size_should_be_less_than', { value: '10 MB' }),\n        ],\n      },\n    }\n  },\n  computed: {\n    threadId() {\n      return this.item.threadId\n    },\n    userTimezone() {\n      return this.$store.getters['user/timeZone']\n    },\n    recipientName() {\n      return !this.item.userIsDeleted\n        ? this.item.firstName\n        : this.$t('deleted_user')\n    },\n    recipientLink() {\n      return this.item.username ? `/teacher/${this.item.username}` : null\n    },\n    hasNextLesson() {\n      return this.item.nextLessonDate?.length\n    },\n    status() {\n      let status = 'offline'\n\n      if (\n        Object.prototype.hasOwnProperty.call(\n          this.userStatuses,\n          this.item.userId?.toString()\n        )\n      ) {\n        status = this.userStatuses[this.item.userId]\n      }\n\n      return status\n    },\n    totalPages() {\n      return Math.ceil(\n        this.item.countMessages / process.env.NUXT_ENV_MESSAGES_PER_PAGE\n      )\n    },\n    messages() {\n      return this.item.messages ?? []\n    },\n    isMoreButtonShown() {\n      return this.totalPages > 1 && this.messagesPage < this.totalPages\n    },\n  },\n  watch: {\n    threadId() {\n      this.messagesPage = 1\n    },\n  },\n  beforeDestroy() {\n    this.newMessage = ''\n    this.file = null\n  },\n  methods: {\n    uploadFile(file) {\n      this.$store.dispatch('message/uploadFile', {\n        threadId: this.threadId,\n        file,\n      })\n    },\n    submitNewMessageHandler() {\n      if (this.isMessageValid) {\n        this.$store\n          .dispatch('message/sendMessage', {\n            threadId: this.threadId,\n            message: this.newMessage,\n          })\n          .then(() => {\n            this.newMessage = ''\n          })\n      }\n    },\n    async fetchMessages() {\n      this.messagesPage++\n\n      await this.$store.dispatch('loadingAllow', false)\n      await this.$store.dispatch('message/getConversation', {\n        threadId: this.threadId,\n        page: this.messagesPage,\n      })\n      await this.$store.dispatch('loadingAllow', true)\n    },\n    handleKeydown(event) {\n      if (event.key === 'Enter' && !event.ctrlKey) {\n        event.preventDefault()\n        this.newMessage += '\\n'\n      } else if (event.key === 'Enter' && event.ctrlKey) {\n        event.preventDefault()\n        this.submitNewMessageHandler()\n      }\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Conversation.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Conversation.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Conversation.vue?vue&type=template&id=21d9c4c8&\"\nimport script from \"./Conversation.vue?vue&type=script&lang=js&\"\nexport * from \"./Conversation.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./Conversation.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"23391dae\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {UserStatus: require('D:/languworks/langu-frontend/components/UserStatus.vue').default,LoadMoreBtn: require('D:/languworks/langu-frontend/components/LoadMoreBtn.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VAvatar } from 'vuetify/lib/components/VAvatar';\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VFileInput } from 'vuetify/lib/components/VFileInput';\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VAvatar,VBtn,VFileInput,VImg})\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"messages-empty-content\"},[_vm._ssrNode(\"<div class=\\\"messages-empty-content-title font-weight-medium mb-3 mb-sm-4\\\" data-v-58e2b6d0>\"+_vm._ssrEscape(\"\\n    \"+_vm._s(_vm.$t(_vm.isTeacher ? 'no_messages_teacher_yet' : 'no_messages_student_yet'))+\"\\n    💬\\n  \")+\"</div> \"),_vm._ssrNode(\"<div class=\\\"messages-empty-content-text\\\" data-v-58e2b6d0>\",\"</div>\",[(_vm.isTeacher)?[_vm._ssrNode(((_vm.locale === 'pl')?(\"\\n        Kiedy uczeń rezerwuje lekcję próbną, jest poproszony o napisanie\\n        krótkiej wiadom<PERSON>, która pojawi się tutaj.\\n        <br data-v-58e2b6d0><br data-v-58e2b6d0>\\n        Zanim to się stanie, upewnij się, że Twoja strona profilowa wzbudza\\n        jak największe zainteresowanie.\\n        <br data-v-58e2b6d0><br data-v-58e2b6d0> <ul class=\\\"mb-0\\\" data-v-58e2b6d0><li data-v-58e2b6d0>Dodaj wysokiej jakości wideo na YouTube</li> <li data-v-58e2b6d0>\\n            Zwróć uwagę na dodanie przyjaznego zdjęcia profilowego (powinno\\n            być jasne i przejrzyste)\\n          </li> <li data-v-58e2b6d0>\\n            Dodaj Kursy do swojego profilu, aby zaprezentować swoją\\n            specjalistyczną wiedzę\\n          </li> <li data-v-58e2b6d0>Spraw, żeby Twój profil się wyróżniał!</li></ul> <br data-v-58e2b6d0>\\n        Możesz także udostępnić link do swojego profilu nauczyciela. Wyślij go\\n        do poprzednich uczniów, opublikuj w mediach społecznościowych lub\\n        reklamuj w kanałach lokalnych.\\n      \"):(_vm.locale === 'es')?(\"\\n        Cuando un estudiante reserva una lección de prueba, debe escribir un\\n        mensaje de introducción, que aparecerá aquí.\\n        <br data-v-58e2b6d0><br data-v-58e2b6d0>\\n        Hasta entonces, asegúrese de que su perfil docente sea lo más\\n        atractivo posible.\\n        <br data-v-58e2b6d0><br data-v-58e2b6d0> <ul class=\\\"mb-0\\\" data-v-58e2b6d0><li data-v-58e2b6d0>Agregar un video de YouTube de alta calidad</li> <li data-v-58e2b6d0>\\n            Agregue una foto de perfil de bienvenida (brillante y clara)\\n          </li> <li data-v-58e2b6d0>\\n            Agregue uno o más cursos a su perfil para mostrar su experiencia\\n          </li> <li data-v-58e2b6d0>¡Haz que tu personalidad destaque!</li></ul> <br data-v-58e2b6d0>\\n        También puede compartir un enlace a su perfil de maestro. Envíelo a\\n        los alumnos anteriores, publíquelo en las redes sociales o publíquelo\\n        en los canales locales.\\n      \"):(\"\\n        When a student books a trial lesson, they must write an intro message,\\n        which will appear here. You may also receive questions without a trial\\n        booking.\\n        <br data-v-58e2b6d0><br data-v-58e2b6d0>\\n        Until then, make sure to make your teaching profile as engaging as\\n        possible.\\n        <br data-v-58e2b6d0><br data-v-58e2b6d0> <ul class=\\\"mb-0\\\" data-v-58e2b6d0><li data-v-58e2b6d0>Add a high-quality YouTube video</li> <li data-v-58e2b6d0>Add a welcoming profile photo (bright and clear)</li> <li data-v-58e2b6d0>\\n            Add one or more Courses to your profile to showcase your expertise\\n          </li> <li data-v-58e2b6d0>Make your personality stand out!</li></ul> <br data-v-58e2b6d0>\\n        You can also share a link to your teacher profile. Send it to previous\\n        students, post it on social media, or advertise it in local channels.\\n      \"))+\" \"),(_vm.teacherSlug)?[_vm._ssrNode(\"<br data-v-58e2b6d0><br data-v-58e2b6d0> \"),(_vm.$vuetify.breakpoint.smAndUp)?[_c('nuxt-link',{attrs:{\"to\":{ path: (\"/teacher/\" + _vm.teacherSlug) }}},[_vm._v(\"\\n            \"+_vm._s(_vm.profileLink)+\"\\n          \")])]:[_vm._ssrNode(\"<div class=\\\"d-flex align-center\\\" data-v-58e2b6d0>\",\"</div>\",[_vm._ssrNode(\"<svg width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 20 20\\\" class=\\\"mr-1\\\" data-v-58e2b6d0><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#user-icon\")))+\" data-v-58e2b6d0></use></svg> <div data-v-58e2b6d0>\"+_vm._ssrEscape(_vm._s(_vm.teacherSlug))+\"</div> \"),_vm._ssrNode(\"<div class=\\\"d-flex align-center text--gradient ml-2\\\" data-v-58e2b6d0>\",\"</div>\",[_c('v-img',{staticClass:\"mr-1\",attrs:{\"src\":require('~/assets/images/copy-icon-gradient.svg'),\"width\":\"16\",\"height\":\"16\"}}),_vm._ssrNode(\" <input type=\\\"text\\\" class=\\\"d-none\\\" data-v-58e2b6d0> <div data-v-58e2b6d0>\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.$t('copy_link'))+\"\\n              \")+\"</div>\")],2)],2)]]:_vm._e()]:[(_vm.locale === 'pl')?[_vm._ssrNode(\"\\n        Wejdź na stronę\\n        \"),_c('nuxt-link',{attrs:{\"to\":\"/teacher-listing\"}},[_vm._v(\"\\\"Znajdź nauczyciela\\\"\")]),_vm._ssrNode(\", aby\\n        wybrać swojego korepetytora, zarezerwować lekcję próbną lub zapytać\\n        nauczyciela jak może pomóc Ci z osiągnięciem Twoich celów\\n        językowych.<br data-v-58e2b6d0><br data-v-58e2b6d0>\\n        Nie zapomnij odwiedzić naszej strony\\n        \"),_c('nuxt-link',{attrs:{\"to\":\"/faq\"}},[_vm._v(\"FAQ\")]),_vm._ssrNode(\", jeśli nie masz pewności, jak\\n        korzystać z Langu!\\n      \")]:(_vm.locale === 'es')?[_vm._ssrNode(\"\\n        Consulte la página\\n        \"),_c('nuxt-link',{attrs:{\"to\":\"/teacher-listing\"}},[_vm._v(\"Encontrar un Profesor\")]),_vm._ssrNode(\"\\n        para elegir un maestro, reservar una lección de prueba o hacer una\\n        pregunta sobre cómo un maestro puede ayudarlo a alcanzar sus\\n        objetivos. <br data-v-58e2b6d0><br data-v-58e2b6d0>\\n        ¡Y asegúrese de visitar nuestra página\\n        \"),_c('nuxt-link',{attrs:{\"to\":\"/faq\"}},[_vm._v(\"de preguntas frecuentes\")]),_vm._ssrNode(\" si no está\\n        seguro de cómo funciona Langu!\\n      \")]:[_vm._ssrNode(\"\\n        Check out the\\n        \"),_c('nuxt-link',{attrs:{\"to\":\"/teacher-listing\"}},[_vm._v(\"Find a Teacher\")]),_vm._ssrNode(\" page to\\n        pick a teacher, book a trial lesson, or ask a question about how a\\n        teacher can help you achieve your goals. <br data-v-58e2b6d0><br data-v-58e2b6d0>\\n        And be sure to visit our \"),_c('nuxt-link',{attrs:{\"href\":\"/faq\"}},[_vm._v(\"FAQ\")]),_vm._ssrNode(\" page\\n        if you’re not sure how Langu works!\\n      \")]]],2)],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'UserMessagesEmptyContent',\n  computed: {\n    locale() {\n      return this.$i18n.locale\n    },\n    isTeacher() {\n      return this.$store.getters['user/isTeacher']\n    },\n    teacherSlug() {\n      return this.$store.getters['user/teacherSlug']\n    },\n    profileLink() {\n      return this.teacherSlug\n        ? `${process.env.NUXT_ENV_URL}/teacher/${this.teacherSlug}`\n        : null\n    },\n  },\n  methods: {\n    copyLink() {\n      try {\n        const el = this.$refs.profileLink\n\n        el.setAttribute('value', this.profileLink)\n        el.select()\n        el.setSelectionRange(0, 99999)\n\n        navigator.clipboard.writeText(el.value)\n\n        this.$store.dispatch('snackbar/success', {\n          successMessage: 'link_copied',\n          timeout: 1500,\n        })\n      } catch (e) {\n        console.log(e)\n      }\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./EmptyContent.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./EmptyContent.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./EmptyContent.vue?vue&type=template&id=58e2b6d0&scoped=true&\"\nimport script from \"./EmptyContent.vue?vue&type=script&lang=js&\"\nexport * from \"./EmptyContent.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./EmptyContent.vue?vue&type=style&index=0&id=58e2b6d0&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"58e2b6d0\",\n  \"09c49e92\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VImg})\n", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MessagesPage.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".user-messages{--sidebar-width:280px;margin-top:10px}@media only screen and (max-width:991px){.user-messages{--sidebar-width:255px}}.user-messages-wrap{max-width:1030px}@media only screen and (min-width:768px){.user-messages-wrap{display:flex}}.user-messages-wrap>div{width:100%}.user-messages-title{font-size:24px;line-height:1.333}@media only screen and (max-width:479px){.user-messages-title{font-size:20px}}.user-messages .user-status{position:absolute;right:0;bottom:0}.user-messages-content{width:calc(100% - var(--sidebar-width));padding-left:36px}@media only screen and (max-width:991px){.user-messages-content{padding-left:20px}}@media only screen and (max-width:767px){.user-messages-content{width:100%;padding-left:0}}.user-messages-sidebar{width:var(--sidebar-width)}.user-messages-sidebar-sticky{position:sticky;top:80px}.user-messages-sidebar .user-messages-tabs-nav{overflow:hidden}.user-messages-sidebar .user-messages-tabs-nav>div{width:calc(100% + 15px);height:calc(100vh - 198px);padding-right:15px;overflow-y:scroll}.user-messages-sidebar .user-messages-tabs-nav>div .nav-item{margin-bottom:28px}@media only screen and (max-width:991px){.user-messages-sidebar .user-messages-tabs-nav>div .nav-item{margin-bottom:16px}}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn{padding:0 10px 0 26px;border-radius:20px;font-size:18px;background-color:transparent!important}@media only screen and (max-width:991px){.user-messages-sidebar .user-messages-tabs-nav>div .v-btn{padding:0 10px;font-size:16px}}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn__content{justify-content:flex-start;color:var(--v-greyDark-base);text-align:left}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn:before{transition:none!important}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn.active{background:linear-gradient(126.15deg,rgba(128,182,34,.18),rgba(60,135,248,.18) 102.93%)}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn.active .v-btn__content{color:var(--v-dark-base);font-weight:600!important}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn.active:focus:before,.user-messages-sidebar .user-messages-tabs-nav>div .v-btn.active:hover:before{display:none!important}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn:focus:before,.user-messages-sidebar .user-messages-tabs-nav>div .v-btn:hover:before{background:linear-gradient(126.15deg,rgba(128,182,34,.18),rgba(60,135,248,.18) 102.93%);opacity:.6}.user-messages-sidebar-search .v-input,.user-messages-sidebar-search .v-input.v-input--is-focused .v-input__control .v-input__slot:before{border-radius:8px!important}.user-messages-sidebar-show-more-btn{margin:14px 0}.user-messages-sidebar-show-more-btn .v-btn{padding:0 10px 0 26px!important}@media only screen and (max-width:991px){.user-messages-sidebar-show-more-btn .v-btn{padding:0 10px!important}}@media only screen and (min-width:768px){.user-messages-sidebar-show-more-btn .v-btn__content{justify-content:flex-start!important}}@media only screen and (max-width:767px){.user-messages-sidebar-show-more-btn{max-width:280px;margin-left:auto;margin-right:auto}}.user-messages .nav-item-avatar{position:relative;filter:drop-shadow(0 4px 5px rgba(0,0,0,.2));z-index:2}.user-messages .nav-item-avatar .envelop{position:absolute;top:-3px;left:-14px}@media only screen and (max-width:991px){.user-messages .nav-item-avatar .envelop{top:-2px;left:-10px}}.user-messages .tabs-mobile.v-expansion-panels{border-radius:0}.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel{margin-bottom:16px;background-color:transparent!important}.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-header{min-height:48px;padding:0 15px}@media only screen and (max-width:479px){.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-header{padding:0 10px}}.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-header>:not(.v-expansion-panel-header__icon){flex-grow:0}.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-header--active{border-radius:20px;background:linear-gradient(126.15deg,rgba(128,182,34,.18),rgba(60,135,248,.18) 102.93%)}.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-content{margin-top:12px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1);border-radius:20px}.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-content>.v-expansion-panel-content__wrap{padding:24px 16px 32px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-col',{staticClass:\"col-12 px-0\"},[_c('div',{staticClass:\"user-messages\"},[_c('v-container',{staticClass:\"pa-0\",attrs:{\"fluid\":\"\"}},[_c('v-row',{attrs:{\"no-gutters\":\"\"}},[_c('v-col',{staticClass:\"col-12\"},[_c('div',{staticClass:\"user-messages-wrap mx-auto\"},[_c('div',{staticClass:\"user-messages-title font-weight-medium mb-3 d-sm-none\"},[_vm._v(\"\\n              \"+_vm._s(_vm.$t('my_messages'))+\" 📬\\n            \")]),_vm._v(\" \"),(_vm.$vuetify.breakpoint.smAndUp)?_c('div',{staticClass:\"d-none d-sm-flex\"},[(_vm.$vuetify.breakpoint.smAndUp)?_c('aside',{staticClass:\"user-messages-sidebar d-none d-sm-block\"},[_c('div',{staticClass:\"user-messages-title font-weight-medium mb-2\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('my_messages'))+\" 📬\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"user-messages-sidebar-sticky\"},[_c('div',{staticClass:\"user-messages-sidebar-search mb-2\"},[_c('search-input',{attrs:{\"disabled\":!_vm.threadsNotEmpty && !_vm.searchQueryParam,\"placeholder\":\"search_for_recipient\",\"small\":\"\"},on:{\"submit\":_vm.searchSubmitHandler},model:{value:(_vm.searchQuery),callback:function ($$v) {_vm.searchQuery=(typeof $$v === 'string'? $$v.trim(): $$v)},expression:\"searchQuery\"}})],1),_vm._v(\" \"),(_vm.threadsNotEmpty)?_c('div',{staticClass:\"user-messages-tabs-nav\"},[_c('div',{ref:\"threadsList\",staticClass:\"py-1\"},[_vm._l((_vm.threads),function(thread,idx){return _c('div',{key:idx,staticClass:\"nav-item\",attrs:{\"id\":(\"thread-\" + (thread.id))}},[_c('v-btn',{class:[\n                            'font-weight-regular',\n                            { active: _vm.selectedThread.id === thread.id } ],attrs:{\"dark\":_vm.selectedThread.id === thread.id,\"width\":\"100%\",\"height\":\"48\"},on:{\"click\":function($event){return _vm.threadClickHandler(thread)}}},[_c('div',{staticClass:\"nav-item-avatar mr-1\"},[(!thread.isRead)?_c('v-img',{staticClass:\"envelop\",attrs:{\"src\":require('~/assets/images/envelop-icon-gradient.svg'),\"width\":\"22\",\"height\":\"22\"}}):_vm._e(),_vm._v(\" \"),_c('v-avatar',{attrs:{\"width\":\"52\",\"height\":\"52\"}},[_c('v-img',{attrs:{\"src\":_vm.getSrcAvatar(\n                                    !thread.userIsDeleted\n                                      ? thread.avatars\n                                      : {},\n                                    'user_thumb_52x52'\n                                  ),\"srcset\":_vm.getSrcSetAvatar(\n                                    !thread.userIsDeleted\n                                      ? thread.avatars\n                                      : {},\n                                    'user_thumb_52x52',\n                                    'user_thumb_104x104'\n                                  ),\"options\":{ rootMargin: '50%' }}})],1),_vm._v(\" \"),(!thread.userIsDeleted)?_c('user-status',{attrs:{\"user-id\":thread.userId,\"user-statuses\":_vm.userStatuses}}):_vm._e()],1),_vm._v(\" \"),(!thread.userIsDeleted)?[_vm._v(\"\\n                            \"+_vm._s(thread.firstName)+\" \"+_vm._s(thread.lastName)+\"\\n                          \")]:[_vm._v(\"\\n                            \"+_vm._s(_vm.$t('deleted_user'))+\"\\n                          \")]],2)],1)}),_vm._v(\" \"),(_vm.isMoreButtonShown)?_c('div',{staticClass:\"user-messages-sidebar-show-more-btn\"},[_c('load-more-btn',{attrs:{\"large\":\"\",\"text-btn\":_vm.$t('load_more_threads'),\"fetch-func\":_vm.fetchThreads}})],1):_vm._e()],2)]):_vm._e()])]):_vm._e(),_vm._v(\" \"),_c('div',{staticClass:\"user-messages-content\"},[(_vm.threadsNotEmpty)?[_c('conversation',{attrs:{\"item\":_vm.conversation,\"user-statuses\":_vm.userStatuses}})]:[_c('empty-content')]],2)]):_c('div',{staticClass:\"d-sm-none\"},[(_vm.threadsNotEmpty)?[_c('div',{staticClass:\"user-messages-sidebar-search mb-2 mb-sm-3\"},[_c('search-input',{attrs:{\"disabled\":!_vm.threadsNotEmpty && !_vm.searchQueryParam,\"placeholder\":\"search_for_recipient\",\"small\":\"\"},on:{\"submit\":_vm.searchSubmitHandler},model:{value:(_vm.searchQuery),callback:function ($$v) {_vm.searchQuery=(typeof $$v === 'string'? $$v.trim(): $$v)},expression:\"searchQuery\"}})],1),_vm._v(\" \"),_c('client-only',[_c('v-expansion-panels',{staticClass:\"tabs-mobile\",attrs:{\"accordion\":\"\",\"flat\":\"\"},model:{value:(_vm.tabActive),callback:function ($$v) {_vm.tabActive=$$v},expression:\"tabActive\"}},_vm._l((_vm.threads),function(thread,idx){return _c('v-expansion-panel',{key:idx},[_c('v-expansion-panel-header',{ref:(\"panel-\" + idx),refInFor:true,attrs:{\"disable-icon-rotate\":\"\"},on:{\"click\":function($event){return _vm.threadClickHandler(thread)}},scopedSlots:_vm._u([{key:\"actions\",fn:function(){return [(_vm.tabActive === idx)?[_c('v-icon',{attrs:{\"color\":\"dark\"}},[_vm._v(\"\\n                              \"+_vm._s(_vm.mdiMinus)+\"\\n                            \")])]:[_c('v-img',{attrs:{\"src\":require('~/assets/images/add-icon-gradient.svg'),\"width\":\"24\",\"height\":\"24\"}})]]},proxy:true}],null,true)},[_c('div',{staticClass:\"nav-item-avatar mr-1\"},[(!thread.isRead)?_c('v-img',{staticClass:\"envelop\",attrs:{\"src\":require('~/assets/images/envelop-icon-gradient.svg'),\"width\":\"22\",\"height\":\"22\"}}):_vm._e(),_vm._v(\" \"),_c('v-avatar',{attrs:{\"width\":\"48\",\"height\":\"48\"}},[_c('v-img',{attrs:{\"src\":_vm.getSrcAvatar(\n                                  !thread.userIsDeleted ? thread.avatars : {},\n                                  'user_thumb_52x52'\n                                ),\"srcset\":_vm.getSrcSetAvatar(\n                                  !thread.userIsDeleted ? thread.avatars : {},\n                                  'user_thumb_52x52',\n                                  'user_thumb_104x104'\n                                ),\"options\":{ rootMargin: '50%' }}})],1),_vm._v(\" \"),(!thread.userIsDeleted)?_c('user-status',{attrs:{\"user-id\":thread.userId,\"user-statuses\":_vm.userStatuses}}):_vm._e()],1),_vm._v(\" \"),(!thread.userIsDeleted)?[_vm._v(\"\\n                          \"+_vm._s(thread.firstName)+\" \"+_vm._s(thread.lastName)+\"\\n                        \")]:[_vm._v(\"\\n                          \"+_vm._s(_vm.$t('deleted_user'))+\"\\n                        \")]],2),_vm._v(\" \"),_c('v-expansion-panel-content',[_c('conversation',{attrs:{\"item\":_vm.conversation,\"user-statuses\":_vm.userStatuses}})],1)],1)}),1)],1),_vm._v(\" \"),(_vm.isMoreButtonShown)?_c('div',{staticClass:\"user-messages-sidebar-show-more-btn mt-1\"},[_c('load-more-btn',{attrs:{\"large\":\"\",\"text-btn\":_vm.$t('load_more_threads'),\"fetch-func\":_vm.fetchThreads}})],1):_vm._e()]:[_c('empty-content')]],2)])])],1)],1)],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mdiMinus } from '@mdi/js'\nimport Avatars from '~/mixins/Avatars'\nimport StatusOnline from '~/mixins/StatusOnline'\nimport SearchInput from '~/components/form/SearchInput'\nimport UserStatus from '~/components/UserStatus'\nimport LoadMoreBtn from '~/components/LoadMoreBtn'\nimport Conversation from '~/components/user-messages/Conversation'\nimport EmptyContent from '~/components/user-messages/EmptyContent'\n\nexport default {\n  name: 'MessagesPage',\n  components: {\n    SearchInput,\n    UserStatus,\n    LoadMoreBtn,\n    Conversation,\n    EmptyContent,\n  },\n  mixins: [Avatars, StatusOnline],\n  props: {\n    totalQuantity: {\n      type: Number,\n      required: true,\n    },\n    additionalUser: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  data() {\n    return {\n      mdiMinus,\n      tabActive: null,\n      searchQuery: '',\n      threadsPage: 1,\n    }\n  },\n  computed: {\n    threads() {\n      return this.$store.state.message.items\n    },\n    threadsNotEmpty() {\n      return !!this.threads.length\n    },\n    selectedThread() {\n      return this.$store.state.message.item\n    },\n    conversation() {\n      return this.$store.state.message.conversation\n    },\n    totalPages() {\n      return Math.ceil(\n        this.totalQuantity / process.env.NUXT_ENV_THREADS_PER_PAGE\n      )\n    },\n    isMoreButtonShown() {\n      return this.totalPages > 1 && this.threadsPage < this.totalPages\n    },\n    searchQueryParam() {\n      return this.$route.query?.search ?? ''\n    },\n    userId() {\n      return this.$store.state.user.item?.id\n    },\n  },\n  watch: {\n    '$route.params.search': {\n      handler() {\n        this.threadsPage = 1\n        this.searchQuery = this.searchQueryParam\n\n        this.setArrStatusId()\n      },\n      deep: true,\n    },\n    'threads.length'() {\n      this.setArrStatusId()\n\n      this.tabActive = null\n    },\n    tabActive(newValue, oldValue) {\n      if (newValue != null && this.$vuetify.breakpoint.xsOnly) {\n        const el = this.$refs[`panel-${this.tabActive}`][0].$el\n\n        if (el) {\n          this.$nextTick(() => {\n            window.setTimeout(() => {\n              this.$vuetify.goTo(el, {\n                duration: 0,\n                offset: 10,\n                easing: 'linear',\n              })\n            }, 400)\n          })\n        }\n      }\n    },\n  },\n  beforeMount() {\n    this.searchQuery = this.searchQueryParam\n\n    this.setArrStatusId()\n    this.refreshStatusOnline()\n  },\n  methods: {\n    setArrStatusId() {\n      this.arrStatusId = this.threads\n        .filter((item) => !item.userIsDeleted)\n        .map((item) => item.userId)\n\n      if (this.userId) {\n        this.arrStatusId.push(this.userId)\n      }\n\n      if (this.additionalUser) {\n        this.arrStatusId.push(this.conversation.userId)\n      }\n    },\n    searchSubmitHandler() {\n      this.$router.push({\n        name: this.$route.name,\n        query: this.searchQuery ? { search: this.searchQuery } : {},\n      })\n    },\n    threadClickHandler(thread) {\n      this.$store\n        .dispatch('message/getConversation', {\n          threadId: thread.id,\n        })\n        .then(() => {\n          this.$store.commit('message/SET_ITEM', thread)\n        })\n    },\n    async fetchThreads() {\n      this.threadsPage++\n\n      await this.$store.dispatch('loadingAllow', false)\n      await this.$store\n        .dispatch('message/getItems', { page: this.threadsPage })\n        .then(() => {\n          this.$nextTick(() => {\n            const lastItem = this.threads[\n              (this.threadsPage - 1) * process.env.NUXT_ENV_THREADS_PER_PAGE\n            ]\n            const lastEl = document.getElementById(`thread-${lastItem.id}`)\n\n            if (lastEl) {\n              this.$refs.threadsList.scrollTo({\n                top: lastEl.offsetTop - 76,\n                behavior: 'smooth',\n              })\n            }\n          })\n        })\n      await this.$store.dispatch('loadingAllow', true)\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MessagesPage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./MessagesPage.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./MessagesPage.vue?vue&type=template&id=a1a71ef0&\"\nimport script from \"./MessagesPage.vue?vue&type=script&lang=js&\"\nexport * from \"./MessagesPage.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./MessagesPage.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"147f6aa1\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {UserStatus: require('D:/languworks/langu-frontend/components/UserStatus.vue').default,LoadMoreBtn: require('D:/languworks/langu-frontend/components/LoadMoreBtn.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VAvatar } from 'vuetify/lib/components/VAvatar';\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VContainer } from 'vuetify/lib/components/VGrid';\nimport { VExpansionPanel } from 'vuetify/lib/components/VExpansionPanel';\nimport { VExpansionPanelContent } from 'vuetify/lib/components/VExpansionPanel';\nimport { VExpansionPanelHeader } from 'vuetify/lib/components/VExpansionPanel';\nimport { VExpansionPanels } from 'vuetify/lib/components/VExpansionPanel';\nimport { VIcon } from 'vuetify/lib/components/VIcon';\nimport { VImg } from 'vuetify/lib/components/VImg';\nimport { VRow } from 'vuetify/lib/components/VGrid';\ninstallComponents(component, {VAvatar,VBtn,VCol,VContainer,VExpansionPanel,VExpansionPanelContent,VExpansionPanelHeader,VExpansionPanels,VIcon,VImg,VRow})\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('messages-page',{attrs:{\"total-quantity\":_vm.count}})}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n\nimport MessagesPage from '~/components/user-messages/MessagesPage'\n\nexport default {\n  name: 'UserMessages',\n  components: { MessagesPage },\n  middleware: 'authenticated',\n  async asyncData({ store, query }) {\n    const searchQuery = query?.search\n    let count\n\n    await store\n      .dispatch('message/getItems', { page: 1, searchQuery })\n      .then(async (data) => {\n        count = data?.count ?? 0\n\n        if (data?.threads?.length) {\n          store.commit('message/SET_ITEM', data.threads[0])\n          await store.dispatch('message/getConversation', {\n            threadId: data.threads[0].id,\n          })\n        }\n      })\n\n    return { count }\n  },\n  head() {\n    return {\n      title: this.$t('user_messages_page.seo_title'),\n      meta: [\n        {\n          hid: 'description',\n          name: 'description',\n          content: this.$t('user_messages_page.seo_description'),\n        },\n        {\n          hid: 'og:title',\n          name: 'og:title',\n          property: 'og:title',\n          content: this.$t('user_messages_page.seo_title'),\n        },\n        {\n          property: 'og:description',\n          content: this.$t('user_messages_page.seo_description'),\n        },\n      ],\n      bodyAttrs: {\n        class: `${this.locale} user-messages-page`,\n      },\n    }\n  },\n  computed: {\n    locale() {\n      return this.$i18n.locale\n    },\n  },\n  watchQuery: true,\n}\n", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=c524a18a&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"15a1e166\"\n  \n)\n\nexport default component.exports", "import VTextField from './VTextField'\n\nexport { VTextField }\nexport default VTextField\n", "// Styles\nimport './VChip.sass'\n\n// Types\nimport { VNode } from 'vue'\nimport mixins from '../../util/mixins'\n\n// Components\nimport { VExpandXTransition } from '../transitions'\nimport VIcon from '../VIcon'\n\n// Mixins\nimport Colorable from '../../mixins/colorable'\nimport { factory as GroupableFactory } from '../../mixins/groupable'\nimport Themeable from '../../mixins/themeable'\nimport { factory as ToggleableFactory } from '../../mixins/toggleable'\nimport Routable from '../../mixins/routable'\nimport Sizeable from '../../mixins/sizeable'\n\n// Utilities\nimport { breaking } from '../../util/console'\n\n// Types\nimport { PropValidator, PropType } from 'vue/types/options'\n\n/* @vue/component */\nexport default mixins(\n  Colorable,\n  Sizeable,\n  Routable,\n  Themeable,\n  GroupableFactory('chipGroup'),\n  ToggleableFactory('inputValue')\n).extend({\n  name: 'v-chip',\n\n  props: {\n    active: {\n      type: Boolean,\n      default: true,\n    },\n    activeClass: {\n      type: String,\n      default (): string | undefined {\n        if (!this.chipGroup) return ''\n\n        return this.chipGroup.activeClass\n      },\n    } as any as PropValidator<string>,\n    close: Boolean,\n    closeIcon: {\n      type: String,\n      default: '$delete',\n    },\n    closeLabel: {\n      type: String,\n      default: '$vuetify.close',\n    },\n    disabled: Boolean,\n    draggable: Boolean,\n    filter: Boolean,\n    filterIcon: {\n      type: String,\n      default: '$complete',\n    },\n    label: Boolean,\n    link: Boolean,\n    outlined: Boolean,\n    pill: Boolean,\n    tag: {\n      type: String,\n      default: 'span',\n    },\n    textColor: String,\n    value: null as any as PropType<any>,\n  },\n\n  data: () => ({\n    proxyClass: 'v-chip--active',\n  }),\n\n  computed: {\n    classes (): object {\n      return {\n        'v-chip': true,\n        ...Routable.options.computed.classes.call(this),\n        'v-chip--clickable': this.isClickable,\n        'v-chip--disabled': this.disabled,\n        'v-chip--draggable': this.draggable,\n        'v-chip--label': this.label,\n        'v-chip--link': this.isLink,\n        'v-chip--no-color': !this.color,\n        'v-chip--outlined': this.outlined,\n        'v-chip--pill': this.pill,\n        'v-chip--removable': this.hasClose,\n        ...this.themeClasses,\n        ...this.sizeableClasses,\n        ...this.groupClasses,\n      }\n    },\n    hasClose (): boolean {\n      return Boolean(this.close)\n    },\n    isClickable (): boolean {\n      return Boolean(\n        Routable.options.computed.isClickable.call(this) ||\n        this.chipGroup\n      )\n    },\n  },\n\n  created () {\n    const breakingProps = [\n      ['outline', 'outlined'],\n      ['selected', 'input-value'],\n      ['value', 'active'],\n      ['@input', '@active.sync'],\n    ]\n\n    /* istanbul ignore next */\n    breakingProps.forEach(([original, replacement]) => {\n      if (this.$attrs.hasOwnProperty(original)) breaking(original, replacement, this)\n    })\n  },\n\n  methods: {\n    click (e: MouseEvent): void {\n      this.$emit('click', e)\n\n      this.chipGroup && this.toggle()\n    },\n    genFilter (): VNode {\n      const children = []\n\n      if (this.isActive) {\n        children.push(\n          this.$createElement(VIcon, {\n            staticClass: 'v-chip__filter',\n            props: { left: true },\n          }, this.filterIcon)\n        )\n      }\n\n      return this.$createElement(VExpandXTransition, children)\n    },\n    genClose (): VNode {\n      return this.$createElement(VIcon, {\n        staticClass: 'v-chip__close',\n        props: {\n          right: true,\n          size: 18,\n        },\n        attrs: {\n          'aria-label': this.$vuetify.lang.t(this.closeLabel),\n        },\n        on: {\n          click: (e: Event) => {\n            e.stopPropagation()\n            e.preventDefault()\n\n            this.$emit('click:close')\n            this.$emit('update:active', false)\n          },\n        },\n      }, this.closeIcon)\n    },\n    genContent (): VNode {\n      return this.$createElement('span', {\n        staticClass: 'v-chip__content',\n      }, [\n        this.filter && this.genFilter(),\n        this.$slots.default,\n        this.hasClose && this.genClose(),\n      ])\n    },\n  },\n\n  render (h): VNode {\n    const children = [this.genContent()]\n    let { tag, data } = this.generateRouteLink()\n\n    data.attrs = {\n      ...data.attrs,\n      draggable: this.draggable ? 'true' : undefined,\n      tabindex: this.chipGroup && !this.disabled ? 0 : data.attrs!.tabindex,\n    }\n    data.directives!.push({\n      name: 'show',\n      value: this.active,\n    })\n    data = this.setBackgroundColor(this.color, data)\n\n    const color = this.textColor || (this.outlined && this.color)\n\n    return h(tag, this.setTextColor(color, data), children)\n  },\n})\n", "// Styles\nimport './VItemGroup.sass'\n\n// Mixins\nimport Groupable from '../../mixins/groupable'\nimport Proxyable from '../../mixins/proxyable'\nimport Themeable from '../../mixins/themeable'\n\n// Utilities\nimport mixins from '../../util/mixins'\nimport { consoleWarn } from '../../util/console'\n\n// Types\nimport { VNode } from 'vue/types'\n\nexport type GroupableInstance = InstanceType<typeof Groupable> & {\n  id?: string\n  to?: any\n  value?: any\n }\n\nexport const BaseItemGroup = mixins(\n  Proxyable,\n  Themeable\n).extend({\n  name: 'base-item-group',\n\n  props: {\n    activeClass: {\n      type: String,\n      default: 'v-item--active',\n    },\n    mandatory: Boolean,\n    max: {\n      type: [Number, String],\n      default: null,\n    },\n    multiple: Boolean,\n    tag: {\n      type: String,\n      default: 'div',\n    },\n  },\n\n  data () {\n    return {\n      // As long as a value is defined, show it\n      // Otherwise, check if multiple\n      // to determine which default to provide\n      internalLazyValue: this.value !== undefined\n        ? this.value\n        : this.multiple ? [] : undefined,\n      items: [] as GroupableInstance[],\n    }\n  },\n\n  computed: {\n    classes (): Record<string, boolean> {\n      return {\n        'v-item-group': true,\n        ...this.themeClasses,\n      }\n    },\n    selectedIndex (): number {\n      return (this.selectedItem && this.items.indexOf(this.selectedItem)) || -1\n    },\n    selectedItem (): GroupableInstance | undefined {\n      if (this.multiple) return undefined\n\n      return this.selectedItems[0]\n    },\n    selectedItems (): GroupableInstance[] {\n      return this.items.filter((item, index) => {\n        return this.toggleMethod(this.getValue(item, index))\n      })\n    },\n    selectedValues (): any[] {\n      if (this.internalValue == null) return []\n\n      return Array.isArray(this.internalValue)\n        ? this.internalValue\n        : [this.internalValue]\n    },\n    toggleMethod (): (v: any) => boolean {\n      if (!this.multiple) {\n        return (v: any) => this.internalValue === v\n      }\n\n      const internalValue = this.internalValue\n      if (Array.isArray(internalValue)) {\n        return (v: any) => internalValue.includes(v)\n      }\n\n      return () => false\n    },\n  },\n\n  watch: {\n    internalValue: 'updateItemsState',\n    items: 'updateItemsState',\n  },\n\n  created () {\n    if (this.multiple && !Array.isArray(this.internalValue)) {\n      consoleWarn('Model must be bound to an array if the multiple property is true.', this)\n    }\n  },\n\n  methods: {\n\n    genData (): object {\n      return {\n        class: this.classes,\n      }\n    },\n    getValue (item: GroupableInstance, i: number): unknown {\n      return item.value == null || item.value === ''\n        ? i\n        : item.value\n    },\n    onClick (item: GroupableInstance) {\n      this.updateInternalValue(\n        this.getValue(item, this.items.indexOf(item))\n      )\n    },\n    register (item: GroupableInstance) {\n      const index = this.items.push(item) - 1\n\n      item.$on('change', () => this.onClick(item))\n\n      // If no value provided and mandatory,\n      // assign first registered item\n      if (this.mandatory && !this.selectedValues.length) {\n        this.updateMandatory()\n      }\n\n      this.updateItem(item, index)\n    },\n    unregister (item: GroupableInstance) {\n      if (this._isDestroyed) return\n\n      const index = this.items.indexOf(item)\n      const value = this.getValue(item, index)\n\n      this.items.splice(index, 1)\n\n      const valueIndex = this.selectedValues.indexOf(value)\n\n      // Items is not selected, do nothing\n      if (valueIndex < 0) return\n\n      // If not mandatory, use regular update process\n      if (!this.mandatory) {\n        return this.updateInternalValue(value)\n      }\n\n      // Remove the value\n      if (this.multiple && Array.isArray(this.internalValue)) {\n        this.internalValue = this.internalValue.filter(v => v !== value)\n      } else {\n        this.internalValue = undefined\n      }\n\n      // If mandatory and we have no selection\n      // add the last item as value\n      /* istanbul ignore else */\n      if (!this.selectedItems.length) {\n        this.updateMandatory(true)\n      }\n    },\n    updateItem (item: GroupableInstance, index: number) {\n      const value = this.getValue(item, index)\n\n      item.isActive = this.toggleMethod(value)\n    },\n    // https://github.com/vuetifyjs/vuetify/issues/5352\n    updateItemsState () {\n      this.$nextTick(() => {\n        if (this.mandatory &&\n          !this.selectedItems.length\n        ) {\n          return this.updateMandatory()\n        }\n\n        // TODO: Make this smarter so it\n        // doesn't have to iterate every\n        // child in an update\n        this.items.forEach(this.updateItem)\n      })\n    },\n    updateInternalValue (value: any) {\n      this.multiple\n        ? this.updateMultiple(value)\n        : this.updateSingle(value)\n    },\n    updateMandatory (last?: boolean) {\n      if (!this.items.length) return\n\n      const items = this.items.slice()\n\n      if (last) items.reverse()\n\n      const item = items.find(item => !item.disabled)\n\n      // If no tabs are available\n      // aborts mandatory value\n      if (!item) return\n\n      const index = this.items.indexOf(item)\n\n      this.updateInternalValue(\n        this.getValue(item, index)\n      )\n    },\n    updateMultiple (value: any) {\n      const defaultValue = Array.isArray(this.internalValue)\n        ? this.internalValue\n        : []\n      const internalValue = defaultValue.slice()\n      const index = internalValue.findIndex(val => val === value)\n\n      if (\n        this.mandatory &&\n        // Item already exists\n        index > -1 &&\n        // value would be reduced below min\n        internalValue.length - 1 < 1\n      ) return\n\n      if (\n        // Max is set\n        this.max != null &&\n        // Item doesn't exist\n        index < 0 &&\n        // value would be increased above max\n        internalValue.length + 1 > this.max\n      ) return\n\n      index > -1\n        ? internalValue.splice(index, 1)\n        : internalValue.push(value)\n\n      this.internalValue = internalValue\n    },\n    updateSingle (value: any) {\n      const isSame = value === this.internalValue\n\n      if (this.mandatory && isSame) return\n\n      this.internalValue = isSame ? undefined : value\n    },\n  },\n\n  render (h): VNode {\n    return h(this.tag, this.genData(), this.$slots.default)\n  },\n})\n\nexport default BaseItemGroup.extend({\n  name: 'v-item-group',\n\n  provide (): object {\n    return {\n      itemGroup: this,\n    }\n  },\n})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./VItemGroup.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"73707fd0\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v-item-group{flex:0 1 auto;position:relative;max-width:100%;transition:.3s cubic-bezier(.25,.8,.5,1)}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./VChip.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"197fcea4\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v-chip:not(.v-chip--outlined).accent,.v-chip:not(.v-chip--outlined).error,.v-chip:not(.v-chip--outlined).info,.v-chip:not(.v-chip--outlined).primary,.v-chip:not(.v-chip--outlined).secondary,.v-chip:not(.v-chip--outlined).success,.v-chip:not(.v-chip--outlined).warning{color:#fff}.theme--light.v-chip{border-color:rgba(0,0,0,.12);color:rgba(0,0,0,.87)}.theme--light.v-chip:not(.v-chip--active){background:#e0e0e0}.theme--light.v-chip:hover:before{opacity:.04}.theme--light.v-chip--active:before,.theme--light.v-chip--active:hover:before,.theme--light.v-chip:focus:before{opacity:.12}.theme--light.v-chip--active:focus:before{opacity:.16}.theme--dark.v-chip{border-color:hsla(0,0%,100%,.12);color:#fff}.theme--dark.v-chip:not(.v-chip--active){background:#555}.theme--dark.v-chip:hover:before{opacity:.08}.theme--dark.v-chip--active:before,.theme--dark.v-chip--active:hover:before,.theme--dark.v-chip:focus:before{opacity:.24}.theme--dark.v-chip--active:focus:before{opacity:.32}.v-chip{align-items:center;cursor:default;display:inline-flex;line-height:20px;max-width:100%;outline:none;overflow:hidden;padding:0 12px;position:relative;text-decoration:none;transition-duration:.28s;transition-property:box-shadow,opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);vertical-align:middle;white-space:nowrap}.v-chip:before{background-color:currentColor;bottom:0;border-radius:inherit;content:\\\"\\\";left:0;opacity:0;position:absolute;pointer-events:none;right:0;top:0}.v-chip .v-avatar{height:24px!important;min-width:24px!important;width:24px!important}.v-chip .v-icon{font-size:24px}.v-application--is-ltr .v-chip .v-avatar--left,.v-application--is-ltr .v-chip .v-icon--left{margin-left:-6px;margin-right:6px}.v-application--is-ltr .v-chip .v-avatar--right,.v-application--is-ltr .v-chip .v-icon--right,.v-application--is-rtl .v-chip .v-avatar--left,.v-application--is-rtl .v-chip .v-icon--left{margin-left:6px;margin-right:-6px}.v-application--is-rtl .v-chip .v-avatar--right,.v-application--is-rtl .v-chip .v-icon--right{margin-left:-6px;margin-right:6px}.v-chip:not(.v-chip--no-color) .v-icon{color:inherit}.v-chip .v-chip__close.v-icon{font-size:18px;max-height:18px;max-width:18px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right{margin-right:-4px}.v-application--is-rtl .v-chip .v-chip__close.v-icon.v-icon--right{margin-left:-4px}.v-chip .v-chip__close.v-icon:active,.v-chip .v-chip__close.v-icon:focus,.v-chip .v-chip__close.v-icon:hover{opacity:.72}.v-chip .v-chip__content{align-items:center;display:inline-flex;height:100%;max-width:100%}.v-chip--active .v-icon{color:inherit}.v-chip--link:before{transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-chip--link:focus:before{opacity:.32}.v-chip--clickable{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip--clickable:active{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-chip--disabled{opacity:.4;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip__filter{max-width:24px}.v-chip__filter.v-icon{color:inherit}.v-chip__filter.expand-x-transition-enter,.v-chip__filter.expand-x-transition-leave-active{margin:0}.v-chip--pill .v-chip__filter{margin-right:0 16px 0 0}.v-chip--pill .v-avatar{height:32px!important;width:32px!important}.v-application--is-ltr .v-chip--pill .v-avatar--left{margin-left:-12px}.v-application--is-ltr .v-chip--pill .v-avatar--right,.v-application--is-rtl .v-chip--pill .v-avatar--left{margin-right:-12px}.v-application--is-rtl .v-chip--pill .v-avatar--right{margin-left:-12px}.v-chip--label{border-radius:4px!important}.v-chip.v-chip--outlined{border-width:thin;border-style:solid}.v-chip.v-chip--outlined.v-chip--active:before{opacity:.08}.v-chip.v-chip--outlined .v-icon{color:inherit}.v-chip.v-chip--outlined.v-chip.v-chip{background-color:transparent!important}.v-chip.v-chip--selected{background:transparent}.v-chip.v-chip--selected:after{opacity:.28}.v-chip.v-size--x-small{border-radius:8px;font-size:10px;height:16px}.v-chip.v-size--small{border-radius:12px;font-size:12px;height:24px}.v-chip.v-size--default{border-radius:16px;font-size:14px;height:32px}.v-chip.v-size--large{border-radius:27px;font-size:16px;height:54px}.v-chip.v-size--x-large{border-radius:33px;font-size:18px;height:66px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Editor.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"a98bb618\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[\n    'user-status',\n    (\"user-status--\" + _vm.status),\n    { 'user-status--large': _vm.large } ]},[])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'UserStatus',\n  props: {\n    userId: {\n      type: Number,\n      default: 0,\n    },\n    large: {\n      type: Boolean,\n      default: false,\n    },\n    userStatuses: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  computed: {\n    status() {\n      let status = 'offline'\n\n      if (\n        Object.prototype.hasOwnProperty.call(\n          this.userStatuses,\n          this.userId?.toString()\n        )\n      ) {\n        status = this.userStatuses[this.userId]\n      }\n\n      return status\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserStatus.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserStatus.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./UserStatus.vue?vue&type=template&id=652352c7&scoped=true&\"\nimport script from \"./UserStatus.vue?vue&type=script&lang=js&\"\nexport * from \"./UserStatus.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./UserStatus.vue?vue&type=style&index=0&id=652352c7&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"652352c7\",\n  \"4c070a35\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SearchInput.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"637a1dfc\", content, true, context)\n};", "\nexport default {\n  methods: {\n    getSrcAvatar(images, property, defaultImage = 'avatar.png') {\n      return images?.[property]\n        ? images[property]\n        : require(`~/assets/images/homepage/${defaultImage}`)\n    },\n    getSrcSetAvatar(images, property1, property2) {\n      return images?.[property1] && images?.[property2]\n        ? `\n            ${images[property1]} 1x,\n            ${images[property2]} 2x,\n          `\n        : ''\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Avatars.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Avatars.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./Avatars.vue?vue&type=script&lang=js&\"\nexport * from \"./Avatars.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"0af9ff4e\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserStatus.vue?vue&type=style&index=0&id=652352c7&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"006007e9\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Editor.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".text-editor{position:relative}.text-editor-buttons{position:absolute;right:18px;top:8px;z-index:2}.text-editor-buttons button{display:inline-flex;justify-content:center;align-items:center;width:24px;height:24px;margin-left:8px;border-radius:2px;border:1px solid transparent}.text-editor-buttons button.is-active{background-color:var(--v-greyBg-base);border-color:var(--v-greyLight-base)}.text-editor .ProseMirror{min-height:280px;margin-bottom:4px;padding:40px 12px 12px;border:1px solid #bebebe;font-size:13px;border-radius:16px;line-height:1.23}.text-editor .ProseMirror>*{position:relative}.text-editor .ProseMirror p{margin-bottom:0}.text-editor .ProseMirror ul{padding-left:28px}.text-editor .ProseMirror ul>li p{margin-bottom:0}.text-editor .ProseMirror strong{font-weight:700!important}.text-editor .ProseMirror.focus-visible,.text-editor .ProseMirror:focus,.text-editor .ProseMirror:focus-visible{outline:none!important}.text-editor .ProseMirror-focused:before{content:\\\"\\\";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:16px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}.text-editor .v-text-field__details{padding:0 14px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"text-editor\"},[_vm._ssrNode(((_vm.editor)?(\"<div class=\\\"text-editor-buttons\\\"><button\"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bold') }))+\"><svg width=\\\"16\\\" height=\\\"16\\\" viewBox=\\\"0 0 16 16\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#editor-bold-icon\")))+\"></use></svg></button> <button\"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bulletList') }))+\"><svg width=\\\"16\\\" height=\\\"16\\\" viewBox=\\\"0 0 16 16\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#editor-list-icon\")))+\"></use></svg></button></div>\"):\"<!---->\")+\" \"),_c('editor-content',{attrs:{\"editor\":_vm.editor}}),_vm._ssrNode(\" \"+((_vm.counter)?(\"<div class=\\\"v-text-field__details\\\"><div class=\\\"v-messages theme--light\\\"><div class=\\\"v-messages__wrapper\\\"></div></div> <div\"+(_vm._ssrClass(null,[\n        'v-counter theme--light',\n        { 'error--text': !_vm.isValid && _vm.isDirty } ]))+\">\"+_vm._ssrEscape(\"\\n      \"+_vm._s(_vm.text.length)+\" / \"+_vm._s(_vm.limit)+\"\\n    \")+\"</div></div>\"):\"<!---->\"))],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { Editor, EditorContent } from '@tiptap/vue-2'\nimport StarterKit from '@tiptap/starter-kit'\nimport CharacterCount from '@tiptap/extension-character-count'\nimport Link from '@tiptap/extension-link'\n\nexport default {\n  name: 'Editor',\n  components: {\n    EditorContent,\n  },\n  props: {\n    value: {\n      type: String,\n      required: true,\n    },\n    counter: {\n      type: Boolean,\n      default: false,\n    },\n    autoLink: {\n      type: Boolean,\n      default: false,\n    },\n    limit: {\n      type: Number,\n      default: null,\n    },\n  },\n  data() {\n    return {\n      editor: null,\n      text: '',\n      isValid: true,\n      editorEl: null,\n      keysPressed: {},\n      isDirty: false,\n    }\n  },\n  watch: {\n    value(value) {\n      const isSame = this.editor.getHTML() === value\n\n      if (isSame) {\n        return\n      }\n\n      this.editor.commands.setContent(value, false)\n    },\n  },\n  mounted() {\n    this.editor = new Editor({\n      content: this.value,\n      extensions: [\n        StarterKit,\n        CharacterCount.configure({\n          limit: this.limit,\n        }),\n        Link.configure({\n          autolink: true,\n        }),\n      ],\n    })\n\n    this.editor.on('create', ({ editor }) => {\n      this.text = editor.getText()\n\n      this.$nextTick(() => {\n        this.editorEl = document.getElementsByClassName('ProseMirror')[0]\n\n        if (this.editorEl) {\n          this.editorEl.addEventListener('keydown', this.keydownHandler)\n          this.editorEl.addEventListener('keyup', this.keyupHandler)\n        }\n      })\n\n      this.validation()\n    })\n\n    this.editor.on('update', ({ editor }) => {\n      this.isDirty = true\n      this.text = editor.getText()\n\n      this.validation()\n      this.$emit('update', this.text ? editor.getHTML() : '')\n    })\n  },\n  beforeDestroy() {\n    if (this.editorEl) {\n      this.editorEl.removeEventListener('keydown', this.keydownHandler)\n      this.editorEl.removeEventListener('keyup', this.keyupHandler)\n    }\n\n    this.editor.destroy()\n  },\n  methods: {\n    keydownHandler(e) {\n      this.keysPressed[e.keyCode] = true\n\n      if (\n        (e.ctrlKey ||\n          this.keysPressed[17] ||\n          this.keysPressed[91] ||\n          this.keysPressed[93] ||\n          this.keysPressed[224]) &&\n        this.keysPressed[13]\n      ) {\n        e.preventDefault()\n\n        this.$emit('submit')\n\n        this.keysPressed = {}\n      } else if (e.keyCode === 13 && !e.shiftKey) {\n        e.preventDefault()\n        this.editor.commands.enter()\n      }\n    },\n    keyupHandler(e) {\n      delete this.keysPressed[e.keyCode]\n    },\n    validation() {\n      const strLength = this.text.trim().length\n\n      this.isValid = !!strLength\n\n      if (!!strLength && this.limit) {\n        this.isValid = strLength <= this.limit\n      }\n\n      this.$emit('validation', this.isValid)\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Editor.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Editor.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Editor.vue?vue&type=template&id=23b137ee&\"\nimport script from \"./Editor.vue?vue&type=script&lang=js&\"\nexport * from \"./Editor.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./Editor.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"0bb70d5d\"\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-form',{on:{\"submit\":function($event){$event.preventDefault();return _vm.submit.apply(null, arguments)}}},[_c('text-input',{class:['search-input', { 'search-input--small': _vm.small }],attrs:{\"value\":_vm.value,\"type-class\":\"border-gradient\",\"hide-details\":\"\",\"disabled\":_vm.disabled,\"placeholder\":_vm.$t(_vm.placeholder)},on:{\"input\":function($event){return _vm.$emit('input', $event)}},scopedSlots:_vm._u([{key:\"append\",fn:function(){return [_c('div',{staticStyle:{\"margin-top\":\"6px\",\"cursor\":\"pointer\"},on:{\"click\":_vm.submit}},[_c('v-img',{attrs:{\"src\":require('~/assets/images/search-icon.svg')}})],1)]},proxy:true}])})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport TextInput from '~/components/form/TextInput'\n\nexport default {\n  name: 'SearchInput',\n  components: { TextInput },\n  props: {\n    value: {\n      type: String,\n      default: '',\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    placeholder: {\n      type: String,\n      required: true,\n    },\n    small: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  methods: {\n    submit() {\n      this.$emit('submit')\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SearchInput.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SearchInput.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./SearchInput.vue?vue&type=template&id=8bfec74e&\"\nimport script from \"./SearchInput.vue?vue&type=script&lang=js&\"\nexport * from \"./SearchInput.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./SearchInput.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"86c5c87c\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VForm } from 'vuetify/lib/components/VForm';\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VForm,VImg})\n", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SearchInput.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".search-input .v-input{background-color:#fff;border-radius:50px!important;box-shadow:0 4px 14px rgba(217,225,236,.47)}@media only screen and (max-width:767px){.search-input .v-input{border-radius:10px!important}}.search-input .v-input input::-moz-placeholder{color:var(--v-greyLight-darken2)}.search-input .v-input input:-ms-input-placeholder{color:var(--v-greyLight-darken2)}.search-input .v-input input::placeholder{color:var(--v-greyLight-darken2)}.search-input .v-input .v-input__control>.v-input__slot{height:56px!important}@media only screen and (max-width:767px){.search-input .v-input .v-input__control>.v-input__slot{height:40px!important}}.search-input .v-input .v-input__append-inner{margin-top:9px!important}@media only screen and (max-width:767px){.search-input .v-input .v-input__append-inner{margin-top:6px!important}}.search-input .v-input .v-input__append-inner .v-image{width:26px!important;height:26px!important}@media only screen and (max-width:767px){.search-input .v-input .v-input__append-inner .v-image{width:20px!important;height:20px!important}}.search-input .v-input.v-input--is-focused .v-input__control>.v-input__slot:before{border-radius:16px!important}.search-input .v-input.v-input.v-text-field--outlined fieldset{border-color:transparent!important;box-shadow:0 4px 14px rgba(217,225,236,.47)}.search-input--inner-border .v-input .v-input__control>.v-input__slot{padding-top:5px!important;padding-left:5px!important;padding-bottom:5px!important}.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot{position:relative;padding:0 16px;background-color:transparent!important}@media only screen and (max-width:1215px){.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot{padding:0 12px}}@media only screen and (max-width:767px){.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot{padding:0 10px}}.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot:before{display:none!important;content:\\\"\\\";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:15px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}@media only screen and (max-width:767px){.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot:before{border-radius:9px}}.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot input{position:relative;z-index:2}.search-input--inner-border .v-input .v-input__append-inner{margin-top:4px!important;padding-left:15px}@media only screen and (max-width:767px){.search-input--inner-border .v-input .v-input__append-inner{margin-top:0!important}}.search-input--inner-border .v-input.v-input--is-focused .v-input__control>.v-input__slot:before{display:none!important}.search-input--inner-border .v-input.v-input--is-focused .v-input__control>.v-input__slot>.v-text-field__slot:before{display:block!important}.search-input--small .v-input .v-input__control>.v-input__slot{height:44px!important}.search-input--small .v-input .v-input__append-inner{margin-top:6px!important}.search-input--small .v-input .v-input__append-inner .v-image{width:20px!important;height:20px!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserStatus.vue?vue&type=style&index=0&id=652352c7&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".user-status[data-v-652352c7]{width:16px;height:16px;border-radius:50%;border:2px solid #fff;background:#636363;z-index:2}.user-status--idle[data-v-652352c7]{background:linear-gradient(122.42deg,var(--v-redLight-base),var(--v-orangeLight2-base))}.user-status--online[data-v-652352c7]{background:var(--v-success-base)}.user-status--large[data-v-652352c7]{width:25px;height:25px}@media only screen and (max-width:991px){.user-status--large[data-v-652352c7]{width:23px;height:23px}}@media only screen and (max-width:639px){.user-status--large[data-v-652352c7]{width:21px;height:21px}}@media only screen and (max-width:479px){.user-status--large[data-v-652352c7]{width:19px;height:19px}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./VExpansionPanel.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"48751daa\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".theme--light.v-expansion-panels .v-expansion-panel{background-color:#fff;color:rgba(0,0,0,.87)}.theme--light.v-expansion-panels .v-expansion-panel--disabled{color:rgba(0,0,0,.38)}.theme--light.v-expansion-panels .v-expansion-panel:not(:first-child):after{border-color:rgba(0,0,0,.12)}.theme--light.v-expansion-panels .v-expansion-panel-header .v-expansion-panel-header__icon .v-icon{color:rgba(0,0,0,.54)}.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:hover:before{opacity:.04}.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:before,.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:hover:before,.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:focus:before{opacity:.12}.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:focus:before{opacity:.16}.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:hover:before{opacity:.04}.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:before,.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:hover:before,.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:focus:before{opacity:.12}.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:focus:before{opacity:.16}.theme--dark.v-expansion-panels .v-expansion-panel{background-color:#1e1e1e;color:#fff}.theme--dark.v-expansion-panels .v-expansion-panel--disabled{color:hsla(0,0%,100%,.5)}.theme--dark.v-expansion-panels .v-expansion-panel:not(:first-child):after{border-color:hsla(0,0%,100%,.12)}.theme--dark.v-expansion-panels .v-expansion-panel-header .v-expansion-panel-header__icon .v-icon{color:#fff}.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:hover:before{opacity:.08}.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:before,.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:hover:before,.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:focus:before{opacity:.24}.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:focus:before{opacity:.32}.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:hover:before{opacity:.08}.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:before,.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:hover:before,.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:focus:before{opacity:.24}.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:focus:before{opacity:.32}.v-expansion-panels{border-radius:8px;display:flex;flex-wrap:wrap;justify-content:center;list-style-type:none;padding:0;width:100%;z-index:1}.v-expansion-panels>*{cursor:auto}.v-expansion-panels>:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.v-expansion-panels>:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--active{border-radius:8px}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--active+.v-expansion-panel{border-top-left-radius:8px;border-top-right-radius:8px}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--next-active{border-bottom-left-radius:8px;border-bottom-right-radius:8px}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--next-active .v-expansion-panel-header{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.v-expansion-panel{flex:1 0 100%;max-width:100%;position:relative;transition:.3s cubic-bezier(.25,.8,.5,1)}.v-expansion-panel:before{border-radius:inherit;bottom:0;content:\\\"\\\";left:0;position:absolute;right:0;top:0;z-index:-1;transition:box-shadow .28s cubic-bezier(.4,0,.2,1);will-change:box-shadow;box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-expansion-panel:not(:first-child):after{border-top:thin solid;content:\\\"\\\";left:0;position:absolute;right:0;top:0;transition:border-color .2s cubic-bezier(.4,0,.2,1),opacity .2s cubic-bezier(.4,0,.2,1)}.v-expansion-panel--disabled .v-expansion-panel-header{pointer-events:none}.v-expansion-panel--active+.v-expansion-panel,.v-expansion-panel--active:not(:first-child){margin-top:16px}.v-expansion-panel--active+.v-expansion-panel:after,.v-expansion-panel--active:not(:first-child):after{opacity:0}.v-expansion-panel--active>.v-expansion-panel-header{min-height:64px}.v-expansion-panel--active>.v-expansion-panel-header--active .v-expansion-panel-header__icon:not(.v-expansion-panel-header__icon--disable-rotate) .v-icon{transform:rotate(-180deg)}.v-expansion-panel-header__icon{display:inline-flex;margin-bottom:-4px;margin-top:-4px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-expansion-panel-header__icon{margin-left:auto}.v-application--is-rtl .v-expansion-panel-header__icon{margin-right:auto}.v-expansion-panel-header{align-items:center;border-top-left-radius:inherit;border-top-right-radius:inherit;display:flex;font-size:.9375rem;line-height:1;min-height:64px;outline:none;padding:20px 24px;position:relative;transition:min-height .3s cubic-bezier(.25,.8,.5,1);width:100%}.v-application--is-ltr .v-expansion-panel-header{text-align:left}.v-application--is-rtl .v-expansion-panel-header{text-align:right}.v-expansion-panel-header:not(.v-expansion-panel-header--mousedown):focus:before{opacity:.12}.v-expansion-panel-header:before{background-color:currentColor;border-radius:inherit;bottom:0;content:\\\"\\\";left:0;opacity:0;pointer-events:none;position:absolute;right:0;top:0;transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-expansion-panel-header>:not(.v-expansion-panel-header__icon){flex:1 1 auto}.v-expansion-panel-content{display:flex}.v-expansion-panel-content__wrap{padding:0 24px 20px;flex:1 1 auto;max-width:100%}.v-expansion-panels--accordion>.v-expansion-panel{margin-top:0}.v-expansion-panels--accordion>.v-expansion-panel:after{opacity:1}.v-expansion-panels--popout>.v-expansion-panel{max-width:calc(100% - 32px)}.v-expansion-panels--popout>.v-expansion-panel--active{max-width:calc(100% + 16px)}.v-expansion-panels--inset>.v-expansion-panel{max-width:100%}.v-expansion-panels--inset>.v-expansion-panel--active{max-width:calc(100% - 32px)}.v-expansion-panels--flat>.v-expansion-panel:after{border-top:none}.v-expansion-panels--flat>.v-expansion-panel:before{box-shadow:0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)}.v-expansion-panels--tile,.v-expansion-panels--tile>.v-expansion-panel:before{border-radius:0}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ConfirmDialog.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"f203485e\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.isShownConfirmDialog)?_c('l-dialog',_vm._g({attrs:{\"dialog\":_vm.isShownConfirmDialog,\"hide-close-button\":\"\",\"max-width\":\"418\",\"custom-class\":\"remove-illustration text-center\"}},_vm.$listeners),[_c('div',[_c('div',{staticClass:\"remove-illustration-title font-weight-medium\"},[_vm._v(\"\\n      \"+_vm._s(_vm.$t('are_you_sure'))+\"\\n    \")]),_vm._v(\" \"),_c('div',{staticClass:\"mt-2\"},[_vm._t(\"default\")],2),_vm._v(\" \"),_c('div',{staticClass:\"d-flex justify-space-around justify-sm-space-between flex-wrap mt-2\"},[_c('v-btn',{staticClass:\"gradient font-weight-medium my-1\",on:{\"click\":function($event){return _vm.$emit('close-dialog')}}},[_c('div',{staticClass:\"text--gradient\"},[_vm._v(\"\\n          \"+_vm._s(_vm.$t(_vm.cancelTextButton))+\"\\n        \")])]),_vm._v(\" \"),_c('v-btn',{staticClass:\"font-weight-medium my-1\",attrs:{\"color\":\"primary\"},on:{\"click\":function($event){return _vm.$emit('confirm')}}},[_vm._v(\"\\n        \"+_vm._s(_vm.$t(_vm.confirmTextButton))+\"\\n      \")])],1)])]):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'ConfirmDialog',\n  props: {\n    isShownConfirmDialog: {\n      type: Boolean,\n      required: true,\n    },\n    cancelTextButton: {\n      type: String,\n      default: 'close',\n    },\n    confirmTextButton: {\n      type: String,\n      default: 'confirm',\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ConfirmDialog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ConfirmDialog.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ConfirmDialog.vue?vue&type=template&id=2a649283&\"\nimport script from \"./ConfirmDialog.vue?vue&type=script&lang=js&\"\nexport * from \"./ConfirmDialog.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./ConfirmDialog.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"33ddf780\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {LDialog: require('D:/languworks/langu-frontend/components/LDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\ninstallComponents(component, {VBtn})\n", "\nexport default {\n  data() {\n    return {\n      timeoutId: null,\n      userStatuses: {},\n      arrStatusId: [],\n    }\n  },\n  computed: {\n    preparedArr() {\n      return [...new Set(this.arrStatusId)]\n    },\n  },\n  mounted() {\n    this.timeoutId = window.setInterval(() => {\n      this.refreshStatusOnline()\n\n      if (!this.arrStatusId.length) {\n        this.clearInterval()\n      }\n    }, 10000)\n  },\n  beforeDestroy() {\n    if (this.timeoutId) {\n      this.clearInterval()\n    }\n  },\n  methods: {\n    refreshStatusOnline() {\n      if (this.arrStatusId.length) {\n        this.$store\n          .dispatch('user/refreshStatusOnline', this.preparedArr)\n          .then((res) => (this.userStatuses = res))\n      }\n    },\n    clearInterval() {\n      window.clearInterval(this.timeoutId)\n      this.timeoutId = null\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StatusOnline.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StatusOnline.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./StatusOnline.vue?vue&type=script&lang=js&\"\nexport * from \"./StatusOnline.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"2b0aab01\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AATA;AAFA;;ACnBA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACDA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAQA;AAGA;AAHA;AAKA;AAEA;AACA;AACA;AAFA;AACA;AAIA;AACA;AACA;AACA;AACA;AAHA;AATA;AACA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AAFA;AACA;AAQA;AACA;AAVA;AACA;AAWA;AACA;AACA;AACA;AAfA;AAiBA;AACA;AACA;AAFA;AACA;AAGA;AACA;AALA;AACA;AAMA;AACA;AACA;AATA;AACA;AAUA;AACA;AAZA;AACA;AAaA;AACA;AAEA;AAEA;AAnBA;AACA;AAoBA;AACA;AACA;AACA;AACA;AACA;AA1BA;AACA;AA2BA;AACA;AACA;AACA;AACA;AACA;AADA;AAHA;AAOA;AACA;AAtEA;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAKA;AAUA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAJA;AACA;AAKA;AACA;AAVA;AACA;AAYA;AACA;AAdA;AACA;AAgBA;AACA;AAEA;AACA;AACA;AACA;AAFA;AAFA;AAOA;AAAA;AAGA;AACA;AA9BA;;;;;;;;AC3BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAKA;AAUA;AACA;AAEA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAPA;AAaA;AACA;AADA;AAIA;AACA;AACA;AACA;AACA;AAFA;AAFA;AACA;AAMA;AACA;AARA;AACA;AASA;AACA;AAXA;AACA;AAYA;AACA;AACA;AACA;AAhBA;AACA;AAiBA;AACA;AAzCA;AACA;AA2CA;AACA;AA7CA;AACA;AA+CA;AACA;AACA;AAFA;AACA;AAGA;AACA;AAGA;AAEA;AACA;AACA;AADA;AAGA;AACA;AACA;AAFA;AALA;AAWA;AACA;AArBA;AACA;AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AAEA;AACA;AACA;AAJA;AAXA;AAkBA;AAAA;AAGA;AACA;AA9FA;;;;;;;;AC7BA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAGA;AAKA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AADA;AAJA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;AAYA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;AAWA;AACA;AAdA;AACA;AAeA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AADA;AAKA;AACA;AAlDA;AACA;AAoDA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AARA;AArDA;;;;;;;ACdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAKA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAFA;AAKA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AAIA;AARA;AAUA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AAJA;AA3CA;AAmDA;AACA;AACA;AAEA;AAFA;AAFA;AACA;AAMA;AACA;AAIA;AAEA;AAAA;AAAA;AACA;AADA;AAIA;AAlBA;AACA;AAuBA;AACA;AAzBA;AACA;AA0BA;AACA;AACA;AAFA;AACA;AAGA;AACA;AACA;AACA;AACA;AARA;AACA;AAQA;AACA;AArCA;AACA;AAsCA;AACA;AAxCA;AACA;AAyCA;AACA;AA3CA;AACA;AA4CA;AACA;AAEA;AACA;AACA;AACA;AAFA;AAKA;AAEA;AARA;AAhDA;AACA;AA4DA;AACA;AA9DA;AACA;AA+DA;AACA;AACA;AACA;AAnEA;AAqEA;AACA;AACA;AACA;AAFA;AACA;AAGA;AAJA;AACA;AAKA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA;AAoBA;AACA;AACA;AACA;AAHA;AACA;AAIA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAFA;AARA;AACA;AAkBA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAEA;AACA;AACA;AA7BA;AACA;AA8BA;AACA;AAGA;AACA;AACA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AACA;AAEA;AA9CA;AACA;AA+CA;AACA;AAEA;AACA;AADA;AAIA;AAvDA;AACA;AAwDA;AACA;AAEA;AACA;AACA;AA9DA;AACA;AA+DA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AADA;AAGA;AAHA;AAJA;AADA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAFA;AAnFA;AACA;AA0FA;AACA;AAEA;AAEA;AAFA;AAKA;AAnGA;AACA;AAoGA;AACA;AAEA;AAGA;AACA;AACA;AAAA;AA7GA;AACA;AA8GA;AACA;AAhHA;AACA;AAiHA;AACA;AACA;AACA;AACA;AACA;AAvHA;AApJA;;;;;;;ACjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACbA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AArBA;AACA;AAyBA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAhBA;AAiBA;AACA;AACA;AACA;AACA;AACA;AAHA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAvBA;AApDA;;ACjGA;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AClCA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAAA;AANA;AAJA;AAcA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AA3CA;AA4CA;AACA;AACA;AACA;AACA;AAJA;AACA;AAIA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AACA;AAEA;AACA;AAFA;AAKA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtCA;AAnFA;;ACxLA;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACnCA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAfA;AAgBA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AAnBA;AAlBA;;AC/IA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AA3BA;AA4BA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAPA;AACA;AAQA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AACA;AACA;AAhCA;AACA;AAgCA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAFA;AAIA;AACA;AAAA;AACA;AAEA;AADA;AAIA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AApDA;AA/FA;;ACxPA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC1CA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAMA;AACA;AACA;AACA;AAJA;AAOA;AACA;AAFA;AAKA;AACA;AADA;AAnBA;AAuBA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAKA;AArDA;;ACPA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AClBA;AAAA;AAEA;AACA;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAIA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAKA;AACA;AAAA;AAQA;AAEA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAPA;AAQA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAtCA;AAyCA;AACA;AADA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;AAFA;AACA;AAkBA;AACA;AApBA;AACA;AAqBA;AACA;AAIA;AACA;AA5BA;AACA;AA6BA;AACA;AAOA;AACA;AAAA;AACA;AADA;AAvFA;AACA;AA2FA;AACA;AACA;AAEA;AAJA;AACA;AAKA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAFA;AAKA;AACA;AACA;AAlBA;AACA;AAmBA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AAPA;AATA;AArBA;AACA;AAwCA;AACA;AACA;AADA;AAOA;AACA;AAlDA;AACA;AAmDA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAEA;AACA;AAHA;AAKA;AACA;AACA;AAFA;AAIA;AAEA;AAEA;AACA;AACA;AAnKA;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAIA;AACA;AACA;AAEA;AACA;AAWA;AAIA;AAEA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAXA;AACA;AAgBA;AACA;AACA;AACA;AACA;AACA;AAGA;AAPA;AArBA;AACA;AA+BA;AACA;AACA;AACA;AACA;AAFA;AAFA;AACA;AAMA;AACA;AARA;AACA;AASA;AACA;AAEA;AAbA;AACA;AAcA;AACA;AACA;AADA;AAhBA;AACA;AAmBA;AACA;AAEA;AAvBA;AACA;AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAvCA;AAyCA;AACA;AACA;AAFA;AACA;AAIA;AACA;AACA;AACA;AAjFA;AACA;AAmFA;AAEA;AACA;AACA;AADA;AAHA;AACA;AAMA;AACA;AARA;AACA;AAWA;AACA;AAbA;AACA;AAgBA;AACA;AAEA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AA5BA;AACA;AA6BA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAfA;AACA;AACA;AAiBA;AACA;AADA;AAGA;AAtBA;AA0BA;AACA;AAAA;AACA;AACA;AADA;AACA;AACA;AA5DA;AACA;AA6DA;AACA;AAEA;AAjEA;AACA;AAkEA;AACA;AACA;AACA;AAGA;AAJA;AAQA;AACA;AACA;AACA;AADA;AAVA;AArEA;AACA;AAiFA;AACA;AAnFA;AACA;AAsFA;AACA;AAEA;AAEA;AAEA;AAGA;AACA;AAAA;AAEA;AAEA;AAtGA;AACA;AAyGA;AACA;AAGA;AACA;AAEA;AAGA;AAEA;AAGA;AAEA;AAEA;AAEA;AAGA;AAIA;AAtIA;AACA;AAuIA;AACA;AAEA;AAEA;AACA;AACA;AA/IA;AACA;AAgJA;AACA;AACA;AACA;AAxOA;AA0OA;AACA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AARA;;;;;;;AClQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AATA;AAcA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AAfA;AAhBA;;ACXA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;ACVA;AACA;AACA;AACA;AAGA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAdA;AADA;;ACDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AACA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAQA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;AACA;AAUA;AACA;AACA;AACA;AAGA;AADA;AAIA;AADA;AAPA;AAaA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAQA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApCA;AAzFA;;AC/CA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACvBA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AACA;AACA;AACA;AAJA;AArBA;;ACvBA;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AC7BA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AATA;AAFA;;ACxCA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/BA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAZA;AA3BA;;ACDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}