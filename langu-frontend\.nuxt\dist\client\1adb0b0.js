(window.webpackJsonp=window.webpackJsonp||[]).push([[41],{1621:function(e,t,r){"use strict";var o=r(2),n=r(28),c=r(10),l=(r(62),r(40),r(174),r(24),r(38),r(20),r(80),r(6),r(7),r(8),r(9),r(14),r(15),r(859)),d=r(266);function f(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}function m(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?f(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):f(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var v={computed:{lessonId:function(){return this.$store.state.classroom.lessonId},role:function(){return this.$store.getters["classroom/role"]},acceptedFiles:function(){return this.$store.state.classroom.acceptedFiles}},methods:{uploadFiles:function(e){var t=this;return Object(c.a)(regeneratorRuntime.mark((function r(){var o,i,c,f,v,data,h;return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:e=Object(n.a)(e),o=new FormData,i=0;case 3:if(!(i<=e.length-1)){r.next=23;break}if(c=e[i],f=Object(l.getFileExtension)(c.name),!(c.size>d.a)){r.next=10;break}return r.next=9,t.$store.dispatch("snackbar/error",{errorMessage:t.$t("filename_size_should_be_less_than",{fileName:c.name,value:"".concat((d.a/8/1e3).toFixed(0)," Mb")}),timeout:5e3});case 9:return r.abrupt("continue",20);case 10:if(!t.acceptedFiles.officeTypes.includes(f)){r.next=19;break}return r.next=13,t.$store.dispatch("classroom/convertOfficeToPdf",c);case 13:v=r.sent,data=v.data,h=v.fileName,o.append(i.toString(),new Blob([data]),h),r.next=20;break;case 19:o.append(i.toString(),c);case 20:i++,r.next=3;break;case 23:t.$store.dispatch("classroom/uploadFiles",o).then((function(e){var r=0,o=0;t.$store.commit("classroom/addAssets",e),e.forEach((function(e){var n,c,d,f,v={id:e.id,lessonId:t.lessonId,asset:m(m({},e.asset),{},{index:t.$store.state.classroom.maxIndex+1,owner:t.role,top:t.$store.getters["classroom/zoomAsset"].asset.y+o+100,left:t.viewportWidth/2+t.$store.getters["classroom/zoomAsset"].asset.x+r-250})},h=Object(l.getFileExtension)(v.asset.path);if(null!==(n=t.acceptedFiles)&&void 0!==n&&n.pdfTypes.includes(h))f="pdf";else if(null!==(c=t.acceptedFiles)&&void 0!==c&&c.imageTypes.includes(h))f="image";else{if(null===(d=t.acceptedFiles)||void 0===d||!d.audioTypes.includes(h))return;f="audio"}v.asset.type=f,t.$store.commit("classroom/moveAsset",v),t.$store.dispatch("classroom/moveAsset",v),t.$socket.emit("asset-added",v),r+=50,o+=50}))})).catch((function(e){throw e}));case 24:case"end":return r.stop()}}),r)})))()}}},h=r(22),component=Object(h.a)(v,undefined,undefined,!1,null,null,null);t.a=component.exports},1658:function(e,t,r){var content=r(1766);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(19).default)("88facf08",content,!0,{sourceMap:!1})},1765:function(e,t,r){"use strict";r(1658)},1766:function(e,t,r){var o=r(18)(!1);o.push([e.i,".popup-load-files-drop-wrap[data-v-190879a5]{position:relative!important;display:flex;justify-content:center;align-items:center;height:100vh!important;background-color:hsla(0,0%,100%,.9);z-index:999999;pointer-events:none}.popup-load-files-drop-wrap .drop-area--wrapper[data-v-190879a5]{padding:30px}.popup-load-files-drop-wrap .drop-area--wrapper__dropbox-img[data-v-190879a5]{width:100%;height:auto}",""]),e.exports=o},1936:function(e,t,r){"use strict";r.r(t);r(31);var o={name:"DropFileArea",mixins:[r(1621).a],props:{viewportWidth:{type:Number,required:!0}},computed:{isDragging:function(){return this.$store.state.classroom.isDragging}},mounted:function(){document.addEventListener("dragover",this.dragover)},beforeDestroy:function(){document.removeEventListener("dragover",this.dragover)},methods:{dragover:function(e){e.preventDefault(),this.$store.commit("classroom/isDraggingTrigger",!0)},dragleave:function(){this.$store.commit("classroom/isDraggingTrigger",!1)},drop:function(e){this.$store.commit("classroom/isDraggingTrigger",!1),this.uploadFiles(e.dataTransfer.files)}}},n=(r(1765),r(22)),component=Object(n.a)(o,(function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{on:{drop:function(t){return t.preventDefault(),e.drop.apply(null,arguments)},dragleave:function(t){return t.preventDefault(),e.dragleave.apply(null,arguments)}}},[o("div",{directives:[{name:"show",rawName:"v-show",value:e.isDragging,expression:"isDragging"}],staticClass:"popup-load-files-drop-wrap"},[o("div",{staticClass:"drop-area--wrapper"},[o("img",{staticClass:"drop-area--wrapper__dropbox-img",attrs:{src:r(908),alt:""}})])])])}),[],!1,null,"190879a5",null);t.default=component.exports}}]);