#!/bin/bash
set -eu
envsubst '${APP_ENV}'
envsubst '${DOMAINS_NAME}'
envsubst '${EMAIL_FOR_CERTBOT}'

if [[ ${APP_ENV} == "prod" ]] ; then \
      apt-get update \
      && apt-get install -y certbot python-certbot-nginx bash wget \
      && certbot certonly --standalone --agree-tos -m ${EMAIL_FOR_CERTBOT} -n -d liveserver.langu.io \
      && rm -rf /var/lib/apt/lists/* \
      && echo "@monthly certbot renew --nginx >> /var/log/cron.log 2>&1" >/etc/cron.d/certbot-renew \
      && crontab /etc/cron.d/certbot-renew
    fi

touch /var/log/cron.log
exec "$@"