{"version": 3, "file": "components/user-settings-speciality-dialog.js", "sources": ["webpack:///./components/user-settings/SpecialityDialog.vue?45f5", "webpack:///./components/user-settings/SpecialityDialog.vue?c205", "webpack:///./components/user-settings/SpecialityDialog.vue?20a5", "webpack:///./components/user-settings/SpecialityDialog.vue?b276", "webpack:///./components/user-settings/SpecialityDialog.vue", "webpack:///./components/user-settings/SpecialityDialog.vue?074e", "webpack:///./components/user-settings/SpecialityDialog.vue?cefe"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SpecialityDialog.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"16b2336a\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SpecialityDialog.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".speciality-picker{height:calc(100vh - 10%)}.speciality-picker .dialog-content,.speciality-picker .v-card{height:100%}@media only screen and (max-width:1439px){.speciality-picker .v-card{padding:32px 28px}}.speciality-picker .dialog-content{position:relative}.speciality-picker-content{padding-bottom:88px}.speciality-picker-content>.row{height:100%}.speciality-picker-content>.row>.col,.speciality-picker-content>.row>.col>.column{height:inherit}.speciality-picker-title{font-size:20px}.speciality-picker-text{color:var(--v-grey-base);letter-spacing:.3px}.speciality-picker-text ul{padding-left:0;list-style-type:none}.speciality-picker-text ul>li:not(:last-child){margin-bottom:4px}.speciality-picker-bottom{position:absolute;left:0;bottom:0;width:100%}.speciality-picker .column{padding:20px 15px;border:1px solid #00a500;border-radius:24px}.speciality-picker .column-helper{height:100%;overflow-y:auto;overflow-x:hidden}.speciality-picker .column-helper>div,.speciality-picker .column-helper>div>div{height:100%}.speciality-picker .column .list-group-item{cursor:move}.speciality-picker .column .list-group-item:not(:last-child){margin-bottom:8px}.speciality-picker .column .list-group-item.highest-priority{color:var(--v-success-base)}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({directives:[{name:\"resize\",rawName:\"v-resize\",value:(_vm.onResize),expression:\"onResize\"}],attrs:{\"dialog\":_vm.isShownSpecialitiesDialog,\"max-width\":\"820\",\"custom-class\":\"speciality-picker\"}},_vm.$listeners),[_c('div',{ref:\"header\",staticClass:\"header\"},[_c('div',{staticClass:\"speciality-picker-title font-weight-medium\"},[_vm._v(\"\\n      \"+_vm._s(_vm.$t('manage_specialties'))+\":\\n    \")]),_vm._v(\" \"),_c('div',{staticClass:\"speciality-picker-text body-2 mt-2\"},[_c('ul',[_c('li',[_vm._v(\"1. \"+_vm._s(_vm.$t('drag_your_teaching_specialities')))]),_vm._v(\" \"),_c('li',[_vm._v(\"\\n          2.\\n          \"+_vm._s(_vm.$t(\n              'top_specialities_will_be_featured_on_teacher_search_results_page'\n            ))+\"\\n        \")])])]),_vm._v(\" \"),_c('v-row',{staticClass:\"my-0\"},[_c('v-col',{staticClass:\"col-6 py-0\"},[_c('div',{staticClass:\"text--gradient text-center subtitle-2 font-weight-medium mt-3 mb-1\"},[_vm._v(\"\\n          \"+_vm._s(_vm.$t('available_specialties'))+\":\\n        \")])]),_vm._v(\" \"),_c('v-col',{staticClass:\"col-6 py-0\"},[_c('div',{staticClass:\"text--gradient text-center subtitle-2 font-weight-medium mt-3 mb-1\"},[_vm._v(\"\\n          \"+_vm._s(_vm.$t('selected_specialties'))+\":\\n        \")])])],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"speciality-picker-content\",style:({ height: _vm.contentElHeight })},[_c('v-row',{staticClass:\"my-0\"},[_c('v-col',{staticClass:\"col-6 py-0\"},[_c('div',{staticClass:\"column\"},[_c('div',{staticClass:\"column-helper l-scroll l-scroll--dark-grey l-scroll--large\"},[_c('div',{staticClass:\"column-content\"},[_c('draggable',{staticClass:\"list-group\",attrs:{\"group\":\"specialities\"},on:{\"end\":_vm.onEnd},model:{value:(_vm.availableSpecializations),callback:function ($$v) {_vm.availableSpecializations=$$v},expression:\"availableSpecializations\"}},_vm._l((_vm.availableSpecializations),function(element){return _c('div',{key:element.name,staticClass:\"list-group-item body-1\"},[_vm._v(\"\\n                  \"+_vm._s(element.name)+\"\\n                \")])}),0)],1)])])]),_vm._v(\" \"),_c('v-col',{staticClass:\"col-6 py-0\"},[_c('div',{staticClass:\"column l-scroll\"},[_c('div',{staticClass:\"column-helper l-scroll--dark-grey l-scroll--large\"},[_c('div',{staticClass:\"column-content\"},[_c('draggable',{staticClass:\"list-group\",attrs:{\"group\":\"specialities\"},model:{value:(_vm.teacherSpecialities),callback:function ($$v) {_vm.teacherSpecialities=$$v},expression:\"teacherSpecialities\"}},_vm._l((_vm.teacherSpecialities),function(element,idx){return _c('div',{key:element.name,class:[\n                    'list-group-item body-1',\n                    { 'highest-priority': idx < 3 } ]},[_vm._v(\"\\n                  \"+_vm._s(element.name)+\"\\n                \")])}),0)],1)])])])],1),_vm._v(\" \"),_c('div',{staticClass:\"speciality-picker-bottom d-flex justify-end\"},[_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"primary\"},on:{\"click\":_vm.submitData}},[_c('svg',{staticClass:\"mr-1\",attrs:{\"width\":\"18\",\"height\":\"18\",\"viewBox\":\"0 0 18 18\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#save-icon\")}})]),_vm._v(\"\\n        \"+_vm._s(_vm.$t('save_changes'))+\"\\n      \")])],1)],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport draggable from 'vuedraggable'\n\nexport default {\n  name: 'SpecialityDialog',\n  components: {\n    draggable,\n  },\n  props: {\n    isShownSpecialitiesDialog: {\n      type: Boolean,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      contentElHeight: 'auto',\n    }\n  },\n  computed: {\n    availableSpecializations: {\n      get() {\n        return this.$store.getters['settings/availableSpecializations']\n      },\n      set(value) {\n        this.$store.commit('settings/UPDATE_AVAILABLE_SPECIALIZATIONS', value)\n      },\n    },\n    teacherSpecialities: {\n      get() {\n        return this.$store.getters['settings/teacherSpecialities']\n      },\n      set(value) {\n        this.$store.commit('settings/UPDATE_TEACHER_SPECIALITIES', value)\n      },\n    },\n  },\n  watch: {\n    isShownSpecialitiesDialog(newValue, oldValue) {\n      if (newValue) {\n        this.$nextTick(() => {\n          window.setTimeout(() => this.setContentElHeight())\n        })\n      }\n    },\n  },\n  methods: {\n    onEnd(e) {\n      if (this.teacherSpecialities.length > 8) {\n        const _availableSpecializations = [...this.availableSpecializations]\n        const _teacherSpecialities = [...this.teacherSpecialities]\n\n        _availableSpecializations.splice(\n          e.oldIndex,\n          0,\n          _teacherSpecialities[e.newIndex]\n        )\n        _teacherSpecialities.splice(e.newIndex, 1)\n\n        this.$store.commit(\n          'settings/UPDATE_AVAILABLE_SPECIALIZATIONS',\n          _availableSpecializations\n        )\n        this.$store.commit(\n          'settings/UPDATE_TEACHER_SPECIALITIES',\n          _teacherSpecialities\n        )\n      }\n    },\n    setContentElHeight() {\n      this.contentElHeight = `calc(100% - ${\n        this.$refs.header?.clientHeight ?? 0\n      }px)`\n    },\n    onResize() {\n      this.setContentElHeight()\n    },\n    submitData() {\n      this.$store\n        .dispatch('settings/updateSpecialities')\n        .then(() => this.$emit('close-dialog'))\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SpecialityDialog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SpecialityDialog.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./SpecialityDialog.vue?vue&type=template&id=437fbe4b&\"\nimport script from \"./SpecialityDialog.vue?vue&type=script&lang=js&\"\nexport * from \"./SpecialityDialog.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./SpecialityDialog.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"3bb9d92c\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {LDialog: require('D:/languworks/langu-frontend/components/LDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VRow } from 'vuetify/lib/components/VGrid';\ninstallComponents(component, {VBtn,VCol,VRow})\n\n\n/* vuetify-loader */\nimport installDirectives from \"!../../node_modules/vuetify-loader/lib/runtime/installDirectives.js\"\nimport Resize from 'vuetify/lib/directives/resize'\ninstallDirectives(component, {Resize})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;AAQA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;AATA;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AAIA;AAIA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAGA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AApCA;AA3CA;;AChHA;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}