(window.webpackJsonp=window.webpackJsonp||[]).push([[140],{1522:function(t,e,r){"use strict";r.r(e);r(31),r(20),r(80);var d={name:"UserStatus",props:{userId:{type:Number,default:0},large:{type:Boolean,default:!1},userStatuses:{type:Object,default:function(){return{}}}},computed:{status:function(){var t,e="offline";return Object.prototype.hasOwnProperty.call(this.userStatuses,null===(t=this.userId)||void 0===t?void 0:t.toString())&&(e=this.userStatuses[this.userId]),e}}},n=(r(1597),r(22)),component=Object(n.a)(d,(function(){var t=this,e=t.$createElement;return(t._self._c||e)("div",{class:["user-status","user-status--"+t.status,{"user-status--large":t.large}]})}),[],!1,null,"652352c7",null);e.default=component.exports},1545:function(t,e,r){var content=r(1598);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(19).default)("006007e9",content,!0,{sourceMap:!1})},1597:function(t,e,r){"use strict";r(1545)},1598:function(t,e,r){var d=r(18)(!1);d.push([t.i,".user-status[data-v-652352c7]{width:16px;height:16px;border-radius:50%;border:2px solid #fff;background:#636363;z-index:2}.user-status--idle[data-v-652352c7]{background:linear-gradient(122.42deg,var(--v-redLight-base),var(--v-orangeLight2-base))}.user-status--online[data-v-652352c7]{background:var(--v-success-base)}.user-status--large[data-v-652352c7]{width:25px;height:25px}@media only screen and (max-width:991px){.user-status--large[data-v-652352c7]{width:23px;height:23px}}@media only screen and (max-width:639px){.user-status--large[data-v-652352c7]{width:21px;height:21px}}@media only screen and (max-width:479px){.user-status--large[data-v-652352c7]{width:19px;height:19px}}",""]),t.exports=d}}]);