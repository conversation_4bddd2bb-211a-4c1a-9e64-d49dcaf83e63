exports.ids = [75];
exports.modules = {

/***/ 1215:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1289);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("36fed771", content, true, context)
};

/***/ }),

/***/ 1288:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SetPasswordDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1215);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SetPasswordDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SetPasswordDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SetPasswordDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SetPasswordDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1289:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".set-password .set-password-title{color:var(--v-dark-base)!important;font-size:20px;font-weight:600}@media only screen and (max-width:639px){.set-password .set-password-title{text-align:center;font-size:18px}}.set-password .set-password-text{color:var(--v-dark-base)!important;font-size:16px}@media only screen and (max-width:639px){.set-password .set-password-text{font-size:15px}}@media only screen and (max-width:479px){.set-password .set-password-text{font-size:14px}}.set-password .set-password-button{text-align:right}@media only screen and (max-width:639px){.set-password .set-password-button{text-align:center}}.set-password .set-password-button .v-btn{border-radius:16px!important}.set-password .set-password-button .v-btn .v-btn__content{font-weight:600!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1362:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/SetPasswordDialog.vue?vue&type=template&id=2c70e6ce&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',{attrs:{"dialog":_vm.showSetPasswordDialog,"hide-close-button":_vm.isValid,"max-width":"388","custom-class":"set-password"},on:{"close-dialog":_vm.resetData}},[_c('div',[(_vm.isValid)?[_c('div',{staticClass:"set-password-title"},[_vm._v("\n        "+_vm._s(_vm.$t('thank_you_for_confirming_your_email_address'))+"\n      ")]),_vm._v(" "),_c('div',{staticClass:"set-password-text mt-3 mt-md-5"},[_vm._v("\n        "+_vm._s(_vm.$t('please_create_your_langu_password'))+"\n      ")]),_vm._v(" "),_c('form',{staticClass:"mt-2",on:{"submit":function($event){$event.preventDefault();return _vm.setPasswordSubmitHandler.apply(null, arguments)}}},[_c('div',{staticClass:"mb-3"},[_c('text-input',{attrs:{"type":"password","placeholder":_vm.$t('password'),"name":"password","hide-details":"","autocomplete":"new-password"},scopedSlots:_vm._u([{key:"append",fn:function(){return [_c('div',{staticStyle:{"margin-top":"5px"}},[_c('v-img',{attrs:{"src":__webpack_require__(121),"width":"14","height":"21"}})],1)]},proxy:true}],null,false,3250624211),model:{value:(_vm.password),callback:function ($$v) {_vm.password=$$v},expression:"password"}})],1),_vm._v(" "),_c('div',[_c('text-input',{attrs:{"type":"password","placeholder":_vm.$t('repeat_password'),"name":"confirmPassword","hide-details":"","autocomplete":"new-password"},scopedSlots:_vm._u([{key:"append",fn:function(){return [_c('div',{staticStyle:{"margin-top":"5px"}},[_c('v-img',{attrs:{"src":__webpack_require__(121),"width":"14","height":"21"}})],1)]},proxy:true}],null,false,3250624211),model:{value:(_vm.passwordRepeat),callback:function ($$v) {_vm.passwordRepeat=$$v},expression:"passwordRepeat"}})],1),_vm._v(" "),_c('div',{staticClass:"form-message"},[(_vm.passwordLengthError)?[_c('div',{staticClass:"form-message-wrap form-message-wrap--error"},[_c('div',{staticClass:"form-message-icon"},[_c('svg',{attrs:{"width":"12","height":"12","viewBox":"0 0 12 12"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#attention")}})])]),_vm._v("\n              "+_vm._s(_vm.passwordTextError)+"\n            ")])]:_vm._e()],2),_vm._v(" "),_c('div',{staticClass:"set-password-button mt-1"},[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"orange","x-large":"","type":"submit"}},[_vm._v("\n            "+_vm._s(_vm.$t('save'))+"\n          ")])],1)])]:[(_vm.token)?[_c('div',{staticClass:"set-password-title"},[_vm._v("\n          "+_vm._s(_vm.$t('sorry_this_link_has_expired'))+"\n        ")]),_vm._v(" "),_c('div',{staticClass:"set-password-text mt-3 mt-md-5"},[_vm._v("\n          "+_vm._s(_vm.$t('please_request_new_link_by_clicking_forgot_password_button'))+"\n        ")])]:[_c('div',{staticClass:"set-password-title"},[_vm._v("Something went wrong!")])]]],2)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/SetPasswordDialog.vue?vue&type=template&id=2c70e6ce&

// EXTERNAL MODULE: ./components/LDialog.vue + 5 modules
var LDialog = __webpack_require__(28);

// EXTERNAL MODULE: ./components/form/TextInput.vue + 4 modules
var TextInput = __webpack_require__(102);

// EXTERNAL MODULE: ./helpers/navigationState.js
var navigationState = __webpack_require__(431);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/SetPasswordDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



/* harmony default export */ var SetPasswordDialogvue_type_script_lang_js_ = ({
  name: 'SetPasswordDialog',
  components: {
    LDialog: LDialog["default"],
    TextInput: TextInput["default"]
  },
  props: {
    showSetPasswordDialog: {
      type: Boolean,
      required: true
    }
  },

  data() {
    return {
      password: '',
      passwordRepeat: '',
      passwordTextError: ''
    };
  },

  computed: {
    passwordLengthError() {
      return !!this.passwordTextError.length;
    },

    token() {
      var _this$$store$state$au;

      return (_this$$store$state$au = this.$store.state.auth.passwordTokenItem) === null || _this$$store$state$au === void 0 ? void 0 : _this$$store$state$au.token;
    },

    isValid() {
      var _this$$store$state$au2, _this$$store$state$au3;

      return this.token ? (_this$$store$state$au2 = !((_this$$store$state$au3 = this.$store.state.auth.passwordTokenItem) !== null && _this$$store$state$au3 !== void 0 && _this$$store$state$au3.isExpired)) !== null && _this$$store$state$au2 !== void 0 ? _this$$store$state$au2 : false : false;
    }

  },
  watch: {
    password() {
      this.passwordTextError = '';
    },

    passwordRepeat() {
      this.passwordTextError = '';
    }

  },

  beforeDestroy() {
    this.resetData();
  },

  methods: {
    setPasswordSubmitHandler() {
      this.passwordTextError = '';
      const regex = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[^\w\s]).{8,}$/;

      if (this.password !== this.passwordRepeat) {
        this.passwordTextError = this.$t('passwords_are_different');
        return;
      }

      if (!regex.test(this.password)) {
        this.passwordTextError = this.$t('password_error');
        return;
      }

      this.$store.dispatch('auth/setPassword', {
        token: this.token,
        password: this.password,
        confirmPassword: this.passwordRepeat
      }).then(() => {
        this.$store.dispatch('user/getUserStatus').then(() => {
          this.resetData(); // Try to apply the saved navigation state

          const navigationStateApplied = Object(navigationState["a" /* applyNavigationState */])(this.$router, true); // If navigation state was applied, we're done

          if (navigationStateApplied) {
            return;
          } // Otherwise, use the redirectUrl or default path


          const redirectUrl = this.$store.getters['user/redirectUrl'];

          if (redirectUrl) {
            this.$router.push(redirectUrl);
            this.$store.dispatch('user/clearRedirectUrl');
          } else if (this.$store.getters['user/isStudent'] && !this.$store.getters['user/registrationConfirmed']) {
            this.$router.push({
              path: '/teacher-listing/welcome'
            });
          }
        });
      }).catch(e => {
        if (e.response) {
          if (e.response.status === 400) {
            this.passwordTextError = this.$t('password_error');
          }

          if (e.response.status === 404) {
            this.$store.commit('SET_IS_PASSWORD_LINK_EXPIRED', true);
            this.$store.commit('auth/SET_PASSWORD_TOKEN_ITEM', null);
            this.$store.commit('SET_IS_LOGIN_SIDEBAR', true);
          }
        }
      });
    },

    resetData() {
      this.password = '';
      this.passwordRepeat = '';
      this.$store.commit('auth/SET_PASSWORD_TOKEN_ITEM', null);
    }

  }
});
// CONCATENATED MODULE: ./components/SetPasswordDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_SetPasswordDialogvue_type_script_lang_js_ = (SetPasswordDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/SetPasswordDialog.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1288)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_SetPasswordDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "0707b346"
  
)

/* harmony default export */ var SetPasswordDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LDialog: __webpack_require__(28).default})


/* vuetify-loader */



installComponents_default()(component, {VBtn: VBtn["a" /* default */],VImg: VImg["a" /* default */]})


/***/ })

};;
//# sourceMappingURL=set-password-dialog.js.map