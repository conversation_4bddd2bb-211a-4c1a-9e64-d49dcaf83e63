(window.webpackJsonp=window.webpackJsonp||[]).push([[43],{1555:function(t,e,o){"use strict";o.r(e);o(7),o(8),o(9),o(14),o(15);var n=o(2),r=o(28),h=(o(31),o(6),o(23),o(1018)),l=o.n(h),c=o(266);function v(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var y={name:"Konva",props:{file:{type:Object,required:!0},width:{type:Number,required:!0},height:{type:Number,required:!0},scale:{type:Number,default:1},currentTime:{type:Number,default:null},currentPage:{type:Number,default:null},isMainKonva:{type:Boolean,default:!1}},data:function(){return{shapeData:null,beginPoint:null,konvaEl:null,konvaOverlayREl:null,konvaOverlayBEl:null}},computed:{isCanvasOversizeX:function(){return c.n>this.width},isScaledCanvasOversizeX:function(){return c.n*this.scale>this.width},isCanvasOversizeY:function(){return c.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return c.k*this.scale>this.height},assetType:function(){return this.file.asset.type},userParams:function(){return this.$store.getters["classroom/userParams"]},assetShapes:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.shapes)&&void 0!==t?t:[]},shapes:function(){var t=this,e=Object(r.a)(this.assetShapes),o=[];return this.shapeData&&e.push(this.shapeData),e.forEach((function(e){var r,h=function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?v(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):v(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},e);h.x=e.x-t.zoomX,h.y=e.y-t.zoomY,null!==(r=t.file.asset)&&void 0!==r&&r.originalWidth&&(h.strokeWidth=e.strokeWidth*t.file.asset.originalWidth/t.file.asset.width),o.push(h)})),o},zoomX:function(){return this.isMainKonva?this.zoom.x:0},zoomY:function(){return this.isMainKonva?this.zoom.y:0},zoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},config:function(){return{scale:{x:this.scale,y:this.scale},width:this.width,height:this.height,draggable:!1}}},watch:{width:function(t){this.stage.setWidth(t),this.isMainKonva&&(this.konvaOverlayREl&&this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayBEl&&this.setStyleForVerticalMainKonvaOverlays())},height:function(t){this.stage.setHeight(t),this.isMainKonva&&(this.konvaOverlayREl&&this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayBEl&&this.setStyleForVerticalMainKonvaOverlays())},scale:function(t){this.stage.setScale({x:t,y:t}),this.isMainKonva&&(this.konvaOverlayREl&&this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayBEl&&this.setStyleForVerticalMainKonvaOverlays())},isScaledCanvasOversizeX:function(t){this.isMainKonva&&(t?(this.konvaEl.removeChild(this.konvaOverlayREl),this.konvaOverlayREl=null):this.addHorizontalMainKonvaOverlays())},isScaledCanvasOversizeY:function(t){this.isMainKonva&&(t?(this.konvaEl.removeChild(this.konvaOverlayBEl),this.konvaOverlayBEl=null):this.addVerticalMainKonvaOverlays())}},mounted:function(){var t=this;this.stage=this.$refs.stage.getStage(),this.$nextTick((function(){t.move()})),this.konvaEl=document.getElementById("konva"),this.isMainKonva&&(this.isScaledCanvasOversizeX||this.addHorizontalMainKonvaOverlays(),this.isScaledCanvasOversizeY||this.addVerticalMainKonvaOverlays())},methods:{addHorizontalMainKonvaOverlays:function(){this.konvaOverlayREl||(this.konvaOverlayREl=document.createElement("div"),this.konvaOverlayREl.classList.add("konva-overlay-r"),this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayREl.addEventListener("mouseenter",this.mouseup),this.konvaEl.appendChild(this.konvaOverlayREl))},addVerticalMainKonvaOverlays:function(){this.konvaOverlayBEl||(this.konvaOverlayBEl=document.createElement("div"),this.konvaOverlayBEl.classList.add("konva-overlay-b"),this.setStyleForVerticalMainKonvaOverlays(),this.konvaOverlayBEl.addEventListener("mouseenter",this.mouseup),this.konvaEl.appendChild(this.konvaOverlayBEl))},setStyleForHorizontalMainKonvaOverlays:function(){this.konvaOverlayREl.style.position="absolute",this.konvaOverlayREl.style.top="0",this.konvaOverlayREl.style.right="0",this.konvaOverlayREl.style.width="".concat(this.width-c.n*this.scale,"px"),this.konvaOverlayREl.style.height="".concat(this.height,"px"),this.konvaOverlayREl.style.backgroundColor="#DCDCDD"},setStyleForVerticalMainKonvaOverlays:function(){this.konvaOverlayBEl.style.position="absolute",this.konvaOverlayBEl.style.bottom="0",this.konvaOverlayBEl.style.left="0",this.konvaOverlayBEl.style.width="".concat(this.width,"px"),this.konvaOverlayBEl.style.height="".concat(this.height-c.k*this.scale,"px"),this.konvaOverlayBEl.style.backgroundColor="#DCDCDD"},mousedown:function(t){var e=this.$refs.globalLayer.getNode(),o=t.target.getStage().getPointerPosition();switch(this.beginPoint={x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY},this.userParams.tool){case c.f:var n=new l.a.Circle({x:o.x/this.scale,y:o.y/this.scale,radius:2,stroke:this.userParams.color,strokeWidth:1});return e.add(n),void new l.a.Tween({node:n,duration:1,radius:24,onFinish:function(){return n.destroy()}}).play();case c.e:this.shapeData={type:"v-line",stroke:this.userParams.color,strokeWidth:5,x:0,y:0,points:[o.x/this.scale+this.zoomX,o.y/this.scale+this.zoomY],lineCap:"round",lineJoin:"round",tension:0,bezier:!0,perfectDrawEnabled:!1};break;case c.g:this.shapeData={type:"v-rect",x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY,width:1,height:1,stroke:this.userParams.color,strokeWidth:5};break;case c.b:this.shapeData={type:"v-circle",x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY,radius:1,stroke:this.userParams.color,strokeWidth:5};break;case c.h:this.shapeData={type:"v-line",stroke:this.userParams.color,strokeWidth:5,x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY,points:[0,0,0,0,0,0],tension:0,closed:!0};break;case c.d:this.shapeData={type:"v-line",stroke:this.userParams.color,strokeWidth:5,x:0,y:0,points:[o.x/this.scale+this.zoomX,o.y/this.scale+this.zoomY]};break;case c.c:this.shapeData={type:"v-line",stroke:"#f2f2f2",strokeWidth:30,x:0,y:0,points:[o.x/this.scale+this.zoomX,o.y/this.scale+this.zoomY],globalCompositeOperation:"destination-out"};break;default:console.warn("Requested action doesnt found for selected cursor - "+this.userParams.tool)}this.userParams.tool!==c.f&&("video"===this.assetType&&(this.shapeData.time=this.currentTime),"pdf"===this.assetType&&(this.shapeData.page=this.currentPage))},mouseup:function(){if(this.shapeData&&this.shapeData.type){var t={shapes:[].concat(Object(r.a)(this.assetShapes),[this.shapeData])};this.$store.commit("classroom/moveAsset",{id:this.file.id,asset:t}),this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.file.lessonId,asset:t}),this.beginPoint=null,this.shapeData=null}},move:function(t){if(this.shapeData){var e=t.target.getStage().getPointerPosition();this.drawing(e)}},drawing:function(t){var e,o;if(this.shapeData)switch(this.userParams.tool){case c.e:case c.c:this.shapeData.points=[].concat(Object(r.a)(this.shapeData.points),[t.x/this.scale+this.zoomX,t.y/this.scale+this.zoomY]);break;case c.g:this.shapeData.width=t.x/this.scale+this.zoomX-this.beginPoint.x,this.shapeData.height=t.y/this.scale+this.zoomY-this.beginPoint.y;break;case c.b:e=Math.abs(t.x/this.scale+this.zoomX-this.beginPoint.x),o=Math.abs(t.y/this.scale+this.zoomY-this.beginPoint.y),this.shapeData.radius=Math.max(e,o);break;case c.h:this.shapeData.points=[-(t.x/this.scale+this.zoomX-this.beginPoint.x),t.y/this.scale+this.zoomY-this.beginPoint.y,0,0,t.x/this.scale+this.zoomX-this.beginPoint.x,t.y/this.scale+this.zoomY-this.beginPoint.y];break;case c.d:this.shapeData.points=[this.beginPoint.x,this.beginPoint.y,t.x/this.scale+this.zoomX,t.y/this.scale+this.zoomY];break;default:console.warn("Requested action doesnt found for selected cursor")}}}},d=o(22),component=Object(d.a)(y,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-stage",{ref:"stage",attrs:{config:t.config},on:{mousedown:t.mousedown,touchstart:t.mousedown,touchmove:t.move,touchend:t.mouseup,mouseup:t.mouseup,mousemove:t.move}},[o("v-layer",{ref:"globalLayer"},[t._l(t.shapes,(function(e,n){return[!e.hasOwnProperty("time")&&!e.hasOwnProperty("page")||e.hasOwnProperty("time")&&t.currentTime+1>=e.time&&e.time>=t.currentTime-1||e.hasOwnProperty("page")&&t.currentPage===e.page?o(e.type,{key:n,tag:"component",attrs:{config:e}}):t._e()]}))],2)],1)}),[],!1,null,null,null);e.default=component.exports}}]);