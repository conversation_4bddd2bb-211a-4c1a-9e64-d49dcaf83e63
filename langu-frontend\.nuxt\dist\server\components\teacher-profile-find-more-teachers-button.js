exports.ids = [86];
exports.modules = {

/***/ 1145:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1189);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("a3665a14", content, true, context)
};

/***/ }),

/***/ 1173:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/teacher-profile/FindMoreTeachersButton.vue?vue&type=template&id=67bbc599&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-btn',{class:['font-weight-medium', { gradient: _vm.outlined }],attrs:{"width":"100%","color":_vm.outlined ? '' : 'primary',"to":("/teacher-listing/1/language," + (_vm.language.id))}},[_c('span',{class:['d-flex', { 'text--gradient': _vm.outlined }]},[(_vm.locale === 'pl')?[_vm._v("\n      Znajdź więcej nauczycieli\n      "),(_vm.language.isoCode)?_c('div',{staticClass:"flag-icon-ml elevation-2"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (_vm.language.isoCode) + ".svg"),"width":"24","height":"18"}})],1):_vm._e()]:(_vm.locale === 'es')?[_vm._v("\n      Más profesores de\n      "),(_vm.language.isoCode)?_c('div',{staticClass:"flag-icon-ml elevation-2"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (_vm.language.isoCode) + ".svg"),"width":"24","height":"18"}})],1):_vm._e()]:[_vm._v("\n      Find more\n      "),(_vm.language.isoCode)?_c('div',{staticClass:"flag-icon-ml flag-icon-mr elevation-2"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (_vm.language.isoCode) + ".svg"),"width":"24","height":"18"}})],1):_vm._e(),_vm._v("\n      teachers\n    ")]],2)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/teacher-profile/FindMoreTeachersButton.vue?vue&type=template&id=67bbc599&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/teacher-profile/FindMoreTeachersButton.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var FindMoreTeachersButtonvue_type_script_lang_js_ = ({
  name: 'FindMoreTeachersButton',
  props: {
    language: {
      type: Object,
      required: true
    },
    outlined: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale;
    }

  }
});
// CONCATENATED MODULE: ./components/teacher-profile/FindMoreTeachersButton.vue?vue&type=script&lang=js&
 /* harmony default export */ var teacher_profile_FindMoreTeachersButtonvue_type_script_lang_js_ = (FindMoreTeachersButtonvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/teacher-profile/FindMoreTeachersButton.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1188)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  teacher_profile_FindMoreTeachersButtonvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "67bbc599",
  "11cecc27"
  
)

/* harmony default export */ var FindMoreTeachersButton = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */



installComponents_default()(component, {VBtn: VBtn["a" /* default */],VImg: VImg["a" /* default */]})


/***/ }),

/***/ 1188:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FindMoreTeachersButton_vue_vue_type_style_index_0_id_67bbc599_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1145);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FindMoreTeachersButton_vue_vue_type_style_index_0_id_67bbc599_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FindMoreTeachersButton_vue_vue_type_style_index_0_id_67bbc599_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FindMoreTeachersButton_vue_vue_type_style_index_0_id_67bbc599_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FindMoreTeachersButton_vue_vue_type_style_index_0_id_67bbc599_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1189:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".flag-icon-ml[data-v-67bbc599]{margin-left:5px}.flag-icon-mr[data-v-67bbc599]{margin-right:5px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

};;
//# sourceMappingURL=teacher-profile-find-more-teachers-button.js.map