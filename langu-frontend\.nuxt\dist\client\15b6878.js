(window.webpackJsonp=window.webpackJsonp||[]).push([[51,43],{1555:function(e,t,o){"use strict";o.r(t);o(7),o(8),o(9),o(14),o(15);var n=o(2),r=o(28),l=(o(31),o(6),o(23),o(1018)),h=o.n(l),c=o(266);function d(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,o)}return t}var v={name:"Konva",props:{file:{type:Object,required:!0},width:{type:Number,required:!0},height:{type:Number,required:!0},scale:{type:Number,default:1},currentTime:{type:Number,default:null},currentPage:{type:Number,default:null},isMainKonva:{type:Boolean,default:!1}},data:function(){return{shapeData:null,beginPoint:null,konvaEl:null,konvaOverlayREl:null,konvaOverlayBEl:null}},computed:{isCanvasOversizeX:function(){return c.n>this.width},isScaledCanvasOversizeX:function(){return c.n*this.scale>this.width},isCanvasOversizeY:function(){return c.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return c.k*this.scale>this.height},assetType:function(){return this.file.asset.type},userParams:function(){return this.$store.getters["classroom/userParams"]},assetShapes:function(){var e,t;return null!==(e=null===(t=this.file.asset)||void 0===t?void 0:t.shapes)&&void 0!==e?e:[]},shapes:function(){var e=this,t=Object(r.a)(this.assetShapes),o=[];return this.shapeData&&t.push(this.shapeData),t.forEach((function(t){var r,l=function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(t){Object(n.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({},t);l.x=t.x-e.zoomX,l.y=t.y-e.zoomY,null!==(r=e.file.asset)&&void 0!==r&&r.originalWidth&&(l.strokeWidth=t.strokeWidth*e.file.asset.originalWidth/e.file.asset.width),o.push(l)})),o},zoomX:function(){return this.isMainKonva?this.zoom.x:0},zoomY:function(){return this.isMainKonva?this.zoom.y:0},zoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},config:function(){return{scale:{x:this.scale,y:this.scale},width:this.width,height:this.height,draggable:!1}}},watch:{width:function(e){this.stage.setWidth(e),this.isMainKonva&&(this.konvaOverlayREl&&this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayBEl&&this.setStyleForVerticalMainKonvaOverlays())},height:function(e){this.stage.setHeight(e),this.isMainKonva&&(this.konvaOverlayREl&&this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayBEl&&this.setStyleForVerticalMainKonvaOverlays())},scale:function(e){this.stage.setScale({x:e,y:e}),this.isMainKonva&&(this.konvaOverlayREl&&this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayBEl&&this.setStyleForVerticalMainKonvaOverlays())},isScaledCanvasOversizeX:function(e){this.isMainKonva&&(e?(this.konvaEl.removeChild(this.konvaOverlayREl),this.konvaOverlayREl=null):this.addHorizontalMainKonvaOverlays())},isScaledCanvasOversizeY:function(e){this.isMainKonva&&(e?(this.konvaEl.removeChild(this.konvaOverlayBEl),this.konvaOverlayBEl=null):this.addVerticalMainKonvaOverlays())}},mounted:function(){var e=this;this.stage=this.$refs.stage.getStage(),this.$nextTick((function(){e.move()})),this.konvaEl=document.getElementById("konva"),this.isMainKonva&&(this.isScaledCanvasOversizeX||this.addHorizontalMainKonvaOverlays(),this.isScaledCanvasOversizeY||this.addVerticalMainKonvaOverlays())},methods:{addHorizontalMainKonvaOverlays:function(){this.konvaOverlayREl||(this.konvaOverlayREl=document.createElement("div"),this.konvaOverlayREl.classList.add("konva-overlay-r"),this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayREl.addEventListener("mouseenter",this.mouseup),this.konvaEl.appendChild(this.konvaOverlayREl))},addVerticalMainKonvaOverlays:function(){this.konvaOverlayBEl||(this.konvaOverlayBEl=document.createElement("div"),this.konvaOverlayBEl.classList.add("konva-overlay-b"),this.setStyleForVerticalMainKonvaOverlays(),this.konvaOverlayBEl.addEventListener("mouseenter",this.mouseup),this.konvaEl.appendChild(this.konvaOverlayBEl))},setStyleForHorizontalMainKonvaOverlays:function(){this.konvaOverlayREl.style.position="absolute",this.konvaOverlayREl.style.top="0",this.konvaOverlayREl.style.right="0",this.konvaOverlayREl.style.width="".concat(this.width-c.n*this.scale,"px"),this.konvaOverlayREl.style.height="".concat(this.height,"px"),this.konvaOverlayREl.style.backgroundColor="#DCDCDD"},setStyleForVerticalMainKonvaOverlays:function(){this.konvaOverlayBEl.style.position="absolute",this.konvaOverlayBEl.style.bottom="0",this.konvaOverlayBEl.style.left="0",this.konvaOverlayBEl.style.width="".concat(this.width,"px"),this.konvaOverlayBEl.style.height="".concat(this.height-c.k*this.scale,"px"),this.konvaOverlayBEl.style.backgroundColor="#DCDCDD"},mousedown:function(e){var t=this.$refs.globalLayer.getNode(),o=e.target.getStage().getPointerPosition();switch(this.beginPoint={x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY},this.userParams.tool){case c.f:var n=new h.a.Circle({x:o.x/this.scale,y:o.y/this.scale,radius:2,stroke:this.userParams.color,strokeWidth:1});return t.add(n),void new h.a.Tween({node:n,duration:1,radius:24,onFinish:function(){return n.destroy()}}).play();case c.e:this.shapeData={type:"v-line",stroke:this.userParams.color,strokeWidth:5,x:0,y:0,points:[o.x/this.scale+this.zoomX,o.y/this.scale+this.zoomY],lineCap:"round",lineJoin:"round",tension:0,bezier:!0,perfectDrawEnabled:!1};break;case c.g:this.shapeData={type:"v-rect",x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY,width:1,height:1,stroke:this.userParams.color,strokeWidth:5};break;case c.b:this.shapeData={type:"v-circle",x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY,radius:1,stroke:this.userParams.color,strokeWidth:5};break;case c.h:this.shapeData={type:"v-line",stroke:this.userParams.color,strokeWidth:5,x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY,points:[0,0,0,0,0,0],tension:0,closed:!0};break;case c.d:this.shapeData={type:"v-line",stroke:this.userParams.color,strokeWidth:5,x:0,y:0,points:[o.x/this.scale+this.zoomX,o.y/this.scale+this.zoomY]};break;case c.c:this.shapeData={type:"v-line",stroke:"#f2f2f2",strokeWidth:30,x:0,y:0,points:[o.x/this.scale+this.zoomX,o.y/this.scale+this.zoomY],globalCompositeOperation:"destination-out"};break;default:console.warn("Requested action doesnt found for selected cursor - "+this.userParams.tool)}this.userParams.tool!==c.f&&("video"===this.assetType&&(this.shapeData.time=this.currentTime),"pdf"===this.assetType&&(this.shapeData.page=this.currentPage))},mouseup:function(){if(this.shapeData&&this.shapeData.type){var e={shapes:[].concat(Object(r.a)(this.assetShapes),[this.shapeData])};this.$store.commit("classroom/moveAsset",{id:this.file.id,asset:e}),this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.file.lessonId,asset:e}),this.beginPoint=null,this.shapeData=null}},move:function(e){if(this.shapeData){var t=e.target.getStage().getPointerPosition();this.drawing(t)}},drawing:function(e){var t,o;if(this.shapeData)switch(this.userParams.tool){case c.e:case c.c:this.shapeData.points=[].concat(Object(r.a)(this.shapeData.points),[e.x/this.scale+this.zoomX,e.y/this.scale+this.zoomY]);break;case c.g:this.shapeData.width=e.x/this.scale+this.zoomX-this.beginPoint.x,this.shapeData.height=e.y/this.scale+this.zoomY-this.beginPoint.y;break;case c.b:t=Math.abs(e.x/this.scale+this.zoomX-this.beginPoint.x),o=Math.abs(e.y/this.scale+this.zoomY-this.beginPoint.y),this.shapeData.radius=Math.max(t,o);break;case c.h:this.shapeData.points=[-(e.x/this.scale+this.zoomX-this.beginPoint.x),e.y/this.scale+this.zoomY-this.beginPoint.y,0,0,e.x/this.scale+this.zoomX-this.beginPoint.x,e.y/this.scale+this.zoomY-this.beginPoint.y];break;case c.d:this.shapeData.points=[this.beginPoint.x,this.beginPoint.y,e.x/this.scale+this.zoomX,e.y/this.scale+this.zoomY];break;default:console.warn("Requested action doesnt found for selected cursor")}}}},m=o(22),component=Object(m.a)(v,(function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("v-stage",{ref:"stage",attrs:{config:e.config},on:{mousedown:e.mousedown,touchstart:e.mousedown,touchmove:e.move,touchend:e.mouseup,mouseup:e.mouseup,mousemove:e.move}},[o("v-layer",{ref:"globalLayer"},[e._l(e.shapes,(function(t,n){return[!t.hasOwnProperty("time")&&!t.hasOwnProperty("page")||t.hasOwnProperty("time")&&e.currentTime+1>=t.time&&t.time>=e.currentTime-1||t.hasOwnProperty("page")&&e.currentPage===t.page?o(t.type,{key:n,tag:"component",attrs:{config:t}}):e._e()]}))],2)],1)}),[],!1,null,null,null);t.default=component.exports},1662:function(e,t,o){var content=o(1776);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,o(19).default)("013ffd6a",content,!0,{sourceMap:!1})},1775:function(e,t,o){"use strict";o(1662)},1776:function(e,t,o){var n=o(18)(!1);n.push([e.i,".plyr--video .plyr__controls{position:fixed!important}.plyr--video .plyr__progress .time-markers{position:absolute;top:50%;left:0;width:calc(100% + 13px);width:calc(100% + var(--plyr-range-thumb-height, 13px));height:5px;height:var(--plyr-range-track-height,5px);margin-top:-2.5px;margin-top:calc(var(--plyr-range-track-height, 5px)/2*-1);margin-left:-6.5px;margin-left:calc(var(--plyr-range-thumb-height, 13px)*-0.5)}.plyr--video .plyr__progress .time-markers span{position:absolute;top:0;display:block;width:1px;height:100%;background-color:#fff;z-index:2}",""]),e.exports=n},1939:function(e,t,o){"use strict";o.r(t);var n=o(28),r=(o(9),o(24),o(38),o(6),o(20),o(80),o(174),o(1690)),l=o.n(r),h=o(266),c=o(1389),d=o(1459),v=o(1555),m={name:"VideoItem",components:{ClassroomContainer:c.default,ClassroomContainerHeader:d.default,Konva:v.default},props:{file:{type:Object,required:!0}},data:function(){return{timeMarkerContainer:null,currentTime:0,duration:null,loaded:!1,changedBySocket:!1,player:{config:{title:""}}}},computed:{scale:function(){return this.width?this.width/h.j:1},width:function(){var e,t;return null!==(e=null===(t=this.file.asset)||void 0===t?void 0:t.width)&&void 0!==e?e:0},height:function(){var e,t;return null!==(e=null===(t=this.file.asset)||void 0===t?void 0:t.height)&&void 0!==e?e:0},shapes:function(){return Object(n.a)(this.file.asset.shapes)},zoomIndex:function(){var e,t,o;return null!==(e=null===(t=this.$store.getters["classroom/zoomAsset"])||void 0===t||null===(o=t.asset)||void 0===o?void 0:o.zoomIndex)&&void 0!==e?e:1},isSocketConnected:function(){return this.$store.state.socket.isConnected},isUserInteracted:function(){return this.$store.state.classroom.isUserInteracted}},watch:{shapes:function(e,t){if(e.length){var o=e.filter((function(e){return!t.includes(e)}));this.addMarker(o[0])}}},mounted:function(){var e=this,t={controls:["play","progress","current-time","mute","volume","captions","pip"],clickToPlay:!1,hideControls:!1,resetOnEnd:!0,volume:this.file.asset.volume||1,muted:this.file.asset.muted||!1,storage:{enabled:!1},speed:{selected:this.file.asset.speed||1,options:[.5,.75,1,1.25,1.5,1.75,2]}};this.player=new l.a("#player-".concat(this.file.id),t),this.player.on("ready",(function(){e.player.on("play",e.playVideo),e.player.on("pause",e.pauseVideo),e.player.on("ratechange",e.changeSpeed),e.player.on("timeupdate",e.timeupdate),e.currentTime=e.file.asset.currentTime||0,e.player.currentTime=e.currentTime,e.player.pause(),e.duration=e.player.duration,e.resize(),e.createTimeMarketContainer(),e.loaded=!0})),new ResizeObserver(this.resize).observe(this.$refs.childComponent)},beforeDestroy:function(){this.player.destroy()},methods:{resize:function(){if(!this.width){var e={width:this.$refs.childComponent.getBoundingClientRect().width/this.zoomIndex,height:this.$refs.childComponent.getBoundingClientRect().height/this.zoomIndex,originalWidth:h.j};this.$store.commit("classroom/moveAsset",{id:this.file.id,asset:e}),this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.file.lessonId,asset:e})}},onPlayerChange:function(e,t){if(this.changedBySocket)this.changedBySocket=!1;else{var data={id:this.file.id,lessonId:this.file.lessonId,asset:t};this.isSocketConnected&&this.$socket.emit("video-updated",data),this.$store.dispatch("classroom/updateAssetWithoutSync",data)}},playVideo:function(e){this.onPlayerChange(e,{currentTime:e.detail.plyr.media.currentTime,play:!0})},pauseVideo:function(e){this.onPlayerChange(e,{currentTime:e.detail.plyr.media.currentTime,play:!1})},timeupdate:function(){this.currentTime=this.player.currentTime},changeSpeed:function(e){this.onPlayerChange(e,{speed:e.detail.plyr.config.speed.selected})},createTimeMarketContainer:function(){var e=this;if(this.timeMarkerContainer=document.getElementById("time-markers-".concat(this.file.id)),!this.timeMarkerContainer){var t=document.getElementById("video-".concat(this.file.id)).getElementsByClassName("plyr__progress__buffer")[0];this.timeMarkerContainer=document.createElement("div"),this.timeMarkerContainer.setAttribute("id","time-markers-".concat(this.file.id)),this.timeMarkerContainer.setAttribute("class","time-markers"),t.before(this.timeMarkerContainer),this.shapes.length&&this.shapes.forEach((function(t){return e.addMarker(t)}))}},addMarker:function(e){var t=this;if(e){var o=(100*e.time/this.duration).toFixed(2).toString(),span=document.createElement("span");span.setAttribute("class","time-markers-item"),span.setAttribute("style","left: ".concat(o,"%;")),span.addEventListener("click",(function(){t.currentTime=e.time,t.player.currentTime=e.time,t.player.play()}),!1),this.timeMarkerContainer.appendChild(span)}}},sockets:{"video-updated":function(data){data.id===this.file.id&&(this.changedBySocket=!0,this.$store.commit("classroom/updateAsset",data),data.asset.speed&&(this.player.speed=data.asset.speed),data.asset.currentTime&&(this.player.currentTime=data.asset.currentTime),"play"in data.asset&&(data.asset.play?(this.isUserInteracted||(this.player.muted=!0),this.player.play()):this.player.pause()))}}},y=(o(1775),o(22)),component=Object(y.a)(m,(function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("classroom-container",{attrs:{asset:e.file,"child-header-height":80}},[o("classroom-container-header",{attrs:{file:e.file,title:e.player.config.title}},[o("div",{ref:"childComponent",attrs:{id:"video-"+e.file.id}},[o("div",{attrs:{id:"player-"+e.file.id,"data-plyr-embed-id":e.file.asset.videoId,"data-plyr-provider":"youtube"}})]),e._v(" "),o("div",{staticClass:"transparent"},[e.file&&e.width&&e.height?o("konva",{style:{marginTop:"-"+e.height+"px"},attrs:{file:e.file,width:e.width,height:e.height,scale:e.scale,"current-time":e.currentTime}}):e._e()],1)])],1)}),[],!1,null,null,null);t.default=component.exports;installComponents(component,{ClassroomContainerHeader:o(1459).default,ClassroomContainer:o(1389).default})}}]);