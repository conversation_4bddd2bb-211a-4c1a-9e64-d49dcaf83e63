FROM php:8.1-fpm

RUN apt-get update && apt-get install -y \
    openssl \
    git \
    unzip \
    curl \
    libjpeg-dev \
    zlib1g-dev \
    libpng-dev \
    libfreetype6-dev \
    libssl-dev

RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install gd

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer \
&& composer --version

RUN ln -snf /usr/share/zoneinfo/${TIMEZONE} /etc/localtime && echo ${TIMEZONE} > /etc/timezone \
&& printf '[PHP]\ndate.timezone = "%s"\n', ${TIMEZONE} > /usr/local/etc/php/conf.d/tzone.ini \
&& "date"

RUN pecl install -o -f redis \
&&  rm -rf /tmp/pear \
&&  docker-php-ext-enable redis

RUN pecl install -o -f timezonedb \
&&  rm -rf /tmp/pear \
&&  docker-php-ext-enable timezonedb

RUN apt-get install -y libicu-dev g++

RUN docker-php-ext-install pdo pdo_mysql intl

ADD https://pecl.php.net/get/apcu-5.1.22.tgz /tmp/apcu.tar.gz
RUN mkdir -p /usr/src/php/ext/apcu \
  && tar xf /tmp/apcu.tar.gz -C /usr/src/php/ext/apcu --strip-components=1

# configure and install
RUN docker-php-ext-configure apcu \
  && docker-php-ext-install apcu

RUN rm -rd /usr/src/php/ext/apcu && rm /tmp/apcu.tar.gz

RUN apt-get install -y libssl1.1

RUN apt-get install -y libzip-dev zip \
   && docker-php-ext-install zip

RUN apt-get install -y libfreetype6-dev libonig-dev libmcrypt-dev libfontconfig1 libxrender1 libxml2-dev

RUN apt-get install -y cron nano \
        && docker-php-ext-install bcmath mysqli mbstring opcache soap

# Install Blackfire
RUN version=$(php -r "echo PHP_MAJOR_VERSION.PHP_MINOR_VERSION;") \
    && curl -A "Docker" -o /tmp/blackfire-probe.tar.gz -D - -L -s https://blackfire.io/api/v1/releases/probe/php/linux/amd64/$version \
    && tar zxpf /tmp/blackfire-probe.tar.gz -C /tmp \
    && mv /tmp/blackfire-*.so $(php -r "echo ini_get('extension_dir');")/blackfire.so \
    && printf "extension=blackfire.so\nblackfire.agent_socket=tcp://blackfire:8707\n" > $PHP_INI_DIR/conf.d/blackfire.ini

ARG USERID
ARG GROUPID

RUN usermod -u $USERID www-data
RUN groupmod -g $GROUPID www-data
RUN mkdir /var/www/langu
ADD memory.ini /usr/local/etc/php/conf.d/memory.ini

WORKDIR /var/www/langu

ADD crontab /etc/cron.d/hello-cron
RUN crontab -u www-data /etc/cron.d/hello-cron  # <---- setting crontab for user www-data
RUN chmod u+s /usr/sbin/cron  # <---- setting setuid
RUN touch /var/log/cron.log

COPY docker-entrypoint.sh /
RUN chmod +x /docker-entrypoint.sh
ENTRYPOINT ["/docker-entrypoint.sh"]

RUN chown -R $USERID:$GROUPID /var/log
