{"name": "l2-live", "version": "0.0.1", "description": "L2 Live components", "main": "main.js", "dependencies": {"body-parser": "^1.17.1", "collections": "^5.0.6", "cookie": "^0.3.1", "dotenv": "^4.0.0", "express": "^4.15.2", "http": "0.0.0", "node-cleanup": "^2.1.2", "php-unserialize": "0.0.1", "redis": "^2.6.5", "request": "^2.81.0", "socket.io": "^2.0.1", "sorted-map": "^0.1.8", "winston": "^3.3.3"}, "devDependencies": {}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC"}