(window.webpackJsonp=window.webpackJsonp||[]).push([[2],{1426:function(t,e,n){"use strict";n.d(e,"a",(function(){return ve})),n.d(e,"b",(function(){return xt})),n.d(e,"c",(function(){return gt})),n.d(e,"d",(function(){return Oe})),n.d(e,"e",(function(){return Se})),n.d(e,"f",(function(){return et})),n.d(e,"g",(function(){return Ce})),n.d(e,"h",(function(){return qt})),n.d(e,"i",(function(){return Kt})),n.d(e,"j",(function(){return Gt})),n.d(e,"k",(function(){return Xt})),n.d(e,"l",(function(){return U})),n.d(e,"m",(function(){return Qt})),n.d(e,"n",(function(){return Ut})),n.d(e,"o",(function(){return St})),n.d(e,"p",(function(){return re})),n.d(e,"q",(function(){return Dt})),n.d(e,"r",(function(){return be})),n.d(e,"s",(function(){return Me})),n.d(e,"t",(function(){return Q})),n.d(e,"u",(function(){return we})),n.d(e,"v",(function(){return oe})),n.d(e,"w",(function(){return ke})),n.d(e,"x",(function(){return xe}));var r=n(1599),o=n(1728),l=n(1827),c=n(1590),d=n(1576),h=n(1569);const f=(t,e)=>!t.selection.empty&&(e&&e(t.tr.deleteSelection().scrollIntoView()),!0);function m(t,view){let{$cursor:e}=t.selection;return!e||(view?!view.endOfTextblock("backward",t):e.parentOffset>0)?null:e}const y=(t,e,view)=>{let n=m(t,view);if(!n)return!1;let r=x(n);if(!r){let r=n.blockRange(),o=r&&Object(d.j)(r);return null!=o&&(e&&e(t.tr.lift(r,o).scrollIntoView()),!0)}let o=r.nodeBefore;if(j(t,r,e,-1))return!0;if(0==n.parent.content.size&&(w(o,"end")||h.c.isSelectable(o)))for(let l=n.depth;;l--){let f=Object(d.k)(t.doc,n.before(l),n.after(l),c.j.empty);if(f&&f.slice.size<f.to-f.from){if(e){let tr=t.tr.step(f);tr.setSelection(w(o,"end")?h.f.findFrom(tr.doc.resolve(tr.mapping.map(r.pos,-1)),-1):h.c.create(tr.doc,r.pos-o.nodeSize)),e(tr.scrollIntoView())}return!0}if(1==l||n.node(l-1).childCount>1)break}return!(!o.isAtom||r.depth!=n.depth-1)&&(e&&e(t.tr.delete(r.pos-o.nodeSize,r.pos).scrollIntoView()),!0)};function v(t,e,n){let r=e.nodeBefore,o=e.pos-1;for(;!r.isTextblock;o--){if(r.type.spec.isolating)return!1;let t=r.lastChild;if(!t)return!1;r=t}let l=e.nodeAfter,f=e.pos+1;for(;!l.isTextblock;f++){if(l.type.spec.isolating)return!1;let t=l.firstChild;if(!t)return!1;l=t}let m=Object(d.k)(t.doc,o,f,c.j.empty);if(!m||m.from!=o||m instanceof d.c&&m.slice.size>=f-o)return!1;if(n){let tr=t.tr.step(m);tr.setSelection(h.h.create(tr.doc,o)),n(tr.scrollIntoView())}return!0}function w(t,e,n=!1){for(let r=t;r;r="start"==e?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)return!1}return!1}const k=(t,e,view)=>{let{$head:n,empty:r}=t.selection,o=n;if(!r)return!1;if(n.parent.isTextblock){if(view?!view.endOfTextblock("backward",t):n.parentOffset>0)return!1;o=x(n)}let l=o&&o.nodeBefore;return!(!l||!h.c.isSelectable(l))&&(e&&e(t.tr.setSelection(h.c.create(t.doc,o.pos-l.nodeSize)).scrollIntoView()),!0)};function x(t){if(!t.parent.type.spec.isolating)for(let i=t.depth-1;i>=0;i--){if(t.index(i)>0)return t.doc.resolve(t.before(i+1));if(t.node(i).type.spec.isolating)break}return null}function O(t,view){let{$cursor:e}=t.selection;return!e||(view?!view.endOfTextblock("forward",t):e.parentOffset<e.parent.content.size)?null:e}const S=(t,e,view)=>{let n=O(t,view);if(!n)return!1;let r=C(n);if(!r)return!1;let o=r.nodeAfter;if(j(t,r,e,1))return!0;if(0==n.parent.content.size&&(w(o,"start")||h.c.isSelectable(o))){let l=Object(d.k)(t.doc,n.before(),n.after(),c.j.empty);if(l&&l.slice.size<l.to-l.from){if(e){let tr=t.tr.step(l);tr.setSelection(w(o,"start")?h.f.findFrom(tr.doc.resolve(tr.mapping.map(r.pos)),1):h.c.create(tr.doc,tr.mapping.map(r.pos))),e(tr.scrollIntoView())}return!0}}return!(!o.isAtom||r.depth!=n.depth-1)&&(e&&e(t.tr.delete(r.pos,r.pos+o.nodeSize).scrollIntoView()),!0)},M=(t,e,view)=>{let{$head:n,empty:r}=t.selection,o=n;if(!r)return!1;if(n.parent.isTextblock){if(view?!view.endOfTextblock("forward",t):n.parentOffset<n.parent.content.size)return!1;o=C(n)}let l=o&&o.nodeAfter;return!(!l||!h.c.isSelectable(l))&&(e&&e(t.tr.setSelection(h.c.create(t.doc,o.pos)).scrollIntoView()),!0)};function C(t){if(!t.parent.type.spec.isolating)for(let i=t.depth-1;i>=0;i--){let e=t.node(i);if(t.index(i)+1<e.childCount)return t.doc.resolve(t.after(i+1));if(e.type.spec.isolating)break}return null}const T=(t,e)=>{let{$head:n,$anchor:r}=t.selection;return!(!n.parent.type.spec.code||!n.sameParent(r))&&(e&&e(t.tr.insertText("\n").scrollIntoView()),!0)};function E(t){for(let i=0;i<t.edgeCount;i++){let{type:e}=t.edge(i);if(e.isTextblock&&!e.hasRequiredAttrs())return e}return null}const A=(t,e)=>{let{$head:n,$anchor:r}=t.selection;if(!n.parent.type.spec.code||!n.sameParent(r))return!1;let o=n.node(-1),l=n.indexAfter(-1),c=E(o.contentMatchAt(l));if(!c||!o.canReplaceWith(l,l,c))return!1;if(e){let r=n.after(),tr=t.tr.replaceWith(r,r,c.createAndFill());tr.setSelection(h.f.near(tr.doc.resolve(r),1)),e(tr.scrollIntoView())}return!0},N=(t,e)=>{let n=t.selection,{$from:r,$to:o}=n;if(n instanceof h.a||r.parent.inlineContent||o.parent.inlineContent)return!1;let l=E(o.parent.contentMatchAt(o.indexAfter()));if(!l||!l.isTextblock)return!1;if(e){let n=(!r.parentOffset&&o.index()<o.parent.childCount?r:o).pos,tr=t.tr.insert(n,l.createAndFill());tr.setSelection(h.h.create(tr.doc,n+1)),e(tr.scrollIntoView())}return!0},D=(t,e)=>{let{$cursor:n}=t.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let r=n.before();if(Object(d.f)(t.doc,r))return e&&e(t.tr.split(r).scrollIntoView()),!0}let r=n.blockRange(),o=r&&Object(d.j)(r);return null!=o&&(e&&e(t.tr.lift(r,o).scrollIntoView()),!0)};const P=(t,e)=>{let{$from:n,$to:r}=t.selection;if(t.selection instanceof h.c&&t.selection.node.isBlock)return!(!n.parentOffset||!Object(d.f)(t.doc,n.pos)||(e&&e(t.tr.split(n.pos).scrollIntoView()),0));if(!n.depth)return!1;let o,l,c=[],f=!1,m=!1;for(let t=n.depth;;t--){if(n.node(t).isBlock){f=n.end(t)==n.pos+(n.depth-t),m=n.start(t)==n.pos-(n.depth-t),l=E(n.node(t-1).contentMatchAt(n.indexAfter(t-1)));let e=R&&R(r.parent,f,n);c.unshift(e||(f&&l?{type:l}:null)),o=t;break}if(1==t)return!1;c.unshift(null)}let tr=t.tr;(t.selection instanceof h.h||t.selection instanceof h.a)&&tr.deleteSelection();let y=tr.mapping.map(n.pos),v=Object(d.f)(tr.doc,y,c.length,c);if(v||(c[0]=l?{type:l}:null,v=Object(d.f)(tr.doc,y,c.length,c)),!v)return!1;if(tr.split(y,c.length,c),!f&&m&&n.node(o).type!=l){let t=tr.mapping.map(n.before(o)),e=tr.doc.resolve(t);l&&n.node(o-1).canReplaceWith(e.index(),e.index()+1,l)&&tr.setNodeMarkup(tr.mapping.map(n.before(o)),l)}return e&&e(tr.scrollIntoView()),!0};var R;function j(t,e,n,r){let o,l,f=e.nodeBefore,m=e.nodeAfter,y=f.type.spec.isolating||m.type.spec.isolating;if(!y&&function(t,e,n){let r=e.nodeBefore,o=e.nodeAfter,l=e.index();return!(!(r&&o&&r.type.compatibleContent(o.type))||(!r.content.size&&e.parent.canReplace(l-1,l)?(n&&n(t.tr.delete(e.pos-r.nodeSize,e.pos).scrollIntoView()),0):!e.parent.canReplace(l,l+1)||!o.isTextblock&&!Object(d.e)(t.doc,e.pos)||(n&&n(t.tr.join(e.pos).scrollIntoView()),0)))}(t,e,n))return!0;let v=!y&&e.parent.canReplace(e.index(),e.index()+1);if(v&&(o=(l=f.contentMatchAt(f.childCount)).findWrapping(m.type))&&l.matchType(o[0]||m.type).validEnd){if(n){let r=e.pos+m.nodeSize,l=c.c.empty;for(let i=o.length-1;i>=0;i--)l=c.c.from(o[i].create(null,l));l=c.c.from(f.copy(l));let tr=t.tr.step(new d.b(e.pos-1,r,e.pos,r,new c.j(l,1,0),o.length,!0)),h=tr.doc.resolve(r+2*o.length);h.nodeAfter&&h.nodeAfter.type==f.type&&Object(d.e)(tr.doc,h.pos)&&tr.join(h.pos),n(tr.scrollIntoView())}return!0}let k=m.type.spec.isolating||r>0&&y?null:h.f.findFrom(e,1),x=k&&k.$from.blockRange(k.$to),O=x&&Object(d.j)(x);if(null!=O&&O>=e.depth)return n&&n(t.tr.lift(x,O).scrollIntoView()),!0;if(v&&w(m,"start",!0)&&w(f,"end")){let r=f,o=[];for(;o.push(r),!r.isTextblock;)r=r.lastChild;let l=m,h=1;for(;!l.isTextblock;l=l.firstChild)h++;if(r.canReplace(r.childCount,r.childCount,l.content)){if(n){let r=c.c.empty;for(let i=o.length-1;i>=0;i--)r=c.c.from(o[i].copy(r));n(t.tr.step(new d.b(e.pos-o.length,e.pos+m.nodeSize,e.pos+h,e.pos+m.nodeSize-h,new c.j(r,o.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function I(t){return function(e,n){let r=e.selection,o=t<0?r.$from:r.$to,l=o.depth;for(;o.node(l).isInline;){if(!l)return!1;l--}return!!o.node(l).isTextblock&&(n&&n(e.tr.setSelection(h.h.create(e.doc,t<0?o.start(l):o.end(l)))),!0)}}const L=I(-1),B=I(1);function $(t,e=null){return function(n,r){let o=!1;for(let i=0;i<n.selection.ranges.length&&!o;i++){let{$from:{pos:r},$to:{pos:l}}=n.selection.ranges[i];n.doc.nodesBetween(r,l,((r,l)=>{if(o)return!1;if(r.isTextblock&&!r.hasMarkup(t,e))if(r.type==t)o=!0;else{let e=n.doc.resolve(l),r=e.index();o=e.parent.canReplaceWith(r,r+1,t)}}))}if(!o)return!1;if(r){let tr=n.tr;for(let i=0;i<n.selection.ranges.length;i++){let{$from:{pos:r},$to:{pos:o}}=n.selection.ranges[i];tr.setBlockType(r,o,t,e)}r(tr.scrollIntoView())}return!0}}function z(...t){return function(e,n,view){for(let i=0;i<t.length;i++)if(t[i](e,n,view))return!0;return!1}}let H=z(f,y,k),del=z(f,S,M);const V={Enter:z(T,N,D,P),"Mod-Enter":A,Backspace:H,"Mod-Backspace":H,"Shift-Backspace":H,Delete:del,"Mod-Delete":del,"Mod-a":(t,e)=>(e&&e(t.tr.setSelection(new h.a(t.doc))),!0)},F={"Ctrl-h":V.Backspace,"Alt-Backspace":V["Mod-Backspace"],"Ctrl-d":V.Delete,"Ctrl-Alt-Backspace":V["Mod-Delete"],"Alt-Delete":V["Mod-Delete"],"Alt-d":V["Mod-Delete"],"Ctrl-a":L,"Ctrl-e":B};for(let t in V)F[t]=V[t];"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):!("undefined"==typeof os||!os.platform)&&os.platform();function _(t,e=null){return function(n,r){let{$from:o,$to:l}=n.selection,h=o.blockRange(l);if(!h)return!1;let tr=r?n.tr:null;return!!function(tr,t,e,n=null){let r=!1,o=t,l=t.$from.doc;if(t.depth>=2&&t.$from.node(t.depth-1).type.compatibleContent(e)&&0==t.startIndex){if(0==t.$from.index(t.depth-1))return!1;let e=l.resolve(t.start-2);o=new c.g(e,e,t.depth),t.endIndex<t.parent.childCount&&(t=new c.g(t.$from,l.resolve(t.$to.end(t.depth)),t.depth)),r=!0}let h=Object(d.h)(o,e,n,t);if(!h)return!1;tr&&function(tr,t,e,n,r){let content=c.c.empty;for(let i=e.length-1;i>=0;i--)content=c.c.from(e[i].type.create(e[i].attrs,content));tr.step(new d.b(t.start-(n?2:0),t.end,t.start,t.end,new c.j(content,0,0),e.length,!0));let o=0;for(let i=0;i<e.length;i++)e[i].type==r&&(o=i+1);let l=e.length-o,h=t.start+e.length-(n?2:0),f=t.parent;for(let i=t.startIndex,e=t.endIndex,n=!0;i<e;i++,n=!1)!n&&Object(d.f)(tr.doc,h,l)&&(tr.split(h,l),h+=2*l),h+=f.child(i).nodeSize}(tr,t,h,r,e);return!0}(tr,h,t,e)&&(r&&r(tr.scrollIntoView()),!0)}}function W(t){return function(e,n){let{$from:r,$to:o}=e.selection,l=r.blockRange(o,(e=>e.childCount>0&&e.firstChild.type==t));return!!l&&(!n||(r.node(l.depth-1).type==t?function(t,e,n,r){let tr=t.tr,o=r.end,l=r.$to.end(r.depth);o<l&&(tr.step(new d.b(o-1,l,o,l,new c.j(c.c.from(n.create(null,r.parent.copy())),1,0),1,!0)),r=new c.g(tr.doc.resolve(r.$from.pos),tr.doc.resolve(l),r.depth));const h=Object(d.j)(r);if(null==h)return!1;tr.lift(r,h);let f=tr.doc.resolve(tr.mapping.map(o,-1)-1);Object(d.e)(tr.doc,f.pos)&&f.nodeBefore.type==f.nodeAfter.type&&tr.join(f.pos);return e(tr.scrollIntoView()),!0}(e,n,t,l):function(t,e,n){let tr=t.tr,r=n.parent;for(let t=n.end,i=n.endIndex-1,e=n.startIndex;i>e;i--)t-=r.child(i).nodeSize,tr.delete(t-1,t+1);let o=tr.doc.resolve(n.start),l=o.nodeAfter;if(tr.mapping.map(n.end)!=n.start+o.nodeAfter.nodeSize)return!1;let h=0==n.startIndex,f=n.endIndex==r.childCount,m=o.node(-1),y=o.index(-1);if(!m.canReplace(y+(h?0:1),y+1,l.content.append(f?c.c.empty:c.c.from(r))))return!1;let v=o.pos,w=v+l.nodeSize;return tr.step(new d.b(v-(h?1:0),w+(f?1:0),v+1,w-1,new c.j((h?c.c.empty:c.c.from(r.copy(c.c.empty))).append(f?c.c.empty:c.c.from(r.copy(c.c.empty))),h?0:1,f?0:1),h?0:1)),e(tr.scrollIntoView()),!0}(e,n,l)))}}function K(t){const{state:e,transaction:n}=t;let{selection:r}=n,{doc:o}=n,{storedMarks:l}=n;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return l},get selection(){return r},get doc(){return o},get tr(){return r=n.selection,o=n.doc,l=n.storedMarks,n}}}class J{constructor(t){this.editor=t.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=t.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:t,editor:e,state:n}=this,{view:view}=e,{tr:tr}=n,r=this.buildProps(tr);return Object.fromEntries(Object.entries(t).map((([t,e])=>[t,(...t)=>{const n=e(...t)(r);return tr.getMeta("preventDispatch")||this.hasCustomState||view.dispatch(tr),n}])))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(t,e=!0){const{rawCommands:n,editor:r,state:o}=this,{view:view}=r,l=[],c=!!t,tr=t||o.tr,d={...Object.fromEntries(Object.entries(n).map((([t,n])=>[t,(...t)=>{const r=this.buildProps(tr,e),o=n(...t)(r);return l.push(o),d}]))),run:()=>(c||!e||tr.getMeta("preventDispatch")||this.hasCustomState||view.dispatch(tr),l.every((t=>!0===t)))};return d}createCan(t){const{rawCommands:e,state:n}=this,tr=t||n.tr,r=this.buildProps(tr,false);return{...Object.fromEntries(Object.entries(e).map((([t,e])=>[t,(...t)=>e(...t)({...r,dispatch:void 0})]))),chain:()=>this.createChain(tr,false)}}buildProps(tr,t=!0){const{rawCommands:e,editor:n,state:r}=this,{view:view}=n,o={tr:tr,editor:n,view:view,state:K({state:r,transaction:tr}),dispatch:t?()=>{}:void 0,chain:()=>this.createChain(tr,t),can:()=>this.createCan(tr),get commands(){return Object.fromEntries(Object.entries(e).map((([t,e])=>[t,(...t)=>e(...t)(o)])))}};return o}}function U(t,e,n){if(void 0===t.config[e]&&t.parent)return U(t.parent,e,n);if("function"==typeof t.config[e]){return t.config[e].bind({...n,parent:t.parent?U(t.parent,e,n):null})}return t.config[e]}function G(t){return{baseExtensions:t.filter((t=>"extension"===t.type)),nodeExtensions:t.filter((t=>"node"===t.type)),markExtensions:t.filter((t=>"mark"===t.type))}}function Y(t){const e=[],{nodeExtensions:n,markExtensions:r}=G(t),o=[...n,...r],l={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return t.forEach((t=>{const n=U(t,"addGlobalAttributes",{name:t.name,options:t.options,storage:t.storage,extensions:o});if(!n)return;n().forEach((t=>{t.types.forEach((n=>{Object.entries(t.attributes).forEach((([t,r])=>{e.push({type:n,name:t,attribute:{...l,...r}})}))}))}))})),o.forEach((t=>{const n={name:t.name,options:t.options,storage:t.storage},r=U(t,"addAttributes",n);if(!r)return;const o=r();Object.entries(o).forEach((([n,r])=>{const o={...l,...r};"function"==typeof(null==o?void 0:o.default)&&(o.default=o.default()),(null==o?void 0:o.isRequired)&&void 0===(null==o?void 0:o.default)&&delete o.default,e.push({type:t.name,name:n,attribute:o})}))})),e}function X(t,e){if("string"==typeof t){if(!e.nodes[t])throw Error(`There is no node type named '${t}'. Maybe you forgot to add the extension?`);return e.nodes[t]}return t}function Q(...t){return t.filter((t=>!!t)).reduce(((t,e)=>{const n={...t};return Object.entries(e).forEach((([t,e])=>{if(n[t])if("class"===t){const r=e?String(e).split(" "):[],o=n[t]?n[t].split(" "):[],l=r.filter((t=>!o.includes(t)));n[t]=[...o,...l].join(" ")}else if("style"===t){const r=e?e.split(";").map((style=>style.trim())).filter(Boolean):[],o=n[t]?n[t].split(";").map((style=>style.trim())).filter(Boolean):[],l=new Map;o.forEach((style=>{const[t,e]=style.split(":").map((t=>t.trim()));l.set(t,e)})),r.forEach((style=>{const[t,e]=style.split(":").map((t=>t.trim()));l.set(t,e)})),n[t]=Array.from(l.entries()).map((([t,e])=>`${t}: ${e}`)).join("; ")}else n[t]=e;else n[t]=e})),n}),{})}function Z(t,e){return e.filter((e=>e.type===t.type.name)).filter((t=>t.attribute.rendered)).map((e=>e.attribute.renderHTML?e.attribute.renderHTML(t.attrs)||{}:{[e.name]:t.attrs[e.name]})).reduce(((t,e)=>Q(t,e)),{})}function tt(t){return"function"==typeof t}function et(t,e,...n){return tt(t)?e?t.bind(e)(...n):t(...n):t}function nt(t,e){return"style"in t?t:{...t,getAttrs:n=>{const r=t.getAttrs?t.getAttrs(n):t.attrs;if(!1===r)return!1;const o=e.reduce(((t,e)=>{const r=e.attribute.parseHTML?e.attribute.parseHTML(n):function(t){return"string"!=typeof t?t:t.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(t):"true"===t||"false"!==t&&t}(n.getAttribute(e.name));return null==r?t:{...t,[e.name]:r}}),{});return{...r,...o}}}}function ot(data){return Object.fromEntries(Object.entries(data).filter((([t,e])=>("attrs"!==t||!function(t={}){return 0===Object.keys(t).length&&t.constructor===Object}(e))&&null!=e)))}function it(t,e){var n;const r=Y(t),{nodeExtensions:o,markExtensions:l}=G(t),d=null===(n=o.find((t=>U(t,"topNode"))))||void 0===n?void 0:n.name,h=Object.fromEntries(o.map((n=>{const o=r.filter((t=>t.type===n.name)),l={name:n.name,options:n.options,storage:n.storage,editor:e},c=ot({...t.reduce(((t,e)=>{const r=U(e,"extendNodeSchema",l);return{...t,...r?r(n):{}}}),{}),content:et(U(n,"content",l)),marks:et(U(n,"marks",l)),group:et(U(n,"group",l)),inline:et(U(n,"inline",l)),atom:et(U(n,"atom",l)),selectable:et(U(n,"selectable",l)),draggable:et(U(n,"draggable",l)),code:et(U(n,"code",l)),whitespace:et(U(n,"whitespace",l)),linebreakReplacement:et(U(n,"linebreakReplacement",l)),defining:et(U(n,"defining",l)),isolating:et(U(n,"isolating",l)),attrs:Object.fromEntries(o.map((t=>{var e;return[t.name,{default:null===(e=null==t?void 0:t.attribute)||void 0===e?void 0:e.default}]})))}),d=et(U(n,"parseHTML",l));d&&(c.parseDOM=d.map((t=>nt(t,o))));const h=U(n,"renderHTML",l);h&&(c.toDOM=t=>h({node:t,HTMLAttributes:Z(t,o)}));const f=U(n,"renderText",l);return f&&(c.toText=f),[n.name,c]}))),f=Object.fromEntries(l.map((n=>{const o=r.filter((t=>t.type===n.name)),l={name:n.name,options:n.options,storage:n.storage,editor:e},c=ot({...t.reduce(((t,e)=>{const r=U(e,"extendMarkSchema",l);return{...t,...r?r(n):{}}}),{}),inclusive:et(U(n,"inclusive",l)),excludes:et(U(n,"excludes",l)),group:et(U(n,"group",l)),spanning:et(U(n,"spanning",l)),code:et(U(n,"code",l)),attrs:Object.fromEntries(o.map((t=>{var e;return[t.name,{default:null===(e=null==t?void 0:t.attribute)||void 0===e?void 0:e.default}]})))}),d=et(U(n,"parseHTML",l));d&&(c.parseDOM=d.map((t=>nt(t,o))));const h=U(n,"renderHTML",l);return h&&(c.toDOM=mark=>h({mark:mark,HTMLAttributes:Z(mark,o)})),[n.name,c]})));return new c.i({topNode:d,nodes:h,marks:f})}function st(t,e){return e.nodes[t]||e.marks[t]||null}function at(t,e){return Array.isArray(e)?e.some((e=>("string"==typeof e?e:e.name)===t.name)):e}function lt(t,e){const n=c.b.fromSchema(e).serializeFragment(t),r=document.implementation.createHTMLDocument().createElement("div");return r.appendChild(n),r.innerHTML}function ct(t){return"[object RegExp]"===Object.prototype.toString.call(t)}class ht{constructor(t){this.find=t.find,this.handler=t.handler}}function pt(t){var e;const{editor:n,from:r,to:o,text:text,rules:l,plugin:c}=t,{view:view}=n;if(view.composing)return!1;const d=view.state.doc.resolve(r);if(d.parent.type.spec.code||(null===(e=d.nodeBefore||d.nodeAfter)||void 0===e?void 0:e.marks.find((mark=>mark.type.spec.code))))return!1;let h=!1;const f=((t,e=500)=>{let n="";const r=t.parentOffset;return t.parent.nodesBetween(Math.max(0,r-e),r,((t,e,o,l)=>{var c,d;const h=(null===(d=(c=t.type.spec).toText)||void 0===d?void 0:d.call(c,{node:t,pos:e,parent:o,index:l}))||t.textContent||"%leaf%";n+=t.isAtom&&!t.isText?h:h.slice(0,Math.max(0,r-e))})),n})(d)+text;return l.forEach((t=>{if(h)return;const e=((text,t)=>{if(ct(t))return t.exec(text);const e=t(text);if(!e)return null;const n=[e.text];return n.index=e.index,n.input=text,n.data=e.data,e.replaceWith&&(e.text.includes(e.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),n.push(e.replaceWith)),n})(f,t.find);if(!e)return;const tr=view.state.tr,l=K({state:view.state,transaction:tr}),d={from:r-(e[0].length-text.length),to:o},{commands:m,chain:y,can:v}=new J({editor:n,state:l});null!==t.handler({state:l,range:d,match:e,commands:m,chain:y,can:v})&&tr.steps.length&&(tr.setMeta(c,{transform:tr,from:r,to:o,text:text}),view.dispatch(tr),h=!0)})),h}function ut(t){const{editor:e,rules:n}=t,o=new r.d({state:{init:()=>null,apply(tr,t,r){const l=tr.getMeta(o);if(l)return l;const d=tr.getMeta("applyInputRules");return!!d&&setTimeout((()=>{let{text:text}=d;text="string"==typeof text?text:lt(c.c.from(text),r.schema);const{from:t}=d,l=t+text.length;pt({editor:e,from:t,to:l,text:text,rules:n,plugin:o})})),tr.selectionSet||tr.docChanged?null:t}},props:{handleTextInput:(view,t,r,text)=>pt({editor:e,from:t,to:r,text:text,rules:n,plugin:o}),handleDOMEvents:{compositionend:view=>(setTimeout((()=>{const{$cursor:t}=view.state.selection;t&&pt({editor:e,from:t.pos,to:t.pos,text:"",rules:n,plugin:o})})),!1)},handleKeyDown(view,t){if("Enter"!==t.key)return!1;const{$cursor:r}=view.state.selection;return!!r&&pt({editor:e,from:r.pos,to:r.pos,text:"\n",rules:n,plugin:o})}},isInputRules:!0});return o}function ft(t){return"Object"===function(t){return Object.prototype.toString.call(t).slice(8,-1)}(t)&&(t.constructor===Object&&Object.getPrototypeOf(t)===Object.prototype)}function mt(t,source){const output={...t};return ft(t)&&ft(source)&&Object.keys(source).forEach((e=>{ft(source[e])&&ft(t[e])?output[e]=mt(t[e],source[e]):output[e]=source[e]})),output}class gt{constructor(t={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=et(U(this,"addOptions",{name:this.name}))),this.storage=et(U(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new gt(t)}configure(t={}){const e=this.extend({...this.config,addOptions:()=>mt(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){const e=new gt(t);return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=et(U(e,"addOptions",{name:e.name})),e.storage=et(U(e,"addStorage",{name:e.name,options:e.options})),e}static handleExit({editor:t,mark:mark}){const{tr:tr}=t.state,e=t.state.selection.$from;if(e.pos===e.end()){const n=e.marks();if(!!!n.find((t=>(null==t?void 0:t.type.name)===mark.name)))return!1;const r=n.find((t=>(null==t?void 0:t.type.name)===mark.name));return r&&tr.removeStoredMark(r),tr.insertText(" ",e.pos),t.view.dispatch(tr),!0}return!1}}class yt{constructor(t){this.find=t.find,this.handler=t.handler}}function vt(t){const{editor:e,state:n,from:r,to:o,rule:l,pasteEvent:c,dropEvent:d}=t,{commands:h,chain:f,can:m}=new J({editor:e,state:n}),y=[];n.doc.nodesBetween(r,o,((t,e)=>{if(!t.isTextblock||t.type.spec.code)return;const v=Math.max(r,e),w=Math.min(o,e+t.content.size);((text,t,e)=>{if(ct(t))return[...text.matchAll(t)];const n=t(text,e);return n?n.map((t=>{const e=[t.text];return e.index=t.index,e.input=text,e.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),e.push(t.replaceWith)),e})):[]})(t.textBetween(v-e,w-e,void 0,"￼"),l.find,c).forEach((t=>{if(void 0===t.index)return;const e=v+t.index+1,r=e+t[0].length,o={from:n.tr.mapping.map(e),to:n.tr.mapping.map(r)},w=l.handler({state:n,range:o,match:t,commands:h,chain:f,can:m,pasteEvent:c,dropEvent:d});y.push(w)}))}));return y.every((t=>null!==t))}let bt=null;function wt(t){const{editor:e,rules:n}=t;let o,l=null,d=!1,h=!1,f="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null;try{o="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{o=null}const m=({state:t,from:n,to:r,rule:l,pasteEvt:c})=>{const tr=t.tr,d=K({state:t,transaction:tr});if(vt({editor:e,state:d,from:Math.max(n-1,0),to:r.b-1,rule:l,pasteEvent:c,dropEvent:o})&&tr.steps.length){try{o="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{o=null}return f="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,tr}};return n.map((t=>new r.d({view(view){const t=t=>{var n;l=(null===(n=view.dom.parentElement)||void 0===n?void 0:n.contains(t.target))?view.dom.parentElement:null,l&&(bt=e)},n=()=>{bt&&(bt=null)};return window.addEventListener("dragstart",t),window.addEventListener("dragend",n),{destroy(){window.removeEventListener("dragstart",t),window.removeEventListener("dragend",n)}}},props:{handleDOMEvents:{drop:(view,t)=>{if(h=l===view.dom.parentElement,o=t,!h){const t=bt;(null==t?void 0:t.isEditable)&&setTimeout((()=>{const e=t.state.selection;e&&t.commands.deleteRange({from:e.from,to:e.to})}),10)}return!1},paste:(t,e)=>{var n;const html=null===(n=e.clipboardData)||void 0===n?void 0:n.getData("text/html");return f=e,d=!!(null==html?void 0:html.includes("data-pm-slice")),!1}}},appendTransaction:(e,n,r)=>{const o=e[0],l="paste"===o.getMeta("uiEvent")&&!d,y="drop"===o.getMeta("uiEvent")&&!h,v=o.getMeta("applyPasteRules"),w=!!v;if(!l&&!y&&!w)return;if(w){let{text:text}=v;text="string"==typeof text?text:lt(c.c.from(text),r.schema);const{from:e}=v,n=e+text.length,o=(text=>{var t;const e=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null===(t=e.clipboardData)||void 0===t||t.setData("text/html",text),e})(text);return m({rule:t,state:r,from:e,to:{b:n},pasteEvt:o})}const k=n.doc.content.findDiffStart(r.doc.content),x=n.doc.content.findDiffEnd(r.doc.content);return"number"==typeof k&&x&&k!==x.b?m({rule:t,state:r,from:k,to:x,pasteEvt:f}):void 0}})))}class kt{constructor(t,e){this.splittableMarks=[],this.editor=e,this.extensions=kt.resolve(t),this.schema=it(this.extensions,e),this.setupExtensions()}static resolve(t){const e=kt.sort(kt.flatten(t)),n=function(t){const e=t.filter(((e,n)=>t.indexOf(e)!==n));return Array.from(new Set(e))}(e.map((t=>t.name)));return n.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${n.map((t=>`'${t}'`)).join(", ")}]. This can lead to issues.`),e}static flatten(t){return t.map((t=>{const e=U(t,"addExtensions",{name:t.name,options:t.options,storage:t.storage});return e?[t,...this.flatten(e())]:t})).flat(10)}static sort(t){return t.sort(((a,b)=>{const t=U(a,"priority")||100,e=U(b,"priority")||100;return t>e?-1:t<e?1:0}))}get commands(){return this.extensions.reduce(((t,e)=>{const n=U(e,"addCommands",{name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:st(e.name,this.schema)});return n?{...t,...n()}:t}),{})}get plugins(){const{editor:t}=this,e=kt.sort([...this.extensions].reverse()),n=[],r=[],o=e.map((e=>{const o={name:e.name,options:e.options,storage:e.storage,editor:t,type:st(e.name,this.schema)},c=[],d=U(e,"addKeyboardShortcuts",o);let h={};if("mark"===e.type&&U(e,"exitable",o)&&(h.ArrowRight=()=>gt.handleExit({editor:t,mark:e})),d){const e=Object.fromEntries(Object.entries(d()).map((([e,n])=>[e,()=>n({editor:t})])));h={...h,...e}}const f=Object(l.b)(h);c.push(f);const m=U(e,"addInputRules",o);at(e,t.options.enableInputRules)&&m&&n.push(...m());const y=U(e,"addPasteRules",o);at(e,t.options.enablePasteRules)&&y&&r.push(...y());const v=U(e,"addProseMirrorPlugins",o);if(v){const t=v();c.push(...t)}return c})).flat();return[ut({editor:t,rules:n}),...wt({editor:t,rules:r}),...o]}get attributes(){return Y(this.extensions)}get nodeViews(){const{editor:t}=this,{nodeExtensions:e}=G(this.extensions);return Object.fromEntries(e.filter((t=>!!U(t,"addNodeView"))).map((e=>{const n=this.attributes.filter((t=>t.type===e.name)),r={name:e.name,options:e.options,storage:e.storage,editor:t,type:X(e.name,this.schema)},o=U(e,"addNodeView",r);if(!o)return[];return[e.name,(r,view,l,c,d)=>{const h=Z(r,n);return o()({node:r,view:view,getPos:l,decorations:c,innerDecorations:d,editor:t,extension:e,HTMLAttributes:h})}]})))}setupExtensions(){this.extensions.forEach((t=>{var e;this.editor.extensionStorage[t.name]=t.storage;const n={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:st(t.name,this.schema)};if("mark"===t.type){(null===(e=et(U(t,"keepOnSplit",n)))||void 0===e||e)&&this.splittableMarks.push(t.name)}const r=U(t,"onBeforeCreate",n),o=U(t,"onCreate",n),l=U(t,"onUpdate",n),c=U(t,"onSelectionUpdate",n),d=U(t,"onTransaction",n),h=U(t,"onFocus",n),f=U(t,"onBlur",n),m=U(t,"onDestroy",n);r&&this.editor.on("beforeCreate",r),o&&this.editor.on("create",o),l&&this.editor.on("update",l),c&&this.editor.on("selectionUpdate",c),d&&this.editor.on("transaction",d),h&&this.editor.on("focus",h),f&&this.editor.on("blur",f),m&&this.editor.on("destroy",m)}))}}class xt{constructor(t={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=et(U(this,"addOptions",{name:this.name}))),this.storage=et(U(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new xt(t)}configure(t={}){const e=this.extend({...this.config,addOptions:()=>mt(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){const e=new xt({...this.config,...t});return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=et(U(e,"addOptions",{name:e.name})),e.storage=et(U(e,"addStorage",{name:e.name,options:e.options})),e}}function Ot(t,e,n){const{from:r,to:o}=e,{blockSeparator:l="\n\n",textSerializers:c={}}=n||{};let text="";return t.nodesBetween(r,o,((t,n,d,h)=>{var f;t.isBlock&&n>r&&(text+=l);const m=null==c?void 0:c[t.type.name];if(m)return d&&(text+=m({node:t,pos:n,parent:d,index:h,range:e})),!1;t.isText&&(text+=null===(f=null==t?void 0:t.text)||void 0===f?void 0:f.slice(Math.max(r,n)-n,o-n))})),text}function St(t){return Object.fromEntries(Object.entries(t.nodes).filter((([,t])=>t.spec.toText)).map((([t,e])=>[t,e.spec.toText])))}const Mt=xt.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new r.d({key:new r.e("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:t}=this,{state:e,schema:n}=t,{doc:r,selection:o}=e,{ranges:l}=o,c=Math.min(...l.map((t=>t.$from.pos))),d=Math.max(...l.map((t=>t.$to.pos))),h=St(n);return Ot(r,{from:c,to:d},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:h})}}})]}});function Ct(t,e,n={strict:!0}){const r=Object.keys(e);return!r.length||r.every((r=>n.strict?e[r]===t[r]:ct(e[r])?e[r].test(t[r]):e[r]===t[r]))}function Tt(t,e,n={}){return t.find((t=>t.type===e&&Ct(Object.fromEntries(Object.keys(n).map((e=>[e,t.attrs[e]]))),n)))}function Et(t,e,n={}){return!!Tt(t,e,n)}function At(t,e,n){var r;if(!t||!e)return;let o=t.parent.childAfter(t.parentOffset);if(o.node&&o.node.marks.some((mark=>mark.type===e))||(o=t.parent.childBefore(t.parentOffset)),!o.node||!o.node.marks.some((mark=>mark.type===e)))return;n=n||(null===(r=o.node.marks[0])||void 0===r?void 0:r.attrs);if(!Tt([...o.node.marks],e,n))return;let l=o.index,c=t.start()+o.offset,d=l+1,h=c+o.node.nodeSize;for(;l>0&&Et([...t.parent.child(l-1).marks],e,n);)l-=1,c-=t.parent.child(l).nodeSize;for(;d<t.parent.childCount&&Et([...t.parent.child(d).marks],e,n);)h+=t.parent.child(d).nodeSize,d+=1;return{from:c,to:h}}function Nt(t,e){if("string"==typeof t){if(!e.marks[t])throw Error(`There is no mark type named '${t}'. Maybe you forgot to add the extension?`);return e.marks[t]}return t}function Dt(t){return t instanceof r.g}function Pt(t=0,e=0,n=0){return Math.min(Math.max(t,e),n)}function Rt(t,e=null){if(!e)return null;const n=r.f.atStart(t),o=r.f.atEnd(t);if("start"===e||!0===e)return n;if("end"===e)return o;const l=n.from,c=o.to;return"all"===e?r.g.create(t,Pt(0,l,c),Pt(t.content.size,l,c)):r.g.create(t,Pt(e,l,c),Pt(e,l,c))}function jt(){return"Android"===navigator.platform||/android/i.test(navigator.userAgent)}function It(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const Lt=t=>{const e=t.childNodes;for(let i=e.length-1;i>=0;i-=1){const n=e[i];3===n.nodeType&&n.nodeValue&&/^(\n\s\s|\n)$/.test(n.nodeValue)?t.removeChild(n):1===n.nodeType&&Lt(n)}return t};function Bt(t){const e=`<body>${t}</body>`,html=(new window.DOMParser).parseFromString(e,"text/html").body;return Lt(html)}function $t(content,t,e){if(content instanceof c.f||content instanceof c.c)return content;e={slice:!0,parseOptions:{},...e};const n="string"==typeof content;if("object"==typeof content&&null!==content)try{if(Array.isArray(content)&&content.length>0)return c.c.fromArray(content.map((e=>t.nodeFromJSON(e))));const n=t.nodeFromJSON(content);return e.errorOnInvalidContent&&n.check(),n}catch(n){if(e.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:n});return console.warn("[tiptap warn]: Invalid content.","Passed value:",content,"Error:",n),$t("",t,e)}if(n){if(e.errorOnInvalidContent){let n=!1,r="";const o=new c.i({topNode:t.spec.topNode,marks:t.spec.marks,nodes:t.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:t=>(n=!0,r="string"==typeof t?t:t.outerHTML,null)}]}})});if(e.slice?c.a.fromSchema(o).parseSlice(Bt(content),e.parseOptions):c.a.fromSchema(o).parse(Bt(content),e.parseOptions),e.errorOnInvalidContent&&n)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${r}`)})}const n=c.a.fromSchema(t);return e.slice?n.parseSlice(Bt(content),e.parseOptions).content:n.parse(Bt(content),e.parseOptions)}return $t("",t,e)}function zt(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}function Ht(t,e,n={}){const{from:r,to:o,empty:l}=t.selection,c=e?X(e,t.schema):null,d=[];t.doc.nodesBetween(r,o,((t,e)=>{if(t.isText)return;const n=Math.max(r,e),l=Math.min(o,e+t.nodeSize);d.push({node:t,from:n,to:l})}));const h=o-r,f=d.filter((t=>!c||c.name===t.node.type.name)).filter((t=>Ct(t.node.attrs,n,{strict:!1})));if(l)return!!f.length;return f.reduce(((t,e)=>t+e.to-e.from),0)>=h}function Vt(t,e){return e.nodes[t]?"node":e.marks[t]?"mark":null}function Ft(t,e){const n="string"==typeof e?[e]:e;return Object.keys(t).reduce(((e,r)=>(n.includes(r)||(e[r]=t[r]),e)),{})}function _t(content,t,e={},n={}){return $t(content,t,{slice:!1,parseOptions:e,errorOnInvalidContent:n.errorOnInvalidContent})}function Wt(t,e){const n=Nt(e,t.schema),{from:r,to:o,empty:l}=t.selection,c=[];l?(t.storedMarks&&c.push(...t.storedMarks),c.push(...t.selection.$head.marks())):t.doc.nodesBetween(r,o,(t=>{c.push(...t.marks)}));const mark=c.find((t=>t.type.name===n.name));return mark?{...mark.attrs}:{}}function qt(t,e){const n=new d.d(t);return e.forEach((t=>{t.steps.forEach((t=>{n.step(t)}))})),n}function Kt(t,e,n){const r=[];return t.nodesBetween(e.from,e.to,((t,e)=>{n(t)&&r.push({node:t,pos:e})})),r}function Jt(t){return e=>function(t,e){for(let i=t.depth;i>0;i-=1){const n=t.node(i);if(e(n))return{pos:i>0?t.before(i):0,start:t.start(i),depth:i,node:n}}}(e.$from,t)}function Ut(t,e){return Ot(t,{from:0,to:t.content.size},e)}function Gt(t,e){const n=Vt("string"==typeof e?e:e.name,t.schema);return"node"===n?function(t,e){const n=X(e,t.schema),{from:r,to:o}=t.selection,l=[];t.doc.nodesBetween(r,o,(t=>{l.push(t)}));const c=l.reverse().find((t=>t.type.name===n.name));return c?{...c.attrs}:{}}(t,e):"mark"===n?Wt(t,e):{}}function Yt(t){const e=function(t,e=JSON.stringify){const n={};return t.filter((t=>{const r=e(t);return!Object.prototype.hasOwnProperty.call(n,r)&&(n[r]=!0)}))}(t);return 1===e.length?e:e.filter(((t,n)=>!e.filter(((t,i)=>i!==n)).some((e=>t.oldRange.from>=e.oldRange.from&&t.oldRange.to<=e.oldRange.to&&t.newRange.from>=e.newRange.from&&t.newRange.to<=e.newRange.to))))}function Xt(t){const{mapping:e,steps:n}=t,r=[];return e.maps.forEach(((t,o)=>{const l=[];if(t.ranges.length)t.forEach(((t,e)=>{l.push({from:t,to:e})}));else{const{from:t,to:e}=n[o];if(void 0===t||void 0===e)return;l.push({from:t,to:e})}l.forEach((({from:t,to:n})=>{const l=e.slice(o).map(t,-1),c=e.slice(o).map(n),d=e.invert().map(l,-1),h=e.invert().map(c);r.push({oldRange:{from:d,to:h},newRange:{from:l,to:c}})}))})),Yt(r)}function Qt(t,e,n){const r=[];return t===e?n.resolve(t).marks().forEach((mark=>{const e=At(n.resolve(t),mark.type);e&&r.push({mark:mark,...e})})):n.nodesBetween(t,e,((t,e)=>{t&&void 0!==(null==t?void 0:t.nodeSize)&&r.push(...t.marks.map((mark=>({from:e,to:e+t.nodeSize,mark:mark}))))})),r}function Zt(t,e,n){return Object.fromEntries(Object.entries(n).filter((([n])=>{const r=t.find((t=>t.type===e&&t.name===n));return!!r&&r.attribute.keepOnSplit})))}function te(t,e,n={}){const{empty:r,ranges:o}=t.selection,l=e?Nt(e,t.schema):null;if(r)return!!(t.storedMarks||t.selection.$from.marks()).filter((mark=>!l||l.name===mark.type.name)).find((mark=>Ct(mark.attrs,n,{strict:!1})));let c=0;const d=[];if(o.forEach((({$from:e,$to:n})=>{const r=e.pos,o=n.pos;t.doc.nodesBetween(r,o,((t,e)=>{if(!t.isText&&!t.marks.length)return;const n=Math.max(r,e),l=Math.min(o,e+t.nodeSize);c+=l-n,d.push(...t.marks.map((mark=>({mark:mark,from:n,to:l}))))}))})),0===c)return!1;const h=d.filter((t=>!l||l.name===t.mark.type.name)).filter((t=>Ct(t.mark.attrs,n,{strict:!1}))).reduce(((t,e)=>t+e.to-e.from),0),f=d.filter((t=>!l||t.mark.type!==l&&t.mark.type.excludes(l))).reduce(((t,e)=>t+e.to-e.from),0);return(h>0?h+f:h)>=c}function ee(t,e){const{nodeExtensions:n}=G(e),r=n.find((e=>e.name===t));if(!r)return!1;const o=et(U(r,"group",{name:r.name,options:r.options,storage:r.storage}));return"string"==typeof o&&o.split(" ").includes("list")}function ne(t,{checkChildren:e=!0,ignoreWhitespace:n=!1}={}){var r;if(n){if("hardBreak"===t.type.name)return!0;if(t.isText)return/^\s*$/m.test(null!==(r=t.text)&&void 0!==r?r:"")}if(t.isText)return!t.text;if(t.isAtom||t.isLeaf)return!1;if(0===t.content.childCount)return!0;if(e){let r=!0;return t.content.forEach((t=>{!1!==r&&(ne(t,{ignoreWhitespace:n,checkChildren:e})||(r=!1))})),r}return!1}function re(t){return t instanceof r.c}function oe(view,t,e){const n=view.state.doc.content.size,r=Pt(t,0,n),o=Pt(e,0,n),l=view.coordsAtPos(r),c=view.coordsAtPos(o,-1),d=Math.min(l.top,c.top),h=Math.max(l.bottom,c.bottom),f=Math.min(l.left,c.left),m=Math.max(l.right,c.right),data={top:d,bottom:h,left:f,right:m,width:m-f,height:h-d,x:f,y:d};return{...data,toJSON:()=>data}}function ie(t,e){const n=t.storedMarks||t.selection.$to.parentOffset&&t.selection.$from.marks();if(n){const r=n.filter((mark=>null==e?void 0:e.includes(mark.type.name)));t.tr.ensureMarks(r)}}const se=(tr,t)=>{const e=Jt((e=>e.type===t))(tr.selection);if(!e)return!0;const n=tr.doc.resolve(Math.max(0,e.pos-1)).before(e.depth);if(void 0===n)return!0;const r=tr.doc.nodeAt(n);return e.node.type!==(null==r?void 0:r.type)||!Object(d.e)(tr.doc,e.pos)||(tr.join(e.pos),!0)},ae=(tr,t)=>{const e=Jt((e=>e.type===t))(tr.selection);if(!e)return!0;const n=tr.doc.resolve(e.start).after(e.depth);if(void 0===n)return!0;const r=tr.doc.nodeAt(n);return e.node.type!==(null==r?void 0:r.type)||!Object(d.e)(tr.doc,n)||(tr.join(n),!0)};var le=Object.freeze({__proto__:null,blur:()=>({editor:t,view:view})=>(requestAnimationFrame((()=>{var e;t.isDestroyed||(view.dom.blur(),null===(e=null===window||void 0===window?void 0:window.getSelection())||void 0===e||e.removeAllRanges())})),!0),clearContent:(t=!1)=>({commands:e})=>e.setContent("",t),clearNodes:()=>({state:t,tr:tr,dispatch:e})=>{const{selection:n}=tr,{ranges:r}=n;return!e||(r.forEach((({$from:e,$to:n})=>{t.doc.nodesBetween(e.pos,n.pos,((t,e)=>{if(t.type.isText)return;const{doc:n,mapping:r}=tr,o=n.resolve(r.map(e)),l=n.resolve(r.map(e+t.nodeSize)),c=o.blockRange(l);if(!c)return;const h=Object(d.j)(c);if(t.type.isTextblock){const{defaultType:t}=o.parent.contentMatchAt(o.index());tr.setNodeMarkup(c.start,t)}(h||0===h)&&tr.lift(c,h)}))})),!0)},command:t=>e=>t(e),createParagraphNear:()=>({state:t,dispatch:e})=>N(t,e),cut:(t,e)=>({editor:n,tr:tr})=>{const{state:o}=n,l=o.doc.slice(t.from,t.to);tr.deleteRange(t.from,t.to);const c=tr.mapping.map(e);return tr.insert(c,l.content),tr.setSelection(new r.g(tr.doc.resolve(c-1))),!0},deleteCurrentNode:()=>({tr:tr,dispatch:t})=>{const{selection:e}=tr,n=e.$anchor.node();if(n.content.size>0)return!1;const r=tr.selection.$anchor;for(let e=r.depth;e>0;e-=1){if(r.node(e).type===n.type){if(t){const t=r.before(e),n=r.after(e);tr.delete(t,n).scrollIntoView()}return!0}}return!1},deleteNode:t=>({tr:tr,state:e,dispatch:n})=>{const r=X(t,e.schema),o=tr.selection.$anchor;for(let t=o.depth;t>0;t-=1){if(o.node(t).type===r){if(n){const e=o.before(t),n=o.after(t);tr.delete(e,n).scrollIntoView()}return!0}}return!1},deleteRange:t=>({tr:tr,dispatch:e})=>{const{from:n,to:r}=t;return e&&tr.delete(n,r),!0},deleteSelection:()=>({state:t,dispatch:e})=>f(t,e),enter:()=>({commands:t})=>t.keyboardShortcut("Enter"),exitCode:()=>({state:t,dispatch:e})=>A(t,e),extendMarkRange:(t,e={})=>({tr:tr,state:n,dispatch:o})=>{const l=Nt(t,n.schema),{doc:c,selection:d}=tr,{$from:h,from:f,to:m}=d;if(o){const t=At(h,l,e);if(t&&t.from<=f&&t.to>=m){const e=r.g.create(c,t.from,t.to);tr.setSelection(e)}}return!0},first:t=>e=>{const n="function"==typeof t?t(e):t;for(let i=0;i<n.length;i+=1)if(n[i](e))return!0;return!1},focus:(t=null,e={})=>({editor:n,view:view,tr:tr,dispatch:r})=>{e={scrollIntoView:!0,...e};const o=()=>{(It()||jt())&&view.dom.focus(),requestAnimationFrame((()=>{n.isDestroyed||(view.focus(),(null==e?void 0:e.scrollIntoView)&&n.commands.scrollIntoView())}))};if(view.hasFocus()&&null===t||!1===t)return!0;if(r&&null===t&&!Dt(n.state.selection))return o(),!0;const l=Rt(tr.doc,t)||n.state.selection,c=n.state.selection.eq(l);return r&&(c||tr.setSelection(l),c&&tr.storedMarks&&tr.setStoredMarks(tr.storedMarks),o()),!0},forEach:(t,e)=>n=>t.every(((t,r)=>e(t,{...n,index:r}))),insertContent:(t,e)=>({tr:tr,commands:n})=>n.insertContentAt({from:tr.selection.from,to:tr.selection.to},t,e),insertContentAt:(t,e,n)=>({tr:tr,dispatch:o,editor:l})=>{var h;if(o){let content;const o=t=>{l.emit("contentError",{editor:l,error:t,disableCollaboration:()=>{l.storage.collaboration&&(l.storage.collaboration.isDisabled=!0)}})},f={preserveWhitespace:"full",...(n={parseOptions:l.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n}).parseOptions};if(!n.errorOnInvalidContent&&!l.options.enableContentCheck&&l.options.emitContentError)try{$t(e,l.schema,{parseOptions:f,errorOnInvalidContent:!0})}catch(t){o(t)}try{content=$t(e,l.schema,{parseOptions:f,errorOnInvalidContent:null!==(h=n.errorOnInvalidContent)&&void 0!==h?h:l.options.enableContentCheck})}catch(t){return o(t),!1}let{from:m,to:y}="number"==typeof t?{from:t,to:t}:{from:t.from,to:t.to},v=!0,w=!0;if(("type"in content?[content]:content).forEach((t=>{t.check(),v=!!v&&(t.isText&&0===t.marks.length),w=!!w&&t.isBlock})),m===y&&w){const{parent:t}=tr.doc.resolve(m);t.isTextblock&&!t.type.spec.code&&!t.childCount&&(m-=1,y+=1)}let k;if(v){if(Array.isArray(e))k=e.map((t=>t.text||"")).join("");else if(e instanceof c.c){let text="";e.forEach((t=>{t.text&&(text+=t.text)})),k=text}else k="object"==typeof e&&e&&e.text?e.text:e;tr.insertText(k,m,y)}else k=content,tr.replaceWith(m,y,k);n.updateSelection&&function(tr,t,e){const n=tr.steps.length-1;if(n<t)return;const o=tr.steps[n];if(!(o instanceof d.c||o instanceof d.b))return;const map=tr.mapping.maps[n];let l=0;map.forEach(((t,e,n,r)=>{0===l&&(l=r)})),tr.setSelection(r.f.near(tr.doc.resolve(l),e))}(tr,tr.steps.length-1,-1),n.applyInputRules&&tr.setMeta("applyInputRules",{from:m,text:k}),n.applyPasteRules&&tr.setMeta("applyPasteRules",{from:m,text:k})}return!0},joinBackward:()=>({state:t,dispatch:e})=>y(t,e),joinDown:()=>({state:t,dispatch:e})=>((t,e)=>{let n,r=t.selection;if(r instanceof h.c){if(r.node.isTextblock||!Object(d.e)(t.doc,r.to))return!1;n=r.to}else if(n=Object(d.i)(t.doc,r.to,1),null==n)return!1;return e&&e(t.tr.join(n).scrollIntoView()),!0})(t,e),joinForward:()=>({state:t,dispatch:e})=>S(t,e),joinItemBackward:()=>({state:t,dispatch:e,tr:tr})=>{try{const n=Object(d.i)(t.doc,t.selection.$from.pos,-1);return null!=n&&(tr.join(n,2),e&&e(tr),!0)}catch{return!1}},joinItemForward:()=>({state:t,dispatch:e,tr:tr})=>{try{const n=Object(d.i)(t.doc,t.selection.$from.pos,1);return null!=n&&(tr.join(n,2),e&&e(tr),!0)}catch{return!1}},joinTextblockBackward:()=>({state:t,dispatch:e})=>((t,e,view)=>{let n=m(t,view);if(!n)return!1;let r=x(n);return!!r&&v(t,r,e)})(t,e),joinTextblockForward:()=>({state:t,dispatch:e})=>((t,e,view)=>{let n=O(t,view);if(!n)return!1;let r=C(n);return!!r&&v(t,r,e)})(t,e),joinUp:()=>({state:t,dispatch:e})=>((t,e)=>{let n,r=t.selection,o=r instanceof h.c;if(o){if(r.node.isTextblock||!Object(d.e)(t.doc,r.from))return!1;n=r.from}else if(n=Object(d.i)(t.doc,r.from,-1),null==n)return!1;if(e){let tr=t.tr.join(n);o&&tr.setSelection(h.c.create(tr.doc,n-t.doc.resolve(n).nodeBefore.nodeSize)),e(tr.scrollIntoView())}return!0})(t,e),keyboardShortcut:t=>({editor:e,view:view,tr:tr,dispatch:n})=>{const r=function(t){const e=t.split(/-(?!$)/);let n,r,o,meta,l=e[e.length-1];"Space"===l&&(l=" ");for(let i=0;i<e.length-1;i+=1){const t=e[i];if(/^(cmd|meta|m)$/i.test(t))meta=!0;else if(/^a(lt)?$/i.test(t))n=!0;else if(/^(c|ctrl|control)$/i.test(t))r=!0;else if(/^s(hift)?$/i.test(t))o=!0;else{if(!/^mod$/i.test(t))throw new Error(`Unrecognized modifier name: ${t}`);It()||zt()?meta=!0:r=!0}}return n&&(l=`Alt-${l}`),r&&(l=`Ctrl-${l}`),meta&&(l=`Meta-${l}`),o&&(l=`Shift-${l}`),l}(t).split(/-(?!$)/),o=r.find((t=>!["Alt","Ctrl","Meta","Shift"].includes(t))),l=new KeyboardEvent("keydown",{key:"Space"===o?" ":o,altKey:r.includes("Alt"),ctrlKey:r.includes("Ctrl"),metaKey:r.includes("Meta"),shiftKey:r.includes("Shift"),bubbles:!0,cancelable:!0}),c=e.captureTransaction((()=>{view.someProp("handleKeyDown",(t=>t(view,l)))}));return null==c||c.steps.forEach((t=>{const e=t.map(tr.mapping);e&&n&&tr.maybeStep(e)})),!0},lift:(t,e={})=>({state:n,dispatch:r})=>!!Ht(n,X(t,n.schema),e)&&((t,e)=>{let{$from:n,$to:r}=t.selection,o=n.blockRange(r),l=o&&Object(d.j)(o);return null!=l&&(e&&e(t.tr.lift(o,l).scrollIntoView()),!0)})(n,r),liftEmptyBlock:()=>({state:t,dispatch:e})=>D(t,e),liftListItem:t=>({state:e,dispatch:n})=>W(X(t,e.schema))(e,n),newlineInCode:()=>({state:t,dispatch:e})=>T(t,e),resetAttributes:(t,e)=>({tr:tr,state:n,dispatch:r})=>{let o=null,l=null;const c=Vt("string"==typeof t?t:t.name,n.schema);return!!c&&("node"===c&&(o=X(t,n.schema)),"mark"===c&&(l=Nt(t,n.schema)),r&&tr.selection.ranges.forEach((t=>{n.doc.nodesBetween(t.$from.pos,t.$to.pos,((t,n)=>{o&&o===t.type&&tr.setNodeMarkup(n,void 0,Ft(t.attrs,e)),l&&t.marks.length&&t.marks.forEach((mark=>{l===mark.type&&tr.addMark(n,n+t.nodeSize,l.create(Ft(mark.attrs,e)))}))}))})),!0)},scrollIntoView:()=>({tr:tr,dispatch:t})=>(t&&tr.scrollIntoView(),!0),selectAll:()=>({tr:tr,dispatch:t})=>{if(t){const t=new r.a(tr.doc);tr.setSelection(t)}return!0},selectNodeBackward:()=>({state:t,dispatch:e})=>k(t,e),selectNodeForward:()=>({state:t,dispatch:e})=>M(t,e),selectParentNode:()=>({state:t,dispatch:e})=>((t,e)=>{let n,{$from:r,to:o}=t.selection,l=r.sharedDepth(o);return 0!=l&&(n=r.before(l),e&&e(t.tr.setSelection(h.c.create(t.doc,n))),!0)})(t,e),selectTextblockEnd:()=>({state:t,dispatch:e})=>B(t,e),selectTextblockStart:()=>({state:t,dispatch:e})=>L(t,e),setContent:(content,t=!1,e={},n={})=>({editor:r,tr:tr,dispatch:o,commands:l})=>{var c,d;const{doc:h}=tr;if("full"!==e.preserveWhitespace){const l=_t(content,r.schema,e,{errorOnInvalidContent:null!==(c=n.errorOnInvalidContent)&&void 0!==c?c:r.options.enableContentCheck});return o&&tr.replaceWith(0,h.content.size,l).setMeta("preventUpdate",!t),!0}return o&&tr.setMeta("preventUpdate",!t),l.insertContentAt({from:0,to:h.content.size},content,{parseOptions:e,errorOnInvalidContent:null!==(d=n.errorOnInvalidContent)&&void 0!==d?d:r.options.enableContentCheck})},setMark:(t,e={})=>({tr:tr,state:n,dispatch:r})=>{const{selection:o}=tr,{empty:l,ranges:c}=o,d=Nt(t,n.schema);if(r)if(l){const t=Wt(n,d);tr.addStoredMark(d.create({...t,...e}))}else c.forEach((t=>{const r=t.$from.pos,o=t.$to.pos;n.doc.nodesBetween(r,o,((t,n)=>{const l=Math.max(n,r),c=Math.min(n+t.nodeSize,o);t.marks.find((mark=>mark.type===d))?t.marks.forEach((mark=>{d===mark.type&&tr.addMark(l,c,d.create({...mark.attrs,...e}))})):tr.addMark(l,c,d.create(e))}))}));return function(t,tr,e){var n;const{selection:r}=tr;let cursor=null;if(Dt(r)&&(cursor=r.$cursor),cursor){const r=null!==(n=t.storedMarks)&&void 0!==n?n:cursor.marks();return!!e.isInSet(r)||!r.some((mark=>mark.type.excludes(e)))}const{ranges:o}=r;return o.some((({$from:n,$to:r})=>{let o=0===n.depth&&t.doc.inlineContent&&t.doc.type.allowsMarkType(e);return t.doc.nodesBetween(n.pos,r.pos,((t,n,r)=>{if(o)return!1;if(t.isInline){const n=!r||r.type.allowsMarkType(e),l=!!e.isInSet(t.marks)||!t.marks.some((t=>t.type.excludes(e)));o=n&&l}return!o})),o}))}(n,tr,d)},setMeta:(t,e)=>({tr:tr})=>(tr.setMeta(t,e),!0),setNode:(t,e={})=>({state:n,dispatch:r,chain:o})=>{const l=X(t,n.schema);let c;return n.selection.$anchor.sameParent(n.selection.$head)&&(c=n.selection.$anchor.parent.attrs),l.isTextblock?o().command((({commands:t})=>!!$(l,{...c,...e})(n)||t.clearNodes())).command((({state:t})=>$(l,{...c,...e})(t,r))).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:t=>({tr:tr,dispatch:e})=>{if(e){const{doc:e}=tr,n=Pt(t,0,e.content.size),o=r.c.create(e,n);tr.setSelection(o)}return!0},setTextSelection:t=>({tr:tr,dispatch:e})=>{if(e){const{doc:e}=tr,{from:n,to:o}="number"==typeof t?{from:t,to:t}:t,l=r.g.atStart(e).from,c=r.g.atEnd(e).to,d=Pt(n,l,c),h=Pt(o,l,c),f=r.g.create(e,d,h);tr.setSelection(f)}return!0},sinkListItem:t=>({state:e,dispatch:n})=>{const r=X(t,e.schema);return(o=r,function(t,e){let{$from:n,$to:r}=t.selection,l=n.blockRange(r,(t=>t.childCount>0&&t.firstChild.type==o));if(!l)return!1;let h=l.startIndex;if(0==h)return!1;let f=l.parent,m=f.child(h-1);if(m.type!=o)return!1;if(e){let n=m.lastChild&&m.lastChild.type==f.type,r=c.c.from(n?o.create():null),h=new c.j(c.c.from(o.create(null,c.c.from(f.type.create(null,r)))),n?3:1,0),y=l.start,v=l.end;e(t.tr.step(new d.b(y-(n?3:1),v,y,v,h,1,!0)).scrollIntoView())}return!0})(e,n);var o},splitBlock:({keepMarks:t=!0}={})=>({tr:tr,state:e,dispatch:n,editor:o})=>{const{selection:l,doc:c}=tr,{$from:h,$to:f}=l,m=Zt(o.extensionManager.attributes,h.node().type.name,h.node().attrs);if(l instanceof r.c&&l.node.isBlock)return!(!h.parentOffset||!Object(d.f)(c,h.pos))&&(n&&(t&&ie(e,o.extensionManager.splittableMarks),tr.split(h.pos).scrollIntoView()),!0);if(!h.parent.isBlock)return!1;const y=f.parentOffset===f.parent.content.size,v=0===h.depth?void 0:function(t){for(let i=0;i<t.edgeCount;i+=1){const{type:e}=t.edge(i);if(e.isTextblock&&!e.hasRequiredAttrs())return e}return null}(h.node(-1).contentMatchAt(h.indexAfter(-1)));let w=y&&v?[{type:v,attrs:m}]:void 0,k=Object(d.f)(tr.doc,tr.mapping.map(h.pos),1,w);if(w||k||!Object(d.f)(tr.doc,tr.mapping.map(h.pos),1,v?[{type:v}]:void 0)||(k=!0,w=v?[{type:v,attrs:m}]:void 0),n){if(k&&(l instanceof r.g&&tr.deleteSelection(),tr.split(tr.mapping.map(h.pos),1,w),v&&!y&&!h.parentOffset&&h.parent.type!==v)){const t=tr.mapping.map(h.before()),e=tr.doc.resolve(t);h.node(-1).canReplaceWith(e.index(),e.index()+1,v)&&tr.setNodeMarkup(tr.mapping.map(h.before()),v)}t&&ie(e,o.extensionManager.splittableMarks),tr.scrollIntoView()}return k},splitListItem:(t,e={})=>({tr:tr,state:n,dispatch:o,editor:l})=>{var h;const f=X(t,n.schema),{$from:m,$to:y}=n.selection,v=n.selection.node;if(v&&v.isBlock||m.depth<2||!m.sameParent(y))return!1;const w=m.node(-1);if(w.type!==f)return!1;const k=l.extensionManager.attributes;if(0===m.parent.content.size&&m.node(-1).childCount===m.indexAfter(-1)){if(2===m.depth||m.node(-3).type!==f||m.index(-2)!==m.node(-2).childCount-1)return!1;if(o){let t=c.c.empty;const n=m.index(-1)?1:m.index(-2)?2:3;for(let e=m.depth-n;e>=m.depth-3;e-=1)t=c.c.from(m.node(e).copy(t));const o=m.indexAfter(-1)<m.node(-2).childCount?1:m.indexAfter(-2)<m.node(-3).childCount?2:3,l={...Zt(k,m.node().type.name,m.node().attrs),...e},d=(null===(h=f.contentMatch.defaultType)||void 0===h?void 0:h.createAndFill(l))||void 0;t=t.append(c.c.from(f.createAndFill(null,d)||void 0));const y=m.before(m.depth-(n-1));tr.replace(y,m.after(-o),new c.j(t,4-n,0));let v=-1;tr.doc.nodesBetween(y,tr.doc.content.size,((t,e)=>{if(v>-1)return!1;t.isTextblock&&0===t.content.size&&(v=e+1)})),v>-1&&tr.setSelection(r.g.near(tr.doc.resolve(v))),tr.scrollIntoView()}return!0}const x=y.pos===m.end()?w.contentMatchAt(0).defaultType:null,O={...Zt(k,w.type.name,w.attrs),...e},S={...Zt(k,m.node().type.name,m.node().attrs),...e};tr.delete(m.pos,y.pos);const M=x?[{type:f,attrs:O},{type:x,attrs:S}]:[{type:f,attrs:O}];if(!Object(d.f)(tr.doc,m.pos,2))return!1;if(o){const{selection:t,storedMarks:e}=n,{splittableMarks:r}=l.extensionManager,c=e||t.$to.parentOffset&&t.$from.marks();if(tr.split(m.pos,2,M).scrollIntoView(),!c||!o)return!0;const d=c.filter((mark=>r.includes(mark.type.name)));tr.ensureMarks(d)}return!0},toggleList:(t,e,n,r={})=>({editor:o,tr:tr,state:l,dispatch:c,chain:d,commands:h,can:f})=>{const{extensions:m,splittableMarks:y}=o.extensionManager,v=X(t,l.schema),w=X(e,l.schema),{selection:k,storedMarks:x}=l,{$from:O,$to:S}=k,M=O.blockRange(S),C=x||k.$to.parentOffset&&k.$from.marks();if(!M)return!1;const T=Jt((t=>ee(t.type.name,m)))(k);if(M.depth>=1&&T&&M.depth-T.depth<=1){if(T.node.type===v)return h.liftListItem(w);if(ee(T.node.type.name,m)&&v.validContent(T.node.content)&&c)return d().command((()=>(tr.setNodeMarkup(T.pos,v),!0))).command((()=>se(tr,v))).command((()=>ae(tr,v))).run()}return n&&C&&c?d().command((()=>{const t=f().wrapInList(v,r),e=C.filter((mark=>y.includes(mark.type.name)));return tr.ensureMarks(e),!!t||h.clearNodes()})).wrapInList(v,r).command((()=>se(tr,v))).command((()=>ae(tr,v))).run():d().command((()=>!!f().wrapInList(v,r)||h.clearNodes())).wrapInList(v,r).command((()=>se(tr,v))).command((()=>ae(tr,v))).run()},toggleMark:(t,e={},n={})=>({state:r,commands:o})=>{const{extendEmptyMarkRange:l=!1}=n,c=Nt(t,r.schema);return te(r,c,e)?o.unsetMark(c,{extendEmptyMarkRange:l}):o.setMark(c,e)},toggleNode:(t,e,n={})=>({state:r,commands:o})=>{const l=X(t,r.schema),c=X(e,r.schema),d=Ht(r,l,n);let h;return r.selection.$anchor.sameParent(r.selection.$head)&&(h=r.selection.$anchor.parent.attrs),d?o.setNode(c,h):o.setNode(l,{...h,...n})},toggleWrap:(t,e={})=>({state:n,commands:r})=>{const o=X(t,n.schema);return Ht(n,o,e)?r.lift(o):r.wrapIn(o,e)},undoInputRule:()=>({state:t,dispatch:e})=>{const n=t.plugins;for(let i=0;i<n.length;i+=1){const r=n[i];let o;if(r.spec.isInputRules&&(o=r.getState(t))){if(e){const tr=t.tr,e=o.transform;for(let t=e.steps.length-1;t>=0;t-=1)tr.step(e.steps[t].invert(e.docs[t]));if(o.text){const e=tr.doc.resolve(o.from).marks();tr.replaceWith(o.from,o.to,t.schema.text(o.text,e))}else tr.delete(o.from,o.to)}return!0}}return!1},unsetAllMarks:()=>({tr:tr,dispatch:t})=>{const{selection:e}=tr,{empty:n,ranges:r}=e;return n||t&&r.forEach((t=>{tr.removeMark(t.$from.pos,t.$to.pos)})),!0},unsetMark:(t,e={})=>({tr:tr,state:n,dispatch:r})=>{var o;const{extendEmptyMarkRange:l=!1}=e,{selection:c}=tr,d=Nt(t,n.schema),{$from:h,empty:f,ranges:m}=c;if(!r)return!0;if(f&&l){let{from:t,to:e}=c;const n=null===(o=h.marks().find((mark=>mark.type===d)))||void 0===o?void 0:o.attrs,r=At(h,d,n);r&&(t=r.from,e=r.to),tr.removeMark(t,e,d)}else m.forEach((t=>{tr.removeMark(t.$from.pos,t.$to.pos,d)}));return tr.removeStoredMark(d),!0},updateAttributes:(t,e={})=>({tr:tr,state:n,dispatch:r})=>{let o=null,l=null;const c=Vt("string"==typeof t?t:t.name,n.schema);return!!c&&("node"===c&&(o=X(t,n.schema)),"mark"===c&&(l=Nt(t,n.schema)),r&&tr.selection.ranges.forEach((t=>{const r=t.$from.pos,c=t.$to.pos;let d,h,f,m;tr.selection.empty?n.doc.nodesBetween(r,c,((t,e)=>{o&&o===t.type&&(f=Math.max(e,r),m=Math.min(e+t.nodeSize,c),d=e,h=t)})):n.doc.nodesBetween(r,c,((t,n)=>{n<r&&o&&o===t.type&&(f=Math.max(n,r),m=Math.min(n+t.nodeSize,c),d=n,h=t),n>=r&&n<=c&&(o&&o===t.type&&tr.setNodeMarkup(n,void 0,{...t.attrs,...e}),l&&t.marks.length&&t.marks.forEach((mark=>{if(l===mark.type){const o=Math.max(n,r),d=Math.min(n+t.nodeSize,c);tr.addMark(o,d,l.create({...mark.attrs,...e}))}})))})),h&&(void 0!==d&&tr.setNodeMarkup(d,void 0,{...h.attrs,...e}),l&&h.marks.length&&h.marks.forEach((mark=>{l===mark.type&&tr.addMark(f,m,l.create({...mark.attrs,...e}))})))})),!0)},wrapIn:(t,e={})=>({state:n,dispatch:r})=>function(t,e=null){return function(n,r){let{$from:o,$to:l}=n.selection,c=o.blockRange(l),h=c&&Object(d.h)(c,t,e);return!!h&&(r&&r(n.tr.wrap(c,h).scrollIntoView()),!0)}}(X(t,n.schema),e)(n,r),wrapInList:(t,e={})=>({state:n,dispatch:r})=>_(X(t,n.schema),e)(n,r)});const ce=xt.create({name:"commands",addCommands:()=>({...le})}),de=xt.create({name:"drop",addProseMirrorPlugins(){return[new r.d({key:new r.e("tiptapDrop"),props:{handleDrop:(t,e,n,r)=>{this.editor.emit("drop",{editor:this.editor,event:e,slice:n,moved:r})}}})]}}),he=xt.create({name:"editable",addProseMirrorPlugins(){return[new r.d({key:new r.e("editable"),props:{editable:()=>this.editor.options.editable}})]}}),pe=new r.e("focusEvents"),ue=xt.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:t}=this;return[new r.d({key:pe,props:{handleDOMEvents:{focus:(view,e)=>{t.isFocused=!0;const n=t.state.tr.setMeta("focus",{event:e}).setMeta("addToHistory",!1);return view.dispatch(n),!1},blur:(view,e)=>{t.isFocused=!1;const n=t.state.tr.setMeta("blur",{event:e}).setMeta("addToHistory",!1);return view.dispatch(n),!1}}}})]}}),fe=xt.create({name:"keymap",addKeyboardShortcuts(){const t=()=>this.editor.commands.first((({commands:t})=>[()=>t.undoInputRule(),()=>t.command((({tr:tr})=>{const{selection:e,doc:n}=tr,{empty:o,$anchor:l}=e,{pos:c,parent:d}=l,h=l.parent.isTextblock&&c>0?tr.doc.resolve(c-1):l,f=h.parent.type.spec.isolating,m=l.pos-l.parentOffset,y=f&&1===h.parent.childCount?m===l.pos:r.f.atStart(n).from===c;return!(!o||!d.type.isTextblock||d.textContent.length||!y||y&&"paragraph"===l.parent.type.name)&&t.clearNodes()})),()=>t.deleteSelection(),()=>t.joinBackward(),()=>t.selectNodeBackward()])),e=()=>this.editor.commands.first((({commands:t})=>[()=>t.deleteSelection(),()=>t.deleteCurrentNode(),()=>t.joinForward(),()=>t.selectNodeForward()])),n={Enter:()=>this.editor.commands.first((({commands:t})=>[()=>t.newlineInCode(),()=>t.createParagraphNear(),()=>t.liftEmptyBlock(),()=>t.splitBlock()])),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:t,"Mod-Backspace":t,"Shift-Backspace":t,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},o={...n},l={...n,"Ctrl-h":t,"Alt-Backspace":t,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return It()||zt()?l:o},addProseMirrorPlugins(){return[new r.d({key:new r.e("clearDocument"),appendTransaction:(t,e,n)=>{if(t.some((tr=>tr.getMeta("composition"))))return;const o=t.some((t=>t.docChanged))&&!e.doc.eq(n.doc),l=t.some((t=>t.getMeta("preventClearDocument")));if(!o||l)return;const{empty:c,from:d,to:h}=e.selection,f=r.f.atStart(e.doc).from,m=r.f.atEnd(e.doc).to;if(c||!(d===f&&h===m))return;if(!ne(n.doc))return;const tr=n.tr,y=K({state:n,transaction:tr}),{commands:v}=new J({editor:this.editor,state:y});return v.clearNodes(),tr.steps.length?tr:void 0}})]}}),me=xt.create({name:"paste",addProseMirrorPlugins(){return[new r.d({key:new r.e("tiptapPaste"),props:{handlePaste:(t,e,n)=>{this.editor.emit("paste",{editor:this.editor,event:e,slice:n})}}})]}}),ge=xt.create({name:"tabindex",addProseMirrorPlugins(){return[new r.d({key:new r.e("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});class ye{get name(){return this.node.type.name}constructor(t,e,n=!1,r=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=t,this.editor=e,this.currentNode=r}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var t;return null!==(t=this.actualDepth)&&void 0!==t?t:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(content){let t=this.from,e=this.to;if(this.isBlock){if(0===this.content.size)return void console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);t=this.from+1,e=this.to-1}this.editor.commands.insertContentAt({from:t,to:e},content)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(0===this.depth)return null;const t=this.resolvedPos.start(this.resolvedPos.depth-1),e=this.resolvedPos.doc.resolve(t);return new ye(e,this.editor)}get before(){let t=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return t.depth!==this.depth&&(t=this.resolvedPos.doc.resolve(this.from-3)),new ye(t,this.editor)}get after(){let t=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return t.depth!==this.depth&&(t=this.resolvedPos.doc.resolve(this.to+3)),new ye(t,this.editor)}get children(){const t=[];return this.node.content.forEach(((e,n)=>{const r=e.isBlock&&!e.isTextblock,o=e.isAtom&&!e.isText,l=this.pos+n+(o?0:1);if(l<0||l>this.resolvedPos.doc.nodeSize-2)return;const c=this.resolvedPos.doc.resolve(l);if(!r&&c.depth<=this.depth)return;const d=new ye(c,this.editor,r,r?e:null);r&&(d.actualDepth=this.depth+1),t.push(new ye(c,this.editor,r,r?e:null))})),t}get firstChild(){return this.children[0]||null}get lastChild(){const t=this.children;return t[t.length-1]||null}closest(t,e={}){let n=null,r=this.parent;for(;r&&!n;){if(r.node.type.name===t)if(Object.keys(e).length>0){const t=r.node.attrs,n=Object.keys(e);for(let r=0;r<n.length;r+=1){const o=n[r];if(t[o]!==e[o])break}}else n=r;r=r.parent}return n}querySelector(t,e={}){return this.querySelectorAll(t,e,!0)[0]||null}querySelectorAll(t,e={},n=!1){let r=[];if(!this.children||0===this.children.length)return r;const o=Object.keys(e);return this.children.forEach((l=>{if(!(n&&r.length>0)){if(l.node.type.name===t){o.every((t=>e[t]===l.node.attrs[t]))&&r.push(l)}n&&r.length>0||(r=r.concat(l.querySelectorAll(t,e,n)))}})),r}setAttribute(t){const{tr:tr}=this.editor.state;tr.setNodeMarkup(this.from,void 0,{...this.node.attrs,...t}),this.editor.view.dispatch(tr)}}class ve extends class{constructor(){this.callbacks={}}on(t,e){return this.callbacks[t]||(this.callbacks[t]=[]),this.callbacks[t].push(e),this}emit(t,...e){const n=this.callbacks[t];return n&&n.forEach((t=>t.apply(this,e))),this}off(t,e){const n=this.callbacks[t];return n&&(e?this.callbacks[t]=n.filter((t=>t!==e)):delete this.callbacks[t]),this}once(t,e){const n=(...r)=>{this.off(t,n),e.apply(this,r)};return this.on(t,n)}removeAllListeners(){this.callbacks={}}}{constructor(t={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,emitContentError:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:t})=>{throw t},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(t),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",(({event:t,slice:e,moved:n})=>this.options.onDrop(t,e,n))),this.on("paste",(({event:t,slice:e})=>this.options.onPaste(t,e))),window.setTimeout((()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)}),0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(style,t,e){const n=document.querySelector(`style[data-tiptap-style${e?`-${e}`:""}]`);if(null!==n)return n;const r=document.createElement("style");return t&&r.setAttribute("nonce",t),r.setAttribute("data-tiptap-style"+(e?`-${e}`:""),""),r.innerHTML=style,document.getElementsByTagName("head")[0].appendChild(r),r}('.ProseMirror {\n  position: relative;\n}\n\n.ProseMirror {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  white-space: break-spaces;\n  -webkit-font-variant-ligatures: none;\n  font-variant-ligatures: none;\n  font-feature-settings: "liga" 0; /* the above doesn\'t seem to work in Edge */\n}\n\n.ProseMirror [contenteditable="false"] {\n  white-space: normal;\n}\n\n.ProseMirror [contenteditable="false"] [contenteditable="true"] {\n  white-space: pre-wrap;\n}\n\n.ProseMirror pre {\n  white-space: pre-wrap;\n}\n\nimg.ProseMirror-separator {\n  display: inline !important;\n  border: none !important;\n  margin: 0 !important;\n  width: 0 !important;\n  height: 0 !important;\n}\n\n.ProseMirror-gapcursor {\n  display: none;\n  pointer-events: none;\n  position: absolute;\n  margin: 0;\n}\n\n.ProseMirror-gapcursor:after {\n  content: "";\n  display: block;\n  position: absolute;\n  top: -2px;\n  width: 20px;\n  border-top: 1px solid black;\n  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;\n}\n\n@keyframes ProseMirror-cursor-blink {\n  to {\n    visibility: hidden;\n  }\n}\n\n.ProseMirror-hideselection *::selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection *::-moz-selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection * {\n  caret-color: transparent;\n}\n\n.ProseMirror-focused .ProseMirror-gapcursor {\n  display: block;\n}\n\n.tippy-box[data-animation=fade][data-state=hidden] {\n  opacity: 0\n}',this.options.injectNonce))}setOptions(t={}){this.options={...this.options,...t},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(t,e=!0){this.setOptions({editable:t}),e&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(t,e){const n=tt(e)?e(t,[...this.state.plugins]):[...this.state.plugins,t],r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}unregisterPlugin(t){if(this.isDestroyed)return;const e=this.state.plugins;let n=e;if([].concat(t).forEach((t=>{const e="string"==typeof t?`${t}$`:t.key;n=n.filter((t=>!t.key.startsWith(e)))})),e.length===n.length)return;const r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}createExtensionManager(){var t,e;const n=[...this.options.enableCoreExtensions?[he,Mt.configure({blockSeparator:null===(e=null===(t=this.options.coreExtensionOptions)||void 0===t?void 0:t.clipboardTextSerializer)||void 0===e?void 0:e.blockSeparator}),ce,ue,fe,ge,de,me].filter((t=>"object"!=typeof this.options.enableCoreExtensions||!1!==this.options.enableCoreExtensions[t.name])):[],...this.options.extensions].filter((t=>["extension","node","mark"].includes(null==t?void 0:t.type)));this.extensionManager=new kt(n,this)}createCommandManager(){this.commandManager=new J({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var t;let e;try{e=_t(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(t){if(!(t instanceof Error&&["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(t.message)))throw t;this.emit("contentError",{editor:this,error:t,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter((t=>"collaboration"!==t.name)),this.createExtensionManager()}}),e=_t(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}const n=Rt(e,this.options.autofocus);this.view=new o.c(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...null===(t=this.options.editorProps)||void 0===t?void 0:t.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:r.b.create({doc:e,selection:n||void 0})});const l=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(l),this.createNodeViews(),this.prependClass();this.view.dom.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(t){this.isCapturingTransaction=!0,t(),this.isCapturingTransaction=!1;const tr=this.capturedTransaction;return this.capturedTransaction=null,tr}dispatchTransaction(t){if(this.view.isDestroyed)return;if(this.isCapturingTransaction)return this.capturedTransaction?void t.steps.forEach((t=>{var e;return null===(e=this.capturedTransaction)||void 0===e?void 0:e.step(t)})):void(this.capturedTransaction=t);const e=this.state.apply(t),n=!this.state.selection.eq(e.selection);this.emit("beforeTransaction",{editor:this,transaction:t,nextState:e}),this.view.updateState(e),this.emit("transaction",{editor:this,transaction:t}),n&&this.emit("selectionUpdate",{editor:this,transaction:t});const r=t.getMeta("focus"),o=t.getMeta("blur");r&&this.emit("focus",{editor:this,event:r.event,transaction:t}),o&&this.emit("blur",{editor:this,event:o.event,transaction:t}),t.docChanged&&!t.getMeta("preventUpdate")&&this.emit("update",{editor:this,transaction:t})}getAttributes(t){return Gt(this.state,t)}isActive(t,e){const n="string"==typeof t?t:null,r="string"==typeof t?e:t;return function(t,e,n={}){if(!e)return Ht(t,null,n)||te(t,null,n);const r=Vt(e,t.schema);return"node"===r?Ht(t,e,n):"mark"===r&&te(t,e,n)}(this.state,n,r)}getJSON(){return this.state.doc.toJSON()}getHTML(){return lt(this.state.doc.content,this.schema)}getText(t){const{blockSeparator:e="\n\n",textSerializers:n={}}=t||{};return Ut(this.state.doc,{blockSeparator:e,textSerializers:{...St(this.schema),...n}})}get isEmpty(){return ne(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){const t=this.view.dom;t&&t.editor&&delete t.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var t;return!(null===(t=this.view)||void 0===t?void 0:t.docView)}$node(t,e){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelector(t,e))||null}$nodes(t,e){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelectorAll(t,e))||null}$pos(t){const e=this.state.doc.resolve(t);return new ye(e,this)}get $doc(){return this.$pos(0)}}function be(t){return new ht({find:t.find,handler:({state:e,range:n,match:r})=>{const o=et(t.getAttributes,void 0,r);if(!1===o||null===o)return null;const{tr:tr}=e,l=r[r.length-1],c=r[0];if(l){const r=c.search(/\S/),d=n.from+c.indexOf(l),h=d+l.length;if(Qt(n.from,n.to,e.doc).filter((e=>e.mark.type.excluded.find((n=>n===t.type&&n!==e.mark.type)))).filter((t=>t.to>d)).length)return null;h<n.to&&tr.delete(h,n.to),d>n.from&&tr.delete(n.from+r,d);const f=n.from+r+l.length;tr.addMark(n.from+r,f,t.type.create(o||{})),tr.removeStoredMark(t.type)}}})}function we(t){return new ht({find:t.find,handler:({state:e,range:n,match:r})=>{const o=et(t.getAttributes,void 0,r)||{},{tr:tr}=e,l=n.from;let c=n.to;const d=t.type.create(o);if(r[1]){let t=l+r[0].lastIndexOf(r[1]);t>c?t=c:c=t+r[1].length;const e=r[0][r[0].length-1];tr.insertText(e,l+r[0].length-1),tr.replaceWith(t,c,d)}else if(r[0]){const e=t.type.isInline?l:l-1;tr.insert(e,t.type.create(o)).delete(tr.mapping.map(l),tr.mapping.map(c))}tr.scrollIntoView()}})}function ke(t){return new ht({find:t.find,handler:({state:e,range:n,match:r})=>{const o=e.doc.resolve(n.from),l=et(t.getAttributes,void 0,r)||{};if(!o.node(-1).canReplaceWith(o.index(-1),o.indexAfter(-1),t.type))return null;e.tr.delete(n.from,n.to).setBlockType(n.from,n.from,t.type,l)}})}function xe(t){return new ht({find:t.find,handler:({state:e,range:n,match:r,chain:o})=>{const l=et(t.getAttributes,void 0,r)||{},tr=e.tr.delete(n.from,n.to),c=tr.doc.resolve(n.from).blockRange(),h=c&&Object(d.h)(c,t.type,l);if(!h)return null;if(tr.wrap(c,h),t.keepMarks&&t.editor){const{selection:n,storedMarks:r}=e,{splittableMarks:o}=t.editor.extensionManager,l=r||n.$to.parentOffset&&n.$from.marks();if(l){const t=l.filter((mark=>o.includes(mark.type.name)));tr.ensureMarks(t)}}if(t.keepAttributes){const e="bulletList"===t.type.name||"orderedList"===t.type.name?"listItem":"taskList";o().updateAttributes(e,l).run()}const f=tr.doc.resolve(n.from-1).nodeBefore;f&&f.type===t.type&&Object(d.e)(tr.doc,n.from-1)&&(!t.joinPredicate||t.joinPredicate(r,f))&&tr.join(n.from-1)}})}class Oe{constructor(t={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=et(U(this,"addOptions",{name:this.name}))),this.storage=et(U(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new Oe(t)}configure(t={}){const e=this.extend({...this.config,addOptions:()=>mt(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){const e=new Oe(t);return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&Object.keys(t.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${e.name}".`),e.options=et(U(e,"addOptions",{name:e.name})),e.storage=et(U(e,"addStorage",{name:e.name,options:e.options})),e}}class Se{constructor(component,t,e){this.isDragging=!1,this.component=component,this.editor=t.editor,this.options={stopEvent:null,ignoreMutation:null,...e},this.extension=t.extension,this.node=t.node,this.decorations=t.decorations,this.innerDecorations=t.innerDecorations,this.view=t.view,this.HTMLAttributes=t.HTMLAttributes,this.getPos=t.getPos,this.mount()}mount(){}get dom(){return this.editor.view.dom}get contentDOM(){return null}onDragStart(t){var e,n,o,l,c,d,h;const{view:view}=this.editor,f=t.target,m=3===f.nodeType?null===(e=f.parentElement)||void 0===e?void 0:e.closest("[data-drag-handle]"):f.closest("[data-drag-handle]");if(!this.dom||(null===(n=this.contentDOM)||void 0===n?void 0:n.contains(f))||!m)return;let y=0,v=0;if(this.dom!==m){const e=this.dom.getBoundingClientRect(),n=m.getBoundingClientRect(),r=null!==(o=t.offsetX)&&void 0!==o?o:null===(l=t.nativeEvent)||void 0===l?void 0:l.offsetX,h=null!==(c=t.offsetY)&&void 0!==c?c:null===(d=t.nativeEvent)||void 0===d?void 0:d.offsetY;y=n.x-e.x+r,v=n.y-e.y+h}const w=this.dom.cloneNode(!0);null===(h=t.dataTransfer)||void 0===h||h.setDragImage(w,y,v);const k=this.getPos();if("number"!=typeof k)return;const x=r.c.create(view.state.doc,k),O=view.state.tr.setSelection(x);view.dispatch(O)}stopEvent(t){var e;if(!this.dom)return!1;if("function"==typeof this.options.stopEvent)return this.options.stopEvent({event:t});const n=t.target;if(!(this.dom.contains(n)&&!(null===(e=this.contentDOM)||void 0===e?void 0:e.contains(n))))return!1;const o=t.type.startsWith("drag"),l="drop"===t.type;if((["INPUT","BUTTON","SELECT","TEXTAREA"].includes(n.tagName)||n.isContentEditable)&&!l&&!o)return!0;const{isEditable:c}=this.editor,{isDragging:d}=this,h=!!this.node.type.spec.draggable,f=r.c.isSelectable(this.node),m="copy"===t.type,y="paste"===t.type,v="cut"===t.type,w="mousedown"===t.type;if(!h&&f&&o&&t.target===this.dom&&t.preventDefault(),h&&o&&!d&&t.target===this.dom)return t.preventDefault(),!1;if(h&&c&&!d&&w){const t=n.closest("[data-drag-handle]");t&&(this.dom===t||this.dom.contains(t))&&(this.isDragging=!0,document.addEventListener("dragend",(()=>{this.isDragging=!1}),{once:!0}),document.addEventListener("drop",(()=>{this.isDragging=!1}),{once:!0}),document.addEventListener("mouseup",(()=>{this.isDragging=!1}),{once:!0}))}return!(d||l||m||y||v||w&&f)}ignoreMutation(t){if(!this.dom||!this.contentDOM)return!0;if("function"==typeof this.options.ignoreMutation)return this.options.ignoreMutation({mutation:t});if(this.node.isLeaf||this.node.isAtom)return!0;if("selection"===t.type)return!1;if(this.dom.contains(t.target)&&"childList"===t.type&&(It()||jt())&&this.editor.isFocused){if([...Array.from(t.addedNodes),...Array.from(t.removedNodes)].every((t=>t.isContentEditable)))return!1}return this.contentDOM===t.target&&"attributes"===t.type||!this.contentDOM.contains(t.target)}updateAttributes(t){this.editor.commands.command((({tr:tr})=>{const e=this.getPos();return"number"==typeof e&&(tr.setNodeMarkup(e,void 0,{...this.node.attrs,...t}),!0)}))}deleteNode(){const t=this.getPos();if("number"!=typeof t)return;const e=t+this.node.nodeSize;this.editor.commands.deleteRange({from:t,to:e})}}function Me(t){return new yt({find:t.find,handler:({state:e,range:n,match:r,pasteEvent:o})=>{const l=et(t.getAttributes,void 0,r,o);if(!1===l||null===l)return null;const{tr:tr}=e,c=r[r.length-1],d=r[0];let h=n.to;if(c){const r=d.search(/\S/),o=n.from+d.indexOf(c),f=o+c.length;if(Qt(n.from,n.to,e.doc).filter((e=>e.mark.type.excluded.find((n=>n===t.type&&n!==e.mark.type)))).filter((t=>t.to>o)).length)return null;f<n.to&&tr.delete(f,n.to),o>n.from&&tr.delete(n.from+r,o),h=n.from+r+c.length,tr.addMark(n.from+r,h,t.type.create(l||{})),tr.removeStoredMark(t.type)}}})}function Ce(t,e){const{selection:n}=t,{$from:o}=n;if(n instanceof r.c){const t=o.index();return o.parent.canReplaceWith(t,t+1,e)}let l=o.depth;for(;l>=0;){const t=o.index(l);if(o.node(l).contentMatchAt(t).matchType(e))return!0;l-=1}return!1}},1524:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.propOptionsGenerator=void 0;const r=n(1613);e.propOptionsGenerator=(t,e,...n)=>({optional:{type:t,required:!1,default:void 0,validator:(0,r.vuePropValidator)(e,...n)},nullable:{type:t,required:!1,default:null,validator:(0,r.vuePropValidator)(e,...n)},withDefault:o=>({type:t,required:!1,default:o,validator:(0,r.vuePropValidator)(e,...n)}),required:{type:t,required:!0,validator:(0,r.vuePropValidator)(e,...n)}})},1569:function(t,e,n){"use strict";n.d(e,"a",(function(){return k})),n.d(e,"b",(function(){return N})),n.d(e,"c",(function(){return v})),n.d(e,"d",(function(){return P})),n.d(e,"e",(function(){return I})),n.d(e,"f",(function(){return c})),n.d(e,"g",(function(){return d})),n.d(e,"h",(function(){return m}));var r=n(1590),o=n(1576);const l=Object.create(null);class c{constructor(t,e,n){this.$anchor=t,this.$head=e,this.ranges=n||[new d(t.min(e),t.max(e))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let t=this.ranges;for(let i=0;i<t.length;i++)if(t[i].$from.pos!=t[i].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(tr,content=r.j.empty){let t=content.content.lastChild,e=null;for(let i=0;i<content.openEnd;i++)e=t,t=t.lastChild;let n=tr.steps.length,o=this.ranges;for(let i=0;i<o.length;i++){let{$from:l,$to:c}=o[i],d=tr.mapping.slice(n);tr.replaceRange(d.map(l.pos),d.map(c.pos),i?r.j.empty:content),0==i&&S(tr,n,(t?t.isInline:e&&e.isTextblock)?-1:1)}}replaceWith(tr,t){let e=tr.steps.length,n=this.ranges;for(let i=0;i<n.length;i++){let{$from:r,$to:o}=n[i],l=tr.mapping.slice(e),c=l.map(r.pos),d=l.map(o.pos);i?tr.deleteRange(c,d):(tr.replaceRangeWith(c,d,t),S(tr,e,t.isInline?-1:1))}}static findFrom(t,e,n=!1){let r=t.parent.inlineContent?new m(t):O(t.node(0),t.parent,t.pos,t.index(),e,n);if(r)return r;for(let r=t.depth-1;r>=0;r--){let o=e<0?O(t.node(0),t.node(r),t.before(r+1),t.index(r),e,n):O(t.node(0),t.node(r),t.after(r+1),t.index(r)+1,e,n);if(o)return o}return null}static near(t,e=1){return this.findFrom(t,e)||this.findFrom(t,-e)||new k(t.node(0))}static atStart(t){return O(t,t,0,0,1)||new k(t)}static atEnd(t){return O(t,t,t.content.size,t.childCount,-1)||new k(t)}static fromJSON(t,e){if(!e||!e.type)throw new RangeError("Invalid input for Selection.fromJSON");let n=l[e.type];if(!n)throw new RangeError(`No selection type ${e.type} defined`);return n.fromJSON(t,e)}static jsonID(t,e){if(t in l)throw new RangeError("Duplicate use of selection JSON ID "+t);return l[t]=e,e.prototype.jsonID=t,e}getBookmark(){return m.between(this.$anchor,this.$head).getBookmark()}}c.prototype.visible=!0;class d{constructor(t,e){this.$from=t,this.$to=e}}let h=!1;function f(t){h||t.parent.inlineContent||(h=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+t.parent.type.name+")"))}class m extends c{constructor(t,e=t){f(t),f(e),super(t,e)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(t,e){let n=t.resolve(e.map(this.head));if(!n.parent.inlineContent)return c.near(n);let r=t.resolve(e.map(this.anchor));return new m(r.parent.inlineContent?r:n,n)}replace(tr,content=r.j.empty){if(super.replace(tr,content),content==r.j.empty){let t=this.$from.marksAcross(this.$to);t&&tr.ensureMarks(t)}}eq(t){return t instanceof m&&t.anchor==this.anchor&&t.head==this.head}getBookmark(){return new y(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(t,e){if("number"!=typeof e.anchor||"number"!=typeof e.head)throw new RangeError("Invalid input for TextSelection.fromJSON");return new m(t.resolve(e.anchor),t.resolve(e.head))}static create(t,e,head=e){let n=t.resolve(e);return new this(n,head==e?n:t.resolve(head))}static between(t,e,n){let r=t.pos-e.pos;if(n&&!r||(n=r>=0?1:-1),!e.parent.inlineContent){let t=c.findFrom(e,n,!0)||c.findFrom(e,-n,!0);if(!t)return c.near(e,n);e=t.$head}return t.parent.inlineContent||(0==r||(t=(c.findFrom(t,-n,!0)||c.findFrom(t,n,!0)).$anchor).pos<e.pos!=r<0)&&(t=e),new m(t,e)}}c.jsonID("text",m);class y{constructor(t,head){this.anchor=t,this.head=head}map(t){return new y(t.map(this.anchor),t.map(this.head))}resolve(t){return m.between(t.resolve(this.anchor),t.resolve(this.head))}}class v extends c{constructor(t){let e=t.nodeAfter,n=t.node(0).resolve(t.pos+e.nodeSize);super(t,n),this.node=e}map(t,e){let{deleted:n,pos:r}=e.mapResult(this.anchor),o=t.resolve(r);return n?c.near(o):new v(o)}content(){return new r.j(r.c.from(this.node),0,0)}eq(t){return t instanceof v&&t.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new w(this.anchor)}static fromJSON(t,e){if("number"!=typeof e.anchor)throw new RangeError("Invalid input for NodeSelection.fromJSON");return new v(t.resolve(e.anchor))}static create(t,e){return new v(t.resolve(e))}static isSelectable(t){return!t.isText&&!1!==t.type.spec.selectable}}v.prototype.visible=!1,c.jsonID("node",v);class w{constructor(t){this.anchor=t}map(t){let{deleted:e,pos:n}=t.mapResult(this.anchor);return e?new y(n,n):new w(n)}resolve(t){let e=t.resolve(this.anchor),n=e.nodeAfter;return n&&v.isSelectable(n)?new v(e):c.near(e)}}class k extends c{constructor(t){super(t.resolve(0),t.resolve(t.content.size))}replace(tr,content=r.j.empty){if(content==r.j.empty){tr.delete(0,tr.doc.content.size);let t=c.atStart(tr.doc);t.eq(tr.selection)||tr.setSelection(t)}else super.replace(tr,content)}toJSON(){return{type:"all"}}static fromJSON(t){return new k(t)}map(t){return new k(t)}eq(t){return t instanceof k}getBookmark(){return x}}c.jsonID("all",k);const x={map(){return this},resolve:t=>new k(t)};function O(t,e,n,r,o,text=!1){if(e.inlineContent)return m.create(t,n);for(let i=r-(o>0?0:1);o>0?i<e.childCount:i>=0;i+=o){let r=e.child(i);if(r.isAtom){if(!text&&v.isSelectable(r))return v.create(t,n-(o<0?r.nodeSize:0))}else{let e=O(t,r,n+o,o<0?r.childCount:0,o,text);if(e)return e}n+=r.nodeSize*o}return null}function S(tr,t,e){let n=tr.steps.length-1;if(n<t)return;let r,l=tr.steps[n];(l instanceof o.c||l instanceof o.b)&&(tr.mapping.maps[n].forEach(((t,e,n,o)=>{null==r&&(r=o)})),tr.setSelection(c.near(tr.doc.resolve(r),e)))}class M extends o.d{constructor(t){super(t.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=t.selection,this.storedMarks=t.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(t){if(t.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=t,this.curSelectionFor=this.steps.length,this.updated=-3&(1|this.updated),this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(t){return this.storedMarks=t,this.updated|=2,this}ensureMarks(t){return r.d.sameSet(this.storedMarks||this.selection.$from.marks(),t)||this.setStoredMarks(t),this}addStoredMark(mark){return this.ensureMarks(mark.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(mark){return this.ensureMarks(mark.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(t,e){super.addStep(t,e),this.updated=-3&this.updated,this.storedMarks=null}setTime(time){return this.time=time,this}replaceSelection(t){return this.selection.replace(this,t),this}replaceSelectionWith(t,e=!0){let n=this.selection;return e&&(t=t.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||r.d.none))),n.replaceWith(this,t),this}deleteSelection(){return this.selection.replace(this),this}insertText(text,t,e){let n=this.doc.type.schema;if(null==t)return text?this.replaceSelectionWith(n.text(text),!0):this.deleteSelection();{if(null==e&&(e=t),e=null==e?t:e,!text)return this.deleteRange(t,e);let r=this.storedMarks;if(!r){let n=this.doc.resolve(t);r=e==t?n.marks():n.marksAcross(this.doc.resolve(e))}return this.replaceRangeWith(t,e,n.text(text,r)),this.selection.empty||this.setSelection(c.near(this.selection.$to)),this}}setMeta(t,e){return this.meta["string"==typeof t?t:t.key]=e,this}getMeta(t){return this.meta["string"==typeof t?t:t.key]}get isGeneric(){for(let t in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function C(t,e){return e&&t?t.bind(e):t}class T{constructor(t,desc,e){this.name=t,this.init=C(desc.init,e),this.apply=C(desc.apply,e)}}const E=[new T("doc",{init:t=>t.doc||t.schema.topNodeType.createAndFill(),apply:tr=>tr.doc}),new T("selection",{init:(t,e)=>t.selection||c.atStart(e.doc),apply:tr=>tr.selection}),new T("storedMarks",{init:t=>t.storedMarks||null,apply:(tr,t,e,n)=>n.selection.$cursor?tr.storedMarks:null}),new T("scrollToSelection",{init:()=>0,apply:(tr,t)=>tr.scrolledIntoView?t+1:t})];class A{constructor(t,e){this.schema=t,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=E.slice(),e&&e.forEach((t=>{if(this.pluginsByKey[t.key])throw new RangeError("Adding different instances of a keyed plugin ("+t.key+")");this.plugins.push(t),this.pluginsByKey[t.key]=t,t.spec.state&&this.fields.push(new T(t.key,t.spec.state,t))}))}}class N{constructor(t){this.config=t}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(tr){return this.applyTransaction(tr).state}filterTransaction(tr,t=-1){for(let i=0;i<this.config.plugins.length;i++)if(i!=t){let t=this.config.plugins[i];if(t.spec.filterTransaction&&!t.spec.filterTransaction.call(t,tr,this))return!1}return!0}applyTransaction(t){if(!this.filterTransaction(t))return{state:this,transactions:[]};let e=[t],n=this.applyInner(t),r=null;for(;;){let o=!1;for(let i=0;i<this.config.plugins.length;i++){let l=this.config.plugins[i];if(l.spec.appendTransaction){let c=r?r[i].n:0,d=r?r[i].state:this,tr=c<e.length&&l.spec.appendTransaction.call(l,c?e.slice(c):e,d,n);if(tr&&n.filterTransaction(tr,i)){if(tr.setMeta("appendedTransaction",t),!r){r=[];for(let t=0;t<this.config.plugins.length;t++)r.push(t<i?{state:n,n:e.length}:{state:this,n:0})}e.push(tr),n=n.applyInner(tr),o=!0}r&&(r[i]={state:n,n:e.length})}}if(!o)return{state:n,transactions:e}}}applyInner(tr){if(!tr.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new N(this.config),e=this.config.fields;for(let i=0;i<e.length;i++){let n=e[i];t[n.name]=n.apply(tr,this[n.name],this,t)}return t}get tr(){return new M(this)}static create(t){let e=new A(t.doc?t.doc.type.schema:t.schema,t.plugins),n=new N(e);for(let i=0;i<e.fields.length;i++)n[e.fields[i].name]=e.fields[i].init(t,n);return n}reconfigure(t){let e=new A(this.schema,t.plugins),n=e.fields,r=new N(e);for(let i=0;i<n.length;i++){let e=n[i].name;r[e]=this.hasOwnProperty(e)?this[e]:n[i].init(t,r)}return r}toJSON(t){let e={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(e.storedMarks=this.storedMarks.map((t=>t.toJSON()))),t&&"object"==typeof t)for(let n in t){if("doc"==n||"selection"==n)throw new RangeError("The JSON fields `doc` and `selection` are reserved");let r=t[n],o=r.spec.state;o&&o.toJSON&&(e[n]=o.toJSON.call(r,this[r.key]))}return e}static fromJSON(t,e,n){if(!e)throw new RangeError("Invalid input for EditorState.fromJSON");if(!t.schema)throw new RangeError("Required config field 'schema' missing");let o=new A(t.schema,t.plugins),l=new N(o);return o.fields.forEach((o=>{if("doc"==o.name)l.doc=r.f.fromJSON(t.schema,e.doc);else if("selection"==o.name)l.selection=c.fromJSON(l.doc,e.selection);else if("storedMarks"==o.name)e.storedMarks&&(l.storedMarks=e.storedMarks.map(t.schema.markFromJSON));else{if(n)for(let r in n){let c=n[r],d=c.spec.state;if(c.key==o.name&&d&&d.fromJSON&&Object.prototype.hasOwnProperty.call(e,r))return void(l[o.name]=d.fromJSON.call(c,t,e[r],l))}l[o.name]=o.init(t,l)}})),l}}function D(t,e,n){for(let r in t){let o=t[r];o instanceof Function?o=o.bind(e):"handleDOMEvents"==r&&(o=D(o,e,{})),n[r]=o}return n}class P{constructor(t){this.spec=t,this.props={},t.props&&D(t.props,this,this.props),this.key=t.key?t.key.key:j("plugin")}getState(t){return t[this.key]}}const R=Object.create(null);function j(t){return t in R?t+"$"+ ++R[t]:(R[t]=0,t+"$")}class I{constructor(t="key"){this.key=j(t)}get(t){return t.config.pluginsByKey[this.key]}getState(t){return t[this.key]}}},1576:function(t,e,n){"use strict";n.d(e,"a",(function(){return h})),n.d(e,"b",(function(){return M})),n.d(e,"c",(function(){return S})),n.d(e,"d",(function(){return Z})),n.d(e,"e",(function(){return I})),n.d(e,"f",(function(){return j})),n.d(e,"g",(function(){return $})),n.d(e,"h",(function(){return N})),n.d(e,"i",(function(){return B})),n.d(e,"j",(function(){return A})),n.d(e,"k",(function(){return z}));var r=n(1590);const o=Math.pow(2,16);function l(t){return 65535&t}class c{constructor(t,e,n){this.pos=t,this.delInfo=e,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class d{constructor(t,e=!1){if(this.ranges=t,this.inverted=e,!t.length&&d.empty)return d.empty}recover(t){let e=0,n=l(t);if(!this.inverted)for(let i=0;i<n;i++)e+=this.ranges[3*i+2]-this.ranges[3*i+1];return this.ranges[3*n]+e+function(t){return(t-(65535&t))/o}(t)}mapResult(t,e=1){return this._map(t,e,!1)}map(t,e=1){return this._map(t,e,!0)}_map(t,e,n){let r=0,l=this.inverted?2:1,d=this.inverted?1:2;for(let i=0;i<this.ranges.length;i+=3){let h=this.ranges[i]-(this.inverted?r:0);if(h>t)break;let f=this.ranges[i+l],m=this.ranges[i+d],y=h+f;if(t<=y){let l=h+r+((f?t==h?-1:t==y?1:e:e)<0?0:m);if(n)return l;let d=t==(e<0?h:y)?null:i/3+(t-h)*o,del=t==h?2:t==y?1:4;return(e<0?t!=h:t!=y)&&(del|=8),new c(l,del,d)}r+=m-f}return n?t+r:new c(t+r,0,null)}touches(t,e){let n=0,r=l(e),o=this.inverted?2:1,c=this.inverted?1:2;for(let i=0;i<this.ranges.length;i+=3){let e=this.ranges[i]-(this.inverted?n:0);if(e>t)break;let l=this.ranges[i+o];if(t<=e+l&&i==3*r)return!0;n+=this.ranges[i+c]-l}return!1}forEach(t){let e=this.inverted?2:1,n=this.inverted?1:2;for(let i=0,r=0;i<this.ranges.length;i+=3){let o=this.ranges[i],l=o-(this.inverted?r:0),c=o+(this.inverted?0:r),d=this.ranges[i+e],h=this.ranges[i+n];t(l,l+d,c,c+h),r+=h-d}}invert(){return new d(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(t){return 0==t?d.empty:new d(t<0?[0,-t,0]:[0,0,t])}}d.empty=new d([]);class h{constructor(t,e,n=0,r=(t?t.length:0)){this.mirror=e,this.from=n,this.to=r,this._maps=t||[],this.ownData=!(t||e)}get maps(){return this._maps}slice(t=0,e=this.maps.length){return new h(this._maps,this.mirror,t,e)}appendMap(map,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(map),null!=t&&this.setMirror(this._maps.length-1,t)}appendMapping(t){for(let i=0,e=this._maps.length;i<t._maps.length;i++){let n=t.getMirror(i);this.appendMap(t._maps[i],null!=n&&n<i?e+n:void 0)}}getMirror(t){if(this.mirror)for(let i=0;i<this.mirror.length;i++)if(this.mirror[i]==t)return this.mirror[i+(i%2?-1:1)]}setMirror(t,e){this.mirror||(this.mirror=[]),this.mirror.push(t,e)}appendMappingInverted(t){for(let i=t.maps.length-1,e=this._maps.length+t._maps.length;i>=0;i--){let n=t.getMirror(i);this.appendMap(t._maps[i].invert(),null!=n&&n>i?e-n-1:void 0)}}invert(){let t=new h;return t.appendMappingInverted(this),t}map(t,e=1){if(this.mirror)return this._map(t,e,!0);for(let i=this.from;i<this.to;i++)t=this._maps[i].map(t,e);return t}mapResult(t,e=1){return this._map(t,e,!1)}_map(t,e,n){let r=0;for(let i=this.from;i<this.to;i++){let n=this._maps[i].mapResult(t,e);if(null!=n.recover){let e=this.getMirror(i);if(null!=e&&e>i&&e<this.to){i=e,t=this._maps[e].recover(n.recover);continue}}r|=n.delInfo,t=n.pos}return n?t:new c(t,r,null)}}const f=Object.create(null);class m{getMap(){return d.empty}merge(t){return null}static fromJSON(t,e){if(!e||!e.stepType)throw new RangeError("Invalid input for Step.fromJSON");let n=f[e.stepType];if(!n)throw new RangeError(`No step type ${e.stepType} defined`);return n.fromJSON(t,e)}static jsonID(t,e){if(t in f)throw new RangeError("Duplicate use of step JSON ID "+t);return f[t]=e,e.prototype.jsonID=t,e}}class y{constructor(t,e){this.doc=t,this.failed=e}static ok(t){return new y(t,null)}static fail(t){return new y(null,t)}static fromReplace(t,e,n,o){try{return y.ok(t.replace(e,n,o))}catch(t){if(t instanceof r.h)return y.fail(t.message);throw t}}}function v(t,e,n){let o=[];for(let i=0;i<t.childCount;i++){let r=t.child(i);r.content.size&&(r=r.copy(v(r.content,e,r))),r.isInline&&(r=e(r,n,i)),o.push(r)}return r.c.fromArray(o)}class w extends m{constructor(t,e,mark){super(),this.from=t,this.to=e,this.mark=mark}apply(t){let e=t.slice(this.from,this.to),n=t.resolve(this.from),o=n.node(n.sharedDepth(this.to)),l=new r.j(v(e.content,((t,e)=>t.isAtom&&e.type.allowsMarkType(this.mark.type)?t.mark(this.mark.addToSet(t.marks)):t),o),e.openStart,e.openEnd);return y.fromReplace(t,this.from,this.to,l)}invert(){return new k(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deleted&&n.deleted||e.pos>=n.pos?null:new w(e.pos,n.pos,this.mark)}merge(t){return t instanceof w&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new w(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new w(e.from,e.to,t.markFromJSON(e.mark))}}m.jsonID("addMark",w);class k extends m{constructor(t,e,mark){super(),this.from=t,this.to=e,this.mark=mark}apply(t){let e=t.slice(this.from,this.to),n=new r.j(v(e.content,(t=>t.mark(this.mark.removeFromSet(t.marks))),t),e.openStart,e.openEnd);return y.fromReplace(t,this.from,this.to,n)}invert(){return new w(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deleted&&n.deleted||e.pos>=n.pos?null:new k(e.pos,n.pos,this.mark)}merge(t){return t instanceof k&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new k(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new k(e.from,e.to,t.markFromJSON(e.mark))}}m.jsonID("removeMark",k);class x extends m{constructor(t,mark){super(),this.pos=t,this.mark=mark}apply(t){let e=t.nodeAt(this.pos);if(!e)return y.fail("No node at mark step's position");let n=e.type.create(e.attrs,null,this.mark.addToSet(e.marks));return y.fromReplace(t,this.pos,this.pos+1,new r.j(r.c.from(n),0,e.isLeaf?0:1))}invert(t){let e=t.nodeAt(this.pos);if(e){let t=this.mark.addToSet(e.marks);if(t.length==e.marks.length){for(let i=0;i<e.marks.length;i++)if(!e.marks[i].isInSet(t))return new x(this.pos,e.marks[i]);return new x(this.pos,this.mark)}}return new O(this.pos,this.mark)}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new x(e.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new x(e.pos,t.markFromJSON(e.mark))}}m.jsonID("addNodeMark",x);class O extends m{constructor(t,mark){super(),this.pos=t,this.mark=mark}apply(t){let e=t.nodeAt(this.pos);if(!e)return y.fail("No node at mark step's position");let n=e.type.create(e.attrs,null,this.mark.removeFromSet(e.marks));return y.fromReplace(t,this.pos,this.pos+1,new r.j(r.c.from(n),0,e.isLeaf?0:1))}invert(t){let e=t.nodeAt(this.pos);return e&&this.mark.isInSet(e.marks)?new x(this.pos,this.mark):this}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new O(e.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new O(e.pos,t.markFromJSON(e.mark))}}m.jsonID("removeNodeMark",O);class S extends m{constructor(t,e,n,r=!1){super(),this.from=t,this.to=e,this.slice=n,this.structure=r}apply(t){return this.structure&&C(t,this.from,this.to)?y.fail("Structure replace would overwrite content"):y.fromReplace(t,this.from,this.to,this.slice)}getMap(){return new d([this.from,this.to-this.from,this.slice.size])}invert(t){return new S(this.from,this.from+this.slice.size,t.slice(this.from,this.to))}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deletedAcross&&n.deletedAcross?null:new S(e.pos,Math.max(e.pos,n.pos),this.slice,this.structure)}merge(t){if(!(t instanceof S)||t.structure||this.structure)return null;if(this.from+this.slice.size!=t.from||this.slice.openEnd||t.slice.openStart){if(t.to!=this.from||this.slice.openStart||t.slice.openEnd)return null;{let e=this.slice.size+t.slice.size==0?r.j.empty:new r.j(t.slice.content.append(this.slice.content),t.slice.openStart,this.slice.openEnd);return new S(t.from,this.to,e,this.structure)}}{let e=this.slice.size+t.slice.size==0?r.j.empty:new r.j(this.slice.content.append(t.slice.content),this.slice.openStart,t.slice.openEnd);return new S(this.from,this.to+(t.to-t.from),e,this.structure)}}toJSON(){let t={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new S(e.from,e.to,r.j.fromJSON(t,e.slice),!!e.structure)}}m.jsonID("replace",S);class M extends m{constructor(t,e,n,r,o,l,c=!1){super(),this.from=t,this.to=e,this.gapFrom=n,this.gapTo=r,this.slice=o,this.insert=l,this.structure=c}apply(t){if(this.structure&&(C(t,this.from,this.gapFrom)||C(t,this.gapTo,this.to)))return y.fail("Structure gap-replace would overwrite content");let e=t.slice(this.gapFrom,this.gapTo);if(e.openStart||e.openEnd)return y.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,e.content);return n?y.fromReplace(t,this.from,this.to,n):y.fail("Content does not fit in gap")}getMap(){return new d([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(t){let e=this.gapTo-this.gapFrom;return new M(this.from,this.from+this.slice.size+e,this.from+this.insert,this.from+this.insert+e,t.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1),r=this.from==this.gapFrom?e.pos:t.map(this.gapFrom,-1),o=this.to==this.gapTo?n.pos:t.map(this.gapTo,1);return e.deletedAcross&&n.deletedAcross||r<e.pos||o>n.pos?null:new M(e.pos,n.pos,r,o,this.slice,this.insert,this.structure)}toJSON(){let t={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to||"number"!=typeof e.gapFrom||"number"!=typeof e.gapTo||"number"!=typeof e.insert)throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new M(e.from,e.to,e.gapFrom,e.gapTo,r.j.fromJSON(t,e.slice),e.insert,!!e.structure)}}function C(t,e,n){let r=t.resolve(e),o=n-e,l=r.depth;for(;o>0&&l>0&&r.indexAfter(l)==r.node(l).childCount;)l--,o--;if(o>0){let t=r.node(l).maybeChild(r.indexAfter(l));for(;o>0;){if(!t||t.isLeaf)return!0;t=t.firstChild,o--}}return!1}function T(tr,t,e,n=e.contentMatch,o=!0){let l=tr.doc.nodeAt(t),c=[],d=t+1;for(let i=0;i<l.childCount;i++){let t=l.child(i),h=d+t.nodeSize,f=n.matchType(t.type);if(f){n=f;for(let n=0;n<t.marks.length;n++)e.allowsMarkType(t.marks[n].type)||tr.step(new k(d,h,t.marks[n]));if(o&&t.isText&&"pre"!=e.whitespace){let n,o,l=/\r?\n|\r/g;for(;n=l.exec(t.text);)o||(o=new r.j(r.c.from(e.schema.text(" ",e.allowedMarks(t.marks))),0,0)),c.push(new S(d+n.index,d+n.index+n[0].length,o))}}else c.push(new S(d,h,r.j.empty));d=h}if(!n.validEnd){let t=n.fillBefore(r.c.empty,!0);tr.replace(d,d,new r.j(t,0,0))}for(let i=c.length-1;i>=0;i--)tr.step(c[i])}function E(t,e,n){return(0==e||t.canReplace(e,t.childCount))&&(n==t.childCount||t.canReplace(0,n))}function A(t){let content=t.parent.content.cutByIndex(t.startIndex,t.endIndex);for(let e=t.depth;;--e){let n=t.$from.node(e),r=t.$from.index(e),o=t.$to.indexAfter(e);if(e<t.depth&&n.canReplace(r,o,content))return e;if(0==e||n.type.spec.isolating||!E(n,r,o))break}return null}function N(t,e,n=null,r=t){let o=function(t,e){let{parent:n,startIndex:r,endIndex:o}=t,l=n.contentMatchAt(r).findWrapping(e);if(!l)return null;let c=l.length?l[0]:e;return n.canReplaceWith(r,o,c)?l:null}(t,e),l=o&&function(t,e){let{parent:n,startIndex:r,endIndex:o}=t,l=n.child(r),c=e.contentMatch.findWrapping(l.type);if(!c)return null;let d=(c.length?c[c.length-1]:e).contentMatch;for(let i=r;d&&i<o;i++)d=d.matchType(n.child(i).type);return d&&d.validEnd?c:null}(r,e);return l?o.map(D).concat({type:e,attrs:n}).concat(l.map(D)):null}function D(t){return{type:t,attrs:null}}function P(tr,t,e,n){t.forEach(((r,o)=>{if(r.isText){let l,c=/\r?\n|\r/g;for(;l=c.exec(r.text);){let r=tr.mapping.slice(n).map(e+1+o+l.index);tr.replaceWith(r,r+1,t.type.schema.linebreakReplacement.create())}}}))}function R(tr,t,e,n){t.forEach(((r,o)=>{if(r.type==r.type.schema.linebreakReplacement){let r=tr.mapping.slice(n).map(e+1+o);tr.replaceWith(r,r+1,t.type.schema.text("\n"))}}))}function j(t,e,n=1,r){let o=t.resolve(e),base=o.depth-n,l=r&&r[r.length-1]||o.parent;if(base<0||o.parent.type.spec.isolating||!o.parent.canReplace(o.index(),o.parent.childCount)||!l.type.validContent(o.parent.content.cutByIndex(o.index(),o.parent.childCount)))return!1;for(let t=o.depth-1,i=n-2;t>base;t--,i--){let e=o.node(t),n=o.index(t);if(e.type.spec.isolating)return!1;let l=e.content.cutByIndex(n,e.childCount),c=r&&r[i+1];c&&(l=l.replaceChild(0,c.type.create(c.attrs)));let d=r&&r[i]||e;if(!e.canReplace(n+1,e.childCount)||!d.type.validContent(l))return!1}let c=o.indexAfter(base),d=r&&r[0];return o.node(base).canReplaceWith(c,c,d?d.type:o.node(base+1).type)}function I(t,e){let n=t.resolve(e),r=n.index();return L(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function L(a,b){return!(!a||!b||a.isLeaf||!function(a,b){b.content.size||a.type.compatibleContent(b.type);let t=a.contentMatchAt(a.childCount),{linebreakReplacement:e}=a.type.schema;for(let i=0;i<b.childCount;i++){let n=b.child(i),r=n.type==e?a.type.schema.nodes.text:n.type;if(t=t.matchType(r),!t)return!1;if(!a.type.allowsMarks(n.marks))return!1}return t.validEnd}(a,b))}function B(t,e,n=-1){let r=t.resolve(e);for(let t=r.depth;;t--){let o,l,c=r.index(t);if(t==r.depth?(o=r.nodeBefore,l=r.nodeAfter):n>0?(o=r.node(t+1),c++,l=r.node(t).maybeChild(c)):(o=r.node(t).maybeChild(c-1),l=r.node(t+1)),o&&!o.isTextblock&&L(o,l)&&r.node(t).canReplace(c,c+1))return e;if(0==t)break;e=n<0?r.before(t):r.after(t)}}function $(t,e,n){let r=t.resolve(e);if(!n.content.size)return e;let content=n.content;for(let i=0;i<n.openStart;i++)content=content.firstChild.content;for(let t=1;t<=(0==n.openStart&&n.size?2:1);t++)for(let e=r.depth;e>=0;e--){let n=e==r.depth?0:r.pos<=(r.start(e+1)+r.end(e+1))/2?-1:1,o=r.index(e)+(n>0?1:0),l=r.node(e),c=!1;if(1==t)c=l.canReplace(o,o,content);else{let t=l.contentMatchAt(o).findWrapping(content.firstChild.type);c=t&&l.canReplaceWith(o,o,t[0])}if(c)return 0==n?r.pos:n<0?r.before(e+1):r.after(e+1)}return null}function z(t,e,n=e,o=r.j.empty){if(e==n&&!o.size)return null;let l=t.resolve(e),c=t.resolve(n);return H(l,c,o)?new S(e,n,o):new V(l,c,o).fit()}function H(t,e,n){return!n.openStart&&!n.openEnd&&t.start()==e.start()&&t.parent.canReplace(t.index(),e.index(),n.content)}m.jsonID("replaceAround",M);class V{constructor(t,e,n){this.$from=t,this.$to=e,this.unplaced=n,this.frontier=[],this.placed=r.c.empty;for(let i=0;i<=t.depth;i++){let e=t.node(i);this.frontier.push({type:e.type,match:e.contentMatchAt(t.indexAfter(i))})}for(let i=t.depth;i>0;i--)this.placed=r.c.from(t.node(i).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let t=this.findFittable();t?this.placeNodes(t):this.openMore()||this.dropNode()}let t=this.mustMoveInline(),e=this.placed.size-this.depth-this.$from.depth,n=this.$from,o=this.close(t<0?this.$to:n.doc.resolve(t));if(!o)return null;let content=this.placed,l=n.depth,c=o.depth;for(;l&&c&&1==content.childCount;)content=content.firstChild.content,l--,c--;let d=new r.j(content,l,c);return t>-1?new M(n.pos,t,this.$to.pos,this.$to.end(),d,e):d.size||n.pos!=this.$to.pos?new S(n.pos,o.pos,d):null}findFittable(){let t=this.unplaced.openStart;for(let e=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<t;n++){let o=e.firstChild;if(e.childCount>1&&(r=0),o.type.spec.isolating&&r<=n){t=n;break}e=o.content}for(let e=1;e<=2;e++)for(let n=1==e?t:this.unplaced.openStart;n>=0;n--){let t,o=null;n?(o=W(this.unplaced.content,n-1).firstChild,t=o.content):t=this.unplaced.content;let l=t.firstChild;for(let t=this.depth;t>=0;t--){let c,{type:d,match:h}=this.frontier[t],f=null;if(1==e&&(l?h.matchType(l.type)||(f=h.fillBefore(r.c.from(l),!1)):o&&d.compatibleContent(o.type)))return{sliceDepth:n,frontierDepth:t,parent:o,inject:f};if(2==e&&l&&(c=h.findWrapping(l.type)))return{sliceDepth:n,frontierDepth:t,parent:o,wrap:c};if(o&&h.matchType(o.type))break}}}openMore(){let{content:content,openStart:t,openEnd:e}=this.unplaced,n=W(content,t);return!(!n.childCount||n.firstChild.isLeaf)&&(this.unplaced=new r.j(content,t+1,Math.max(e,n.size+t>=content.size-e?t+1:0)),!0)}dropNode(){let{content:content,openStart:t,openEnd:e}=this.unplaced,n=W(content,t);if(n.childCount<=1&&t>0){let o=content.size-t<=t+n.size;this.unplaced=new r.j(F(content,t-1,1),t-1,o?t-1:e)}else this.unplaced=new r.j(F(content,t,1),t,e)}placeNodes({sliceDepth:t,frontierDepth:e,parent:n,inject:o,wrap:l}){for(;this.depth>e;)this.closeFrontierNode();if(l)for(let i=0;i<l.length;i++)this.openFrontierNode(l[i]);let c=this.unplaced,d=n?n.content:c.content,h=c.openStart-t,f=0,m=[],{match:y,type:v}=this.frontier[e];if(o){for(let i=0;i<o.childCount;i++)m.push(o.child(i));y=y.matchFragment(o)}let w=d.size+t-(c.content.size-c.openEnd);for(;f<d.childCount;){let t=d.child(f),e=y.matchType(t.type);if(!e)break;f++,(f>1||0==h||t.content.size)&&(y=e,m.push(K(t.mark(v.allowedMarks(t.marks)),1==f?h:0,f==d.childCount?w:-1)))}let k=f==d.childCount;k||(w=-1),this.placed=_(this.placed,e,r.c.from(m)),this.frontier[e].match=y,k&&w<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let i=0,t=d;i<w;i++){let e=t.lastChild;this.frontier.push({type:e.type,match:e.contentMatchAt(e.childCount)}),t=e.content}this.unplaced=k?0==t?r.j.empty:new r.j(F(c.content,t-1,1),t-1,w<0?c.openEnd:t-1):new r.j(F(c.content,t,f),c.openStart,c.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let t,e=this.frontier[this.depth];if(!e.type.isTextblock||!J(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(t){t:for(let i=Math.min(this.depth,t.depth);i>=0;i--){let{match:e,type:n}=this.frontier[i],r=i<t.depth&&t.end(i+1)==t.pos+(t.depth-(i+1)),o=J(t,i,n,e,r);if(o){for(let e=i-1;e>=0;e--){let{match:n,type:r}=this.frontier[e],o=J(t,e,r,n,!0);if(!o||o.childCount)continue t}return{depth:i,fit:o,move:r?t.doc.resolve(t.after(i+1)):t}}}}close(t){let e=this.findCloseLevel(t);if(!e)return null;for(;this.depth>e.depth;)this.closeFrontierNode();e.fit.childCount&&(this.placed=_(this.placed,e.depth,e.fit)),t=e.move;for(let n=e.depth+1;n<=t.depth;n++){let e=t.node(n),r=e.type.contentMatch.fillBefore(e.content,!0,t.index(n));this.openFrontierNode(e.type,e.attrs,r)}return t}openFrontierNode(t,e=null,content){let n=this.frontier[this.depth];n.match=n.match.matchType(t),this.placed=_(this.placed,this.depth,r.c.from(t.create(e,content))),this.frontier.push({type:t,match:t.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(r.c.empty,!0);t.childCount&&(this.placed=_(this.placed,this.frontier.length,t))}}function F(t,e,n){return 0==e?t.cutByIndex(n,t.childCount):t.replaceChild(0,t.firstChild.copy(F(t.firstChild.content,e-1,n)))}function _(t,e,content){return 0==e?t.append(content):t.replaceChild(t.childCount-1,t.lastChild.copy(_(t.lastChild.content,e-1,content)))}function W(t,e){for(let i=0;i<e;i++)t=t.firstChild.content;return t}function K(t,e,n){if(e<=0)return t;let o=t.content;return e>1&&(o=o.replaceChild(0,K(o.firstChild,e-1,1==o.childCount?n-1:0))),e>0&&(o=t.type.contentMatch.fillBefore(o).append(o),n<=0&&(o=o.append(t.type.contentMatch.matchFragment(o).fillBefore(r.c.empty,!0)))),t.copy(o)}function J(t,e,n,r,o){let l=t.node(e),c=o?t.indexAfter(e):t.index(e);if(c==l.childCount&&!n.compatibleContent(l.type))return null;let d=r.fillBefore(l.content,!0,c);return d&&!function(t,e,n){for(let i=n;i<e.childCount;i++)if(!t.allowsMarks(e.child(i).marks))return!0;return!1}(n,l.content,c)?d:null}function U(t,e,n,o,l){if(e<n){let r=t.firstChild;t=t.replaceChild(0,r.copy(U(r.content,e+1,n,o,r)))}if(e>o){let e=l.contentMatchAt(0),n=e.fillBefore(t).append(t);t=n.append(e.matchFragment(n).fillBefore(r.c.empty,!0))}return t}function G(t,e){let n=[];for(let r=Math.min(t.depth,e.depth);r>=0;r--){let o=t.start(r);if(o<t.pos-(t.depth-r)||e.end(r)>e.pos+(e.depth-r)||t.node(r).type.spec.isolating||e.node(r).type.spec.isolating)break;(o==e.start(r)||r==t.depth&&r==e.depth&&t.parent.inlineContent&&e.parent.inlineContent&&r&&e.start(r-1)==o-1)&&n.push(r)}return n}class Y extends m{constructor(t,e,n){super(),this.pos=t,this.attr=e,this.value=n}apply(t){let e=t.nodeAt(this.pos);if(!e)return y.fail("No node at attribute step's position");let n=Object.create(null);for(let t in e.attrs)n[t]=e.attrs[t];n[this.attr]=this.value;let o=e.type.create(n,null,e.marks);return y.fromReplace(t,this.pos,this.pos+1,new r.j(r.c.from(o),0,e.isLeaf?0:1))}getMap(){return d.empty}invert(t){return new Y(this.pos,this.attr,t.nodeAt(this.pos).attrs[this.attr])}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new Y(e.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(t,e){if("number"!=typeof e.pos||"string"!=typeof e.attr)throw new RangeError("Invalid input for AttrStep.fromJSON");return new Y(e.pos,e.attr,e.value)}}m.jsonID("attr",Y);class X extends m{constructor(t,e){super(),this.attr=t,this.value=e}apply(t){let e=Object.create(null);for(let n in t.attrs)e[n]=t.attrs[n];e[this.attr]=this.value;let n=t.type.create(e,t.content,t.marks);return y.ok(n)}getMap(){return d.empty}invert(t){return new X(this.attr,t.attrs[this.attr])}map(t){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(t,e){if("string"!=typeof e.attr)throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new X(e.attr,e.value)}}m.jsonID("docAttr",X);let Q=class extends Error{};Q=function t(e){let n=Error.call(this,e);return n.__proto__=t.prototype,n},(Q.prototype=Object.create(Error.prototype)).constructor=Q,Q.prototype.name="TransformError";class Z{constructor(t){this.doc=t,this.steps=[],this.docs=[],this.mapping=new h}get before(){return this.docs.length?this.docs[0]:this.doc}step(t){let e=this.maybeStep(t);if(e.failed)throw new Q(e.failed);return this}maybeStep(t){let e=t.apply(this.doc);return e.failed||this.addStep(t,e.doc),e}get docChanged(){return this.steps.length>0}addStep(t,e){this.docs.push(this.doc),this.steps.push(t),this.mapping.appendMap(t.getMap()),this.doc=e}replace(t,e=t,n=r.j.empty){let o=z(this.doc,t,e,n);return o&&this.step(o),this}replaceWith(t,e,content){return this.replace(t,e,new r.j(r.c.from(content),0,0))}delete(t,e){return this.replace(t,e,r.j.empty)}insert(t,content){return this.replaceWith(t,t,content)}replaceRange(t,e,n){return function(tr,t,e,n){if(!n.size)return tr.deleteRange(t,e);let o=tr.doc.resolve(t),l=tr.doc.resolve(e);if(H(o,l,n))return tr.step(new S(t,e,n));let c=G(o,tr.doc.resolve(e));0==c[c.length-1]&&c.pop();let d=-(o.depth+1);c.unshift(d);for(let t=o.depth,e=o.pos-1;t>0;t--,e--){let n=o.node(t).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;c.indexOf(t)>-1?d=t:o.before(t)==e&&c.splice(1,0,-t)}let h=c.indexOf(d),f=[],m=n.openStart;for(let content=n.content,i=0;;i++){let t=content.firstChild;if(f.push(t),i==n.openStart)break;content=t.content}for(let t=m-1;t>=0;t--){let e=f[t],n=(y=e.type).spec.defining||y.spec.definingForContent;if(n&&!e.sameMarkup(o.node(Math.abs(d)-1)))m=t;else if(n||!e.type.isTextblock)break}var y;for(let t=n.openStart;t>=0;t--){let d=(t+m+1)%(n.openStart+1),y=f[d];if(y)for(let i=0;i<c.length;i++){let t=c[(i+h)%c.length],f=!0;t<0&&(f=!1,t=-t);let m=o.node(t-1),v=o.index(t-1);if(m.canReplaceWith(v,v,y.type,y.marks))return tr.replace(o.before(t),f?l.after(t):e,new r.j(U(n.content,0,n.openStart,d),d,n.openEnd))}}let v=tr.steps.length;for(let i=c.length-1;i>=0&&(tr.replace(t,e,n),!(tr.steps.length>v));i--){let n=c[i];n<0||(t=o.before(n),e=l.after(n))}}(this,t,e,n),this}replaceRangeWith(t,e,n){return function(tr,t,e,n){if(!n.isInline&&t==e&&tr.doc.resolve(t).parent.content.size){let r=function(t,e,n){let r=t.resolve(e);if(r.parent.canReplaceWith(r.index(),r.index(),n))return e;if(0==r.parentOffset)for(let t=r.depth-1;t>=0;t--){let e=r.index(t);if(r.node(t).canReplaceWith(e,e,n))return r.before(t+1);if(e>0)return null}if(r.parentOffset==r.parent.content.size)for(let t=r.depth-1;t>=0;t--){let e=r.indexAfter(t);if(r.node(t).canReplaceWith(e,e,n))return r.after(t+1);if(e<r.node(t).childCount)return null}return null}(tr.doc,t,n.type);null!=r&&(t=e=r)}tr.replaceRange(t,e,new r.j(r.c.from(n),0,0))}(this,t,e,n),this}deleteRange(t,e){return function(tr,t,e){let n=tr.doc.resolve(t),r=tr.doc.resolve(e),o=G(n,r);for(let i=0;i<o.length;i++){let t=o[i],e=i==o.length-1;if(e&&0==t||n.node(t).type.contentMatch.validEnd)return tr.delete(n.start(t),r.end(t));if(t>0&&(e||n.node(t-1).canReplace(n.index(t-1),r.indexAfter(t-1))))return tr.delete(n.before(t),r.after(t))}for(let o=1;o<=n.depth&&o<=r.depth;o++)if(t-n.start(o)==n.depth-o&&e>n.end(o)&&r.end(o)-e!=r.depth-o&&n.start(o-1)==r.start(o-1)&&n.node(o-1).canReplace(n.index(o-1),r.index(o-1)))return tr.delete(n.before(o),e);tr.delete(t,e)}(this,t,e),this}lift(t,e){return function(tr,t,e){let{$from:n,$to:o,depth:l}=t,c=n.before(l+1),d=o.after(l+1),h=c,f=d,m=r.c.empty,y=0;for(let t=l,o=!1;t>e;t--)o||n.index(t)>0?(o=!0,m=r.c.from(n.node(t).copy(m)),y++):h--;let v=r.c.empty,w=0;for(let t=l,n=!1;t>e;t--)n||o.after(t+1)<o.end(t)?(n=!0,v=r.c.from(o.node(t).copy(v)),w++):f++;tr.step(new M(h,f,c,d,new r.j(m.append(v),y,w),m.size-y,!0))}(this,t,e),this}join(t,e=1){return function(tr,t,e){let n=null,{linebreakReplacement:o}=tr.doc.type.schema,l=tr.doc.resolve(t-e),c=l.node().type;if(o&&c.inlineContent){let pre="pre"==c.whitespace,t=!!c.contentMatch.matchType(o);pre&&!t?n=!1:!pre&&t&&(n=!0)}let d=tr.steps.length;if(!1===n){let n=tr.doc.resolve(t+e);R(tr,n.node(),n.before(),d)}c.inlineContent&&T(tr,t+e-1,c,l.node().contentMatchAt(l.index()),null==n);let h=tr.mapping.slice(d),f=h.map(t-e);if(tr.step(new S(f,h.map(t+e,-1),r.j.empty,!0)),!0===n){let t=tr.doc.resolve(f);P(tr,t.node(),t.before(),tr.steps.length)}}(this,t,e),this}wrap(t,e){return function(tr,t,e){let content=r.c.empty;for(let i=e.length-1;i>=0;i--){if(content.size){let t=e[i].type.contentMatch.matchFragment(content);if(!t||!t.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}content=r.c.from(e[i].type.create(e[i].attrs,content))}let n=t.start,o=t.end;tr.step(new M(n,o,n,o,new r.j(content,0,0),e.length,!0))}(this,t,e),this}setBlockType(t,e=t,n,o=null){return function(tr,t,e,n,o){if(!n.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let l=tr.steps.length;tr.doc.nodesBetween(t,e,((t,e)=>{let c="function"==typeof o?o(t):o;if(t.isTextblock&&!t.hasMarkup(n,c)&&function(t,e,n){let r=t.resolve(e),o=r.index();return r.parent.canReplaceWith(o,o+1,n)}(tr.doc,tr.mapping.slice(l).map(e),n)){let o=null;if(n.schema.linebreakReplacement){let pre="pre"==n.whitespace,t=!!n.contentMatch.matchType(n.schema.linebreakReplacement);pre&&!t?o=!1:!pre&&t&&(o=!0)}!1===o&&R(tr,t,e,l),T(tr,tr.mapping.slice(l).map(e,1),n,void 0,null===o);let d=tr.mapping.slice(l),h=d.map(e,1),f=d.map(e+t.nodeSize,1);return tr.step(new M(h,f,h+1,f-1,new r.j(r.c.from(n.create(c,null,t.marks)),0,0),1,!0)),!0===o&&P(tr,t,e,l),!1}}))}(this,t,e,n,o),this}setNodeMarkup(t,e,n=null,o){return function(tr,t,e,n,o){let l=tr.doc.nodeAt(t);if(!l)throw new RangeError("No node at given position");e||(e=l.type);let c=e.create(n,null,o||l.marks);if(l.isLeaf)return tr.replaceWith(t,t+l.nodeSize,c);if(!e.validContent(l.content))throw new RangeError("Invalid content for node type "+e.name);tr.step(new M(t,t+l.nodeSize,t+1,t+l.nodeSize-1,new r.j(r.c.from(c),0,0),1,!0))}(this,t,e,n,o),this}setNodeAttribute(t,e,n){return this.step(new Y(t,e,n)),this}setDocAttribute(t,e){return this.step(new X(t,e)),this}addNodeMark(t,mark){return this.step(new x(t,mark)),this}removeNodeMark(t,mark){let e=this.doc.nodeAt(t);if(!e)throw new RangeError("No node at position "+t);if(mark instanceof r.d)mark.isInSet(e.marks)&&this.step(new O(t,mark));else{let n,r=e.marks,o=[];for(;n=mark.isInSet(r);)o.push(new O(t,n)),r=n.removeFromSet(r);for(let i=o.length-1;i>=0;i--)this.step(o[i])}return this}split(t,e=1,n){return function(tr,t,e=1,n){let o=tr.doc.resolve(t),l=r.c.empty,c=r.c.empty;for(let t=o.depth,d=o.depth-e,i=e-1;t>d;t--,i--){l=r.c.from(o.node(t).copy(l));let e=n&&n[i];c=r.c.from(e?e.type.create(e.attrs,c):o.node(t).copy(c))}tr.step(new S(t,t,new r.j(l.append(c),e,e),!0))}(this,t,e,n),this}addMark(t,e,mark){return function(tr,t,e,mark){let n,r,o=[],l=[];tr.doc.nodesBetween(t,e,((c,d,h)=>{if(!c.isInline)return;let f=c.marks;if(!mark.isInSet(f)&&h.type.allowsMarkType(mark.type)){let h=Math.max(d,t),m=Math.min(d+c.nodeSize,e),y=mark.addToSet(f);for(let i=0;i<f.length;i++)f[i].isInSet(y)||(n&&n.to==h&&n.mark.eq(f[i])?n.to=m:o.push(n=new k(h,m,f[i])));r&&r.to==h?r.to=m:l.push(r=new w(h,m,mark))}})),o.forEach((s=>tr.step(s))),l.forEach((s=>tr.step(s)))}(this,t,e,mark),this}removeMark(t,e,mark){return function(tr,t,e,mark){let n=[],o=0;tr.doc.nodesBetween(t,e,((l,c)=>{if(!l.isInline)return;o++;let d=null;if(mark instanceof r.e){let t,e=l.marks;for(;t=mark.isInSet(e);)(d||(d=[])).push(t),e=t.removeFromSet(e)}else mark?mark.isInSet(l.marks)&&(d=[mark]):d=l.marks;if(d&&d.length){let r=Math.min(c+l.nodeSize,e);for(let i=0;i<d.length;i++){let e,style=d[i];for(let t=0;t<n.length;t++){let r=n[t];r.step==o-1&&style.eq(n[t].style)&&(e=r)}e?(e.to=r,e.step=o):n.push({style:style,from:Math.max(c,t),to:r,step:o})}}})),n.forEach((t=>tr.step(new k(t.from,t.to,t.style))))}(this,t,e,mark),this}clearIncompatible(t,e,n){return T(this,t,e,n),this}}},1577:function(t,e,n){"use strict";n.d(e,"a",(function(){return l}));var r=n(1426),o=n(1599);const l=r.b.create({name:"characterCount",addOptions:()=>({limit:null,mode:"textSize",textCounter:text=>text.length,wordCounter:text=>text.split(" ").filter((t=>""!==t)).length}),addStorage:()=>({characters:()=>0,words:()=>0}),onBeforeCreate(){this.storage.characters=t=>{const e=(null==t?void 0:t.node)||this.editor.state.doc;if("textSize"===((null==t?void 0:t.mode)||this.options.mode)){const text=e.textBetween(0,e.content.size,void 0," ");return this.options.textCounter(text)}return e.nodeSize},this.storage.words=t=>{const e=(null==t?void 0:t.node)||this.editor.state.doc,text=e.textBetween(0,e.content.size," "," ");return this.options.wordCounter(text)}},addProseMirrorPlugins(){let t=!1;return[new o.d({key:new o.e("characterCount"),appendTransaction:(e,n,r)=>{if(t)return;const o=this.options.limit;if(null==o||0===o)return void(t=!0);const l=this.storage.characters({node:r.doc});if(l>o){const e=0,n=l-o;console.warn(`[CharacterCount] Initial content exceeded limit of ${o} characters. Content was automatically trimmed.`);const tr=r.tr.deleteRange(e,n);return t=!0,tr}t=!0},filterTransaction:(t,e)=>{const n=this.options.limit;if(!t.docChanged||0===n||null==n)return!0;const r=this.storage.characters({node:e.doc}),o=this.storage.characters({node:t.doc});if(o<=n)return!0;if(r>n&&o>n&&o<=r)return!0;if(r>n&&o>n&&o>r)return!1;if(!t.getMeta("paste"))return!1;const l=t.selection.$head.pos,c=l-(o-n),d=l;t.deleteRange(c,d);return!(this.storage.characters({node:t.doc})>n)}})]}})},1586:function(t,e,n){"use strict";n.d(e,"a",(function(){return xe})),n.d(e,"b",(function(){return Oe}));var r=n(1426),o=n(1599);function l(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function c(t){return t instanceof l(t).Element||t instanceof Element}function d(t){return t instanceof l(t).HTMLElement||t instanceof HTMLElement}function h(t){return"undefined"!=typeof ShadowRoot&&(t instanceof l(t).ShadowRoot||t instanceof ShadowRoot)}var f=Math.max,m=Math.min,y=Math.round;function v(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map((function(t){return t.brand+"/"+t.version})).join(" "):navigator.userAgent}function w(){return!/^((?!chrome|android).)*safari/i.test(v())}function k(element,t,e){void 0===t&&(t=!1),void 0===e&&(e=!1);var n=element.getBoundingClientRect(),r=1,o=1;t&&d(element)&&(r=element.offsetWidth>0&&y(n.width)/element.offsetWidth||1,o=element.offsetHeight>0&&y(n.height)/element.offsetHeight||1);var h=(c(element)?l(element):window).visualViewport,f=!w()&&e,m=(n.left+(f&&h?h.offsetLeft:0))/r,v=(n.top+(f&&h?h.offsetTop:0))/o,k=n.width/r,x=n.height/o;return{width:k,height:x,top:v,right:m+k,bottom:v+x,left:m,x:m,y:v}}function x(t){var e=l(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function O(element){return element?(element.nodeName||"").toLowerCase():null}function S(element){return((c(element)?element.ownerDocument:element.document)||window.document).documentElement}function M(element){return k(S(element)).left+x(element).scrollLeft}function C(element){return l(element).getComputedStyle(element)}function T(element){var t=C(element),e=t.overflow,n=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(e+r+n)}function E(t,e,n){void 0===n&&(n=!1);var r,element,o=d(e),c=d(e)&&function(element){var rect=element.getBoundingClientRect(),t=y(rect.width)/element.offsetWidth||1,e=y(rect.height)/element.offsetHeight||1;return 1!==t||1!==e}(e),h=S(e),rect=k(t,c,n),f={scrollLeft:0,scrollTop:0},m={x:0,y:0};return(o||!o&&!n)&&(("body"!==O(e)||T(h))&&(f=(r=e)!==l(r)&&d(r)?{scrollLeft:(element=r).scrollLeft,scrollTop:element.scrollTop}:x(r)),d(e)?((m=k(e,!0)).x+=e.clientLeft,m.y+=e.clientTop):h&&(m.x=M(h))),{x:rect.left+f.scrollLeft-m.x,y:rect.top+f.scrollTop-m.y,width:rect.width,height:rect.height}}function A(element){var t=k(element),e=element.offsetWidth,n=element.offsetHeight;return Math.abs(t.width-e)<=1&&(e=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:element.offsetLeft,y:element.offsetTop,width:e,height:n}}function N(element){return"html"===O(element)?element:element.assignedSlot||element.parentNode||(h(element)?element.host:null)||S(element)}function D(t){return["html","body","#document"].indexOf(O(t))>=0?t.ownerDocument.body:d(t)&&T(t)?t:D(N(t))}function P(element,t){var e;void 0===t&&(t=[]);var n=D(element),r=n===(null==(e=element.ownerDocument)?void 0:e.body),o=l(n),c=r?[o].concat(o.visualViewport||[],T(n)?n:[]):n,d=t.concat(c);return r?d:d.concat(P(N(c)))}function R(element){return["table","td","th"].indexOf(O(element))>=0}function j(element){return d(element)&&"fixed"!==C(element).position?element.offsetParent:null}function I(element){for(var t=l(element),e=j(element);e&&R(e)&&"static"===C(e).position;)e=j(e);return e&&("html"===O(e)||"body"===O(e)&&"static"===C(e).position)?t:e||function(element){var t=/firefox/i.test(v());if(/Trident/i.test(v())&&d(element)&&"fixed"===C(element).position)return null;var e=N(element);for(h(e)&&(e=e.host);d(e)&&["html","body"].indexOf(O(e))<0;){var n=C(e);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||t&&"filter"===n.willChange||t&&n.filter&&"none"!==n.filter)return e;e=e.parentNode}return null}(element)||t}var L="top",B="bottom",$="right",z="left",H="auto",V=[L,B,$,z],F="start",_="end",W="viewport",K="popper",J=V.reduce((function(t,e){return t.concat([e+"-"+F,e+"-"+_])}),[]),U=[].concat(V,[H]).reduce((function(t,e){return t.concat([e,e+"-"+F,e+"-"+_])}),[]),G=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function Y(t){var map=new Map,e=new Set,n=[];function r(t){e.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!e.has(t)){var n=map.get(t);n&&r(n)}})),n.push(t)}return t.forEach((function(t){map.set(t.name,t)})),t.forEach((function(t){e.has(t.name)||r(t)})),n}var X={placement:"bottom",modifiers:[],strategy:"absolute"};function Q(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return!e.some((function(element){return!(element&&"function"==typeof element.getBoundingClientRect)}))}function Z(t){void 0===t&&(t={});var e=t,n=e.defaultModifiers,r=void 0===n?[]:n,o=e.defaultOptions,l=void 0===o?X:o;return function(t,e,n){void 0===n&&(n=l);var o,d,h={placement:"bottom",orderedModifiers:[],options:Object.assign({},X,l),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},f=[],m=!1,y={state:h,setOptions:function(n){var o="function"==typeof n?n(h.options):n;v(),h.options=Object.assign({},l,h.options,o),h.scrollParents={reference:c(t)?P(t):t.contextElement?P(t.contextElement):[],popper:P(e)};var d,m,w=function(t){var e=Y(t);return G.reduce((function(t,n){return t.concat(e.filter((function(t){return t.phase===n})))}),[])}((d=[].concat(r,h.options.modifiers),m=d.reduce((function(t,e){var n=t[e.name];return t[e.name]=n?Object.assign({},n,e,{options:Object.assign({},n.options,e.options),data:Object.assign({},n.data,e.data)}):e,t}),{}),Object.keys(m).map((function(t){return m[t]}))));return h.orderedModifiers=w.filter((function(t){return t.enabled})),h.orderedModifiers.forEach((function(t){var e=t.name,n=t.options,r=void 0===n?{}:n,o=t.effect;if("function"==typeof o){var l=o({state:h,name:e,instance:y,options:r}),c=function(){};f.push(l||c)}})),y.update()},forceUpdate:function(){if(!m){var t=h.elements,e=t.reference,n=t.popper;if(Q(e,n)){h.rects={reference:E(e,I(n),"fixed"===h.options.strategy),popper:A(n)},h.reset=!1,h.placement=h.options.placement,h.orderedModifiers.forEach((function(t){return h.modifiersData[t.name]=Object.assign({},t.data)}));for(var r=0;r<h.orderedModifiers.length;r++)if(!0!==h.reset){var o=h.orderedModifiers[r],l=o.fn,c=o.options,d=void 0===c?{}:c,f=o.name;"function"==typeof l&&(h=l({state:h,options:d,name:f,instance:y})||h)}else h.reset=!1,r=-1}}},update:(o=function(){return new Promise((function(t){y.forceUpdate(),t(h)}))},function(){return d||(d=new Promise((function(t){Promise.resolve().then((function(){d=void 0,t(o())}))}))),d}),destroy:function(){v(),m=!0}};if(!Q(t,e))return y;function v(){f.forEach((function(t){return t()})),f=[]}return y.setOptions(n).then((function(t){!m&&n.onFirstUpdate&&n.onFirstUpdate(t)})),y}}var tt={passive:!0};function et(t){return t.split("-")[0]}function nt(t){return t.split("-")[1]}function ot(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function it(t){var e,n=t.reference,element=t.element,r=t.placement,o=r?et(r):null,l=r?nt(r):null,c=n.x+n.width/2-element.width/2,d=n.y+n.height/2-element.height/2;switch(o){case L:e={x:c,y:n.y-element.height};break;case B:e={x:c,y:n.y+n.height};break;case $:e={x:n.x+n.width,y:d};break;case z:e={x:n.x-element.width,y:d};break;default:e={x:n.x,y:n.y}}var h=o?ot(o):null;if(null!=h){var f="y"===h?"height":"width";switch(l){case F:e[h]=e[h]-(n[f]/2-element[f]/2);break;case _:e[h]=e[h]+(n[f]/2-element[f]/2)}}return e}var st={top:"auto",right:"auto",bottom:"auto",left:"auto"};function at(t){var e,n=t.popper,r=t.popperRect,o=t.placement,c=t.variation,d=t.offsets,h=t.position,f=t.gpuAcceleration,m=t.adaptive,v=t.roundOffsets,w=t.isFixed,k=d.x,x=void 0===k?0:k,O=d.y,M=void 0===O?0:O,T="function"==typeof v?v({x:x,y:M}):{x:x,y:M};x=T.x,M=T.y;var E=d.hasOwnProperty("x"),A=d.hasOwnProperty("y"),N=z,D=L,P=window;if(m){var R=I(n),j="clientHeight",H="clientWidth";if(R===l(n)&&"static"!==C(R=S(n)).position&&"absolute"===h&&(j="scrollHeight",H="scrollWidth"),R=R,o===L||(o===z||o===$)&&c===_)D=B,M-=(w&&R===P&&P.visualViewport?P.visualViewport.height:R[j])-r.height,M*=f?1:-1;if(o===z||(o===L||o===B)&&c===_)N=$,x-=(w&&R===P&&P.visualViewport?P.visualViewport.width:R[H])-r.width,x*=f?1:-1}var V,F=Object.assign({position:h},m&&st),W=!0===v?function(t,e){var n=t.x,r=t.y,o=e.devicePixelRatio||1;return{x:y(n*o)/o||0,y:y(r*o)/o||0}}({x:x,y:M},l(n)):{x:x,y:M};return x=W.x,M=W.y,f?Object.assign({},F,((V={})[D]=A?"0":"",V[N]=E?"0":"",V.transform=(P.devicePixelRatio||1)<=1?"translate("+x+"px, "+M+"px)":"translate3d("+x+"px, "+M+"px, 0)",V)):Object.assign({},F,((e={})[D]=A?M+"px":"",e[N]=E?x+"px":"",e.transform="",e))}var lt={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var style=e.styles[t]||{},n=e.attributes[t]||{},element=e.elements[t];d(element)&&O(element)&&(Object.assign(element.style,style),Object.keys(n).forEach((function(t){var e=n[t];!1===e?element.removeAttribute(t):element.setAttribute(t,!0===e?"":e)})))}))},effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow),function(){Object.keys(e.elements).forEach((function(t){var element=e.elements[t],r=e.attributes[t]||{},style=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:n[t]).reduce((function(style,t){return style[t]="",style}),{});d(element)&&O(element)&&(Object.assign(element.style,style),Object.keys(r).forEach((function(t){element.removeAttribute(t)})))}))}},requires:["computeStyles"]};var ct={left:"right",right:"left",bottom:"top",top:"bottom"};function ht(t){return t.replace(/left|right|bottom|top/g,(function(t){return ct[t]}))}var pt={start:"end",end:"start"};function ut(t){return t.replace(/start|end/g,(function(t){return pt[t]}))}function ft(t,e){var n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&h(n)){var r=e;do{if(r&&t.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function mt(rect){return Object.assign({},rect,{left:rect.x,top:rect.y,right:rect.x+rect.width,bottom:rect.y+rect.height})}function gt(element,t,e){return t===W?mt(function(element,t){var e=l(element),html=S(element),n=e.visualViewport,r=html.clientWidth,o=html.clientHeight,c=0,d=0;if(n){r=n.width,o=n.height;var h=w();(h||!h&&"fixed"===t)&&(c=n.offsetLeft,d=n.offsetTop)}return{width:r,height:o,x:c+M(element),y:d}}(element,e)):c(t)?function(element,t){var rect=k(element,!1,"fixed"===t);return rect.top=rect.top+element.clientTop,rect.left=rect.left+element.clientLeft,rect.bottom=rect.top+element.clientHeight,rect.right=rect.left+element.clientWidth,rect.width=element.clientWidth,rect.height=element.clientHeight,rect.x=rect.left,rect.y=rect.top,rect}(t,e):mt(function(element){var t,html=S(element),e=x(element),body=null==(t=element.ownerDocument)?void 0:t.body,n=f(html.scrollWidth,html.clientWidth,body?body.scrollWidth:0,body?body.clientWidth:0),r=f(html.scrollHeight,html.clientHeight,body?body.scrollHeight:0,body?body.clientHeight:0),o=-e.scrollLeft+M(element),l=-e.scrollTop;return"rtl"===C(body||html).direction&&(o+=f(html.clientWidth,body?body.clientWidth:0)-n),{width:n,height:r,x:o,y:l}}(S(element)))}function yt(element,t,e,n){var r="clippingParents"===t?function(element){var t=P(N(element)),e=["absolute","fixed"].indexOf(C(element).position)>=0&&d(element)?I(element):element;return c(e)?t.filter((function(t){return c(t)&&ft(t,e)&&"body"!==O(t)})):[]}(element):[].concat(t),o=[].concat(r,[e]),l=o[0],h=o.reduce((function(t,e){var rect=gt(element,e,n);return t.top=f(rect.top,t.top),t.right=m(rect.right,t.right),t.bottom=m(rect.bottom,t.bottom),t.left=f(rect.left,t.left),t}),gt(element,l,n));return h.width=h.right-h.left,h.height=h.bottom-h.top,h.x=h.left,h.y=h.top,h}function vt(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function bt(t,e){return e.reduce((function(e,n){return e[n]=t,e}),{})}function wt(t,e){void 0===e&&(e={});var n=e,r=n.placement,o=void 0===r?t.placement:r,l=n.strategy,d=void 0===l?t.strategy:l,h=n.boundary,f=void 0===h?"clippingParents":h,m=n.rootBoundary,y=void 0===m?W:m,v=n.elementContext,w=void 0===v?K:v,x=n.altBoundary,O=void 0!==x&&x,M=n.padding,C=void 0===M?0:M,T=vt("number"!=typeof C?C:bt(C,V)),E=w===K?"reference":K,A=t.rects.popper,element=t.elements[O?E:w],N=yt(c(element)?element:element.contextElement||S(t.elements.popper),f,y,d),D=k(t.elements.reference),P=it({reference:D,element:A,strategy:"absolute",placement:o}),R=mt(Object.assign({},A,P)),j=w===K?R:D,I={top:N.top-j.top+T.top,bottom:j.bottom-N.bottom+T.bottom,left:N.left-j.left+T.left,right:j.right-N.right+T.right},z=t.modifiersData.offset;if(w===K&&z){var H=z[o];Object.keys(I).forEach((function(t){var e=[$,B].indexOf(t)>=0?1:-1,n=[L,B].indexOf(t)>=0?"y":"x";I[t]+=H[n]*e}))}return I}function kt(t,e,n){return f(t,m(e,n))}function xt(t,rect,e){return void 0===e&&(e={x:0,y:0}),{top:t.top-rect.height-e.y,right:t.right-rect.width+e.x,bottom:t.bottom-rect.height+e.y,left:t.left-rect.width-e.x}}function Ot(t){return[L,$,B,z].some((function(e){return t[e]>=0}))}var St=Z({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,n=t.instance,r=t.options,o=r.scroll,c=void 0===o||o,d=r.resize,h=void 0===d||d,f=l(e.elements.popper),m=[].concat(e.scrollParents.reference,e.scrollParents.popper);return c&&m.forEach((function(t){t.addEventListener("scroll",n.update,tt)})),h&&f.addEventListener("resize",n.update,tt),function(){c&&m.forEach((function(t){t.removeEventListener("scroll",n.update,tt)})),h&&f.removeEventListener("resize",n.update,tt)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,n=t.name;e.modifiersData[n]=it({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,n=t.options,r=n.gpuAcceleration,o=void 0===r||r,l=n.adaptive,c=void 0===l||l,d=n.roundOffsets,h=void 0===d||d,f={placement:et(e.placement),variation:nt(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:o,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,at(Object.assign({},f,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:c,roundOffsets:h})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,at(Object.assign({},f,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:h})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}},lt,{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,n=t.options,r=t.name,o=n.offset,l=void 0===o?[0,0]:o,data=U.reduce((function(t,n){return t[n]=function(t,e,n){var r=et(t),o=[z,L].indexOf(r)>=0?-1:1,l="function"==typeof n?n(Object.assign({},e,{placement:t})):n,c=l[0],d=l[1];return c=c||0,d=(d||0)*o,[z,$].indexOf(r)>=0?{x:d,y:c}:{x:c,y:d}}(n,e.rects,l),t}),{}),c=data[e.placement],d=c.x,h=c.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=d,e.modifiersData.popperOffsets.y+=h),e.modifiersData[r]=data}},{name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,r=t.name;if(!e.modifiersData[r]._skip){for(var o=n.mainAxis,l=void 0===o||o,c=n.altAxis,d=void 0===c||c,h=n.fallbackPlacements,f=n.padding,m=n.boundary,y=n.rootBoundary,v=n.altBoundary,w=n.flipVariations,k=void 0===w||w,x=n.allowedAutoPlacements,O=e.options.placement,S=et(O),M=h||(S===O||!k?[ht(O)]:function(t){if(et(t)===H)return[];var e=ht(t);return[ut(t),e,ut(e)]}(O)),C=[O].concat(M).reduce((function(t,n){return t.concat(et(n)===H?function(t,e){void 0===e&&(e={});var n=e,r=n.placement,o=n.boundary,l=n.rootBoundary,c=n.padding,d=n.flipVariations,h=n.allowedAutoPlacements,f=void 0===h?U:h,m=nt(r),y=m?d?J:J.filter((function(t){return nt(t)===m})):V,v=y.filter((function(t){return f.indexOf(t)>=0}));0===v.length&&(v=y);var w=v.reduce((function(e,n){return e[n]=wt(t,{placement:n,boundary:o,rootBoundary:l,padding:c})[et(n)],e}),{});return Object.keys(w).sort((function(a,b){return w[a]-w[b]}))}(e,{placement:n,boundary:m,rootBoundary:y,padding:f,flipVariations:k,allowedAutoPlacements:x}):n)}),[]),T=e.rects.reference,E=e.rects.popper,A=new Map,N=!0,D=C[0],i=0;i<C.length;i++){var P=C[i],R=et(P),j=nt(P)===F,I=[L,B].indexOf(R)>=0,_=I?"width":"height",W=wt(e,{placement:P,boundary:m,rootBoundary:y,altBoundary:v,padding:f}),K=I?j?$:z:j?B:L;T[_]>E[_]&&(K=ht(K));var G=ht(K),Y=[];if(l&&Y.push(W[R]<=0),d&&Y.push(W[K]<=0,W[G]<=0),Y.every((function(t){return t}))){D=P,N=!1;break}A.set(P,Y)}if(N)for(var X=function(t){var e=C.find((function(e){var n=A.get(e);if(n)return n.slice(0,t).every((function(t){return t}))}));if(e)return D=e,"break"},Q=k?3:1;Q>0;Q--){if("break"===X(Q))break}e.placement!==D&&(e.modifiersData[r]._skip=!0,e.placement=D,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,r=t.name,o=n.mainAxis,l=void 0===o||o,c=n.altAxis,d=void 0!==c&&c,h=n.boundary,y=n.rootBoundary,v=n.altBoundary,w=n.padding,k=n.tether,x=void 0===k||k,O=n.tetherOffset,S=void 0===O?0:O,M=wt(e,{boundary:h,rootBoundary:y,padding:w,altBoundary:v}),C=et(e.placement),T=nt(e.placement),E=!T,N=ot(C),D="x"===N?"y":"x",P=e.modifiersData.popperOffsets,R=e.rects.reference,j=e.rects.popper,H="function"==typeof S?S(Object.assign({},e.rects,{placement:e.placement})):S,V="number"==typeof H?{mainAxis:H,altAxis:H}:Object.assign({mainAxis:0,altAxis:0},H),_=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,data={x:0,y:0};if(P){if(l){var W,K="y"===N?L:z,J="y"===N?B:$,U="y"===N?"height":"width",G=P[N],Y=G+M[K],X=G-M[J],Q=x?-j[U]/2:0,Z=T===F?R[U]:j[U],tt=T===F?-j[U]:-R[U],it=e.elements.arrow,st=x&&it?A(it):{width:0,height:0},at=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},lt=at[K],ct=at[J],ht=kt(0,R[U],st[U]),pt=E?R[U]/2-Q-ht-lt-V.mainAxis:Z-ht-lt-V.mainAxis,ut=E?-R[U]/2+Q+ht+ct+V.mainAxis:tt+ht+ct+V.mainAxis,ft=e.elements.arrow&&I(e.elements.arrow),mt=ft?"y"===N?ft.clientTop||0:ft.clientLeft||0:0,gt=null!=(W=null==_?void 0:_[N])?W:0,yt=G+ut-gt,vt=kt(x?m(Y,G+pt-gt-mt):Y,G,x?f(X,yt):X);P[N]=vt,data[N]=vt-G}if(d){var bt,xt="x"===N?L:z,Ot="x"===N?B:$,St=P[D],Mt="y"===D?"height":"width",Ct=St+M[xt],Tt=St-M[Ot],Et=-1!==[L,z].indexOf(C),At=null!=(bt=null==_?void 0:_[D])?bt:0,Nt=Et?Ct:St-R[Mt]-j[Mt]-At+V.altAxis,Dt=Et?St+R[Mt]+j[Mt]-At-V.altAxis:Tt,Pt=x&&Et?function(t,e,n){var r=kt(t,e,n);return r>n?n:r}(Nt,St,Dt):kt(x?Nt:Ct,St,x?Dt:Tt);P[D]=Pt,data[D]=Pt-St}e.modifiersData[r]=data}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,n=t.state,r=t.name,o=t.options,l=n.elements.arrow,c=n.modifiersData.popperOffsets,d=et(n.placement),h=ot(d),f=[z,$].indexOf(d)>=0?"height":"width";if(l&&c){var m=function(t,e){return vt("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:bt(t,V))}(o.padding,n),y=A(l),v="y"===h?L:z,w="y"===h?B:$,k=n.rects.reference[f]+n.rects.reference[h]-c[h]-n.rects.popper[f],x=c[h]-n.rects.reference[h],O=I(l),S=O?"y"===h?O.clientHeight||0:O.clientWidth||0:0,M=k/2-x/2,C=m[v],T=S-y[f]-m[w],E=S/2-y[f]/2+M,N=kt(C,E,T),D=h;n.modifiersData[r]=((e={})[D]=N,e.centerOffset=N-E,e)}},effect:function(t){var e=t.state,n=t.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=e.elements.popper.querySelector(r)))&&ft(e.elements.popper,r)&&(e.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,n=t.name,r=e.rects.reference,o=e.rects.popper,l=e.modifiersData.preventOverflow,c=wt(e,{elementContext:"reference"}),d=wt(e,{altBoundary:!0}),h=xt(c,r),f=xt(d,o,l),m=Ot(h),y=Ot(f);e.modifiersData[n]={referenceClippingOffsets:h,popperEscapeOffsets:f,isReferenceHidden:m,hasPopperEscaped:y},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":m,"data-popper-escaped":y})}}]}),Mt="tippy-content",Ct="tippy-backdrop",Tt="tippy-arrow",Et="tippy-svg-arrow",At={passive:!0,capture:!0},Nt=function(){return document.body};function Dt(t,e,n){if(Array.isArray(t)){var r=t[e];return null==r?Array.isArray(n)?n[e]:n:r}return t}function Pt(t,e){var n={}.toString.call(t);return 0===n.indexOf("[object")&&n.indexOf(e+"]")>-1}function Rt(t,e){return"function"==typeof t?t.apply(void 0,e):t}function jt(t,e){return 0===e?t:function(r){clearTimeout(n),n=setTimeout((function(){t(r)}),e)};var n}function It(t){return[].concat(t)}function Lt(t,e){-1===t.indexOf(e)&&t.push(e)}function Bt(t){return t.split("-")[0]}function $t(t){return[].slice.call(t)}function zt(t){return Object.keys(t).reduce((function(e,n){return void 0!==t[n]&&(e[n]=t[n]),e}),{})}function div(){return document.createElement("div")}function Ht(t){return["Element","Fragment"].some((function(e){return Pt(t,e)}))}function Vt(t){return Pt(t,"MouseEvent")}function Ft(t){return!(!t||!t._tippy||t._tippy.reference!==t)}function _t(t){return Ht(t)?[t]:function(t){return Pt(t,"NodeList")}(t)?$t(t):Array.isArray(t)?t:$t(document.querySelectorAll(t))}function Wt(t,e){t.forEach((function(t){t&&(t.style.transitionDuration=e+"ms")}))}function qt(t,e){t.forEach((function(t){t&&t.setAttribute("data-state",e)}))}function Kt(t){var e,element=It(t)[0];return null!=element&&null!=(e=element.ownerDocument)&&e.body?element.ownerDocument:document}function Jt(t,e,n){var r=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(e){t[r](e,n)}))}function Ut(t,e){for(var n=e;n;){var r;if(t.contains(n))return!0;n=null==n.getRootNode||null==(r=n.getRootNode())?void 0:r.host}return!1}var Gt={isTouch:!1},Yt=0;function Xt(){Gt.isTouch||(Gt.isTouch=!0,window.performance&&document.addEventListener("mousemove",Qt))}function Qt(){var t=performance.now();t-Yt<20&&(Gt.isTouch=!1,document.removeEventListener("mousemove",Qt)),Yt=t}function Zt(){var t=document.activeElement;if(Ft(t)){var e=t._tippy;t.blur&&!e.state.isVisible&&t.blur()}}var te=!!("undefined"!=typeof window&&"undefined"!=typeof document)&&!!window.msCrypto;var ee={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},ne=Object.assign({appendTo:Nt,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},ee,{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),re=Object.keys(ne);function oe(t){var e=(t.plugins||[]).reduce((function(e,n){var r,o=n.name,l=n.defaultValue;o&&(e[o]=void 0!==t[o]?t[o]:null!=(r=ne[o])?r:l);return e}),{});return Object.assign({},t,e)}function ie(t,e){var n=Object.assign({},e,{content:Rt(e.content,[t])},e.ignoreAttributes?{}:function(t,e){return(e?Object.keys(oe(Object.assign({},ne,{plugins:e}))):re).reduce((function(e,n){var r=(t.getAttribute("data-tippy-"+n)||"").trim();if(!r)return e;if("content"===n)e[n]=r;else try{e[n]=JSON.parse(r)}catch(t){e[n]=r}return e}),{})}(t,e.plugins));return n.aria=Object.assign({},ne.aria,n.aria),n.aria={expanded:"auto"===n.aria.expanded?e.interactive:n.aria.expanded,content:"auto"===n.aria.content?e.interactive?null:"describedby":n.aria.content},n}function se(element,html){element.innerHTML=html}function ae(t){var e=div();return!0===t?e.className=Tt:(e.className=Et,Ht(t)?e.appendChild(t):se(e,t)),e}function le(content,t){Ht(t.content)?(se(content,""),content.appendChild(t.content)):"function"!=typeof t.content&&(t.allowHTML?se(content,t.content):content.textContent=t.content)}function ce(t){var e=t.firstElementChild,n=$t(e.children);return{box:e,content:n.find((function(t){return t.classList.contains(Mt)})),arrow:n.find((function(t){return t.classList.contains(Tt)||t.classList.contains(Et)})),backdrop:n.find((function(t){return t.classList.contains(Ct)}))}}function de(t){var e=div(),n=div();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var content=div();function r(n,r){var o=ce(e),l=o.box,content=o.content,c=o.arrow;r.theme?l.setAttribute("data-theme",r.theme):l.removeAttribute("data-theme"),"string"==typeof r.animation?l.setAttribute("data-animation",r.animation):l.removeAttribute("data-animation"),r.inertia?l.setAttribute("data-inertia",""):l.removeAttribute("data-inertia"),l.style.maxWidth="number"==typeof r.maxWidth?r.maxWidth+"px":r.maxWidth,r.role?l.setAttribute("role",r.role):l.removeAttribute("role"),n.content===r.content&&n.allowHTML===r.allowHTML||le(content,t.props),r.arrow?c?n.arrow!==r.arrow&&(l.removeChild(c),l.appendChild(ae(r.arrow))):l.appendChild(ae(r.arrow)):c&&l.removeChild(c)}return content.className=Mt,content.setAttribute("data-state","hidden"),le(content,t.props),e.appendChild(n),n.appendChild(content),r(t.props,t.props),{popper:e,onUpdate:r}}de.$$tippy=!0;var he=1,pe=[],ue=[];function fe(t,e){var n,r,o,l,c,d,h,f,m=ie(t,Object.assign({},ne,oe(zt(e)))),y=!1,v=!1,w=!1,k=!1,x=[],O=jt(et,m.interactiveDebounce),S=he++,M=(f=m.plugins).filter((function(t,e){return f.indexOf(t)===e})),C={id:S,reference:t,popper:div(),popperInstance:null,props:m,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:M,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(r),cancelAnimationFrame(o)},setProps:function(e){0;if(C.state.isDestroyed)return;H("onBeforeUpdate",[C,e]),Z();var n=C.props,r=ie(t,Object.assign({},n,zt(e),{ignoreAttributes:!0}));C.props=r,Q(),n.interactiveDebounce!==r.interactiveDebounce&&(_(),O=jt(et,r.interactiveDebounce));n.triggerTarget&&!r.triggerTarget?It(n.triggerTarget).forEach((function(t){t.removeAttribute("aria-expanded")})):r.triggerTarget&&t.removeAttribute("aria-expanded");F(),z(),A&&A(n,r);C.popperInstance&&(st(),lt().forEach((function(t){requestAnimationFrame(t._tippy.popperInstance.forceUpdate)})));H("onAfterUpdate",[C,e])},setContent:function(content){C.setProps({content:content})},show:function(){0;var t=C.state.isVisible,e=C.state.isDestroyed,n=!C.state.isEnabled,r=Gt.isTouch&&!C.props.touch,o=Dt(C.props.duration,0,ne.duration);if(t||e||n||r)return;if(I().hasAttribute("disabled"))return;if(H("onShow",[C],!1),!1===C.props.onShow(C))return;C.state.isVisible=!0,j()&&(E.style.visibility="visible");z(),U(),C.state.isMounted||(E.style.transition="none");if(j()){var l=B(),c=l.box,content=l.content;Wt([c,content],0)}d=function(){var t;if(C.state.isVisible&&!k){if(k=!0,E.offsetHeight,E.style.transition=C.props.moveTransition,j()&&C.props.animation){var e=B(),n=e.box,r=e.content;Wt([n,r],o),qt([n,r],"visible")}V(),F(),Lt(ue,C),null==(t=C.popperInstance)||t.forceUpdate(),H("onMount",[C]),C.props.animation&&j()&&function(t,e){Y(t,e)}(o,(function(){C.state.isShown=!0,H("onShown",[C])}))}},function(){var t,e=C.props.appendTo,n=I();t=C.props.interactive&&e===Nt||"parent"===e?n.parentNode:Rt(e,[n]);t.contains(E)||t.appendChild(E);C.state.isMounted=!0,st(),!1}()},hide:function(){0;var t=!C.state.isVisible,e=C.state.isDestroyed,n=!C.state.isEnabled,r=Dt(C.props.duration,1,ne.duration);if(t||e||n)return;if(H("onHide",[C],!1),!1===C.props.onHide(C))return;C.state.isVisible=!1,C.state.isShown=!1,k=!1,y=!1,j()&&(E.style.visibility="hidden");if(_(),G(),z(!0),j()){var o=B(),l=o.box,content=o.content;C.props.animation&&(Wt([l,content],r),qt([l,content],"hidden"))}V(),F(),C.props.animation?j()&&function(t,e){Y(t,(function(){!C.state.isVisible&&E.parentNode&&E.parentNode.contains(E)&&e()}))}(r,C.unmount):C.unmount()},hideWithInteractivity:function(t){0;L().addEventListener("mousemove",O),Lt(pe,O),O(t)},enable:function(){C.state.isEnabled=!0},disable:function(){C.hide(),C.state.isEnabled=!1},unmount:function(){0;C.state.isVisible&&C.hide();if(!C.state.isMounted)return;at(),lt().forEach((function(t){t._tippy.unmount()})),E.parentNode&&E.parentNode.removeChild(E);ue=ue.filter((function(i){return i!==C})),C.state.isMounted=!1,H("onHidden",[C])},destroy:function(){0;if(C.state.isDestroyed)return;C.clearDelayTimeouts(),C.unmount(),Z(),delete t._tippy,C.state.isDestroyed=!0,H("onDestroy",[C])}};if(!m.render)return C;var T=m.render(C),E=T.popper,A=T.onUpdate;E.setAttribute("data-tippy-root",""),E.id="tippy-"+C.id,C.popper=E,t._tippy=C,E._tippy=C;var N=M.map((function(t){return t.fn(C)})),D=t.hasAttribute("aria-expanded");return Q(),F(),z(),H("onCreate",[C]),m.showOnCreate&&ct(),E.addEventListener("mouseenter",(function(){C.props.interactive&&C.state.isVisible&&C.clearDelayTimeouts()})),E.addEventListener("mouseleave",(function(){C.props.interactive&&C.props.trigger.indexOf("mouseenter")>=0&&L().addEventListener("mousemove",O)})),C;function P(){var t=C.props.touch;return Array.isArray(t)?t:[t,0]}function R(){return"hold"===P()[0]}function j(){var t;return!(null==(t=C.props.render)||!t.$$tippy)}function I(){return h||t}function L(){var t=I().parentNode;return t?Kt(t):document}function B(){return ce(E)}function $(t){return C.state.isMounted&&!C.state.isVisible||Gt.isTouch||l&&"focus"===l.type?0:Dt(C.props.delay,t?0:1,ne.delay)}function z(t){void 0===t&&(t=!1),E.style.pointerEvents=C.props.interactive&&!t?"":"none",E.style.zIndex=""+C.props.zIndex}function H(t,e,n){var r;(void 0===n&&(n=!0),N.forEach((function(n){n[t]&&n[t].apply(n,e)})),n)&&(r=C.props)[t].apply(r,e)}function V(){var e=C.props.aria;if(e.content){var n="aria-"+e.content,r=E.id;It(C.props.triggerTarget||t).forEach((function(t){var e=t.getAttribute(n);if(C.state.isVisible)t.setAttribute(n,e?e+" "+r:r);else{var o=e&&e.replace(r,"").trim();o?t.setAttribute(n,o):t.removeAttribute(n)}}))}}function F(){!D&&C.props.aria.expanded&&It(C.props.triggerTarget||t).forEach((function(t){C.props.interactive?t.setAttribute("aria-expanded",C.state.isVisible&&t===I()?"true":"false"):t.removeAttribute("aria-expanded")}))}function _(){L().removeEventListener("mousemove",O),pe=pe.filter((function(t){return t!==O}))}function W(e){if(!Gt.isTouch||!w&&"mousedown"!==e.type){var n=e.composedPath&&e.composedPath()[0]||e.target;if(!C.props.interactive||!Ut(E,n)){if(It(C.props.triggerTarget||t).some((function(t){return Ut(t,n)}))){if(Gt.isTouch)return;if(C.state.isVisible&&C.props.trigger.indexOf("click")>=0)return}else H("onClickOutside",[C,e]);!0===C.props.hideOnClick&&(C.clearDelayTimeouts(),C.hide(),v=!0,setTimeout((function(){v=!1})),C.state.isMounted||G())}}}function K(){w=!0}function J(){w=!1}function U(){var t=L();t.addEventListener("mousedown",W,!0),t.addEventListener("touchend",W,At),t.addEventListener("touchstart",J,At),t.addEventListener("touchmove",K,At)}function G(){var t=L();t.removeEventListener("mousedown",W,!0),t.removeEventListener("touchend",W,At),t.removeEventListener("touchstart",J,At),t.removeEventListener("touchmove",K,At)}function Y(t,e){var n=B().box;function r(t){t.target===n&&(Jt(n,"remove",r),e())}if(0===t)return e();Jt(n,"remove",c),Jt(n,"add",r),c=r}function X(e,n,r){void 0===r&&(r=!1),It(C.props.triggerTarget||t).forEach((function(t){t.addEventListener(e,n,r),x.push({node:t,eventType:e,handler:n,options:r})}))}function Q(){var t;R()&&(X("touchstart",tt,{passive:!0}),X("touchend",nt,{passive:!0})),(t=C.props.trigger,t.split(/\s+/).filter(Boolean)).forEach((function(t){if("manual"!==t)switch(X(t,tt),t){case"mouseenter":X("mouseleave",nt);break;case"focus":X(te?"focusout":"blur",ot);break;case"focusin":X("focusout",ot)}}))}function Z(){x.forEach((function(t){var e=t.node,n=t.eventType,r=t.handler,o=t.options;e.removeEventListener(n,r,o)})),x=[]}function tt(t){var e,n=!1;if(C.state.isEnabled&&!it(t)&&!v){var r="focus"===(null==(e=l)?void 0:e.type);l=t,h=t.currentTarget,F(),!C.state.isVisible&&Vt(t)&&pe.forEach((function(e){return e(t)})),"click"===t.type&&(C.props.trigger.indexOf("mouseenter")<0||y)&&!1!==C.props.hideOnClick&&C.state.isVisible?n=!0:ct(t),"click"===t.type&&(y=!n),n&&!r&&ht(t)}}function et(t){var e=t.target,n=I().contains(e)||E.contains(e);"mousemove"===t.type&&n||function(t,e){var n=e.clientX,r=e.clientY;return t.every((function(t){var e=t.popperRect,o=t.popperState,l=t.props.interactiveBorder,c=Bt(o.placement),d=o.modifiersData.offset;if(!d)return!0;var h="bottom"===c?d.top.y:0,f="top"===c?d.bottom.y:0,m="right"===c?d.left.x:0,y="left"===c?d.right.x:0,v=e.top-r+h>l,w=r-e.bottom-f>l,k=e.left-n+m>l,x=n-e.right-y>l;return v||w||k||x}))}(lt().concat(E).map((function(t){var e,n=null==(e=t._tippy.popperInstance)?void 0:e.state;return n?{popperRect:t.getBoundingClientRect(),popperState:n,props:m}:null})).filter(Boolean),t)&&(_(),ht(t))}function nt(t){it(t)||C.props.trigger.indexOf("click")>=0&&y||(C.props.interactive?C.hideWithInteractivity(t):ht(t))}function ot(t){C.props.trigger.indexOf("focusin")<0&&t.target!==I()||C.props.interactive&&t.relatedTarget&&E.contains(t.relatedTarget)||ht(t)}function it(t){return!!Gt.isTouch&&R()!==t.type.indexOf("touch")>=0}function st(){at();var e=C.props,n=e.popperOptions,r=e.placement,o=e.offset,l=e.getReferenceClientRect,c=e.moveTransition,h=j()?ce(E).arrow:null,f=l?{getBoundingClientRect:l,contextElement:l.contextElement||I()}:t,m=[{name:"offset",options:{offset:o}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!c}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(t){var e=t.state;if(j()){var n=B().box;["placement","reference-hidden","escaped"].forEach((function(t){"placement"===t?n.setAttribute("data-placement",e.placement):e.attributes.popper["data-popper-"+t]?n.setAttribute("data-"+t,""):n.removeAttribute("data-"+t)})),e.attributes.popper={}}}}];j()&&h&&m.push({name:"arrow",options:{element:h,padding:3}}),m.push.apply(m,(null==n?void 0:n.modifiers)||[]),C.popperInstance=St(f,E,Object.assign({},n,{placement:r,onFirstUpdate:d,modifiers:m}))}function at(){C.popperInstance&&(C.popperInstance.destroy(),C.popperInstance=null)}function lt(){return $t(E.querySelectorAll("[data-tippy-root]"))}function ct(t){C.clearDelayTimeouts(),t&&H("onTrigger",[C,t]),U();var e=$(!0),r=P(),o=r[0],l=r[1];Gt.isTouch&&"hold"===o&&l&&(e=l),e?n=setTimeout((function(){C.show()}),e):C.show()}function ht(t){if(C.clearDelayTimeouts(),H("onUntrigger",[C,t]),C.state.isVisible){if(!(C.props.trigger.indexOf("mouseenter")>=0&&C.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(t.type)>=0&&y)){var e=$(!1);e?r=setTimeout((function(){C.state.isVisible&&C.hide()}),e):o=requestAnimationFrame((function(){C.hide()}))}}else G()}}function me(t,e){void 0===e&&(e={});var n=ne.plugins.concat(e.plugins||[]);document.addEventListener("touchstart",Xt,At),window.addEventListener("blur",Zt);var r=Object.assign({},e,{plugins:n}),o=_t(t).reduce((function(t,e){var n=e&&fe(e,r);return n&&t.push(n),t}),[]);return Ht(t)?o[0]:o}me.defaultProps=ne,me.setDefaultProps=function(t){Object.keys(t).forEach((function(e){ne[e]=t[e]}))},me.currentInput=Gt;Object.assign({},lt,{effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow)}});me.setDefaultProps({render:de});var ge=me;class ye{constructor({editor:t,element:element,view:view,tippyOptions:e={},updateDelay:n=250,shouldShow:o}){this.preventHide=!1,this.shouldShow=({view:view,state:t,from:e,to:n})=>{const{doc:o,selection:l}=t,{empty:c}=l,d=!o.textBetween(e,n).length&&Object(r.q)(t.selection),h=this.element.contains(document.activeElement);return!(!view.hasFocus()&&!h||c||d||!this.editor.isEditable)},this.mousedownHandler=()=>{this.preventHide=!0},this.dragstartHandler=()=>{this.hide()},this.focusHandler=()=>{setTimeout((()=>this.update(this.editor.view)))},this.blurHandler=({event:t})=>{var e;this.preventHide?this.preventHide=!1:(null==t?void 0:t.relatedTarget)&&(null===(e=this.element.parentNode)||void 0===e?void 0:e.contains(t.relatedTarget))||(null==t?void 0:t.relatedTarget)!==this.editor.view.dom&&this.hide()},this.tippyBlurHandler=t=>{this.blurHandler({event:t})},this.handleDebouncedUpdate=(view,t)=>{const e=!(null==t?void 0:t.selection.eq(view.state.selection)),n=!(null==t?void 0:t.doc.eq(view.state.doc));(e||n)&&(this.updateDebounceTimer&&clearTimeout(this.updateDebounceTimer),this.updateDebounceTimer=window.setTimeout((()=>{this.updateHandler(view,e,n,t)}),this.updateDelay))},this.updateHandler=(view,t,e,n)=>{var o,l,c;const{state:d,composing:h}=view,{selection:f}=d;if(h||!t&&!e)return;this.createTooltip();const{ranges:m}=f,y=Math.min(...m.map((t=>t.$from.pos))),v=Math.max(...m.map((t=>t.$to.pos)));(null===(o=this.shouldShow)||void 0===o?void 0:o.call(this,{editor:this.editor,element:this.element,view:view,state:d,oldState:n,from:y,to:v}))?(null===(l=this.tippy)||void 0===l||l.setProps({getReferenceClientRect:(null===(c=this.tippyOptions)||void 0===c?void 0:c.getReferenceClientRect)||(()=>{if(Object(r.p)(d.selection)){let t=view.nodeDOM(y);if(t){const e=t.dataset.nodeViewWrapper?t:t.querySelector("[data-node-view-wrapper]");if(e&&(t=e.firstChild),t)return t.getBoundingClientRect()}}return Object(r.v)(view,y,v)})}),this.show()):this.hide()},this.editor=t,this.element=element,this.view=view,this.updateDelay=n,o&&(this.shouldShow=o),this.element.addEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.view.dom.addEventListener("dragstart",this.dragstartHandler),this.editor.on("focus",this.focusHandler),this.editor.on("blur",this.blurHandler),this.tippyOptions=e,this.element.remove(),this.element.style.visibility="visible"}createTooltip(){const{element:t}=this.editor.options,e=!!t.parentElement;!this.tippy&&e&&(this.tippy=ge(t,{duration:0,getReferenceClientRect:null,content:this.element,interactive:!0,trigger:"manual",placement:"top",hideOnClick:"toggle",...this.tippyOptions}),this.tippy.popper.firstChild&&this.tippy.popper.firstChild.addEventListener("blur",this.tippyBlurHandler))}update(view,t){const{state:e}=view,n=e.selection.from!==e.selection.to;if(this.updateDelay>0&&n)return void this.handleDebouncedUpdate(view,t);const r=!(null==t?void 0:t.selection.eq(view.state.selection)),o=!(null==t?void 0:t.doc.eq(view.state.doc));this.updateHandler(view,r,o,t)}show(){var t;null===(t=this.tippy)||void 0===t||t.show()}hide(){var t;null===(t=this.tippy)||void 0===t||t.hide()}destroy(){var t,e;(null===(t=this.tippy)||void 0===t?void 0:t.popper.firstChild)&&this.tippy.popper.firstChild.removeEventListener("blur",this.tippyBlurHandler),null===(e=this.tippy)||void 0===e||e.destroy(),this.element.removeEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.view.dom.removeEventListener("dragstart",this.dragstartHandler),this.editor.off("focus",this.focusHandler),this.editor.off("blur",this.blurHandler)}}const ve=t=>new o.d({key:"string"==typeof t.pluginKey?new o.e(t.pluginKey):t.pluginKey,view:view=>new ye({view:view,...t})});r.b.create({name:"bubbleMenu",addOptions:()=>({element:null,tippyOptions:{},pluginKey:"bubbleMenu",updateDelay:void 0,shouldShow:null}),addProseMirrorPlugins(){return this.options.element?[ve({pluginKey:this.options.pluginKey,editor:this.editor,element:this.options.element,tippyOptions:this.options.tippyOptions,updateDelay:this.options.updateDelay,shouldShow:this.options.shouldShow})]:[]}});class be{getTextContent(t){return Object(r.n)(t,{textSerializers:Object(r.o)(this.editor.schema)})}constructor({editor:t,element:element,view:view,tippyOptions:e={},shouldShow:n}){this.preventHide=!1,this.shouldShow=({view:view,state:t})=>{const{selection:e}=t,{$anchor:n,empty:r}=e,o=1===n.depth,l=n.parent.isTextblock&&!n.parent.type.spec.code&&!n.parent.textContent&&0===n.parent.childCount&&!this.getTextContent(n.parent);return!!(view.hasFocus()&&r&&o&&l&&this.editor.isEditable)},this.mousedownHandler=()=>{this.preventHide=!0},this.focusHandler=()=>{setTimeout((()=>this.update(this.editor.view)))},this.blurHandler=({event:t})=>{var e;this.preventHide?this.preventHide=!1:(null==t?void 0:t.relatedTarget)&&(null===(e=this.element.parentNode)||void 0===e?void 0:e.contains(t.relatedTarget))||(null==t?void 0:t.relatedTarget)!==this.editor.view.dom&&this.hide()},this.tippyBlurHandler=t=>{this.blurHandler({event:t})},this.editor=t,this.element=element,this.view=view,n&&(this.shouldShow=n),this.element.addEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.editor.on("focus",this.focusHandler),this.editor.on("blur",this.blurHandler),this.tippyOptions=e,this.element.remove(),this.element.style.visibility="visible"}createTooltip(){const{element:t}=this.editor.options,e=!!t.parentElement;!this.tippy&&e&&(this.tippy=ge(t,{duration:0,getReferenceClientRect:null,content:this.element,interactive:!0,trigger:"manual",placement:"right",hideOnClick:"toggle",...this.tippyOptions}),this.tippy.popper.firstChild&&this.tippy.popper.firstChild.addEventListener("blur",this.tippyBlurHandler))}update(view,t){var e,n,o;const{state:l}=view,{doc:c,selection:d}=l,{from:h,to:f}=d;if(t&&t.doc.eq(c)&&t.selection.eq(d))return;this.createTooltip();(null===(e=this.shouldShow)||void 0===e?void 0:e.call(this,{editor:this.editor,view:view,state:l,oldState:t}))?(null===(n=this.tippy)||void 0===n||n.setProps({getReferenceClientRect:(null===(o=this.tippyOptions)||void 0===o?void 0:o.getReferenceClientRect)||(()=>Object(r.v)(view,h,f))}),this.show()):this.hide()}show(){var t;null===(t=this.tippy)||void 0===t||t.show()}hide(){var t;null===(t=this.tippy)||void 0===t||t.hide()}destroy(){var t,e;(null===(t=this.tippy)||void 0===t?void 0:t.popper.firstChild)&&this.tippy.popper.firstChild.removeEventListener("blur",this.tippyBlurHandler),null===(e=this.tippy)||void 0===e||e.destroy(),this.element.removeEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.editor.off("focus",this.focusHandler),this.editor.off("blur",this.blurHandler)}}const we=t=>new o.d({key:"string"==typeof t.pluginKey?new o.e(t.pluginKey):t.pluginKey,view:view=>new be({view:view,...t})});r.b.create({name:"floatingMenu",addOptions:()=>({element:null,tippyOptions:{},pluginKey:"floatingMenu",shouldShow:null}),addProseMirrorPlugins(){return this.options.element?[we({pluginKey:this.options.pluginKey,editor:this.editor,element:this.options.element,tippyOptions:this.options.tippyOptions,shouldShow:this.options.shouldShow})]:[]}});n(3);var ke=n(2015);class xe extends r.a{constructor(){super(...arguments),this.contentComponent=null}}const Oe={name:"EditorContent",props:{editor:{default:null,type:Object}},watch:{editor:{immediate:!0,handler(t){t&&t.options.element&&this.$nextTick((()=>{const element=this.$el;element&&t.options.element.firstChild&&(element.append(...t.options.element.childNodes),t.contentComponent=this,t.setOptions({element:element}),t.createNodeViews())}))}}},render:t=>t("div"),beforeDestroy(){const{editor:t}=this;if(!t)return;if(t.isDestroyed||t.view.setProps({nodeViews:{}}),t.contentComponent=null,!t.options.element.firstChild)return;const e=document.createElement("div");e.append(...t.options.element.childNodes),t.setOptions({element:e})}};Object(ke.objectProp)().required,Object(ke.objectProp)().required,Object(ke.objectProp)().required,Object(ke.booleanProp)().required,Object(ke.objectProp)().required,Object(ke.functionProp)().required,Object(ke.functionProp)().required,Object(ke.functionProp)().required;r.e},1587:function(t,e,n){"use strict";n.d(e,"a",(function(){return Pt}));var r=n(1426);const o=/^\s*>\s$/,l=r.d.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:t}){return["blockquote",Object(r.t)(this.options.HTMLAttributes,t),0]},addCommands(){return{setBlockquote:()=>({commands:t})=>t.wrapIn(this.name),toggleBlockquote:()=>({commands:t})=>t.toggleWrap(this.name),unsetBlockquote:()=>({commands:t})=>t.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[Object(r.x)({find:o,type:this.type})]}}),c=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,d=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,h=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,f=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,m=r.c.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:t=>"normal"!==t.style.fontWeight&&null},{style:"font-weight=400",clearMark:mark=>mark.type.name===this.name},{style:"font-weight",getAttrs:t=>/^(bold(er)?|[5-9]\d{2,})$/.test(t)&&null}]},renderHTML({HTMLAttributes:t}){return["strong",Object(r.t)(this.options.HTMLAttributes,t),0]},addCommands(){return{setBold:()=>({commands:t})=>t.setMark(this.name),toggleBold:()=>({commands:t})=>t.toggleMark(this.name),unsetBold:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[Object(r.r)({find:c,type:this.type}),Object(r.r)({find:h,type:this.type})]},addPasteRules(){return[Object(r.s)({find:d,type:this.type}),Object(r.s)({find:f,type:this.type})]}}),y="textStyle",v=/^\s*([-+*])\s$/,w=r.d.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:t}){return["ul",Object(r.t)(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleBulletList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(y)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let t=Object(r.x)({find:v,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(t=Object(r.x)({find:v,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(y),editor:this.editor})),[t]}}),k=/(^|[^`])`([^`]+)`(?!`)/,x=/(^|[^`])`([^`]+)`(?!`)/g,O=r.c.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:t}){return["code",Object(r.t)(this.options.HTMLAttributes,t),0]},addCommands(){return{setCode:()=>({commands:t})=>t.setMark(this.name),toggleCode:()=>({commands:t})=>t.toggleMark(this.name),unsetCode:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[Object(r.r)({find:k,type:this.type})]},addPasteRules(){return[Object(r.s)({find:x,type:this.type})]}});var S=n(1599);const M=/^```([a-z]+)?[\s\n]$/,C=/^~~~([a-z]+)?[\s\n]$/,T=r.d.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:element=>{var t;const{languageClassPrefix:e}=this.options,n=[...(null===(t=element.firstElementChild)||void 0===t?void 0:t.classList)||[]].filter((t=>t.startsWith(e))).map((t=>t.replace(e,"")))[0];return n||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:t,HTMLAttributes:e}){return["pre",Object(r.t)(this.options.HTMLAttributes,e),["code",{class:t.attrs.language?this.options.languageClassPrefix+t.attrs.language:null},0]]},addCommands(){return{setCodeBlock:t=>({commands:e})=>e.setNode(this.name,t),toggleCodeBlock:t=>({commands:e})=>e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{const{empty:t,$anchor:e}=this.editor.state.selection,n=1===e.pos;return!(!t||e.parent.type.name!==this.name)&&(!(!n&&e.parent.textContent.length)&&this.editor.commands.clearNodes())},Enter:({editor:t})=>{if(!this.options.exitOnTripleEnter)return!1;const{state:e}=t,{selection:n}=e,{$from:r,empty:o}=n;if(!o||r.parent.type!==this.type)return!1;const l=r.parentOffset===r.parent.nodeSize-2,c=r.parent.textContent.endsWith("\n\n");return!(!l||!c)&&t.chain().command((({tr:tr})=>(tr.delete(r.pos-2,r.pos),!0))).exitCode().run()},ArrowDown:({editor:t})=>{if(!this.options.exitOnArrowDown)return!1;const{state:e}=t,{selection:n,doc:r}=e,{$from:o,empty:l}=n;if(!l||o.parent.type!==this.type)return!1;if(!(o.parentOffset===o.parent.nodeSize-2))return!1;const c=o.after();if(void 0===c)return!1;return r.nodeAt(c)?t.commands.command((({tr:tr})=>(tr.setSelection(S.f.near(r.resolve(c))),!0))):t.commands.exitCode()}}},addInputRules(){return[Object(r.w)({find:M,type:this.type,getAttributes:t=>({language:t[1]})}),Object(r.w)({find:C,type:this.type,getAttributes:t=>({language:t[1]})})]},addProseMirrorPlugins(){return[new S.d({key:new S.e("codeBlockVSCodeHandler"),props:{handlePaste:(view,t)=>{if(!t.clipboardData)return!1;if(this.editor.isActive(this.type.name))return!1;const text=t.clipboardData.getData("text/plain"),e=t.clipboardData.getData("vscode-editor-data"),n=e?JSON.parse(e):void 0,r=null==n?void 0:n.mode;if(!text||!r)return!1;const{tr:tr,schema:o}=view.state,l=o.text(text.replace(/\r\n?/g,"\n"));return tr.replaceSelectionWith(this.type.create({language:r},l)),tr.selection.$from.parent.type!==this.type&&tr.setSelection(S.g.near(tr.doc.resolve(Math.max(0,tr.selection.from-2)))),tr.setMeta("paste",!0),view.dispatch(tr),!0}}})]}}),E=r.d.create({name:"doc",topNode:!0,content:"block+"});var A=n(1569),N=n(1576);function D(t={}){return new A.d({view:e=>new P(e,t)})}class P{constructor(t,e){var n;this.editorView=t,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!==(n=e.width)&&void 0!==n?n:1,this.color=!1===e.color?void 0:e.color||"black",this.class=e.class,this.handlers=["dragover","dragend","drop","dragleave"].map((e=>{let n=t=>{this[e](t)};return t.dom.addEventListener(e,n),{name:e,handler:n}}))}destroy(){this.handlers.forEach((({name:t,handler:e})=>this.editorView.dom.removeEventListener(t,e)))}update(t,e){null!=this.cursorPos&&e.doc!=t.state.doc&&(this.cursorPos>t.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(t){t!=this.cursorPos&&(this.cursorPos=t,null==t?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let rect,t=this.editorView.state.doc.resolve(this.cursorPos),e=!t.parent.inlineContent,n=this.editorView.dom,r=n.getBoundingClientRect(),o=r.width/n.offsetWidth,l=r.height/n.offsetHeight;if(e){let e=t.nodeBefore,n=t.nodeAfter;if(e||n){let t=this.editorView.nodeDOM(this.cursorPos-(e?e.nodeSize:0));if(t){let r=t.getBoundingClientRect(),o=e?r.bottom:r.top;e&&n&&(o=(o+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let c=this.width/2*l;rect={left:r.left,right:r.right,top:o-c,bottom:o+c}}}}if(!rect){let t=this.editorView.coordsAtPos(this.cursorPos),e=this.width/2*o;rect={left:t.left-e,right:t.left+e,top:t.top,bottom:t.bottom}}let c,d,h=this.editorView.dom.offsetParent;if(this.element||(this.element=h.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",e),this.element.classList.toggle("prosemirror-dropcursor-inline",!e),!h||h==document.body&&"static"==getComputedStyle(h).position)c=-pageXOffset,d=-pageYOffset;else{let rect=h.getBoundingClientRect(),t=rect.width/h.offsetWidth,e=rect.height/h.offsetHeight;c=rect.left-h.scrollLeft*t,d=rect.top-h.scrollTop*e}this.element.style.left=(rect.left-c)/o+"px",this.element.style.top=(rect.top-d)/l+"px",this.element.style.width=(rect.right-rect.left)/o+"px",this.element.style.height=(rect.bottom-rect.top)/l+"px"}scheduleRemoval(t){clearTimeout(this.timeout),this.timeout=setTimeout((()=>this.setCursor(null)),t)}dragover(t){if(!this.editorView.editable)return;let e=this.editorView.posAtCoords({left:t.clientX,top:t.clientY}),n=e&&e.inside>=0&&this.editorView.state.doc.nodeAt(e.inside),r=n&&n.type.spec.disableDropCursor,o="function"==typeof r?r(this.editorView,e,t):r;if(e&&!o){let t=e.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let e=Object(N.g)(this.editorView.state.doc,t,this.editorView.dragging.slice);null!=e&&(t=e)}this.setCursor(t),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(t){this.editorView.dom.contains(t.relatedTarget)||this.setCursor(null)}}const R=r.b.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[D(this.options)]}});var j=n(1827),I=n(1590),L=n(1728);class B extends A.f{constructor(t){super(t,t)}map(t,e){let n=t.resolve(e.map(this.head));return B.valid(n)?new B(n):A.f.near(n)}content(){return I.j.empty}eq(t){return t instanceof B&&t.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(t,e){if("number"!=typeof e.pos)throw new RangeError("Invalid input for GapCursor.fromJSON");return new B(t.resolve(e.pos))}getBookmark(){return new $(this.anchor)}static valid(t){let e=t.parent;if(e.isTextblock||!function(t){for(let e=t.depth;e>=0;e--){let n=t.index(e),r=t.node(e);if(0!=n)for(let t=r.child(n-1);;t=t.lastChild){if(0==t.childCount&&!t.inlineContent||t.isAtom||t.type.spec.isolating)return!0;if(t.inlineContent)return!1}else if(r.type.spec.isolating)return!0}return!0}(t)||!function(t){for(let e=t.depth;e>=0;e--){let n=t.indexAfter(e),r=t.node(e);if(n!=r.childCount)for(let t=r.child(n);;t=t.firstChild){if(0==t.childCount&&!t.inlineContent||t.isAtom||t.type.spec.isolating)return!0;if(t.inlineContent)return!1}else if(r.type.spec.isolating)return!0}return!0}(t))return!1;let n=e.type.spec.allowGapCursor;if(null!=n)return n;let r=e.contentMatchAt(t.index()).defaultType;return r&&r.isTextblock}static findGapCursorFrom(t,e,n=!1){t:for(;;){if(!n&&B.valid(t))return t;let r=t.pos,o=null;for(let n=t.depth;;n--){let l=t.node(n);if(e>0?t.indexAfter(n)<l.childCount:t.index(n)>0){o=l.child(e>0?t.indexAfter(n):t.index(n)-1);break}if(0==n)return null;r+=e;let c=t.doc.resolve(r);if(B.valid(c))return c}for(;;){let l=e>0?o.firstChild:o.lastChild;if(!l){if(o.isAtom&&!o.isText&&!A.c.isSelectable(o)){t=t.doc.resolve(r+o.nodeSize*e),n=!1;continue t}break}o=l,r+=e;let c=t.doc.resolve(r);if(B.valid(c))return c}return null}}}B.prototype.visible=!1,B.findFrom=B.findGapCursorFrom,A.f.jsonID("gapcursor",B);class ${constructor(t){this.pos=t}map(t){return new $(t.map(this.pos))}resolve(t){let e=t.resolve(this.pos);return B.valid(e)?new B(e):A.f.near(e)}}const z=Object(j.a)({ArrowLeft:H("horiz",-1),ArrowRight:H("horiz",1),ArrowUp:H("vert",-1),ArrowDown:H("vert",1)});function H(t,e){const n="vert"==t?e>0?"down":"up":e>0?"right":"left";return function(t,r,view){let o=t.selection,l=e>0?o.$to:o.$from,c=o.empty;if(o instanceof A.h){if(!view.endOfTextblock(n)||0==l.depth)return!1;c=!1,l=t.doc.resolve(e>0?l.after():l.before())}let d=B.findGapCursorFrom(l,e,c);return!!d&&(r&&r(t.tr.setSelection(new B(d))),!0)}}function V(view,t,e){if(!view||!view.editable)return!1;let n=view.state.doc.resolve(t);if(!B.valid(n))return!1;let r=view.posAtCoords({left:e.clientX,top:e.clientY});return!(r&&r.inside>-1&&A.c.isSelectable(view.state.doc.nodeAt(r.inside)))&&(view.dispatch(view.state.tr.setSelection(new B(n))),!0)}function F(view,t){if("insertCompositionText"!=t.inputType||!(view.state.selection instanceof B))return!1;let{$from:e}=view.state.selection,n=e.parent.contentMatchAt(e.index()).findWrapping(view.state.schema.nodes.text);if(!n)return!1;let r=I.c.empty;for(let i=n.length-1;i>=0;i--)r=I.c.from(n[i].createAndFill(null,r));let tr=view.state.tr.replace(e.pos,e.pos,new I.j(r,0,0));return tr.setSelection(A.h.near(tr.doc.resolve(e.pos+1))),view.dispatch(tr),!1}function _(t){if(!(t.selection instanceof B))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",L.b.create(t.doc,[L.a.widget(t.selection.head,e,{key:"gapcursor"})])}const W=r.b.create({name:"gapCursor",addProseMirrorPlugins:()=>[new A.d({props:{decorations:_,createSelectionBetween:(t,e,n)=>e.pos==n.pos&&B.valid(n)?new B(n):null,handleClick:V,handleKeyDown:z,handleDOMEvents:{beforeinput:F}}})],extendNodeSchema(t){var e;const n={name:t.name,options:t.options,storage:t.storage};return{allowGapCursor:null!==(e=Object(r.f)(Object(r.l)(t,"allowGapCursor",n)))&&void 0!==e?e:null}}}),K=r.d.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:t}){return["br",Object(r.t)(this.options.HTMLAttributes,t)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:t,chain:e,state:n,editor:r})=>t.first([()=>t.exitCode(),()=>t.command((()=>{const{selection:t,storedMarks:o}=n;if(t.$from.parent.type.spec.isolating)return!1;const{keepMarks:l}=this.options,{splittableMarks:c}=r.extensionManager,d=o||t.$to.parentOffset&&t.$from.marks();return e().insertContent({type:this.name}).command((({tr:tr,dispatch:t})=>{if(t&&d&&l){const t=d.filter((mark=>c.includes(mark.type.name)));tr.ensureMarks(t)}return!0})).run()}))])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),J=r.d.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map((t=>({tag:`h${t}`,attrs:{level:t}})))},renderHTML({node:t,HTMLAttributes:e}){return[`h${this.options.levels.includes(t.attrs.level)?t.attrs.level:this.options.levels[0]}`,Object(r.t)(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.setNode(this.name,t),toggleHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return this.options.levels.reduce(((t,e)=>({...t,[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})})),{})},addInputRules(){return this.options.levels.map((t=>Object(r.w)({find:new RegExp(`^(#{${Math.min(...this.options.levels)},${t}})\\s$`),type:this.type,getAttributes:{level:t}})))}});var U=200,G=function(){};G.prototype.append=function(t){return t.length?(t=G.from(t),!this.length&&t||t.length<U&&this.leafAppend(t)||this.length<U&&t.leafPrepend(this)||this.appendInner(t)):this},G.prototype.prepend=function(t){return t.length?G.from(t).append(this):this},G.prototype.appendInner=function(t){return new X(this,t)},G.prototype.slice=function(t,e){return void 0===t&&(t=0),void 0===e&&(e=this.length),t>=e?G.empty:this.sliceInner(Math.max(0,t),Math.min(this.length,e))},G.prototype.get=function(i){if(!(i<0||i>=this.length))return this.getInner(i)},G.prototype.forEach=function(t,e,n){void 0===e&&(e=0),void 0===n&&(n=this.length),e<=n?this.forEachInner(t,e,n,0):this.forEachInvertedInner(t,e,n,0)},G.prototype.map=function(t,e,n){void 0===e&&(e=0),void 0===n&&(n=this.length);var r=[];return this.forEach((function(e,i){return r.push(t(e,i))}),e,n),r},G.from=function(t){return t instanceof G?t:t&&t.length?new Y(t):G.empty};var Y=function(t){function e(e){t.call(this),this.values=e}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var n={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(t,n){return 0==t&&n==this.length?this:new e(this.values.slice(t,n))},e.prototype.getInner=function(i){return this.values[i]},e.prototype.forEachInner=function(t,e,n,r){for(var i=e;i<n;i++)if(!1===t(this.values[i],r+i))return!1},e.prototype.forEachInvertedInner=function(t,e,n,r){for(var i=e-1;i>=n;i--)if(!1===t(this.values[i],r+i))return!1},e.prototype.leafAppend=function(t){if(this.length+t.length<=U)return new e(this.values.concat(t.flatten()))},e.prototype.leafPrepend=function(t){if(this.length+t.length<=U)return new e(t.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(e.prototype,n),e}(G);G.empty=new Y([]);var X=function(t){function e(e,n){t.call(this),this.left=e,this.right=n,this.length=e.length+n.length,this.depth=Math.max(e.depth,n.depth)+1}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(i){return i<this.left.length?this.left.get(i):this.right.get(i-this.left.length)},e.prototype.forEachInner=function(t,e,n,r){var o=this.left.length;return!(e<o&&!1===this.left.forEachInner(t,e,Math.min(n,o),r))&&(!(n>o&&!1===this.right.forEachInner(t,Math.max(e-o,0),Math.min(this.length,n)-o,r+o))&&void 0)},e.prototype.forEachInvertedInner=function(t,e,n,r){var o=this.left.length;return!(e>o&&!1===this.right.forEachInvertedInner(t,e-o,Math.max(n,o)-o,r+o))&&(!(n<o&&!1===this.left.forEachInvertedInner(t,Math.min(e,o),n,r))&&void 0)},e.prototype.sliceInner=function(t,e){if(0==t&&e==this.length)return this;var n=this.left.length;return e<=n?this.left.slice(t,e):t>=n?this.right.slice(t-n,e-n):this.left.slice(t,n).append(this.right.slice(0,e-n))},e.prototype.leafAppend=function(t){var n=this.right.leafAppend(t);if(n)return new e(this.left,n)},e.prototype.leafPrepend=function(t){var n=this.left.leafPrepend(t);if(n)return new e(n,this.right)},e.prototype.appendInner=function(t){return this.left.depth>=Math.max(this.right.depth,t.depth)+1?new e(this.left,new e(this.right,t)):new e(this,t)},e}(G),Q=G;class Z{constructor(t,e){this.items=t,this.eventCount=e}popEvent(t,e){if(0==this.eventCount)return null;let n,r,o=this.items.length;for(;;o--){if(this.items.get(o-1).selection){--o;break}}e&&(n=this.remapping(o,this.items.length),r=n.maps.length);let l,c,d=t.tr,h=[],f=[];return this.items.forEach(((t,i)=>{if(!t.step)return n||(n=this.remapping(o,i+1),r=n.maps.length),r--,void f.push(t);if(n){f.push(new tt(t.map));let map,e=t.step.map(n.slice(r));e&&d.maybeStep(e).doc&&(map=d.mapping.maps[d.mapping.maps.length-1],h.push(new tt(map,void 0,void 0,h.length+f.length))),r--,map&&n.appendMap(map,r)}else d.maybeStep(t.step);return t.selection?(l=n?t.selection.map(n.slice(r)):t.selection,c=new Z(this.items.slice(0,o).append(f.reverse().concat(h)),this.eventCount-1),!1):void 0}),this.items.length,0),{remaining:c,transform:d,selection:l}}addTransform(t,e,n,r){let o=[],l=this.eventCount,c=this.items,d=!r&&c.length?c.get(c.length-1):null;for(let i=0;i<t.steps.length;i++){let n,h=t.steps[i].invert(t.docs[i]),f=new tt(t.mapping.maps[i],h,e);(n=d&&d.merge(f))&&(f=n,i?o.pop():c=c.slice(0,c.length-1)),o.push(f),e&&(l++,e=void 0),r||(d=f)}let h=l-n.depth;return h>nt&&(c=function(t,e){let n;return t.forEach(((t,i)=>{if(t.selection&&0==e--)return n=i,!1})),t.slice(n)}(c,h),l-=h),new Z(c.append(o),l)}remapping(t,e){let n=new N.a;return this.items.forEach(((e,i)=>{let r=null!=e.mirrorOffset&&i-e.mirrorOffset>=t?n.maps.length-e.mirrorOffset:void 0;n.appendMap(e.map,r)}),t,e),n}addMaps(t){return 0==this.eventCount?this:new Z(this.items.append(t.map((map=>new tt(map)))),this.eventCount)}rebased(t,e){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-e),o=t.mapping,l=t.steps.length,c=this.eventCount;this.items.forEach((t=>{t.selection&&c--}),r);let d=e;this.items.forEach((e=>{let r=o.getMirror(--d);if(null==r)return;l=Math.min(l,r);let map=o.maps[r];if(e.step){let l=t.steps[r].invert(t.docs[r]),h=e.selection&&e.selection.map(o.slice(d+1,r));h&&c++,n.push(new tt(map,l,h))}else n.push(new tt(map))}),r);let h=[];for(let i=e;i<l;i++)h.push(new tt(o.maps[i]));let f=this.items.slice(0,r).append(h).append(n),m=new Z(f,c);return m.emptyItemCount()>500&&(m=m.compress(this.items.length-n.length)),m}emptyItemCount(){let t=0;return this.items.forEach((e=>{e.step||t++})),t}compress(t=this.items.length){let e=this.remapping(0,t),n=e.maps.length,r=[],o=0;return this.items.forEach(((l,i)=>{if(i>=t)r.push(l),l.selection&&o++;else if(l.step){let t=l.step.map(e.slice(n)),map=t&&t.getMap();if(n--,map&&e.appendMap(map,n),t){let c=l.selection&&l.selection.map(e.slice(n));c&&o++;let d,h=new tt(map.invert(),t,c),f=r.length-1;(d=r.length&&r[f].merge(h))?r[f]=d:r.push(h)}}else l.map&&n--}),this.items.length,0),new Z(Q.from(r.reverse()),o)}}Z.empty=new Z(Q.empty,0);class tt{constructor(map,t,e,n){this.map=map,this.step=t,this.selection=e,this.mirrorOffset=n}merge(t){if(this.step&&t.step&&!t.selection){let e=t.step.merge(this.step);if(e)return new tt(e.getMap().invert(),e,this.selection)}}}class et{constructor(t,e,n,r,o){this.done=t,this.undone=e,this.prevRanges=n,this.prevTime=r,this.prevComposition=o}}const nt=20;function ot(t){let e=[];for(let i=t.length-1;i>=0&&0==e.length;i--)t[i].forEach(((t,n,r,o)=>e.push(r,o)));return e}function it(t,e){if(!t)return null;let n=[];for(let i=0;i<t.length;i+=2){let r=e.map(t[i],1),o=e.map(t[i+1],-1);r<=o&&n.push(r,o)}return n}let st=!1,at=null;function lt(t){let e=t.plugins;if(at!=e){st=!1,at=e;for(let i=0;i<e.length;i++)if(e[i].spec.historyPreserveItems){st=!0;break}}return st}const ct=new A.e("history"),ht=new A.e("closeHistory");function pt(t={}){return t={depth:t.depth||100,newGroupDelay:t.newGroupDelay||500},new A.d({key:ct,state:{init:()=>new et(Z.empty,Z.empty,null,0,-1),apply:(tr,e,n)=>function(t,e,tr,n){let r,o=tr.getMeta(ct);if(o)return o.historyState;tr.getMeta(ht)&&(t=new et(t.done,t.undone,null,0,-1));let l=tr.getMeta("appendedTransaction");if(0==tr.steps.length)return t;if(l&&l.getMeta(ct))return l.getMeta(ct).redo?new et(t.done.addTransform(tr,void 0,n,lt(e)),t.undone,ot(tr.mapping.maps),t.prevTime,t.prevComposition):new et(t.done,t.undone.addTransform(tr,void 0,n,lt(e)),null,t.prevTime,t.prevComposition);if(!1===tr.getMeta("addToHistory")||l&&!1===l.getMeta("addToHistory"))return(r=tr.getMeta("rebased"))?new et(t.done.rebased(tr,r),t.undone.rebased(tr,r),it(t.prevRanges,tr.mapping),t.prevTime,t.prevComposition):new et(t.done.addMaps(tr.mapping.maps),t.undone.addMaps(tr.mapping.maps),it(t.prevRanges,tr.mapping),t.prevTime,t.prevComposition);{let r=tr.getMeta("composition"),o=0==t.prevTime||!l&&t.prevComposition!=r&&(t.prevTime<(tr.time||0)-n.newGroupDelay||!function(t,e){if(!e)return!1;if(!t.docChanged)return!0;let n=!1;return t.mapping.maps[0].forEach(((t,r)=>{for(let i=0;i<e.length;i+=2)t<=e[i+1]&&r>=e[i]&&(n=!0)})),n}(tr,t.prevRanges)),c=l?it(t.prevRanges,tr.mapping):ot(tr.mapping.maps);return new et(t.done.addTransform(tr,o?e.selection.getBookmark():void 0,n,lt(e)),Z.empty,c,tr.time,null==r?t.prevComposition:r)}}(e,n,tr,t)},config:t,props:{handleDOMEvents:{beforeinput(view,t){let e=t.inputType,n="historyUndo"==e?ft:"historyRedo"==e?mt:null;return!!n&&(t.preventDefault(),n(view.state,view.dispatch))}}}})}function ut(t,e){return(n,r)=>{let o=ct.getState(n);if(!o||0==(t?o.undone:o.done).eventCount)return!1;if(r){let tr=function(t,e,n){let r=lt(e),o=ct.get(e).spec.config,l=(n?t.undone:t.done).popEvent(e,r);if(!l)return null;let c=l.selection.resolve(l.transform.doc),d=(n?t.done:t.undone).addTransform(l.transform,e.selection.getBookmark(),o,r),h=new et(n?d:l.remaining,n?l.remaining:d,null,0,-1);return l.transform.setSelection(c).setMeta(ct,{redo:n,historyState:h})}(o,n,t);tr&&r(e?tr.scrollIntoView():tr)}return!0}}const ft=ut(!1,!0),mt=ut(!0,!0);ut(!1,!1),ut(!0,!1);const gt=r.b.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:t,dispatch:e})=>ft(t,e),redo:()=>({state:t,dispatch:e})=>mt(t,e)}),addProseMirrorPlugins(){return[pt(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),yt=r.d.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:t}){return["hr",Object(r.t)(this.options.HTMLAttributes,t)]},addCommands(){return{setHorizontalRule:()=>({chain:t,state:e})=>{if(!Object(r.g)(e,e.schema.nodes[this.name]))return!1;const{selection:n}=e,{$from:o,$to:l}=n,c=t();return 0===o.parentOffset?c.insertContentAt({from:Math.max(o.pos-1,0),to:l.pos},{type:this.name}):Object(r.p)(n)?c.insertContentAt(l.pos,{type:this.name}):c.insertContent({type:this.name}),c.command((({tr:tr,dispatch:t})=>{var e;if(t){const{$to:t}=tr.selection,n=t.end();if(t.nodeAfter)t.nodeAfter.isTextblock?tr.setSelection(S.g.create(tr.doc,t.pos+1)):t.nodeAfter.isBlock?tr.setSelection(S.c.create(tr.doc,t.pos)):tr.setSelection(S.g.create(tr.doc,t.pos));else{const r=null===(e=t.parent.type.contentMatch.defaultType)||void 0===e?void 0:e.create();r&&(tr.insert(n,r),tr.setSelection(S.g.create(tr.doc,n+1)))}tr.scrollIntoView()}return!0})).run()}}},addInputRules(){return[Object(r.u)({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),vt=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,bt=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,wt=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,kt=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,xt=r.c.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:t=>"normal"!==t.style.fontStyle&&null},{style:"font-style=normal",clearMark:mark=>mark.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:t}){return["em",Object(r.t)(this.options.HTMLAttributes,t),0]},addCommands(){return{setItalic:()=>({commands:t})=>t.setMark(this.name),toggleItalic:()=>({commands:t})=>t.toggleMark(this.name),unsetItalic:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[Object(r.r)({find:vt,type:this.type}),Object(r.r)({find:wt,type:this.type})]},addPasteRules(){return[Object(r.s)({find:bt,type:this.type}),Object(r.s)({find:kt,type:this.type})]}}),Ot=r.d.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:t}){return["li",Object(r.t)(this.options.HTMLAttributes,t),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),St="textStyle",Mt=/^(\d+)\.\s$/,Ct=r.d.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:element=>element.hasAttribute("start")?parseInt(element.getAttribute("start")||"",10):1},type:{default:null,parseHTML:element=>element.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:t}){const{start:e,...n}=t;return 1===e?["ol",Object(r.t)(this.options.HTMLAttributes,n),0]:["ol",Object(r.t)(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleOrderedList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(St)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let t=Object(r.x)({find:Mt,type:this.type,getAttributes:t=>({start:+t[1]}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(t=Object(r.x)({find:Mt,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:t=>({start:+t[1],...this.editor.getAttributes(St)}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1],editor:this.editor})),[t]}}),Tt=r.d.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:t}){return["p",Object(r.t)(this.options.HTMLAttributes,t),0]},addCommands(){return{setParagraph:()=>({commands:t})=>t.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),Et=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,At=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,Nt=r.c.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:style=>!!style.includes("line-through")&&{}}],renderHTML({HTMLAttributes:t}){return["s",Object(r.t)(this.options.HTMLAttributes,t),0]},addCommands(){return{setStrike:()=>({commands:t})=>t.setMark(this.name),toggleStrike:()=>({commands:t})=>t.toggleMark(this.name),unsetStrike:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[Object(r.r)({find:Et,type:this.type})]},addPasteRules(){return[Object(r.s)({find:At,type:this.type})]}}),Dt=r.d.create({name:"text",group:"inline"}),Pt=r.b.create({name:"starterKit",addExtensions(){const t=[];return!1!==this.options.bold&&t.push(m.configure(this.options.bold)),!1!==this.options.blockquote&&t.push(l.configure(this.options.blockquote)),!1!==this.options.bulletList&&t.push(w.configure(this.options.bulletList)),!1!==this.options.code&&t.push(O.configure(this.options.code)),!1!==this.options.codeBlock&&t.push(T.configure(this.options.codeBlock)),!1!==this.options.document&&t.push(E.configure(this.options.document)),!1!==this.options.dropcursor&&t.push(R.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&t.push(W.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&t.push(K.configure(this.options.hardBreak)),!1!==this.options.heading&&t.push(J.configure(this.options.heading)),!1!==this.options.history&&t.push(gt.configure(this.options.history)),!1!==this.options.horizontalRule&&t.push(yt.configure(this.options.horizontalRule)),!1!==this.options.italic&&t.push(xt.configure(this.options.italic)),!1!==this.options.listItem&&t.push(Ot.configure(this.options.listItem)),!1!==this.options.orderedList&&t.push(Ct.configure(this.options.orderedList)),!1!==this.options.paragraph&&t.push(Tt.configure(this.options.paragraph)),!1!==this.options.strike&&t.push(Nt.configure(this.options.strike)),!1!==this.options.text&&t.push(Dt.configure(this.options.text)),t}})},1589:function(t,e,n){"use strict";n.d(e,"a",(function(){return le}));var r=n(1426);const o=(t,e)=>{for(const n in e)t[n]=e[n];return t},l="numeric",c="ascii",d="alpha",h="asciinumeric",f="alphanumeric",m="emoji",y="whitespace";function v(t,e){return t in e||(e[t]=[]),e[t]}function w(t,e,n){e.numeric&&(e.asciinumeric=!0,e.alphanumeric=!0),e.ascii&&(e.asciinumeric=!0,e.alpha=!0),e.asciinumeric&&(e.alphanumeric=!0),e.alpha&&(e.alphanumeric=!0),e.alphanumeric&&(e.domain=!0),e.emoji&&(e.domain=!0);for(const r in e){const e=v(r,n);e.indexOf(t)<0&&e.push(t)}}function k(t=null){this.j={},this.jr=[],this.jd=null,this.t=t}k.groups={},k.prototype={accepts(){return!!this.t},go(input){const t=this,e=t.j[input];if(e)return e;for(let i=0;i<t.jr.length;i++){const e=t.jr[i][0],n=t.jr[i][1];if(n&&e.test(input))return n}return t.jd},has(input,t=!1){return t?input in this.j:!!this.go(input)},ta(t,e,n,r){for(let i=0;i<t.length;i++)this.tt(t[i],e,n,r)},tr(t,e,n,r){let o;return r=r||k.groups,e&&e.j?o=e:(o=new k(e),n&&r&&w(e,n,r)),this.jr.push([t,o]),o},ts(input,t,e,n){let r=this;const o=input.length;if(!o)return r;for(let i=0;i<o-1;i++)r=r.tt(input[i]);return r.tt(input[o-1],t,e,n)},tt(input,t,e,n){n=n||k.groups;const r=this;if(t&&t.j)return r.j[input]=t,t;const l=t;let c,d=r.go(input);if(d?(c=new k,o(c.j,d.j),c.jr.push.apply(c.jr,d.jr),c.jd=d.jd,c.t=d.t):c=new k,l){if(n)if(c.t&&"string"==typeof c.t){w(l,o(function(t,e){const n={};for(const r in e)e[r].indexOf(t)>=0&&(n[r]=!0);return n}(c.t,n),e),n)}else e&&w(l,e,n);c.t=l}return r.j[input]=c,c}};const x=(t,input,e,n,r)=>t.ta(input,e,n,r),O=(t,e,n,r,o)=>t.tr(e,n,r,o),S=(t,input,e,n,r)=>t.ts(input,e,n,r),M=(t,input,e,n,r)=>t.tt(input,e,n,r),C="WORD",T="UWORD",E="ASCIINUMERICAL",A="ALPHANUMERICAL",N="LOCALHOST",D="TLD",P="UTLD",R="SCHEME",j="SLASH_SCHEME",I="NUM",L="WS",B="NL",$="OPENBRACE",z="CLOSEBRACE",H="OPENBRACKET",V="CLOSEBRACKET",F="OPENPAREN",_="CLOSEPAREN",W="OPENANGLEBRACKET",K="CLOSEANGLEBRACKET",J="FULLWIDTHLEFTPAREN",U="FULLWIDTHRIGHTPAREN",G="LEFTCORNERBRACKET",Y="RIGHTCORNERBRACKET",X="LEFTWHITECORNERBRACKET",Q="RIGHTWHITECORNERBRACKET",Z="FULLWIDTHLESSTHAN",tt="FULLWIDTHGREATERTHAN",et="AMPERSAND",nt="APOSTROPHE",ot="ASTERISK",it="AT",st="BACKSLASH",at="BACKTICK",lt="CARET",ct="COLON",ht="COMMA",pt="DOLLAR",ut="DOT",ft="EQUALS",mt="EXCLAMATION",gt="HYPHEN",yt="PERCENT",vt="PIPE",bt="PLUS",wt="POUND",kt="QUERY",xt="QUOTE",Ot="FULLWIDTHMIDDLEDOT",St="SEMI",Mt="SLASH",Ct="TILDE",Tt="UNDERSCORE",Et="EMOJI",At="SYM";var Nt=Object.freeze({__proto__:null,ALPHANUMERICAL:A,AMPERSAND:et,APOSTROPHE:nt,ASCIINUMERICAL:E,ASTERISK:ot,AT:it,BACKSLASH:st,BACKTICK:at,CARET:lt,CLOSEANGLEBRACKET:K,CLOSEBRACE:z,CLOSEBRACKET:V,CLOSEPAREN:_,COLON:ct,COMMA:ht,DOLLAR:pt,DOT:ut,EMOJI:Et,EQUALS:ft,EXCLAMATION:mt,FULLWIDTHGREATERTHAN:tt,FULLWIDTHLEFTPAREN:J,FULLWIDTHLESSTHAN:Z,FULLWIDTHMIDDLEDOT:Ot,FULLWIDTHRIGHTPAREN:U,HYPHEN:gt,LEFTCORNERBRACKET:G,LEFTWHITECORNERBRACKET:X,LOCALHOST:N,NL:B,NUM:I,OPENANGLEBRACKET:W,OPENBRACE:$,OPENBRACKET:H,OPENPAREN:F,PERCENT:yt,PIPE:vt,PLUS:bt,POUND:wt,QUERY:kt,QUOTE:xt,RIGHTCORNERBRACKET:Y,RIGHTWHITECORNERBRACKET:Q,SCHEME:R,SEMI:St,SLASH:Mt,SLASH_SCHEME:j,SYM:At,TILDE:Ct,TLD:D,UNDERSCORE:Tt,UTLD:P,UWORD:T,WORD:C,WS:L});const Dt=/[a-z]/,Pt=/\p{L}/u,Rt=/\p{Emoji}/u,jt=/\d/,It=/\s/;let Lt=null,Bt=null;function $t(t,e){const n=function(t){const e=[],n=t.length;let r=0;for(;r<n;){let o,l=t.charCodeAt(r),c=l<55296||l>56319||r+1===n||(o=t.charCodeAt(r+1))<56320||o>57343?t[r]:t.slice(r,r+2);e.push(c),r+=c.length}return e}(e.replace(/[A-Z]/g,(t=>t.toLowerCase()))),r=n.length,o=[];let cursor=0,l=0;for(;l<r;){let c=t,d=null,h=0,f=null,m=-1,y=-1;for(;l<r&&(d=c.go(n[l]));)c=d,c.accepts()?(m=0,y=0,f=c):m>=0&&(m+=n[l].length,y++),h+=n[l].length,cursor+=n[l].length,l++;cursor-=m,l-=y,h-=m,o.push({t:f.t,v:e.slice(cursor-h,cursor),s:cursor-h,e:cursor})}return o}function zt(t,input,e,n,r){let o;const l=input.length;for(let i=0;i<l-1;i++){const e=input[i];t.j[e]?o=t.j[e]:(o=new k(n),o.jr=r.slice(),t.j[e]=o),t=o}return o=new k(e),o.jr=r.slice(),t.j[input[l-1]]=o,o}function Ht(t){const e=[],n=[];let i=0;for(;i<t.length;){let r=0;for(;"0123456789".indexOf(t[i+r])>=0;)r++;if(r>0){e.push(n.join(""));for(let e=parseInt(t.substring(i,i+r),10);e>0;e--)n.pop();i+=r}else n.push(t[i]),i++}return e}const Vt={defaultProtocol:"http",events:null,format:_t,formatHref:_t,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function Ft(t,e=null){let n=o({},Vt);t&&(n=o(n,t instanceof Ft?t.o:t));const r=n.ignoreTags,l=[];for(let i=0;i<r.length;i++)l.push(r[i].toUpperCase());this.o=n,e&&(this.defaultRender=e),this.ignoreTags=l}function _t(t){return t}Ft.prototype={o:Vt,ignoreTags:[],defaultRender:t=>t,check(t){return this.get("validate",t.toString(),t)},get(t,e,n){const r=null!=e;let option=this.o[t];return option?("object"==typeof option?(option=n.t in option?option[n.t]:Vt[t],"function"==typeof option&&r&&(option=option(e,n))):"function"==typeof option&&r&&(option=option(e,n.t,n)),option):option},getObj(t,e,n){let r=this.o[t];return"function"==typeof r&&null!=e&&(r=r(e,n.t,n)),r},render(t){const e=t.render(this);return(this.get("render",null,t)||this.defaultRender)(e,t.t,t)}};function Wt(t,e){this.t="token",this.v=t,this.tk=e}function qt(t,e){class n extends Wt{constructor(e,n){super(e,n),this.t=t}}for(const p in e)n.prototype[p]=e[p];return n.t=t,n}Wt.prototype={isLink:!1,toString(){return this.v},toHref(t){return this.toString()},toFormattedString(t){const e=this.toString(),n=t.get("truncate",e,this),r=t.get("format",e,this);return n&&r.length>n?r.substring(0,n)+"…":r},toFormattedHref(t){return t.get("formatHref",this.toHref(t.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(t=Vt.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(t),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(t){return{type:this.t,value:this.toFormattedString(t),isLink:this.isLink,href:this.toFormattedHref(t),start:this.startIndex(),end:this.endIndex()}},validate(t){return t.get("validate",this.toString(),this)},render(t){const e=this,n=this.toHref(t.get("defaultProtocol")),r=t.get("formatHref",n,this),l=t.get("tagName",n,e),content=this.toFormattedString(t),c={},d=t.get("className",n,e),h=t.get("target",n,e),f=t.get("rel",n,e),m=t.getObj("attributes",n,e),y=t.getObj("events",n,e);return c.href=r,d&&(c.class=d),h&&(c.target=h),f&&(c.rel=f),m&&o(c,m),{tagName:l,attributes:c,content:content,eventListeners:y}}};const Kt=qt("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),Jt=qt("text"),Ut=qt("nl"),Gt=qt("url",{isLink:!0,toHref(t=Vt.defaultProtocol){return this.hasProtocol()?this.v:`${t}://${this.v}`},hasProtocol(){const t=this.tk;return t.length>=2&&t[0].t!==N&&t[1].t===ct}});const Yt=t=>new k(t);function Xt(t,input,e){const n=e[0].s,r=e[e.length-1].e;return new t(input.slice(n,r),e)}const Qt="undefined"!=typeof console&&console&&console.warn||(()=>{}),Zt="until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.",te={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function ee(t,e=!1){if(te.initialized&&Qt(`linkifyjs: already initialized - will not register custom scheme "${t}" ${Zt}`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(t))throw new Error('linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or "-"\n2. Cannot start or end with "-"\n3. "-" cannot repeat');te.customSchemes.push([t,e])}function ne(){te.scanner=function(t=[]){const e={};k.groups=e;const n=new k;null==Lt&&(Lt=Ht("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==Bt&&(Bt=Ht("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),M(n,"'",nt),M(n,"{",$),M(n,"}",z),M(n,"[",H),M(n,"]",V),M(n,"(",F),M(n,")",_),M(n,"<",W),M(n,">",K),M(n,"（",J),M(n,"）",U),M(n,"「",G),M(n,"」",Y),M(n,"『",X),M(n,"』",Q),M(n,"＜",Z),M(n,"＞",tt),M(n,"&",et),M(n,"*",ot),M(n,"@",it),M(n,"`",at),M(n,"^",lt),M(n,":",ct),M(n,",",ht),M(n,"$",pt),M(n,".",ut),M(n,"=",ft),M(n,"!",mt),M(n,"-",gt),M(n,"%",yt),M(n,"|",vt),M(n,"+",bt),M(n,"#",wt),M(n,"?",kt),M(n,'"',xt),M(n,"/",Mt),M(n,";",St),M(n,"~",Ct),M(n,"_",Tt),M(n,"\\",st),M(n,"・",Ot);const r=O(n,jt,I,{[l]:!0});O(r,jt,r);const v=O(r,Dt,E,{[h]:!0}),x=O(r,Pt,A,{[f]:!0}),$t=O(n,Dt,C,{[c]:!0});O($t,jt,v),O($t,Dt,$t),O(v,jt,v),O(v,Dt,v);const Vt=O(n,Pt,T,{[d]:!0});O(Vt,Dt),O(Vt,jt,x),O(Vt,Pt,Vt),O(x,jt,x),O(x,Dt),O(x,Pt,x);const Ft=M(n,"\n",B,{[y]:!0}),_t=M(n,"\r",L,{[y]:!0}),Wt=O(n,It,L,{[y]:!0});M(n,"￼",Wt),M(_t,"\n",Ft),M(_t,"￼",Wt),O(_t,It,Wt),M(Wt,"\r"),M(Wt,"\n"),O(Wt,It,Wt),M(Wt,"￼",Wt);const qt=O(n,Rt,Et,{[m]:!0});M(qt,"#"),O(qt,Rt,qt),M(qt,"️",qt);const Kt=M(qt,"‍");M(Kt,"#"),O(Kt,Rt,qt);const Jt=[[Dt,$t],[jt,v]],Ut=[[Dt,null],[Pt,Vt],[jt,x]];for(let i=0;i<Lt.length;i++)zt(n,Lt[i],D,C,Jt);for(let i=0;i<Bt.length;i++)zt(n,Bt[i],P,T,Ut);w(D,{tld:!0,ascii:!0},e),w(P,{utld:!0,alpha:!0},e),zt(n,"file",R,C,Jt),zt(n,"mailto",R,C,Jt),zt(n,"http",j,C,Jt),zt(n,"https",j,C,Jt),zt(n,"ftp",j,C,Jt),zt(n,"ftps",j,C,Jt),w(R,{scheme:!0,ascii:!0},e),w(j,{slashscheme:!0,ascii:!0},e),t=t.sort(((a,b)=>a[0]>b[0]?1:-1));for(let i=0;i<t.length;i++){const e=t[i][0],r=t[i][1]?{scheme:!0}:{slashscheme:!0};e.indexOf("-")>=0?r.domain=!0:Dt.test(e)?jt.test(e)?r.asciinumeric=!0:r.ascii=!0:r.numeric=!0,S(n,e,e,r)}return S(n,"localhost",N,{ascii:!0}),n.jd=new k(At),{start:n,tokens:o({groups:e},Nt)}}(te.customSchemes);for(let i=0;i<te.tokenQueue.length;i++)te.tokenQueue[i][1]({scanner:te.scanner});te.parser=function({groups:t}){const e=t.domain.concat([et,ot,it,st,at,lt,pt,ft,gt,I,yt,vt,bt,wt,Mt,At,Ct,Tt]),n=[nt,ct,ht,ut,mt,yt,kt,xt,St,W,K,$,z,V,H,F,_,J,U,G,Y,X,Q,Z,tt],r=[et,nt,ot,st,at,lt,pt,ft,gt,$,z,yt,vt,bt,wt,kt,Mt,At,Ct,Tt],o=Yt(),l=M(o,Ct);x(l,r,l),x(l,t.domain,l);const c=Yt(),d=Yt(),h=Yt();x(o,t.domain,c),x(o,t.scheme,d),x(o,t.slashscheme,h),x(c,r,l),x(c,t.domain,c);const f=M(c,it);M(l,it,f),M(d,it,f),M(h,it,f);const m=M(l,ut);x(m,r,l),x(m,t.domain,l);const y=Yt();x(f,t.domain,y),x(y,t.domain,y);const v=M(y,ut);x(v,t.domain,y);const w=Yt(Kt);x(v,t.tld,w),x(v,t.utld,w),M(f,N,w);const k=M(y,gt);M(k,gt,k),x(k,t.domain,y),x(w,t.domain,y),M(w,ut,v),M(w,gt,k);const O=M(w,ct);x(O,t.numeric,Kt);const S=M(c,gt),C=M(c,ut);M(S,gt,S),x(S,t.domain,c),x(C,r,l),x(C,t.domain,c);const T=Yt(Gt);x(C,t.tld,T),x(C,t.utld,T),x(T,t.domain,c),x(T,r,l),M(T,ut,C),M(T,gt,S),M(T,it,f);const E=M(T,ct),A=Yt(Gt);x(E,t.numeric,A);const D=Yt(Gt),P=Yt();x(D,e,D),x(D,n,P),x(P,e,D),x(P,n,P),M(T,Mt,D),M(A,Mt,D);const R=M(d,ct),j=M(h,ct),L=M(j,Mt),Ot=M(L,Mt);x(d,t.domain,c),M(d,ut,C),M(d,gt,S),x(h,t.domain,c),M(h,ut,C),M(h,gt,S),x(R,t.domain,D),M(R,Mt,D),M(R,kt,D),x(Ot,t.domain,D),x(Ot,e,D),M(Ot,Mt,D);const Et=[[$,z],[H,V],[F,_],[W,K],[J,U],[G,Y],[X,Q],[Z,tt]];for(let i=0;i<Et.length;i++){const[t,r]=Et[i],o=M(D,t);M(P,t,o),M(o,r,D);const l=Yt(Gt);x(o,e,l);const c=Yt();x(o,n),x(l,e,l),x(l,n,c),x(c,e,l),x(c,n,c),M(l,r,D),M(c,r,D)}return M(o,N,T),M(o,B,Ut),{start:o,tokens:Nt}}(te.scanner.tokens);for(let i=0;i<te.pluginQueue.length;i++)te.pluginQueue[i][1]({scanner:te.scanner,parser:te.parser});return te.initialized=!0,te}function re(t){return te.initialized||ne(),function(t,input,e){let n=e.length,cursor=0,r=[],o=[];for(;cursor<n;){let l=t,c=null,d=null,h=0,f=null,m=-1;for(;cursor<n&&!(c=l.go(e[cursor].t));)o.push(e[cursor++]);for(;cursor<n&&(d=c||l.go(e[cursor].t));)c=null,l=d,l.accepts()?(m=0,f=l):m>=0&&m++,cursor++,h++;if(m<0)cursor-=h,cursor<n&&(o.push(e[cursor]),cursor++);else{o.length>0&&(r.push(Xt(Jt,input,o)),o=[]),cursor-=m,h-=m;const t=f.t,n=e.slice(cursor-h,cursor);r.push(Xt(t,input,n))}}return o.length>0&&r.push(Xt(Jt,input,o)),r}(te.parser.start,t,$t(te.scanner.start,t))}function oe(t,e=null,n=null){if(e&&"object"==typeof e){if(n)throw Error(`linkifyjs: Invalid link type ${e}; must be a string`);n=e,e=null}const r=new Ft(n),o=re(t),l=[];for(let i=0;i<o.length;i++){const t=o[i];!t.isLink||e&&t.t!==e||!r.check(t)||l.push(t.toFormattedObject(r))}return l}re.scan=$t;var ie=n(1599);const se=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function ae(t,e){const n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return e&&e.forEach((t=>{const e="string"==typeof t?t:t.scheme;e&&n.push(e)})),!t||t.replace(se,"").match(new RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}const le=r.c.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach((t=>{"string"!=typeof t?ee(t.scheme,t.optionalSlashes):ee(t)}))},onDestroy(){k.groups={},te.scanner=null,te.parser=null,te.tokenQueue=[],te.pluginQueue=[],te.customSchemes=[],te.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(t,e)=>!!ae(t,e.protocols),validate:t=>!!t,shouldAutoLink:t=>!!t}),addAttributes(){return{href:{default:null,parseHTML:element=>element.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:t=>{const e=t.getAttribute("href");return!(!e||!this.options.isAllowedUri(e,{defaultValidate:t=>!!ae(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol}))&&null}}]},renderHTML({HTMLAttributes:t}){return this.options.isAllowedUri(t.href,{defaultValidate:t=>!!ae(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",Object(r.t)(this.options.HTMLAttributes,t),0]:["a",Object(r.t)(this.options.HTMLAttributes,{...t,href:""}),0]},addCommands(){return{setLink:t=>({chain:e})=>{const{href:n}=t;return!!this.options.isAllowedUri(n,{defaultValidate:t=>!!ae(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&e().setMark(this.name,t).setMeta("preventAutolink",!0).run()},toggleLink:t=>({chain:e})=>{const{href:n}=t;return!!this.options.isAllowedUri(n,{defaultValidate:t=>!!ae(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&e().toggleMark(this.name,t,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:t})=>t().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[Object(r.s)({find:text=>{const t=[];if(text){const{protocols:e,defaultProtocol:n}=this.options,r=oe(text).filter((t=>t.isLink&&this.options.isAllowedUri(t.value,{defaultValidate:t=>!!ae(t,e),protocols:e,defaultProtocol:n})));r.length&&r.forEach((link=>t.push({text:link.value,data:{href:link.href},index:link.start})))}return t},type:this.type,getAttributes:t=>{var e;return{href:null===(e=t.data)||void 0===e?void 0:e.href}}})]},addProseMirrorPlugins(){const t=[],{protocols:e,defaultProtocol:n}=this.options;var o;return this.options.autolink&&t.push((o={type:this.type,defaultProtocol:this.options.defaultProtocol,validate:t=>this.options.isAllowedUri(t,{defaultValidate:t=>!!ae(t,e),protocols:e,defaultProtocol:n}),shouldAutoLink:this.options.shouldAutoLink},new ie.d({key:new ie.e("autolink"),appendTransaction:(t,e,n)=>{const l=t.some((t=>t.docChanged))&&!e.doc.eq(n.doc),c=t.some((t=>t.getMeta("preventAutolink")));if(!l||c)return;const{tr:tr}=n,d=Object(r.h)(e.doc,[...t]);return Object(r.k)(d).forEach((({newRange:t})=>{const e=Object(r.i)(n.doc,t,(t=>t.isTextblock));let l,c;if(e.length>1?(l=e[0],c=n.doc.textBetween(l.pos,l.pos+l.node.nodeSize,void 0," ")):e.length&&n.doc.textBetween(t.from,t.to," "," ").endsWith(" ")&&(l=e[0],c=n.doc.textBetween(l.pos,t.to,void 0," ")),l&&c){const t=c.split(" ").filter((s=>""!==s));if(t.length<=0)return!1;const e=t[t.length-1],h=l.pos+c.lastIndexOf(e);if(!e)return!1;const f=re(e).map((t=>t.toObject(o.defaultProtocol)));if(!(1===(d=f).length?d[0].isLink:3===d.length&&d[1].isLink&&["()","[]"].includes(d[0].value+d[2].value)))return!1;f.filter((link=>link.isLink)).map((link=>({...link,from:h+link.start+1,to:h+link.end+1}))).filter((link=>!n.schema.marks.code||!n.doc.rangeHasMark(link.from,link.to,n.schema.marks.code))).filter((link=>o.validate(link.value))).filter((link=>o.shouldAutoLink(link.value))).forEach((link=>{Object(r.m)(link.from,link.to,n.doc).some((t=>t.mark.type===o.type))||tr.addMark(link.from,link.to,o.type.create({href:link.href}))}))}var d})),tr.steps.length?tr:void 0}}))),!0===this.options.openOnClick&&t.push(function(t){return new ie.d({key:new ie.e("handleClickLink"),props:{handleClick:(view,e,n)=>{var o,l;if(0!==n.button)return!1;if(!view.editable)return!1;let a=n.target;const c=[];for(;"DIV"!==a.nodeName;)c.push(a),a=a.parentNode;if(!c.find((t=>"A"===t.nodeName)))return!1;const d=Object(r.j)(view.state,t.type.name),link=n.target,h=null!==(o=null==link?void 0:link.href)&&void 0!==o?o:d.href,f=null!==(l=null==link?void 0:link.target)&&void 0!==l?l:d.target;return!(!link||!h||(window.open(h,f),0))}}})}({type:this.type})),this.options.linkOnPaste&&t.push(function(t){return new ie.d({key:new ie.e("handlePasteLink"),props:{handlePaste:(view,e,n)=>{const{state:r}=view,{selection:o}=r,{empty:l}=o;if(l)return!1;let c="";n.content.forEach((t=>{c+=t.textContent}));const link=oe(c,{defaultProtocol:t.defaultProtocol}).find((t=>t.isLink&&t.value===c));return!(!c||!link)&&t.editor.commands.setMark(t.type,{href:link.href})}}})}({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),t}})},1590:function(t,e,n){"use strict";function r(content){this.content=content}n.d(e,"a",(function(){return at})),n.d(e,"b",(function(){return vt})),n.d(e,"c",(function(){return d})),n.d(e,"d",(function(){return y})),n.d(e,"e",(function(){return ot})),n.d(e,"f",(function(){return $})),n.d(e,"g",(function(){return L})),n.d(e,"h",(function(){return v})),n.d(e,"i",(function(){return it})),n.d(e,"j",(function(){return w})),r.prototype={constructor:r,find:function(t){for(var i=0;i<this.content.length;i+=2)if(this.content[i]===t)return i;return-1},get:function(t){var e=this.find(t);return-1==e?void 0:this.content[e+1]},update:function(t,e,n){var o=n&&n!=t?this.remove(n):this,l=o.find(t),content=o.content.slice();return-1==l?content.push(n||t,e):(content[l+1]=e,n&&(content[l]=n)),new r(content)},remove:function(t){var e=this.find(t);if(-1==e)return this;var content=this.content.slice();return content.splice(e,2),new r(content)},addToStart:function(t,e){return new r([t,e].concat(this.remove(t).content))},addToEnd:function(t,e){var content=this.remove(t).content.slice();return content.push(t,e),new r(content)},addBefore:function(t,e,n){var o=this.remove(e),content=o.content.slice(),l=o.find(t);return content.splice(-1==l?content.length:l,0,e,n),new r(content)},forEach:function(t){for(var i=0;i<this.content.length;i+=2)t(this.content[i],this.content[i+1])},prepend:function(map){return(map=r.from(map)).size?new r(map.content.concat(this.subtract(map).content)):this},append:function(map){return(map=r.from(map)).size?new r(this.subtract(map).content.concat(map.content)):this},subtract:function(map){var t=this;map=r.from(map);for(var i=0;i<map.content.length;i+=2)t=t.remove(map.content[i]);return t},toObject:function(){var t={};return this.forEach((function(e,n){t[e]=n})),t},get size(){return this.content.length>>1}},r.from=function(t){if(t instanceof r)return t;var content=[];if(t)for(var e in t)content.push(e,t[e]);return new r(content)};var o=r;function l(a,b,t){for(let i=0;;i++){if(i==a.childCount||i==b.childCount)return a.childCount==b.childCount?null:t;let e=a.child(i),n=b.child(i);if(e!=n){if(!e.sameMarkup(n))return t;if(e.isText&&e.text!=n.text){for(let r=0;e.text[r]==n.text[r];r++)t++;return t}if(e.content.size||n.content.size){let r=l(e.content,n.content,t+1);if(null!=r)return r}t+=e.nodeSize}else t+=e.nodeSize}}function c(a,b,t,e){for(let n=a.childCount,r=b.childCount;;){if(0==n||0==r)return n==r?null:{a:t,b:e};let o=a.child(--n),l=b.child(--r),d=o.nodeSize;if(o!=l){if(!o.sameMarkup(l))return{a:t,b:e};if(o.isText&&o.text!=l.text){let n=0,r=Math.min(o.text.length,l.text.length);for(;n<r&&o.text[o.text.length-n-1]==l.text[l.text.length-n-1];)n++,t--,e--;return{a:t,b:e}}if(o.content.size||l.content.size){let n=c(o.content,l.content,t-1,e-1);if(n)return n}t-=d,e-=d}else t-=d,e-=d}}class d{constructor(content,t){if(this.content=content,this.size=t||0,null==t)for(let i=0;i<content.length;i++)this.size+=content[i].nodeSize}nodesBetween(t,e,n,r=0,o){for(let i=0,l=0;l<e;i++){let c=this.content[i],d=l+c.nodeSize;if(d>t&&!1!==n(c,r+l,o||null,i)&&c.content.size){let o=l+1;c.nodesBetween(Math.max(0,t-o),Math.min(c.content.size,e-o),n,r+o)}l=d}}descendants(t){this.nodesBetween(0,this.size,t)}textBetween(t,e,n,r){let text="",o=!0;return this.nodesBetween(t,e,((l,c)=>{let d=l.isText?l.text.slice(Math.max(t,c)-c,e-c):l.isLeaf?r?"function"==typeof r?r(l):r:l.type.spec.leafText?l.type.spec.leafText(l):"":"";l.isBlock&&(l.isLeaf&&d||l.isTextblock)&&n&&(o?o=!1:text+=n),text+=d}),0),text}append(t){if(!t.size)return this;if(!this.size)return t;let e=this.lastChild,n=t.firstChild,content=this.content.slice(),i=0;for(e.isText&&e.sameMarkup(n)&&(content[content.length-1]=e.withText(e.text+n.text),i=1);i<t.content.length;i++)content.push(t.content[i]);return new d(content,this.size+t.size)}cut(t,e=this.size){if(0==t&&e==this.size)return this;let n=[],r=0;if(e>t)for(let i=0,o=0;o<e;i++){let l=this.content[i],c=o+l.nodeSize;c>t&&((o<t||c>e)&&(l=l.isText?l.cut(Math.max(0,t-o),Math.min(l.text.length,e-o)):l.cut(Math.max(0,t-o-1),Math.min(l.content.size,e-o-1))),n.push(l),r+=l.nodeSize),o=c}return new d(n,r)}cutByIndex(t,e){return t==e?d.empty:0==t&&e==this.content.length?this:new d(this.content.slice(t,e))}replaceChild(t,e){let n=this.content[t];if(n==e)return this;let r=this.content.slice(),o=this.size+e.nodeSize-n.nodeSize;return r[t]=e,new d(r,o)}addToStart(t){return new d([t].concat(this.content),this.size+t.nodeSize)}addToEnd(t){return new d(this.content.concat(t),this.size+t.nodeSize)}eq(t){if(this.content.length!=t.content.length)return!1;for(let i=0;i<this.content.length;i++)if(!this.content[i].eq(t.content[i]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(t){let e=this.content[t];if(!e)throw new RangeError("Index "+t+" out of range for "+this);return e}maybeChild(t){return this.content[t]||null}forEach(t){for(let i=0,p=0;i<this.content.length;i++){let e=this.content[i];t(e,p,i),p+=e.nodeSize}}findDiffStart(t,e=0){return l(this,t,e)}findDiffEnd(t,e=this.size,n=t.size){return c(this,t,e,n)}findIndex(t,e=-1){if(0==t)return f(0,t);if(t==this.size)return f(this.content.length,t);if(t>this.size||t<0)throw new RangeError(`Position ${t} outside of fragment (${this})`);for(let i=0,n=0;;i++){let r=n+this.child(i).nodeSize;if(r>=t)return r==t||e>0?f(i+1,r):f(i,n);n=r}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map((t=>t.toJSON())):null}static fromJSON(t,e){if(!e)return d.empty;if(!Array.isArray(e))throw new RangeError("Invalid input for Fragment.fromJSON");return new d(e.map(t.nodeFromJSON))}static fromArray(t){if(!t.length)return d.empty;let e,n=0;for(let i=0;i<t.length;i++){let r=t[i];n+=r.nodeSize,i&&r.isText&&t[i-1].sameMarkup(r)?(e||(e=t.slice(0,i)),e[e.length-1]=r.withText(e[e.length-1].text+r.text)):e&&e.push(r)}return new d(e||t,n)}static from(t){if(!t)return d.empty;if(t instanceof d)return t;if(Array.isArray(t))return this.fromArray(t);if(t.attrs)return new d([t],t.nodeSize);throw new RangeError("Can not convert "+t+" to a Fragment"+(t.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}d.empty=new d([],0);const h={index:0,offset:0};function f(t,e){return h.index=t,h.offset=e,h}function m(a,b){if(a===b)return!0;if(!a||"object"!=typeof a||!b||"object"!=typeof b)return!1;let t=Array.isArray(a);if(Array.isArray(b)!=t)return!1;if(t){if(a.length!=b.length)return!1;for(let i=0;i<a.length;i++)if(!m(a[i],b[i]))return!1}else{for(let p in a)if(!(p in b)||!m(a[p],b[p]))return!1;for(let p in b)if(!(p in a))return!1}return!0}class y{constructor(t,e){this.type=t,this.attrs=e}addToSet(t){let e,n=!1;for(let i=0;i<t.length;i++){let r=t[i];if(this.eq(r))return t;if(this.type.excludes(r.type))e||(e=t.slice(0,i));else{if(r.type.excludes(this.type))return t;!n&&r.type.rank>this.type.rank&&(e||(e=t.slice(0,i)),e.push(this),n=!0),e&&e.push(r)}}return e||(e=t.slice()),n||e.push(this),e}removeFromSet(t){for(let i=0;i<t.length;i++)if(this.eq(t[i]))return t.slice(0,i).concat(t.slice(i+1));return t}isInSet(t){for(let i=0;i<t.length;i++)if(this.eq(t[i]))return!0;return!1}eq(t){return this==t||this.type==t.type&&m(this.attrs,t.attrs)}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return t}static fromJSON(t,e){if(!e)throw new RangeError("Invalid input for Mark.fromJSON");let n=t.marks[e.type];if(!n)throw new RangeError(`There is no mark type ${e.type} in this schema`);let mark=n.create(e.attrs);return n.checkAttrs(mark.attrs),mark}static sameSet(a,b){if(a==b)return!0;if(a.length!=b.length)return!1;for(let i=0;i<a.length;i++)if(!a[i].eq(b[i]))return!1;return!0}static setFrom(t){if(!t||Array.isArray(t)&&0==t.length)return y.none;if(t instanceof y)return[t];let e=t.slice();return e.sort(((a,b)=>a.type.rank-b.type.rank)),e}}y.none=[];class v extends Error{}class w{constructor(content,t,e){this.content=content,this.openStart=t,this.openEnd=e}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(t,e){let content=x(this.content,t+this.openStart,e);return content&&new w(content,this.openStart,this.openEnd)}removeBetween(t,e){return new w(k(this.content,t+this.openStart,e+this.openStart),this.openStart,this.openEnd)}eq(t){return this.content.eq(t.content)&&this.openStart==t.openStart&&this.openEnd==t.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let t={content:this.content.toJSON()};return this.openStart>0&&(t.openStart=this.openStart),this.openEnd>0&&(t.openEnd=this.openEnd),t}static fromJSON(t,e){if(!e)return w.empty;let n=e.openStart||0,r=e.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw new RangeError("Invalid input for Slice.fromJSON");return new w(d.fromJSON(t,e.content),n,r)}static maxOpen(t,e=!0){let n=0,r=0;for(let r=t.firstChild;r&&!r.isLeaf&&(e||!r.type.spec.isolating);r=r.firstChild)n++;for(let n=t.lastChild;n&&!n.isLeaf&&(e||!n.type.spec.isolating);n=n.lastChild)r++;return new w(t,n,r)}}function k(content,t,e){let{index:n,offset:r}=content.findIndex(t),o=content.maybeChild(n),{index:l,offset:c}=content.findIndex(e);if(r==t||o.isText){if(c!=e&&!content.child(l).isText)throw new RangeError("Removing non-flat range");return content.cut(0,t).append(content.cut(e))}if(n!=l)throw new RangeError("Removing non-flat range");return content.replaceChild(n,o.copy(k(o.content,t-r-1,e-r-1)))}function x(content,t,e,n){let{index:r,offset:o}=content.findIndex(t),l=content.maybeChild(r);if(o==t||l.isText)return n&&!n.canReplace(r,r,e)?null:content.cut(0,t).append(e).append(content.cut(t));let c=x(l.content,t-o-1,e);return c&&content.replaceChild(r,l.copy(c))}function O(t,e,n){if(n.openStart>t.depth)throw new v("Inserted content deeper than insertion position");if(t.depth-n.openStart!=e.depth-n.openEnd)throw new v("Inconsistent open depths");return S(t,e,n,0)}function S(t,e,n,r){let o=t.index(r),l=t.node(r);if(o==e.index(r)&&r<t.depth-n.openStart){let c=S(t,e,n,r+1);return l.copy(l.content.replaceChild(o,c))}if(n.content.size){if(n.openStart||n.openEnd||t.depth!=r||e.depth!=r){let{start:o,end:c}=function(t,e){let n=e.depth-t.openStart,r=e.node(n).copy(t.content);for(let i=n-1;i>=0;i--)r=e.node(i).copy(d.from(r));return{start:r.resolveNoCache(t.openStart+n),end:r.resolveNoCache(r.content.size-t.openEnd-n)}}(n,t);return A(l,N(t,o,c,e,r))}{let r=t.parent,content=r.content;return A(r,content.cut(0,t.parentOffset).append(n.content).append(content.cut(e.parentOffset)))}}return A(l,D(t,e,r))}function M(main,sub){if(!sub.type.compatibleContent(main.type))throw new v("Cannot join "+sub.type.name+" onto "+main.type.name)}function C(t,e,n){let r=t.node(n);return M(r,e.node(n)),r}function T(t,e){let n=e.length-1;n>=0&&t.isText&&t.sameMarkup(e[n])?e[n]=t.withText(e[n].text+t.text):e.push(t)}function E(t,e,n,r){let o=(e||t).node(n),l=0,c=e?e.index(n):o.childCount;t&&(l=t.index(n),t.depth>n?l++:t.textOffset&&(T(t.nodeAfter,r),l++));for(let i=l;i<c;i++)T(o.child(i),r);e&&e.depth==n&&e.textOffset&&T(e.nodeBefore,r)}function A(t,content){return t.type.checkContent(content),t.copy(content)}function N(t,e,n,r,o){let l=t.depth>o&&C(t,e,o+1),c=r.depth>o&&C(n,r,o+1),content=[];return E(null,t,o,content),l&&c&&e.index(o)==n.index(o)?(M(l,c),T(A(l,N(t,e,n,r,o+1)),content)):(l&&T(A(l,D(t,e,o+1)),content),E(e,n,o,content),c&&T(A(c,D(n,r,o+1)),content)),E(r,null,o,content),new d(content)}function D(t,e,n){let content=[];if(E(null,t,n,content),t.depth>n){T(A(C(t,e,n+1),D(t,e,n+1)),content)}return E(e,null,n,content),new d(content)}w.empty=new w(d.empty,0,0);class P{constructor(t,path,e){this.pos=t,this.path=path,this.parentOffset=e,this.depth=path.length/3-1}resolveDepth(t){return null==t?this.depth:t<0?this.depth+t:t}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(t){return this.path[3*this.resolveDepth(t)]}index(t){return this.path[3*this.resolveDepth(t)+1]}indexAfter(t){return t=this.resolveDepth(t),this.index(t)+(t!=this.depth||this.textOffset?1:0)}start(t){return 0==(t=this.resolveDepth(t))?0:this.path[3*t-1]+1}end(t){return t=this.resolveDepth(t),this.start(t)+this.node(t).content.size}before(t){if(!(t=this.resolveDepth(t)))throw new RangeError("There is no position before the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]}after(t){if(!(t=this.resolveDepth(t)))throw new RangeError("There is no position after the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]+this.path[3*t].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let t=this.parent,e=this.index(this.depth);if(e==t.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=t.child(e);return n?t.child(e).cut(n):r}get nodeBefore(){let t=this.index(this.depth),e=this.pos-this.path[this.path.length-1];return e?this.parent.child(t).cut(0,e):0==t?null:this.parent.child(t-1)}posAtIndex(t,e){e=this.resolveDepth(e);let n=this.path[3*e],r=0==e?0:this.path[3*e-1]+1;for(let i=0;i<t;i++)r+=n.child(i).nodeSize;return r}marks(){let t=this.parent,e=this.index();if(0==t.content.size)return y.none;if(this.textOffset)return t.child(e).marks;let main=t.maybeChild(e-1),n=t.maybeChild(e);if(!main){let t=main;main=n,n=t}let r=main.marks;for(var i=0;i<r.length;i++)!1!==r[i].type.spec.inclusive||n&&r[i].isInSet(n.marks)||(r=r[i--].removeFromSet(r));return r}marksAcross(t){let e=this.parent.maybeChild(this.index());if(!e||!e.isInline)return null;let n=e.marks,r=t.parent.maybeChild(t.index());for(var i=0;i<n.length;i++)!1!==n[i].type.spec.inclusive||r&&n[i].isInSet(r.marks)||(n=n[i--].removeFromSet(n));return n}sharedDepth(t){for(let e=this.depth;e>0;e--)if(this.start(e)<=t&&this.end(e)>=t)return e;return 0}blockRange(t=this,e){if(t.pos<this.pos)return t.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==t.pos?1:0);n>=0;n--)if(t.pos<=this.end(n)&&(!e||e(this.node(n))))return new L(this,t,n);return null}sameParent(t){return this.pos-this.parentOffset==t.pos-t.parentOffset}max(t){return t.pos>this.pos?t:this}min(t){return t.pos<this.pos?t:this}toString(){let t="";for(let i=1;i<=this.depth;i++)t+=(t?"/":"")+this.node(i).type.name+"_"+this.index(i-1);return t+":"+this.parentOffset}static resolve(t,e){if(!(e>=0&&e<=t.content.size))throw new RangeError("Position "+e+" out of range");let path=[],n=0,r=e;for(let e=t;;){let{index:t,offset:o}=e.content.findIndex(r),l=r-o;if(path.push(e,t,n+o),!l)break;if(e=e.child(t),e.isText)break;r=l-1,n+=o+1}return new P(e,path,r)}static resolveCached(t,e){let n=I.get(t);if(n)for(let i=0;i<n.elts.length;i++){let t=n.elts[i];if(t.pos==e)return t}else I.set(t,n=new R);let r=n.elts[n.i]=P.resolve(t,e);return n.i=(n.i+1)%j,r}}class R{constructor(){this.elts=[],this.i=0}}const j=12,I=new WeakMap;class L{constructor(t,e,n){this.$from=t,this.$to=e,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const B=Object.create(null);class ${constructor(t,e,content,n=y.none){this.type=t,this.attrs=e,this.marks=n,this.content=content||d.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(t){return this.content.child(t)}maybeChild(t){return this.content.maybeChild(t)}forEach(t){this.content.forEach(t)}nodesBetween(t,e,n,r=0){this.content.nodesBetween(t,e,n,r,this)}descendants(t){this.nodesBetween(0,this.content.size,t)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(t,e,n,r){return this.content.textBetween(t,e,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(t){return this==t||this.sameMarkup(t)&&this.content.eq(t.content)}sameMarkup(t){return this.hasMarkup(t.type,t.attrs,t.marks)}hasMarkup(t,e,n){return this.type==t&&m(this.attrs,e||t.defaultAttrs||B)&&y.sameSet(this.marks,n||y.none)}copy(content=null){return content==this.content?this:new $(this.type,this.attrs,content,this.marks)}mark(t){return t==this.marks?this:new $(this.type,this.attrs,this.content,t)}cut(t,e=this.content.size){return 0==t&&e==this.content.size?this:this.copy(this.content.cut(t,e))}slice(t,e=this.content.size,n=!1){if(t==e)return w.empty;let r=this.resolve(t),o=this.resolve(e),l=n?0:r.sharedDepth(e),c=r.start(l),content=r.node(l).content.cut(r.pos-c,o.pos-c);return new w(content,r.depth-l,o.depth-l)}replace(t,e,n){return O(this.resolve(t),this.resolve(e),n)}nodeAt(t){for(let e=this;;){let{index:n,offset:r}=e.content.findIndex(t);if(e=e.maybeChild(n),!e)return null;if(r==t||e.isText)return e;t-=r+1}}childAfter(t){let{index:e,offset:n}=this.content.findIndex(t);return{node:this.content.maybeChild(e),index:e,offset:n}}childBefore(t){if(0==t)return{node:null,index:0,offset:0};let{index:e,offset:n}=this.content.findIndex(t);if(n<t)return{node:this.content.child(e),index:e,offset:n};let r=this.content.child(e-1);return{node:r,index:e-1,offset:n-r.nodeSize}}resolve(t){return P.resolveCached(this,t)}resolveNoCache(t){return P.resolve(this,t)}rangeHasMark(t,e,n){let r=!1;return e>t&&this.nodesBetween(t,e,(t=>(n.isInSet(t.marks)&&(r=!0),!r))),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let t=this.type.name;return this.content.size&&(t+="("+this.content.toStringInner()+")"),H(this.marks,t)}contentMatchAt(t){let e=this.type.contentMatch.matchFragment(this.content,0,t);if(!e)throw new Error("Called contentMatchAt on a node with invalid content");return e}canReplace(t,e,n=d.empty,r=0,o=n.childCount){let l=this.contentMatchAt(t).matchFragment(n,r,o),c=l&&l.matchFragment(this.content,e);if(!c||!c.validEnd)return!1;for(let i=r;i<o;i++)if(!this.type.allowsMarks(n.child(i).marks))return!1;return!0}canReplaceWith(t,e,n,r){if(r&&!this.type.allowsMarks(r))return!1;let o=this.contentMatchAt(t).matchType(n),l=o&&o.matchFragment(this.content,e);return!!l&&l.validEnd}canAppend(t){return t.content.size?this.canReplace(this.childCount,this.childCount,t.content):this.type.compatibleContent(t.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let t=y.none;for(let i=0;i<this.marks.length;i++){let mark=this.marks[i];mark.type.checkAttrs(mark.attrs),t=mark.addToSet(t)}if(!y.sameSet(t,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map((t=>t.type.name))}`);this.content.forEach((t=>t.check()))}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return this.content.size&&(t.content=this.content.toJSON()),this.marks.length&&(t.marks=this.marks.map((t=>t.toJSON()))),t}static fromJSON(t,e){if(!e)throw new RangeError("Invalid input for Node.fromJSON");let n;if(e.marks){if(!Array.isArray(e.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=e.marks.map(t.markFromJSON)}if("text"==e.type){if("string"!=typeof e.text)throw new RangeError("Invalid text node in JSON");return t.text(e.text,n)}let content=d.fromJSON(t,e.content),r=t.nodeType(e.type).create(e.attrs,content,n);return r.type.checkAttrs(r.attrs),r}}$.prototype.text=void 0;class z extends ${constructor(t,e,content,n){if(super(t,e,null,n),!content)throw new RangeError("Empty text nodes are not allowed");this.text=content}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):H(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(t,e){return this.text.slice(t,e)}get nodeSize(){return this.text.length}mark(t){return t==this.marks?this:new z(this.type,this.attrs,this.text,t)}withText(text){return text==this.text?this:new z(this.type,this.attrs,text,this.marks)}cut(t=0,e=this.text.length){return 0==t&&e==this.text.length?this:this.withText(this.text.slice(t,e))}eq(t){return this.sameMarkup(t)&&this.text==t.text}toJSON(){let base=super.toJSON();return base.text=this.text,base}}function H(t,e){for(let i=t.length-1;i>=0;i--)e=t[i].type.name+"("+e+")";return e}class V{constructor(t){this.validEnd=t,this.next=[],this.wrapCache=[]}static parse(t,e){let n=new F(t,e);if(null==n.next)return V.empty;let r=_(n);n.next&&n.err("Unexpected trailing text");let o=function(t){let e=Object.create(null);return n(Y(t,0));function n(r){let o=[];r.forEach((e=>{t[e].forEach((({term:e,to:n})=>{if(!e)return;let r;for(let i=0;i<o.length;i++)o[i][0]==e&&(r=o[i][1]);Y(t,n).forEach((t=>{r||o.push([e,r=[]]),-1==r.indexOf(t)&&r.push(t)}))}))}));let l=e[r.join(",")]=new V(r.indexOf(t.length-1)>-1);for(let i=0;i<o.length;i++){let t=o[i][1].sort(G);l.next.push({type:o[i][0],next:e[t.join(",")]||n(t)})}return l}}(function(t){let e=[[]];return o(l(t,0),n()),e;function n(){return e.push([])-1}function r(t,n,r){let o={term:r,to:n};return e[t].push(o),o}function o(t,e){t.forEach((t=>t.to=e))}function l(t,e){if("choice"==t.type)return t.exprs.reduce(((t,n)=>t.concat(l(n,e))),[]);if("seq"!=t.type){if("star"==t.type){let c=n();return r(e,c),o(l(t.expr,c),c),[r(c)]}if("plus"==t.type){let c=n();return o(l(t.expr,e),c),o(l(t.expr,c),c),[r(c)]}if("opt"==t.type)return[r(e)].concat(l(t.expr,e));if("range"==t.type){let c=e;for(let i=0;i<t.min;i++){let e=n();o(l(t.expr,c),e),c=e}if(-1==t.max)o(l(t.expr,c),c);else for(let i=t.min;i<t.max;i++){let e=n();r(c,e),o(l(t.expr,c),e),c=e}return[r(c)]}if("name"==t.type)return[r(e,void 0,t.value)];throw new Error("Unknown expr type")}for(let i=0;;i++){let r=l(t.exprs[i],e);if(i==t.exprs.length-1)return r;o(r,e=n())}}}(r));return function(t,e){for(let i=0,n=[t];i<n.length;i++){let t=n[i],r=!t.validEnd,o=[];for(let e=0;e<t.next.length;e++){let{type:l,next:c}=t.next[e];o.push(l.name),!r||l.isText||l.hasRequiredAttrs()||(r=!1),-1==n.indexOf(c)&&n.push(c)}r&&e.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(o,n),o}matchType(t){for(let i=0;i<this.next.length;i++)if(this.next[i].type==t)return this.next[i].next;return null}matchFragment(t,e=0,n=t.childCount){let r=this;for(let i=e;r&&i<n;i++)r=r.matchType(t.child(i).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let i=0;i<this.next.length;i++){let{type:t}=this.next[i];if(!t.isText&&!t.hasRequiredAttrs())return t}return null}compatible(t){for(let i=0;i<this.next.length;i++)for(let e=0;e<t.next.length;e++)if(this.next[i].type==t.next[e].type)return!0;return!1}fillBefore(t,e=!1,n=0){let r=[this];return function o(l,c){let h=l.matchFragment(t,n);if(h&&(!e||h.validEnd))return d.from(c.map((t=>t.createAndFill())));for(let i=0;i<l.next.length;i++){let{type:t,next:e}=l.next[i];if(!t.isText&&!t.hasRequiredAttrs()&&-1==r.indexOf(e)){r.push(e);let n=o(e,c.concat(t));if(n)return n}}return null}(this,[])}findWrapping(t){for(let i=0;i<this.wrapCache.length;i+=2)if(this.wrapCache[i]==t)return this.wrapCache[i+1];let e=this.computeWrapping(t);return this.wrapCache.push(t,e),e}computeWrapping(t){let e=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),o=r.match;if(o.matchType(t)){let t=[];for(let e=r;e.type;e=e.via)t.push(e.type);return t.reverse()}for(let i=0;i<o.next.length;i++){let{type:t,next:l}=o.next[i];t.isLeaf||t.hasRequiredAttrs()||t.name in e||r.type&&!l.validEnd||(n.push({match:t.contentMatch,type:t,via:r}),e[t.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(t){if(t>=this.next.length)throw new RangeError(`There's no ${t}th edge in this content match`);return this.next[t]}toString(){let t=[];return function e(n){t.push(n);for(let i=0;i<n.next.length;i++)-1==t.indexOf(n.next[i].next)&&e(n.next[i].next)}(this),t.map(((e,i)=>{let n=i+(e.validEnd?"*":" ")+" ";for(let i=0;i<e.next.length;i++)n+=(i?", ":"")+e.next[i].type.name+"->"+t.indexOf(e.next[i].next);return n})).join("\n")}}V.empty=new V(!0);class F{constructor(t,e){this.string=t,this.nodeTypes=e,this.inline=null,this.pos=0,this.tokens=t.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(t){return this.next==t&&(this.pos++||!0)}err(t){throw new SyntaxError(t+" (in content expression '"+this.string+"')")}}function _(t){let e=[];do{e.push(W(t))}while(t.eat("|"));return 1==e.length?e[0]:{type:"choice",exprs:e}}function W(t){let e=[];do{e.push(K(t))}while(t.next&&")"!=t.next&&"|"!=t.next);return 1==e.length?e[0]:{type:"seq",exprs:e}}function K(t){let e=function(t){if(t.eat("(")){let e=_(t);return t.eat(")")||t.err("Missing closing paren"),e}if(!/\W/.test(t.next)){let e=function(t,e){let n=t.nodeTypes,r=n[e];if(r)return[r];let o=[];for(let t in n){let r=n[t];r.isInGroup(e)&&o.push(r)}0==o.length&&t.err("No node type or group '"+e+"' found");return o}(t,t.next).map((e=>(null==t.inline?t.inline=e.isInline:t.inline!=e.isInline&&t.err("Mixing inline and block content"),{type:"name",value:e})));return t.pos++,1==e.length?e[0]:{type:"choice",exprs:e}}t.err("Unexpected token '"+t.next+"'")}(t);for(;;)if(t.eat("+"))e={type:"plus",expr:e};else if(t.eat("*"))e={type:"star",expr:e};else if(t.eat("?"))e={type:"opt",expr:e};else{if(!t.eat("{"))break;e=U(t,e)}return e}function J(t){/\D/.test(t.next)&&t.err("Expected number, got '"+t.next+"'");let e=Number(t.next);return t.pos++,e}function U(t,e){let n=J(t),r=n;return t.eat(",")&&(r="}"!=t.next?J(t):-1),t.eat("}")||t.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:e}}function G(a,b){return b-a}function Y(t,e){let n=[];return function e(r){let o=t[r];if(1==o.length&&!o[0].term)return e(o[0].to);n.push(r);for(let i=0;i<o.length;i++){let{term:t,to:r}=o[i];t||-1!=n.indexOf(r)||e(r)}}(e),n.sort(G)}function X(t){let e=Object.create(null);for(let n in t){let r=t[n];if(!r.hasDefault)return null;e[n]=r.default}return e}function Q(t,e){let n=Object.create(null);for(let r in t){let o=e&&e[r];if(void 0===o){let e=t[r];if(!e.hasDefault)throw new RangeError("No value supplied for attribute "+r);o=e.default}n[r]=o}return n}function Z(t,e,n,r){for(let r in e)if(!(r in t))throw new RangeError(`Unsupported attribute ${r} for ${n} of type ${r}`);for(let n in t){let r=t[n];r.validate&&r.validate(e[n])}}function tt(t,e){let n=Object.create(null);if(e)for(let r in e)n[r]=new nt(t,r,e[r]);return n}class et{constructor(t,e,n){this.name=t,this.schema=e,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=tt(t,n.attrs),this.defaultAttrs=X(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==t),this.isText="text"==t}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==V.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(t){return this.groups.indexOf(t)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let t in this.attrs)if(this.attrs[t].isRequired)return!0;return!1}compatibleContent(t){return this==t||this.contentMatch.compatible(t.contentMatch)}computeAttrs(t){return!t&&this.defaultAttrs?this.defaultAttrs:Q(this.attrs,t)}create(t=null,content,e){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new $(this,this.computeAttrs(t),d.from(content),y.setFrom(e))}createChecked(t=null,content,e){return content=d.from(content),this.checkContent(content),new $(this,this.computeAttrs(t),content,y.setFrom(e))}createAndFill(t=null,content,e){if(t=this.computeAttrs(t),(content=d.from(content)).size){let t=this.contentMatch.fillBefore(content);if(!t)return null;content=t.append(content)}let n=this.contentMatch.matchFragment(content),r=n&&n.fillBefore(d.empty,!0);return r?new $(this,t,content.append(r),y.setFrom(e)):null}validContent(content){let t=this.contentMatch.matchFragment(content);if(!t||!t.validEnd)return!1;for(let i=0;i<content.childCount;i++)if(!this.allowsMarks(content.child(i).marks))return!1;return!0}checkContent(content){if(!this.validContent(content))throw new RangeError(`Invalid content for node ${this.name}: ${content.toString().slice(0,50)}`)}checkAttrs(t){Z(this.attrs,t,"node",this.name)}allowsMarkType(t){return null==this.markSet||this.markSet.indexOf(t)>-1}allowsMarks(t){if(null==this.markSet)return!0;for(let i=0;i<t.length;i++)if(!this.allowsMarkType(t[i].type))return!1;return!0}allowedMarks(t){if(null==this.markSet)return t;let e;for(let i=0;i<t.length;i++)this.allowsMarkType(t[i].type)?e&&e.push(t[i]):e||(e=t.slice(0,i));return e?e.length?e:y.none:t}static compile(t,e){let n=Object.create(null);t.forEach(((t,r)=>n[t]=new et(t,e,r)));let r=e.spec.topNode||"doc";if(!n[r])throw new RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw new RangeError("Every schema needs a 'text' type");for(let t in n.text.attrs)throw new RangeError("The text node type should not have attributes");return n}}class nt{constructor(t,e,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function(t,e,n){let r=n.split("|");return n=>{let o=null===n?"null":typeof n;if(r.indexOf(o)<0)throw new RangeError(`Expected value of type ${r} for attribute ${e} on type ${t}, got ${o}`)}}(t,e,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class ot{constructor(t,e,n,r){this.name=t,this.rank=e,this.schema=n,this.spec=r,this.attrs=tt(t,r.attrs),this.excluded=null;let o=X(this.attrs);this.instance=o?new y(this,o):null}create(t=null){return!t&&this.instance?this.instance:new y(this,Q(this.attrs,t))}static compile(t,e){let n=Object.create(null),r=0;return t.forEach(((t,o)=>n[t]=new ot(t,r++,e,o))),n}removeFromSet(t){for(var i=0;i<t.length;i++)t[i].type==this&&(t=t.slice(0,i).concat(t.slice(i+1)),i--);return t}isInSet(t){for(let i=0;i<t.length;i++)if(t[i].type==this)return t[i]}checkAttrs(t){Z(this.attrs,t,"mark",this.name)}excludes(t){return this.excluded.indexOf(t)>-1}}class it{constructor(t){this.linebreakReplacement=null,this.cached=Object.create(null);let e=this.spec={};for(let n in t)e[n]=t[n];e.nodes=o.from(t.nodes),e.marks=o.from(t.marks||{}),this.nodes=et.compile(this.spec.nodes,this),this.marks=ot.compile(this.spec.marks,this);let n=Object.create(null);for(let t in this.nodes){if(t in this.marks)throw new RangeError(t+" can not be both a node and a mark");let e=this.nodes[t],r=e.spec.content||"",o=e.spec.marks;if(e.contentMatch=n[r]||(n[r]=V.parse(r,this.nodes)),e.inlineContent=e.contentMatch.inlineContent,e.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!e.isInline||!e.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=e}e.markSet="_"==o?null:o?st(this,o.split(" ")):""!=o&&e.inlineContent?null:[]}for(let t in this.marks){let e=this.marks[t],n=e.spec.excludes;e.excluded=null==n?[e]:""==n?[]:st(this,n.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(t,e=null,content,n){if("string"==typeof t)t=this.nodeType(t);else{if(!(t instanceof et))throw new RangeError("Invalid node type: "+t);if(t.schema!=this)throw new RangeError("Node type from different schema used ("+t.name+")")}return t.createChecked(e,content,n)}text(text,t){let e=this.nodes.text;return new z(e,e.defaultAttrs,text,y.setFrom(t))}mark(t,e){return"string"==typeof t&&(t=this.marks[t]),t.create(e)}nodeFromJSON(t){return $.fromJSON(this,t)}markFromJSON(t){return y.fromJSON(this,t)}nodeType(t){let e=this.nodes[t];if(!e)throw new RangeError("Unknown node type: "+t);return e}}function st(t,e){let n=[];for(let i=0;i<e.length;i++){let r=e[i],mark=t.marks[r],o=mark;if(mark)n.push(mark);else for(let e in t.marks){let mark=t.marks[e];("_"==r||mark.spec.group&&mark.spec.group.split(" ").indexOf(r)>-1)&&n.push(o=mark)}if(!o)throw new SyntaxError("Unknown mark type: '"+e[i]+"'")}return n}class at{constructor(t,e){this.schema=t,this.rules=e,this.tags=[],this.styles=[];let n=this.matchedStyles=[];e.forEach((t=>{if(function(t){return null!=t.tag}(t))this.tags.push(t);else if(function(t){return null!=t.style}(t)){let e=/[^=]*/.exec(t.style)[0];n.indexOf(e)<0&&n.push(e),this.styles.push(t)}})),this.normalizeLists=!this.tags.some((e=>{if(!/^(ul|ol)\b/.test(e.tag)||!e.node)return!1;let n=t.nodes[e.node];return n.contentMatch.matchType(n)}))}parse(t,e={}){let n=new ft(this,e,!1);return n.addAll(t,y.none,e.from,e.to),n.finish()}parseSlice(t,e={}){let n=new ft(this,e,!0);return n.addAll(t,y.none,e.from,e.to),w.maxOpen(n.finish())}matchTag(t,e,n){for(let i=n?this.tags.indexOf(n)+1:0;i<this.tags.length;i++){let n=this.tags[i];if(mt(t,n.tag)&&(void 0===n.namespace||t.namespaceURI==n.namespace)&&(!n.context||e.matchesContext(n.context))){if(n.getAttrs){let e=n.getAttrs(t);if(!1===e)continue;n.attrs=e||void 0}return n}}}matchStyle(t,e,n,r){for(let i=r?this.styles.indexOf(r)+1:0;i<this.styles.length;i++){let r=this.styles[i],style=r.style;if(!(0!=style.indexOf(t)||r.context&&!n.matchesContext(r.context)||style.length>t.length&&(61!=style.charCodeAt(t.length)||style.slice(t.length+1)!=e))){if(r.getAttrs){let t=r.getAttrs(e);if(!1===t)continue;r.attrs=t||void 0}return r}}}static schemaRules(t){let e=[];function n(t){let n=null==t.priority?50:t.priority,i=0;for(;i<e.length;i++){let t=e[i];if((null==t.priority?50:t.priority)<n)break}e.splice(i,0,t)}for(let e in t.marks){let r=t.marks[e].spec.parseDOM;r&&r.forEach((t=>{n(t=gt(t)),t.mark||t.ignore||t.clearMark||(t.mark=e)}))}for(let e in t.nodes){let r=t.nodes[e].spec.parseDOM;r&&r.forEach((t=>{n(t=gt(t)),t.node||t.ignore||t.mark||(t.node=e)}))}return e}static fromSchema(t){return t.cached.domParser||(t.cached.domParser=new at(t,at.schemaRules(t)))}}const lt={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},ct={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},ht={ol:!0,ul:!0};function pt(t,e,base){return null!=e?(e?1:0)|("full"===e?2:0):t&&"pre"==t.whitespace?3:-5&base}class ut{constructor(t,e,n,r,o,l){this.type=t,this.attrs=e,this.marks=n,this.solid=r,this.options=l,this.content=[],this.activeMarks=y.none,this.match=o||(4&l?null:t.contentMatch)}findWrapping(t){if(!this.match){if(!this.type)return[];let e=this.type.contentMatch.fillBefore(d.from(t));if(!e){let e,n=this.type.contentMatch;return(e=n.findWrapping(t.type))?(this.match=n,e):null}this.match=this.type.contentMatch.matchFragment(e)}return this.match.findWrapping(t.type)}finish(t){if(!(1&this.options)){let t,e=this.content[this.content.length-1];if(e&&e.isText&&(t=/[ \t\r\n\u000c]+$/.exec(e.text))){let text=e;e.text.length==t[0].length?this.content.pop():this.content[this.content.length-1]=text.withText(text.text.slice(0,text.text.length-t[0].length))}}let content=d.from(this.content);return!t&&this.match&&(content=content.append(this.match.fillBefore(d.empty,!0))),this.type?this.type.create(this.attrs,content,this.marks):content}inlineContext(t){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:t.parentNode&&!lt.hasOwnProperty(t.parentNode.nodeName.toLowerCase())}}class ft{constructor(t,e,n){this.parser=t,this.options=e,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let r,o=e.topNode,l=pt(null,e.preserveWhitespace,0)|(n?4:0);r=o?new ut(o.type,o.attrs,y.none,!0,e.topMatch||o.type.contentMatch,l):new ut(n?null:t.schema.topNodeType,null,y.none,!0,null,l),this.nodes=[r],this.find=e.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(t,e){3==t.nodeType?this.addTextNode(t,e):1==t.nodeType&&this.addElement(t,e)}addTextNode(t,e){let n=t.nodeValue,r=this.top,o=2&r.options?"full":this.localPreserveWS||(1&r.options)>0;if("full"===o||r.inlineContext(t)||/[^ \t\r\n\u000c]/.test(n)){if(o)n="full"!==o?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let e=r.content[r.content.length-1],o=t.previousSibling;(!e||o&&"BR"==o.nodeName||e.isText&&/[ \t\r\n\u000c]$/.test(e.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),e,!/\S/.test(n)),this.findInText(t)}else this.findInside(t)}addElement(t,e,n){let r=this.localPreserveWS,o=this.top;("PRE"==t.tagName||/pre/.test(t.style&&t.style.whiteSpace))&&(this.localPreserveWS=!0);let l,c=t.nodeName.toLowerCase();ht.hasOwnProperty(c)&&this.parser.normalizeLists&&function(t){for(let e=t.firstChild,n=null;e;e=e.nextSibling){let t=1==e.nodeType?e.nodeName.toLowerCase():null;t&&ht.hasOwnProperty(t)&&n?(n.appendChild(e),e=n):"li"==t?n=e:t&&(n=null)}}(t);let d=this.options.ruleFromNode&&this.options.ruleFromNode(t)||(l=this.parser.matchTag(t,this,n));t:if(d?d.ignore:ct.hasOwnProperty(c))this.findInside(t),this.ignoreFallback(t,e);else if(!d||d.skip||d.closeParent){d&&d.closeParent?this.open=Math.max(0,this.open-1):d&&d.skip.nodeType&&(t=d.skip);let n,r=this.needsBlock;if(lt.hasOwnProperty(c))o.content.length&&o.content[0].isInline&&this.open&&(this.open--,o=this.top),n=!0,o.type||(this.needsBlock=!0);else if(!t.firstChild){this.leafFallback(t,e);break t}let l=d&&d.skip?e:this.readStyles(t,e);l&&this.addAll(t,l),n&&this.sync(o),this.needsBlock=r}else{let n=this.readStyles(t,e);n&&this.addElementByRule(t,d,n,!1===d.consuming?l:void 0)}this.localPreserveWS=r}leafFallback(t,e){"BR"==t.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(t.ownerDocument.createTextNode("\n"),e)}ignoreFallback(t,e){"BR"!=t.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),e,!0)}readStyles(t,e){let n=t.style;if(n&&n.length)for(let i=0;i<this.parser.matchedStyles.length;i++){let t=this.parser.matchedStyles[i],r=n.getPropertyValue(t);if(r)for(let n;;){let o=this.parser.matchStyle(t,r,this,n);if(!o)break;if(o.ignore)return null;if(e=o.clearMark?e.filter((t=>!o.clearMark(t))):e.concat(this.parser.schema.marks[o.mark].create(o.attrs)),!1!==o.consuming)break;n=o}}return e}addElementByRule(t,e,n,r){let o,l;if(e.node)if(l=this.parser.schema.nodes[e.node],l.isLeaf)this.insertNode(l.create(e.attrs),n,"BR"==t.nodeName)||this.leafFallback(t,n);else{let t=this.enter(l,e.attrs||null,n,e.preserveWhitespace);t&&(o=!0,n=t)}else{let t=this.parser.schema.marks[e.mark];n=n.concat(t.create(e.attrs))}let c=this.top;if(l&&l.isLeaf)this.findInside(t);else if(r)this.addElement(t,n,r);else if(e.getContent)this.findInside(t),e.getContent(t,this.parser.schema).forEach((t=>this.insertNode(t,n,!1)));else{let r=t;"string"==typeof e.contentElement?r=t.querySelector(e.contentElement):"function"==typeof e.contentElement?r=e.contentElement(t):e.contentElement&&(r=e.contentElement),this.findAround(t,r,!0),this.addAll(r,n),this.findAround(t,r,!1)}o&&this.sync(c)&&this.open--}addAll(t,e,n,r){let o=n||0;for(let l=n?t.childNodes[n]:t.firstChild,c=null==r?null:t.childNodes[r];l!=c;l=l.nextSibling,++o)this.findAtPoint(t,o),this.addDOM(l,e);this.findAtPoint(t,o)}findPlace(t,e,n){let r,o;for(let e=this.open,l=0;e>=0;e--){let c=this.nodes[e],d=c.findWrapping(t);if(d&&(!r||r.length>d.length+l)&&(r=d,o=c,!d.length))break;if(c.solid){if(n)break;l+=2}}if(!r)return null;this.sync(o);for(let i=0;i<r.length;i++)e=this.enterInner(r[i],null,e,!1);return e}insertNode(t,e,n){if(t.isInline&&this.needsBlock&&!this.top.type){let t=this.textblockFromContext();t&&(e=this.enterInner(t,null,e))}let r=this.findPlace(t,e,n);if(r){this.closeExtra();let e=this.top;e.match&&(e.match=e.match.matchType(t.type));let n=y.none;for(let o of r.concat(t.marks))(e.type?e.type.allowsMarkType(o.type):yt(o.type,t.type))&&(n=o.addToSet(n));return e.content.push(t.mark(n)),!0}return!1}enter(t,e,n,r){let o=this.findPlace(t.create(e),n,!1);return o&&(o=this.enterInner(t,e,n,!0,r)),o}enterInner(t,e,n,r=!1,o){this.closeExtra();let l=this.top;l.match=l.match&&l.match.matchType(t);let c=pt(t,o,l.options);4&l.options&&0==l.content.length&&(c|=4);let d=y.none;return n=n.filter((e=>!(l.type?l.type.allowsMarkType(e.type):yt(e.type,t))||(d=e.addToSet(d),!1))),this.nodes.push(new ut(t,e,d,r,null,c)),this.open++,n}closeExtra(t=!1){let i=this.nodes.length-1;if(i>this.open){for(;i>this.open;i--)this.nodes[i-1].content.push(this.nodes[i].finish(t));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!(!this.isOpen&&!this.options.topOpen))}sync(t){for(let i=this.open;i>=0;i--){if(this.nodes[i]==t)return this.open=i,!0;this.localPreserveWS&&(this.nodes[i].options|=1)}return!1}get currentPos(){this.closeExtra();let t=0;for(let i=this.open;i>=0;i--){let content=this.nodes[i].content;for(let e=content.length-1;e>=0;e--)t+=content[e].nodeSize;i&&t++}return t}findAtPoint(t,e){if(this.find)for(let i=0;i<this.find.length;i++)this.find[i].node==t&&this.find[i].offset==e&&(this.find[i].pos=this.currentPos)}findInside(t){if(this.find)for(let i=0;i<this.find.length;i++)null==this.find[i].pos&&1==t.nodeType&&t.contains(this.find[i].node)&&(this.find[i].pos=this.currentPos)}findAround(t,content,e){if(t!=content&&this.find)for(let i=0;i<this.find.length;i++)if(null==this.find[i].pos&&1==t.nodeType&&t.contains(this.find[i].node)){content.compareDocumentPosition(this.find[i].node)&(e?2:4)&&(this.find[i].pos=this.currentPos)}}findInText(t){if(this.find)for(let i=0;i<this.find.length;i++)this.find[i].node==t&&(this.find[i].pos=this.currentPos-(t.nodeValue.length-this.find[i].offset))}matchesContext(t){if(t.indexOf("|")>-1)return t.split(/\s*\|\s*/).some(this.matchesContext,this);let e=t.split("/"),option=this.options.context,n=!(this.isOpen||option&&option.parent.type!=this.nodes[0].type),r=-(option?option.depth+1:0)+(n?0:1),o=(i,t)=>{for(;i>=0;i--){let l=e[i];if(""==l){if(i==e.length-1||0==i)continue;for(;t>=r;t--)if(o(i-1,t))return!0;return!1}{let e=t>0||0==t&&n?this.nodes[t].type:option&&t>=r?option.node(t-r).type:null;if(!e||e.name!=l&&!e.isInGroup(l))return!1;t--}}return!0};return o(e.length-1,this.open)}textblockFromContext(){let t=this.options.context;if(t)for(let e=t.depth;e>=0;e--){let n=t.node(e).contentMatchAt(t.indexAfter(e)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let e=this.parser.schema.nodes[t];if(e.isTextblock&&e.defaultAttrs)return e}}}function mt(t,e){return(t.matches||t.msMatchesSelector||t.webkitMatchesSelector||t.mozMatchesSelector).call(t,e)}function gt(t){let e={};for(let n in t)e[n]=t[n];return e}function yt(t,e){let n=e.schema.nodes;for(let r in n){let o=n[r];if(!o.allowsMarkType(t))continue;let l=[],c=t=>{l.push(t);for(let i=0;i<t.edgeCount;i++){let{type:n,next:r}=t.edge(i);if(n==e)return!0;if(l.indexOf(r)<0&&c(r))return!0}};if(c(o.contentMatch))return!0}}class vt{constructor(t,e){this.nodes=t,this.marks=e}serializeFragment(t,e={},n){n||(n=wt(e).createDocumentFragment());let r=n,o=[];return t.forEach((t=>{if(o.length||t.marks.length){let n=0,l=0;for(;n<o.length&&l<t.marks.length;){let e=t.marks[l];if(this.marks[e.type.name]){if(!e.eq(o[n][0])||!1===e.type.spec.spanning)break;n++,l++}else l++}for(;n<o.length;)r=o.pop()[1];for(;l<t.marks.length;){let n=t.marks[l++],c=this.serializeMark(n,t.isInline,e);c&&(o.push([n,r]),r.appendChild(c.dom),r=c.contentDOM||c.dom)}}r.appendChild(this.serializeNodeInner(t,e))})),n}serializeNodeInner(t,e){let{dom:n,contentDOM:r}=Ot(wt(e),this.nodes[t.type.name](t),null,t.attrs);if(r){if(t.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(t.content,e,r)}return n}serializeNode(t,e={}){let n=this.serializeNodeInner(t,e);for(let i=t.marks.length-1;i>=0;i--){let r=this.serializeMark(t.marks[i],t.isInline,e);r&&((r.contentDOM||r.dom).appendChild(n),n=r.dom)}return n}serializeMark(mark,t,e={}){let n=this.marks[mark.type.name];return n&&Ot(wt(e),n(mark,t),null,mark.attrs)}static renderSpec(t,e,n=null,r){return Ot(t,e,n,r)}static fromSchema(t){return t.cached.domSerializer||(t.cached.domSerializer=new vt(this.nodesFromSchema(t),this.marksFromSchema(t)))}static nodesFromSchema(t){let e=bt(t.nodes);return e.text||(e.text=t=>t.text),e}static marksFromSchema(t){return bt(t.marks)}}function bt(t){let e={};for(let n in t){let r=t[n].spec.toDOM;r&&(e[n]=r)}return e}function wt(t){return t.document||window.document}const kt=new WeakMap;function xt(t){let e=kt.get(t);return void 0===e&&kt.set(t,e=function(t){let e=null;function n(t){if(t&&"object"==typeof t)if(Array.isArray(t))if("string"==typeof t[0])e||(e=[]),e.push(t);else for(let i=0;i<t.length;i++)n(t[i]);else for(let e in t)n(t[e])}return n(t),e}(t)),e}function Ot(t,e,n,r){if("string"==typeof e)return{dom:t.createTextNode(e)};if(null!=e.nodeType)return{dom:e};if(e.dom&&null!=e.dom.nodeType)return e;let o,l=e[0];if("string"!=typeof l)throw new RangeError("Invalid array passed to renderSpec");if(r&&(o=xt(r))&&o.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let c,d=l.indexOf(" ");d>0&&(n=l.slice(0,d),l=l.slice(d+1));let h=n?t.createElementNS(n,l):t.createElement(l),f=e[1],m=1;if(f&&"object"==typeof f&&null==f.nodeType&&!Array.isArray(f)){m=2;for(let t in f)if(null!=f[t]){let e=t.indexOf(" ");e>0?h.setAttributeNS(t.slice(0,e),t.slice(e+1),f[t]):h.setAttribute(t,f[t])}}for(let i=m;i<e.length;i++){let o=e[i];if(0===o){if(i<e.length-1||i>m)throw new RangeError("Content hole must be the only child of its parent node");return{dom:h,contentDOM:h}}{let{dom:e,contentDOM:l}=Ot(t,o,n,r);if(h.appendChild(e),l){if(c)throw new RangeError("Multiple content holes");c=l}}}return{dom:h,contentDOM:c}}},1599:function(t,e,n){"use strict";var r=n(1569);n.d(e,"a",(function(){return r.a})),n.d(e,"b",(function(){return r.b})),n.d(e,"c",(function(){return r.c})),n.d(e,"d",(function(){return r.d})),n.d(e,"e",(function(){return r.e})),n.d(e,"f",(function(){return r.f})),n.d(e,"g",(function(){return r.h}))},1613:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.isSymbol=e.isOneOf=e.isInteger=e.isInstanceOf=e.vuePropValidator=void 0;const o=r(n(3));e.vuePropValidator=function(t,...e){const n=t?[...e,t]:e;if(0!==n.length)return t=>{for(const e of n){const n=e(t);if(n)return"object"==typeof o.default&&"util"in o.default?o.default.util.warn(`${n} (received: '${String(t)}')`):console.warn(`${n} (received: '${String(t)}')`),!1}return!0}};var l=n(2017);Object.defineProperty(e,"isInstanceOf",{enumerable:!0,get:function(){return l.isInstanceOf}});var c=n(2018);Object.defineProperty(e,"isInteger",{enumerable:!0,get:function(){return c.isInteger}});var d=n(2019);Object.defineProperty(e,"isOneOf",{enumerable:!0,get:function(){return d.isOneOf}});var h=n(2020);Object.defineProperty(e,"isSymbol",{enumerable:!0,get:function(){return h.isSymbol}})},1728:function(t,e,n){"use strict";n.d(e,"a",(function(){return Xe})),n.d(e,"b",(function(){return tn})),n.d(e,"c",(function(){return Cn}));var r=n(1569),o=n(1590),l=n(1576);const c=function(t){for(var e=0;;e++)if(!(t=t.previousSibling))return e},d=function(t){let e=t.assignedSlot||t.parentNode;return e&&11==e.nodeType?e.host:e};let h=null;const f=function(t,e,n){let r=h||(h=document.createRange());return r.setEnd(t,null==n?t.nodeValue.length:n),r.setStart(t,e||0),r},m=function(t,e,n,r){return n&&(v(t,e,n,r,-1)||v(t,e,n,r,1))},y=/^(img|br|input|textarea|hr)$/i;function v(t,e,n,r,o){for(var l;;){if(t==n&&e==r)return!0;if(e==(o<0?0:w(t))){let n=t.parentNode;if(!n||1!=n.nodeType||k(t)||y.test(t.nodeName)||"false"==t.contentEditable)return!1;e=c(t)+(o<0?0:1),t=n}else{if(1!=t.nodeType)return!1;{let n=t.childNodes[e+(o<0?-1:0)];if(1==n.nodeType&&"false"==n.contentEditable){if(!(null===(l=n.pmViewDesc)||void 0===l?void 0:l.ignoreForSelection))return!1;e+=o}else t=n,e=o<0?w(t):0}}}}function w(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function k(t){let desc;for(let e=t;e&&!(desc=e.pmViewDesc);e=e.parentNode);return desc&&desc.node&&desc.node.isBlock&&(desc.dom==t||desc.contentDOM==t)}const x=function(t){return t.focusNode&&m(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)};function O(t,e){let n=document.createEvent("Event");return n.initEvent("keydown",!0,!0),n.keyCode=t,n.key=n.code=e,n}const nav="undefined"!=typeof navigator?navigator:null,S="undefined"!=typeof document?document:null,M=nav&&nav.userAgent||"",C=/Edge\/(\d+)/.exec(M),T=/MSIE \d/.exec(M),E=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(M),A=!!(T||E||C),N=T?document.documentMode:E?+E[1]:C?+C[1]:0,D=!A&&/gecko\/(\d+)/i.test(M);D&&(/Firefox\/(\d+)/.exec(M)||[0,0])[1];const P=!A&&/Chrome\/(\d+)/.exec(M),R=!!P,j=P?+P[1]:0,I=!A&&!!nav&&/Apple Computer/.test(nav.vendor),L=I&&(/Mobile\/\w+/.test(M)||!!nav&&nav.maxTouchPoints>2),B=L||!!nav&&/Mac/.test(nav.platform),$=!!nav&&/Win/.test(nav.platform),z=/Android \d/.test(M),H=!!S&&"webkitFontSmoothing"in S.documentElement.style,V=H?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function F(t){let e=t.defaultView&&t.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:t.documentElement.clientWidth,top:0,bottom:t.documentElement.clientHeight}}function _(t,e){return"number"==typeof t?t:t[e]}function W(t){let rect=t.getBoundingClientRect(),e=rect.width/t.offsetWidth||1,n=rect.height/t.offsetHeight||1;return{left:rect.left,right:rect.left+t.clientWidth*e,top:rect.top,bottom:rect.top+t.clientHeight*n}}function K(view,rect,t){let e=view.someProp("scrollThreshold")||0,n=view.someProp("scrollMargin")||5,r=view.dom.ownerDocument;for(let o=t||view.dom;o;){if(1!=o.nodeType){o=d(o);continue}let t=o,l=t==r.body,c=l?F(r):W(t),h=0,f=0;if(rect.top<c.top+_(e,"top")?f=-(c.top-rect.top+_(n,"top")):rect.bottom>c.bottom-_(e,"bottom")&&(f=rect.bottom-rect.top>c.bottom-c.top?rect.top+_(n,"top")-c.top:rect.bottom-c.bottom+_(n,"bottom")),rect.left<c.left+_(e,"left")?h=-(c.left-rect.left+_(n,"left")):rect.right>c.right-_(e,"right")&&(h=rect.right-c.right+_(n,"right")),h||f)if(l)r.defaultView.scrollBy(h,f);else{let e=t.scrollLeft,n=t.scrollTop;f&&(t.scrollTop+=f),h&&(t.scrollLeft+=h);let r=t.scrollLeft-e,o=t.scrollTop-n;rect={left:rect.left-r,top:rect.top-o,right:rect.right-r,bottom:rect.bottom-o}}let m=l?"fixed":getComputedStyle(o).position;if(/^(fixed|sticky)$/.test(m))break;o="absolute"==m?o.offsetParent:d(o)}}function J(t){let e=[],n=t.ownerDocument;for(let r=t;r&&(e.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),t!=n);r=d(r));return e}function U(t,e){for(let i=0;i<t.length;i++){let{dom:n,top:r,left:o}=t[i];n.scrollTop!=r+e&&(n.scrollTop=r+e),n.scrollLeft!=o&&(n.scrollLeft=o)}}let G=null;function Y(t,e){let n,r,o,l,c=2e8,d=0,h=e.top,m=e.top;for(let y=t.firstChild,v=0;y;y=y.nextSibling,v++){let t;if(1==y.nodeType)t=y.getClientRects();else{if(3!=y.nodeType)continue;t=f(y).getClientRects()}for(let i=0;i<t.length;i++){let rect=t[i];if(rect.top<=h&&rect.bottom>=m){h=Math.max(rect.bottom,h),m=Math.min(rect.top,m);let t=rect.left>e.left?rect.left-e.left:rect.right<e.left?e.left-rect.right:0;if(t<c){n=y,c=t,r=t&&3==n.nodeType?{left:rect.right<e.left?rect.right:rect.left,top:e.top}:e,1==y.nodeType&&t&&(d=v+(e.left>=(rect.left+rect.right)/2?1:0));continue}}else rect.top>e.top&&!o&&rect.left<=e.left&&rect.right>=e.left&&(o=y,l={left:Math.max(rect.left,Math.min(rect.right,e.left)),top:rect.top});!n&&(e.left>=rect.right&&e.top>=rect.top||e.left>=rect.left&&e.top>=rect.bottom)&&(d=v+1)}}return!n&&o&&(n=o,r=l,c=0),n&&3==n.nodeType?function(t,e){let n=t.nodeValue.length,r=document.createRange();for(let i=0;i<n;i++){r.setEnd(t,i+1),r.setStart(t,i);let rect=et(r,1);if(rect.top!=rect.bottom&&X(e,rect))return{node:t,offset:i+(e.left>=(rect.left+rect.right)/2?1:0)}}return{node:t,offset:0}}(n,r):!n||c&&1==n.nodeType?{node:t,offset:d}:Y(n,r)}function X(t,rect){return t.left>=rect.left-1&&t.left<=rect.right+1&&t.top>=rect.top-1&&t.top<=rect.bottom+1}function Q(element,t,e){let n=element.childNodes.length;if(n&&e.top<e.bottom)for(let r=Math.max(0,Math.min(n-1,Math.floor(n*(t.top-e.top)/(e.bottom-e.top))-2)),i=r;;){let e=element.childNodes[i];if(1==e.nodeType){let n=e.getClientRects();for(let r=0;r<n.length;r++){let rect=n[r];if(X(t,rect))return Q(e,t,rect)}}if((i=(i+1)%n)==r)break}return element}function Z(view,t){let e,n=view.dom.ownerDocument,r=0,o=function(t,e,n){if(t.caretPositionFromPoint)try{let r=t.caretPositionFromPoint(e,n);if(r)return{node:r.offsetNode,offset:Math.min(w(r.offsetNode),r.offset)}}catch(t){}if(t.caretRangeFromPoint){let r=t.caretRangeFromPoint(e,n);if(r)return{node:r.startContainer,offset:Math.min(w(r.startContainer),r.startOffset)}}}(n,t.left,t.top);o&&({node:e,offset:r}=o);let l,c=(view.root.elementFromPoint?view.root:n).elementFromPoint(t.left,t.top);if(!c||!view.dom.contains(1!=c.nodeType?c.parentNode:c)){let e=view.dom.getBoundingClientRect();if(!X(t,e))return null;if(c=Q(view.dom,t,e),!c)return null}if(I)for(let p=c;e&&p;p=d(p))p.draggable&&(e=void 0);if(c=function(t,e){let n=t.parentNode;return n&&/^li$/i.test(n.nodeName)&&e.left<t.getBoundingClientRect().left?n:t}(c,t),e){if(D&&1==e.nodeType&&(r=Math.min(r,e.childNodes.length),r<e.childNodes.length)){let n,o=e.childNodes[r];"IMG"==o.nodeName&&(n=o.getBoundingClientRect()).right<=t.left&&n.bottom>t.top&&r++}let n;H&&r&&1==e.nodeType&&1==(n=e.childNodes[r-1]).nodeType&&"false"==n.contentEditable&&n.getBoundingClientRect().top>=t.top&&r--,e==view.dom&&r==e.childNodes.length-1&&1==e.lastChild.nodeType&&t.top>e.lastChild.getBoundingClientRect().bottom?l=view.state.doc.content.size:0!=r&&1==e.nodeType&&"BR"==e.childNodes[r-1].nodeName||(l=function(view,t,e,n){let r=-1;for(let e=t,o=!1;e!=view.dom;){let rect,desc=view.docView.nearestDesc(e,!0);if(!desc)return null;if(1==desc.dom.nodeType&&(desc.node.isBlock&&desc.parent||!desc.contentDOM)&&((rect=desc.dom.getBoundingClientRect()).width||rect.height)&&(desc.node.isBlock&&desc.parent&&(!o&&rect.left>n.left||rect.top>n.top?r=desc.posBefore:(!o&&rect.right<n.left||rect.bottom<n.top)&&(r=desc.posAfter),o=!0),!desc.contentDOM&&r<0&&!desc.node.isText))return(desc.node.isBlock?n.top<(rect.top+rect.bottom)/2:n.left<(rect.left+rect.right)/2)?desc.posBefore:desc.posAfter;e=desc.dom.parentNode}return r>-1?r:view.docView.posFromDOM(t,e,-1)}(view,e,r,t))}null==l&&(l=function(view,t,e){let{node:n,offset:r}=Y(t,e),o=-1;if(1==n.nodeType&&!n.firstChild){let rect=n.getBoundingClientRect();o=rect.left!=rect.right&&e.left>(rect.left+rect.right)/2?1:-1}return view.docView.posFromDOM(n,r,o)}(view,c,t));let desc=view.docView.nearestDesc(c,!0);return{pos:l,inside:desc?desc.posAtStart-desc.border:-1}}function tt(rect){return rect.top<rect.bottom||rect.left<rect.right}function et(t,e){let n=t.getClientRects();if(n.length){let t=n[e<0?0:n.length-1];if(tt(t))return t}return Array.prototype.find.call(n,tt)||t.getBoundingClientRect()}const nt=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function ot(view,t,e){let{node:n,offset:r,atom:o}=view.docView.domFromPos(t,e<0?-1:1),l=H||D;if(3==n.nodeType){if(!l||!nt.test(n.nodeValue)&&(e<0?r:r!=n.nodeValue.length)){let t=r,o=r,l=e<0?1:-1;return e<0&&!r?(o++,l=-1):e>=0&&r==n.nodeValue.length?(t--,l=1):e<0?t--:o++,it(et(f(n,t,o),l),l<0)}{let rect=et(f(n,r,r),e);if(D&&r&&/\s/.test(n.nodeValue[r-1])&&r<n.nodeValue.length){let t=et(f(n,r-1,r-1),-1);if(t.top==rect.top){let e=et(f(n,r,r+1),-1);if(e.top!=rect.top)return it(e,e.left<t.left)}}return rect}}if(!view.state.doc.resolve(t-(o||0)).parent.inlineContent){if(null==o&&r&&(e<0||r==w(n))){let t=n.childNodes[r-1];if(1==t.nodeType)return st(t.getBoundingClientRect(),!1)}if(null==o&&r<w(n)){let t=n.childNodes[r];if(1==t.nodeType)return st(t.getBoundingClientRect(),!0)}return st(n.getBoundingClientRect(),e>=0)}if(null==o&&r&&(e<0||r==w(n))){let t=n.childNodes[r-1],e=3==t.nodeType?f(t,w(t)-(l?0:1)):1!=t.nodeType||"BR"==t.nodeName&&t.nextSibling?null:t;if(e)return it(et(e,1),!1)}if(null==o&&r<w(n)){let t=n.childNodes[r];for(;t.pmViewDesc&&t.pmViewDesc.ignoreForCoords;)t=t.nextSibling;let e=t?3==t.nodeType?f(t,0,l?0:1):1==t.nodeType?t:null:null;if(e)return it(et(e,-1),!0)}return it(et(3==n.nodeType?f(n):n,-e),e>=0)}function it(rect,t){if(0==rect.width)return rect;let e=t?rect.left:rect.right;return{top:rect.top,bottom:rect.bottom,left:e,right:e}}function st(rect,t){if(0==rect.height)return rect;let e=t?rect.top:rect.bottom;return{top:e,bottom:e,left:rect.left,right:rect.right}}function at(view,t,e){let n=view.state,r=view.root.activeElement;n!=t&&view.updateState(t),r!=view.dom&&view.focus();try{return e()}finally{n!=t&&view.updateState(n),r!=view.dom&&r&&r.focus()}}const lt=/[\u0590-\u08ac]/;let ct=null,ht=null,pt=!1;function ut(view,t,e){return ct==t&&ht==e?pt:(ct=t,ht=e,pt="up"==e||"down"==e?function(view,t,e){let n=t.selection,r="up"==e?n.$from:n.$to;return at(view,t,(()=>{let{node:t}=view.docView.domFromPos(r.pos,"up"==e?-1:1);for(;;){let e=view.docView.nearestDesc(t,!0);if(!e)break;if(e.node.isBlock){t=e.contentDOM||e.dom;break}t=e.dom.parentNode}let n=ot(view,r.pos,1);for(let r=t.firstChild;r;r=r.nextSibling){let t;if(1==r.nodeType)t=r.getClientRects();else{if(3!=r.nodeType)continue;t=f(r,0,r.nodeValue.length).getClientRects()}for(let i=0;i<t.length;i++){let r=t[i];if(r.bottom>r.top+1&&("up"==e?n.top-r.top>2*(r.bottom-n.top):r.bottom-n.bottom>2*(n.bottom-r.top)))return!1}}return!0}))}(view,t,e):function(view,t,e){let{$head:n}=t.selection;if(!n.parent.isTextblock)return!1;let r=n.parentOffset,o=!r,l=r==n.parent.content.size,c=view.domSelection();return c?lt.test(n.parent.textContent)&&c.modify?at(view,t,(()=>{let{focusNode:t,focusOffset:r,anchorNode:o,anchorOffset:l}=view.domSelectionRange(),d=c.caretBidiLevel;c.modify("move",e,"character");let h=n.depth?view.docView.domAfterPos(n.before()):view.dom,{focusNode:f,focusOffset:m}=view.domSelectionRange(),y=f&&!h.contains(1==f.nodeType?f:f.parentNode)||t==f&&r==m;try{c.collapse(o,l),t&&(t!=o||r!=l)&&c.extend&&c.extend(t,r)}catch(t){}return null!=d&&(c.caretBidiLevel=d),y})):"left"==e||"backward"==e?o:l:n.pos==n.start()||n.pos==n.end()}(view,t,e))}class ft{constructor(t,e,n,r){this.parent=t,this.children=e,this.dom=n,this.contentDOM=r,this.dirty=0,n.pmViewDesc=this}matchesWidget(t){return!1}matchesMark(mark){return!1}matchesNode(t,e,n){return!1}matchesHack(t){return!1}parseRule(){return null}stopEvent(t){return!1}get size(){let t=0;for(let i=0;i<this.children.length;i++)t+=this.children[i].size;return t}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let i=0;i<this.children.length;i++)this.children[i].destroy()}posBeforeChild(t){for(let i=0,e=this.posAtStart;;i++){let n=this.children[i];if(n==t)return e;e+=n.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(t,e,n){if(this.contentDOM&&this.contentDOM.contains(1==t.nodeType?t:t.parentNode)){if(n<0){let n,desc;if(t==this.contentDOM)n=t.childNodes[e-1];else{for(;t.parentNode!=this.contentDOM;)t=t.parentNode;n=t.previousSibling}for(;n&&(!(desc=n.pmViewDesc)||desc.parent!=this);)n=n.previousSibling;return n?this.posBeforeChild(desc)+desc.size:this.posAtStart}{let n,desc;if(t==this.contentDOM)n=t.childNodes[e];else{for(;t.parentNode!=this.contentDOM;)t=t.parentNode;n=t.nextSibling}for(;n&&(!(desc=n.pmViewDesc)||desc.parent!=this);)n=n.nextSibling;return n?this.posBeforeChild(desc):this.posAtEnd}}let r;if(t==this.dom&&this.contentDOM)r=e>c(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))r=2&t.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==e)for(let e=t;;e=e.parentNode){if(e==this.dom){r=!1;break}if(e.previousSibling)break}if(null==r&&e==t.childNodes.length)for(let e=t;;e=e.parentNode){if(e==this.dom){r=!0;break}if(e.nextSibling)break}}return(null==r?n>0:r)?this.posAtEnd:this.posAtStart}nearestDesc(t,e=!1){for(let n=!0,r=t;r;r=r.parentNode){let o,desc=this.getDesc(r);if(desc&&(!e||desc.node)){if(!n||!(o=desc.nodeDOM)||(1==o.nodeType?o.contains(1==t.nodeType?t:t.parentNode):o==t))return desc;n=!1}}}getDesc(t){let desc=t.pmViewDesc;for(let t=desc;t;t=t.parent)if(t==this)return desc}posFromDOM(t,e,n){for(let r=t;r;r=r.parentNode){let desc=this.getDesc(r);if(desc)return desc.localPosFromDOM(t,e,n)}return-1}descAt(t){for(let i=0,e=0;i<this.children.length;i++){let n=this.children[i],r=e+n.size;if(e==t&&r!=e){for(;!n.border&&n.children.length;)for(let i=0;i<n.children.length;i++){let t=n.children[i];if(t.size){n=t;break}}return n}if(t<r)return n.descAt(t-e-n.border);e=r}}domFromPos(t,e){if(!this.contentDOM)return{node:this.dom,offset:0,atom:t+1};let n,i=0,r=0;for(let e=0;i<this.children.length;i++){let n=this.children[i],o=e+n.size;if(o>t||n instanceof kt){r=t-e;break}e=o}if(r)return this.children[i].domFromPos(r-this.children[i].border,e);for(;i&&!(n=this.children[i-1]).size&&n instanceof mt&&n.side>=0;i--);if(e<=0){let t,n=!0;for(;t=i?this.children[i-1]:null,t&&t.dom.parentNode!=this.contentDOM;i--,n=!1);return t&&e&&n&&!t.border&&!t.domAtom?t.domFromPos(t.size,e):{node:this.contentDOM,offset:t?c(t.dom)+1:0}}{let t,n=!0;for(;t=i<this.children.length?this.children[i]:null,t&&t.dom.parentNode!=this.contentDOM;i++,n=!1);return t&&n&&!t.border&&!t.domAtom?t.domFromPos(0,e):{node:this.contentDOM,offset:t?c(t.dom):this.contentDOM.childNodes.length}}}parseRange(t,e,base=0){if(0==this.children.length)return{node:this.contentDOM,from:t,to:e,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let n=-1,r=-1;for(let o=base,i=0;;i++){let l=this.children[i],d=o+l.size;if(-1==n&&t<=d){let r=o+l.border;if(t>=r&&e<=d-l.border&&l.node&&l.contentDOM&&this.contentDOM.contains(l.contentDOM))return l.parseRange(t,e,r);t=o;for(let e=i;e>0;e--){let r=this.children[e-1];if(r.size&&r.dom.parentNode==this.contentDOM&&!r.emptyChildAt(1)){n=c(r.dom)+1;break}t-=r.size}-1==n&&(n=0)}if(n>-1&&(d>e||i==this.children.length-1)){e=d;for(let t=i+1;t<this.children.length;t++){let n=this.children[t];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(-1)){r=c(n.dom);break}e+=n.size}-1==r&&(r=this.contentDOM.childNodes.length);break}o=d}return{node:this.contentDOM,from:t,to:e,fromOffset:n,toOffset:r}}emptyChildAt(t){if(this.border||!this.contentDOM||!this.children.length)return!1;let e=this.children[t<0?0:this.children.length-1];return 0==e.size||e.emptyChildAt(t)}domAfterPos(t){let{node:e,offset:n}=this.domFromPos(t,0);if(1!=e.nodeType||n==e.childNodes.length)throw new RangeError("No node after pos "+t);return e.childNodes[n]}setSelection(t,head,view,e=!1){let n=Math.min(t,head),r=Math.max(t,head);for(let i=0,o=0;i<this.children.length;i++){let l=this.children[i],c=o+l.size;if(n>o&&r<c)return l.setSelection(t-o-l.border,head-o-l.border,view,e);o=c}let o=this.domFromPos(t,t?-1:1),l=head==t?o:this.domFromPos(head,head?-1:1),d=view.root.getSelection(),h=view.domSelectionRange(),f=!1;if((D||I)&&t==head){let{node:t,offset:e}=o;if(3==t.nodeType){if(f=!(!e||"\n"!=t.nodeValue[e-1]),f&&e==t.nodeValue.length)for(let e,n=t;n;n=n.parentNode){if(e=n.nextSibling){"BR"==e.nodeName&&(o=l={node:e.parentNode,offset:c(e)+1});break}let desc=n.pmViewDesc;if(desc&&desc.node&&desc.node.isBlock)break}}else{let n=t.childNodes[e-1];f=n&&("BR"==n.nodeName||"false"==n.contentEditable)}}if(D&&h.focusNode&&h.focusNode!=l.node&&1==h.focusNode.nodeType){let t=h.focusNode.childNodes[h.focusOffset];t&&"false"==t.contentEditable&&(e=!0)}if(!(e||f&&I)&&m(o.node,o.offset,h.anchorNode,h.anchorOffset)&&m(l.node,l.offset,h.focusNode,h.focusOffset))return;let y=!1;if((d.extend||t==head)&&!f){d.collapse(o.node,o.offset);try{t!=head&&d.extend(l.node,l.offset),y=!0}catch(t){}}if(!y){if(t>head){let t=o;o=l,l=t}let e=document.createRange();e.setEnd(l.node,l.offset),e.setStart(o.node,o.offset),d.removeAllRanges(),d.addRange(e)}}ignoreMutation(t){return!this.contentDOM&&"selection"!=t.type}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(t,e){for(let n=0,i=0;i<this.children.length;i++){let r=this.children[i],o=n+r.size;if(n==o?t<=o&&e>=n:t<o&&e>n){let l=n+r.border,c=o-r.border;if(t>=l&&e<=c)return this.dirty=t==n||e==o?2:1,void(t!=l||e!=c||!r.contentLost&&r.dom.parentNode==this.contentDOM?r.markDirty(t-l,e-l):r.dirty=3);r.dirty=r.dom!=r.contentDOM||r.dom.parentNode!=this.contentDOM||r.children.length?3:2}n=o}this.dirty=2}markParentsDirty(){let t=1;for(let e=this.parent;e;e=e.parent,t++){let n=1==t?2:1;e.dirty<n&&(e.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}get ignoreForSelection(){return!1}isText(text){return!1}}class mt extends ft{constructor(t,e,view,n){let r,o=e.type.toDOM;if("function"==typeof o&&(o=o(view,(()=>r?r.parent?r.parent.posBeforeChild(r):void 0:n))),!e.type.spec.raw){if(1!=o.nodeType){let t=document.createElement("span");t.appendChild(o),o=t}o.contentEditable="false",o.classList.add("ProseMirror-widget")}super(t,[],o,null),this.widget=e,this.widget=e,r=this}matchesWidget(t){return 0==this.dirty&&t.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(t){let e=this.widget.spec.stopEvent;return!!e&&e(t)}ignoreMutation(t){return"selection"!=t.type||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get ignoreForSelection(){return!!this.widget.type.spec.relaxedSide}get side(){return this.widget.type.side}}class gt extends ft{constructor(t,e,n,text){super(t,[],e,null),this.textDOM=n,this.text=text}get size(){return this.text.length}localPosFromDOM(t,e){return t!=this.textDOM?this.posAtStart+(e?this.size:0):this.posAtStart+e}domFromPos(t){return{node:this.textDOM,offset:t}}ignoreMutation(t){return"characterData"===t.type&&t.target.nodeValue==t.oldValue}}class yt extends ft{constructor(t,mark,e,n,r){super(t,[],e,n),this.mark=mark,this.spec=r}static create(t,mark,e,view){let n=view.nodeViews[mark.type.name],r=n&&n(mark,view,e);return r&&r.dom||(r=o.b.renderSpec(document,mark.type.spec.toDOM(mark,e),null,mark.attrs)),new yt(t,mark,r.dom,r.contentDOM||r.dom,r)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(mark){return 3!=this.dirty&&this.mark.eq(mark)}markDirty(t,e){if(super.markDirty(t,e),0!=this.dirty){let t=this.parent;for(;!t.node;)t=t.parent;t.dirty<this.dirty&&(t.dirty=this.dirty),this.dirty=0}}slice(t,e,view){let n=yt.create(this.parent,this.mark,!0,view),r=this.children,o=this.size;e<o&&(r=jt(r,e,o,view)),t>0&&(r=jt(r,0,t,view));for(let i=0;i<r.length;i++)r[i].parent=n;return n.children=r,n}ignoreMutation(t){return this.spec.ignoreMutation?this.spec.ignoreMutation(t):super.ignoreMutation(t)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class vt extends ft{constructor(t,e,n,r,o,l,c,view,d){super(t,[],o,l),this.node=e,this.outerDeco=n,this.innerDeco=r,this.nodeDOM=c}static create(t,e,n,r,view,l){let c,d=view.nodeViews[e.type.name],h=d&&d(e,view,(()=>c?c.parent?c.parent.posBeforeChild(c):void 0:l),n,r),f=h&&h.dom,m=h&&h.contentDOM;if(e.isText)if(f){if(3!=f.nodeType)throw new RangeError("Text must be rendered as a DOM text node")}else f=document.createTextNode(e.text);else if(!f){let t=o.b.renderSpec(document,e.type.spec.toDOM(e),null,e.attrs);({dom:f,contentDOM:m}=t)}m||e.isText||"BR"==f.nodeName||(f.hasAttribute("contenteditable")||(f.contentEditable="false"),e.type.spec.draggable&&(f.draggable=!0));let y=f;return f=At(f,n,e),h?c=new xt(t,e,n,r,f,m||null,y,h,view,l+1):e.isText?new wt(t,e,n,r,f,y,view):new vt(t,e,n,r,f,m||null,y,view,l+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let t={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(t.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let i=this.children.length-1;i>=0;i--){let e=this.children[i];if(this.dom.contains(e.dom.parentNode)){t.contentElement=e.dom.parentNode;break}}t.contentElement||(t.getContent=()=>o.c.empty)}else t.contentElement=this.contentDOM;else t.getContent=()=>this.node.content;return t}matchesNode(t,e,n){return 0==this.dirty&&t.eq(this.node)&&Nt(e,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(view,t){let e=this.node.inlineContent,n=t,r=view.composing?this.localCompositionInfo(view,t):null,l=r&&r.pos>-1?r:null,c=r&&r.pos<0,d=new Pt(this,l&&l.node,view);!function(t,e,n,r){let o=e.locals(t),l=0;if(0==o.length){for(let i=0;i<t.childCount;i++){let n=t.child(i);r(n,o,e.forChild(l,n),i),l+=n.nodeSize}return}let c=0,d=[],h=null;for(let f=0;;){let m,y,v,w;for(;c<o.length&&o[c].to==l;){let t=o[c++];t.widget&&(m?(y||(y=[m])).push(t):m=t)}if(m)if(y){y.sort(Rt);for(let i=0;i<y.length;i++)n(y[i],f,!!h)}else n(m,f,!!h);if(h)w=-1,v=h,h=null;else{if(!(f<t.childCount))break;w=f,v=t.child(f++)}for(let i=0;i<d.length;i++)d[i].to<=l&&d.splice(i--,1);for(;c<o.length&&o[c].from<=l&&o[c].to>l;)d.push(o[c++]);let k=l+v.nodeSize;if(v.isText){let t=k;c<o.length&&o[c].from<t&&(t=o[c].from);for(let i=0;i<d.length;i++)d[i].to<t&&(t=d[i].to);t<k&&(h=v.cut(t-l),v=v.cut(0,t-l),k=t,w=-1)}else for(;c<o.length&&o[c].to<k;)c++;r(v,v.isInline&&!v.isLeaf?d.filter((t=>!t.inline)):d.slice(),e.forChild(l,v),w),l=k}}(this.node,this.innerDeco,((t,i,r)=>{t.spec.marks?d.syncToMarks(t.spec.marks,e,view):t.type.side>=0&&!r&&d.syncToMarks(i==this.node.childCount?o.d.none:this.node.child(i).marks,e,view),d.placeWidget(t,view,n)}),((t,o,l,i)=>{let h;d.syncToMarks(t.marks,e,view),d.findNodeMatch(t,o,l,i)||c&&view.state.selection.from>n&&view.state.selection.to<n+t.nodeSize&&(h=d.findIndexWithChild(r.node))>-1&&d.updateNodeAt(t,o,l,h,view)||d.updateNextNode(t,o,l,view,i,n)||d.addNode(t,o,l,view,n),n+=t.nodeSize})),d.syncToMarks([],e,view),this.node.isTextblock&&d.addTextblockHacks(),d.destroyRest(),(d.changed||2==this.dirty)&&(l&&this.protectLocalComposition(view,l),Ot(this.contentDOM,this.children,view),L&&function(t){if("UL"==t.nodeName||"OL"==t.nodeName){let e=t.style.cssText;t.style.cssText=e+"; list-style: square !important",window.getComputedStyle(t).listStyle,t.style.cssText=e}}(this.dom))}localCompositionInfo(view,t){let{from:e,to:n}=view.state.selection;if(!(view.state.selection instanceof r.h)||e<t||n>t+this.node.content.size)return null;let o=view.input.compositionNode;if(!o||!this.dom.contains(o.parentNode))return null;if(this.node.inlineContent){let text=o.nodeValue,r=function(t,text,e,n){for(let i=0,r=0;i<t.childCount&&r<=n;){let o=t.child(i++),l=r;if(r+=o.nodeSize,!o.isText)continue;let c=o.text;for(;i<t.childCount;){let e=t.child(i++);if(r+=e.nodeSize,!e.isText)break;c+=e.text}if(r>=e){if(r>=n&&c.slice(n-text.length-l,n-l)==text)return n-text.length;let t=l<n?c.lastIndexOf(text,n-l-1):-1;if(t>=0&&t+text.length+l>=e)return l+t;if(e==n&&c.length>=n+text.length-l&&c.slice(n-l,n-l+text.length)==text)return n}}return-1}(this.node.content,text,e-t,n-t);return r<0?null:{node:o,pos:r,text:text}}return{node:o,pos:-1,text:""}}protectLocalComposition(view,{node:t,pos:e,text:text}){if(this.getDesc(t))return;let n=t;for(;n.parentNode!=this.contentDOM;n=n.parentNode){for(;n.previousSibling;)n.parentNode.removeChild(n.previousSibling);for(;n.nextSibling;)n.parentNode.removeChild(n.nextSibling);n.pmViewDesc&&(n.pmViewDesc=void 0)}let desc=new gt(this,n,t,text);view.input.compositionNodes.push(desc),this.children=jt(this.children,e,e+text.length,view,desc)}update(t,e,n,view){return!(3==this.dirty||!t.sameMarkup(this.node))&&(this.updateInner(t,e,n,view),!0)}updateInner(t,e,n,view){this.updateOuterDeco(e),this.node=t,this.innerDeco=n,this.contentDOM&&this.updateChildren(view,this.posAtStart),this.dirty=0}updateOuterDeco(t){if(Nt(t,this.outerDeco))return;let e=1!=this.nodeDOM.nodeType,n=this.dom;this.dom=Tt(this.dom,this.nodeDOM,Ct(this.outerDeco,this.node,e),Ct(t,this.node,e)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=t}selectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||(this.dom.draggable=!0)}deselectNode(){1==this.nodeDOM.nodeType&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function bt(t,e,n,r,view){At(r,e,t);let o=new vt(void 0,t,e,n,r,r,r,view,0);return o.contentDOM&&o.updateChildren(view,0),o}class wt extends vt{constructor(t,e,n,r,o,l,view){super(t,e,n,r,o,null,l,view,0)}parseRule(){let t=this.nodeDOM.parentNode;for(;t&&t!=this.dom&&!t.pmIsDeco;)t=t.parentNode;return{skip:t||!0}}update(t,e,n,view){return!(3==this.dirty||0!=this.dirty&&!this.inParent()||!t.sameMarkup(this.node))&&(this.updateOuterDeco(e),0==this.dirty&&t.text==this.node.text||t.text==this.nodeDOM.nodeValue||(this.nodeDOM.nodeValue=t.text,view.trackWrites==this.nodeDOM&&(view.trackWrites=null)),this.node=t,this.dirty=0,!0)}inParent(){let t=this.parent.contentDOM;for(let e=this.nodeDOM;e;e=e.parentNode)if(e==t)return!0;return!1}domFromPos(t){return{node:this.nodeDOM,offset:t}}localPosFromDOM(t,e,n){return t==this.nodeDOM?this.posAtStart+Math.min(e,this.node.text.length):super.localPosFromDOM(t,e,n)}ignoreMutation(t){return"characterData"!=t.type&&"selection"!=t.type}slice(t,e,view){let n=this.node.cut(t,e),r=document.createTextNode(n.text);return new wt(this.parent,n,this.outerDeco,this.innerDeco,r,r,view)}markDirty(t,e){super.markDirty(t,e),this.dom==this.nodeDOM||0!=t&&e!=this.nodeDOM.nodeValue.length||(this.dirty=3)}get domAtom(){return!1}isText(text){return this.node.text==text}}class kt extends ft{parseRule(){return{ignore:!0}}matchesHack(t){return 0==this.dirty&&this.dom.nodeName==t}get domAtom(){return!0}get ignoreForCoords(){return"IMG"==this.dom.nodeName}}class xt extends vt{constructor(t,e,n,r,o,l,c,d,view,h){super(t,e,n,r,o,l,c,view,h),this.spec=d}update(t,e,n,view){if(3==this.dirty)return!1;if(this.spec.update&&(this.node.type==t.type||this.spec.multiType)){let r=this.spec.update(t,e,n);return r&&this.updateInner(t,e,n,view),r}return!(!this.contentDOM&&!t.isLeaf)&&super.update(t,e,n,view)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(t,head,view,e){this.spec.setSelection?this.spec.setSelection(t,head,view.root):super.setSelection(t,head,view,e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(t){return!!this.spec.stopEvent&&this.spec.stopEvent(t)}ignoreMutation(t){return this.spec.ignoreMutation?this.spec.ignoreMutation(t):super.ignoreMutation(t)}}function Ot(t,e,view){let n=t.firstChild,r=!1;for(let i=0;i<e.length;i++){let desc=e[i],o=desc.dom;if(o.parentNode==t){for(;o!=n;)n=Dt(n),r=!0;n=n.nextSibling}else r=!0,t.insertBefore(o,n);if(desc instanceof yt){let e=n?n.previousSibling:t.lastChild;Ot(desc.contentDOM,desc.children,view),n=e?e.nextSibling:t.firstChild}}for(;n;)n=Dt(n),r=!0;r&&view.trackWrites==t&&(view.trackWrites=null)}const St=function(t){t&&(this.nodeName=t)};St.prototype=Object.create(null);const Mt=[new St];function Ct(t,e,n){if(0==t.length)return Mt;let r=n?Mt[0]:new St,o=[r];for(let i=0;i<t.length;i++){let l=t[i].type.attrs;if(l){l.nodeName&&o.push(r=new St(l.nodeName));for(let t in l){let c=l[t];null!=c&&(n&&1==o.length&&o.push(r=new St(e.isInline?"span":"div")),"class"==t?r.class=(r.class?r.class+" ":"")+c:"style"==t?r.style=(r.style?r.style+";":"")+c:"nodeName"!=t&&(r[t]=c))}}}return o}function Tt(t,e,n,r){if(n==Mt&&r==Mt)return e;let o=e;for(let i=0;i<r.length;i++){let e=r[i],l=n[i];if(i){let n;l&&l.nodeName==e.nodeName&&o!=t&&(n=o.parentNode)&&n.nodeName.toLowerCase()==e.nodeName||(n=document.createElement(e.nodeName),n.pmIsDeco=!0,n.appendChild(o),l=Mt[0]),o=n}Et(o,l||Mt[0],e)}return o}function Et(t,e,n){for(let r in e)"class"==r||"style"==r||"nodeName"==r||r in n||t.removeAttribute(r);for(let r in n)"class"!=r&&"style"!=r&&"nodeName"!=r&&n[r]!=e[r]&&t.setAttribute(r,n[r]);if(e.class!=n.class){let r=e.class?e.class.split(" ").filter(Boolean):[],o=n.class?n.class.split(" ").filter(Boolean):[];for(let i=0;i<r.length;i++)-1==o.indexOf(r[i])&&t.classList.remove(r[i]);for(let i=0;i<o.length;i++)-1==r.indexOf(o[i])&&t.classList.add(o[i]);0==t.classList.length&&t.removeAttribute("class")}if(e.style!=n.style){if(e.style){let n,r=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g;for(;n=r.exec(e.style);)t.style.removeProperty(n[1])}n.style&&(t.style.cssText+=n.style)}}function At(t,e,n){return Tt(t,t,Mt,Ct(e,n,1!=t.nodeType))}function Nt(a,b){if(a.length!=b.length)return!1;for(let i=0;i<a.length;i++)if(!a[i].type.eq(b[i].type))return!1;return!0}function Dt(t){let e=t.nextSibling;return t.parentNode.removeChild(t),e}class Pt{constructor(t,e,view){this.lock=e,this.view=view,this.index=0,this.stack=[],this.changed=!1,this.top=t,this.preMatch=function(t,e){let n=e,r=n.children.length,o=t.childCount,l=new Map,c=[];t:for(;o>0;){let desc;for(;;)if(r){let t=n.children[r-1];if(!(t instanceof yt)){desc=t,r--;break}n=t,r=t.children.length}else{if(n==e)break t;r=n.parent.children.indexOf(n),n=n.parent}let d=desc.node;if(d){if(d!=t.child(o-1))break;--o,l.set(desc,o),c.push(desc)}}return{index:o,matched:l,matches:c.reverse()}}(t.node.content,t)}destroyBetween(t,e){if(t!=e){for(let i=t;i<e;i++)this.top.children[i].destroy();this.top.children.splice(t,e-t),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(t,e,view){let n=0,r=this.stack.length>>1,o=Math.min(r,t.length);for(;n<o&&(n==r-1?this.top:this.stack[n+1<<1]).matchesMark(t[n])&&!1!==t[n].type.spec.spanning;)n++;for(;n<r;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),r--;for(;r<t.length;){this.stack.push(this.top,this.index+1);let n=-1;for(let i=this.index;i<Math.min(this.index+3,this.top.children.length);i++){let e=this.top.children[i];if(e.matchesMark(t[r])&&!this.isLocked(e.dom)){n=i;break}}if(n>-1)n>this.index&&(this.changed=!0,this.destroyBetween(this.index,n)),this.top=this.top.children[this.index];else{let n=yt.create(this.top,t[r],e,view);this.top.children.splice(this.index,0,n),this.top=n,this.changed=!0}this.index=0,r++}}findNodeMatch(t,e,n,r){let o,l=-1;if(r>=this.preMatch.index&&(o=this.preMatch.matches[r-this.preMatch.index]).parent==this.top&&o.matchesNode(t,e,n))l=this.top.children.indexOf(o,this.index);else for(let i=this.index,r=Math.min(this.top.children.length,i+5);i<r;i++){let r=this.top.children[i];if(r.matchesNode(t,e,n)&&!this.preMatch.matched.has(r)){l=i;break}}return!(l<0)&&(this.destroyBetween(this.index,l),this.index++,!0)}updateNodeAt(t,e,n,r,view){let o=this.top.children[r];return 3==o.dirty&&o.dom==o.contentDOM&&(o.dirty=2),!!o.update(t,e,n,view)&&(this.destroyBetween(this.index,r),this.index++,!0)}findIndexWithChild(t){for(;;){let e=t.parentNode;if(!e)return-1;if(e==this.top.contentDOM){let desc=t.pmViewDesc;if(desc)for(let i=this.index;i<this.top.children.length;i++)if(this.top.children[i]==desc)return i;return-1}t=e}}updateNextNode(t,e,n,view,r,o){for(let i=this.index;i<this.top.children.length;i++){let l=this.top.children[i];if(l instanceof vt){let c=this.preMatch.matched.get(l);if(null!=c&&c!=r)return!1;let d,h=l.dom,f=this.isLocked(h)&&!(t.isText&&l.node&&l.node.isText&&l.nodeDOM.nodeValue==t.text&&3!=l.dirty&&Nt(e,l.outerDeco));if(!f&&l.update(t,e,n,view))return this.destroyBetween(this.index,i),l.dom!=h&&(this.changed=!0),this.index++,!0;if(!f&&(d=this.recreateWrapper(l,t,e,n,view,o)))return this.destroyBetween(this.index,i),this.top.children[this.index]=d,d.contentDOM&&(d.dirty=2,d.updateChildren(view,o+1),d.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(t,e,n,r,view,o){if(t.dirty||e.isAtom||!t.children.length||!t.node.content.eq(e.content)||!Nt(n,t.outerDeco)||!r.eq(t.innerDeco))return null;let l=vt.create(this.top,e,n,r,view,o);if(l.contentDOM){l.children=t.children,t.children=[];for(let t of l.children)t.parent=l}return t.destroy(),l}addNode(t,e,n,view,r){let desc=vt.create(this.top,t,e,n,view,r);desc.contentDOM&&desc.updateChildren(view,r+1),this.top.children.splice(this.index++,0,desc),this.changed=!0}placeWidget(t,view,e){let n=this.index<this.top.children.length?this.top.children[this.index]:null;if(!n||!n.matchesWidget(t)||t!=n.widget&&n.widget.type.toDOM.parentNode){let desc=new mt(this.top,t,view,e);this.top.children.splice(this.index++,0,desc),this.changed=!0}else this.index++}addTextblockHacks(){let t=this.top.children[this.index-1],e=this.top;for(;t instanceof yt;)e=t,t=e.children[e.children.length-1];(!t||!(t instanceof wt)||/\n$/.test(t.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(t.node.text))&&((I||R)&&t&&"false"==t.dom.contentEditable&&this.addHackNode("IMG",e),this.addHackNode("BR",this.top))}addHackNode(t,e){if(e==this.top&&this.index<e.children.length&&e.children[this.index].matchesHack(t))this.index++;else{let n=document.createElement(t);"IMG"==t&&(n.className="ProseMirror-separator",n.alt=""),"BR"==t&&(n.className="ProseMirror-trailingBreak");let r=new kt(this.top,[],n,null);e!=this.top?e.children.push(r):e.children.splice(this.index++,0,r),this.changed=!0}}isLocked(t){return this.lock&&(t==this.lock||1==t.nodeType&&t.contains(this.lock.parentNode))}}function Rt(a,b){return a.type.side-b.type.side}function jt(t,e,n,view,r){let o=[];for(let i=0,l=0;i<t.length;i++){let c=t[i],d=l,h=l+=c.size;d>=n||h<=e?o.push(c):(d<e&&o.push(c.slice(0,e-d,view)),r&&(o.push(r),r=void 0),h>n&&o.push(c.slice(n-d,c.size,view)))}return o}function It(view,t=null){let e=view.domSelectionRange(),n=view.state.doc;if(!e.focusNode)return null;let o=view.docView.nearestDesc(e.focusNode),l=o&&0==o.size,head=view.docView.posFromDOM(e.focusNode,e.focusOffset,1);if(head<0)return null;let d,h,f=n.resolve(head);if(x(e)){for(d=head;o&&!o.node;)o=o.parent;let t=o.node;if(o&&t.isAtom&&r.c.isSelectable(t)&&o.parent&&(!t.isInline||!function(t,e,n){for(let r=0==e,o=e==w(t);r||o;){if(t==n)return!0;let e=c(t);if(!(t=t.parentNode))return!1;r=r&&0==e,o=o&&e==w(t)}}(e.focusNode,e.focusOffset,o.dom))){let t=o.posBefore;h=new r.c(head==t?f:n.resolve(t))}}else{if(e instanceof view.dom.ownerDocument.defaultView.Selection&&e.rangeCount>1){let t=head,r=head;for(let i=0;i<e.rangeCount;i++){let n=e.getRangeAt(i);t=Math.min(t,view.docView.posFromDOM(n.startContainer,n.startOffset,1)),r=Math.max(r,view.docView.posFromDOM(n.endContainer,n.endOffset,-1))}if(t<0)return null;[d,head]=r==view.state.selection.anchor?[r,t]:[t,r],f=n.resolve(head)}else d=view.docView.posFromDOM(e.anchorNode,e.anchorOffset,1);if(d<0)return null}let m=n.resolve(d);if(!h){h=Wt(view,m,f,"pointer"==t||view.state.selection.head<f.pos&&!l?1:-1)}return h}function Lt(view){return view.editable?view.hasFocus():Kt(view)&&document.activeElement&&document.activeElement.contains(view.dom)}function Bt(view,t=!1){let e=view.state.selection;if(Ft(view,e),Lt(view)){if(!t&&view.input.mouseDown&&view.input.mouseDown.allowDefault&&R){let t=view.domSelectionRange(),e=view.domObserver.currentSelection;if(t.anchorNode&&e.anchorNode&&m(t.anchorNode,t.anchorOffset,e.anchorNode,e.anchorOffset))return view.input.mouseDown.delayedSelectionSync=!0,void view.domObserver.setCurSelection()}if(view.domObserver.disconnectSelection(),view.cursorWrapper)!function(view){let t=view.domSelection(),e=document.createRange();if(!t)return;let n=view.cursorWrapper.dom,img="IMG"==n.nodeName;img?e.setStart(n.parentNode,c(n)+1):e.setStart(n,0);e.collapse(!0),t.removeAllRanges(),t.addRange(e),!img&&!view.state.selection.visible&&A&&N<=11&&(n.disabled=!0,n.disabled=!1)}(view);else{let n,o,{anchor:l,head:head}=e;!$t||e instanceof r.h||(e.$from.parent.inlineContent||(n=zt(view,e.from)),e.empty||e.$from.parent.inlineContent||(o=zt(view,e.to))),view.docView.setSelection(l,head,view,t),$t&&(n&&Vt(n),o&&Vt(o)),e.visible?view.dom.classList.remove("ProseMirror-hideselection"):(view.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&function(view){let t=view.dom.ownerDocument;t.removeEventListener("selectionchange",view.input.hideSelectionGuard);let e=view.domSelectionRange(),n=e.anchorNode,r=e.anchorOffset;t.addEventListener("selectionchange",view.input.hideSelectionGuard=()=>{e.anchorNode==n&&e.anchorOffset==r||(t.removeEventListener("selectionchange",view.input.hideSelectionGuard),setTimeout((()=>{Lt(view)&&!view.state.selection.visible||view.dom.classList.remove("ProseMirror-hideselection")}),20))})}(view))}view.domObserver.setCurSelection(),view.domObserver.connectSelection()}}const $t=I||R&&j<63;function zt(view,t){let{node:e,offset:n}=view.docView.domFromPos(t,0),r=n<e.childNodes.length?e.childNodes[n]:null,o=n?e.childNodes[n-1]:null;if(I&&r&&"false"==r.contentEditable)return Ht(r);if(!(r&&"false"!=r.contentEditable||o&&"false"!=o.contentEditable)){if(r)return Ht(r);if(o)return Ht(o)}}function Ht(element){return element.contentEditable="true",I&&element.draggable&&(element.draggable=!1,element.wasDraggable=!0),element}function Vt(element){element.contentEditable="false",element.wasDraggable&&(element.draggable=!0,element.wasDraggable=null)}function Ft(view,t){if(t instanceof r.c){let desc=view.docView.descAt(t.from);desc!=view.lastSelectedViewDesc&&(_t(view),desc&&desc.selectNode(),view.lastSelectedViewDesc=desc)}else _t(view)}function _t(view){view.lastSelectedViewDesc&&(view.lastSelectedViewDesc.parent&&view.lastSelectedViewDesc.deselectNode(),view.lastSelectedViewDesc=void 0)}function Wt(view,t,e,n){return view.someProp("createSelectionBetween",(n=>n(view,t,e)))||r.h.between(t,e,n)}function qt(view){return!(view.editable&&!view.hasFocus())&&Kt(view)}function Kt(view){let t=view.domSelectionRange();if(!t.anchorNode)return!1;try{return view.dom.contains(3==t.anchorNode.nodeType?t.anchorNode.parentNode:t.anchorNode)&&(view.editable||view.dom.contains(3==t.focusNode.nodeType?t.focusNode.parentNode:t.focusNode))}catch(t){return!1}}function Jt(t,e){let{$anchor:n,$head:o}=t.selection,l=e>0?n.max(o):n.min(o),c=l.parent.inlineContent?l.depth?t.doc.resolve(e>0?l.after():l.before()):null:l;return c&&r.f.findFrom(c,e)}function Ut(view,t){return view.dispatch(view.state.tr.setSelection(t).scrollIntoView()),!0}function Gt(view,t,e){let n=view.state.selection;if(!(n instanceof r.h)){if(n instanceof r.c&&n.node.isInline)return Ut(view,new r.h(t>0?n.$to:n.$from));{let e=Jt(view.state,t);return!!e&&Ut(view,e)}}if(e.indexOf("s")>-1){let{$head:e}=n,o=e.textOffset?null:t<0?e.nodeBefore:e.nodeAfter;if(!o||o.isText||!o.isLeaf)return!1;let l=view.state.doc.resolve(e.pos+o.nodeSize*(t<0?-1:1));return Ut(view,new r.h(n.$anchor,l))}if(!n.empty)return!1;if(view.endOfTextblock(t>0?"forward":"backward")){let e=Jt(view.state,t);return!!(e&&e instanceof r.c)&&Ut(view,e)}if(!(B&&e.indexOf("m")>-1)){let desc,e=n.$head,o=e.textOffset?null:t<0?e.nodeBefore:e.nodeAfter;if(!o||o.isText)return!1;let l=t<0?e.pos-o.nodeSize:e.pos;return!!(o.isAtom||(desc=view.docView.descAt(l))&&!desc.contentDOM)&&(r.c.isSelectable(o)?Ut(view,new r.c(t<0?view.state.doc.resolve(e.pos-o.nodeSize):e)):!!H&&Ut(view,new r.h(view.state.doc.resolve(t<0?l:l+o.nodeSize))))}}function Yt(t){return 3==t.nodeType?t.nodeValue.length:t.childNodes.length}function Xt(t,e){let desc=t.pmViewDesc;return desc&&0==desc.size&&(e<0||t.nextSibling||"BR"!=t.nodeName)}function Qt(view,t){return t<0?function(view){let t=view.domSelectionRange(),e=t.focusNode,n=t.focusOffset;if(!e)return;let r,o,l=!1;D&&1==e.nodeType&&n<Yt(e)&&Xt(e.childNodes[n],-1)&&(l=!0);for(;;)if(n>0){if(1!=e.nodeType)break;{let t=e.childNodes[n-1];if(Xt(t,-1))r=e,o=--n;else{if(3!=t.nodeType)break;e=t,n=e.nodeValue.length}}}else{if(Zt(e))break;{let t=e.previousSibling;for(;t&&Xt(t,-1);)r=e.parentNode,o=c(t),t=t.previousSibling;if(t)e=t,n=Yt(e);else{if(e=e.parentNode,e==view.dom)break;n=0}}}l?te(view,e,n):r&&te(view,r,o)}(view):function(view){let t=view.domSelectionRange(),e=t.focusNode,n=t.focusOffset;if(!e)return;let r,o,l=Yt(e);for(;;)if(n<l){if(1!=e.nodeType)break;if(!Xt(e.childNodes[n],1))break;r=e,o=++n}else{if(Zt(e))break;{let t=e.nextSibling;for(;t&&Xt(t,1);)r=t.parentNode,o=c(t)+1,t=t.nextSibling;if(t)e=t,n=0,l=Yt(e);else{if(e=e.parentNode,e==view.dom)break;n=l=0}}}r&&te(view,r,o)}(view)}function Zt(t){let desc=t.pmViewDesc;return desc&&desc.node&&desc.node.isBlock}function te(view,t,e){if(3!=t.nodeType){let n,r;(r=function(t,e){for(;t&&e==t.childNodes.length&&!k(t);)e=c(t)+1,t=t.parentNode;for(;t&&e<t.childNodes.length;){let n=t.childNodes[e];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;t=n,e=0}}(t,e))?(t=r,e=0):(n=function(t,e){for(;t&&!e&&!k(t);)e=c(t),t=t.parentNode;for(;t&&e;){let n=t.childNodes[e-1];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;e=(t=n).childNodes.length}}(t,e))&&(t=n,e=n.nodeValue.length)}let n=view.domSelection();if(!n)return;if(x(n)){let r=document.createRange();r.setEnd(t,e),r.setStart(t,e),n.removeAllRanges(),n.addRange(r)}else n.extend&&n.extend(t,e);view.domObserver.setCurSelection();let{state:r}=view;setTimeout((()=>{view.state==r&&Bt(view)}),50)}function ee(view,t){let e=view.state.doc.resolve(t);if(!R&&!$&&e.parent.inlineContent){let n=view.coordsAtPos(t);if(t>e.start()){let e=view.coordsAtPos(t-1),r=(e.top+e.bottom)/2;if(r>n.top&&r<n.bottom&&Math.abs(e.left-n.left)>1)return e.left<n.left?"ltr":"rtl"}if(t<e.end()){let e=view.coordsAtPos(t+1),r=(e.top+e.bottom)/2;if(r>n.top&&r<n.bottom&&Math.abs(e.left-n.left)>1)return e.left>n.left?"ltr":"rtl"}}return"rtl"==getComputedStyle(view.dom).direction?"rtl":"ltr"}function ne(view,t,e){let n=view.state.selection;if(n instanceof r.h&&!n.empty||e.indexOf("s")>-1)return!1;if(B&&e.indexOf("m")>-1)return!1;let{$from:o,$to:l}=n;if(!o.parent.inlineContent||view.endOfTextblock(t<0?"up":"down")){let e=Jt(view.state,t);if(e&&e instanceof r.c)return Ut(view,e)}if(!o.parent.inlineContent){let e=t<0?o:l,c=n instanceof r.a?r.f.near(e,t):r.f.findFrom(e,t);return!!c&&Ut(view,c)}return!1}function re(view,t){if(!(view.state.selection instanceof r.h))return!0;let{$head:e,$anchor:n,empty:o}=view.state.selection;if(!e.sameParent(n))return!0;if(!o)return!1;if(view.endOfTextblock(t>0?"forward":"backward"))return!0;let l=!e.textOffset&&(t<0?e.nodeBefore:e.nodeAfter);if(l&&!l.isText){let tr=view.state.tr;return t<0?tr.delete(e.pos-l.nodeSize,e.pos):tr.delete(e.pos,e.pos+l.nodeSize),view.dispatch(tr),!0}return!1}function oe(view,t,e){view.domObserver.stop(),t.contentEditable=e,view.domObserver.start()}function ie(view,t){let code=t.keyCode,e=function(t){let e="";return t.ctrlKey&&(e+="c"),t.metaKey&&(e+="m"),t.altKey&&(e+="a"),t.shiftKey&&(e+="s"),e}(t);if(8==code||B&&72==code&&"c"==e)return re(view,-1)||Qt(view,-1);if(46==code&&!t.shiftKey||B&&68==code&&"c"==e)return re(view,1)||Qt(view,1);if(13==code||27==code)return!0;if(37==code||B&&66==code&&"c"==e){let t=37==code?"ltr"==ee(view,view.state.selection.from)?-1:1:-1;return Gt(view,t,e)||Qt(view,t)}if(39==code||B&&70==code&&"c"==e){let t=39==code?"ltr"==ee(view,view.state.selection.from)?1:-1:1;return Gt(view,t,e)||Qt(view,t)}return 38==code||B&&80==code&&"c"==e?ne(view,-1,e)||Qt(view,-1):40==code||B&&78==code&&"c"==e?function(view){if(!I||view.state.selection.$head.parentOffset>0)return!1;let{focusNode:t,focusOffset:e}=view.domSelectionRange();if(t&&1==t.nodeType&&0==e&&t.firstChild&&"false"==t.firstChild.contentEditable){let e=t.firstChild;oe(view,e,"true"),setTimeout((()=>oe(view,e,"false")),20)}return!1}(view)||ne(view,1,e)||Qt(view,1):e==(B?"m":"c")&&(66==code||73==code||89==code||90==code)}function se(view,t){view.someProp("transformCopied",(e=>{t=e(t,view)}));let e=[],{content:content,openStart:n,openEnd:r}=t;for(;n>1&&r>1&&1==content.childCount&&1==content.firstChild.childCount;){n--,r--;let t=content.firstChild;e.push(t.type.name,t.attrs!=t.type.defaultAttrs?t.attrs:null),content=t.content}let l=view.someProp("clipboardSerializer")||o.b.fromSchema(view.state.schema),c=ge(),d=c.createElement("div");d.appendChild(l.serializeFragment(content,{document:c}));let h,f=d.firstChild,m=0;for(;f&&1==f.nodeType&&(h=fe[f.nodeName.toLowerCase()]);){for(let i=h.length-1;i>=0;i--){let t=c.createElement(h[i]);for(;d.firstChild;)t.appendChild(d.firstChild);d.appendChild(t),m++}f=d.firstChild}return f&&1==f.nodeType&&f.setAttribute("data-pm-slice",`${n} ${r}${m?` -${m}`:""} ${JSON.stringify(e)}`),{dom:d,text:view.someProp("clipboardTextSerializer",(e=>e(t,view)))||t.content.textBetween(0,t.content.size,"\n\n"),slice:t}}function ae(view,text,html,t,e){let n,r,l=e.parent.type.spec.code;if(!html&&!text)return null;let c=text&&(t||l||!html);if(c){if(view.someProp("transformPastedText",(e=>{text=e(text,l||t,view)})),l)return text?new o.j(o.c.from(view.state.schema.text(text.replace(/\r\n?/g,"\n"))),0,0):o.j.empty;let c=view.someProp("clipboardTextParser",(n=>n(text,e,t,view)));if(c)r=c;else{let t=e.marks(),{schema:r}=view.state,l=o.b.fromSchema(r);n=document.createElement("div"),text.split(/(?:\r\n?|\n)+/).forEach((e=>{let p=n.appendChild(document.createElement("p"));e&&p.appendChild(l.serializeNode(r.text(e,t)))}))}}else view.someProp("transformPastedHTML",(t=>{html=t(html,view)})),n=function(html){let t=/^(\s*<meta [^>]*>)*/.exec(html);t&&(html=html.slice(t[0].length));let e,n=ge().createElement("div"),r=/<([a-z][^>\s]+)/i.exec(html);(e=r&&fe[r[1].toLowerCase()])&&(html=e.map((t=>"<"+t+">")).join("")+html+e.map((t=>"</"+t+">")).reverse().join(""));if(n.innerHTML=function(html){let t=window.trustedTypes;if(!t)return html;ye||(ye=t.defaultPolicy||t.createPolicy("ProseMirrorClipboard",{createHTML:s=>s}));return ye.createHTML(html)}(html),e)for(let i=0;i<e.length;i++)n=n.querySelector(e[i])||n;return n}(html),H&&function(t){let e=t.querySelectorAll(R?"span:not([class]):not([style])":"span.Apple-converted-space");for(let i=0;i<e.length;i++){let n=e[i];1==n.childNodes.length&&" "==n.textContent&&n.parentNode&&n.parentNode.replaceChild(t.ownerDocument.createTextNode(" "),n)}}(n);let d=n&&n.querySelector("[data-pm-slice]"),h=d&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(d.getAttribute("data-pm-slice")||"");if(h&&h[3])for(let i=+h[3];i>0;i--){let t=n.firstChild;for(;t&&1!=t.nodeType;)t=t.nextSibling;if(!t)break;n=t}if(!r){let t=view.someProp("clipboardParser")||view.someProp("domParser")||o.a.fromSchema(view.state.schema);r=t.parseSlice(n,{preserveWhitespace:!(!c&&!h),context:e,ruleFromNode:t=>"BR"!=t.nodeName||t.nextSibling||!t.parentNode||le.test(t.parentNode.nodeName)?null:{ignore:!0}})}if(h)r=function(t,e){if(!t.size)return t;let n,r=t.content.firstChild.type.schema;try{n=JSON.parse(e)}catch(e){return t}let{content:content,openStart:l,openEnd:c}=t;for(let i=n.length-2;i>=0;i-=2){let t=r.nodes[n[i]];if(!t||t.hasRequiredAttrs())break;content=o.c.from(t.create(n[i+1],content)),l++,c++}return new o.j(content,l,c)}(ue(r,+h[1],+h[2]),h[4]);else if(r=o.j.maxOpen(function(t,e){if(t.childCount<2)return t;for(let n=e.depth;n>=0;n--){let r,l=e.node(n).contentMatchAt(e.index(n)),c=[];if(t.forEach((t=>{if(!c)return;let e,n=l.findWrapping(t.type);if(!n)return c=null;if(e=c.length&&r.length&&de(n,r,t,c[c.length-1],0))c[c.length-1]=e;else{c.length&&(c[c.length-1]=he(c[c.length-1],r.length));let e=ce(t,n);c.push(e),l=l.matchType(e.type),r=n}})),c)return o.c.from(c)}return t}(r.content,e),!0),r.openStart||r.openEnd){let t=0,e=0;for(let e=r.content.firstChild;t<r.openStart&&!e.type.spec.isolating;t++,e=e.firstChild);for(let t=r.content.lastChild;e<r.openEnd&&!t.type.spec.isolating;e++,t=t.lastChild);r=ue(r,t,e)}return view.someProp("transformPasted",(t=>{r=t(r,view)})),r}const le=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function ce(t,e,n=0){for(let i=e.length-1;i>=n;i--)t=e[i].create(null,o.c.from(t));return t}function de(t,e,n,r,l){if(l<t.length&&l<e.length&&t[l]==e[l]){let c=de(t,e,n,r.lastChild,l+1);if(c)return r.copy(r.content.replaceChild(r.childCount-1,c));if(r.contentMatchAt(r.childCount).matchType(l==t.length-1?n.type:t[l+1]))return r.copy(r.content.append(o.c.from(ce(n,t,l+1))))}}function he(t,e){if(0==e)return t;let n=t.content.replaceChild(t.childCount-1,he(t.lastChild,e-1)),r=t.contentMatchAt(t.childCount).fillBefore(o.c.empty,!0);return t.copy(n.append(r))}function pe(t,e,n,r,l,c){let d=e<0?t.firstChild:t.lastChild,h=d.content;return t.childCount>1&&(c=0),l<r-1&&(h=pe(h,e,n,r,l+1,c)),l>=n&&(h=e<0?d.contentMatchAt(0).fillBefore(h,c<=l).append(h):h.append(d.contentMatchAt(d.childCount).fillBefore(o.c.empty,!0))),t.replaceChild(e<0?0:t.childCount-1,d.copy(h))}function ue(t,e,n){return e<t.openStart&&(t=new o.j(pe(t.content,-1,e,t.openStart,0,t.openEnd),e,t.openEnd)),n<t.openEnd&&(t=new o.j(pe(t.content,1,n,t.openEnd,0,0),t.openStart,n)),t}const fe={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let me=null;function ge(){return me||(me=document.implementation.createHTMLDocument("title"))}let ye=null;const ve={},be={},we={touchstart:!0,touchmove:!0};class ke{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:"",button:0},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function xe(view,t){view.input.lastSelectionOrigin=t,view.input.lastSelectionTime=Date.now()}function Oe(view){view.someProp("handleDOMEvents",(t=>{for(let e in t)view.input.eventHandlers[e]||view.dom.addEventListener(e,view.input.eventHandlers[e]=t=>Se(view,t))}))}function Se(view,t){return view.someProp("handleDOMEvents",(e=>{let n=e[t.type];return!!n&&(n(view,t)||t.defaultPrevented)}))}function Me(view,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let e=t.target;e!=view.dom;e=e.parentNode)if(!e||11==e.nodeType||e.pmViewDesc&&e.pmViewDesc.stopEvent(t))return!1;return!0}function Ce(t){return{left:t.clientX,top:t.clientY}}function Te(view,t,e,n,r){if(-1==n)return!1;let o=view.state.doc.resolve(n);for(let i=o.depth+1;i>0;i--)if(view.someProp(t,(t=>i>o.depth?t(view,e,o.nodeAfter,o.before(i),r,!0):t(view,e,o.node(i),o.before(i),r,!1))))return!0;return!1}function Ee(view,t,e){if(view.focused||view.focus(),view.state.selection.eq(t))return;let tr=view.state.tr.setSelection(t);"pointer"==e&&tr.setMeta("pointer",!0),view.dispatch(tr)}function Ae(view,t,e,n,o){return Te(view,"handleClickOn",t,e,n)||view.someProp("handleClick",(e=>e(view,t,n)))||(o?function(view,t){if(-1==t)return!1;let e,n,o=view.state.selection;o instanceof r.c&&(e=o.node);let l=view.state.doc.resolve(t);for(let i=l.depth+1;i>0;i--){let t=i>l.depth?l.nodeAfter:l.node(i);if(r.c.isSelectable(t)){n=e&&o.$from.depth>0&&i>=o.$from.depth&&l.before(o.$from.depth+1)==o.$from.pos?l.before(o.$from.depth):l.before(i);break}}return null!=n&&(Ee(view,r.c.create(view.state.doc,n),"pointer"),!0)}(view,e):function(view,t){if(-1==t)return!1;let e=view.state.doc.resolve(t),n=e.nodeAfter;return!!(n&&n.isAtom&&r.c.isSelectable(n))&&(Ee(view,new r.c(e),"pointer"),!0)}(view,e))}function Ne(view,t,e,n){return Te(view,"handleDoubleClickOn",t,e,n)||view.someProp("handleDoubleClick",(e=>e(view,t,n)))}function De(view,t,e,n){return Te(view,"handleTripleClickOn",t,e,n)||view.someProp("handleTripleClick",(e=>e(view,t,n)))||function(view,t,e){if(0!=e.button)return!1;let n=view.state.doc;if(-1==t)return!!n.inlineContent&&(Ee(view,r.h.create(n,0,n.content.size),"pointer"),!0);let o=n.resolve(t);for(let i=o.depth+1;i>0;i--){let t=i>o.depth?o.nodeAfter:o.node(i),e=o.before(i);if(t.inlineContent)Ee(view,r.h.create(n,e+1,e+1+t.content.size),"pointer");else{if(!r.c.isSelectable(t))continue;Ee(view,r.c.create(n,e),"pointer")}return!0}}(view,e,n)}function Pe(view){return He(view)}be.keydown=(view,t)=>{let e=t;if(view.input.shiftKey=16==e.keyCode||e.shiftKey,!Ie(view,e)&&(view.input.lastKeyCode=e.keyCode,view.input.lastKeyCodeTime=Date.now(),!z||!R||13!=e.keyCode))if(229!=e.keyCode&&view.domObserver.forceFlush(),!L||13!=e.keyCode||e.ctrlKey||e.altKey||e.metaKey)view.someProp("handleKeyDown",(t=>t(view,e)))||ie(view,e)?e.preventDefault():xe(view,"key");else{let t=Date.now();view.input.lastIOSEnter=t,view.input.lastIOSEnterFallbackTimeout=setTimeout((()=>{view.input.lastIOSEnter==t&&(view.someProp("handleKeyDown",(t=>t(view,O(13,"Enter")))),view.input.lastIOSEnter=0)}),200)}},be.keyup=(view,t)=>{16==t.keyCode&&(view.input.shiftKey=!1)},be.keypress=(view,t)=>{let e=t;if(Ie(view,e)||!e.charCode||e.ctrlKey&&!e.altKey||B&&e.metaKey)return;if(view.someProp("handleKeyPress",(t=>t(view,e))))return void e.preventDefault();let n=view.state.selection;if(!(n instanceof r.h&&n.$from.sameParent(n.$to))){let text=String.fromCharCode(e.charCode),t=()=>view.state.tr.insertText(text).scrollIntoView();/[\r\n]/.test(text)||view.someProp("handleTextInput",(e=>e(view,n.$from.pos,n.$to.pos,text,t)))||view.dispatch(t()),e.preventDefault()}};const Re=B?"metaKey":"ctrlKey";ve.mousedown=(view,t)=>{let e=t;view.input.shiftKey=e.shiftKey;let n=Pe(view),r=Date.now(),o="singleClick";r-view.input.lastClick.time<500&&function(t,e){let n=e.x-t.clientX,r=e.y-t.clientY;return n*n+r*r<100}(e,view.input.lastClick)&&!e[Re]&&view.input.lastClick.button==e.button&&("singleClick"==view.input.lastClick.type?o="doubleClick":"doubleClick"==view.input.lastClick.type&&(o="tripleClick")),view.input.lastClick={time:r,x:e.clientX,y:e.clientY,type:o,button:e.button};let l=view.posAtCoords(Ce(e));l&&("singleClick"==o?(view.input.mouseDown&&view.input.mouseDown.done(),view.input.mouseDown=new je(view,l,e,!!n)):("doubleClick"==o?Ne:De)(view,l.pos,l.inside,e)?e.preventDefault():xe(view,"pointer"))};class je{constructor(view,t,e,n){let o,l;if(this.view=view,this.pos=t,this.event=e,this.flushed=n,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=view.state.doc,this.selectNode=!!e[Re],this.allowDefault=e.shiftKey,t.inside>-1)o=view.state.doc.nodeAt(t.inside),l=t.inside;else{let e=view.state.doc.resolve(t.pos);o=e.parent,l=e.depth?e.before():0}const c=n?null:e.target,d=c?view.docView.nearestDesc(c,!0):null;this.target=d&&1==d.dom.nodeType?d.dom:null;let{selection:h}=view.state;(0==e.button&&o.type.spec.draggable&&!1!==o.type.spec.selectable||h instanceof r.c&&h.from<=l&&h.to>l)&&(this.mightDrag={node:o,pos:l,addAttr:!(!this.target||this.target.draggable),setUneditable:!(!this.target||!D||this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout((()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")}),20),this.view.domObserver.start()),view.root.addEventListener("mouseup",this.up=this.up.bind(this)),view.root.addEventListener("mousemove",this.move=this.move.bind(this)),xe(view,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout((()=>Bt(this.view))),this.view.input.mouseDown=null}up(t){if(this.done(),!this.view.dom.contains(t.target))return;let e=this.pos;this.view.state.doc!=this.startDoc&&(e=this.view.posAtCoords(Ce(t))),this.updateAllowDefault(t),this.allowDefault||!e?xe(this.view,"pointer"):Ae(this.view,e.pos,e.inside,t,this.selectNode)?t.preventDefault():0==t.button&&(this.flushed||I&&this.mightDrag&&!this.mightDrag.node.isAtom||R&&!this.view.state.selection.visible&&Math.min(Math.abs(e.pos-this.view.state.selection.from),Math.abs(e.pos-this.view.state.selection.to))<=2)?(Ee(this.view,r.f.near(this.view.state.doc.resolve(e.pos)),"pointer"),t.preventDefault()):xe(this.view,"pointer")}move(t){this.updateAllowDefault(t),xe(this.view,"pointer"),0==t.buttons&&this.done()}updateAllowDefault(t){!this.allowDefault&&(Math.abs(this.event.x-t.clientX)>4||Math.abs(this.event.y-t.clientY)>4)&&(this.allowDefault=!0)}}function Ie(view,t){return!!view.composing||!!(I&&Math.abs(t.timeStamp-view.input.compositionEndedAt)<500)&&(view.input.compositionEndedAt=-2e8,!0)}ve.touchstart=view=>{view.input.lastTouch=Date.now(),Pe(view),xe(view,"pointer")},ve.touchmove=view=>{view.input.lastTouch=Date.now(),xe(view,"pointer")},ve.contextmenu=view=>Pe(view);const Le=z?5e3:-1;function Be(view,t){clearTimeout(view.input.composingTimeout),t>-1&&(view.input.composingTimeout=setTimeout((()=>He(view)),t))}function $e(view){for(view.composing&&(view.input.composing=!1,view.input.compositionEndedAt=function(){let t=document.createEvent("Event");return t.initEvent("event",!0,!0),t.timeStamp}());view.input.compositionNodes.length>0;)view.input.compositionNodes.pop().markParentsDirty()}function ze(view){let t=view.domSelectionRange();if(!t.focusNode)return null;let e=function(t,e){for(;;){if(3==t.nodeType&&e)return t;if(1==t.nodeType&&e>0){if("false"==t.contentEditable)return null;e=w(t=t.childNodes[e-1])}else{if(!t.parentNode||k(t))return null;e=c(t),t=t.parentNode}}}(t.focusNode,t.focusOffset),n=function(t,e){for(;;){if(3==t.nodeType&&e<t.nodeValue.length)return t;if(1==t.nodeType&&e<t.childNodes.length){if("false"==t.contentEditable)return null;t=t.childNodes[e],e=0}else{if(!t.parentNode||k(t))return null;e=c(t)+1,t=t.parentNode}}}(t.focusNode,t.focusOffset);if(e&&n&&e!=n){let t=n.pmViewDesc,r=view.domObserver.lastChangedTextNode;if(e==r||n==r)return r;if(!t||!t.isText(n.nodeValue))return n;if(view.input.compositionNode==n){let t=e.pmViewDesc;if(t&&t.isText(e.nodeValue))return n}}return e||n}function He(view,t=!1){if(!(z&&view.domObserver.flushingSoon>=0)){if(view.domObserver.forceFlush(),$e(view),t||view.docView&&view.docView.dirty){let e=It(view),n=view.state.selection;return e&&!e.eq(n)?view.dispatch(view.state.tr.setSelection(e)):!view.markCursor&&!t||n.$from.node(n.$from.sharedDepth(n.to)).inlineContent?view.updateState(view.state):view.dispatch(view.state.tr.deleteSelection()),!0}return!1}}be.compositionstart=be.compositionupdate=view=>{if(!view.composing){view.domObserver.flush();let{state:t}=view,e=t.selection.$to;if(t.selection instanceof r.h&&(t.storedMarks||!e.textOffset&&e.parentOffset&&e.nodeBefore.marks.some((t=>!1===t.type.spec.inclusive))))view.markCursor=view.state.storedMarks||e.marks(),He(view,!0),view.markCursor=null;else if(He(view,!t.selection.empty),D&&t.selection.empty&&e.parentOffset&&!e.textOffset&&e.nodeBefore.marks.length){let t=view.domSelectionRange();for(let e=t.focusNode,n=t.focusOffset;e&&1==e.nodeType&&0!=n;){let t=n<0?e.lastChild:e.childNodes[n-1];if(!t)break;if(3==t.nodeType){let e=view.domSelection();e&&e.collapse(t,t.nodeValue.length);break}e=t,n=-1}}view.input.composing=!0}Be(view,Le)},be.compositionend=(view,t)=>{view.composing&&(view.input.composing=!1,view.input.compositionEndedAt=t.timeStamp,view.input.compositionPendingChanges=view.domObserver.pendingRecords().length?view.input.compositionID:0,view.input.compositionNode=null,view.input.compositionPendingChanges&&Promise.resolve().then((()=>view.domObserver.flush())),view.input.compositionID++,Be(view,20))};const Ve=A&&N<15||L&&V<604;function Fe(view,text,html,t,e){let n=ae(view,text,html,t,view.state.selection.$from);if(view.someProp("handlePaste",(t=>t(view,e,n||o.j.empty))))return!0;if(!n)return!1;let r=function(t){return 0==t.openStart&&0==t.openEnd&&1==t.content.childCount?t.content.firstChild:null}(n),tr=r?view.state.tr.replaceSelectionWith(r,t):view.state.tr.replaceSelection(n);return view.dispatch(tr.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function _e(t){let text=t.getData("text/plain")||t.getData("Text");if(text)return text;let e=t.getData("text/uri-list");return e?e.replace(/\r?\n/g," "):""}ve.copy=be.cut=(view,t)=>{let e=t,n=view.state.selection,r="cut"==e.type;if(n.empty)return;let data=Ve?null:e.clipboardData,o=n.content(),{dom:l,text:text}=se(view,o);data?(e.preventDefault(),data.clearData(),data.setData("text/html",l.innerHTML),data.setData("text/plain",text)):function(view,t){if(!view.dom.parentNode)return;let e=view.dom.parentNode.appendChild(document.createElement("div"));e.appendChild(t),e.style.cssText="position: fixed; left: -10000px; top: 10px";let n=getSelection(),r=document.createRange();r.selectNodeContents(t),view.dom.blur(),n.removeAllRanges(),n.addRange(r),setTimeout((()=>{e.parentNode&&e.parentNode.removeChild(e),view.focus()}),50)}(view,l),r&&view.dispatch(view.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},be.paste=(view,t)=>{let e=t;if(view.composing&&!z)return;let data=Ve?null:e.clipboardData,n=view.input.shiftKey&&45!=view.input.lastKeyCode;data&&Fe(view,_e(data),data.getData("text/html"),n,e)?e.preventDefault():function(view,t){if(!view.dom.parentNode)return;let e=view.input.shiftKey||view.state.selection.$from.parent.type.spec.code,n=view.dom.parentNode.appendChild(document.createElement(e?"textarea":"div"));e||(n.contentEditable="true"),n.style.cssText="position: fixed; left: -10000px; top: 10px",n.focus();let r=view.input.shiftKey&&45!=view.input.lastKeyCode;setTimeout((()=>{view.focus(),n.parentNode&&n.parentNode.removeChild(n),e?Fe(view,n.value,null,r,t):Fe(view,n.textContent,n.innerHTML,r,t)}),50)}(view,e)};class We{constructor(t,e,n){this.slice=t,this.move=e,this.node=n}}const qe=B?"altKey":"ctrlKey";function Ke(view,t){let e=view.someProp("dragCopies",(e=>!e(t)));return null!=e?e:!t[qe]}ve.dragstart=(view,t)=>{let e=t,n=view.input.mouseDown;if(n&&n.done(),!e.dataTransfer)return;let o,l=view.state.selection,c=l.empty?null:view.posAtCoords(Ce(e));if(c&&c.pos>=l.from&&c.pos<=(l instanceof r.c?l.to-1:l.to));else if(n&&n.mightDrag)o=r.c.create(view.state.doc,n.mightDrag.pos);else if(e.target&&1==e.target.nodeType){let desc=view.docView.nearestDesc(e.target,!0);desc&&desc.node.type.spec.draggable&&desc!=view.docView&&(o=r.c.create(view.state.doc,desc.posBefore))}let d=(o||view.state.selection).content(),{dom:h,text:text,slice:f}=se(view,d);(!e.dataTransfer.files.length||!R||j>120)&&e.dataTransfer.clearData(),e.dataTransfer.setData(Ve?"Text":"text/html",h.innerHTML),e.dataTransfer.effectAllowed="copyMove",Ve||e.dataTransfer.setData("text/plain",text),view.dragging=new We(f,Ke(view,e),o)},ve.dragend=view=>{let t=view.dragging;window.setTimeout((()=>{view.dragging==t&&(view.dragging=null)}),50)},be.dragover=be.dragenter=(t,e)=>e.preventDefault(),be.drop=(view,t)=>{let e=t,n=view.dragging;if(view.dragging=null,!e.dataTransfer)return;let c=view.posAtCoords(Ce(e));if(!c)return;let d=view.state.doc.resolve(c.pos),h=n&&n.slice;h?view.someProp("transformPasted",(t=>{h=t(h,view)})):h=ae(view,_e(e.dataTransfer),Ve?null:e.dataTransfer.getData("text/html"),!1,d);let f=!(!n||!Ke(view,e));if(view.someProp("handleDrop",(t=>t(view,e,h||o.j.empty,f))))return void e.preventDefault();if(!h)return;e.preventDefault();let m=h?Object(l.g)(view.state.doc,d.pos,h):d.pos;null==m&&(m=d.pos);let tr=view.state.tr;if(f){let{node:t}=n;t?t.replace(tr):tr.deleteSelection()}let y=tr.mapping.map(m),v=0==h.openStart&&0==h.openEnd&&1==h.content.childCount,w=tr.doc;if(v?tr.replaceRangeWith(y,y,h.content.firstChild):tr.replaceRange(y,y,h),tr.doc.eq(w))return;let k=tr.doc.resolve(y);if(v&&r.c.isSelectable(h.content.firstChild)&&k.nodeAfter&&k.nodeAfter.sameMarkup(h.content.firstChild))tr.setSelection(new r.c(k));else{let t=tr.mapping.map(m);tr.mapping.maps[tr.mapping.maps.length-1].forEach(((e,n,r,o)=>t=o)),tr.setSelection(Wt(view,k,tr.doc.resolve(t)))}view.focus(),view.dispatch(tr.setMeta("uiEvent","drop"))},ve.focus=view=>{view.input.lastFocus=Date.now(),view.focused||(view.domObserver.stop(),view.dom.classList.add("ProseMirror-focused"),view.domObserver.start(),view.focused=!0,setTimeout((()=>{view.docView&&view.hasFocus()&&!view.domObserver.currentSelection.eq(view.domSelectionRange())&&Bt(view)}),20))},ve.blur=(view,t)=>{let e=t;view.focused&&(view.domObserver.stop(),view.dom.classList.remove("ProseMirror-focused"),view.domObserver.start(),e.relatedTarget&&view.dom.contains(e.relatedTarget)&&view.domObserver.currentSelection.clear(),view.focused=!1)},ve.beforeinput=(view,t)=>{if(R&&z&&"deleteContentBackward"==t.inputType){view.domObserver.flushSoon();let{domChangeCount:t}=view.input;setTimeout((()=>{if(view.input.domChangeCount!=t)return;if(view.dom.blur(),view.focus(),view.someProp("handleKeyDown",(t=>t(view,O(8,"Backspace")))))return;let{$cursor:e}=view.state.selection;e&&e.pos>0&&view.dispatch(view.state.tr.delete(e.pos-1,e.pos).scrollIntoView())}),50)}};for(let t in be)ve[t]=be[t];function Je(a,b){if(a==b)return!0;for(let p in a)if(a[p]!==b[p])return!1;for(let p in b)if(!(p in a))return!1;return!0}class Ue{constructor(t,e){this.toDOM=t,this.spec=e||Ze,this.side=this.spec.side||0}map(t,span,e,n){let{pos:r,deleted:o}=t.mapResult(span.from+n,this.side<0?-1:1);return o?null:new Xe(r-e,r-e,this)}valid(){return!0}eq(t){return this==t||t instanceof Ue&&(this.spec.key&&this.spec.key==t.spec.key||this.toDOM==t.toDOM&&Je(this.spec,t.spec))}destroy(t){this.spec.destroy&&this.spec.destroy(t)}}class Ge{constructor(t,e){this.attrs=t,this.spec=e||Ze}map(t,span,e,n){let r=t.map(span.from+n,this.spec.inclusiveStart?-1:1)-e,o=t.map(span.to+n,this.spec.inclusiveEnd?1:-1)-e;return r>=o?null:new Xe(r,o,this)}valid(t,span){return span.from<span.to}eq(t){return this==t||t instanceof Ge&&Je(this.attrs,t.attrs)&&Je(this.spec,t.spec)}static is(span){return span.type instanceof Ge}destroy(){}}class Ye{constructor(t,e){this.attrs=t,this.spec=e||Ze}map(t,span,e,n){let r=t.mapResult(span.from+n,1);if(r.deleted)return null;let o=t.mapResult(span.to+n,-1);return o.deleted||o.pos<=r.pos?null:new Xe(r.pos-e,o.pos-e,this)}valid(t,span){let e,{index:n,offset:r}=t.content.findIndex(span.from);return r==span.from&&!(e=t.child(n)).isText&&r+e.nodeSize==span.to}eq(t){return this==t||t instanceof Ye&&Je(this.attrs,t.attrs)&&Je(this.spec,t.spec)}destroy(){}}class Xe{constructor(t,e,n){this.from=t,this.to=e,this.type=n}copy(t,e){return new Xe(t,e,this.type)}eq(t,e=0){return this.type.eq(t.type)&&this.from+e==t.from&&this.to+e==t.to}map(t,e,n){return this.type.map(t,this,e,n)}static widget(t,e,n){return new Xe(t,t,new Ue(e,n))}static inline(t,e,n,r){return new Xe(t,e,new Ge(n,r))}static node(t,e,n,r){return new Xe(t,e,new Ye(n,r))}get spec(){return this.type.spec}get inline(){return this.type instanceof Ge}get widget(){return this.type instanceof Ue}}const Qe=[],Ze={};class tn{constructor(t,e){this.local=t.length?t:Qe,this.children=e.length?e:Qe}static create(t,e){return e.length?an(e,t,0,Ze):en}find(t,e,n){let r=[];return this.findInner(null==t?0:t,null==e?1e9:e,r,0,n),r}findInner(t,e,n,r,o){for(let i=0;i<this.local.length;i++){let span=this.local[i];span.from<=e&&span.to>=t&&(!o||o(span.spec))&&n.push(span.copy(span.from+r,span.to+r))}for(let i=0;i<this.children.length;i+=3)if(this.children[i]<e&&this.children[i+1]>t){let l=this.children[i]+1;this.children[i+2].findInner(t-l,e-l,n,r+l,o)}}map(t,e,n){return this==en||0==t.maps.length?this:this.mapInner(t,e,0,0,n||Ze)}mapInner(t,e,n,r,o){let l;for(let i=0;i<this.local.length;i++){let c=this.local[i].map(t,n,r);c&&c.type.valid(e,c)?(l||(l=[])).push(c):o.onRemove&&o.onRemove(this.local[i].spec)}return this.children.length?function(t,e,n,r,o,l,c){let d=t.slice();for(let i=0,t=l;i<n.maps.length;i++){let e=0;n.maps[i].forEach(((n,r,o,l)=>{let c=l-o-(r-n);for(let i=0;i<d.length;i+=3){let o=d[i+1];if(o<0||n>o+t-e)continue;let l=d[i]+t-e;r>=l?d[i+1]=n<=l?-2:-1:n>=t&&c&&(d[i]+=c,d[i+1]+=c)}e+=c})),t=n.maps[i].map(t,-1)}let h=!1;for(let i=0;i<d.length;i+=3)if(d[i+1]<0){if(-2==d[i+1]){h=!0,d[i+1]=-1;continue}let e=n.map(t[i]+l),f=e-o;if(f<0||f>=r.content.size){h=!0;continue}let m=n.map(t[i+1]+l,-1)-o,{index:y,offset:v}=r.content.findIndex(f),w=r.maybeChild(y);if(w&&v==f&&v+w.nodeSize==m){let r=d[i+2].mapInner(n,w,e+1,t[i]+l+1,c);r!=en?(d[i]=f,d[i+1]=m,d[i+2]=r):(d[i+1]=-2,h=!0)}else h=!0}if(h){let h=an(function(t,e,n,r,o,l,c){function d(t,e){for(let i=0;i<t.local.length;i++){let l=t.local[i].map(r,o,e);l?n.push(l):c.onRemove&&c.onRemove(t.local[i].spec)}for(let i=0;i<t.children.length;i+=3)d(t.children[i+2],t.children[i]+e+1)}for(let i=0;i<t.length;i+=3)-1==t[i+1]&&d(t[i+2],e[i]+l+1);return n}(d,t,e,n,o,l,c),r,0,c);e=h.local;for(let i=0;i<d.length;i+=3)d[i+1]<0&&(d.splice(i,3),i-=3);for(let i=0,t=0;i<h.children.length;i+=3){let e=h.children[i];for(;t<d.length&&d[t]<e;)t+=3;d.splice(t,0,h.children[i],h.children[i+1],h.children[i+2])}}return new tn(e.sort(ln),d)}(this.children,l||[],t,e,n,r,o):l?new tn(l.sort(ln),Qe):en}add(t,e){return e.length?this==en?tn.create(t,e):this.addInner(t,e,0):this}addInner(t,e,n){let r,o=0;t.forEach(((t,l)=>{let c,d=l+n;if(c=on(e,t,d)){for(r||(r=this.children.slice());o<r.length&&r[o]<l;)o+=3;r[o]==l?r[o+2]=r[o+2].addInner(t,c,d+1):r.splice(o,0,l,l+t.nodeSize,an(c,t,d+1,Ze)),o+=3}}));let l=rn(o?sn(e):e,-n);for(let i=0;i<l.length;i++)l[i].type.valid(t,l[i])||l.splice(i--,1);return new tn(l.length?this.local.concat(l).sort(ln):this.local,r||this.children)}remove(t){return 0==t.length||this==en?this:this.removeInner(t,0)}removeInner(t,e){let n=this.children,r=this.local;for(let i=0;i<n.length;i+=3){let r,o=n[i]+e,l=n[i+1]+e;for(let span,e=0;e<t.length;e++)(span=t[e])&&span.from>o&&span.to<l&&(t[e]=null,(r||(r=[])).push(span));if(!r)continue;n==this.children&&(n=this.children.slice());let c=n[i+2].removeInner(r,o+1);c!=en?n[i+2]=c:(n.splice(i,3),i-=3)}if(r.length)for(let span,i=0;i<t.length;i++)if(span=t[i])for(let t=0;t<r.length;t++)r[t].eq(span,e)&&(r==this.local&&(r=this.local.slice()),r.splice(t--,1));return n==this.children&&r==this.local?this:r.length||n.length?new tn(r,n):en}forChild(t,e){if(this==en)return this;if(e.isLeaf)return tn.empty;let n,r;for(let i=0;i<this.children.length;i+=3)if(this.children[i]>=t){this.children[i]==t&&(n=this.children[i+2]);break}let o=t+1,l=o+e.content.size;for(let i=0;i<this.local.length;i++){let t=this.local[i];if(t.from<l&&t.to>o&&t.type instanceof Ge){let e=Math.max(o,t.from)-o,n=Math.min(l,t.to)-o;e<n&&(r||(r=[])).push(t.copy(e,n))}}if(r){let t=new tn(r.sort(ln),Qe);return n?new nn([t,n]):t}return n||en}eq(t){if(this==t)return!0;if(!(t instanceof tn)||this.local.length!=t.local.length||this.children.length!=t.children.length)return!1;for(let i=0;i<this.local.length;i++)if(!this.local[i].eq(t.local[i]))return!1;for(let i=0;i<this.children.length;i+=3)if(this.children[i]!=t.children[i]||this.children[i+1]!=t.children[i+1]||!this.children[i+2].eq(t.children[i+2]))return!1;return!0}locals(t){return cn(this.localsInner(t))}localsInner(t){if(this==en)return Qe;if(t.inlineContent||!this.local.some(Ge.is))return this.local;let e=[];for(let i=0;i<this.local.length;i++)this.local[i].type instanceof Ge||e.push(this.local[i]);return e}forEachSet(t){t(this)}}tn.empty=new tn([],[]),tn.removeOverlap=cn;const en=tn.empty;class nn{constructor(t){this.members=t}map(t,e){const n=this.members.map((n=>n.map(t,e,Ze)));return nn.from(n)}forChild(t,e){if(e.isLeaf)return tn.empty;let n=[];for(let i=0;i<this.members.length;i++){let r=this.members[i].forChild(t,e);r!=en&&(r instanceof nn?n=n.concat(r.members):n.push(r))}return nn.from(n)}eq(t){if(!(t instanceof nn)||t.members.length!=this.members.length)return!1;for(let i=0;i<this.members.length;i++)if(!this.members[i].eq(t.members[i]))return!1;return!0}locals(t){let e,n=!0;for(let i=0;i<this.members.length;i++){let r=this.members[i].localsInner(t);if(r.length)if(e){n&&(e=e.slice(),n=!1);for(let t=0;t<r.length;t++)e.push(r[t])}else e=r}return e?cn(n?e:e.sort(ln)):Qe}static from(t){switch(t.length){case 0:return en;case 1:return t[0];default:return new nn(t.every((t=>t instanceof tn))?t:t.reduce(((t,e)=>t.concat(e instanceof tn?e:e.members)),[]))}}forEachSet(t){for(let i=0;i<this.members.length;i++)this.members[i].forEachSet(t)}}function rn(t,e){if(!e||!t.length)return t;let n=[];for(let i=0;i<t.length;i++){let span=t[i];n.push(new Xe(span.from+e,span.to+e,span.type))}return n}function on(t,e,n){if(e.isLeaf)return null;let r=n+e.nodeSize,o=null;for(let span,i=0;i<t.length;i++)(span=t[i])&&span.from>n&&span.to<r&&((o||(o=[])).push(span),t[i]=null);return o}function sn(t){let e=[];for(let i=0;i<t.length;i++)null!=t[i]&&e.push(t[i]);return e}function an(t,e,n,r){let o=[],l=!1;e.forEach(((e,c)=>{let d=on(t,e,c+n);if(d){l=!0;let t=an(d,e,n+c+1,r);t!=en&&o.push(c,c+e.nodeSize,t)}}));let c=rn(l?sn(t):t,-n).sort(ln);for(let i=0;i<c.length;i++)c[i].type.valid(e,c[i])||(r.onRemove&&r.onRemove(c[i].spec),c.splice(i--,1));return c.length||o.length?new tn(c,o):en}function ln(a,b){return a.from-b.from||a.to-b.to}function cn(t){let e=t;for(let i=0;i<e.length-1;i++){let span=e[i];if(span.from!=span.to)for(let n=i+1;n<e.length;n++){let r=e[n];if(r.from!=span.from){r.from<span.to&&(e==t&&(e=t.slice()),e[i]=span.copy(span.from,r.from),dn(e,n,span.copy(r.from,span.to)));break}r.to!=span.to&&(e==t&&(e=t.slice()),e[n]=r.copy(r.from,span.to),dn(e,n+1,r.copy(span.to,r.to)))}}return e}function dn(t,i,e){for(;i<t.length&&ln(e,t[i])>0;)i++;t.splice(i,0,e)}function hn(view){let t=[];return view.someProp("decorations",(e=>{let n=e(view.state);n&&n!=en&&t.push(n)})),view.cursorWrapper&&t.push(tn.create(view.state.doc,[view.cursorWrapper.deco])),nn.from(t)}const pn={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},un=A&&N<=11;class fn{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(t){this.anchorNode=t.anchorNode,this.anchorOffset=t.anchorOffset,this.focusNode=t.focusNode,this.focusOffset=t.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(t){return t.anchorNode==this.anchorNode&&t.anchorOffset==this.anchorOffset&&t.focusNode==this.focusNode&&t.focusOffset==this.focusOffset}}class mn{constructor(view,t){this.view=view,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new fn,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver((t=>{for(let i=0;i<t.length;i++)this.queue.push(t[i]);A&&N<=11&&t.some((t=>"childList"==t.type&&t.removedNodes.length||"characterData"==t.type&&t.oldValue.length>t.target.nodeValue.length))?this.flushSoon():this.flush()})),un&&(this.onCharData=t=>{this.queue.push({target:t.target,type:"characterData",oldValue:t.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout((()=>{this.flushingSoon=-1,this.flush()}),20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,pn)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let t=this.observer.takeRecords();if(t.length){for(let i=0;i<t.length;i++)this.queue.push(t[i]);window.setTimeout((()=>this.flush()),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout((()=>this.suppressingSelectionUpdates=!1),50)}onSelectionChange(){if(qt(this.view)){if(this.suppressingSelectionUpdates)return Bt(this.view);if(A&&N<=11&&!this.view.state.selection.empty){let t=this.view.domSelectionRange();if(t.focusNode&&m(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(t){if(!t.focusNode)return!0;let e,n=new Set;for(let e=t.focusNode;e;e=d(e))n.add(e);for(let r=t.anchorNode;r;r=d(r))if(n.has(r)){e=r;break}let desc=e&&this.view.docView.nearestDesc(e);return desc&&desc.ignoreMutation({type:"selection",target:3==e.nodeType?e.parentNode:e})?(this.setCurSelection(),!0):void 0}pendingRecords(){if(this.observer)for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}flush(){let{view:view}=this;if(!view.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let e=view.domSelectionRange(),n=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(e)&&qt(view)&&!this.ignoreSelectionChange(e),o=-1,l=-1,c=!1,d=[];if(view.editable)for(let i=0;i<t.length;i++){let e=this.registerMutation(t[i],d);e&&(o=o<0?e.from:Math.min(e.from,o),l=l<0?e.to:Math.max(e.to,l),e.typeOver&&(c=!0))}if(D&&d.length){let t=d.filter((t=>"BR"==t.nodeName));if(2==t.length){let[a,b]=t;a.parentNode&&a.parentNode.parentNode==b.parentNode?b.remove():a.remove()}else{let{focusNode:e}=this.currentSelection;for(let br of t){let t=br.parentNode;!t||"LI"!=t.nodeName||e&&bn(view,e)==t||br.remove()}}}let h=null;o<0&&n&&view.input.lastFocus>Date.now()-200&&Math.max(view.input.lastTouch,view.input.lastClick.time)<Date.now()-300&&x(e)&&(h=It(view))&&h.eq(r.f.near(view.state.doc.resolve(0),1))?(view.input.lastFocus=0,Bt(view),this.currentSelection.set(e),view.scrollToSelection()):(o>-1||n)&&(o>-1&&(view.docView.markDirty(o,l),function(view){if(gn.has(view))return;if(gn.set(view,null),-1!==["normal","nowrap","pre-line"].indexOf(getComputedStyle(view.dom).whiteSpace)){if(view.requiresGeckoHackNode=D,yn)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),yn=!0}}(view)),this.handleDOMChange(o,l,c,d),view.docView&&view.docView.dirty?view.updateState(view.state):this.currentSelection.eq(e)||Bt(view),this.currentSelection.set(e))}registerMutation(t,e){if(e.indexOf(t.target)>-1)return null;let desc=this.view.docView.nearestDesc(t.target);if("attributes"==t.type&&(desc==this.view.docView||"contenteditable"==t.attributeName||"style"==t.attributeName&&!t.oldValue&&!t.target.getAttribute("style")))return null;if(!desc||desc.ignoreMutation(t))return null;if("childList"==t.type){for(let i=0;i<t.addedNodes.length;i++){let n=t.addedNodes[i];e.push(n),3==n.nodeType&&(this.lastChangedTextNode=n)}if(desc.contentDOM&&desc.contentDOM!=desc.dom&&!desc.contentDOM.contains(t.target))return{from:desc.posBefore,to:desc.posAfter};let n=t.previousSibling,r=t.nextSibling;if(A&&N<=11&&t.addedNodes.length)for(let i=0;i<t.addedNodes.length;i++){let{previousSibling:e,nextSibling:o}=t.addedNodes[i];(!e||Array.prototype.indexOf.call(t.addedNodes,e)<0)&&(n=e),(!o||Array.prototype.indexOf.call(t.addedNodes,o)<0)&&(r=o)}let o=n&&n.parentNode==t.target?c(n)+1:0,l=desc.localPosFromDOM(t.target,o,-1),d=r&&r.parentNode==t.target?c(r):t.target.childNodes.length;return{from:l,to:desc.localPosFromDOM(t.target,d,1)}}return"attributes"==t.type?{from:desc.posAtStart-desc.border,to:desc.posAtEnd+desc.border}:(this.lastChangedTextNode=t.target,{from:desc.posAtStart,to:desc.posAtEnd,typeOver:t.target.nodeValue==t.oldValue})}}let gn=new WeakMap,yn=!1;function vn(view,t){let e=t.startContainer,n=t.startOffset,r=t.endContainer,o=t.endOffset,l=view.domAtPos(view.state.selection.anchor);return m(l.node,l.offset,r,o)&&([e,n,r,o]=[r,o,e,n]),{anchorNode:e,anchorOffset:n,focusNode:r,focusOffset:o}}function bn(view,t){for(let p=t.parentNode;p&&p!=view.dom;p=p.parentNode){let desc=view.docView.nearestDesc(p,!0);if(desc&&desc.node.isBlock)return p}return null}function wn(t){let desc=t.pmViewDesc;if(desc)return desc.parseRule();if("BR"==t.nodeName&&t.parentNode){if(I&&/^(ul|ol)$/i.test(t.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}if(t.parentNode.lastChild==t||I&&/^(tr|table)$/i.test(t.parentNode.nodeName))return{ignore:!0}}else if("IMG"==t.nodeName&&t.getAttribute("mark-placeholder"))return{ignore:!0};return null}const kn=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function xn(view,t,e,n,l){let c=view.input.compositionPendingChanges||(view.composing?view.input.compositionID:0);if(view.input.compositionPendingChanges=0,t<0){let t=view.input.lastSelectionTime>Date.now()-50?view.input.lastSelectionOrigin:null,e=It(view,t);if(e&&!view.state.selection.eq(e)){if(R&&z&&13===view.input.lastKeyCode&&Date.now()-100<view.input.lastKeyCodeTime&&view.someProp("handleKeyDown",(t=>t(view,O(13,"Enter")))))return;let tr=view.state.tr.setSelection(e);"pointer"==t?tr.setMeta("pointer",!0):"key"==t&&tr.scrollIntoView(),c&&tr.setMeta("composition",c),view.dispatch(tr)}return}let d=view.state.doc.resolve(t),h=d.sharedDepth(e);t=d.before(h+1),e=view.state.doc.resolve(e).after(h+1);let f,m,y=view.state.selection,v=function(view,t,e){let n,{node:r,fromOffset:l,toOffset:c,from:d,to:h}=view.docView.parseRange(t,e),f=view.domSelectionRange(),m=f.anchorNode;if(m&&view.dom.contains(1==m.nodeType?m:m.parentNode)&&(n=[{node:m,offset:f.anchorOffset}],x(f)||n.push({node:f.focusNode,offset:f.focusOffset})),R&&8===view.input.lastKeyCode)for(let t=c;t>l;t--){let e=r.childNodes[t-1],desc=e.pmViewDesc;if("BR"==e.nodeName&&!desc){c=t;break}if(!desc||desc.size)break}let y=view.state.doc,v=view.someProp("domParser")||o.a.fromSchema(view.state.schema),w=y.resolve(d),k=null,O=v.parse(r,{topNode:w.parent,topMatch:w.parent.contentMatchAt(w.index()),topOpen:!0,from:l,to:c,preserveWhitespace:"pre"!=w.parent.type.whitespace||"full",findPositions:n,ruleFromNode:wn,context:w});if(n&&null!=n[0].pos){let t=n[0].pos,head=n[1]&&n[1].pos;null==head&&(head=t),k={anchor:t+d,head:head+d}}return{doc:O,sel:k,from:d,to:h}}(view,t,e),w=view.state.doc,k=w.slice(v.from,v.to);8===view.input.lastKeyCode&&Date.now()-100<view.input.lastKeyCodeTime?(f=view.state.selection.to,m="end"):(f=view.state.selection.from,m="start"),view.input.lastKeyCode=null;let S=function(a,b,t,e,n){let r=a.findDiffStart(b,t);if(null==r)return null;let{a:o,b:l}=a.findDiffEnd(b,t+a.size,t+b.size);if("end"==n){e-=o+Math.max(0,r-Math.min(o,l))-r}if(o<r&&a.size<b.size){let t=e<=r&&e>=o?r-e:0;r-=t,r&&r<b.size&&Mn(b.textBetween(r-1,r+1))&&(r+=t?1:-1),l=r+(l-o),o=r}else if(l<r){let t=e<=r&&e>=l?r-e:0;r-=t,r&&r<a.size&&Mn(a.textBetween(r-1,r+1))&&(r+=t?1:-1),o=r+(o-l),l=r}return{start:r,endA:o,endB:l}}(k.content,v.doc.content,v.from,f,m);if(S&&view.input.domChangeCount++,(L&&view.input.lastIOSEnter>Date.now()-225||z)&&l.some((t=>1==t.nodeType&&!kn.test(t.nodeName)))&&(!S||S.endA>=S.endB)&&view.someProp("handleKeyDown",(t=>t(view,O(13,"Enter")))))return void(view.input.lastIOSEnter=0);if(!S){if(!(n&&y instanceof r.h&&!y.empty&&y.$head.sameParent(y.$anchor))||view.composing||v.sel&&v.sel.anchor!=v.sel.head){if(v.sel){let t=On(view,view.state.doc,v.sel);if(t&&!t.eq(view.state.selection)){let tr=view.state.tr.setSelection(t);c&&tr.setMeta("composition",c),view.dispatch(tr)}}return}S={start:y.from,endA:y.to,endB:y.to}}view.state.selection.from<view.state.selection.to&&S.start==S.endB&&view.state.selection instanceof r.h&&(S.start>view.state.selection.from&&S.start<=view.state.selection.from+2&&view.state.selection.from>=v.from?S.start=view.state.selection.from:S.endA<view.state.selection.to&&S.endA>=view.state.selection.to-2&&view.state.selection.to<=v.to&&(S.endB+=view.state.selection.to-S.endA,S.endA=view.state.selection.to)),A&&N<=11&&S.endB==S.start+1&&S.endA==S.start&&S.start>v.from&&"  "==v.doc.textBetween(S.start-v.from-1,S.start-v.from+1)&&(S.start--,S.endA--,S.endB--);let M,C=v.doc.resolveNoCache(S.start-v.from),T=v.doc.resolveNoCache(S.endB-v.from),E=w.resolve(S.start),D=C.sameParent(T)&&C.parent.inlineContent&&E.end()>=S.endA;if((L&&view.input.lastIOSEnter>Date.now()-225&&(!D||l.some((t=>"DIV"==t.nodeName||"P"==t.nodeName)))||!D&&C.pos<v.doc.content.size&&(!C.sameParent(T)||!C.parent.inlineContent)&&!/\S/.test(v.doc.textBetween(C.pos,T.pos,"",""))&&(M=r.f.findFrom(v.doc.resolve(C.pos+1),1,!0))&&M.head>C.pos)&&view.someProp("handleKeyDown",(t=>t(view,O(13,"Enter")))))return void(view.input.lastIOSEnter=0);if(view.state.selection.anchor>S.start&&function(t,e,n,r,o){if(n-e<=o.pos-r.pos||Sn(r,!0,!1)<o.pos)return!1;let l=t.resolve(e);if(!r.parent.isTextblock){let t=l.nodeAfter;return null!=t&&n==e+t.nodeSize}if(l.parentOffset<l.parent.content.size||!l.parent.isTextblock)return!1;let c=t.resolve(Sn(l,!0,!0));return!(!c.parent.isTextblock||c.pos>n||Sn(c,!0,!1)<n)&&r.parent.content.cut(r.parentOffset).eq(c.parent.content)}(w,S.start,S.endA,C,T)&&view.someProp("handleKeyDown",(t=>t(view,O(8,"Backspace")))))return void(z&&R&&view.domObserver.suppressSelectionUpdates());R&&S.endB==S.start&&(view.input.lastChromeDelete=Date.now()),z&&!D&&C.start()!=T.start()&&0==T.parentOffset&&C.depth==T.depth&&v.sel&&v.sel.anchor==v.sel.head&&v.sel.head==S.endA&&(S.endB-=2,T=v.doc.resolveNoCache(S.endB-v.from),setTimeout((()=>{view.someProp("handleKeyDown",(function(t){return t(view,O(13,"Enter"))}))}),20));let P,j=S.start,I=S.endA,B=base=>{let tr=base||view.state.tr.replace(j,I,v.doc.slice(S.start-v.from,S.endB-v.from));if(v.sel){let t=On(view,tr.doc,v.sel);t&&!(R&&view.composing&&t.empty&&(S.start!=S.endB||view.input.lastChromeDelete<Date.now()-100)&&(t.head==j||t.head==tr.mapping.map(I)-1)||A&&t.empty&&t.head==j)&&tr.setSelection(t)}return c&&tr.setMeta("composition",c),tr.scrollIntoView()};if(D){if(C.pos==T.pos){A&&N<=11&&0==C.parentOffset&&(view.domObserver.suppressSelectionUpdates(),setTimeout((()=>Bt(view)),20));let tr=B(view.state.tr.delete(j,I)),t=w.resolve(S.start).marksAcross(w.resolve(S.endA));t&&tr.ensureMarks(t),view.dispatch(tr)}else if(S.endA==S.endB&&(P=function(t,e){let n,mark,r,l=t.firstChild.marks,c=e.firstChild.marks,d=l,h=c;for(let i=0;i<c.length;i++)d=c[i].removeFromSet(d);for(let i=0;i<l.length;i++)h=l[i].removeFromSet(h);if(1==d.length&&0==h.length)mark=d[0],n="add",r=t=>t.mark(mark.addToSet(t.marks));else{if(0!=d.length||1!=h.length)return null;mark=h[0],n="remove",r=t=>t.mark(mark.removeFromSet(t.marks))}let f=[];for(let i=0;i<e.childCount;i++)f.push(r(e.child(i)));if(o.c.from(f).eq(t))return{mark:mark,type:n}}(C.parent.content.cut(C.parentOffset,T.parentOffset),E.parent.content.cut(E.parentOffset,S.endA-E.start())))){let tr=B(view.state.tr);"add"==P.type?tr.addMark(j,I,P.mark):tr.removeMark(j,I,P.mark),view.dispatch(tr)}else if(C.parent.child(C.index()).isText&&C.index()==T.index()-(T.textOffset?0:1)){let text=C.parent.textBetween(C.parentOffset,T.parentOffset),t=()=>B(view.state.tr.insertText(text,j,I));view.someProp("handleTextInput",(e=>e(view,j,I,text,t)))||view.dispatch(t())}}else view.dispatch(B())}function On(view,t,e){return Math.max(e.anchor,e.head)>t.content.size?null:Wt(view,t.resolve(e.anchor),t.resolve(e.head))}function Sn(t,e,n){let r=t.depth,o=e?t.end():t.pos;for(;r>0&&(e||t.indexAfter(r)==t.node(r).childCount);)r--,o++,e=!1;if(n){let e=t.node(r).maybeChild(t.indexAfter(r));for(;e&&!e.isLeaf;)e=e.firstChild,o++}return o}function Mn(t){if(2!=t.length)return!1;let a=t.charCodeAt(0),b=t.charCodeAt(1);return a>=56320&&a<=57343&&b>=55296&&b<=56319}class Cn{constructor(t,e){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new ke,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=e,this.state=e.state,this.directPlugins=e.plugins||[],this.directPlugins.forEach(Dn),this.dispatch=this.dispatch.bind(this),this.dom=t&&t.mount||document.createElement("div"),t&&(t.appendChild?t.appendChild(this.dom):"function"==typeof t?t(this.dom):t.mount&&(this.mounted=!0)),this.editable=An(this),En(this),this.nodeViews=Nn(this),this.docView=bt(this.state.doc,Tn(this),hn(this),this.dom,this),this.domObserver=new mn(this,((t,e,n,r)=>xn(this,t,e,n,r))),this.domObserver.start(),function(view){for(let t in ve){let e=ve[t];view.dom.addEventListener(t,view.input.eventHandlers[t]=t=>{!Me(view,t)||Se(view,t)||!view.editable&&t.type in be||e(view,t)},we[t]?{passive:!0}:void 0)}I&&view.dom.addEventListener("input",(()=>null)),Oe(view)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let t=this._props;this._props={};for(let e in t)this._props[e]=t[e];this._props.state=this.state}return this._props}update(t){t.handleDOMEvents!=this._props.handleDOMEvents&&Oe(this);let e=this._props;this._props=t,t.plugins&&(t.plugins.forEach(Dn),this.directPlugins=t.plugins),this.updateStateInner(t.state,e)}setProps(t){let e={};for(let t in this._props)e[t]=this._props[t];e.state=this.state;for(let n in t)e[n]=t[n];this.update(e)}updateState(t){this.updateStateInner(t,this._props)}updateStateInner(t,e){var n;let r=this.state,o=!1,l=!1;t.storedMarks&&this.composing&&($e(this),l=!0),this.state=t;let c=r.plugins!=t.plugins||this._props.plugins!=e.plugins;if(c||this._props.plugins!=e.plugins||this._props.nodeViews!=e.nodeViews){let t=Nn(this);(function(a,b){let t=0,e=0;for(let e in a){if(a[e]!=b[e])return!0;t++}for(let t in b)e++;return t!=e})(t,this.nodeViews)&&(this.nodeViews=t,o=!0)}(c||e.handleDOMEvents!=this._props.handleDOMEvents)&&Oe(this),this.editable=An(this),En(this);let d=hn(this),h=Tn(this),f=r.plugins==t.plugins||r.doc.eq(t.doc)?t.scrollToSelection>r.scrollToSelection?"to selection":"preserve":"reset",y=o||!this.docView.matchesNode(t.doc,h,d);!y&&t.selection.eq(r.selection)||(l=!0);let v="preserve"==f&&l&&null==this.dom.style.overflowAnchor&&function(view){let t,e,rect=view.dom.getBoundingClientRect(),n=Math.max(0,rect.top);for(let r=(rect.left+rect.right)/2,o=n+1;o<Math.min(innerHeight,rect.bottom);o+=5){let l=view.root.elementFromPoint(r,o);if(!l||l==view.dom||!view.dom.contains(l))continue;let c=l.getBoundingClientRect();if(c.top>=n-20){t=l,e=c.top;break}}return{refDOM:t,refTop:e,stack:J(view.dom)}}(this);if(l){this.domObserver.stop();let e=y&&(A||R)&&!this.composing&&!r.selection.empty&&!t.selection.empty&&function(t,e){let n=Math.min(t.$anchor.sharedDepth(t.head),e.$anchor.sharedDepth(e.head));return t.$anchor.start(n)!=e.$anchor.start(n)}(r.selection,t.selection);if(y){let n=R?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=ze(this)),!o&&this.docView.update(t.doc,h,d,this)||(this.docView.updateOuterDeco(h),this.docView.destroy(),this.docView=bt(t.doc,h,d,this.dom,this)),n&&!this.trackWrites&&(e=!0)}e||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&function(view){let t=view.docView.domFromPos(view.state.selection.anchor,0),e=view.domSelectionRange();return m(t.node,t.offset,e.anchorNode,e.anchorOffset)}(this))?Bt(this,e):(Ft(this,t.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(r),(null===(n=this.dragging)||void 0===n?void 0:n.node)&&!r.doc.eq(t.doc)&&this.updateDraggedNode(this.dragging,r),"reset"==f?this.dom.scrollTop=0:"to selection"==f?this.scrollToSelection():v&&function({refDOM:t,refTop:e,stack:n}){let r=t?t.getBoundingClientRect().top:0;U(n,0==r?0:r-e)}(v)}scrollToSelection(){let t=this.domSelectionRange().focusNode;if(t&&this.dom.contains(1==t.nodeType?t:t.parentNode))if(this.someProp("handleScrollToSelection",(t=>t(this))));else if(this.state.selection instanceof r.c){let e=this.docView.domAfterPos(this.state.selection.from);1==e.nodeType&&K(this,e.getBoundingClientRect(),t)}else K(this,this.coordsAtPos(this.state.selection.head,1),t);else;}destroyPluginViews(){let view;for(;view=this.pluginViews.pop();)view.destroy&&view.destroy()}updatePluginViews(t){if(t&&t.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let i=0;i<this.pluginViews.length;i++){let e=this.pluginViews[i];e.update&&e.update(this,t)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let i=0;i<this.directPlugins.length;i++){let t=this.directPlugins[i];t.spec.view&&this.pluginViews.push(t.spec.view(this))}for(let i=0;i<this.state.plugins.length;i++){let t=this.state.plugins[i];t.spec.view&&this.pluginViews.push(t.spec.view(this))}}}updateDraggedNode(t,e){let n=t.node,o=-1;if(this.state.doc.nodeAt(n.from)==n.node)o=n.from;else{let t=n.from+(this.state.doc.content.size-e.doc.content.size);(t>0&&this.state.doc.nodeAt(t))==n.node&&(o=t)}this.dragging=new We(t.slice,t.move,o<0?void 0:r.c.create(this.state.doc,o))}someProp(t,e){let n,r=this._props&&this._props[t];if(null!=r&&(n=e?e(r):r))return n;for(let i=0;i<this.directPlugins.length;i++){let r=this.directPlugins[i].props[t];if(null!=r&&(n=e?e(r):r))return n}let o=this.state.plugins;if(o)for(let i=0;i<o.length;i++){let r=o[i].props[t];if(null!=r&&(n=e?e(r):r))return n}}hasFocus(){if(A){let t=this.root.activeElement;if(t==this.dom)return!0;if(!t||!this.dom.contains(t))return!1;for(;t&&this.dom!=t&&this.dom.contains(t);){if("false"==t.contentEditable)return!1;t=t.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(t){if(t.setActive)return t.setActive();if(G)return t.focus(G);let e=J(t);t.focus(null==G?{get preventScroll(){return G={preventScroll:!0},!0}}:void 0),G||(G=!1,U(e,0))}(this.dom),Bt(this),this.domObserver.start()}get root(){let t=this._root;if(null==t)for(let t=this.dom.parentNode;t;t=t.parentNode)if(9==t.nodeType||11==t.nodeType&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t;return t||document}updateRoot(){this._root=null}posAtCoords(t){return Z(this,t)}coordsAtPos(t,e=1){return ot(this,t,e)}domAtPos(t,e=0){return this.docView.domFromPos(t,e)}nodeDOM(t){let desc=this.docView.descAt(t);return desc?desc.nodeDOM:null}posAtDOM(t,e,n=-1){let r=this.docView.posFromDOM(t,e,n);if(null==r)throw new RangeError("DOM position not inside the editor");return r}endOfTextblock(t,e){return ut(this,e||this.state,t)}pasteHTML(html,t){return Fe(this,"",html,!1,t||new ClipboardEvent("paste"))}pasteText(text,t){return Fe(this,text,null,!0,t||new ClipboardEvent("paste"))}serializeForClipboard(t){return se(this,t)}destroy(){this.docView&&(!function(view){view.domObserver.stop();for(let t in view.input.eventHandlers)view.dom.removeEventListener(t,view.input.eventHandlers[t]);clearTimeout(view.input.composingTimeout),clearTimeout(view.input.lastIOSEnterFallbackTimeout)}(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],hn(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,h=null)}get isDestroyed(){return null==this.docView}dispatchEvent(t){return function(view,t){Se(view,t)||!ve[t.type]||!view.editable&&t.type in be||ve[t.type](view,t)}(this,t)}domSelectionRange(){let t=this.domSelection();return t?I&&11===this.root.nodeType&&function(t){let e=t.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}(this.dom.ownerDocument)==this.dom&&function(view,t){if(t.getComposedRanges){let e=t.getComposedRanges(view.root)[0];if(e)return vn(view,e)}let e;function n(t){t.preventDefault(),t.stopImmediatePropagation(),e=t.getTargetRanges()[0]}return view.dom.addEventListener("beforeinput",n,!0),document.execCommand("indent"),view.dom.removeEventListener("beforeinput",n,!0),e?vn(view,e):null}(this,t)||t:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}function Tn(view){let t=Object.create(null);return t.class="ProseMirror",t.contenteditable=String(view.editable),view.someProp("attributes",(e=>{if("function"==typeof e&&(e=e(view.state)),e)for(let n in e)"class"==n?t.class+=" "+e[n]:"style"==n?t.style=(t.style?t.style+";":"")+e[n]:t[n]||"contenteditable"==n||"nodeName"==n||(t[n]=String(e[n]))})),t.translate||(t.translate="no"),[Xe.node(0,view.state.doc.content.size,t)]}function En(view){if(view.markCursor){let t=document.createElement("img");t.className="ProseMirror-separator",t.setAttribute("mark-placeholder","true"),t.setAttribute("alt",""),view.cursorWrapper={dom:t,deco:Xe.widget(view.state.selection.from,t,{raw:!0,marks:view.markCursor})}}else view.cursorWrapper=null}function An(view){return!view.someProp("editable",(t=>!1===t(view.state)))}function Nn(view){let t=Object.create(null);function e(e){for(let n in e)Object.prototype.hasOwnProperty.call(t,n)||(t[n]=e[n])}return view.someProp("nodeViews",e),view.someProp("markViews",e),t}function Dn(t){if(t.spec.state||t.spec.filterTransaction||t.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}Cn.prototype.dispatch=function(tr){let t=this._props.dispatchTransaction;t?t.call(this,tr):this.updateState(this.state.apply(tr))}},1827:function(t,e,n){"use strict";n.d(e,"a",(function(){return v})),n.d(e,"b",(function(){return y}));for(var base={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},r={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},o="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),l="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),i=0;i<10;i++)base[48+i]=base[96+i]=String(i);for(i=1;i<=24;i++)base[i+111]="F"+i;for(i=65;i<=90;i++)base[i]=String.fromCharCode(i+32),r[i]=String.fromCharCode(i);for(var code in base)r.hasOwnProperty(code)||(r[code]=base[code]);var c=n(1569);const d="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),h="undefined"!=typeof navigator&&/Win/.test(navigator.platform);function f(t){let e,n,r,meta,o=t.split(/-(?!$)/),l=o[o.length-1];"Space"==l&&(l=" ");for(let i=0;i<o.length-1;i++){let t=o[i];if(/^(cmd|meta|m)$/i.test(t))meta=!0;else if(/^a(lt)?$/i.test(t))e=!0;else if(/^(c|ctrl|control)$/i.test(t))n=!0;else if(/^s(hift)?$/i.test(t))r=!0;else{if(!/^mod$/i.test(t))throw new Error("Unrecognized modifier name: "+t);d?meta=!0:n=!0}}return e&&(l="Alt-"+l),n&&(l="Ctrl-"+l),meta&&(l="Meta-"+l),r&&(l="Shift-"+l),l}function m(t,e,n=!0){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),n&&e.shiftKey&&(t="Shift-"+t),t}function y(t){return new c.d({props:{handleKeyDown:v(t)}})}function v(t){let map=function(map){let t=Object.create(null);for(let e in map)t[f(e)]=map[e];return t}(t);return function(view,t){let e,n=function(t){var e=!(o&&t.metaKey&&t.shiftKey&&!t.ctrlKey&&!t.altKey||l&&t.shiftKey&&t.key&&1==t.key.length||"Unidentified"==t.key)&&t.key||(t.shiftKey?r:base)[t.keyCode]||t.key||"Unidentified";return"Esc"==e&&(e="Escape"),"Del"==e&&(e="Delete"),"Left"==e&&(e="ArrowLeft"),"Up"==e&&(e="ArrowUp"),"Right"==e&&(e="ArrowRight"),"Down"==e&&(e="ArrowDown"),e}(t),c=map[m(n,t)];if(c&&c(view.state,view.dispatch,view))return!0;if(1==n.length&&" "!=n){if(t.shiftKey){let e=map[m(n,t,!1)];if(e&&e(view.state,view.dispatch,view))return!0}if((t.altKey||t.metaKey||t.ctrlKey)&&!(h&&t.ctrlKey&&t.altKey)&&(e=base[t.keyCode])&&e!=n){let n=map[m(e,t)];if(n&&n(view.state,view.dispatch,view))return!0}}return!1}}},1840:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.oneOfProp=void 0;const r=n(1524),o=n(1613);e.oneOfProp=(t,e)=>(0,r.propOptionsGenerator)((t=>{const e=[...new Set(t.flatMap((t=>{var e;return null==t?[]:null!==(e=t.constructor)&&void 0!==e?e:[]})))];if(0!==e.length)return 1===e.length?e[0]:e})(t),e,(0,o.isOneOf)(t))},2015:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isNonPositive=e.isNonNegative=e.isPositive=e.isNegative=e.instanceOfProp=e.oneOfTypesProp=e.oneOfObjectKeysProp=e.oneOfProp=e.functionProp=e.objectProp=e.arrayProp=e.anyProp=e.vueComponentProp=e.symbolProp=e.integerProp=e.numberProp=e.booleanProp=e.stringProp=void 0;var r=n(2016);Object.defineProperty(e,"stringProp",{enumerable:!0,get:function(){return r.stringProp}});var o=n(2021);Object.defineProperty(e,"booleanProp",{enumerable:!0,get:function(){return o.booleanProp}});var l=n(2022);Object.defineProperty(e,"numberProp",{enumerable:!0,get:function(){return l.numberProp}});var c=n(2023);Object.defineProperty(e,"integerProp",{enumerable:!0,get:function(){return c.integerProp}});var d=n(2024);Object.defineProperty(e,"symbolProp",{enumerable:!0,get:function(){return d.symbolProp}});var h=n(2025);Object.defineProperty(e,"vueComponentProp",{enumerable:!0,get:function(){return h.vueComponentProp}});var f=n(2026);Object.defineProperty(e,"anyProp",{enumerable:!0,get:function(){return f.anyProp}});var m=n(2027);Object.defineProperty(e,"arrayProp",{enumerable:!0,get:function(){return m.arrayProp}});var y=n(2028);Object.defineProperty(e,"objectProp",{enumerable:!0,get:function(){return y.objectProp}});var v=n(2029);Object.defineProperty(e,"functionProp",{enumerable:!0,get:function(){return v.functionProp}});var w=n(1840);Object.defineProperty(e,"oneOfProp",{enumerable:!0,get:function(){return w.oneOfProp}});var k=n(2030);Object.defineProperty(e,"oneOfObjectKeysProp",{enumerable:!0,get:function(){return k.oneOfObjectKeysProp}});var x=n(2031);Object.defineProperty(e,"oneOfTypesProp",{enumerable:!0,get:function(){return x.oneOfTypesProp}});var O=n(2032);Object.defineProperty(e,"instanceOfProp",{enumerable:!0,get:function(){return O.instanceOfProp}});var S=n(2033);Object.defineProperty(e,"isNegative",{enumerable:!0,get:function(){return S.isNegative}});var M=n(2034);Object.defineProperty(e,"isPositive",{enumerable:!0,get:function(){return M.isPositive}});var C=n(2035);Object.defineProperty(e,"isNonNegative",{enumerable:!0,get:function(){return C.isNonNegative}});var T=n(2036);Object.defineProperty(e,"isNonPositive",{enumerable:!0,get:function(){return T.isNonPositive}})},2016:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.stringProp=void 0;const r=n(1524);e.stringProp=t=>(0,r.propOptionsGenerator)(String,t)},2017:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isInstanceOf=void 0;e.isInstanceOf=t=>e=>{if(!(e instanceof t))return`value should be an instance of ${t.name}`}},2018:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isInteger=void 0;e.isInteger=t=>{if("number"!=typeof t||!Number.isInteger(t))return"value should be an integer"}},2019:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isOneOf=void 0;e.isOneOf=t=>e=>{if(!t.includes(e))return`value should be one of "${t.join('", "')}"`}},2020:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isSymbol=void 0;e.isSymbol=t=>{if("symbol"!=typeof t)return"value should be a symbol"}},2021:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.booleanProp=void 0;const r=n(1524);e.booleanProp=t=>(0,r.propOptionsGenerator)(Boolean,t)},2022:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.numberProp=void 0;const r=n(1524);e.numberProp=t=>(0,r.propOptionsGenerator)(Number,t)},2023:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.integerProp=void 0;const r=n(1524),o=n(1613);e.integerProp=t=>(0,r.propOptionsGenerator)(Number,t,o.isInteger)},2024:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.symbolProp=void 0;const r=n(1524),o=n(1613);e.symbolProp=t=>(0,r.propOptionsGenerator)(void 0,t,o.isSymbol)},2025:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.vueComponentProp=void 0;const r=n(1524);e.vueComponentProp=t=>(0,r.propOptionsGenerator)([Object,String],t)},2026:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.anyProp=void 0;const r=n(1524);e.anyProp=t=>(0,r.propOptionsGenerator)(void 0,t)},2027:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.arrayProp=void 0;const r=n(1524);e.arrayProp=t=>(0,r.propOptionsGenerator)(Array,t)},2028:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.objectProp=void 0;const r=n(1524);e.objectProp=t=>(0,r.propOptionsGenerator)(Object,t)},2029:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.functionProp=void 0;const r=n(1613);e.functionProp=t=>({optional:{type:Function,required:!1,default:void 0,validator:(0,r.vuePropValidator)(t)},nullable:{type:Function,required:!1,default:null,validator:(0,r.vuePropValidator)(t)},required:{type:Function,required:!0,validator:(0,r.vuePropValidator)(t)}})},2030:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.oneOfObjectKeysProp=void 0;const r=n(1840);e.oneOfObjectKeysProp=(object,t)=>(0,r.oneOfProp)(Object.keys(object),t)},2031:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.oneOfTypesProp=void 0;const r=n(1524);e.oneOfTypesProp=(t,e)=>(0,r.propOptionsGenerator)(t,e)},2032:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.instanceOfProp=void 0;const r=n(1524),o=n(1613);e.instanceOfProp=(t,e)=>(0,r.propOptionsGenerator)(t,e,(0,o.isInstanceOf)(t))},2033:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isNegative=void 0;e.isNegative=t=>{if("number"!=typeof t||t>=0||Number.isNaN(t))return"value should be a negative number"}},2034:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isPositive=void 0;e.isPositive=t=>{if("number"!=typeof t||t<=0||Number.isNaN(t))return"value should be a positive number"}},2035:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isNonNegative=void 0;e.isNonNegative=t=>{if("number"!=typeof t||t<0||Number.isNaN(t))return"value should be a non-negative number"}},2036:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isNonPositive=void 0;e.isNonPositive=t=>{if("number"!=typeof t||t>0||Number.isNaN(t))return"value should be a non-positive number"}}}]);