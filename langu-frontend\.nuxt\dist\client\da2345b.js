(window.webpackJsonp=window.webpackJsonp||[]).push([[54,39,56],{1372:function(e,t,o){var r=o(43);e.exports=function(e){return r(Set.prototype.values,e)}},1384:function(e,t,o){"use strict";var r=o(43),n=o(79),l=o(32);e.exports=function(){for(var e=l(this),t=n(e.add),o=0,d=arguments.length;o<d;o++)r(t,e,arguments[o]);return e}},1389:function(e,t,o){"use strict";o.r(t);o(31),o(35),o(60);var r=o(1528),n=o(266),l=o(1428),d=(o(1449),{components:{VueDraggableResizable:r.default},mixins:[l.a],props:{asset:{type:Object,required:!0},childHeaderHeight:{type:Number,default:0},lockAspectRatio:{type:Boolean,default:!0},scalable:{type:Boolean,default:!0},hoverEnabled:{type:Boolean,default:!0},isDraggableProp:{type:Boolean,default:!0},hideResizeIcon:{type:Boolean,default:!1},handles:{type:Array,default:function(){return["tl","tm","tr","mr","br","bm","bl","ml"]}}},data:function(){return{width:n.j,height:n.i,top:50,left:500,isHovered:!1,isHoveredByAsset:!1,index:5,eventBodyClass:null,allowIndexChange:!0,resizable:!0,draggable:!0,synchronizeable:!0,viewportWidth:window.innerWidth,viewportHeight:window.innerHeight,offset:5}},computed:{isCanvasOversizeX:function(){return n.n>this.viewportWidth},isCanvasOversizeY:function(){return n.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return n.k*this.zoomIndex>this.viewportHeight},getRoleHoverColor:function(){return"teacher"===this.role?"var(--v-teacherColor-base)":"var(--v-studentColor-base)"},enabledResizeable:function(){return this.resizable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},enabledDraggable:function(){return this.isDraggableProp&&this.draggable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},maxIndex:function(){return this.$store.state.classroom.maxIndex},zoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},zoomIndex:function(){var e,t;return null!==(e=null===(t=this.zoom)||void 0===t?void 0:t.zoomIndex)&&void 0!==e?e:1},type:function(){var e,t;return null===(e=this.asset)||void 0===e||null===(t=e.asset)||void 0===t?void 0:t.type},topScale:function(){return"toolbar"===this.type?this.top<0||this.top+this.height>this.viewportHeight?this.viewportHeight-this.height-70:this.top:this.synchronizeable?this.top-this.zoom.y:this.top},leftScale:function(){return"toolbar"===this.type?this.left+this.width>this.viewportWidth||this.left<0?this.viewportWidth-this.width-15:this.left:this.synchronizeable?this.left-this.zoom.x:this.left},isLockedForStudent:function(){return this.$store.getters["classroom/isLocked"]&&"student"===this.role},isSocketConnected:function(){return this.$store.state.socket.isConnected}},watch:{"asset.asset":function(e){this.move(e)}},mounted:function(){this.move(this.asset.asset)},methods:{mouseenter:function(){var e,t;(this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!0,this.socketAssetMoved({isHovered:!0})),"twilio"===this.type||"tokbox"===this.type||"editor"===this.type)&&(this.$store.commit("classroom/setCursorNameBeforeChange",(null===(e=this.$store.getters["classroom/userParams"])||void 0===e?void 0:e.cursor)||"cursor-pointer"),this.$store.commit("classroom/setToolNameBeforeChange",(null===(t=this.$store.getters["classroom/userParams"])||void 0===t?void 0:t.tool)||"pointer"),this.setTool("pointer","cursor-pointer"))},mouseleave:function(){this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!1,this.socketAssetMoved({isHovered:!1})),"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||this.setTool(this.$store.state.classroom.toolNameBeforeChange,this.$store.state.classroom.cursorNameBeforeChange)},onIndex:function(){this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{index:this.index}}),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{index:this.index}})},updateAsset:function(e,t){this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{left:this.synchronizeable?e+this.zoom.x:e,top:this.synchronizeable?t+this.zoom.y:t,index:this.index}})},onDrag:function(e,t){if(this.synchronizeable){if(this.left=e+this.zoom.x,this.top=t+this.zoom.y,this.allowIndexChange){var o=document.body;o.classList.contains(this.eventBodyClass)||(this.eventBodyClass="dragging",o.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var r={left:this.left,top:this.top,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:r}),this.socketAssetMoved(r)}},onDragStop:function(e,t){var o=document.body;o.classList.contains(this.eventBodyClass)&&(o.classList.remove(this.eventBodyClass),this.eventBodyClass=null),this.allowIndexChange=!0,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?e+this.zoom.x:e,top:this.synchronizeable?t+this.zoom.y:t,index:this.index}})},onResize:function(e,t,o,r,n){if(this.synchronizeable){if(this.left=e+this.zoom.x,this.top=t+this.zoom.y,this.width=o,this.height=r-this.childHeaderHeight,this.allowIndexChange){var l=document.body;l.classList.contains(this.eventBodyClass)||(this.eventBodyClass=n.split(" ")[1],l.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var d={left:this.left,top:this.top,width:this.width,height:this.height,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:d}),this.socketAssetMoved(d)}},onResizeStop:function(e,t,o,r){this.eventBodyClass&&document.body.classList.remove(this.eventBodyClass),this.allowIndexChange=!0,this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?e+this.zoom.x:e,top:this.synchronizeable?t+this.zoom.y:t,width:o,height:r-this.childHeaderHeight,index:this.index}})},socketAssetMoved:function(e){this.isSocketConnected&&this.$socket.emit("asset-moved",{id:this.asset.id,lessonId:this.asset.lessonId,asset:e})},move:function(e){if(void 0!==e.width?this.width=e.width:"editor"===this.type&&(this.width=.66*(this.isCanvasOversizeX?this.viewportWidth:n.n)),void 0!==e.height)this.height=e.height;else{if("editor"===this.type){var t=.8*(this.isCanvasOversizeY?this.viewportHeight:n.k);t>1200&&(t=1200),t<400&&(t=400),this.height=t-2*this.offset}"audio"===this.type&&(this.height=0)}void 0!==e.top?this.top=e.top:"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||(this.top=this.offset),void 0!==e.left?this.left=e.left:("twilio"!==this.type&&"tokbox"!==this.type||(this.left=(this.isCanvasOversizeX?this.viewportWidth:n.n)-this.width-this.offset),"editor"===this.type&&(this.left=this.offset)),void 0!==e.index&&(this.index=e.index),void 0!==e.resizable&&(this.resizable=e.resizable),void 0!==e.draggable&&(this.draggable=e.draggable),void 0!==e.synchronizeable&&(this.synchronizeable=e.synchronizeable),void 0!==e.isHovered&&(this.isHoveredByAsset=e.isHovered)}}}),h=o(22),component=Object(h.a)(d,(function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{on:{mouseenter:e.mouseenter,mouseleave:e.mouseleave}},[o("vue-draggable-resizable",{ref:"vueDraggableResizable",class:{student:"student"===e.role,teacher:"teacher"===e.role,"hide-resize-icon":e.hideResizeIcon},style:{outline:e.isHoveredByAsset||e.isHovered?"3px solid "+e.getRoleHoverColor:"none"},attrs:{draggable:e.enabledDraggable,resizable:e.enabledResizeable,w:e.width,h:e.height+e.childHeaderHeight,x:e.leftScale,y:e.topScale,z:e.index,"zoom-index":e.scalable?e.zoom.zoomIndex:1,"zoom-x":e.zoom.x,"zoom-y":e.zoom.y,"child-header-height":e.childHeaderHeight,"lock-aspect-ratio":e.lockAspectRatio,handles:e.handles},on:{dragging:e.onDrag,resizing:e.onResize,dragstop:e.onDragStop,resizestop:e.onResizeStop,"update-asset":e.updateAsset},nativeOn:{click:function(t){return e.onIndex.apply(null,arguments)}}},[e._t("default",null,{onIndex:e.onIndex})],2)],1)}),[],!1,null,null,null);t.default=component.exports},1390:function(e,t,o){"use strict";o(872)("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),o(873))},1391:function(e,t,o){"use strict";o(11)({target:"Set",proto:!0,real:!0,forced:!0},{addAll:o(1384)})},1392:function(e,t,o){"use strict";o(11)({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:o(874)})},1393:function(e,t,o){"use strict";var r=o(11),n=o(87),l=o(43),d=o(79),h=o(32),c=o(125),m=o(86);r({target:"Set",proto:!0,real:!0,forced:!0},{difference:function(e){var t=h(this),o=new(c(t,n("Set")))(t),r=d(o.delete);return m(e,(function(e){l(r,o,e)})),o}})},1394:function(e,t,o){"use strict";var r=o(11),n=o(32),l=o(69),d=o(1372),h=o(86);r({target:"Set",proto:!0,real:!0,forced:!0},{every:function(e){var t=n(this),o=d(t),r=l(e,arguments.length>1?arguments[1]:void 0);return!h(o,(function(e,o){if(!r(e,e,t))return o()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1395:function(e,t,o){"use strict";var r=o(11),n=o(87),l=o(43),d=o(79),h=o(32),c=o(69),m=o(125),f=o(1372),v=o(86);r({target:"Set",proto:!0,real:!0,forced:!0},{filter:function(e){var t=h(this),o=f(t),r=c(e,arguments.length>1?arguments[1]:void 0),x=new(m(t,n("Set"))),y=d(x.add);return v(o,(function(e){r(e,e,t)&&l(y,x,e)}),{IS_ITERATOR:!0}),x}})},1396:function(e,t,o){"use strict";var r=o(11),n=o(32),l=o(69),d=o(1372),h=o(86);r({target:"Set",proto:!0,real:!0,forced:!0},{find:function(e){var t=n(this),o=d(t),r=l(e,arguments.length>1?arguments[1]:void 0);return h(o,(function(e,o){if(r(e,e,t))return o(e)}),{IS_ITERATOR:!0,INTERRUPTED:!0}).result}})},1397:function(e,t,o){"use strict";var r=o(11),n=o(87),l=o(43),d=o(79),h=o(32),c=o(125),m=o(86);r({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function(e){var t=h(this),o=new(c(t,n("Set"))),r=d(t.has),f=d(o.add);return m(e,(function(e){l(r,t,e)&&l(f,o,e)})),o}})},1398:function(e,t,o){"use strict";var r=o(11),n=o(43),l=o(79),d=o(32),h=o(86);r({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function(e){var t=d(this),o=l(t.has);return!h(e,(function(e,r){if(!0===n(o,t,e))return r()}),{INTERRUPTED:!0}).stopped}})},1399:function(e,t,o){"use strict";var r=o(11),n=o(87),l=o(43),d=o(79),h=o(45),c=o(32),m=o(209),f=o(86);r({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function(e){var t=m(this),o=c(e),r=o.has;return h(r)||(o=new(n("Set"))(e),r=d(o.has)),!f(t,(function(e,t){if(!1===l(r,o,e))return t()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1400:function(e,t,o){"use strict";var r=o(11),n=o(43),l=o(79),d=o(32),h=o(86);r({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function(e){var t=d(this),o=l(t.has);return!h(e,(function(e,r){if(!1===n(o,t,e))return r()}),{INTERRUPTED:!0}).stopped}})},1401:function(e,t,o){"use strict";var r=o(11),n=o(17),l=o(32),d=o(61),h=o(1372),c=o(86),m=n([].join),f=[].push;r({target:"Set",proto:!0,real:!0,forced:!0},{join:function(e){var t=l(this),o=h(t),r=void 0===e?",":d(e),n=[];return c(o,f,{that:n,IS_ITERATOR:!0}),m(n,r)}})},1402:function(e,t,o){"use strict";var r=o(11),n=o(87),l=o(69),d=o(43),h=o(79),c=o(32),m=o(125),f=o(1372),v=o(86);r({target:"Set",proto:!0,real:!0,forced:!0},{map:function(e){var t=c(this),o=f(t),r=l(e,arguments.length>1?arguments[1]:void 0),x=new(m(t,n("Set"))),y=h(x.add);return v(o,(function(e){d(y,x,r(e,e,t))}),{IS_ITERATOR:!0}),x}})},1403:function(e,t,o){"use strict";var r=o(11),n=o(5),l=o(79),d=o(32),h=o(1372),c=o(86),m=n.TypeError;r({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=d(this),o=h(t),r=arguments.length<2,n=r?void 0:arguments[1];if(l(e),c(o,(function(o){r?(r=!1,n=o):n=e(n,o,o,t)}),{IS_ITERATOR:!0}),r)throw m("Reduce of empty set with no initial value");return n}})},1404:function(e,t,o){"use strict";var r=o(11),n=o(32),l=o(69),d=o(1372),h=o(86);r({target:"Set",proto:!0,real:!0,forced:!0},{some:function(e){var t=n(this),o=d(t),r=l(e,arguments.length>1?arguments[1]:void 0);return h(o,(function(e,o){if(r(e,e,t))return o()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1405:function(e,t,o){"use strict";var r=o(11),n=o(87),l=o(43),d=o(79),h=o(32),c=o(125),m=o(86);r({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function(e){var t=h(this),o=new(c(t,n("Set")))(t),r=d(o.delete),f=d(o.add);return m(e,(function(e){l(r,o,e)||l(f,o,e)})),o}})},1406:function(e,t,o){"use strict";var r=o(11),n=o(87),l=o(79),d=o(32),h=o(125),c=o(86);r({target:"Set",proto:!0,real:!0,forced:!0},{union:function(e){var t=d(this),o=new(h(t,n("Set")))(t);return c(e,l(o.add),{that:o}),o}})},1428:function(e,t,o){"use strict";o(35),o(81),o(23),o(6),o(24),o(38);var r={computed:{role:function(){return this.$store.getters["classroom/role"]}},methods:{setTool:function(e,t){this.$store.commit("classroom/enableContainerComponent","pointer"===e),this.$socket.emit("cursor-moved",{tool:e,cursor:t.replace(/(-cursor|cursor-)/i,""),lessonId:this.$store.state.classroom.lessonId}),this.$store.commit("classroom/setUserTool",e),this.$store.commit("classroom/setUserCursor",t);var o=document.body,r=o.classList;this.removeCursors(r),o.classList.add("".concat(this.role,"-").concat(t)),this.classList=o.classList},removeCursors:function(e){e.forEach((function(e){e.includes("cursor")&&document.body.classList.remove(e)}))}}},n=o(22),component=Object(n.a)(r,undefined,undefined,!1,null,null,null);t.a=component.exports},1449:function(e,t,o){var content=o(1477);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,o(19).default)("07a1f444",content,!0,{sourceMap:!1})},1477:function(e,t,o){var r=o(18),n=o(265),l=o(870),d=r(!1),h=n(l);d.push([e.i,'.vdr{touch-action:none;position:absolute;box-sizing:border-box;border:none}.handle{display:block!important}.resizable.vdr:not(.hide-resize-icon):after{content:"";position:absolute;bottom:4px;right:2px;width:8px;height:8px;background-image:url('+h+');background-size:contain;background-repeat:no-repeat;background-position:50%;opacity:.9;z-index:99}.vdr .handle{box-sizing:border-box;position:absolute;width:12px;height:12px;border:none;background-color:transparent;z-index:10}.vdr .handle-tl{top:-7px;left:-7px}.vdr .handle-tm{top:-7px;margin-left:-5px}.vdr .handle-tr{top:-7px;right:-7px}.vdr .handle-ml{margin-top:-5px;left:-7px}.vdr .handle-mr{margin-top:-5px;right:-7px}.vdr .handle-bl{bottom:-7px;left:-7px}.vdr .handle-bm{bottom:-7px;margin-left:-5px}.vdr .handle-br{bottom:-7px;right:-7px}@media only screen and (max-width:768px){[class*=handle-]:before{content:"";left:-10px;right:-10px;bottom:-10px;top:-10px;position:absolute}}.vdr .handle-bm,.vdr .handle-tm{width:100%;left:5px}.vdr .handle-ml,.vdr .handle-mr{height:100%;top:5px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:14px;height:14px;z-index:11}@media only screen and (max-width:1199px){.vdr .handle-bm,.vdr .handle-tm{height:15px}.vdr .handle-ml,.vdr .handle-mr{width:15px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:15px;height:15px}.vdr .handle-tl{top:-10px;left:-10px}.vdr .handle-tm{top:-10px;margin-left:-5px}.vdr .handle-tr{top:-10px;right:-10px}.vdr .handle-ml{margin-top:-5px;left:-10px}.vdr .handle-mr{margin-top:-5px;right:-10px}.vdr .handle-bl{bottom:-10px;left:-10px}.vdr .handle-bm{bottom:-10px;margin-left:-5px}.vdr .handle-br{bottom:-10px;right:-10px}}',""]),e.exports=d},1625:function(e,t,o){"use strict";o.d(t,"a",(function(){return n}));o(2);var r=o(10);o(62),o(20),o(37),o(44),o(151),o(24),o(38),o(23),o(80),o(7),o(8),o(9),o(14),o(6),o(15);function n(e){return l.apply(this,arguments)}function l(){return(l=Object(r.a)(regeneratorRuntime.mark((function e(t){var o,r,n,l,d,h,c,data;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=t.lessonId,r=t.teacherId,n=t.studentId,l=t.isRecurring,d=void 0!==l&&l,e.prev=1,e.next=4,fetch("/_nuxt/api/whereby/create-room",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({lessonId:o,teacherId:r,studentId:n,isRecurring:d})});case 4:if((h=e.sent).ok){e.next=10;break}return e.next=8,h.json();case 8:throw c=e.sent,new Error(c.error||"Failed to create room");case 10:return e.next=12,h.json();case 12:return data=e.sent,e.abrupt("return",data.room);case 16:throw e.prev=16,e.t0=e.catch(1),console.error("Error creating Whereby room:",e.t0),e.t0;case 20:case"end":return e.stop()}}),e,null,[[1,16]])})))).apply(this,arguments)}},1666:function(e,t,o){var content=o(1788);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,o(19).default)("02017f86",content,!0,{sourceMap:!1})},1787:function(e,t,o){"use strict";o(1666)},1788:function(e,t,o){var r=o(18)(!1);r.push([e.i,".whereby-stream .whereby-component[data-v-08cfee22]{position:relative;background:#000;border-radius:8px;overflow:hidden}.whereby-stream .whereby-component .whereby-header[data-v-08cfee22]{position:absolute;top:0;left:0;right:0;height:20px;background:rgba(0,0,0,.7);display:flex;align-items:center;padding:0 12px;z-index:10;border-radius:8px 8px 0 0}.whereby-stream .whereby-component .whereby-header .whereby-header-title[data-v-08cfee22]{color:#fff;font-size:12px;font-weight:500;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.whereby-stream .whereby-component .whereby-header[data-v-08cfee22]:hover{background:rgba(0,0,0,.8)}.whereby-stream .whereby-component .whereby-video-container[data-v-08cfee22]{width:100%;height:100%;min-height:200px}.whereby-stream .whereby-component.screenshare-component .user-name[data-v-08cfee22]{position:absolute;top:10px;left:10px;background:rgba(0,0,0,.7);color:#fff;padding:5px 10px;border-radius:4px;font-size:12px;z-index:10}.whereby-stream .whereby-component.screenshare-component .screenshare-stream[data-v-08cfee22]{width:100%;height:100%;min-height:200px}.whereby-stream .whereby-component.video-window--is-fullscreen[data-v-08cfee22]{position:fixed;top:0;left:0;width:100vw!important;height:100vh!important;z-index:9999;border-radius:0}",""]),e.exports=r},1943:function(e,t,o){"use strict";o.r(t);var r=o(2),n=o(10),l=(o(62),o(63),o(20),o(37),o(44),o(151),o(24),o(38),o(23),o(80),o(7),o(8),o(9),o(14),o(6),o(15),o(695)),d=o(1625),h=o(859);function c(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,o)}return t}function m(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?c(Object(source),!0).forEach((function(t){Object(r.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):c(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var f={name:"Whereby",components:{ClassroomContainer:o(1389).default},props:{file:{type:Object,required:!0},screenShareAsset:{type:Object,required:!0},zoomOtherAsset:{type:Object,required:!0}},data:function(){return{localStreamContainer:null,screenShareStreamContainer:null,isLocalScreenShareEnabled:!1,isRemoteScreenShareEnabled:!1,screenSharingNotSupported:!Object(l.c)(),isJoined:!1,currentRole:"participant",wherebyRoom:null,isCreatingRoom:!1,settings:{isFullscreenEnabled:!1,isScreenShareEnabled:!1},isHandRaised:!1,isChatEnabled:!1,isParticipantsEnabled:!1}},computed:{lessonId:function(){return this.file.lessonId||this.$store.state.classroom.lessonId},teacherId:function(){return this.$store.state.classroom.teacherId},studentId:function(){return this.$store.state.classroom.studentId},role:function(){return this.$store.getters["classroom/role"]},isVideoEnabled:function(){var e,t,o,r;return null===(e=null===(t=this.file.asset)||void 0===t||null===(o=t.settings)||void 0===o||null===(r=o[this.role])||void 0===r?void 0:r.isVideoEnabled)||void 0===e||e},isMuted:function(){var e,t,o,r;return null!==(e=null===(t=this.file.asset)||void 0===t||null===(o=t.settings)||void 0===o||null===(r=o[this.role])||void 0===r?void 0:r.isMuted)&&void 0!==e&&e},maxIndex:function(){return this.$store.state.classroom.maxIndex}},mounted:function(){var e=this;this.$nextTick((function(){e.localStreamContainer=document.getElementById("whereby-video-container"),e.screenShareStreamContainer=document.getElementById("whereby-screenshare-placeholder"),e.localStreamContainer?e.initializeWhereby():(console.error("whereby-video-container not found, retrying in 500ms"),setTimeout((function(){e.localStreamContainer=document.getElementById("whereby-video-container"),e.localStreamContainer?e.initializeWhereby():console.error("whereby-video-container still not found after retry")}),500))})),window.addEventListener("beforeunload",this.closeStream),window.addEventListener("pagehide",this.closeStream),document.addEventListener("fullscreenchange",this.fullscreenChangeHandler)},beforeDestroy:function(){document.removeEventListener("fullscreenchange",this.fullscreenChangeHandler),this.closeStream()},methods:{initializeWhereby:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var o,iframe,r,n,l;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,e.localStreamContainer){t.next=4;break}return console.error("localStreamContainer is null"),t.abrupt("return");case 4:if(o="teacher"===e.role,e.currentRole=o?"host":"participant",e.isCreatingRoom=!0,e.localStreamContainer.innerHTML='\n          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">\n            <div style="margin-bottom: 20px;">\n              <h3 style="color: #333; margin-bottom: 10px;">Creating Whereby Room...</h3>\n              <p style="color: #666; margin-bottom: 20px;">Please wait while we set up your video call</p>\n            </div>\n            <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #5E72E4; border-radius: 50%; animation: spin 1s linear infinite;"></div>\n            <style>\n              @keyframes spin {\n                0% { transform: rotate(0deg); }\n                100% { transform: rotate(360deg); }\n              }\n            </style>\n          </div>\n        ',e.wherebyRoom){t.next=12;break}return t.next=11,Object(d.a)({lessonId:e.lessonId,teacherId:e.teacherId,studentId:e.studentId,isRecurring:!1});case 11:e.wherebyRoom=t.sent;case 12:e.isCreatingRoom=!1,iframe=document.createElement("iframe"),r="host"===e.currentRole?e.wherebyRoom.hostRoomUrl:e.wherebyRoom.roomUrl,n=new URLSearchParams({embed:"",displayName:e.$store.getters["classroom/userName"]||"User",audio:e.isMuted?"off":"on",video:e.isVideoEnabled?"on":"off",chat:"off",people:"off",screenshare:"on",reactions:"on",handRaise:"on",leaveButton:"off",background:"on",recording:"off",breakoutRooms:"on",whiteboard:"on",minimal:"false",skipMediaPermissionPrompt:"true",autoJoin:"true"}),l=r.includes("?")?"&":"?",iframe.src="".concat(r).concat(l).concat(n.toString()),iframe.style.width="100%",iframe.style.height="100%",iframe.style.border="none",iframe.style.borderRadius="8px",iframe.style.pointerEvents="auto",iframe.allow="camera; microphone; fullscreen; display-capture; autoplay",iframe.allowFullscreen=!0,e.localStreamContainer.innerHTML="",e.localStreamContainer.appendChild(iframe),window.addEventListener("message",e.handleWherebyMessage),setTimeout((function(){e.isJoined=!0}),1e3),t.next=36;break;case 31:t.prev=31,t.t0=t.catch(0),console.error("Failed to initialize Whereby:",t.t0),e.isCreatingRoom=!1,e.localStreamContainer&&(e.localStreamContainer.innerHTML='\n            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">\n              <div style="margin-bottom: 20px;">\n                <h3 style="color: #d32f2f; margin-bottom: 10px;">Failed to Create Room</h3>\n                <p style="color: #666; margin-bottom: 20px;">Unable to create Whereby room. Please try switching to another video provider.</p>\n              </div>\n              <button\n                onclick="this.parentElement.parentElement.parentElement.querySelector(\'.toolbar-button-item\').click()"\n                style="\n                  background: #5E72E4;\n                  color: white;\n                  border: none;\n                  padding: 12px 20px;\n                  border-radius: 6px;\n                  font-size: 14px;\n                  cursor: pointer;\n                  font-weight: 600;\n                "\n              >\n                Switch to Provider A\n              </button>\n            </div>\n          ');case 36:case"end":return t.stop()}}),t,null,[[0,31]])})))()},handleWherebyMessage:function(e){if(e.origin.includes("whereby.com"))try{var data=JSON.parse(e.data);switch(data.type){case"participant_join":case"participant_leave":break;case"screenshare_start":this.isRemoteScreenShareEnabled=!0;break;case"screenshare_stop":this.isRemoteScreenShareEnabled=!1;break;case"error":console.error("Whereby error:",data.error),this.handleMediaError(data.error)}}catch(e){}},handleMediaError:function(e){console.error("Whereby media error:",e),alert("Could not connect to video call. Please check your camera and microphone permissions.")},toggleVideo:function(){this.updateData(this.file.id,{settings:m(m({},this.file.asset.settings),{},Object(r.a)({},this.role,{isVideoEnabled:!this.isVideoEnabled,isMuted:this.isMuted}))})},toggleAudio:function(){this.updateData(this.file.id,{settings:m(m({},this.file.asset.settings),{},Object(r.a)({},this.role,{isVideoEnabled:this.isVideoEnabled,isMuted:!this.isMuted}))})},toggleFullScreen:function(){this.settings.isFullscreenEnabled=!this.settings.isFullscreenEnabled,Object(h.switchFullScreen)(this.settings.isFullscreenEnabled)},toggleScreenShare:function(){this.settings.isScreenShareEnabled=!this.settings.isScreenShareEnabled,this.settings.isScreenShareEnabled?(this.isLocalScreenShareEnabled=!1,this.screenShareStreamContainer.innerHTML=""):(this.updateData(this.screenShareAsset.id,{index:this.maxIndex+1}),this.isLocalScreenShareEnabled=!0)},toggleHandRaise:function(){this.isHandRaised=!this.isHandRaised},toggleChat:function(){this.isChatEnabled=!this.isChatEnabled},toggleParticipants:function(){this.isParticipantsEnabled=!this.isParticipantsEnabled},fullscreenChangeHandler:function(){document.fullscreenElement||(this.settings.isFullscreenEnabled=!1)},updateData:function(e,t){this.$store.commit("classroom/moveAsset",{id:e,asset:t}),this.$store.dispatch("classroom/moveAsset",{id:e,lessonId:this.file.lessonId,asset:t})},closeStream:function(){this.localStreamContainer&&(this.localStreamContainer.innerHTML=""),window.removeEventListener("message",this.handleWherebyMessage)}}},v=(o(1787),o(22)),component=Object(v.a)(f,(function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"whereby-stream"},[o("classroom-container",{attrs:{asset:e.file,"hover-enabled":!0,"lock-aspect-ratio":!1}},[o("div",{class:["whereby-component cursor-before-grab",{"video-window--is-fullscreen":e.settings.isFullscreenEnabled}],attrs:{id:"video-window"}},[o("div",{staticClass:"whereby-header cursor-before-grab"},[o("div",{staticClass:"whereby-header-title"})]),e._v(" "),o("div",{staticClass:"whereby-video-container",attrs:{id:"whereby-video-container"}})])]),e._v(" "),o("classroom-container",{directives:[{name:"show",rawName:"v-show",value:e.isLocalScreenShareEnabled||e.isRemoteScreenShareEnabled,expression:"isLocalScreenShareEnabled || isRemoteScreenShareEnabled"}],attrs:{asset:e.screenShareAsset,"hover-enabled":!0}},[o("div",{staticClass:"whereby-component screenshare-component cursor-before-grab"},[o("div",{staticClass:"user-name"},[e.isLocalScreenShareEnabled?[e._v("\n          "+e._s(e.$t("my_screen"))+"\n        ")]:e._e(),e._v(" "),e.isRemoteScreenShareEnabled?[e._v("\n          "+e._s(e.$t("classroom_user_screen",{username:e.zoomOtherAsset.asset.username}))+"\n        ")]:e._e()],2),e._v(" "),o("div",{staticClass:"screenshare-stream",attrs:{id:"whereby-screenshare-placeholder"}})])])],1)}),[],!1,null,"08cfee22",null);t.default=component.exports;installComponents(component,{ClassroomContainer:o(1389).default})}}]);