{"version": 3, "file": "components/steps.js", "sources": ["webpack:///./components/Steps.vue?d5f4", "webpack:///./components/Steps.vue?5dea", "webpack:///./components/Steps.vue?8045", "webpack:///./components/Steps.vue?9897", "webpack:///./components/Steps.vue", "webpack:///./components/Steps.vue?d37c", "webpack:///./components/Steps.vue?d334"], "sourcesContent": ["export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Steps.vue?vue&type=style&index=0&id=307c13c8&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_GET_URL_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/getUrl.js\");\nvar ___CSS_LOADER_URL_IMPORT_0___ = require(\"../assets/images/step-bg.svg\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"@media only screen and (min-width:992px){.steps[data-v-307c13c8]{margin-bottom:20px}}@media only screen and (max-width:991px){.steps-wrap[data-v-307c13c8]{width:calc(100% + 30px);height:50px;margin-left:-15px;margin-bottom:24px;overflow:hidden}}@media only screen and (max-width:991px){.steps-helper[data-v-307c13c8]{padding-bottom:17px;overflow-x:auto;overflow-y:hidden;box-sizing:content-box}}.steps-list[data-v-307c13c8]{display:flex;justify-content:space-between}@media only screen and (max-width:991px){.steps-list[data-v-307c13c8]{height:50px}}.steps .step-item[data-v-307c13c8]{position:relative;display:flex;align-items:center;width:100%;max-width:312px;margin-right:20px;padding:10px 22px;letter-spacing:.3px}@media only screen and (min-width:992px){.steps .step-item[data-v-307c13c8]{height:52px}.steps .step-item[data-v-307c13c8]:last-child{margin-right:0}}@media only screen and (min-width:992px)and (max-width:1439px){.steps .step-item[data-v-307c13c8]{margin-right:10px;padding:10px}}@media only screen and (max-width:991px){.steps .step-item[data-v-307c13c8]{flex:1 0 220px;width:220px;margin:0 0 0 15px}}@media only screen and (max-width:639px){.steps .step-item[data-v-307c13c8]{flex:1 0 280px;width:280px;padding:10px 5px 10px 12px}}.steps .step-item a[data-v-307c13c8]{position:absolute;top:0;left:0;width:100%;height:100%;z-index:2}.steps .step-item-helper[data-v-307c13c8]{position:relative;padding-left:48px}@media only screen and (max-width:639px){.steps .step-item-helper[data-v-307c13c8]{padding-left:45px}}.steps .step-item-number[data-v-307c13c8]{position:absolute;top:50%;left:0;display:flex;align-items:center;justify-content:center;width:33px;height:33px;padding:0 0 3px 2px;border-radius:50%;background:linear-gradient(126.15deg,rgba(128,182,34,.72),rgba(60,135,248,.72) 102.93%);transform:translateY(-50%)}.steps .step-item-number span[data-v-307c13c8]{position:relative;display:inline-block;font-size:16px;font-weight:700}.steps .step-item-title[data-v-307c13c8]{font-size:14px;font-weight:700;line-height:1.28}.steps .step-item-text[data-v-307c13c8]{font-size:12px;line-height:1.5}.steps .step-item:not(.step-item--active) .step-item-number span[data-v-307c13c8]{color:var(--v-success-base);background:linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));background:-webkit-linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));-webkit-background-clip:text;-webkit-text-fill-color:transparent}.steps .step-item:not(.step-item--active) .step-item-number[data-v-307c13c8]:before{content:\\\"\\\";position:absolute;top:1px;left:1px;width:calc(100% - 2px);height:calc(100% - 2px);background-color:var(--v-greyBg-base);border-radius:50%}.steps .step-item--active[data-v-307c13c8],.steps .step-item--link[data-v-307c13c8]:hover{background-image:url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \");background-repeat:no-repeat;background-size:100% 100%;background-position:50%}.steps .step-item--active .step-item-number[data-v-307c13c8],.steps .step-item--link:hover .step-item-number[data-v-307c13c8]{color:#fff}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Steps.vue?vue&type=style&index=0&id=307c13c8&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"9e60533c\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"steps\"},[_vm._ssrNode(\"<div class=\\\"steps-wrap\\\" data-v-307c13c8>\",\"</div>\",[_vm._ssrNode(\"<div id=\\\"steps-helper\\\" class=\\\"steps-helper\\\" data-v-307c13c8>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"steps-list\\\" data-v-307c13c8>\",\"</div>\",_vm._l((_vm.steps),function(step){return _vm._ssrNode(\"<div\"+(_vm._ssrAttr(\"id\",(\"step-\" + (step.id))))+(_vm._ssrClass(null,[\n            'step-item',\n            { 'step-item--active': step.id === _vm.activeItemId },\n            { 'step-item--link': _vm.itemLink.id === step.id } ]))+\" data-v-307c13c8>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"step-item-helper\\\" data-v-307c13c8><div class=\\\"step-item-number\\\" data-v-307c13c8><span data-v-307c13c8>\"+_vm._ssrEscape(_vm._s(step.id)+\".\")+\"</span></div> <div class=\\\"step-item-title\\\" data-v-307c13c8>\"+_vm._ssrEscape(_vm._s(_vm.$t(step.title)))+\"</div></div> \"),(_vm.itemLink.id === step.id)?_c('nuxt-link',{attrs:{\"to\":_vm.itemLink.path}}):_vm._e()],2)}),0)])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'Steps',\n  props: {\n    activeItemId: {\n      type: Number,\n      default: 1,\n    },\n    itemLink: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  mounted() {\n    const el = document.getElementById('steps-helper')\n    const activeEl = document.getElementById(`step-${this.activeItemId}`)\n\n    if (this.$vuetify.breakpoint.smAndDown && el && activeEl) {\n      const x = activeEl.getBoundingClientRect().left - 15\n\n      el.scrollTo({ left: x, behavior: 'smooth' })\n    }\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Steps.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Steps.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Steps.vue?vue&type=template&id=307c13c8&scoped=true&\"\nimport script from \"./Steps.vue?vue&type=script&lang=js&\"\nexport * from \"./Steps.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./Steps.vue?vue&type=style&index=0&id=307c13c8&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"307c13c8\",\n  \"7c31ffbf\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAtBA;;ACrCA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}