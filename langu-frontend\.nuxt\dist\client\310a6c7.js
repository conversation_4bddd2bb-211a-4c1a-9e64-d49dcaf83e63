(window.webpackJsonp=window.webpackJsonp||[]).push([[151,38,41,43,44,45,50,51,55],{1438:function(t,e,o){"use strict";var r=o(28),n=(o(20),o(1390),o(37),o(1391),o(1392),o(1393),o(1394),o(1395),o(1396),o(1397),o(1398),o(1399),o(1400),o(1401),o(1402),o(1403),o(1404),o(1405),o(1406),o(44),o(63),{data:function(){return{timeoutId:null,userStatuses:{},arrStatusId:[]}},computed:{preparedArr:function(){return Object(r.a)(new Set(this.arrStatusId))}},mounted:function(){var t=this;this.timeoutId=window.setInterval((function(){t.refreshStatusOnline(),t.arrStatusId.length||t.clearInterval()}),1e4)},beforeDestroy:function(){this.timeoutId&&this.clearInterval()},methods:{refreshStatusOnline:function(){var t=this;this.arrStatusId.length&&this.$store.dispatch("user/refreshStatusOnline",this.preparedArr).then((function(e){return t.userStatuses=e}))},clearInterval:function(){window.clearInterval(this.timeoutId),this.timeoutId=null}}}),l=o(22),component=Object(l.a)(n,undefined,undefined,!1,null,null,null);e.a=component.exports},1555:function(t,e,o){"use strict";o.r(e);o(7),o(8),o(9),o(14),o(15);var r=o(2),n=o(28),l=(o(31),o(6),o(23),o(1018)),d=o.n(l),c=o(266);function h(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var m={name:"Konva",props:{file:{type:Object,required:!0},width:{type:Number,required:!0},height:{type:Number,required:!0},scale:{type:Number,default:1},currentTime:{type:Number,default:null},currentPage:{type:Number,default:null},isMainKonva:{type:Boolean,default:!1}},data:function(){return{shapeData:null,beginPoint:null,konvaEl:null,konvaOverlayREl:null,konvaOverlayBEl:null}},computed:{isCanvasOversizeX:function(){return c.n>this.width},isScaledCanvasOversizeX:function(){return c.n*this.scale>this.width},isCanvasOversizeY:function(){return c.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return c.k*this.scale>this.height},assetType:function(){return this.file.asset.type},userParams:function(){return this.$store.getters["classroom/userParams"]},assetShapes:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.shapes)&&void 0!==t?t:[]},shapes:function(){var t=this,e=Object(n.a)(this.assetShapes),o=[];return this.shapeData&&e.push(this.shapeData),e.forEach((function(e){var n,l=function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?h(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):h(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},e);l.x=e.x-t.zoomX,l.y=e.y-t.zoomY,null!==(n=t.file.asset)&&void 0!==n&&n.originalWidth&&(l.strokeWidth=e.strokeWidth*t.file.asset.originalWidth/t.file.asset.width),o.push(l)})),o},zoomX:function(){return this.isMainKonva?this.zoom.x:0},zoomY:function(){return this.isMainKonva?this.zoom.y:0},zoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},config:function(){return{scale:{x:this.scale,y:this.scale},width:this.width,height:this.height,draggable:!1}}},watch:{width:function(t){this.stage.setWidth(t),this.isMainKonva&&(this.konvaOverlayREl&&this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayBEl&&this.setStyleForVerticalMainKonvaOverlays())},height:function(t){this.stage.setHeight(t),this.isMainKonva&&(this.konvaOverlayREl&&this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayBEl&&this.setStyleForVerticalMainKonvaOverlays())},scale:function(t){this.stage.setScale({x:t,y:t}),this.isMainKonva&&(this.konvaOverlayREl&&this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayBEl&&this.setStyleForVerticalMainKonvaOverlays())},isScaledCanvasOversizeX:function(t){this.isMainKonva&&(t?(this.konvaEl.removeChild(this.konvaOverlayREl),this.konvaOverlayREl=null):this.addHorizontalMainKonvaOverlays())},isScaledCanvasOversizeY:function(t){this.isMainKonva&&(t?(this.konvaEl.removeChild(this.konvaOverlayBEl),this.konvaOverlayBEl=null):this.addVerticalMainKonvaOverlays())}},mounted:function(){var t=this;this.stage=this.$refs.stage.getStage(),this.$nextTick((function(){t.move()})),this.konvaEl=document.getElementById("konva"),this.isMainKonva&&(this.isScaledCanvasOversizeX||this.addHorizontalMainKonvaOverlays(),this.isScaledCanvasOversizeY||this.addVerticalMainKonvaOverlays())},methods:{addHorizontalMainKonvaOverlays:function(){this.konvaOverlayREl||(this.konvaOverlayREl=document.createElement("div"),this.konvaOverlayREl.classList.add("konva-overlay-r"),this.setStyleForHorizontalMainKonvaOverlays(),this.konvaOverlayREl.addEventListener("mouseenter",this.mouseup),this.konvaEl.appendChild(this.konvaOverlayREl))},addVerticalMainKonvaOverlays:function(){this.konvaOverlayBEl||(this.konvaOverlayBEl=document.createElement("div"),this.konvaOverlayBEl.classList.add("konva-overlay-b"),this.setStyleForVerticalMainKonvaOverlays(),this.konvaOverlayBEl.addEventListener("mouseenter",this.mouseup),this.konvaEl.appendChild(this.konvaOverlayBEl))},setStyleForHorizontalMainKonvaOverlays:function(){this.konvaOverlayREl.style.position="absolute",this.konvaOverlayREl.style.top="0",this.konvaOverlayREl.style.right="0",this.konvaOverlayREl.style.width="".concat(this.width-c.n*this.scale,"px"),this.konvaOverlayREl.style.height="".concat(this.height,"px"),this.konvaOverlayREl.style.backgroundColor="#DCDCDD"},setStyleForVerticalMainKonvaOverlays:function(){this.konvaOverlayBEl.style.position="absolute",this.konvaOverlayBEl.style.bottom="0",this.konvaOverlayBEl.style.left="0",this.konvaOverlayBEl.style.width="".concat(this.width,"px"),this.konvaOverlayBEl.style.height="".concat(this.height-c.k*this.scale,"px"),this.konvaOverlayBEl.style.backgroundColor="#DCDCDD"},mousedown:function(t){var e=this.$refs.globalLayer.getNode(),o=t.target.getStage().getPointerPosition();switch(this.beginPoint={x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY},this.userParams.tool){case c.f:var r=new d.a.Circle({x:o.x/this.scale,y:o.y/this.scale,radius:2,stroke:this.userParams.color,strokeWidth:1});return e.add(r),void new d.a.Tween({node:r,duration:1,radius:24,onFinish:function(){return r.destroy()}}).play();case c.e:this.shapeData={type:"v-line",stroke:this.userParams.color,strokeWidth:5,x:0,y:0,points:[o.x/this.scale+this.zoomX,o.y/this.scale+this.zoomY],lineCap:"round",lineJoin:"round",tension:0,bezier:!0,perfectDrawEnabled:!1};break;case c.g:this.shapeData={type:"v-rect",x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY,width:1,height:1,stroke:this.userParams.color,strokeWidth:5};break;case c.b:this.shapeData={type:"v-circle",x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY,radius:1,stroke:this.userParams.color,strokeWidth:5};break;case c.h:this.shapeData={type:"v-line",stroke:this.userParams.color,strokeWidth:5,x:o.x/this.scale+this.zoomX,y:o.y/this.scale+this.zoomY,points:[0,0,0,0,0,0],tension:0,closed:!0};break;case c.d:this.shapeData={type:"v-line",stroke:this.userParams.color,strokeWidth:5,x:0,y:0,points:[o.x/this.scale+this.zoomX,o.y/this.scale+this.zoomY]};break;case c.c:this.shapeData={type:"v-line",stroke:"#f2f2f2",strokeWidth:30,x:0,y:0,points:[o.x/this.scale+this.zoomX,o.y/this.scale+this.zoomY],globalCompositeOperation:"destination-out"};break;default:console.warn("Requested action doesnt found for selected cursor - "+this.userParams.tool)}this.userParams.tool!==c.f&&("video"===this.assetType&&(this.shapeData.time=this.currentTime),"pdf"===this.assetType&&(this.shapeData.page=this.currentPage))},mouseup:function(){if(this.shapeData&&this.shapeData.type){var t={shapes:[].concat(Object(n.a)(this.assetShapes),[this.shapeData])};this.$store.commit("classroom/moveAsset",{id:this.file.id,asset:t}),this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.file.lessonId,asset:t}),this.beginPoint=null,this.shapeData=null}},move:function(t){if(this.shapeData){var e=t.target.getStage().getPointerPosition();this.drawing(e)}},drawing:function(t){var e,o;if(this.shapeData)switch(this.userParams.tool){case c.e:case c.c:this.shapeData.points=[].concat(Object(n.a)(this.shapeData.points),[t.x/this.scale+this.zoomX,t.y/this.scale+this.zoomY]);break;case c.g:this.shapeData.width=t.x/this.scale+this.zoomX-this.beginPoint.x,this.shapeData.height=t.y/this.scale+this.zoomY-this.beginPoint.y;break;case c.b:e=Math.abs(t.x/this.scale+this.zoomX-this.beginPoint.x),o=Math.abs(t.y/this.scale+this.zoomY-this.beginPoint.y),this.shapeData.radius=Math.max(e,o);break;case c.h:this.shapeData.points=[-(t.x/this.scale+this.zoomX-this.beginPoint.x),t.y/this.scale+this.zoomY-this.beginPoint.y,0,0,t.x/this.scale+this.zoomX-this.beginPoint.x,t.y/this.scale+this.zoomY-this.beginPoint.y];break;case c.d:this.shapeData.points=[this.beginPoint.x,this.beginPoint.y,t.x/this.scale+this.zoomX,t.y/this.scale+this.zoomY];break;default:console.warn("Requested action doesnt found for selected cursor")}}}},f=o(22),component=Object(f.a)(m,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-stage",{ref:"stage",attrs:{config:t.config},on:{mousedown:t.mousedown,touchstart:t.mousedown,touchmove:t.move,touchend:t.mouseup,mouseup:t.mouseup,mousemove:t.move}},[o("v-layer",{ref:"globalLayer"},[t._l(t.shapes,(function(e,r){return[!e.hasOwnProperty("time")&&!e.hasOwnProperty("page")||e.hasOwnProperty("time")&&t.currentTime+1>=e.time&&e.time>=t.currentTime-1||e.hasOwnProperty("page")&&t.currentPage===e.page?o(e.type,{key:r,tag:"component",attrs:{config:e}}):t._e()]}))],2)],1)}),[],!1,null,null,null);e.default=component.exports},1621:function(t,e,o){"use strict";var r=o(2),n=o(28),l=o(10),d=(o(62),o(40),o(174),o(24),o(38),o(20),o(80),o(6),o(7),o(8),o(9),o(14),o(15),o(859)),c=o(266);function h(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}function m(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?h(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):h(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var f={computed:{lessonId:function(){return this.$store.state.classroom.lessonId},role:function(){return this.$store.getters["classroom/role"]},acceptedFiles:function(){return this.$store.state.classroom.acceptedFiles}},methods:{uploadFiles:function(t){var e=this;return Object(l.a)(regeneratorRuntime.mark((function o(){var r,i,l,h,f,data,v;return regeneratorRuntime.wrap((function(o){for(;;)switch(o.prev=o.next){case 0:t=Object(n.a)(t),r=new FormData,i=0;case 3:if(!(i<=t.length-1)){o.next=23;break}if(l=t[i],h=Object(d.getFileExtension)(l.name),!(l.size>c.a)){o.next=10;break}return o.next=9,e.$store.dispatch("snackbar/error",{errorMessage:e.$t("filename_size_should_be_less_than",{fileName:l.name,value:"".concat((c.a/8/1e3).toFixed(0)," Mb")}),timeout:5e3});case 9:return o.abrupt("continue",20);case 10:if(!e.acceptedFiles.officeTypes.includes(h)){o.next=19;break}return o.next=13,e.$store.dispatch("classroom/convertOfficeToPdf",l);case 13:f=o.sent,data=f.data,v=f.fileName,r.append(i.toString(),new Blob([data]),v),o.next=20;break;case 19:r.append(i.toString(),l);case 20:i++,o.next=3;break;case 23:e.$store.dispatch("classroom/uploadFiles",r).then((function(t){var o=0,r=0;e.$store.commit("classroom/addAssets",t),t.forEach((function(t){var n,l,c,h,f={id:t.id,lessonId:e.lessonId,asset:m(m({},t.asset),{},{index:e.$store.state.classroom.maxIndex+1,owner:e.role,top:e.$store.getters["classroom/zoomAsset"].asset.y+r+100,left:e.viewportWidth/2+e.$store.getters["classroom/zoomAsset"].asset.x+o-250})},v=Object(d.getFileExtension)(f.asset.path);if(null!==(n=e.acceptedFiles)&&void 0!==n&&n.pdfTypes.includes(v))h="pdf";else if(null!==(l=e.acceptedFiles)&&void 0!==l&&l.imageTypes.includes(v))h="image";else{if(null===(c=e.acceptedFiles)||void 0===c||!c.audioTypes.includes(v))return;h="audio"}f.asset.type=h,e.$store.commit("classroom/moveAsset",f),e.$store.dispatch("classroom/moveAsset",f),e.$socket.emit("asset-added",f),o+=50,r+=50}))})).catch((function(t){throw t}));case 24:case"end":return o.stop()}}),o)})))()}}},v=o(22),component=Object(v.a)(f,undefined,undefined,!1,null,null,null);e.a=component.exports},1625:function(t,e,o){"use strict";o.d(e,"a",(function(){return n}));o(2);var r=o(10);o(62),o(20),o(37),o(44),o(151),o(24),o(38),o(23),o(80),o(7),o(8),o(9),o(14),o(6),o(15);function n(t){return l.apply(this,arguments)}function l(){return(l=Object(r.a)(regeneratorRuntime.mark((function t(e){var o,r,n,l,d,c,h,data;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o=e.lessonId,r=e.teacherId,n=e.studentId,l=e.isRecurring,d=void 0!==l&&l,t.prev=1,t.next=4,fetch("/_nuxt/api/whereby/create-room",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({lessonId:o,teacherId:r,studentId:n,isRecurring:d})});case 4:if((c=t.sent).ok){t.next=10;break}return t.next=8,c.json();case 8:throw h=t.sent,new Error(h.error||"Failed to create room");case 10:return t.next=12,c.json();case 12:return data=t.sent,t.abrupt("return",data.room);case 16:throw t.prev=16,t.t0=t.catch(1),console.error("Error creating Whereby room:",t.t0),t.t0;case 20:case"end":return t.stop()}}),t,null,[[1,16]])})))).apply(this,arguments)}},1658:function(t,e,o){var content=o(1766);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("88facf08",content,!0,{sourceMap:!1})},1659:function(t,e,o){var content=o(1768);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("ab02e688",content,!0,{sourceMap:!1})},1660:function(t,e,o){var content=o(1771);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("194819e0",content,!0,{sourceMap:!1})},1661:function(t,e,o){var content=o(1774);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("dcaa6c5e",content,!0,{sourceMap:!1})},1662:function(t,e,o){var content=o(1776);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("013ffd6a",content,!0,{sourceMap:!1})},1663:function(t,e,o){var content=o(1778);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("ceb0bb2a",content,!0,{sourceMap:!1})},1664:function(t,e,o){var content=o(1784);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("6cc1002b",content,!0,{sourceMap:!1})},1665:function(t,e,o){var content=o(1786);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("fe9288ee",content,!0,{sourceMap:!1})},1666:function(t,e,o){var content=o(1788);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("02017f86",content,!0,{sourceMap:!1})},1667:function(t,e,o){var content=o(1790);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("67f245fe",content,!0,{sourceMap:!1})},1668:function(t,e,o){var content=o(1792);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("d534cc50",content,!0,{sourceMap:!1})},1669:function(t,e,o){var content=o(1794);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("3a5e080a",content,!0,{sourceMap:!1})},1765:function(t,e,o){"use strict";o(1658)},1766:function(t,e,o){var r=o(18)(!1);r.push([t.i,".popup-load-files-drop-wrap[data-v-190879a5]{position:relative!important;display:flex;justify-content:center;align-items:center;height:100vh!important;background-color:hsla(0,0%,100%,.9);z-index:999999;pointer-events:none}.popup-load-files-drop-wrap .drop-area--wrapper[data-v-190879a5]{padding:30px}.popup-load-files-drop-wrap .drop-area--wrapper__dropbox-img[data-v-190879a5]{width:100%;height:auto}",""]),t.exports=r},1767:function(t,e,o){"use strict";o(1659)},1768:function(t,e,o){var r=o(18)(!1);r.push([t.i,".viewport-component[data-v-4a5272da]{position:absolute;border-width:4px;border-style:solid}.user-name[data-v-4a5272da]{position:absolute;top:0;right:0;padding:4px 13px;line-height:1;background:#fff;font-size:12px;font-weight:500;box-shadow:0 0 5px rgba(0,0,0,.2);border:none;border-bottom-left-radius:6px;z-index:2}.user-name--tl[data-v-4a5272da]{top:0;right:auto;left:0;border-bottom-left-radius:0;border-bottom-right-radius:6px}.user-name--br[data-v-4a5272da]{right:0;border-top-left-radius:6px}.user-name--bl[data-v-4a5272da],.user-name--br[data-v-4a5272da]{top:auto;bottom:0;border-bottom-left-radius:0}.user-name--bl[data-v-4a5272da]{right:auto;left:0;border-top-right-radius:6px}",""]),t.exports=r},1769:function(t,e,o){var map={"./arrow-left.svg":885,"./arrow-right.svg":968,"./chat.svg":932,"./corner-resize-marker.svg":870,"./cursor-student-down.svg":940,"./cursor-student-right.svg":941,"./cursor-teacher-down.svg":942,"./cursor-teacher-right.svg":943,"./cursor_hand_teacher.svg":969,"./dropfiles.svg":908,"./full_screen.svg":882,"./hand.svg":933,"./microphone.svg":883,"./not_share.svg":875,"./participants.svg":934,"./student-arrow-2.svg":944,"./student-arrow.svg":945,"./student-beforeGrab.svg":946,"./student-cursor-link.svg":947,"./student-dragging.svg":948,"./student-eraser.svg":949,"./student-pencil.svg":950,"./student-pointer.svg":951,"./student-text-cursor.svg":952,"./teacher-arrow-2.svg":953,"./teacher-arrow.svg":954,"./teacher-beforeGrab.svg":955,"./teacher-cursor-link.svg":956,"./teacher-dragging.svg":957,"./teacher-eraser.svg":958,"./teacher-pencil.svg":959,"./teacher-pointer.svg":960,"./teacher-text-cursor.svg":961,"./tick2.svg":936,"./toolbar.svg":862,"./videocam.svg":884,"./volume-high.svg":937};function r(t){var e=n(t);return o(e)}function n(t){if(!o.o(map,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return map[t]}r.keys=function(){return Object.keys(map)},r.resolve=n,t.exports=r,r.id=1769},1770:function(t,e,o){"use strict";o(1660)},1771:function(t,e,o){var r=o(18)(!1);r.push([t.i,".other_cursor[data-v-b743e11e]{width:100%;position:absolute;z-index:99998;background-size:contain!important;background-position:50%!important;background-repeat:no-repeat!important}.other_cursor.pointer[data-v-b743e11e]{width:22px;height:23px}.other_cursor.pencil[data-v-b743e11e]{width:23px;height:23px;margin:-22px 0 0}.other_cursor.eraser[data-v-b743e11e]{width:25px;height:23px;margin:-20px 0 0 -10px}.other_cursor .cursor-name[data-v-b743e11e]{position:absolute;left:35px;bottom:-20px;max-width:180px;height:20px;padding:0 8px;white-space:nowrap;text-overflow:ellipsis;color:#fff;font-size:13px;line-height:20px;border-radius:3px;font-weight:600;overflow:hidden}@media only screen and (max-width:767px){.other_cursor .cursor-name[data-v-b743e11e]{left:25px;bottom:-15px;max-width:80px;height:16px;font-weight:400;font-size:11px;line-height:16px}}",""]),t.exports=r},1772:function(t,e,o){"use strict";(function(t){o.d(e,"a",(function(){return n}));var r=function(){return"undefined"!=typeof window?window:t},n=function(){var t=r();return t&&t.tinymce?t.tinymce:null}}).call(this,o(56))},1773:function(t,e,o){"use strict";o(1661)},1774:function(t,e,o){var r=o(18)(!1);r.push([t.i,'body.handle-bl .tox-sidebar-wrap:after,body.handle-bm .tox-sidebar-wrap:after,body.handle-br .tox-sidebar-wrap:after,body.handle-ml .tox-sidebar-wrap:after,body.handle-mr .tox-sidebar-wrap:after,body.handle-tl .tox-sidebar-wrap:after,body.handle-tm .tox-sidebar-wrap:after,body.handle-tr .tox-sidebar-wrap:after{content:"";position:absolute;top:0;left:0;width:100%;height:100%}body .tox-tinymce-aux{z-index:100001!important}body .tox{font-family:"Lato",sans-serif!important}body .tox .tox-dialog-wrap__backdrop{background-color:rgba(0,0,0,.4)}body .tox .tox-dialog{box-shadow:0 8px 17px rgba(17,46,90,.1);border-radius:20px;border:none}@media only screen and (max-width:991px){body .tox .tox-dialog{border-radius:15px}}body .tox .tox-dialog__header{padding:16px 30px 0}@media only screen and (max-width:991px){body .tox .tox-dialog__header{padding:12px 15px 0}}body .tox .tox-dialog__title{font-size:20px;font-weight:700;font-family:inherit;color:var(--v-success-base);background:linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));background:-webkit-linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));-webkit-background-clip:text;-webkit-text-fill-color:transparent}body .tox .tox-dialog__body-content{padding:16px 30px}@media only screen and (max-width:991px){body .tox .tox-dialog__body-content{padding:16px 15px}}body .tox .tox-dialog__footer{padding:8px 30px 24px;border-top:none}@media only screen and (max-width:991px){body .tox .tox-dialog__footer{padding:8px 15px 15px}}body .tox .tox-dialog__footer .tox-button{min-width:162px;height:38px;border-radius:20px;font-weight:600;font-size:15px}@media only screen and (max-width:767px){body .tox .tox-dialog__footer .tox-button{min-width:120px}}body .tox .tox-button{font-family:inherit}body .tox .tox-button,body .tox .tox-button *{cursor:pointer!important}body .tox .tox-button:not(.tox-button--secondary):not(.tox-button--naked){background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);border:none!important}body .tox .tox-button:not(.tox-button--secondary):not(.tox-button--naked):hover{background:linear-gradient(305.26deg,var(--v-success-base) 8.94%,var(--v-primary-base) 110.83%)!important}body .tox label{font-family:inherit!important}body .tox .tox-listboxfield .tox-listbox--select,body .tox .tox-textarea,body .tox .tox-textfield{font-family:inherit;cursor:auto!important}body.teacher-role .tox .tox-listboxfield .tox-listbox--select:focus,body.teacher-role .tox .tox-textarea:focus,body.teacher-role .tox .tox-textfield:focus{border-color:var(--v-teacherColor-base)}body.student-role .tox .tox-listboxfield .tox-listbox--select:focus,body.student-role .tox .tox-textarea:focus,body.student-role .tox .tox-textfield:focus{border-color:var(--v-studentColor-base)}.no-scroll{pointer-events:none}.classroom-editor{display:flex;flex-direction:column;height:100%;background-color:#fff;box-shadow:0 2px 30px rgba(0,0,0,.25)}.classroom-editor button,.classroom-editor button *,.classroom-editor div[role=button],.classroom-editor div[role=button] *{cursor:auto!important}.classroom-editor .tox-sidebar-wrap{position:relative}.classroom-editor .tox-edit-area *{cursor:auto!important}.classroom-editor .tox-edit-area a{cursor:pointer!important}.classroom-editor .tox-statusbar__path{display:none!important}',""]),t.exports=r},1775:function(t,e,o){"use strict";o(1662)},1776:function(t,e,o){var r=o(18)(!1);r.push([t.i,".plyr--video .plyr__controls{position:fixed!important}.plyr--video .plyr__progress .time-markers{position:absolute;top:50%;left:0;width:calc(100% + 13px);width:calc(100% + var(--plyr-range-thumb-height, 13px));height:5px;height:var(--plyr-range-track-height,5px);margin-top:-2.5px;margin-top:calc(var(--plyr-range-track-height, 5px)/2*-1);margin-left:-6.5px;margin-left:calc(var(--plyr-range-thumb-height, 13px)*-0.5)}.plyr--video .plyr__progress .time-markers span{position:absolute;top:0;display:block;width:1px;height:100%;background-color:#fff;z-index:2}",""]),t.exports=r},1777:function(t,e,o){"use strict";o(1663)},1778:function(t,e,o){var r=o(18)(!1);r.push([t.i,'.plyr--audio .plyr__controls{position:relative;width:100%;color:#fff}.plyr--audio .plyr__controls:before{content:"";position:absolute;top:0;left:1px;width:calc(100% - 2px);height:1px;background-color:rgba(0,0,0,.3)}',""]),t.exports=r},1779:function(t,e){},1780:function(t,e){},1781:function(t,e){},1782:function(t,e){},1783:function(t,e,o){"use strict";o(1664)},1784:function(t,e,o){var r=o(18)(!1);r.push([t.i,".pdf-controls{display:flex;justify-content:space-between;align-items:center;box-shadow:0 -1px 3px rgba(0,0,0,.25);position:relative;padding:5px 12px;background:#fff}.pdf-controls button{border:none;background:none}.pdf-controls button,.pdf-controls button *{cursor:pointer!important}.pdf-controls button.btn-next .v-image{transform:rotate(180deg)}.pdf-controls button:disabled,.pdf-controls button[disabled]{opacity:0;visibility:hidden}",""]),t.exports=r},1785:function(t,e,o){"use strict";o(1665)},1786:function(t,e,o){var r=o(18)(!1);r.push([t.i,".image-wrap-classroom img[data-v-6c1f4906]{display:block;width:100%;height:100%}",""]),t.exports=r},1787:function(t,e,o){"use strict";o(1666)},1788:function(t,e,o){var r=o(18)(!1);r.push([t.i,".whereby-stream .whereby-component[data-v-08cfee22]{position:relative;background:#000;border-radius:8px;overflow:hidden}.whereby-stream .whereby-component .whereby-header[data-v-08cfee22]{position:absolute;top:0;left:0;right:0;height:20px;background:rgba(0,0,0,.7);display:flex;align-items:center;padding:0 12px;z-index:10;border-radius:8px 8px 0 0}.whereby-stream .whereby-component .whereby-header .whereby-header-title[data-v-08cfee22]{color:#fff;font-size:12px;font-weight:500;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.whereby-stream .whereby-component .whereby-header[data-v-08cfee22]:hover{background:rgba(0,0,0,.8)}.whereby-stream .whereby-component .whereby-video-container[data-v-08cfee22]{width:100%;height:100%;min-height:200px}.whereby-stream .whereby-component.screenshare-component .user-name[data-v-08cfee22]{position:absolute;top:10px;left:10px;background:rgba(0,0,0,.7);color:#fff;padding:5px 10px;border-radius:4px;font-size:12px;z-index:10}.whereby-stream .whereby-component.screenshare-component .screenshare-stream[data-v-08cfee22]{width:100%;height:100%;min-height:200px}.whereby-stream .whereby-component.video-window--is-fullscreen[data-v-08cfee22]{position:fixed;top:0;left:0;width:100vw!important;height:100vh!important;z-index:9999;border-radius:0}",""]),t.exports=r},1789:function(t,e,o){"use strict";o(1667)},1790:function(t,e,o){var r=o(18)(!1);r.push([t.i,".video-component,.video-component *{cursor:auto!important}.video-component .v-card{padding:70px 15px 20px}.video-component .v-input .v-input__slot{background-color:#fff}.video-component .input-wrap{position:relative}.video-component .input-wrap-error{position:absolute;bottom:8px;left:0;padding-left:16px}.video-component .video-buttons-wrap{display:flex;justify-content:center;align-items:center;width:100%}.video-component button,.video-component button *{cursor:pointer!important}",""]),t.exports=r},1791:function(t,e,o){"use strict";o(1668)},1792:function(t,e,o){var r=o(18)(!1);r.push([t.i,".popup-load-files-item-img .preview-fluid[data-v-6a1d2341]{max-width:75%;max-height:75%}.popup-load-files-header.inactive[data-v-6a1d2341]{display:none}",""]),t.exports=r},1793:function(t,e,o){"use strict";o(1669)},1794:function(t,e,o){var r=o(18)(!1);r.push([t.i,".toolbar[data-v-53b5e223]{position:fixed;z-index:99999!important}label.popup-load-files-label-upload[data-v-53b5e223]{margin-right:0}.toolbar-buttons-wrapper[data-v-53b5e223]{position:absolute;top:50%;right:2%;transform:translateY(-50%)}.cursor-pointer[data-v-53b5e223],.cursor-pointer *[data-v-53b5e223]{cursor:pointer!important}.toolbar-buttons[data-v-53b5e223]{margin-bottom:0;padding:8px 0}.toolbar-buttons-horizontal-file.toolbar-show[data-v-53b5e223],.toolbar-buttons-horizontal.toolbar-show[data-v-53b5e223]{display:flex!important}.toolbar-buttons li[data-v-53b5e223]{list-style:none}.toolbar-button-wrapper form[data-v-53b5e223]{display:inline-block;width:100%}.toolbar-button-wrapper-horizontal[data-v-53b5e223]{width:40px;height:40px;display:flex;justify-content:center;position:relative}.toolbar-button-wrapper-pencil>button[data-v-53b5e223]{padding:9px}.toolbar-button-wrapper-exit button[data-v-53b5e223]{padding-left:7px;padding-right:10px}.toolbar-button-wrapper-reset button[data-v-53b5e223]{padding-left:10px;padding-right:10px}.toolbar-button-wrapper-finish button[data-v-53b5e223]{padding-right:7px}.toolbar-button-wrapper-horizontal-books button[data-v-53b5e223]{padding:9px}.toolbar-button-wrapper-horizontal-laptop button[data-v-53b5e223]{padding-top:10px}.toolbar-buttons-horizontal>ul[data-v-53b5e223]{display:flex;padding:0 10px;background-color:#fff;border-radius:6px;box-shadow:0 2px 10px rgba(0,0,0,.25)}.toolbar-button-wrapper-horizontal-draw-line button[data-v-53b5e223]{padding:10px 5px 6px 10px}.toolbar-button-wrapper-horizontal-draw-pencil button[data-v-53b5e223]{padding:9px}.toolbar-button-item-hand[data-v-53b5e223]{padding-left:0}.toolbar-buttons-horizontal>ul li[data-v-53b5e223]:first-child,.toolbar-buttons-horizontal>ul li:first-child button[data-v-53b5e223]{border-bottom-left-radius:6px!important;border-top-left-radius:6px!important}.toolbar-buttons-horizontal>ul li[data-v-53b5e223]:last-child,.toolbar-buttons-horizontal>ul li:last-child button[data-v-53b5e223]{border-bottom-right-radius:6px!important;border-top-right-radius:6px!important}#toolbar-switch[data-v-53b5e223]{border-top-left-radius:4px;border-top-right-radius:4px}.toolbar--student .toolbar-button-item.selected:not(:disabled) svg[data-v-53b5e223],.toolbar--student .toolbar-button-wrapper-horizontal:hover svg[data-v-53b5e223],.toolbar--student .toolbar-button-wrapper:hover>button:enabled>svg[data-v-53b5e223],.toolbar--student .toolbar-button-wrapper:hover>form>button:enabled>svg[data-v-53b5e223]{color:var(--v-studentColor-base)!important}.toolbar--teacher .toolbar-button-item.selected:not(:disabled) svg[data-v-53b5e223],.toolbar--teacher .toolbar-button-wrapper-horizontal:hover svg[data-v-53b5e223],.toolbar--teacher .toolbar-button-wrapper:hover>button:enabled>svg[data-v-53b5e223],.toolbar--teacher .toolbar-button-wrapper:hover>form>button:enabled>svg[data-v-53b5e223]{color:var(--v-teacherColor-base)!important}.toolbar-button-wrapper .toolbar-button-item:disabled svg[data-v-53b5e223]{color:#c6c6c6!important;fill:#c6c6c6!important;stroke:#c6c6c6!important}.toolbar-button-item svg[data-v-53b5e223]{color:var(--v-darkLight-base)}.hover-btn-info-horizontal[data-v-53b5e223]{top:-20px;right:-60%}.toolbar-button-replace+.hover-btn-info[data-v-53b5e223]{top:40%}.selected[data-v-53b5e223]{border-bottom:none}",""]),t.exports=r},1858:function(t,e,o){var content=o(2064);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("71c58bc3",content,!0,{sourceMap:!1})},1859:function(t,e,o){var content=o(2066);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("200ba9c6",content,!0,{sourceMap:!1})},1912:function(t,e,o){"use strict";o.r(e);o(7),o(8),o(9),o(14),o(6),o(15);var r,n=o(2),l=o(10),d=(o(62),o(88),o(20),o(24),o(80),["onActivate","onAddUndo","onBeforeAddUndo","onBeforeExecCommand","onBeforeGetContent","onBeforeRenderUI","onBeforeSetContent","onBeforePaste","onBlur","onChange","onClearUndos","onClick","onContextMenu","onCopy","onCut","onDblclick","onDeactivate","onDirty","onDrag","onDragDrop","onDragEnd","onDragGesture","onDragOver","onDrop","onExecCommand","onFocus","onFocusIn","onFocusOut","onGetContent","onHide","onInit","onKeyDown","onKeyPress","onKeyUp","onLoadContent","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onNodeChange","onObjectResizeStart","onObjectResized","onObjectSelected","onPaste","onPostProcess","onPostRender","onPreProcess","onProgressState","onRedo","onRemove","onReset","onSaveContent","onSelectionChange","onSetAttrib","onSetContent","onShow","onSubmit","onUndo","onVisualAid"]),c=function(t){return-1!==d.map((function(t){return t.toLowerCase()})).indexOf(t.toLowerCase())},h=function(t,e,o){var r=e.$props.value?e.$props.value:"",n=e.$props.initialValue?e.$props.initialValue:"";o.setContent(r||(e.initialized?e.cache:n)),e.$watch("value",(function(t,r){o&&"string"==typeof t&&t!==r&&t!==o.getContent({format:e.$props.outputFormat})&&o.setContent(t)})),e.$listeners.input&&function(t,e){var o=t.$props.modelEvents?t.$props.modelEvents:null,r=Array.isArray(o)?o.join(" "):o;e.on(r||"change input undo redo",(function(){t.$emit("input",e.getContent({format:t.$props.outputFormat}))}))}(e,o),function(t,e,o){Object.keys(e).filter(c).forEach((function(r){var n=e[r];"function"==typeof n&&("onInit"===r?n(t,o):o.on(r.substring(2),(function(t){return n(t,o)})))}))}(t,e.$listeners,o),e.initialized=!0},m=0,f=function(t){var time=Date.now();return t+"_"+Math.floor(1e9*Math.random())+ ++m+String(time)},v=function(t){return void 0===t||""===t?[]:Array.isArray(t)?t:t.split(" ")},x=function(){return{listeners:[],scriptId:f("tiny-script"),scriptLoaded:!1}},y=(r=x(),{load:function(t,e,o){r.scriptLoaded?o():(r.listeners.push(o),t.getElementById(r.scriptId)||function(t,e,o,r){var n=e.createElement("script");n.referrerPolicy="origin",n.type="application/javascript",n.id=t,n.src=o;var l=function(){n.removeEventListener("load",l),r()};n.addEventListener("load",l),e.head&&e.head.appendChild(n)}(r.scriptId,t,e,(function(){r.listeners.forEach((function(t){return t()})),r.scriptLoaded=!0})))},reinitialize:function(){r=x()}}),w=o(1772),k={apiKey:String,cloudChannel:String,id:String,init:Object,initialValue:String,inline:Boolean,modelEvents:[String,Array],plugins:[String,Array],tagName:String,toolbar:[String,Array],value:String,disabled:Boolean,tinymceScriptSrc:String,outputFormat:{type:String,validator:function(t){return"html"===t||"text"===t}}},C=function(){return(C=Object.assign||function(t){for(var s,i=1,e=arguments.length;i<e;i++)for(var p in s=arguments[i])Object.prototype.hasOwnProperty.call(s,p)&&(t[p]=s[p]);return t}).apply(this,arguments)},z=function(t){return function(){var e,o,element,r=C(C({},t.$props.init),{readonly:t.$props.disabled,selector:"#"+t.elementId,plugins:(e=t.$props.init&&t.$props.init.plugins,o=t.$props.plugins,v(e).concat(v(o))),toolbar:t.$props.toolbar||t.$props.init&&t.$props.init.toolbar,inline:t.inlineEditor,setup:function(e){t.editor=e,e.on("init",(function(o){return h(o,t,e)})),t.$props.init&&"function"==typeof t.$props.init.setup&&t.$props.init.setup(e)}});null!==(element=t.element)&&"textarea"===element.tagName.toLowerCase()&&(t.element.style.visibility="",t.element.style.display=""),Object(w.a)().init(r)}},_={props:k,created:function(){this.elementId=this.$props.id||f("tiny-vue"),this.inlineEditor=this.$props.init&&this.$props.init.inline||this.$props.inline,this.initialized=!1},watch:{disabled:function(){this.editor.setMode(this.disabled?"readonly":"design")}},mounted:function(){if(this.element=this.$el,null!==Object(w.a)())z(this)();else if(this.element&&this.element.ownerDocument){var t=this.$props.cloudChannel?this.$props.cloudChannel:"5",e=this.$props.apiKey?this.$props.apiKey:"no-api-key",o=null==this.$props.tinymceScriptSrc?"https://cdn.tiny.cloud/1/"+e+"/tinymce/"+t+"/tinymce.min.js":this.$props.tinymceScriptSrc;y.load(this.element.ownerDocument,o,z(this))}},beforeDestroy:function(){null!==Object(w.a)()&&Object(w.a)().remove(this.editor)},deactivated:function(){var t;this.inlineEditor||(this.cache=this.editor.getContent(),null===(t=Object(w.a)())||void 0===t||t.remove(this.editor))},activated:function(){!this.inlineEditor&&this.initialized&&z(this)()},render:function(t){return this.inlineEditor?function(t,e,o){return t(o||"div",{attrs:{id:e}})}(t,this.elementId,this.$props.tagName):function(t,e){return t("textarea",{attrs:{id:e},style:{visibility:"hidden"}})}(t,this.elementId)}},O=o(859);function S(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}function $(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?S(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):S(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var E={name:"TinymceVue",components:{ClassroomContainer:o(1389).default,Editor:_},props:{file:{type:Object,required:!0}},data:function(){return{frameElId:"tinymce_editor",apiKey:"myt0tdi0h4brlcvgco6woa4zkus1ouk8l90uxuzlk6lunjn2",editor:null,text:"",toolbar:["bold italic strikethrough underline","forecolor backcolor","link hr table","alignleft aligncenter alignright alignjustify","bullist numlist outdent indent","exportButton codesample"].join(" | "),content_style:"\n        html {\n          height: 100%;\n          cursor: text;\n        }\n\n        body {\n          height: calc(100% - 30px);\n          margin: 15px;\n        }\n\n        .mce-content-body[data-mce-placeholder]::before {\n          cursor: text;\n        }\n\n        body p {\n          margin: 15px 0;\n        }\n      ",frameEl:null,frameEditAreaEl:null,frameDoc:null,changedBySocket:!1,changedByDatabase:!0,startCursorPos:null,endCursorPos:null,pressButtonKey:null,currentNode:null,previousNode:null,offset:null,isCaretPositionFound:!1,isDraggableProp:!0}},computed:{locale:function(){return this.$i18n.locale},isSocketConnected:function(){return this.$store.state.socket.isConnected},isCtrlKeyDown:function(){return this.$store.state.classroom.isCtrlKeyDown},zoom:function(){var t;return null===(t=this.$store.getters["classroom/zoomAsset"])||void 0===t?void 0:t.asset},assetText:function(){return this.file.asset.text}},watch:{text:function(){this.changeHandler(this.text)},isCtrlKeyDown:function(t,e){this.frameEl&&(t?this.frameEl.classList.add("no-scroll"):this.frameEl.classList.remove("no-scroll"))},assetText:function(t,e){this.setText(t)}},beforeDestroy:function(){var t=this;this.frameEditAreaEl&&(["pointerdown","pointerup","pointerout","pointerleave"].forEach((function(e){return t.frameEditAreaEl.removeEventListener(e,t.pointerUpHandler,!1)})),this.frameEditAreaEl.removeEventListener("pointerdown",this.pointerDownHandler,!1),this.frameEditAreaEl.removeEventListener("pointermove",this.pointerMoveHandler,!1))},methods:{initHandler:function(){var t=this;return Object(l.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.setText(t.assetText);case 2:t.frameEl=document.getElementById("".concat(t.frameElId,"_ifr")),t.frameDocument=t.frameEl.contentDocument||t.frameEl.contentWindow.document,t.frameEl.setAttribute("data-hj-allow-iframe",!0),t.frameEditAreaEl=t.frameDocument.getElementById("tinymce"),t.frameEditAreaEl&&(["pointerdown","pointerup","pointerout","pointerleave"].forEach((function(e){return t.frameEditAreaEl.addEventListener(e,t.pointerUpHandler,!1)})),t.frameEditAreaEl.addEventListener("pointerdown",t.pointerDownHandler,!1),t.frameEditAreaEl.addEventListener("pointermove",t.pointerMoveHandler,!1));case 7:case"end":return e.stop()}}),e)})))()},setText:function(t){var e=this;return new Promise((function(o){e.text=t,o()}))},mouseOverHandler:function(t){this.$emit("mouse-move",$($({},t),{},{clientX:(this.file.asset.left-this.zoom.x+t.clientX)*this.zoom.zoomIndex,clientY:(this.file.asset.top-this.zoom.y+t.clientY+40)*this.zoom.zoomIndex}))},pointerDownHandler:function(t){this.$emit("pointer-down",t)},pointerMoveHandler:function(t){this.$emit("pointer-move",t)},pointerUpHandler:function(t){this.$emit("pointer-up",t)},clickHandler:function(){this.assetText||this.setStartCursorPos()},focusHandler:function(t){t.onIndex()},dragOverHandler:function(t){t.preventDefault(),this.$store.commit("classroom/isDraggingTrigger",!0)},keyDownHandler:function(t){this.$store.commit("classroom/SET_IS_CTRL_KEY_DOWN",t.ctrlKey),this.pressButtonKey=t.keyCode},keyUpHandler:function(t){this.$store.commit("classroom/SET_IS_CTRL_KEY_DOWN",!1),[37,38,39,40].includes(t.keyCode)&&this.setStartCursorPos()},setStartCursorPos:function(){this.startCursorPos=this.getCaretCharacterOffsetWithin()},changeHandler:function(text){this.changedBySocket||this.changedByDatabase?(this.changedBySocket=!1,this.changedByDatabase=!1):(this.endCursorPos=this.getCaretCharacterOffsetWithin(),this.$socket.emit("text-editor-updated",{id:this.file.id,lessonId:this.file.lessonId,asset:{text:text,startPos:this.startCursorPos,endPos:this.endCursorPos,pressButtonKey:this.pressButtonKey,offsetTop:this.getCurrentNodeOffsetTop()}}),this.startCursorPos=this.endCursorPos,this.updateAsset(text))},updateAsset:Object(O.debounce)((function(text){this.$store.dispatch("classroom/updateAssetWithoutSync",{id:this.file.id,lessonId:this.file.lessonId,asset:{text:text}})}),500),getCaretCharacterOffsetWithin:function(){var t,e,element=null===(t=this.frameEditAreaEl)||void 0===t||null===(e=t.childNodes)||void 0===e?void 0:e[0];if(element){var o,r=element.ownerDocument||element.document,n=r.defaultView||r.parentWindow,l=0;if(void 0!==(null==n?void 0:n.getSelection)){if((o=n.getSelection()).rangeCount>0){var d=o.getRangeAt(0),c=d.cloneRange();c.selectNodeContents(element),c.setEnd(d.endContainer,d.endOffset),l=c.toString().length}}else if((o=r.selection)&&"Control"!==o.type){var h=o.createRange(),m=r.body.createTextRange();m.moveToElementText(element),m.setEndPoint("EndToEnd",h),l=m.text.length}return l}return 0},getCurrentNodeOffsetTop:function(){var t,e,element=null===(t=this.frameEditAreaEl)||void 0===t||null===(e=t.childNodes)||void 0===e?void 0:e[0];if(element){var o,r,n,l,d=element.ownerDocument||element.document,c=d.defaultView||d.parentWindow,h=0;if(void 0!==(null==c?void 0:c.getSelection))h=null!==(o=null===(r=c.getSelection())||void 0===r||null===(n=r.anchorNode)||void 0===n||null===(l=n.parentElement)||void 0===l?void 0:l.offsetTop)&&void 0!==o?o:0;return h}return 0},getCurrentNodeWithPosition:function(t){var e=this;return new Promise((function(o){for(var i=0;i<t.length;i++){var r,n,l;if((null===(r=t[i])||void 0===r||null===(n=r.childNodes)||void 0===n?void 0:n.length)>0)e.getCurrentNodeWithPosition(null===(l=t[i])||void 0===l?void 0:l.childNodes);else if(!e.isCaretPositionFound){var d,c,h;if(e.previousNode=e.currentNode,e.currentNode=t[i],null!==(d=e.previousNode)&&void 0!==d&&d.textContent.length)e.offset-=null===(h=e.previousNode)||void 0===h?void 0:h.textContent.length;if(e.offset<=(null===(c=e.currentNode)||void 0===c?void 0:c.textContent.length)){e.isCaretPositionFound=!0;break}}}o()}))},setCaretPosition:function(t,e){var o=this;return Object(l.a)(regeneratorRuntime.mark((function r(){var n,l;return regeneratorRuntime.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n=o.frameDocument.getSelection(),l=n.getRangeAt(0),o.offset=e,o.currentNode=null,o.previousNode=null,o.isCaretPositionFound=!1,r.next=8,o.getCurrentNodeWithPosition(t);case 8:null!=o.currentNode&&o.currentNode.length>=o.offset&&(l.setStart(o.currentNode,o.offset),l.collapse(!0),n.removeAllRanges(),n.addRange(l));case 9:case"end":return r.stop()}}),r)})))()},generatePdf:function(){this.$store.dispatch("lesson/generatePdf",this.file.lessonId)}},sockets:{"text-editor-updated":function(data){var t=this;return Object(l.a)(regeneratorRuntime.mark((function e(){var o,r,n;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.changedBySocket=!0,data.asset.text===(null===(o=t.frameEditAreaEl)||void 0===o?void 0:o.innerHTML)){e.next=10;break}return e.next=4,t.setText(data.asset.text);case 4:if(data.asset.offsetTop>0&&null!==(r=t.frameEl)&&void 0!==r&&r.contentWindow&&(t.frameEl.contentWindow.document.body.offsetHeight<data.asset.offsetTop||t.frameEl.contentWindow.scrollY>data.asset.offsetTop)&&t.frameEl.contentWindow.scrollTo({top:data.asset.offsetTop-15,behavior:"smooth"}),null==t.startCursorPos){e.next=10;break}return(data.asset.endPos<data.asset.startPos&&t.startCursorPos>=data.asset.startPos||data.asset.endPos>data.asset.startPos&&t.startCursorPos>data.asset.startPos)&&(t.startCursorPos=t.startCursorPos+data.asset.endPos-data.asset.startPos),t.startCursorPos>0&&t.startCursorPos>data.asset.startPos&&data.asset.endPos===data.asset.startPos&&13!==data.asset.pressButtonKey&&(t.startCursorPos=t.startCursorPos-1),e.next=10,t.setCaretPosition(null===(n=t.frameEditAreaEl)||void 0===n?void 0:n.childNodes,t.startCursorPos);case 10:case"end":return e.stop()}}),e)})))()}}},P=(o(1773),o(22)),component=Object(P.a)(E,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("classroom-container",{attrs:{asset:t.file,"lock-aspect-ratio":!1,"is-draggable-prop":t.isDraggableProp,"hide-resize-icon":""},scopedSlots:t._u([{key:"default",fn:function(e){return[o("div",{staticClass:"classroom-editor"},[o("editor",{attrs:{id:t.frameElId,"api-key":t.apiKey,init:{language:t.locale,height:"100%",placeholder:t.$t("welcome_to_new_langu_classroom"),plugins:["lists link hr table codesample autolink"],extended_valid_elements:"a[href|target=_blank]",menubar:"",branding:!1,contextmenu:!1,browser_spellcheck:!0,toolbar_drawer:"wrap",mobile:{toolbar_drawer:"wrap"},toolbar:t.toolbar,setup:function(e){e.ui.registry.addMenuButton("exportButton",{icon:"export",tooltip:"Export",fetch:function(e){e([{type:"menuitem",text:"PDF",tooltip:"PDF",onAction:function(){t.generatePdf()}}])}})},content_style:t.content_style},disabled:!t.isSocketConnected},on:{onInit:t.initHandler,onClick:t.setStartCursorPos,onFocus:function(o){return t.focusHandler(e)},onKeyUp:t.keyUpHandler,onKeyDown:t.keyDownHandler,onMouseMove:t.mouseOverHandler,onDragOver:t.dragOverHandler},model:{value:t.text,callback:function(e){t.text=e},expression:"text"}})],1)]}}])})}),[],!1,null,null,null);e.default=component.exports;installComponents(component,{ClassroomContainer:o(1389).default})},1936:function(t,e,o){"use strict";o.r(e);o(31);var r={name:"DropFileArea",mixins:[o(1621).a],props:{viewportWidth:{type:Number,required:!0}},computed:{isDragging:function(){return this.$store.state.classroom.isDragging}},mounted:function(){document.addEventListener("dragover",this.dragover)},beforeDestroy:function(){document.removeEventListener("dragover",this.dragover)},methods:{dragover:function(t){t.preventDefault(),this.$store.commit("classroom/isDraggingTrigger",!0)},dragleave:function(){this.$store.commit("classroom/isDraggingTrigger",!1)},drop:function(t){this.$store.commit("classroom/isDraggingTrigger",!1),this.uploadFiles(t.dataTransfer.files)}}},n=(o(1765),o(22)),component=Object(n.a)(r,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{on:{drop:function(e){return e.preventDefault(),t.drop.apply(null,arguments)},dragleave:function(e){return e.preventDefault(),t.dragleave.apply(null,arguments)}}},[r("div",{directives:[{name:"show",rawName:"v-show",value:t.isDragging,expression:"isDragging"}],staticClass:"popup-load-files-drop-wrap"},[r("div",{staticClass:"drop-area--wrapper"},[r("img",{staticClass:"drop-area--wrapper__dropbox-img",attrs:{src:o(908),alt:""}})])])])}),[],!1,null,"190879a5",null);e.default=component.exports},1937:function(t,e,o){"use strict";o.r(e);o(31);var r=o(695),n={name:"Viewport",props:{zoomAsset:{type:Object,required:!0},zoomOtherAsset:{type:Object,required:!0},viewportWidth:{type:Number,required:!0},viewportHeight:{type:Number,required:!0}},data:function(){return{isDevice:Object(r.a)()}},computed:{role:function(){return this.$store.getters["classroom/role"]},color:function(){return"teacher"===this.role?"rgba(60, 135, 248, 0.4)":"rgba(127, 184, 2, 0.4)"},isOtherUserJoinedClassroom:function(){return this.$store.getters["classroom/isOtherUserJoinedClassroom"]},otherScreenTop:function(){return(this.zoomOtherAsset.asset.y-this.zoomAsset.asset.y)*this.zoomAsset.asset.zoomIndex},otherScreenLeft:function(){return(this.zoomOtherAsset.asset.x-this.zoomAsset.asset.x)*this.zoomAsset.asset.zoomIndex},otherScreenWidth:function(){return this.zoomOtherAsset.asset.screen.width/this.zoomOtherAsset.asset.zoomIndex*this.zoomAsset.asset.zoomIndex},otherScreenHeight:function(){return this.zoomOtherAsset.asset.screen.height/this.zoomOtherAsset.asset.zoomIndex*this.zoomAsset.asset.zoomIndex},isOtherScreenAllowed:function(){var t,e;return!this.isDevice&&!(null===(t=this.zoomOtherAsset)||void 0===t||null===(e=t.asset)||void 0===e||!e.screen)&&this.isOtherUserJoinedClassroom&&this.zoomAsset.asset.screen.width>this.zoomOtherAsset.asset.screen.width},styles:function(){return{top:"".concat(this.otherScreenTop,"px"),left:"".concat(this.otherScreenLeft,"px"),width:"".concat(this.otherScreenWidth,"px"),height:"".concat(this.otherScreenHeight,"px"),borderColor:this.color}},username:function(){return this.zoomOtherAsset.asset.username},isTopOffset:function(){return this.otherScreenTop<0},isLeftOffset:function(){return this.otherScreenLeft<0},isBottomOffset:function(){return this.viewportHeight+this.zoomAsset.asset.y<this.otherScreenHeight+this.zoomOtherAsset.asset.y},isRightOffset:function(){return this.viewportWidth+this.zoomAsset.asset.x<this.otherScreenWidth+this.zoomOtherAsset.asset.x}}},l=(o(1767),o(22)),component=Object(l.a)(n,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return t.isOtherScreenAllowed?o("div",{staticClass:"viewport-component",style:t.styles},[o("div",{class:["user-name",{"user-name--br":t.isTopOffset&&!t.isRightOffset,"user-name--tl":t.isRightOffset&&!t.isTopOffset,"user-name--bl":t.isTopOffset&&t.isRightOffset}]},[t._v("\n    "+t._s(t.$t("classroom_user_screen",{username:t.username}))+"\n  ")])]):t._e()}),[],!1,null,"4a5272da",null);e.default=component.exports},1938:function(t,e,o){"use strict";o.r(e);o(23);var r={name:"OtherCursor",data:function(){return{left:0,top:0}},computed:{otherCursor:function(){return this.$store.getters["classroom/otherCursor"]},otherUserRole:function(){return this.$store.getters["classroom/otherUserRole"]},isVisible:function(){return!!this.otherCursor.username},zoom:function(){var t;return null===(t=this.$store.getters["classroom/zoomAsset"])||void 0===t?void 0:t.asset},styles:function(){return{top:"".concat(this.top,"px"),left:"".concat(this.left,"px"),backgroundImage:"url("+o(1769)("./".concat(this.otherUserRole,"-").concat(this.otherCursor.cursor,".svg"))+")"}}},watch:{otherCursor:{handler:function(data){this.left=(data.coords.x-this.zoom.x)*this.zoom.zoomIndex,this.top=(data.coords.y-this.zoom.y)*this.zoom.zoomIndex}}}},n=(o(1770),o(22)),component=Object(n.a)(r,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{directives:[{name:"show",rawName:"v-show",value:t.isVisible,expression:"isVisible"}],ref:"other_cursor",class:["other_cursor",t.otherCursor.cursor],style:t.styles,attrs:{id:"other_cursor"}},[o("div",{staticClass:"cursor-name",style:{backgroundColor:t.otherCursor.bgColorTooltip}},[t._v("\n    "+t._s(t.otherCursor.username)+"\n  ")])])}),[],!1,null,"b743e11e",null);e.default=component.exports},1939:function(t,e,o){"use strict";o.r(e);var r=o(28),n=(o(9),o(24),o(38),o(6),o(20),o(80),o(174),o(1690)),l=o.n(n),d=o(266),c=o(1389),h=o(1459),m=o(1555),f={name:"VideoItem",components:{ClassroomContainer:c.default,ClassroomContainerHeader:h.default,Konva:m.default},props:{file:{type:Object,required:!0}},data:function(){return{timeMarkerContainer:null,currentTime:0,duration:null,loaded:!1,changedBySocket:!1,player:{config:{title:""}}}},computed:{scale:function(){return this.width?this.width/d.j:1},width:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.width)&&void 0!==t?t:0},height:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.height)&&void 0!==t?t:0},shapes:function(){return Object(r.a)(this.file.asset.shapes)},zoomIndex:function(){var t,e,o;return null!==(t=null===(e=this.$store.getters["classroom/zoomAsset"])||void 0===e||null===(o=e.asset)||void 0===o?void 0:o.zoomIndex)&&void 0!==t?t:1},isSocketConnected:function(){return this.$store.state.socket.isConnected},isUserInteracted:function(){return this.$store.state.classroom.isUserInteracted}},watch:{shapes:function(t,e){if(t.length){var o=t.filter((function(t){return!e.includes(t)}));this.addMarker(o[0])}}},mounted:function(){var t=this,e={controls:["play","progress","current-time","mute","volume","captions","pip"],clickToPlay:!1,hideControls:!1,resetOnEnd:!0,volume:this.file.asset.volume||1,muted:this.file.asset.muted||!1,storage:{enabled:!1},speed:{selected:this.file.asset.speed||1,options:[.5,.75,1,1.25,1.5,1.75,2]}};this.player=new l.a("#player-".concat(this.file.id),e),this.player.on("ready",(function(){t.player.on("play",t.playVideo),t.player.on("pause",t.pauseVideo),t.player.on("ratechange",t.changeSpeed),t.player.on("timeupdate",t.timeupdate),t.currentTime=t.file.asset.currentTime||0,t.player.currentTime=t.currentTime,t.player.pause(),t.duration=t.player.duration,t.resize(),t.createTimeMarketContainer(),t.loaded=!0})),new ResizeObserver(this.resize).observe(this.$refs.childComponent)},beforeDestroy:function(){this.player.destroy()},methods:{resize:function(){if(!this.width){var t={width:this.$refs.childComponent.getBoundingClientRect().width/this.zoomIndex,height:this.$refs.childComponent.getBoundingClientRect().height/this.zoomIndex,originalWidth:d.j};this.$store.commit("classroom/moveAsset",{id:this.file.id,asset:t}),this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.file.lessonId,asset:t})}},onPlayerChange:function(t,e){if(this.changedBySocket)this.changedBySocket=!1;else{var data={id:this.file.id,lessonId:this.file.lessonId,asset:e};this.isSocketConnected&&this.$socket.emit("video-updated",data),this.$store.dispatch("classroom/updateAssetWithoutSync",data)}},playVideo:function(t){this.onPlayerChange(t,{currentTime:t.detail.plyr.media.currentTime,play:!0})},pauseVideo:function(t){this.onPlayerChange(t,{currentTime:t.detail.plyr.media.currentTime,play:!1})},timeupdate:function(){this.currentTime=this.player.currentTime},changeSpeed:function(t){this.onPlayerChange(t,{speed:t.detail.plyr.config.speed.selected})},createTimeMarketContainer:function(){var t=this;if(this.timeMarkerContainer=document.getElementById("time-markers-".concat(this.file.id)),!this.timeMarkerContainer){var e=document.getElementById("video-".concat(this.file.id)).getElementsByClassName("plyr__progress__buffer")[0];this.timeMarkerContainer=document.createElement("div"),this.timeMarkerContainer.setAttribute("id","time-markers-".concat(this.file.id)),this.timeMarkerContainer.setAttribute("class","time-markers"),e.before(this.timeMarkerContainer),this.shapes.length&&this.shapes.forEach((function(e){return t.addMarker(e)}))}},addMarker:function(t){var e=this;if(t){var o=(100*t.time/this.duration).toFixed(2).toString(),span=document.createElement("span");span.setAttribute("class","time-markers-item"),span.setAttribute("style","left: ".concat(o,"%;")),span.addEventListener("click",(function(){e.currentTime=t.time,e.player.currentTime=t.time,e.player.play()}),!1),this.timeMarkerContainer.appendChild(span)}}},sockets:{"video-updated":function(data){data.id===this.file.id&&(this.changedBySocket=!0,this.$store.commit("classroom/updateAsset",data),data.asset.speed&&(this.player.speed=data.asset.speed),data.asset.currentTime&&(this.player.currentTime=data.asset.currentTime),"play"in data.asset&&(data.asset.play?(this.isUserInteracted||(this.player.muted=!0),this.player.play()):this.player.pause()))}}},v=(o(1775),o(22)),component=Object(v.a)(f,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("classroom-container",{attrs:{asset:t.file,"child-header-height":80}},[o("classroom-container-header",{attrs:{file:t.file,title:t.player.config.title}},[o("div",{ref:"childComponent",attrs:{id:"video-"+t.file.id}},[o("div",{attrs:{id:"player-"+t.file.id,"data-plyr-embed-id":t.file.asset.videoId,"data-plyr-provider":"youtube"}})]),t._v(" "),o("div",{staticClass:"transparent"},[t.file&&t.width&&t.height?o("konva",{style:{marginTop:"-"+t.height+"px"},attrs:{file:t.file,width:t.width,height:t.height,scale:t.scale,"current-time":t.currentTime}}):t._e()],1)])],1)}),[],!1,null,null,null);e.default=component.exports;installComponents(component,{ClassroomContainerHeader:o(1459).default,ClassroomContainer:o(1389).default})},1940:function(t,e,o){"use strict";o.r(e);o(23);var r=o(1690),n=o.n(r),l=o(859),d=o(266),c=o(1389),h=o(1459),m={name:"AudioItem",components:{ClassroomContainer:c.default,ClassroomContainerHeader:h.default},props:{file:{type:Object,required:!0}},data:function(){return{currentTime:0,changedBySocket:!1,player:{config:{title:""}}}},computed:{width:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.width)&&void 0!==t?t:0},height:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.height)&&void 0!==t?t:0},zoomIndex:function(){var t,e,o;return null!==(t=null===(e=this.$store.getters["classroom/zoomAsset"])||void 0===e||null===(o=e.asset)||void 0===o?void 0:o.zoomIndex)&&void 0!==t?t:1},isSocketConnected:function(){return this.$store.state.socket.isConnected},audioUrl:function(){return"".concat("'http://localhost:3000'").concat(this.file.asset.path)},mimeType:function(){var t="audio/mpeg";return"wav"===Object(l.getFileExtension)(this.file.asset.path)&&(t="audio/wav"),t},isUserInteracted:function(){return this.$store.state.classroom.isUserInteracted}},mounted:function(){var t=this,e={controls:["play","progress","current-time","mute","volume","settings"],hideControls:!1,resetOnEnd:!0,volume:this.file.asset.volume||1,muted:this.file.asset.muted||!1,storage:{enabled:!1},speed:{selected:this.file.asset.speed||1,options:[.5,.75,1,1.25,1.5,1.75,2]},i18n:{speed:this.$t("speed"),normal:this.$t("normal")}};this.player=new n.a("#player-".concat(this.file.id),e),this.player.source={type:"audio",title:this.file.asset.displayName},this.player.on("ready",(function(){t.player.on("play",t.playAudio),t.player.on("pause",t.pauseAudio),t.player.on("ratechange",t.changeSpeed),t.player.on("timeupdate",t.timeupdate),t.player.pause(),t.resize()})),this.player.on("loadeddata",(function(){t.currentTime=t.file.asset.currentTime||0,t.player.currentTime=t.currentTime})),new ResizeObserver(this.resize).observe(this.$refs.childComponent)},beforeDestroy:function(){this.player.destroy()},methods:{resize:function(){if(!this.width){var t={width:this.$refs.childComponent.getBoundingClientRect().width/this.zoomIndex,height:this.$refs.childComponent.getBoundingClientRect().height/this.zoomIndex,originalWidth:d.j};this.$store.commit("classroom/moveAsset",{id:this.file.id,asset:t}),this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.file.lessonId,asset:t})}},playAudio:function(t){this.onPlayerChange({currentTime:t.detail.plyr.media.currentTime,play:!0})},pauseAudio:function(t){this.onPlayerChange({currentTime:t.detail.plyr.media.currentTime,play:!1})},timeupdate:function(t){this.currentTime=this.player.currentTime},changeSpeed:function(t){this.onPlayerChange({speed:t.detail.plyr.config.speed.selected})},onPlayerChange:function(t){if(this.changedBySocket)this.changedBySocket=!1;else{var data={id:this.file.id,lessonId:this.file.lessonId,asset:t};this.isSocketConnected&&this.$socket.emit("audio-updated",data),this.$store.dispatch("classroom/updateAssetWithoutSync",data)}}},sockets:{"audio-updated":function(data){data.id===this.file.id&&(this.changedBySocket=!0,this.$store.commit("classroom/updateAsset",data),data.asset.speed&&(this.player.speed=data.asset.speed),data.asset.currentTime&&(this.player.currentTime=data.asset.currentTime),"play"in data.asset&&(data.asset.play?(this.isUserInteracted||(this.player.muted=!0),this.player.play()):this.player.pause()))}}},f=(o(1777),o(22)),component=Object(f.a)(m,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("classroom-container",{attrs:{asset:t.file,"child-header-height":80,handles:["mr","ml"]}},[o("classroom-container-header",{attrs:{file:t.file,title:t.file.asset.displayName}},[o("div",{ref:"childComponent",attrs:{id:"audio-"+t.file.id}},[o("audio",{attrs:{id:"player-"+t.file.id,crossorigin:"",playsinline:""}},[o("source",{attrs:{src:t.audioUrl,type:t.mimeType}})])])])],1)}),[],!1,null,null,null);e.default=component.exports;installComponents(component,{ClassroomContainerHeader:o(1459).default,ClassroomContainer:o(1389).default})},1941:function(t,e,o){"use strict";o.r(e);var r=o(10),n=(o(62),o(859)),l=o(1389),d=o(1459),c=o(266),h=o(1555),m=o(1428),f={name:"PdfItem",components:{ClassroomContainer:l.default,ClassroomContainerHeader:d.default,Konva:h.default},mixins:[m.a],props:{file:{type:Object,required:!0}},data:function(){return{pdfjsLib:{},renderInProgress:!1,viewport:null,page:1,pageTotal:1,pdfDoc:null,pdfPage:null}},computed:{scale:function(){return this.width?this.width/c.j:1},canvas:function(){return document.querySelector("#pdf-render--".concat(this.file.id))},zoomIndex:function(){var t,e,o;return null!==(t=null===(e=this.$store.getters["classroom/zoomAsset"])||void 0===e||null===(o=e.asset)||void 0===o?void 0:o.zoomIndex)&&void 0!==t?t:1},width:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.width)&&void 0!==t?t:0},height:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.height)&&void 0!==t?t:0},isPrevPageEnabled:function(){return this.pageTotal>1&&this.page>1},isNextPageEnabled:function(){return this.pageTotal>1&&this.page<this.pageTotal}},watch:{"file.asset.page":function(t){this.page=t,this.renderPage()},zoomIndex:function(){this.renderPage()}},mounted:function(){var t=this;return Object(r.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o(1691);case 2:return t.pdfjsLib=e.sent,e.next=5,o(1692);case 5:t.pdfjsLib.GlobalWorkerOptions.workerSrc=e.sent,t.file.asset.page&&(t.page=t.file.asset.page),t.pdfjsLib.getDocument(t.file.asset.path).promise.then((function(e){t.pdfDoc=e,t.pageTotal=t.pdfDoc.numPages,t.renderPage();var o={originalWidth:c.j};t.$store.commit("classroom/moveAsset",{id:t.file.id,asset:o}),t.$store.dispatch("classroom/moveAsset",{id:t.file.id,lessonId:t.file.lessonId,asset:o})})).catch((function(t){alert(t.message)})),new ResizeObserver(Object(n.debounce)(t.resize,200)).observe(t.$refs.childComponent);case 9:case"end":return e.stop()}}),e)})))()},methods:{showPage:function(){this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.file.lessonId,asset:{page:this.page}})},showPrevPage:function(){this.page<=1||(this.page-=1,this.renderPage(),this.showPage())},showNextPage:function(){this.page>=this.pageTotal||(this.page+=1,this.renderPage(),this.showPage())},resize:function(){var t=this;if(this.pdfPage){if(this.renderInProgress)return;this.renderInProgress=!0;var e=this.canvas.getContext("2d"),o=(window.devicePixelRatio||1)*this.zoomIndex;if(this.$refs.childComponent){var r=this.$refs.childComponent.getBoundingClientRect().width,n=this.pdfPage.getViewport({scale:1}).width,l=r/this.zoomIndex/n;this.viewport=this.pdfPage.getViewport({scale:o*l}),this.canvas.height=this.viewport.height,this.canvas.width=this.viewport.width,this.canvas.style.width="100%",this.pdfPage.render({canvasContext:e,viewport:this.viewport}).promise.then((function(){t.$store.commit("classroom/moveAsset",{id:t.file.id,asset:{width:t.canvas.width/o,height:t.canvas.height/o}}),t.renderInProgress=!1}))}}},renderPage:function(){var t=this;this.pdfDoc.getPage(this.page).then((function(e){t.pdfPage=e,t.resize()}))},mouseenterHandler:function(){var t,e,o,r;this.$store.commit("classroom/setCursorNameBeforeChange",(null===(t=this.$store.state)||void 0===t||null===(e=t.userParams)||void 0===e?void 0:e.cursor)||"cursor-pointer"),this.$store.commit("classroom/setToolNameBeforeChange",(null===(o=this.$store.state)||void 0===o||null===(r=o.userParams)||void 0===r?void 0:r.tool)||"pointer"),this.setTool("pointer","cursor-pointer")},mouseleaveHandler:function(){this.setTool(this.$store.state.classroom.toolNameBeforeChange,this.$store.state.classroom.cursorNameBeforeChange)}}},v=(o(1783),o(22)),x=o(42),y=o.n(x),w=o(1327),k=o(261),component=Object(v.a)(f,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("classroom-container",{attrs:{asset:t.file,"child-header-height":92}},[r("div",{ref:"childComponent",staticClass:"image-wrap-classroom"},[r("classroom-container-header",{attrs:{file:t.file,title:t.file.asset.displayName}},[r("canvas",{attrs:{id:"pdf-render--"+t.file.id}}),t._v(" "),r("div",{staticClass:"transparent"},[t.file&&t.width&&t.height?r("konva",{style:{marginTop:"-"+t.height+"px"},attrs:{file:t.file,width:t.width,height:t.height,scale:t.scale,"current-page":t.page}}):t._e()],1),t._v(" "),r("div",{staticClass:"pdf-controls",on:{mouseenter:function(e){return e.preventDefault(),t.mouseenterHandler.apply(null,arguments)},mouseleave:function(e){return e.preventDefault(),t.mouseleaveHandler.apply(null,arguments)}}},[r("v-btn",{attrs:{icon:"",width:"36",height:"36",disabled:!t.isPrevPageEnabled},on:{click:t.showPrevPage}},[r("v-img",{attrs:{src:o(885),height:"14",contain:""}})],1),t._v(" "),r("span",{staticClass:"page-info"},[t._v("\n          "+t._s(t.$t("page"))+" "),r("span",[t._v(t._s(t.page))]),t._v(" of "+t._s(t.pageTotal)),r("span")]),t._v(" "),r("v-btn",{staticClass:"btn-next",attrs:{icon:"",small:"",width:"36",height:"36",disabled:!t.isNextPageEnabled},on:{click:t.showNextPage}},[r("v-img",{attrs:{src:o(885),height:"14",contain:""}})],1)],1)])],1)])}),[],!1,null,null,null);e.default=component.exports;y()(component,{ClassroomContainerHeader:o(1459).default,ClassroomContainer:o(1389).default}),y()(component,{VBtn:w.a,VImg:k.a})},1942:function(t,e,o){"use strict";o.r(e);o(23);var r=o(266),n=o(1389),l=o(1459),d=o(1555),c={name:"ImageItem",components:{ClassroomContainer:n.default,ClassroomContainerHeader:l.default,Konva:d.default},props:{file:{type:Object,required:!0}},computed:{scale:function(){return this.width?this.width/r.j:1},width:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.width)&&void 0!==t?t:0},height:function(){var t,e;return null!==(t=null===(e=this.file.asset)||void 0===e?void 0:e.height)&&void 0!==t?t:0},zoomIndex:function(){var t,e,o;return null!==(t=null===(e=this.$store.getters["classroom/zoomAsset"])||void 0===e||null===(o=e.asset)||void 0===o?void 0:o.zoomIndex)&&void 0!==t?t:1},imageUrl:function(){return"".concat("'http://localhost:3000'").concat(this.file.asset.path)}},methods:{resize:function(){if(!this.width){var t={width:this.$refs.childComponent.getBoundingClientRect().width/this.zoomIndex,height:this.$refs.childComponent.getBoundingClientRect().height/this.zoomIndex,originalWidth:r.j};this.$store.commit("classroom/moveAsset",{id:this.file.id,asset:t}),this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.file.lessonId,asset:t})}}}},h=(o(1785),o(22)),component=Object(h.a)(c,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("classroom-container",{attrs:{asset:t.file,"child-header-height":40}},[o("classroom-container-header",{attrs:{file:t.file,title:t.file.asset.displayName}},[o("div",{ref:"childComponent",staticClass:"image-wrap-classroom"},[o("img",{attrs:{src:t.imageUrl,alt:""},on:{load:t.resize}})]),t._v(" "),o("div",{staticClass:"transparent"},[t.file&&t.width&&t.height?o("konva",{style:{marginTop:"-"+t.height+"px"},attrs:{file:t.file,width:t.width,height:t.height,scale:t.scale}}):t._e()],1)])],1)}),[],!1,null,"6c1f4906",null);e.default=component.exports;installComponents(component,{ClassroomContainerHeader:o(1459).default,ClassroomContainer:o(1389).default})},1943:function(t,e,o){"use strict";o.r(e);var r=o(2),n=o(10),l=(o(62),o(63),o(20),o(37),o(44),o(151),o(24),o(38),o(23),o(80),o(7),o(8),o(9),o(14),o(6),o(15),o(695)),d=o(1625),c=o(859);function h(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}function m(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?h(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):h(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var f={name:"Whereby",components:{ClassroomContainer:o(1389).default},props:{file:{type:Object,required:!0},screenShareAsset:{type:Object,required:!0},zoomOtherAsset:{type:Object,required:!0}},data:function(){return{localStreamContainer:null,screenShareStreamContainer:null,isLocalScreenShareEnabled:!1,isRemoteScreenShareEnabled:!1,screenSharingNotSupported:!Object(l.c)(),isJoined:!1,currentRole:"participant",wherebyRoom:null,isCreatingRoom:!1,settings:{isFullscreenEnabled:!1,isScreenShareEnabled:!1},isHandRaised:!1,isChatEnabled:!1,isParticipantsEnabled:!1}},computed:{lessonId:function(){return this.file.lessonId||this.$store.state.classroom.lessonId},teacherId:function(){return this.$store.state.classroom.teacherId},studentId:function(){return this.$store.state.classroom.studentId},role:function(){return this.$store.getters["classroom/role"]},isVideoEnabled:function(){var t,e,o,r;return null===(t=null===(e=this.file.asset)||void 0===e||null===(o=e.settings)||void 0===o||null===(r=o[this.role])||void 0===r?void 0:r.isVideoEnabled)||void 0===t||t},isMuted:function(){var t,e,o,r;return null!==(t=null===(e=this.file.asset)||void 0===e||null===(o=e.settings)||void 0===o||null===(r=o[this.role])||void 0===r?void 0:r.isMuted)&&void 0!==t&&t},maxIndex:function(){return this.$store.state.classroom.maxIndex}},mounted:function(){var t=this;this.$nextTick((function(){t.localStreamContainer=document.getElementById("whereby-video-container"),t.screenShareStreamContainer=document.getElementById("whereby-screenshare-placeholder"),t.localStreamContainer?t.initializeWhereby():(console.error("whereby-video-container not found, retrying in 500ms"),setTimeout((function(){t.localStreamContainer=document.getElementById("whereby-video-container"),t.localStreamContainer?t.initializeWhereby():console.error("whereby-video-container still not found after retry")}),500))})),window.addEventListener("beforeunload",this.closeStream),window.addEventListener("pagehide",this.closeStream),document.addEventListener("fullscreenchange",this.fullscreenChangeHandler)},beforeDestroy:function(){document.removeEventListener("fullscreenchange",this.fullscreenChangeHandler),this.closeStream()},methods:{initializeWhereby:function(){var t=this;return Object(n.a)(regeneratorRuntime.mark((function e(){var o,iframe,r,n,l;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,t.localStreamContainer){e.next=4;break}return console.error("localStreamContainer is null"),e.abrupt("return");case 4:if(o="teacher"===t.role,t.currentRole=o?"host":"participant",t.isCreatingRoom=!0,t.localStreamContainer.innerHTML='\n          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">\n            <div style="margin-bottom: 20px;">\n              <h3 style="color: #333; margin-bottom: 10px;">Creating Whereby Room...</h3>\n              <p style="color: #666; margin-bottom: 20px;">Please wait while we set up your video call</p>\n            </div>\n            <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #5E72E4; border-radius: 50%; animation: spin 1s linear infinite;"></div>\n            <style>\n              @keyframes spin {\n                0% { transform: rotate(0deg); }\n                100% { transform: rotate(360deg); }\n              }\n            </style>\n          </div>\n        ',t.wherebyRoom){e.next=12;break}return e.next=11,Object(d.a)({lessonId:t.lessonId,teacherId:t.teacherId,studentId:t.studentId,isRecurring:!1});case 11:t.wherebyRoom=e.sent;case 12:t.isCreatingRoom=!1,iframe=document.createElement("iframe"),r="host"===t.currentRole?t.wherebyRoom.hostRoomUrl:t.wherebyRoom.roomUrl,n=new URLSearchParams({embed:"",displayName:t.$store.getters["classroom/userName"]||"User",audio:t.isMuted?"off":"on",video:t.isVideoEnabled?"on":"off",chat:"off",people:"off",screenshare:"on",reactions:"on",handRaise:"on",leaveButton:"off",background:"on",recording:"off",breakoutRooms:"on",whiteboard:"on",minimal:"false",skipMediaPermissionPrompt:"true",autoJoin:"true"}),l=r.includes("?")?"&":"?",iframe.src="".concat(r).concat(l).concat(n.toString()),iframe.style.width="100%",iframe.style.height="100%",iframe.style.border="none",iframe.style.borderRadius="8px",iframe.style.pointerEvents="auto",iframe.allow="camera; microphone; fullscreen; display-capture; autoplay",iframe.allowFullscreen=!0,t.localStreamContainer.innerHTML="",t.localStreamContainer.appendChild(iframe),window.addEventListener("message",t.handleWherebyMessage),setTimeout((function(){t.isJoined=!0}),1e3),e.next=36;break;case 31:e.prev=31,e.t0=e.catch(0),console.error("Failed to initialize Whereby:",e.t0),t.isCreatingRoom=!1,t.localStreamContainer&&(t.localStreamContainer.innerHTML='\n            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">\n              <div style="margin-bottom: 20px;">\n                <h3 style="color: #d32f2f; margin-bottom: 10px;">Failed to Create Room</h3>\n                <p style="color: #666; margin-bottom: 20px;">Unable to create Whereby room. Please try switching to another video provider.</p>\n              </div>\n              <button\n                onclick="this.parentElement.parentElement.parentElement.querySelector(\'.toolbar-button-item\').click()"\n                style="\n                  background: #5E72E4;\n                  color: white;\n                  border: none;\n                  padding: 12px 20px;\n                  border-radius: 6px;\n                  font-size: 14px;\n                  cursor: pointer;\n                  font-weight: 600;\n                "\n              >\n                Switch to Provider A\n              </button>\n            </div>\n          ');case 36:case"end":return e.stop()}}),e,null,[[0,31]])})))()},handleWherebyMessage:function(t){if(t.origin.includes("whereby.com"))try{var data=JSON.parse(t.data);switch(data.type){case"participant_join":case"participant_leave":break;case"screenshare_start":this.isRemoteScreenShareEnabled=!0;break;case"screenshare_stop":this.isRemoteScreenShareEnabled=!1;break;case"error":console.error("Whereby error:",data.error),this.handleMediaError(data.error)}}catch(t){}},handleMediaError:function(t){console.error("Whereby media error:",t),alert("Could not connect to video call. Please check your camera and microphone permissions.")},toggleVideo:function(){this.updateData(this.file.id,{settings:m(m({},this.file.asset.settings),{},Object(r.a)({},this.role,{isVideoEnabled:!this.isVideoEnabled,isMuted:this.isMuted}))})},toggleAudio:function(){this.updateData(this.file.id,{settings:m(m({},this.file.asset.settings),{},Object(r.a)({},this.role,{isVideoEnabled:this.isVideoEnabled,isMuted:!this.isMuted}))})},toggleFullScreen:function(){this.settings.isFullscreenEnabled=!this.settings.isFullscreenEnabled,Object(c.switchFullScreen)(this.settings.isFullscreenEnabled)},toggleScreenShare:function(){this.settings.isScreenShareEnabled=!this.settings.isScreenShareEnabled,this.settings.isScreenShareEnabled?(this.isLocalScreenShareEnabled=!1,this.screenShareStreamContainer.innerHTML=""):(this.updateData(this.screenShareAsset.id,{index:this.maxIndex+1}),this.isLocalScreenShareEnabled=!0)},toggleHandRaise:function(){this.isHandRaised=!this.isHandRaised},toggleChat:function(){this.isChatEnabled=!this.isChatEnabled},toggleParticipants:function(){this.isParticipantsEnabled=!this.isParticipantsEnabled},fullscreenChangeHandler:function(){document.fullscreenElement||(this.settings.isFullscreenEnabled=!1)},updateData:function(t,e){this.$store.commit("classroom/moveAsset",{id:t,asset:e}),this.$store.dispatch("classroom/moveAsset",{id:t,lessonId:this.file.lessonId,asset:e})},closeStream:function(){this.localStreamContainer&&(this.localStreamContainer.innerHTML=""),window.removeEventListener("message",this.handleWherebyMessage)}}},v=(o(1787),o(22)),component=Object(v.a)(f,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"whereby-stream"},[o("classroom-container",{attrs:{asset:t.file,"hover-enabled":!0,"lock-aspect-ratio":!1}},[o("div",{class:["whereby-component cursor-before-grab",{"video-window--is-fullscreen":t.settings.isFullscreenEnabled}],attrs:{id:"video-window"}},[o("div",{staticClass:"whereby-header cursor-before-grab"},[o("div",{staticClass:"whereby-header-title"})]),t._v(" "),o("div",{staticClass:"whereby-video-container",attrs:{id:"whereby-video-container"}})])]),t._v(" "),o("classroom-container",{directives:[{name:"show",rawName:"v-show",value:t.isLocalScreenShareEnabled||t.isRemoteScreenShareEnabled,expression:"isLocalScreenShareEnabled || isRemoteScreenShareEnabled"}],attrs:{asset:t.screenShareAsset,"hover-enabled":!0}},[o("div",{staticClass:"whereby-component screenshare-component cursor-before-grab"},[o("div",{staticClass:"user-name"},[t.isLocalScreenShareEnabled?[t._v("\n          "+t._s(t.$t("my_screen"))+"\n        ")]:t._e(),t._v(" "),t.isRemoteScreenShareEnabled?[t._v("\n          "+t._s(t.$t("classroom_user_screen",{username:t.zoomOtherAsset.asset.username}))+"\n        ")]:t._e()],2),t._v(" "),o("div",{staticClass:"screenshare-stream",attrs:{id:"whereby-screenshare-placeholder"}})])])],1)}),[],!1,null,"08cfee22",null);e.default=component.exports;installComponents(component,{ClassroomContainer:o(1389).default})},1944:function(t,e,o){"use strict";o.r(e);var r=o(10),n=(o(62),o(24),o(38),o(35),o(60),o(118),{name:"VideoInput",components:{TextInput:o(370).default},data:function(){var t=this;return{inputUrl:null,isValid:!0,videoId:null,rules:{required:function(e){return!!e||t.$t("field_required")}}}},computed:{role:function(){return this.$store.getters["classroom/role"]},isVideoInputOpened:function(){return this.$store.state.classroom.isVideoInputOpened}},watch:{inputUrl:function(t,e){var o;if(this.isValid=!0,null!=t&&t.includes("v=")){var r=(o=t.split("v=")[1]).indexOf("&");-1!==r&&(o=o.substring(0,r))}!t.length||o&&o.match(/[^"&=\s?]{11}/)||(this.isValid=!1),this.videoId=o}},mounted:function(){this.$refs.input.focus()},methods:{submit:function(){var t=this;return Object(r.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.isValid&&t.videoId){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,t.$store.dispatch("classroom/createAsset",{type:"video",videoId:t.videoId,index:t.$store.state.classroom.maxIndex+1,shapes:[],owner:t.role});case 4:t.$store.commit("classroom/closeVideoInput");case 5:case"end":return e.stop()}}),e)})))()}}}),l=(o(1789),o(22)),d=o(42),c=o.n(d),h=o(1327),m=o(1363),component=Object(l.a)(n,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-form",{on:{submit:function(e){return e.preventDefault(),t.submit.apply(null,arguments)}}},[o("div",{staticClass:"input-wrap"},[o("text-input",{ref:"input",attrs:{value:t.inputUrl,"type-class":"border-gradient",height:"44",rules:[t.rules.required],placeholder:t.$t("enter_youtube_url")},on:{input:function(e){t.inputUrl=e}}}),t._v(" "),t.isValid?t._e():o("div",{staticClass:"input-wrap-error v-text-field__details"},[o("div",{staticClass:"v-messages theme--light error--text",attrs:{role:"alert"}},[o("div",{staticClass:"v-messages__wrapper"},[o("div",{staticClass:"v-messages__message"},[t._v("\n            "+t._s(t.$t("invalid_url_or_video_id"))+"\n          ")])])])])],1),t._v(" "),o("div",{staticClass:"video-buttons-wrap"},[o("v-btn",{attrs:{color:"primary",small:"",type:"submit"}},[t._v("\n      "+t._s(t.$t("load_video"))+"\n    ")])],1)])}),[],!1,null,null,null);e.default=component.exports;c()(component,{VBtn:h.a,VForm:m.a})},1945:function(t,e,o){"use strict";o.r(e);var r=o(28),n=o(10),l=(o(62),o(31),o(39),o(64),o(37),o(20),o(44),o(71),o(9),o(6),o(40),o(174),o(24),o(38),o(23),o(55),o(8),o(66),o(82),o(859)),d=o(266);function c(t,e){var o="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=function(t,e){if(!t)return;if("string"==typeof t)return h(t,e);var o=Object.prototype.toString.call(t).slice(8,-1);"Object"===o&&t.constructor&&(o=t.constructor.name);if("Map"===o||"Set"===o)return Array.from(t);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return h(t,e)}(t))||e&&t&&"number"==typeof t.length){o&&(t=o);var i=0,r=function(){};return{s:r,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,l=!0,d=!1;return{s:function(){o=o.call(t)},n:function(){var t=o.next();return l=t.done,t},e:function(t){d=!0,n=t},f:function(){try{l||null==o.return||o.return()}finally{if(d)throw n}}}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,o=new Array(e);i<e;i++)o[i]=t[i];return o}var m={name:"Library",props:{viewportWidth:{type:Number,required:!0}},data:function(){return{pdfjsLib:{},rootUrl:"'http://localhost:3000'",files:[],totalPages:1,sortListDisplay:!1,queryStr:"",requestBody:{query:"",sort_direction:"DESC",page:1,sort_type:3},sortOptions:[{label:this.$t("last_added"),value:3},{label:this.$t("last_opened"),value:"last_opened"},{label:this.$t("file_name_a_z"),value:2},{label:this.$t("file_name_z_a"),value:1}],tickedFiles:[],arrayPages:[],uploadPercentage:0,isDragging:!1,uploadingFiles:[]}},computed:{zIndex:function(){return this.$store.state.classroom.maxIndex+1},getZoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},lessonId:function(){return this.$store.state.classroom.lessonId},role:function(){return this.$store.getters["classroom/role"]},acceptedFiles:function(){return this.$store.state.classroom.acceptedFiles},acceptedFilesStr:function(){return this.$store.getters["classroom/acceptedFilesStr"]}},watch:{queryStr:Object(l.debounce)((function(t){this.requestBody.query=t,this.getListOfFiles()}),1e3)},beforeMount:function(){var t=this;return Object(n.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o(1691);case 2:return t.pdfjsLib=e.sent,e.next=5,o(1692);case 5:t.pdfjsLib.GlobalWorkerOptions.workerSrc=e.sent,t.getListOfFiles();case 7:case"end":return e.stop()}}),e)})))()},methods:{getPager:function(){var t,e,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,n=this.totalPages;if(o<1?o=1:o>n&&(o=n),n<=r)t=1,e=n;else{var l=Math.floor(r/2),d=Math.ceil(r/2)-1;o<=l?(t=1,e=r):o+d>=n?(t=n-r+1,e=n):(t=o-l,e=o+d)}return Array.from(Array(e+1-t).keys()).map((function(i){return t+i}))},uncheckFiles:function(){this.tickedFiles=[]},toggleFileMark:function(t){this.tickedFiles.find((function(e){return e.id===t.id}))?this.tickedFiles=this.tickedFiles.filter((function(e){return e.id!==t.id})):this.tickedFiles.push(t)},downloadFiles:function(){this.tickedFiles.forEach((function(t){var link=document.createElement("a");link.href=t.path,link.download=t.displayName,link.click()}))},deleteFiles:function(){var t=this,data=this.tickedFiles.map((function(t){return t.id}));this.$store.dispatch("classroom/deleteFiles",data).then((function(e){t.getListOfFiles(),t.tickedFiles=[]}))},toggleSortOptionsList:function(){return this.sortListDisplay=!this.sortListDisplay},changeSortType:function(option){return this.requestBody.sort_type=option.value,this.getListOfFiles()},nextPage:function(){return this.requestBody.page!==this.totalPages&&(this.requestBody.page++,this.getListOfFiles())},prevPage:function(){return 1!==this.requestBody.page&&(this.requestBody.page--,this.getListOfFiles())},goToPage:function(t){return this.requestBody.page=t,this.getListOfFiles()},isPdf:function(t){return"pdf"===Object(l.getFileExtension)(t.path)},isAudio:function(t){var e=Object(l.getFileExtension)(t.path);return"mp3"===e||"wav"===e},getListOfFiles:function(){var t=this,e=new FormData;for(var o in this.requestBody)e.append(o,this.requestBody[o]);this.$store.dispatch("classroom/getListOfFiles",{formData:e,page:this.requestBody.page}).then((function(e){t.totalPages=e.length?e[0].pages:1,t.files=e,t.arrayPages=t.getPager(t.requestBody.page,18,5),t.files.forEach((function(e){t.isPdf(e)&&t.makePdfThumb(e)}))}))},makePdfThumb:function(t){this.pdfjsLib.getDocument(this.rootUrl+t.path).promise.then((function(e){e.getPage(1).then((function(e){if(e){var o=e.getViewport({scale:1}),canvas=document.querySelector("#pdf-thumb--".concat(t.id));if(canvas&&o.width>0&&o.height>0){var r=o.width/o.height;canvas.height=canvas.width=78,o.width<o.height?canvas.width=78*r:canvas.height=78/r;var n=Math.min(canvas.width/o.width,canvas.height/o.height);e.render({canvasContext:canvas.getContext("2d"),viewport:e.getViewport({scale:n})})}}}))})).catch((function(t){console.log(t.message)}))},handleFileDrop:function(t){var e=t.dataTransfer.files;(null==e?void 0:e.length)>0&&this.uploadFiles(e)},addFiles:function(){var t,e=null===(t=this.$refs.file)||void 0===t?void 0:t.files;(null==e?void 0:e.length)>0&&this.uploadFiles(e)},cancelUpload:function(t){t.source.cancel("Operation canceled by user."),this.uploadingFiles=this.uploadingFiles.filter((function(e){return e.id!==t.id}))},uploadFiles:function(t){var e=this;return Object(n.a)(regeneratorRuntime.mark((function o(){var n,i;return regeneratorRuntime.wrap((function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,e.$store.dispatch("loadingAllow",!1);case 2:t=Object(r.a)(t),e.isDragging=!1,n=regeneratorRuntime.mark((function o(i){var n,c,h,m,source,f,v,x,data,y;return regeneratorRuntime.wrap((function(o){for(;;)switch(o.prev=o.next){case 0:if(n=Math.floor(999999*Math.random()),c=t[i],h=Object(l.getFileExtension)(c.name),m=e.$axios.CancelToken,source=m.source(),f={id:n,source:source,name:c.name,uploadPercentage:0},v=new FormData,!(c.size>d.a)){o.next=11;break}return o.next=10,e.$store.dispatch("snackbar/error",{errorMessage:e.$t("filename_size_should_be_less_than",{fileName:c.name,value:"".concat((d.a/8/1e3).toFixed(0)," Mb")}),timeout:5e3});case 10:return o.abrupt("return","continue");case 11:if(e.uploadingFiles.push(f),!e.acceptedFiles.officeTypes.includes(h)){o.next=21;break}return o.next=15,e.$store.dispatch("classroom/convertOfficeToPdf",c);case 15:x=o.sent,data=x.data,y=x.fileName,v.append("file",new Blob([data]),y),o.next=22;break;case 21:v.append("file",c);case 22:return o.next=24,e.$axios.post("".concat("/api/proxy","/lesson/classroom/upload/library/").concat(e.lessonId),v,{headers:{"Content-Type":"multipart/form-data"},cancelToken:source.token,onUploadProgress:function(t){f.uploadPercentage=parseInt(Math.round(t.loaded/t.total*100))},progress:!1}).then((function(t){var o;e.uploadingFiles=e.uploadingFiles.filter((function(t){return t.id!==n})),e.files=[].concat(Object(r.a)(t.data),Object(r.a)(e.files)),(o=e.tickedFiles).push.apply(o,Object(r.a)(t.data)),t.data.forEach((function(t){e.isPdf(t)&&e.makePdfThumb(t)}))})).catch((function(t){return console.log(t)}));case 24:return o.next=26,e.$store.dispatch("loadingAllow",!0);case 26:case"end":return o.stop()}}),o)})),i=0;case 6:if(!(i<=t.length-1)){o.next=14;break}return o.delegateYield(n(i),"t0",8);case 8:if("continue"!==o.t0){o.next=11;break}return o.abrupt("continue",11);case 11:i++,o.next=6;break;case 14:case"end":return o.stop()}}),o)})))()},closeLibrary:function(){this.$store.commit("classroom/toggleLibrary")},addToClassroom:function(){var t=this;return Object(n.a)(regeneratorRuntime.mark((function e(){var o,r,n,d,h,m,f,v;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:o=0,r=0,n=c(t.tickedFiles),e.prev=3,n.s();case 5:if((d=n.n()).done){e.next=30;break}if(h=d.value,m=t.$store.state.classroom.maxIndex+1,f=Object(l.getFileExtension)(h.path),v=void 0,!t.acceptedFiles.pdfTypes.includes(f)){e.next=14;break}v="pdf",e.next=23;break;case 14:if(!t.acceptedFiles.imageTypes.includes(f)){e.next=18;break}v="image",e.next=23;break;case 18:if(!t.acceptedFiles.audioTypes.includes(f)){e.next=22;break}v="audio",e.next=23;break;case 22:return e.abrupt("return");case 23:return e.next=25,t.$store.dispatch("classroom/createAsset",{type:v,index:m,path:h.path,displayName:h.displayName,owner:t.role,top:t.$store.getters["classroom/zoomAsset"].asset.y+r+100,left:t.viewportWidth/2+t.getZoom.x+o-250});case 25:t.$store.commit("classroom/setMaxIndex",m),o+=50,r+=50;case 28:e.next=5;break;case 30:e.next=35;break;case 32:e.prev=32,e.t0=e.catch(3),n.e(e.t0);case 35:return e.prev=35,n.f(),e.finish(35);case 38:t.uncheckFiles(),t.closeLibrary();case 40:case"end":return e.stop()}}),e,null,[[3,32,35,38]])})))()}}},f=(o(1791),o(22)),v=o(42),x=o.n(v),y=o(1327),w=o(261),k=o(269),component=Object(f.a)(m,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{class:["popup-load-files elevation-3",t.role],style:{zIndex:t.zIndex}},[r("div",{ref:"loadFilesHeader",staticClass:"popup-load-files-header",class:{inactive:t.tickedFiles.length>0}},[r("div",{staticClass:"popup-load-files-title"},[t._v("\n      "+t._s(t.$t("library"))+"\n    ")]),t._v(" "),r("div",{staticClass:"popup-load-files-header-buttons"},[r("div",{staticClass:"popup-load-files-search-wrap"},[r("input",{directives:[{name:"model",rawName:"v-model",value:t.queryStr,expression:"queryStr"}],staticClass:"popup-load-files-search popup-load-files-input",attrs:{placeholder:"Search"},domProps:{value:t.queryStr},on:{input:function(e){e.target.composing||(t.queryStr=e.target.value)}}}),t._v(" "),t._m(0)]),t._v(" "),r("div",{staticClass:"popup-load-files-select-wrap"},[r("div",{staticClass:"popup-load-files-input popup-load-files-select cursor-pointer",on:{click:t.toggleSortOptionsList}},[t._v("\n          "+t._s(t.$t("sort_by"))+"\n        ")]),t._v(" "),r("div",{staticClass:"popup-load-files-select-options",class:{active:t.sortListDisplay}},t._l(t.sortOptions,(function(option,e){return r("div",{key:e,staticClass:"cursor-pointer",class:{"popup-load-files-select-option":t.requestBody.sort_type===option.value},on:{click:function(e){return t.changeSortType(option)}}},[t._v("\n            "+t._s(option.label)+"\n          ")])})),0)])])]),t._v(" "),r("div",{ref:"headerSelectedFiles",staticClass:"popup-load-files-header-selected-files",class:{active:t.tickedFiles.length>0}},[r("button",{staticClass:"popup-load-files-input cursor-pointer",attrs:{id:"add-to-classroom"},on:{click:t.addToClassroom}},[t._v("\n      "+t._s(t.$t("add_to_classroom"))+"\n    ")]),t._v(" "),r("div",{staticClass:"popup-load-files-buttons-wrap"},[r("button",{staticClass:"popup-load-files-input cursor-pointer",attrs:{id:"popup-load-files-download"},on:{click:t.downloadFiles}},[t._v("\n        "+t._s(t.$t("download"))+"\n      ")]),t._v(" "),r("button",{staticClass:"popup-load-files-input cursor-pointer",on:{click:t.deleteFiles}},[t._v("\n        "+t._s(t.$t("delete"))+"\n      ")]),t._v(" "),r("v-btn",{staticClass:"popup-load-files-header-cross cursor-pointer",attrs:{icon:"",color:"white"},on:{click:t.uncheckFiles}},[r("svg",{attrs:{width:"28",height:"28",viewBox:"0 0 21 20"}},[r("use",{attrs:{"xlink:href":o(91)+"#close"}})])])],1)]),t._v(" "),r("div",{staticClass:"popup-load-files-wrap",on:{dragover:function(e){e.stopPropagation(),e.preventDefault(),t.isDragging=!0}}},[r("div",{staticClass:"popup-load-files-body"},[r("div",{directives:[{name:"show",rawName:"v-show",value:t.isDragging,expression:"isDragging"}],staticClass:"popup-load-files-drop-wrap active",on:{dragleave:function(e){e.stopPropagation(),e.preventDefault(),t.isDragging=!1},drop:function(e){return e.stopPropagation(),e.preventDefault(),t.handleFileDrop.apply(null,arguments)}}},[r("div",{staticClass:"drop-area--wrapper"},[r("img",{staticClass:"drop-area--wrapper__dropbox-img",attrs:{src:o(908),alt:""}})])]),t._v(" "),r("div",[r("div",{staticClass:"popup-load-files-list",attrs:{id:"popup-load-files-list"}},[t.uploadingFiles?t._l(t.uploadingFiles,(function(e,n){return r("div",{key:n,staticClass:"popup-load-files-item popup-load-files-item--loading"},[r("div",{staticClass:"popup-load-files-item-helper"},[r("div",{staticClass:"popup-load-files-item-img"},[r("v-progress-circular",{attrs:{rotate:360,size:75,width:8,indeterminate:0===e.uploadPercentage,value:e.uploadPercentage,color:"success"}},[e.uploadPercentage>0?[t._v("\n                      "+t._s(e.uploadPercentage)+"%\n                    ")]:t._e()],2),t._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:e.uploadPercentage>0,expression:"loadingFile.uploadPercentage > 0"}],staticClass:"popup-load-files-item-cancel cursor-pointer",on:{click:function(o){return t.cancelUpload(e)}}},[r("div",{staticClass:"popup-load-files-tick-icon"},[r("svg",{attrs:{width:"15",height:"15",viewBox:"0 0 21 20"}},[r("use",{attrs:{"xlink:href":o(91)+"#close"}})])])])],1)]),t._v(" "),r("div",{staticClass:"popup-load-files-item-name"},[r("p",[t._v(t._s(e.name))])])])})):t._e(),t._v(" "),t._l(t.files,(function(e){return r("div",{key:e.id,staticClass:"popup-load-files-item"},[r("div",{staticClass:"popup-load-files-item-helper"},[r("div",{staticClass:"popup-load-files-item-img cursor-pointer",on:{click:function(o){return t.toggleFileMark(e)}}},[t.isPdf(e)?[r("canvas",{attrs:{id:"pdf-thumb--"+e.id,title:e.displayName}})]:t.isAudio(e)?[r("v-img",{attrs:{src:o(937),"max-width":"60",title:e.displayName}})]:[r("v-img",{attrs:{src:""+t.rootUrl+e.path,"max-width":"75%","max-height":"75%",title:e.displayName}})],t._v(" "),r("div",{staticClass:"popup-load-files-item-tick",class:{active:t.tickedFiles.find((function(t){return t.id===e.id}))}},[r("div",{staticClass:"popup-load-files-tick-icon"},[r("img",{attrs:{src:o(936),alt:""}})])])],2)]),t._v(" "),r("div",{staticClass:"popup-load-files-item-name"},[r("p",{attrs:{title:e.displayName}},[t._v(t._s(e.displayName))])])])}))],2)])]),t._v(" "),r("div",{staticClass:"popup-load-files-footer"},[t.arrayPages.length>1?r("div",{staticClass:"popup-load-files-footer-pagination"},[r("button",{staticClass:"popup-load-files-btn-nav popup-load-files-btn-nav-prev cursor-pointer",attrs:{disabled:1===t.requestBody.page},on:{click:t.prevPage}},[r("span",{staticClass:"d-block popup-load-files-nav-icon popup-load-files-nav-icon-prev"}),t._v(" "),r("span",[t._v(t._s(t.$t("previous")))])]),t._v(" "),r("div",{staticClass:"popup-load-files-nav-wrap",attrs:{id:"popup-load-files-navigation"}},t._l(t.arrayPages,(function(e,o){return r("span",{key:o,staticClass:"popup-load-files-nav-number",class:{active:t.requestBody.page===e},on:{click:function(o){return t.goToPage(e)}}},[t._v("\n            "+t._s(e)+"\n          ")])})),0),t._v(" "),r("button",{staticClass:"popup-load-files-btn-nav popup-load-files-btn-nav-next cursor-pointer",attrs:{disabled:t.requestBody.page===t.arrayPages.length},on:{click:t.nextPage}},[r("span",[t._v(t._s(t.$t("next")))]),t._v(" "),r("span",{staticClass:"d-block popup-load-files-nav-icon popup-load-files-nav-icon-next"})])]):t._e(),t._v(" "),r("div",{staticClass:"popup-load-files-footer-buttons"},[r("label",{staticClass:"popup-load-files-label-upload cursor-pointer font-weight-medium"},[t._v("\n          "+t._s(t.$t("upload_new_file"))+"\n          "),r("input",{ref:"file",staticClass:"popup-load-files-btn-upload",attrs:{id:"upload-library-files",type:"file",accept:t.acceptedFilesStr,multiple:""},on:{change:t.addFiles}})]),t._v(" "),r("v-btn",{staticClass:"font-weight-medium popup-load-files-close cursor-pointer",attrs:{color:"primary",small:""},on:{click:t.closeLibrary}},[t._v("\n          "+t._s(t.$t("cancel"))+"\n        ")])],1)])])])}),[function(){var t=this.$createElement,e=this._self._c||t;return e("button",{staticClass:"popup-load-files-search-icon",attrs:{id:"library-add-search"}},[e("span",{staticClass:"d-block library-add-search-img"})])}],!1,null,"6a1d2341",null);e.default=component.exports;x()(component,{VBtn:y.a,VImg:w.a,VProgressCircular:k.a})},1946:function(t,e,o){"use strict";o.r(e);var r=o(2),n=(o(31),o(20),o(80),o(63),o(6),o(55),o(7),o(8),o(9),o(14),o(15),o(266)),l=o(1428),d=o(1621),c=o(1438);function h(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}var m={name:"Toolbar",mixins:[l.a,d.a,c.a],props:{studentId:{type:String,required:!0},file:{type:Object,required:!0},viewportWidth:{type:Number,required:!0},viewportHeight:{type:Number,required:!0},scale:{type:Number,default:1},minZoom:{type:Number,required:!0},isFinishedAllowed:{type:Boolean,required:!0}},data:function(){return{buzzed:!1,currentTool:"pointer",currentHorizontalMenu:null,offset:5}},computed:{isCanvasOversizeX:function(){return n.n>this.viewportWidth},isScaledCanvasOversizeX:function(){return n.n*this.scale>this.viewportWidth},isCanvasOversizeY:function(){return n.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return n.k*this.scale>this.viewportHeight},style:function(){return{bottom:this.isScaledCanvasOversizeY?"10px":"".concat(this.viewportHeight-n.k*this.scale+2*this.offset,"px"),right:this.isScaledCanvasOversizeX?"10px":"".concat(this.viewportWidth-n.n*this.scale+2*this.offset,"px")}},studentStatus:function(){var t,e="offline";return Object.prototype.hasOwnProperty.call(this.userStatuses,null===(t=this.studentId)||void 0===t?void 0:t.toString())&&(e=this.userStatuses[this.studentId]),e},alertDisabled:function(){return this.buzzed||"online"!==this.studentStatus},maxIndex:function(){return this.$store.state.classroom.maxIndex+100},isLocked:function(){var t,e;return null===(t=this.file)||void 0===t||null===(e=t.asset)||void 0===e?void 0:e.isLocked},isTeacher:function(){return this.$store.getters["user/isTeacher"]},isStudent:function(){return this.$store.getters["user/isStudent"]},isLockedForStudent:function(){return this.isLocked&&this.isStudent},lessonId:function(){return this.$store.state.classroom.lessonId},isLessonFinished:function(){return this.$store.getters["classroom/isLessonFinished"]},defaultZoomIndex:function(){return this.minZoom>1?this.minZoom:1},acceptedFilesStr:function(){return this.$store.getters["classroom/acceptedFilesStr"]}},watch:{isLockedForStudent:function(t,e){t&&(this.resetCurrentValues(),this.$store.commit("classroom/closeVideoInput"))}},beforeMount:function(){this.arrStatusId=[this.studentId],this.refreshStatusOnline()},beforeDestroy:function(){this.resetCurrentValues()},methods:{selectToolClickHandler:function(t,e){this.currentTool=t,this.currentHorizontalMenu=null,this.setTool(t,e)},uploadFromComputer:function(t){this.currentHorizontalMenu=null,this.uploadFiles(t.target.files)},buzz:function(){var t=this;this.buzzed=!0,setTimeout((function(){t.buzzed=!1}),3e4),this.$store.dispatch("classroom/buzz",this.lessonId)},reset:function(){var t,e,o=this,i=1,l=0,d=0;this.$store.state.classroom.assets.slice(0).forEach((function(c){var m=function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?h(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):h(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},c.asset);switch(i++,m.type){case"shape":case"lock":break;case"editor":m.width=.66*(o.isCanvasOversizeX?o.viewportWidth:n.n),(t=.8*(o.isCanvasOversizeY?o.viewportHeight:n.k))>1200&&(t=1200),t<400&&(t=400),m.height=t-2*o.offset,m.top=o.offset,m.left=o.offset,m.index=1;break;case"whereby":m.width=400,m.height=300,m.top=o.offset,m.left=(o.isCanvasOversizeX?o.viewportWidth:n.n)-m.width-o.offset,m.index=i;break;case"pdf":case"image":case"video":case"audio":e=n.j/c.asset.width,m.width=n.j,m.height=m.height*e,m.top=o.$store.getters["classroom/zoomAsset"].asset.y+d+100,m.left=o.viewportWidth/2+o.$store.getters["classroom/zoomAsset"].asset.x+l-250,m.index=i,l+=50,d+=50;break;case"zoom":m.zoomIndex=o.defaultZoomIndex,m.x=0,m.y=0}o.$store.commit("classroom/moveAsset",{id:c.id,asset:m}),o.$store.dispatch("classroom/moveAsset",{id:c.id,lessonId:c.lessonId,asset:m})}))},toggleVideoInput:function(){this.$store.commit("classroom/toggleVideoInput")},openLibrary:function(){this.currentHorizontalMenu=null,this.$store.commit("classroom/toggleLibrary")},toggleStudentRoomStatus:function(){var t={isLocked:!this.isLocked};this.$store.commit("classroom/moveAsset",{id:this.file.id,asset:t}),this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.lessonId,asset:t})},resetCurrentValues:function(){this.currentTool="pointer",this.currentHorizontalMenu=null},finishLesson:function(){var t=this;this.$store.dispatch("lesson/finishLesson",this.lessonId).then((function(){t.exitLesson()})).catch((function(e){t.$store.dispatch("snackbar/error"),console.info(e)}))},exitLesson:function(){window.location="/user/lessons"}}},f=(o(1793),o(22)),component=Object(f.a)(m,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{class:["toolbar","toolbar--"+t.role],style:t.style},[r("ul",{ref:"toolbar_buttons",staticClass:"toolbar-buttons",attrs:{id:"toolbar-buttons"}},[r("li",{staticClass:"toolbar-button-wrapper"},[r("button",{class:["toolbar-button-item toolbar-button-pointer cursor-pointer",{selected:"pointer"===t.currentTool}],attrs:{id:"toolbar-switch","data-toolbar-default-cursor":"",disabled:t.isLockedForStudent},on:{click:function(e){return t.selectToolClickHandler("pointer","cursor-pointer")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"32",height:"34",viewBox:"0 0 32 34"}},[r("use",{attrs:{"xlink:href":o(862)+"#pointer"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v(t._s(t.$t("default_cursor")))])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper toolbar-button-wrapper-pencil",on:{mouseleave:function(e){e.stopPropagation(),t.currentHorizontalMenu=null}}},[r("button",{class:["toolbar-button-item toolbar-button-hand cursor-pointer",{selected:"line"===t.currentTool||"circle"===t.currentTool||"triangle"===t.currentTool||"square"===t.currentTool||"pen"===t.currentTool}],attrs:{disabled:t.isLockedForStudent},on:{click:function(e){t.currentHorizontalMenu="toolbar-horizontal"}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"33",height:"33",viewBox:"0 0 33 33"}},[r("use",{attrs:{"xlink:href":o(862)+"#pencil"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v(t._s(t.$t("drawing")))]),t._v(" "),r("div",{class:["toolbar-buttons-horizontal",{"toolbar-show":"toolbar-horizontal"===t.currentHorizontalMenu}]},[r("ul",[r("li",{staticClass:"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-draw-line"},[r("button",{class:["toolbar-button-item toolbar-button-item-horizontal cursor-pointer",{selected:"line"===t.currentTool}],attrs:{"data-toolbar-tool-line":""},on:{click:function(e){return t.selectToolClickHandler("line","pencil-cursor")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"39",height:"37",viewBox:"0 0 39 37"}},[r("use",{attrs:{"xlink:href":o(862)+"#draw-line"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("draw_line"))+"\n            ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper-horizontal"},[r("button",{class:["toolbar-button-item toolbar-button-item-horizontal cursor-pointer",{selected:"circle"===t.currentTool}],attrs:{"data-toolbar-tool-circle":""},on:{click:function(e){return t.selectToolClickHandler("circle","pencil-cursor")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"36",height:"37",viewBox:"0 0 39 40"}},[r("use",{attrs:{"xlink:href":o(862)+"#draw-circle"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("draw_circle"))+"\n            ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper-horizontal"},[r("button",{class:["toolbar-button-item toolbar-button-item-horizontal cursor-pointer",{selected:"triangle"===t.currentTool}],attrs:{"data-toolbar-tool-triangle":""},on:{click:function(e){return t.selectToolClickHandler("triangle","pencil-cursor")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"41",height:"34",viewBox:"0 0 41 34"}},[r("use",{attrs:{"xlink:href":o(862)+"#draw-triangle"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("draw_triangle"))+"\n            ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper-horizontal"},[r("button",{class:["toolbar-button-item toolbar-button-item-horizontal cursor-pointer",{selected:"square"===t.currentTool}],attrs:{"data-toolbar-tool-square":""},on:{click:function(e){return t.selectToolClickHandler("square","pencil-cursor")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"36",height:"38",viewBox:"0 0 36 38"}},[r("use",{attrs:{"xlink:href":o(862)+"#draw-square"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("draw_square"))+"\n            ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-draw-pencil"},[r("button",{class:["toolbar-button-item toolbar-button-item-horizontal cursor-pointer",{selected:"pen"===t.currentTool}],attrs:{"data-toolbar-tool-pen":""},on:{click:function(e){return t.selectToolClickHandler("pen","pencil-cursor")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"33",height:"33",viewBox:"0 0 33 33"}},[r("use",{attrs:{"xlink:href":o(862)+"#pencil"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("enable_drawing_tool"))+"\n            ")])])])])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper"},[r("button",{class:["toolbar-button-item cursor-pointer",{selected:"eraser"===t.currentTool}],attrs:{"data-toolbar-eraser":"",disabled:t.isLockedForStudent},on:{click:function(e){return t.selectToolClickHandler("eraser","eraser-cursor")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"35",height:"31",viewBox:"0 0 35 31"}},[r("use",{attrs:{"xlink:href":o(862)+"#lastic"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v("\n        "+t._s(t.$t("enable_erasing_tool"))+"\n      ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper"},[r("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{id:"toolbar-button-video","data-toolbar-add-video":"",disabled:t.isLockedForStudent},on:{click:t.toggleVideoInput}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"39",height:"31",viewBox:"0 0 39 31"}},[r("use",{attrs:{"xlink:href":o(862)+"#play"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v(t._s(t.$t("add_video")))])]),t._v(" "),t.isTeacher?r("li",{staticClass:"toolbar-button-wrapper"},[r("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-toolbar-buzz-student":"",disabled:t.alertDisabled},on:{click:function(e){return e.preventDefault(),t.buzz.apply(null,arguments)}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"35",height:"38",viewBox:"0 0 35 38"}},[r("use",{attrs:{"xlink:href":o(862)+"#ring"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v(t._s(t.$t("buzz_student")))])]):t._e(),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper",on:{mouseleave:function(e){e.stopPropagation(),t.currentHorizontalMenu=null}}},[r("button",{staticClass:"toolbar-button-item toolbar-button-hand cursor-pointer",on:{click:function(e){t.currentHorizontalMenu="toolbar-horizontal-file"}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"29",height:"38",viewBox:"0 0 29 38"}},[r("use",{attrs:{"xlink:href":o(862)+"#library"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v("\n        "+t._s(t.$t("library"))+"\n      ")]),t._v(" "),r("div",{class:["toolbar-buttons-horizontal toolbar-buttons-horizontal-file",{"toolbar-show":"toolbar-horizontal-file"===t.currentHorizontalMenu}]},[r("ul",[r("li",{staticClass:"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-books"},[r("button",{staticClass:"toolbar-button-item toolbar-button-item-horizontal cursor-pointer",attrs:{id:"load-files-library","data-toolbar-library":""},on:{click:t.openLibrary}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"38",height:"38",viewBox:"0 0 38 38"}},[r("use",{attrs:{"xlink:href":o(862)+"#books"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("select_from_library"))+"\n            ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-laptop"},[r("button",{staticClass:"toolbar-button-item toolbar-button-item-horizontal cursor-pointer",attrs:{"data-toolbar-computer":""}},[r("label",{staticClass:"popup-load-files-label-upload popup-load-files-label-upload-laptop"},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"41",height:"34",viewBox:"0 0 41 34"}},[r("use",{attrs:{"xlink:href":o(862)+"#laptop"}})]),t._v(" "),r("input",{staticClass:"popup-load-files-btn-upload",attrs:{id:"upload-library-files-laptop",type:"file",multiple:"",accept:t.acceptedFilesStr},on:{change:t.uploadFromComputer}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("upload_from_computer"))+"\n            ")])])])])]),t._v(" "),t.isTeacher?r("li",{staticClass:"toolbar-button-wrapper"},[r("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-toolbar-lock":""},on:{click:function(e){return e.preventDefault(),t.toggleStudentRoomStatus.apply(null,arguments)}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"38",height:"50",viewBox:"0 0 38 50"}},[r("use",{attrs:{"xlink:href":o(862)+"#"+(t.isLocked?"lock":"unlock")}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t.isLocked?[t._v("\n          "+t._s(t.$t("enable_moving_resizing_drawing_for_student"))+"\n        ")]:[t._v("\n          "+t._s(t.$t("disable_moving_resizing_drawing_for_student"))+"\n        ")]],2)]):t._e(),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper toolbar-button-wrapper-reset"},[r("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-toolbar-reset":"",disabled:t.isLockedForStudent},on:{click:t.reset}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"36",height:"36",viewBox:"0 0 37 37"}},[r("use",{attrs:{"xlink:href":o(862)+"#restore"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v("\n        "+t._s(t.$t("restore_whiteboard_video_to_original_positions"))+"\n      ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper toolbar-button-wrapper-exit"},[r("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-toolbar-exit":""},on:{click:t.exitLesson}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"36",height:"36",viewBox:"0 0 37 37"}},[r("use",{attrs:{"xlink:href":o(862)+"#exit"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v(t._s(t.$t("exit_class")))])]),t._v(" "),t.isTeacher?r("li",{staticClass:"toolbar-button-wrapper",on:{mouseleave:function(e){e.stopPropagation(),t.currentHorizontalMenu=null}}},[r("button",{staticClass:"toolbar-button-item toolbar-button-hand cursor-pointer",attrs:{disabled:t.isLessonFinished||!t.isFinishedAllowed},on:{click:function(e){t.currentHorizontalMenu="toolbar-horizontal-finish"}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"43",height:"38",viewBox:"0 0 43 38"}},[r("use",{attrs:{"xlink:href":o(862)+"#tick"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v("\n        "+t._s(t.$t(t.isLessonFinished?"class_finished":"finish_class"))+"\n      ")]),t._v(" "),r("div",{class:["toolbar-buttons-horizontal toolbar-buttons-horizontal-file",{"toolbar-show":"toolbar-horizontal-finish"===t.currentHorizontalMenu}]},[r("ul",[r("li",{staticClass:"toolbar-button-wrapper-horizontal toolbar-button-wrapper-finish"},[r("button",{staticClass:"toolbar-button-item toolbar-button-item-horizontal cursor-pointer",attrs:{"data-toolbar-finish":"",type:"submit"},on:{click:t.finishLesson}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"43",height:"38",viewBox:"0 0 43 38"}},[r("use",{attrs:{"xlink:href":o(862)+"#tick"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("finish_class"))+"\n            ")])])])])]):t._e(),t._v(" "),t.isStudent?r("li",{staticClass:"toolbar-button-wrapper"},[r("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{disabled:"disabled"}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"38",height:"50",viewBox:"0 0 38 50"}},[r("use",{attrs:{"xlink:href":o(862)+"#"+(t.isLocked?"lock":"unlock")}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t.isLocked?[t._v("\n          "+t._s(t.$t("moving_resizing_drawing_are_disabled"))+"\n        ")]:[t._v("\n          "+t._s(t.$t("classroom_controls_are_unlocked"))+"\n        ")]],2)]):t._e()])])}),[],!1,null,"53b5e223",null);e.default=component.exports},2063:function(t,e,o){"use strict";o(1858)},2064:function(t,e,o){var r=o(18),n=o(265),l=o(863),d=o(885),c=o(957),h=o(960),m=o(951),f=o(955),v=o(946),x=o(948),y=o(958),w=o(959),k=o(949),C=o(950),z=o(961),_=o(952),O=o(956),S=o(947),$=o(943),E=o(942),P=o(954),I=o(953),A=o(941),M=o(940),T=o(945),D=o(944),j=r(!1),L=n(l),F=n(d),H=n(c),R=n(h),B=n(m),N=n(f),W=n(v),Y=n(x),U=n(y),X=n(w),V=n(k),K=n(C),Z=n(z),J=n(_),G=n(O),Q=n(S),tt=n($),et=n(E),ot=n(P),st=n(I),it=n(A),nt=n(M),at=n(T),lt=n(D);j.push([t.i,'html.classroom-page{font-size:1em!important;overflow:hidden}.classroom{background:#f2f2f2!important}.classroom div,.classroom iframe,.classroom image{-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.note-editable p{-webkit-touch-callout:default;-webkit-user-select:auto}.note-editable a{color:#00f!important}.note-editable a:hover{text-decoration:underline}.note-editable[contenteditable=false]{position:relative}.note-editable[contenteditable=false]:after{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background-color:hsla(0,0%,100%,.5)}.transparent .konvajs-content>canvas{background:transparent!important}.columns-wrapper .middle .middle-inner,.container-fluid{padding:0;margin:0}.img-classroom-preview{position:absolute;top:2px;right:2px;border:1px solid grey;background-color:#e4e4e4;z-index:1;width:250px}.moveable-control-box,.moveable-control-box .moveable-line{display:none!important}.cursor-pointer,.cursor-pointer *{cursor:pointer!important}.cursor-auto{cursor:auto!important}.selected{border-bottom:none}.selected.button-student svg{color:var(--v-studentColor-base)}.selected.button-teacher svg{color:var(--v-teacherColor-base)}.hide{display:none}.plyr__controls{position:fixed!important;padding:4px!important;background:#5e5e5e!important}.toolbar-buttons{position:relative;width:40px;padding-left:0;background:#fff;border-radius:6px;box-shadow:0 2px 10px rgba(0,0,0,.15)}.toolbar-buttons li{list-style:none}.toolbar-button-wrapper{width:40px;display:flex;height:40px;justify-content:center;position:relative}.toolbar-button-wrapper-replace,.toolbar-button-wrapper-undo{padding:0}.toolbar-button-item{display:flex;align-items:center;justify-content:center;width:100%;height:100%;padding:8px;position:relative;outline:none;border:none;background:transparent}.toolbar-button-undo{padding:8px 0;margin-bottom:8px;width:100%;border-radius:6px;box-shadow:0 4px 2px -2px rgba(0,0,0,.15)}.toolbar-button-item:disabled,.toolbar-button-item:disabled *,.toolbar-button-item[disabled],.toolbar-button-item[disabled] *{cursor:default!important}.toolbar-button-file:before,.toolbar-button-hand:before{content:"";position:absolute;left:2px;bottom:2px;border-left:2px solid transparent;border-bottom:2px solid transparent;border-color:transparent transparent #000 #000;border-style:solid;border-width:2px}.toolbar-button-item:disabled:before,.toolbar-button-item[disabled]:before{opacity:.3}.toolbar-button-icon{width:auto;height:auto;max-height:100%;max-width:100%}.toolbar-buttons-horizontal{display:none;position:absolute;top:0;right:40px;padding-right:5px;border-radius:6px}.toolbar-button-item-draw-line{padding-right:0}.toolbar-button-item-hand{padding-left:0}.toolbar-button-item-horizontal .toolbar-button-icon{height:auto;width:auto}.toolbar-buttons-horizontal .toolbar-button-wrapper:first-child .toolbar-button-item-horizontal{border-bottom-left-radius:4px!important;border-top-left-radius:4px!important}.stream-controls .toolbar-button-item svg{color:var(--v-darkLight-base)}.stream-controls .toolbar-button-item:disabled svg,.stream-controls .toolbar-button-item[disabled] svg{color:#c6c6c6}#toolbar-switch{border-top-left-radius:4px;border-top-right-radius:4px}.toolbar-button-wrapper:last-child .toolbar-button-item{border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-bottom:none!important}.button-teacher:hover svg{color:var(--v-teacherColor-base)!important}.button-student:hover svg{color:var(--v-studentColor-base)!important}.toolbar-button-item svg{color:var(--v-darkLight-base)}.hover-btn-info{display:none;width:auto;right:50px;color:#fff;background:#444;border-radius:5px;padding:5px 10px;white-space:nowrap;font-size:13px}.hover-btn-info,.hover-btn-info:after{position:absolute;top:50%;transform:translateY(-50%)}.hover-btn-info:after{content:"";right:-9px;border:5px solid transparent;border-left-color:#444}.hover-horizontal-button{top:auto;right:auto;bottom:50px;left:50%;transform:translateX(-50%)}.hover-horizontal-button:after{border-top:5px solid #444;border-left:5px solid transparent;top:auto;right:auto;bottom:-9px;left:50%;transform:translateX(-50%)}.hover-btn-info-horizontal{top:-40px;right:-60%}.toolbar-button-replace+.hover-btn-info{top:40%}input.video-load-input{padding:5px;border-radius:3px;margin-bottom:15px}.video-buttons-wrap{display:flex;align-items:center}.video-load-btn{background:#5aac44;color:#fff;border:none;outline:none;padding:6px 12px;border-radius:3px}.video-load-cross{margin-left:10px;font-size:30px;color:#6b778c;vertical-align:middle}.video_annotations{width:2px;height:12px;position:absolute;top:4px;background:red}#video-window{position:absolute;width:100%;height:100%;background:#000}.local-stream{position:absolute;top:0;left:0;width:100px;height:75px;background-color:#000;z-index:3}.remote-stream{z-index:2;position:absolute;width:100%;height:100%}.remote-screenshare{background-color:#000}.hr-flip{transform:scaleX(-1)}.embed-responsive .embed-responsive-item,.embed-responsive embed,.embed-responsive iframe,.embed-responsive object,.embed-responsive video{position:absolute;top:0;left:0;bottom:0;height:100%;width:100%;border:0}.video-window--is-fullscreen .stream-controls{bottom:5px!important}.screenshare-component{position:relative;height:100%;background-color:#000}.screenshare-component .user-name{position:absolute;top:0;right:0;padding:4px 13px;line-height:1;background:#fff;font-size:12px;font-weight:500;box-shadow:0 2px 5px rgba(0,0,0,.2);border:none;border-bottom-left-radius:6px;z-index:5}.student-role .stream-controls{bottom:-20px}.stream-controls--screenshare{bottom:-20px!important}.stream-controls--screenshare .stream-controls-wrapper{padding:0}.stream-controls--screenshare .hover-btn-info{left:50%;right:auto;transform:translateX(-50%);top:auto;bottom:-42px}.stream-controls--screenshare .hover-btn-info:after{left:50%;transform:translateX(-50%);top:-9px;right:auto;border:5px solid transparent;border-bottom-color:#444}.stream-controls--screenshare .stream-controls{bottom:15px}.student-role .video-window--is-fullscreen .stream-controls{bottom:38px}.embed-responsive{position:static}.video-window--is-fullscreen .embed-responsive-4by3{height:100%;padding-bottom:0}.video-window-buttons-wrap{height:100%;display:inline-flex;flex-direction:column;box-shadow:0 2px 10px rgba(0,0,0,.25);border-radius:6px}#screenshare::-webkit-media-controls-play-button,#screenshare::-webkit-media-controls-timeline{display:none!important}#screenshare::-webkit-media-controls-current-time-display,#screenshare::-webkit-media-controls-time-remaining-display{display:none!important}#screenshare::-webkit-media-controls-mute-button,#screenshare::-webkit-media-controls-toggle-closed-captions-button{display:none!important}#screenshare::-webkit-media-controls-volume-slider{display:none!important}#screenshare::-webkit-media-controls-panel{background-image:linear-gradient(transparent,transparent)!important;display:flex!important;opacity:1!important}.OT_publisher,.OT_subscriber{min-width:100%!important;min-height:100%!important}#video-share-catch{z-index:91}.btn{box-shadow:none}.note-btn-group.note-insert,.note-btn-group.note-para,.note-btn-group.note-style{box-shadow:0 0 10px rgba(0,0,0,.12)}.note-editor{height:100%}.note-btn-group.note-style{position:relative;z-index:14}.note-btn-group.note-style,.note-btn-group.note-style .btn:first-child{border-bottom-left-radius:4px;border-top-left-radius:4px}.note-btn-group.note-insert{position:relative;z-index:12}.note-btn-group.note-para .note-btn-group,.note-btn-group.note-para .note-btn-group .btn{border-bottom-right-radius:4px;border-top-right-radius:4px}.note-btn{padding:8px 10px}.note-toolbar-wrapper{height:auto!important}.panel{box-shadow:0 2px 30px rgba(0,0,0,.25);margin-bottom:0}.stream-controls-wrapper{position:relative;display:inline-flex;justify-content:center;background:#fff;padding:0 10px;border-radius:6px;box-shadow:0 2px 10px rgba(0,0,0,.25);z-index:12}.stream-controls-wrapper button:first-child{border-bottom-left-radius:4px;border-top-left-radius:4px}.stream-controls-wrapper button:last-child{border-bottom-right-radius:4px;border-top-right-radius:4px}#play-pause img{margin-top:5px;max-height:27px;max-width:28px}#mute,#play-pause{position:relative;height:100%;transition:all .5s ease-in-out}#mute:after,#play-pause:after{content:"";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%) rotate(-45deg);display:block;width:2px;height:80%;background:#4a4a4a;opacity:0;transition:all .5s ease-in-out}.popup-load-files{position:absolute;left:50%;top:50px;max-width:645px;width:calc(100% - 20px);border-radius:8px;box-shadow:0 2px 30px rgba(0,0,0,.25);transform:translateX(-50%);overflow:hidden;z-index:1000000000!important}.popup-load-files,.popup-load-files *{cursor:auto!important}.popup-load-files .cursor-pointer,.popup-load-files .cursor-pointer *{cursor:pointer!important}.popup-load-files-header{min-height:52px;cursor:auto!important;padding:0 12px 10px;background:var(--v-teacherColor-lighten1);display:flex;flex-wrap:wrap;justify-content:space-between;align-items:center;border-top-right-radius:8px;border-top-left-radius:8px}.popup-load-files-header-buttons{display:flex;flex-wrap:wrap;flex-grow:1;justify-content:flex-end}@media only screen and (max-width:479px){.popup-load-files-header-buttons{justify-content:center}}.popup-load-files-header-buttons>*{margin-top:10px}.popup-load-files-header-selected-files{display:none;position:relative;min-height:53px;flex-wrap:wrap;justify-content:space-between;align-items:center;background:var(--v-teacherColor-lighten1);padding:0 46px 10px 12px;border-top-right-radius:8px;border-top-left-radius:8px}.popup-load-files-header-selected-files button:not(.popup-load-files-header-cross){margin-top:10px}.popup-load-files-header-selected-files.active{cursor:auto!important;display:flex}.popup-load-files-header-cross{position:absolute;top:6px;right:10px}.popup-load-files-title{margin:10px 20px 0 0;color:var(--v-darkLight-base);font-weight:700;font-size:22px}@media only screen and (max-width:639px){.popup-load-files-title{font-size:18px}}input[type=file].popup-load-files-btn-upload{display:none}.popup-load-files-input,label.popup-load-files-label-upload{padding:6px 14px;margin-right:14px;margin-bottom:0;border:none;outline:none;background:#fff;border-radius:8px;box-shadow:0 4px 10px rgba(0,0,0,.15);font-weight:700;font-size:13px;color:var(--v-darkLight-base)}label.popup-load-files-label-upload.popup-load-files-label-upload-laptop{display:flex;justify-content:center;align-items:center;padding:0;box-shadow:none}.popup-load-files .popup-load-files-label-upload{display:flex;align-items:center;justify-content:center;min-width:142px;text-align:center;margin-right:4px;padding:0 16px;font-size:15px;border:1px solid var(--v-teacherColor-base);white-space:nowrap;border-radius:24px;box-shadow:none}.popup-load-files-input:last-child{margin-right:0}.popup-load-files-input::-webkit-input-placeholder{color:var(--v-darkLight-base)}.popup-load-files-input::-moz-placeholder{color:var(--v-darkLight-base)}.popup-load-files-input:-ms-input-placeholder{color:var(--v-darkLight-base)}.popup-load-files-input:-moz-placeholder{color:var(--v-darkLight-base)}.popup-load-files-label-search,.popup-load-files-select-wrap{position:relative}.popup-load-files-select{position:relative;width:166px;margin-right:0}.popup-load-files-select-options:after{content:"";position:absolute;top:-10px;left:50%;transform:translateX(-50%);display:block;border:5px solid transparent;border-bottom-color:#fff}.popup-load-files-select-wrap{position:relative}.popup-load-files-select-wrap:after{content:"";position:absolute;top:50%;left:90%;transform:translateY(-20%);display:block;border:5px solid transparent;border-top-color:#000;z-index:12}.popup-load-files-select-options{position:absolute;display:none;width:100%;top:130%;left:0;background:#fff;padding:12px 20px;border-radius:8px;box-shadow:0 4px 10px rgba(0,0,0,.15);z-index:19}.popup-load-files-select-options.active{display:block}.popup-load-files-select-option{position:relative;font-size:16px;padding:6px 0}.popup-load-files-select-option:last-child{margin-bottom:0}.popup-load-files-select-option:before{content:"";position:absolute;top:50%;left:-13px;display:block;width:6px;height:6px;transform:translateY(-20%);border-radius:50%;background:#000}.popup-load-files-search-wrap{position:relative}.library-add-search-img{width:21px;height:20px;background:url('+L+') no-repeat 50%;background-size:contain}.popup-load-files-search{padding-right:38px}.popup-load-files-search-icon{position:absolute;top:50%;transform:translateY(-50%);right:23px;border:none;background:none;outline:none}.popup-load-files-body{min-height:384px}@media only screen and (max-width:639px){.popup-load-files-body{min-height:278px}}@media only screen and (max-width:479px){.popup-load-files-body{min-height:198px}}.popup-load-files-list{display:flex;flex-wrap:wrap;padding:0 0 15px 15px}@media only screen and (max-width:639px){.popup-load-files-list{padding:0 0 8px 8px}}.popup-load-files-item{width:calc(16.6667% - 15px);max-width:95px;margin-right:15px;display:flex;justify-content:center;align-items:center;flex-direction:column;margin-top:15px}@media only screen and (max-width:639px){.popup-load-files-item{width:calc(16.6667% - 8px);margin-right:8px;margin-top:8px}}.popup-load-files-item-helper{position:relative;width:100%;height:0;padding-bottom:100%}.popup-load-files-item-helper .popup-load-files-item-img{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;border-radius:8px;background:#c4c4c4}.popup-load-files-item-helper .popup-load-files-item-img canvas{max-width:calc(100% - 2px);max-height:calc(100% - 2px)}.popup-load-files-item--loading .popup-load-files-item-img{background:#eee}.popup-load-files-item--loading .popup-load-files-item-name{opacity:.6}.popup-load-files-footer{position:relative;display:flex;justify-content:center;align-items:center;padding:0 17px 17px;flex-direction:column}@media only screen and (max-width:639px){.popup-load-files-footer{padding:0 8px 17px}}.popup-load-files-footer-pagination{display:flex;align-items:center;margin-bottom:16px}.popup-load-files-footer-pagination .popup-load-files-nav-wrap{margin:0 30px}@media only screen and (max-width:479px){.popup-load-files-footer-pagination .popup-load-files-nav-wrap{margin:0 15px}}.popup-load-files-footer-pagination .popup-load-files-nav-number{position:relative;display:inline-block;padding:5px;font-size:14px}.popup-load-files-footer-pagination .popup-load-files-nav-number:after{content:"";position:absolute;top:100%;left:50%;display:none;width:5px;height:5px;border-radius:50%;background:#000;transform:translateX(-40%)}.popup-load-files-footer-pagination .popup-load-files-nav-number.active:after{display:block}.popup-load-files-footer-pagination .popup-load-files-btn-nav{display:flex;align-items:center;min-width:75px;border:none;outline:none;background:none}@media only screen and (max-width:479px){.popup-load-files-footer-pagination .popup-load-files-btn-nav{min-width:68px}}.popup-load-files-footer-pagination .popup-load-files-btn-nav span{font-size:14px}@media only screen and (max-width:479px){.popup-load-files-footer-pagination .popup-load-files-btn-nav span{font-size:12px}}.popup-load-files-footer-pagination .popup-load-files-nav-icon-next,.popup-load-files-footer-pagination .popup-load-files-nav-icon-prev{width:20px;height:14px;background-repeat:no-repeat;background-size:contain;background-position:50%}@media only screen and (max-width:479px){.popup-load-files-footer-pagination .popup-load-files-nav-icon-next,.popup-load-files-footer-pagination .popup-load-files-nav-icon-prev{width:12px;height:8px}}.popup-load-files-footer-pagination .popup-load-files-nav-icon-next{margin-left:6px;background-image:url('+F+");transform:rotate(-180deg)}.popup-load-files-footer-pagination .popup-load-files-nav-icon-prev{margin-right:6px;background-image:url("+F+")}.popup-load-files-footer-pagination button:disabled,.popup-load-files-footer-pagination button[disabled]{opacity:.5}.popup-load-files-footer-buttons{display:flex;justify-content:space-between;width:100%}.popup-load-files-item-name{width:100%}.popup-load-files-item-name p{font-size:12px;width:inherit;margin-bottom:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}@media only screen and (max-width:639px){.popup-load-files-item-name p{font-size:10px}}.popup-load-files-item-cancel,.popup-load-files-item-tick{position:absolute;top:3px;left:3px;width:16px;height:16px;background:#fff;border:1px solid rgba(0,0,0,.5)}.popup-load-files-item-cancel{border-radius:50%}.popup-load-files-item-cancel .popup-load-files-tick-icon{display:flex;align-items:center;justify-content:center;width:15px;height:15px}.popup-load-files-item-tick{border-radius:4px}.popup-load-files-item-tick.active .popup-load-files-tick-icon,.popup-load-files-item-tick img{display:block}.popup-load-files-search:focus,.popup-load-files-search:hover{box-shadow:0 4px 10px rgba(0,0,0,.15)}.popup-load-files-tick-icon{position:absolute;top:50%;left:50%;display:none;transform:translate(-50%,-50%)}.whiteboard_video_el{z-index:20}.popup-load-files-wrap{position:relative;background:#fff}.popup-load-files-drop-wrap{position:absolute;top:0;left:0;height:100%!important;width:100%;background:none}.popup-load-files-drop-wrap.active{background-color:hsla(0,0%,100%,.9);z-index:999999}.popup-load-files-drop-wrap .drop-area--wrapper{width:100%;max-width:100%;height:100%}.popup-load-files-drop-wrap .drop-area--wrapper img{max-width:65%}.popup-load-files.student .popup-load-files-header,.popup-load-files.student .popup-load-files-header-selected-files{background:var(--v-studentColor-lighten1)}.popup-load-files.student .popup-load-files-label-upload{border:1px solid var(--v-studentColor-base)}.drop-area--wrapper{position:relative;display:flex;align-items:center;justify-content:center;width:100%;max-width:640px;height:65%;margin:auto;pointer-events:none}.drop-area--wrapper__upload-meta{display:flex;justify-content:space-between;margin-top:5px}.drop-area--wrapper__dropbox-img{width:100%;height:auto}.image-wrap-classroom{background:#fff;box-shadow:0 2px 30px rgba(0,0,0,.25)}.image-classroom-description,.video-item-description{display:flex;justify-content:space-between;align-items:center;width:100%;background:#5e5e5e;color:#fff}.image-classroom-cross,.video-item-cross{padding:0 10px;font-size:20px}.video-item-name{width:100%;padding:10px}#player,.image-classroom-item-icon{width:100%;height:100%}#video-item-wrap{position:absolute;left:0;top:0;bottom:0;right:0}.plyr__video-embed iframe{top:-50%;height:200%}.plyr--video.plyr--menu-open{overflow:hidden}.plyr__video-wrapper{position:relative}.plyr__control,.plyr__controls__item,.plyr__controls__item span,.plyr__progress,.plyr__progress input,.plyr__progress svg,.plyr__volume input{cursor:pointer!important}.canvas-video{height:100%;width:100%}.drag-move{cursor:url("+H+") 32 24,auto!important}.note-modal .modal-dialog{width:100%!important;max-width:1100px!important}.sn-checkbox-open-in-new-window,.sn-checkbox-use-protocol{display:none!important}body.teacher-cursor-pointer,body.teacher-cursor-pointer *{cursor:url("+R+") 0 0,auto}body.student-cursor-pointer,body.student-cursor-pointer *{cursor:url("+B+") 0 0,auto}body.teacher-cursor-pointer .container-header-title:hover,body.teacher-cursor-pointer .panel-heading:hover,body.teacher-cursor-pointer .tox-editor-header:hover,body.teacher-grabber-hover-cursor-pointer,body.teacher-grabber-hover-cursor-pointer *,body.teacher-role .cursor-before-grab:hover,body.teacher-role .cursor-before-grab:hover *{cursor:url("+N+") 21 16,auto}body.student-cursor-pointer:not(.room-is-disabled) .container-header-title:hover,body.student-cursor-pointer:not(.room-is-disabled) .panel-heading:hover,body.student-cursor-pointer:not(.room-is-disabled) .tox-editor-header:hover,body.student-grabber-hover-cursor-pointer,body.student-grabber-hover-cursor-pointer *,body.student-role:not(.room-is-disabled) .cursor-before-grab:hover,body.student-role:not(.room-is-disabled) .cursor-before-grab:hover *{cursor:url("+W+") 21 16,auto}body.student-cursor-pointer.dragging *{cursor:url("+Y+") 21 16,auto!important}body.teacher-cursor-pointer.dragging *{cursor:url("+H+") 21 16,auto!important}body.teacher-eraser-cursor,body.teacher-eraser-cursor *{cursor:url("+U+") 10 20,auto}body.teacher-pencil-cursor,body.teacher-pencil-cursor *{cursor:url("+X+") 0 22,auto}body.student-eraser-cursor,body.student-eraser-cursor *{cursor:url("+V+") 10 20,auto}body.student-pencil-cursor,body.student-pencil-cursor *{cursor:url("+K+") 0 22,auto}.summernote-teacher .note-editable:hover,.summernote-teacher .note-editable:hover *{cursor:url("+Z+") 32 32,auto}.summernote-student .note-editable:hover,.summernote-student .note-editable:hover *{cursor:url("+J+") 32 32,auto}.summernote-teacher .note-popover:hover,.summernote-teacher a:hover,.summernote-teacher a:hover *,.teacher .plyr__controls__item:hover{cursor:url("+G+") 32 32,auto}.student .plyr__controls__item:hover,.summernote-student .note-popover:hover,.summernote-student a:hover,.summernote-student a:hover *{cursor:url("+Q+") 32 32,auto}.vdr.teacher .handle-ml,.vdr.teacher .handle-mr,body.teacher-cursor-pointer.handle-ml *,body.teacher-cursor-pointer.handle-mr *{cursor:url("+tt+") 25 19,auto!important}.vdr.teacher .handle-bm,.vdr.teacher .handle-tm,body.teacher-cursor-pointer.handle-bm *,body.teacher-cursor-pointer.handle-tm *{cursor:url("+et+") 19 25,auto!important}.vdr.teacher .handle-br,.vdr.teacher .handle-tl,body.teacher-cursor-pointer.handle-br *,body.teacher-cursor-pointer.handle-tl *{cursor:url("+ot+") 18 16,auto!important}.vdr.teacher .handle-bl,.vdr.teacher .handle-tr,body.teacher-cursor-pointer.handle-bl *,body.teacher-cursor-pointer.handle-tr *{cursor:url("+st+") 16 18,auto!important}.vdr.student .handle-ml,.vdr.student .handle-mr,body.student-cursor-pointer.handle-ml *,body.student-cursor-pointer.handle-mr *{cursor:url("+it+") 25 19,auto!important}.vdr.student .handle-bm,.vdr.student .handle-tm,body.student-cursor-pointer.handle-bm *,body.student-cursor-pointer.handle-tm *{cursor:url("+nt+") 19 25,auto!important}.vdr.student .handle-br,.vdr.student .handle-tl,body.student-cursor-pointer.handle-br *,body.student-cursor-pointer.handle-tl *{cursor:url("+at+") 18 16,auto!important}.vdr.student .handle-bl,.vdr.student .handle-tr,body.student-cursor-pointer.handle-bl *,body.student-cursor-pointer.handle-tr *{cursor:url("+lt+") 16 18,auto!important}body:not(.is-touch-device) .toolbar-button-item:active+.hover-btn-info,body:not(.is-touch-device) .toolbar-button-item:hover+.hover-btn-info{display:block}",""]),t.exports=j},2065:function(t,e,o){"use strict";o(1859)},2066:function(t,e,o){var r=o(18)(!1);r.push([t.i,".classroom-timer[data-v-49b4d683],.zoom-percent[data-v-49b4d683]{position:fixed;color:#80b723;line-height:.8;z-index:99999}.classroom-timer[data-v-49b4d683]{font-size:16px}.zoom-percent[data-v-49b4d683]{font-weight:700;font-size:18px;transition:opacity .5s;opacity:0;top:15px;right:15px}.zoom-percent--show[data-v-49b4d683]{opacity:1}@media (max-width:600px){.zoom-percent[data-v-49b4d683]{top:5px;right:5px;font-size:16px}.classroom-timer[data-v-49b4d683]{font-size:14px}}",""]),t.exports=r},2228:function(t,e,o){"use strict";o.r(e);var r=o(10),n=(o(62),o(20),o(80),o(71),o(9),o(35),o(81),o(63),o(23),o(126),o(31),o(24),o(859)),l=o(1936),d=o(1937),c=o(1938),h=o(1912),m=o(1939),f=o(1940),v=o(1941),x=o(1942),y=o(1943),w=o(1944),k=o(1945),C=o(1946),z=o(1555),_=o(149),O=o(695),S=o(1855),$=o(266),E=o(1428),P={name:"Classroom",components:{DropFileArea:l.default,Viewport:d.default,OtherCursor:c.default,TinymceVue:h.default,VideoItem:m.default,AudioItem:f.default,PdfItem:v.default,ImageItem:x.default,Whereby:y.default,VideoInput:w.default,Library:k.default,Toolbar:C.default,Konva:z.default,LDialog:_.default},mixins:[E.a],layout:"classroomLayout",asyncData:function(t){return Object(r.a)(regeneratorRuntime.mark((function e(){var o,r,n,l;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o=t.store,r=t.route,n=t.redirect,l=r.params.id,e.next=4,o.dispatch("classroom/getItem",l).then((function(data){o.commit("classroom/SET_LESSON_ID",l),o.commit("classroom/SET_TEACHER_ID",data.teacherId.toString()),o.commit("classroom/SET_STUDENT_ID",data.studentId.toString())})).catch((function(t){n("/user/lessons")}));case 4:return e.abrupt("return",{lessonId:l});case 5:case"end":return e.stop()}}),e)})))()},data:function(){return{isDevice:!1,viewportWidth:0,viewportHeight:0,startScale:1,mouseClickPosition:{x:0,y:0},evCache:[],prevDiff:-1,pointerMoveTimeout:null,dragTimeout:null,mouseMoveTimeout:null,screenResizeTimeout:null,touchStatus:null,otherCursorPosition:{x:0,y:0},timer:null,keysPressed:{},intervalId:null,isFinishedAllowed:!1}},computed:{classroom:function(){return this.$store.state.classroom.item},userId:function(){return this.$store.getters["classroom/userId"]},otherUserRole:function(){return this.$store.getters["classroom/otherUserRole"]},studentId:function(){return this.$store.state.classroom.studentId},userName:function(){return this.$store.getters["classroom/userName"]},lessonStartTime:function(){return this.classroom.startDate},lessonDuration:function(){return this.classroom.length},isCanvasOversizeX:function(){return $.n>this.viewportWidth},isScaledCanvasOversizeX:function(){return $.n*this.zoomIndex>this.viewportWidth},isCanvasOversizeY:function(){return $.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return $.k*this.zoomIndex>this.viewportHeight},shapeAsset:function(){return this.$store.state.classroom.assets.find((function(t){return"shape"===t.asset.type}))},editorAsset:function(){return this.$store.state.classroom.assets.find((function(t){return"editor"===t.asset.type}))},wherebyAsset:function(){return this.$store.state.classroom.assets.find((function(t){return"whereby"===t.asset.type}))},imageAssets:function(){return this.$store.state.classroom.assets.filter((function(t){return"image"===t.asset.type}))},pdfAssets:function(){return this.$store.state.classroom.assets.filter((function(t){return"pdf"===t.asset.type}))},videoAssets:function(){return this.$store.state.classroom.assets.filter((function(t){return"video"===t.asset.type}))},audioAssets:function(){return this.$store.getters["classroom/audioAssets"]},lockAsset:function(){return this.$store.state.classroom.assets.find((function(t){return"lock"===t.asset.type}))},screenShareAsset:function(){return this.$store.state.classroom.assets.find((function(t){return"screenShare"===t.asset.type}))},isLocked:function(){return this.$store.getters["classroom/isLocked"]},isVideoInputOpened:function(){return this.$store.state.classroom.isVideoInputOpened},isLibraryOpened:function(){return this.$store.state.classroom.isLibraryOpened},isDragging:function(){return this.$store.state.classroom.isDragging},userParams:function(){return this.$store.getters["classroom/userParams"]},zoomAsset:function(){return this.$store.getters["classroom/zoomAsset"]},zoomOtherAsset:function(){return this.$store.getters["classroom/otherZoomAsset"]},zoomIndex:function(){var t,e,o;return null!==(t=null===(e=this.zoomAsset)||void 0===e||null===(o=e.asset)||void 0===o?void 0:o.zoomIndex)&&void 0!==t?t:1},zoomPercent:function(){return Math.round(100*this.zoomIndex)},minSizeX:function(){return $.l},minSizeY:function(){return $.m},maxSizeXPoint:function(){return $.n+$.l},maxSizeYPoint:function(){return $.k+$.m},maxSizeX:function(){return this.isScaledCanvasOversizeX?this.maxSizeXPoint-window.innerWidth/this.zoomIndex:0},maxSizeY:function(){return this.isScaledCanvasOversizeY?this.maxSizeYPoint-window.innerHeight/this.zoomIndex:0},minZoom:function(){return this.isCanvasOversizeX&&this.isCanvasOversizeY?Math.max(window.innerWidth/(this.maxSizeXPoint-this.minSizeX),window.innerHeight/(this.maxSizeYPoint-this.minSizeY)):window.innerWidth/this.maxSizeXPoint},maxZoom:function(){return 2},lessonEndTime:function(){return this.$dayjs(this.lessonStartTime).add(this.lessonDuration,"minute")},userTz:function(){return this.classroom.userTimezone},isScrollMainComponent:function(){return this.$store.state.classroom.isScrollMainComponent},timeRemainingStyle:function(){return{bottom:this.isScaledCanvasOversizeY?"15px":"".concat(this.viewportHeight-$.k*this.zoomIndex+15,"px"),right:this.isScaledCanvasOversizeX?"75px":"".concat(this.viewportWidth-$.n*this.zoomIndex+75,"px")}},isUserInteracted:function(){return this.$store.state.classroom.isUserInteracted}},watch:{"userParams.cursor":function(cursor){this.toggleBodyClass("addClass",cursor)},isLocked:function(t,e){if("student"===this.role){var o=document.body,r="room-is-disabled";t?(this.setTool("pointer","cursor-pointer"),o.classList.add(r)):o.className=o.className.replace(r,"")}}},mounted:function(){var t=this;return Object(r.a)(regeneratorRuntime.mark((function e(){var o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.isDevice=Object(O.a)(),t.viewportWidth=window.innerWidth,t.viewportHeight=window.innerHeight,e.next=5,t.$store.dispatch("classroom/getAssets",t.lessonId);case 5:if(t.intervalId=setInterval((function(){var e=t.$dayjs().tz(t.userTz).format(),o=t.$dayjs(t.lessonEndTime).diff(e,"second");if(o>0)if(60*t.lessonDuration>o){var r=Math.abs(Math.floor(o/60)).toString();r=r.length<2?"0".concat(r):r;var n=Math.abs(o%60).toString();n=n.length<2?"0".concat(n):n,t.timer="".concat(r,":").concat(n),t.isFinishedAllowed||(t.isFinishedAllowed=!0)}else t.timer="".concat(t.lessonDuration,":00");else t.timer="0:00",t.isFinishedAllowed||(t.isFinishedAllowed=!0)}),1e3),t.updateScreenSize(),t.toggleBodyClass("addClass","role"),t.toggleBodyClass("addClass",null===(o=t.userParams)||void 0===o?void 0:o.cursor),t.editorAsset){e.next=12;break}return e.next=12,t.$store.dispatch("classroom/createAsset",{type:"editor"});case 12:if(t.shapeAsset){e.next=15;break}return e.next=15,t.$store.dispatch("classroom/createAsset",{type:"shape",shapes:[]});case 15:if(t.lockAsset){e.next=18;break}return e.next=18,t.$store.dispatch("classroom/createAsset",{type:"lock",isLocked:!1});case 18:if(t.wherebyAsset){e.next=21;break}return e.next=21,t.$store.dispatch("classroom/createAsset",{type:"whereby",width:400,height:300,settings:{teacher:{isMuted:!1,isVideoEnabled:!0},student:{isMuted:!1,isVideoEnabled:!0}}});case 21:if(t.screenShareAsset){e.next=24;break}return e.next=24,t.$store.dispatch("classroom/createAsset",{type:"screenShare",top:100,left:t.viewportWidth/2-200,width:400,height:300});case 24:if(t.zoomAsset){e.next=29;break}return e.next=27,t.$store.dispatch("classroom/createAsset",{type:"zoom",user_id:t.userId,zoomIndex:t.minZoom>1?t.minZoom:1,x:0,y:0,username:t.userName,screen:{width:Math.min(t.viewportWidth,$.n),height:Math.min(t.viewportHeight,$.k)}});case 27:e.next=30;break;case 29:t.updateScreenSize();case 30:t.$nextTick((function(){t.getJoinedUsers(),window.addEventListener("resize",t.updateViewportSizes),window.addEventListener("keydown",t.keyZoom),window.addEventListener("keyup",t.keyZoomUp);var e=document.getElementById("main-component"),o=document.getElementById("konva");o&&(o.addEventListener("touchmove",t.touchMove,!1),o.addEventListener("touchend",t.touchUp,!1)),e&&(["pointerdown","pointerup","pointerout","pointerleave"].forEach((function(o){return e.addEventListener(o,t.pointerUpHandler,!1)})),e.addEventListener("pointerdown",t.pointerDownHandler,!1),e.addEventListener("pointermove",t.pointerMoveHandler,!1))}));case 31:case"end":return e.stop()}}),e)})))()},beforeDestroy:function(){var t=this;window.removeEventListener("resize",this.updateViewportSizes),window.removeEventListener("keydown",this.keyZoom),window.removeEventListener("keyup",this.keyZoomUp),this.intervalId&&window.clearInterval(this.intervalId);var e=document.getElementById("main-component"),o=document.getElementById("konva");o&&(o.removeEventListener("touchmove",this.touchMove,!1),o.removeEventListener("touchend",this.touchUp,!1)),e&&(["pointerdown","pointerup","pointerout","pointerleave"].forEach((function(o){return e.removeEventListener(o,t.pointerUpHandler,!1)})),e.removeEventListener("pointerdown",this.pointerDownHandler,!1),e.removeEventListener("pointermove",this.pointerMoveHandler,!1))},methods:{pointerDownHandler:function(t){if(this.userInteracted(),this.evCache.push(t),this.evCache.length<2){var e=t.offsetX,o=t.offsetY;this.mouseClickPosition.x=e+this.zoomAsset.asset.x,this.mouseClickPosition.y=o+this.zoomAsset.asset.y}var r,n,l,d;2===this.evCache.length&&(this.$store.commit("classroom/setCursorNameBeforeChange",(null===(r=this.$store.state.classroom)||void 0===r||null===(n=r.userParams)||void 0===n?void 0:n.cursor)||"cursor-pointer"),this.$store.commit("classroom/setToolNameBeforeChange",(null===(l=this.$store.state.classroom)||void 0===l||null===(d=l.userParams)||void 0===d?void 0:d.tool)||"pointer"),this.setTool("pointer","cursor-pointer"))},pointerMoveHandler:function(t){var e=this;this.pointerMoveTimeout||(this.pointerMoveTimeout=setTimeout((function(){e.pointerMoveTimeout=null;for(var i=0;i<e.evCache.length;i++)if(t.pointerId===e.evCache[i].pointerId){e.evCache[i]=t;break}if(2===e.evCache.length){e.touchStatus="zooming";var o=e.zoomIndex,r=Object(O.b)()?.005:.025,n=Math.abs(e.evCache[0].clientX-e.evCache[1].clientX),l=Math.abs(e.evCache[0].clientY-e.evCache[1].clientY),d=n>l?n:l;if(e.prevDiff>0){if(d>e.prevDiff){var c=Math.min(Math.max(e.minZoom,o+r),e.maxZoom);e.zoomChange(o,c)}if(d<e.prevDiff){var h=Math.min(Math.max(e.minZoom,o-r),e.maxZoom);e.zoomChange(o,h)}}e.prevDiff=d}}),100))},pointerUpHandler:function(t){2===this.evCache.length&&(this.zoomStop(),this.setTool(this.$store.state.classroom.toolNameBeforeChange,this.$store.state.classroom.cursorNameBeforeChange)),this.removeEvent(t),this.evCache.length<2&&(this.prevDiff=-1),0===this.evCache.length&&(this.touchStatus=null)},removeEvent:function(t){for(var i=0;i<this.evCache.length;i++)if(this.evCache[i].pointerId===t.pointerId){this.evCache.splice(i,1);break}},getEventDeltaX:function(t){return t.deltaMode===WheelEvent.DOM_DELTA_PIXEL?t.deltaX/this.zoomIndex:10*t.deltaX/this.zoomIndex},getEventDeltaY:function(t){return t.deltaMode===WheelEvent.DOM_DELTA_PIXEL?t.deltaY/this.zoomIndex:10*t.deltaY/this.zoomIndex},getEventDeltaZoom:function(t){var e=t.deltaY;return Number(e)===e&&e%1!=0&&(e=t.deltaY>=0?Math.ceil(t.deltaY+7):Math.floor(t.deltaY-7)),t.deltaMode===WheelEvent.DOM_DELTA_PIXEL?-.0025*e:-.01*e},updateViewportSizes:function(){this.viewportWidth=window.innerWidth,this.viewportHeight=window.innerHeight,this.updateScreenSize()},updateScreenSize:function(){var t=this;this.zoomAsset&&!this.screenResizeTimeout&&(this.screenResizeTimeout=setTimeout((function(){t.screenResizeTimeout=null;var data={id:t.zoomAsset.id,lessonId:t.zoomAsset.lessonId,asset:{username:t.userName,screen:{width:Math.min(t.viewportWidth,$.n),height:Math.min(t.viewportHeight,$.k)}}};t.$store.commit("classroom/moveAsset",data),t.$store.dispatch("classroom/moveAsset",data)}),100))},keyZoomUp:function(t){this.$store.commit("classroom/SET_IS_CTRL_KEY_DOWN",!1),delete this.keysPressed[t.keyCode]},keyZoom:function(t){if(this.$store.commit("classroom/SET_IS_CTRL_KEY_DOWN",t.ctrlKey),this.keysPressed[t.keyCode]=!0,this.keysPressed[17]&&(this.keysPressed[187]||this.keysPressed[189]||this.keysPressed[107]||this.keysPressed[109])){t.preventDefault();var e=this.zoomIndex,o=[107,187].includes(t.keyCode)?Math.min(Math.max(this.minZoom,e+.1),this.maxZoom):Math.min(Math.max(this.minZoom,e-.1),this.maxZoom),r=window.innerWidth*(o-e)/o/e/2,n=window.innerHeight*(o-e)/o/e/2,l={zoomIndex:o,x:$.n*o>this.viewportWidth?Math.min(Math.max(this.minSizeX,this.zoomAsset.asset.x+r),this.maxSizeXPoint-window.innerWidth/o):0,y:$.k*o>this.viewportHeight?Math.min(Math.max(this.minSizeY,this.zoomAsset.asset.y+n),this.maxSizeYPoint-window.innerHeight/o):0};this.$store.commit("classroom/moveAsset",{id:this.zoomAsset.id,asset:l}),this.$store.dispatch("classroom/moveAsset",{id:this.zoomAsset.id,lessonId:this.zoomAsset.lessonId,asset:l})}},zoomChange:function(t,e){var o=window.innerWidth*(e-t)/e/t/2,r=window.innerHeight*(e-t)/e/t/2;this.$store.commit("classroom/moveAsset",{id:this.zoomAsset.id,asset:{zoomIndex:e,x:$.n*e>this.viewportWidth?Math.min(Math.max(this.minSizeX,this.zoomAsset.asset.x+o),this.maxSizeXPoint-window.innerWidth/e):0,y:$.k*e>this.viewportHeight?Math.min(Math.max(this.minSizeY,this.zoomAsset.asset.y+r),this.maxSizeYPoint-window.innerHeight/e):0}}),this.updateOtherCursor()},zoomStop:function(){this.$store.dispatch("classroom/moveAsset",{id:this.zoomAsset.id,lessonId:this.zoomAsset.lessonId,asset:{zoomIndex:this.zoomIndex,x:this.zoomAsset.asset.x,y:this.zoomAsset.asset.y}})},gestureStart:function(){this.startScale=this.zoomIndex},gestureChange:function(t){var e=this.zoomIndex,o=this.startScale*t.scale;o=Math.min(Math.max(this.minZoom,o),this.maxZoom);var r=t.clientX*(o-e)/o/e,n=t.clientY*(o-e)/o/e;this.$store.commit("classroom/moveAsset",{id:this.zoomAsset.id,asset:{zoomIndex:o,x:$.n*o>this.viewportWidth?Math.min(Math.max(this.minSizeX,this.zoomAsset.asset.x+r),this.maxSizeXPoint-window.innerWidth/o):0,y:$.k*o>this.viewportHeight?Math.min(Math.max(this.minSizeY,this.zoomAsset.asset.y+n),this.maxSizeYPoint-window.innerHeight/o):0}})},gestureEnd:function(){this.$store.dispatch("classroom/moveAsset",{id:this.zoomAsset.id,lessonId:this.zoomAsset.lessonId,asset:{zoomIndex:this.zoomIndex,x:this.zoomAsset.asset.x,y:this.zoomAsset.asset.y}})},onwheel:function(t){if(t.ctrlKey){t.preventDefault();var e=this.zoomIndex,o=e+this.getEventDeltaZoom(t);o=Math.min(Math.max(this.minZoom,o),this.maxZoom);var r=t.clientX*(o-e)/o/e,n=t.clientY*(o-e)/o/e;this.$store.commit("classroom/moveAsset",{id:this.zoomAsset.id,asset:{zoomIndex:o,x:$.n*o>this.viewportWidth?Math.min(Math.max(this.minSizeX,this.zoomAsset.asset.x+r),this.maxSizeXPoint-window.innerWidth/o):0,y:$.k*o>this.viewportHeight?Math.min(Math.max(this.minSizeY,this.zoomAsset.asset.y+n),this.maxSizeYPoint-window.innerHeight/o):0}}),this.onwheelStop(t)}else this.isScrollMainComponent&&(t.preventDefault(),this.$store.commit("classroom/moveAsset",{id:this.zoomAsset.id,asset:{x:Math.min(Math.max(this.minSizeX,this.zoomAsset.asset.x+this.getEventDeltaX(t)),this.maxSizeX),y:Math.min(Math.max(this.minSizeY,this.zoomAsset.asset.y+this.getEventDeltaY(t)),this.maxSizeY)}}),this.onwheelStop(t));this.updateOtherCursor()},onwheelStop:Object(n.debounce)((function(t){if(t.ctrlKey){var e=this.zoomIndex,o=e+this.getEventDeltaZoom(t);o=Math.min(Math.max(this.minZoom,o),this.maxZoom);var r=t.clientX*(o-e)/o/e,n=t.clientY*(o-e)/o/e;this.$store.dispatch("classroom/moveAsset",{id:this.zoomAsset.id,lessonId:this.zoomAsset.lessonId,asset:{zoomIndex:o,x:$.n*o>this.viewportWidth?Math.min(Math.max(this.minSizeX,this.zoomAsset.asset.x+r),this.maxSizeXPoint-window.innerWidth/o):0,y:$.k*o>this.viewportHeight?Math.min(Math.max(this.minSizeY,this.zoomAsset.asset.y+n),this.maxSizeYPoint-window.innerHeight/o):0}})}else this.$store.dispatch("classroom/moveAsset",{id:this.zoomAsset.id,lessonId:this.zoomAsset.lessonId,asset:{y:Math.min(Math.max(this.minSizeY,this.zoomAsset.asset.y+this.getEventDeltaY(t)),this.maxSizeY)}})}),500),konvaMouseUpHandler:function(){var t=document.body;t.className=t.className.replace("dragging","")},touchMove:function(t){if(t.preventDefault(),this.isDevice&&"zooming"!==this.touchStatus&&this.userParams.tool===$.f&&1===this.evCache.length){var e=t.targetTouches[0].clientX,o=t.targetTouches[0].clientY;this.$store.commit("classroom/moveAsset",{id:this.zoomAsset.id,asset:{x:Math.min(Math.max(this.minSizeX,this.mouseClickPosition.x-e),this.maxSizeX),y:Math.min(Math.max(this.minSizeY,this.mouseClickPosition.y-o),this.maxSizeY)}}),this.updateOtherCursor()}},touchUp:function(){"zooming"!==this.touchStatus&&this.userParams.tool===$.f&&(this.mouseClickPosition.x=0,this.mouseClickPosition.y=0,this.$store.dispatch("classroom/moveAsset",{id:this.zoomAsset.id,lessonId:this.zoomAsset.lessonId,asset:{zoomIndex:this.zoomIndex,x:this.zoomAsset.asset.x,y:this.zoomAsset.asset.y}}))},konvaMouseDownHandler:function(t){this.userParams.tool===$.f&&(this.mouseClickPosition.x=t.x+this.zoomAsset.asset.x,this.mouseClickPosition.y=t.y+this.zoomAsset.asset.y,Object(S.a)(document.documentElement,"mousemove",this.drag),document.body.classList.add("dragging"))},onUp:function(){this.userParams.tool===$.f&&(this.mouseClickPosition.x=0,this.mouseClickPosition.y=0,Object(S.d)(document.documentElement,"mousemove",this.drag),this.$store.dispatch("classroom/moveAsset",{id:this.zoomAsset.id,lessonId:this.zoomAsset.lessonId,asset:{zoomIndex:this.zoomIndex,x:this.zoomAsset.asset.x,y:this.zoomAsset.asset.y}}))},drag:function(t){var e=this;this.dragTimeout||(this.dragTimeout=setTimeout((function(){e.dragTimeout=null,e.$store.commit("classroom/moveAsset",{id:e.zoomAsset.id,asset:{x:Math.min(Math.max(e.minSizeX,e.mouseClickPosition.x-t.x),e.maxSizeX),y:Math.min(Math.max(e.minSizeY,e.mouseClickPosition.y-t.y),e.maxSizeY)}}),e.updateOtherCursor()}),100))},mouseMoved:function(t){var e=this;this.mouseMoveTimeout||(this.mouseMoveTimeout=setTimeout((function(){e.mouseMoveTimeout=null,e.$socket.emit("cursor-moved",{username:e.userName,coords:{x:t.clientX/e.zoomIndex+e.zoomAsset.asset.x,y:t.clientY/e.zoomIndex+e.zoomAsset.asset.y},lessonId:e.lessonId})}),100))},toggleBodyClass:function(t,e){var o=document.body;"addClass"===t?o.classList.add("".concat(this.role,"-").concat(e)):o.classList.remove("".concat(this.role,"-").concat(e))},updateOtherCursor:function(){this.$store.commit("classroom/updateOtherCursor",{coords:{x:this.otherCursorPosition.x,y:this.otherCursorPosition.y}})},getJoinedUsers:function(){this.$socket.emit("get-users-joined-classroom")},closeVideoInputDialog:function(){this.$store.commit("classroom/closeVideoInput")},userInteracted:function(){this.isUserInteracted||this.$store.commit("classroom/USER_INTERACTED")}},sockets:{connect:function(){this.getJoinedUsers()},disconnect:function(){this.getJoinedUsers()},reconnect:function(){var t=this;return Object(r.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$store.dispatch("loadingAllow",!1);case 2:return e.next=4,t.$store.dispatch("classroom/getAssets",t.lessonId).finally((function(){return t.$store.dispatch("loadingAllow",!0)}));case 4:t.getJoinedUsers();case 5:case"end":return e.stop()}}),e)})))()},"cursor-moved":function(data){var t,e;null!==(t=data.coords)&&void 0!==t&&t.x&&null!==(e=data.coords)&&void 0!==e&&e.y&&(this.otherCursorPosition.x=data.coords.x,this.otherCursorPosition.y=data.coords.y),this.$store.commit("classroom/updateOtherCursor",data)},"asset-added":function(t){this.$store.commit("classroom/addAssets",[t])},"asset-deleted":function(t){this.$store.commit("classroom/deleteAsset",t)},"asset-moved":function(t){this.$store.commit("classroom/moveAsset",t)},"users-joined-classroom":function(t){this.$store.commit("classroom/setUsersJoinedClassroom",t.joinedUsers)},"user-left-classroom":function(){this.getJoinedUsers()}}},I=(o(2063),o(2065),o(22)),component=Object(I.a)(P,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return t.zoomAsset?o("div",{staticClass:"classroom",attrs:{id:"main-component"},on:{click:t.userInteracted,wheel:t.onwheel,gesturestart:function(e){return e.preventDefault(),t.gestureStart.apply(null,arguments)},gesturechange:function(e){return e.preventDefault(),t.gestureChange.apply(null,arguments)},gestureend:function(e){return e.preventDefault(),t.gestureEnd.apply(null,arguments)},mouseup:t.onUp,mouseleave:t.onUp,mousemove:t.mouseMoved}},[o("drop-file-area",{attrs:{"viewport-width":t.viewportWidth}}),t._v(" "),o("div",{directives:[{name:"show",rawName:"v-show",value:!t.isDragging,expression:"!isDragging"}]},[t.timer?o("div",{staticClass:"classroom-timer",style:t.timeRemainingStyle},[t._v("\n      "+t._s(t.$t("time_remaining"))+": "+t._s(t.timer)+"\n    ")]):t._e(),t._v(" "),o("div",{class:["zoom-percent",{"zoom-percent--show":100!==t.zoomPercent}]},[t._v("\n      "+t._s(t.$t("zoom"))+": "+t._s(t.zoomPercent)+"%\n    ")]),t._v(" "),o("viewport",{attrs:{"zoom-other-asset":t.zoomOtherAsset,"zoom-asset":t.zoomAsset,"viewport-width":t.viewportWidth,"viewport-height":t.viewportHeight}}),t._v(" "),o("other-cursor"),t._v(" "),t.editorAsset?o("tinymce-vue",{attrs:{file:t.editorAsset},on:{"mouse-move":t.mouseMoved,"pointer-down":function(e){return t.pointerDownHandler(e)},"pointer-move":function(e){return t.pointerMoveHandler(e)},"pointer-up":function(e){return t.pointerUpHandler(e)}}}):t._e(),t._v(" "),t._l(t.videoAssets,(function(t){return o("video-item",{key:t.id,attrs:{file:t}})})),t._v(" "),t._l(t.audioAssets,(function(t){return o("audio-item",{key:t.id,attrs:{file:t}})})),t._v(" "),t._l(t.pdfAssets,(function(t){return o("pdf-item",{key:t.id,attrs:{file:t}})})),t._v(" "),t._l(t.imageAssets,(function(t){return o("image-item",{key:t.id,attrs:{file:t}})})),t._v(" "),t.wherebyAsset&&t.screenShareAsset?o("whereby",{attrs:{file:t.wherebyAsset,"screen-share-asset":t.screenShareAsset,"zoom-other-asset":t.zoomOtherAsset}}):t._e(),t._v(" "),o("l-dialog",{attrs:{"custom-class":"video-component","max-width":"500","z-index":"10000000001",dialog:t.isVideoInputOpened},on:{"close-dialog":t.closeVideoInputDialog}},[o("video-input")],1),t._v(" "),t.isLibraryOpened?o("library",{attrs:{"viewport-width":t.viewportWidth}}):t._e(),t._v(" "),o("toolbar",{attrs:{file:t.lockAsset,"student-id":t.studentId,scale:t.zoomIndex,"min-zoom":t.minZoom,"viewport-width":t.viewportWidth,"viewport-height":t.viewportHeight,"is-finished-allowed":t.isFinishedAllowed}}),t._v(" "),o("div",{attrs:{id:"konva"},on:{mousedown:t.konvaMouseDownHandler,mouseup:t.konvaMouseUpHandler}},[t.shapeAsset?o("konva",{attrs:{scale:t.zoomIndex,file:t.shapeAsset,width:t.viewportWidth,height:t.viewportHeight,"is-main-konva":""}}):t._e()],1)],2)],1):t._e()}),[],!1,null,"49b4d683",null);e.default=component.exports;installComponents(component,{LDialog:o(149).default})}}]);