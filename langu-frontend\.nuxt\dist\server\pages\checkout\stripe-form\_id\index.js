exports.ids = [131];
exports.modules = {

/***/ 1491:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/checkout/stripe-form/_id/index.vue?vue&type=template&id=0ffed8f2&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{"min-height":"calc(100vh - 300px)"}},[])}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/checkout/stripe-form/_id/index.vue?vue&type=template&id=0ffed8f2&

// EXTERNAL MODULE: external "@stripe/stripe-js/pure"
var pure_ = __webpack_require__(879);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/checkout/stripe-form/_id/index.vue?vue&type=script&lang=js&
//
//
//
//

/* harmony default export */ var _idvue_type_script_lang_js_ = ({
  name: 'StripeForm',

  async beforeMount() {
    const sessionId = this.$route.params.id;

    if (!this.$stripe) {
      this.$stripe = await Object(pure_["loadStripe"])("pk_test_MQIviMptDCcknTZ25dojL7Z400OHkru9Qw");
    }

    await this.$cookiz.set('thank_you_page_allowed', 1, {
      domain: ".langu.loc",
      path: '/'
    });

    if (this.$stripe) {
      this.$stripe.redirectToCheckout({
        sessionId
      }).then(() => {
        console.log('success');
      }).catch(e => {
        console.log('error', e);
      });
    } else {
      console.log('stripe not found');
    }
  }

});
// CONCATENATED MODULE: ./pages/checkout/stripe-form/_id/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var stripe_form_idvue_type_script_lang_js_ = (_idvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./pages/checkout/stripe-form/_id/index.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  stripe_form_idvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "40d25318"
  
)

/* harmony default export */ var _id = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=index.js.map