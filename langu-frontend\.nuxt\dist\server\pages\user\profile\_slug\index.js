exports.ids = [158];
exports.modules = {

/***/ 1495:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/profile/_slug/index.vue?vue&type=template&id=727c329f&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[])}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/user/profile/_slug/index.vue?vue&type=template&id=727c329f&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/profile/_slug/index.vue?vue&type=script&lang=js&
//
//
//
//
/* harmony default export */ var _slugvue_type_script_lang_js_ = ({
  middleware({
    route,
    redirect
  }) {
    const queryArrLength = Object.keys(route.query).length;
    let url = `/teacher/${route.params.slug}`;
    let i = 0;

    if (queryArrLength > 0) {
      url += '?';

      for (const property in route.query) {
        i += 1;
        url += `${property}=${route.query[property]}`;

        if (i < queryArrLength) {
          url += '&';
        }
      }
    }

    return redirect(url);
  }

});
// CONCATENATED MODULE: ./pages/user/profile/_slug/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var profile_slugvue_type_script_lang_js_ = (_slugvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./pages/user/profile/_slug/index.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  profile_slugvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "320398d2"
  
)

/* harmony default export */ var _slug = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=index.js.map