(window.webpackJsonp=window.webpackJsonp||[]).push([[37],{1626:function(e,t,l){var content=l(1725);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,l(19).default)("03106de9",content,!0,{sourceMap:!1})},1724:function(e,t,l){"use strict";l(1626)},1725:function(e,t,l){var o=l(18)(!1);o.push([e.i,".check-email-title{color:var(--v-dark-base)!important;font-size:20px;font-weight:600}@media only screen and (max-width:639px){.check-email-title{text-align:center;font-size:18px}}.check-email-button{text-align:right}@media only screen and (max-width:639px){.check-email-button{text-align:center}}.check-email-button .v-btn{border-radius:16px!important}",""]),e.exports=o},1825:function(e,t,l){"use strict";l.r(t);l(35),l(81);var o={name:"CheckEmailDialog",components:{LDialog:l(149).default},props:{showCheckEmailDialog:{type:Boolean,required:!0}},methods:{closeCheckEmailDialog:function(){this.$emit("close"),this.$router.replace({path:"/teacher-listing/1"})}}},c=(l(1724),l(22)),n=l(42),r=l.n(n),m=l(1327),component=Object(c.a)(o,(function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("l-dialog",{attrs:{dialog:e.showCheckEmailDialog,"hide-close-button":"","max-width":"418","custom-class":"check-email"}},[l("div",[l("div",{staticClass:"check-email-title"},[e._v("\n      "+e._s(e.$t("almost_there"))+"\n    ")]),e._v(" "),l("div",{staticClass:"check-email-text mt-3"},[e._v("\n      "+e._s(e.$t("please_check_your_email_to_verify_your_email_address_and_create_password"))+"\n    ")]),e._v(" "),l("div",{staticClass:"check-email-button mt-4"},[l("v-btn",{staticClass:"font-weight-medium",attrs:{color:"orange","min-width":"178","x-large":""},on:{click:e.closeCheckEmailDialog}},[e._v("\n        OK\n      ")])],1)])])}),[],!1,null,null,null);t.default=component.exports;r()(component,{LDialog:l(149).default}),r()(component,{VBtn:m.a})}}]);