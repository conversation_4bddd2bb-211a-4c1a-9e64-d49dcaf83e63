{"version": 3, "file": "components/homepage-tutors-section.js", "sources": ["webpack:///./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css?06df", "webpack:///./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css", "webpack:///./components/homepage/TutorsSection.vue?fa67", "webpack:///./components/homepage/TutorsSection.vue?8607", "webpack:///./components/homepage/TutorsSection.vue?5eef", "webpack:///./components/homepage/TutorsSection.vue?a1db", "webpack:///./components/homepage/TutorsSection.vue", "webpack:///./components/homepage/TutorsSection.vue?ae50", "webpack:///./components/homepage/TutorsSection.vue?571e", "webpack:///./mixins/Avatars.vue", "webpack:///./mixins/Avatars.vue?9044", "webpack:///./mixins/Avatars.vue?7fa3"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../css-loader/dist/cjs.js??ref--3-oneOf-1-1!../../postcss-loader/src/index.js??ref--3-oneOf-1-2!./vue-slick-carousel.css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../vue-style-loader/lib/addStylesServer.js\").default(\"20c2c1c7\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".slick-track[data-v-e4caeaf8]{position:relative;top:0;left:0;display:block;transform:translateZ(0)}.slick-track.slick-center[data-v-e4caeaf8]{margin-left:auto;margin-right:auto}.slick-track[data-v-e4caeaf8]:after,.slick-track[data-v-e4caeaf8]:before{display:table;content:\\\"\\\"}.slick-track[data-v-e4caeaf8]:after{clear:both}.slick-loading .slick-track[data-v-e4caeaf8]{visibility:hidden}.slick-slide[data-v-e4caeaf8]{display:none;float:left;height:100%;min-height:1px}[dir=rtl] .slick-slide[data-v-e4caeaf8]{float:right}.slick-slide img[data-v-e4caeaf8]{display:block}.slick-slide.slick-loading img[data-v-e4caeaf8]{display:none}.slick-slide.dragging img[data-v-e4caeaf8]{pointer-events:none}.slick-initialized .slick-slide[data-v-e4caeaf8]{display:block}.slick-loading .slick-slide[data-v-e4caeaf8]{visibility:hidden}.slick-vertical .slick-slide[data-v-e4caeaf8]{display:block;height:auto;border:1px solid transparent}.slick-arrow.slick-hidden[data-v-21137603]{display:none}.slick-slider[data-v-3d1a4f76]{position:relative;display:block;box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;touch-action:pan-y;-webkit-tap-highlight-color:transparent}.slick-list[data-v-3d1a4f76]{position:relative;display:block;overflow:hidden;margin:0;padding:0;transform:translateZ(0)}.slick-list[data-v-3d1a4f76]:focus{outline:none}.slick-list.dragging[data-v-3d1a4f76]{cursor:pointer;cursor:hand}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TutorsSection.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"6e05f7b8\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TutorsSection.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_GET_URL_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/getUrl.js\");\nvar ___CSS_LOADER_URL_IMPORT_0___ = require(\"../../assets/images/check.svg\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".tutors{position:relative;padding:220px 0 132px}@media only screen and (max-width:1439px){.tutors{padding:150px 0 110px}}@media only screen and (max-width:991px){.tutors{padding:150px 0 80px}}@media only screen and (max-width:767px){.tutors{padding:90px 0 80px}}.tutors-decoration--after,.tutors-decoration--before{position:absolute;width:100%}.tutors-decoration--before{top:0;left:0;width:100%;max-width:874px;height:1015px;max-height:1085px}@media only screen and (max-width:1643px){.tutors-decoration--before{top:120px;max-width:690px;height:820px}}@media only screen and (max-width:1439px){.tutors-decoration--before{top:55px;width:calc(100% - 45px);max-width:480px;height:600px}}@media only screen and (max-width:991px){.tutors-decoration--before{top:0;height:400px}}@media only screen and (max-width:767px){.tutors-decoration--before{height:400px}}.tutors-decoration--before .v-image__image{background-position:0}.tutors-decoration--after{right:0;top:145px;max-width:437px;height:100%;max-height:679px}@media only screen and (max-width:1643px){.tutors-decoration--after{top:185px;max-width:305px;max-height:479px}}@media only screen and (max-width:1439px){.tutors-decoration--after{top:240px;max-width:190px;max-height:295px}}@media only screen and (max-width:991px){.tutors-decoration--after{top:auto;bottom:50px}}.tutors-decoration--after .v-image__image{background-position:100%}.tutors-carousel{position:relative;margin-top:96px;z-index:2}@media only screen and (max-width:1643px){.tutors-carousel{margin-top:50px}}@media only screen and (min-width:1440px){.tutors-carousel{margin-left:-48px;max-width:1262px}}@media only screen and (max-width:1439px){.tutors-carousel{width:calc(100% + 20px);padding-bottom:100px}}@media only screen and (max-width:991px){.tutors-carousel{width:calc(100% + 15px)}}.tutors-carousel .slick-slide{padding:0 37px}@media only screen and (max-width:1439px){.tutors-carousel .slick-slide{padding:0 20px 0 0}}@media only screen and (max-width:991px){.tutors-carousel .slick-slide{padding:0 15px 0 0}.tutors-carousel .slick-slide>div{display:flex;justify-content:center}}.tutors-carousel-item{display:inline-flex!important;color:#fff;background:linear-gradient(126.15deg,var(--v-green-base),var(--v-primary-base) 102.93%),#c4c4c4;border-radius:24px}@media only screen and (min-width:1440px){.tutors-carousel-item{max-width:594px}}@media only screen and (max-width:991px){.tutors-carousel-item{position:relative;flex-direction:column;max-width:640px}}.tutors-carousel-item-left{width:238px;padding:12px 32px 18px;border-right:1px solid hsla(0,0%,100%,.1)}@media only screen and (max-width:1439px){.tutors-carousel-item-left{width:215px;padding:20px 24px}}@media only screen and (max-width:991px){.tutors-carousel-item-left{width:100%;padding:20px 24px 6px;border-bottom:1px solid hsla(0,0%,100%,.1);border-right:none}.tutors-carousel-item-left>div{display:flex;justify-content:space-between}}@media only screen and (max-width:479px){.tutors-carousel-item-left{padding:20px 15px 6px}}.tutors-carousel-item-right{position:relative;width:calc(100% - 238px);padding:32px 32px 82px}@media only screen and (max-width:1439px){.tutors-carousel-item-right{padding:20px 24px 80px;width:calc(100% - 215px)}}@media only screen and (max-width:991px){.tutors-carousel-item-right{width:100%}}@media only screen and (max-width:479px){.tutors-carousel-item-right{padding:32px 15px 95px}}.tutors-carousel-item-image{position:relative;width:163px;height:163px;margin:0 auto 4px}@media only screen and (min-width:992px)and (max-width:1439px){.tutors-carousel-item-image{width:140px;height:140px}.tutors-carousel-item-image .v-avatar{width:140px!important;height:140px!important}}@media only screen and (max-width:991px){.tutors-carousel-item-image{margin:0 38px 0 0}}@media only screen and (max-width:479px){.tutors-carousel-item-image{width:90px;height:90px;margin:0 25px 0 0}.tutors-carousel-item-image .v-avatar{width:90px!important;height:90px!important}}.tutors-carousel-item-image .flags{position:absolute;bottom:5px;right:-24px}@media only screen and (max-width:479px){.tutors-carousel-item-image .flags{width:38px;bottom:0;right:-20px}}.tutors-carousel-item-image .flags-item{margin-top:2px;border-radius:8px;overflow:hidden}.tutors-carousel-item-name{font-size:24px;font-weight:700}@media only screen and (max-width:991px){.tutors-carousel-item-name{text-align:right}}@media only screen and (max-width:639px){.tutors-carousel-item-name{font-size:18px}}.tutors-carousel-item-name a{color:#fff!important;text-decoration:none}.tutors-carousel-item-rating{display:flex;align-items:center;margin-top:8px}@media only screen and (max-width:991px){.tutors-carousel-item-rating{flex-direction:column;align-items:flex-end}}.tutors-carousel-item-rating span{display:inline-block;margin-left:8px;font-size:13px;font-weight:700;line-height:.8;color:var(--v-orange-base)}@media only screen and (max-width:991px){.tutors-carousel-item-rating span{display:block;margin:10px 0 0}}.tutors-carousel-item-list{margin-top:12px;padding-left:0!important;list-style-type:none}@media only screen and (max-width:991px){.tutors-carousel-item-list{display:flex;flex-wrap:wrap;margin-top:18px;margin-left:-20px}}.tutors-carousel-item-list>li{position:relative;margin-top:2px;padding-left:19px;font-size:18px;line-height:1.55}@media only screen and (min-width:992px)and (max-width:1439px){.tutors-carousel-item-list>li{margin-top:4px;font-size:16px;line-height:1.2}}@media only screen and (max-width:991px){.tutors-carousel-item-list>li{margin:0 0 10px 24px}}.tutors-carousel-item-list>li:before{content:\\\"\\\";position:absolute;top:7px;left:0;width:11px;height:9px;background-size:contain;background-repeat:no-repeat;background-position:50%;background-image:url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \")}.tutors-carousel-item-text{font-size:18px;line-height:1.33}.tutors-carousel-item-text span{display:block;margin:25px 0 8px;font-weight:400}.tutors-carousel-item-languages{font-weight:300}.tutors-carousel-item-button{position:absolute;width:100%;left:0;bottom:20px}@media only screen and (max-width:479px){.tutors-carousel-item-button{padding:0 24px}}.tutors-carousel-item-button .v-btn{min-width:222px!important}@media only screen and (max-width:1439px){.tutors-carousel-item-button .v-btn{min-width:180px!important}}@media only screen and (max-width:479px){.tutors-carousel-item-button .v-btn{min-width:100%!important;width:100%!important}}.tutors .slick-arrow{position:absolute;top:50%;transform:translateY(-50%)}@media only screen and (max-width:1439px){.tutors .slick-arrow{top:auto;bottom:-100px;transform:translateX(-50%)}}.tutors .slick-prev{left:calc(50vw + 626px)}@media only screen and (max-width:1643px){.tutors .slick-prev{left:auto;right:-82px}}@media only screen and (max-width:1439px){.tutors .slick-prev{left:calc(50% - 65px);right:auto}}@media only screen and (max-width:991px){.tutors .slick-prev{left:calc(50% - 60px)}}.tutors .slick-next{left:calc(50vw + 706px)}@media only screen and (max-width:1643px){.tutors .slick-next{left:auto;right:-162px}}@media only screen and (max-width:1439px){.tutors .slick-next{left:calc(50% + 35px);right:auto}}@media only screen and (max-width:991px){.tutors .slick-next{left:calc(50% + 40px)}}.tutors-button{display:flex;justify-content:center;margin-top:90px}@media only screen and (max-width:1643px){.tutors-button{margin-top:80px}}@media only screen and (max-width:1439px){.tutors-button{margin-top:50px}}.tutors-button .v-btn{min-width:285px!important}@media only screen and (max-width:479px){.tutors-button .v-btn{min-width:100%!important;width:100%!important}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"tutors\"},[_vm._ssrNode(\"<div class=\\\"tutors-decoration--before\\\">\",\"</div>\",[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/decoration-1.svg'),\"contain\":\"\",\"options\":{ rootMargin: '50%' }}})],1),_vm._ssrNode(\" \"),_c('v-container',{staticClass:\"py-0\"},[_c('v-row',[_c('v-col',{staticClass:\"col-12 py-0\"},[_c('div',{staticClass:\"section-head section-head--decorated\"},[_c('h3',{staticClass:\"section-head-title\",staticStyle:{\"color\":\"#262626\",\"-webkit-text-fill-color\":\"#262626\"}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('home_page.tutors_section_title'))+\"\\n          \")]),_vm._v(\" \"),_c('div',{staticClass:\"section-head-subtitle text--gradient\"},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('home_page.tutors_section_subtitle'))+\"\\n          \")])])])],1)],1),_vm._ssrNode(\" \"),_c('v-container',{staticClass:\"py-0\",attrs:{\"fluid\":\"\"}},[_c('v-row',[_c('v-col',{staticClass:\"col-12\"},[_c('div',{staticClass:\"tutors-carousel\"},[_c('client-only',[_c('VueSlickCarousel',_vm._b({},'VueSlickCarousel',_vm.tutorsCarouselSettings,false),_vm._l((_vm.teachers),function(t,idx){return _c('div',{key:idx,ref:\"tutorsCarouselItem\",refInFor:true,staticClass:\"tutors-carousel-item\"},[_c('div',{staticClass:\"tutors-carousel-item-left\"},[_c('div',[_c('nuxt-link',{attrs:{\"to\":t.profileLink}},[_c('div',{staticClass:\"tutors-carousel-item-image\"},[_c('v-avatar',{attrs:{\"width\":\"163\",\"height\":\"163\"}},[_c('v-img',{attrs:{\"src\":_vm.getSrcAvatar(\n                                t.avatarsResized,\n                                'user_thumb_163x163'\n                              ),\"srcset\":_vm.getSrcSetAvatar(\n                                t.avatarsResized,\n                                'user_thumb_163x163',\n                                'user_thumb_326x326'\n                              ),\"options\":{ rootMargin: '50%' }}})],1),_vm._v(\" \"),(t.languagesTaught.length)?_c('div',{staticClass:\"flags\"},_vm._l((t.languagesTaught),function(languageTaught){return _c('div',{key:languageTaught.isoCode,staticClass:\"flags-item\"},[_c('v-img',{attrs:{\"src\":require((\"~/assets/images/flags/\" + (languageTaught.isoCode) + \".svg\")),\"width\":\"52\",\"height\":\"36\",\"contain\":\"\",\"options\":{ rootMargin: '50%' }}})],1)}),0):_vm._e()],1)]),_vm._v(\" \"),_c('div',[_c('div',{staticClass:\"tutors-carousel-item-name text-uppercase\"},[_c('nuxt-link',{attrs:{\"to\":t.profileLink}},[_vm._v(\"\\n                          \"+_vm._s(t.firstName)+\"\\n                        \")])],1),_vm._v(\" \"),_c('div',{staticClass:\"tutors-carousel-item-rating\"},[_c('div',[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/stars.svg'),\"width\":\"68\",\"contain\":\"\",\"options\":{ rootMargin: '50%' }}})],1),_vm._v(\" \"),_c('span',[_vm._v(\"(\"+_vm._s(_vm.$tc('review', t.countFeedbacks))+\")\")])])])],1),_vm._v(\" \"),(t.languagesTaught.length)?_c('ul',{staticClass:\"tutors-carousel-item-list\"},_vm._l((t.languagesTaught),function(lt,index){return _c('li',{key:index},[_vm._v(\"\\n                      \"+_vm._s(lt.name)+\" \"+_vm._s(_vm.$t('teacher'))+\"\\n                    \")])}),0):_vm._e()]),_vm._v(\" \"),_c('div',{staticClass:\"tutors-carousel-item-right\"},[_c('div',{staticClass:\"tutors-carousel-item-text\"},[_vm._v(\"\\n                    “\"+_vm._s(t.description)+\"”\\n                    \"),(t.specialities && t.specialities.length)?[_c('span',[_vm._v(_vm._s(_vm.$t('top_specialities'))+\":\")]),_vm._v(\" \"),_c('div',{staticClass:\"tutors-carousel-item-languages\"},[_vm._v(\"\\n                        \"+_vm._s(_vm._f(\"specialitiesStr\")(t.specialities))+\"\\n                      \")])]:_vm._e()],2),_vm._v(\" \"),(_vm.$vuetify.breakpoint.mdAndUp)?_c('div',{staticClass:\"tutors-carousel-item-button text-center d-none d-md-block\"},[_c('v-btn',{attrs:{\"to\":t.profileLink,\"large\":\"\",\"color\":\"greyDark\"}},[(t.bookLesson.freeTrial)?[_vm._v(\"\\n                        \"+_vm._s(_vm.$t('free_trial'))+\"\\n                      \")]:[_vm._v(\"\\n                        \"+_vm._s(_vm.$t('trial'))+\":  \"+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.getPrice(t.bookLesson.price))+\"\\n                      \")]],2)],1):_vm._e()]),_vm._v(\" \"),(_vm.$vuetify.breakpoint.smAndDown)?_c('div',{staticClass:\"tutors-carousel-item-button text-center d-md-none\"},[_c('v-btn',{attrs:{\"to\":t.profileLink,\"large\":\"\",\"color\":\"greyDark\"}},[(t.bookLesson.freeTrial)?[_vm._v(\"\\n                      \"+_vm._s(_vm.$t('free_trial'))+\"\\n                    \")]:[_vm._v(\"\\n                      \"+_vm._s(_vm.$t('trial'))+\":  \"+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.getPrice(t.bookLesson.price))+\"\\n                    \")]],2)],1):_vm._e()])}),0)],1)],1)])],1)],1),_vm._ssrNode(\" \"),_c('v-container',{staticClass:\"py-0\"},[_c('v-row',[_c('v-col',{staticClass:\"col-12 py-0\"},[_c('div',{staticClass:\"tutors-button\"},[_c('v-btn',{attrs:{\"to\":_vm.localePath({\n                name: 'teacher-listing',\n                params: {\n                  utm_source: 'homepage',\n                  utm_medium: 'teacher-cards',\n                },\n              }),\"large\":\"\",\"color\":\"primary\"}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('meet_all_our_teachers'))+\"\\n          \")])],1)])],1)],1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"tutors-decoration--after\\\">\",\"</div>\",[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/decoration-2.svg'),\"contain\":\"\",\"options\":{ rootMargin: '50%' }}})],1)],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport VueSlickCarousel from 'vue-slick-carousel'\nimport 'vue-slick-carousel/dist/vue-slick-carousel.css'\n\nimport Avatars from '~/mixins/Avatars'\nimport { getPrice } from '~/helpers'\n\nexport default {\n  name: 'TutorsSection',\n  components: {\n    VueSlickCarousel,\n  },\n  filters: {\n    specialitiesStr(arr) {\n      let str = ''\n\n      for (let i = 0; i < 3; i++) {\n        str += arr[i]?.speciality?.name ?? ''\n\n        if (i < 3 - 1) {\n          str += ', '\n        }\n      }\n\n      return str\n    },\n  },\n  mixins: [Avatars],\n  props: {\n    teachers: {\n      type: Array,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      getPrice,\n      tutorsCarouselSettings: {\n        dots: false,\n        focusOnSelect: true,\n        infinite: true,\n        speed: 800,\n        slidesToShow: 2,\n        slidesToScroll: 1,\n        responsive: [\n          {\n            breakpoint: 992,\n            settings: {\n              slidesToShow: 1,\n            },\n          },\n        ],\n      },\n    }\n  },\n  computed: {\n    currentCurrencySymbol() {\n      return this.$store.getters['currency/currentCurrencySymbol']\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TutorsSection.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TutorsSection.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TutorsSection.vue?vue&type=template&id=0135b7d4&\"\nimport script from \"./TutorsSection.vue?vue&type=script&lang=js&\"\nexport * from \"./TutorsSection.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./TutorsSection.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"f3947edc\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VAvatar } from 'vuetify/lib/components/VAvatar';\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VContainer } from 'vuetify/lib/components/VGrid';\nimport { VImg } from 'vuetify/lib/components/VImg';\nimport { VRow } from 'vuetify/lib/components/VGrid';\ninstallComponents(component, {VAvatar,VBtn,VCol,VContainer,VImg,VRow})\n", "\nexport default {\n  methods: {\n    getSrcAvatar(images, property, defaultImage = 'avatar.png') {\n      return images?.[property]\n        ? images[property]\n        : require(`~/assets/images/homepage/${defaultImage}`)\n    },\n    getSrcSetAvatar(images, property1, property2) {\n      return images?.[property1] && images?.[property2]\n        ? `\n            ${images[property1]} 1x,\n            ${images[property2]} 2x,\n          `\n        : ''\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Avatars.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Avatars.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./Avatars.vue?vue&type=script&lang=js&\"\nexport * from \"./Avatars.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"0af9ff4e\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;AAeA;AACA;AACA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AADA;AAFA;AARA;AAFA;AAmBA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAhDA;;ACnMA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AChCA;AACA;AACA;AACA;AAGA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAdA;AADA;;ACDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}