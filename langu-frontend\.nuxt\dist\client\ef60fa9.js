(window.webpackJsonp=window.webpackJsonp||[]).push([[38],{1663:function(e,t,o){var content=o(1778);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,o(19).default)("ceb0bb2a",content,!0,{sourceMap:!1})},1777:function(e,t,o){"use strict";o(1663)},1778:function(e,t,o){var n=o(18)(!1);n.push([e.i,'.plyr--audio .plyr__controls{position:relative;width:100%;color:#fff}.plyr--audio .plyr__controls:before{content:"";position:absolute;top:0;left:1px;width:calc(100% - 2px);height:1px;background-color:rgba(0,0,0,.3)}',""]),e.exports=n},1940:function(e,t,o){"use strict";o.r(t);o(23);var n=o(1690),r=o.n(n),l=o(859),d=o(266),c=o(1389),h=o(1459),m={name:"AudioItem",components:{ClassroomContainer:c.default,ClassroomContainerHeader:h.default},props:{file:{type:Object,required:!0}},data:function(){return{currentTime:0,changedBySocket:!1,player:{config:{title:""}}}},computed:{width:function(){var e,t;return null!==(e=null===(t=this.file.asset)||void 0===t?void 0:t.width)&&void 0!==e?e:0},height:function(){var e,t;return null!==(e=null===(t=this.file.asset)||void 0===t?void 0:t.height)&&void 0!==e?e:0},zoomIndex:function(){var e,t,o;return null!==(e=null===(t=this.$store.getters["classroom/zoomAsset"])||void 0===t||null===(o=t.asset)||void 0===o?void 0:o.zoomIndex)&&void 0!==e?e:1},isSocketConnected:function(){return this.$store.state.socket.isConnected},audioUrl:function(){return"".concat("'http://localhost:3000'").concat(this.file.asset.path)},mimeType:function(){var e="audio/mpeg";return"wav"===Object(l.getFileExtension)(this.file.asset.path)&&(e="audio/wav"),e},isUserInteracted:function(){return this.$store.state.classroom.isUserInteracted}},mounted:function(){var e=this,t={controls:["play","progress","current-time","mute","volume","settings"],hideControls:!1,resetOnEnd:!0,volume:this.file.asset.volume||1,muted:this.file.asset.muted||!1,storage:{enabled:!1},speed:{selected:this.file.asset.speed||1,options:[.5,.75,1,1.25,1.5,1.75,2]},i18n:{speed:this.$t("speed"),normal:this.$t("normal")}};this.player=new r.a("#player-".concat(this.file.id),t),this.player.source={type:"audio",title:this.file.asset.displayName},this.player.on("ready",(function(){e.player.on("play",e.playAudio),e.player.on("pause",e.pauseAudio),e.player.on("ratechange",e.changeSpeed),e.player.on("timeupdate",e.timeupdate),e.player.pause(),e.resize()})),this.player.on("loadeddata",(function(){e.currentTime=e.file.asset.currentTime||0,e.player.currentTime=e.currentTime})),new ResizeObserver(this.resize).observe(this.$refs.childComponent)},beforeDestroy:function(){this.player.destroy()},methods:{resize:function(){if(!this.width){var e={width:this.$refs.childComponent.getBoundingClientRect().width/this.zoomIndex,height:this.$refs.childComponent.getBoundingClientRect().height/this.zoomIndex,originalWidth:d.j};this.$store.commit("classroom/moveAsset",{id:this.file.id,asset:e}),this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.file.lessonId,asset:e})}},playAudio:function(e){this.onPlayerChange({currentTime:e.detail.plyr.media.currentTime,play:!0})},pauseAudio:function(e){this.onPlayerChange({currentTime:e.detail.plyr.media.currentTime,play:!1})},timeupdate:function(e){this.currentTime=this.player.currentTime},changeSpeed:function(e){this.onPlayerChange({speed:e.detail.plyr.config.speed.selected})},onPlayerChange:function(e){if(this.changedBySocket)this.changedBySocket=!1;else{var data={id:this.file.id,lessonId:this.file.lessonId,asset:e};this.isSocketConnected&&this.$socket.emit("audio-updated",data),this.$store.dispatch("classroom/updateAssetWithoutSync",data)}}},sockets:{"audio-updated":function(data){data.id===this.file.id&&(this.changedBySocket=!0,this.$store.commit("classroom/updateAsset",data),data.asset.speed&&(this.player.speed=data.asset.speed),data.asset.currentTime&&(this.player.currentTime=data.asset.currentTime),"play"in data.asset&&(data.asset.play?(this.isUserInteracted||(this.player.muted=!0),this.player.play()):this.player.pause()))}}},f=(o(1777),o(22)),component=Object(f.a)(m,(function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("classroom-container",{attrs:{asset:e.file,"child-header-height":80,handles:["mr","ml"]}},[o("classroom-container-header",{attrs:{file:e.file,title:e.file.asset.displayName}},[o("div",{ref:"childComponent",attrs:{id:"audio-"+e.file.id}},[o("audio",{attrs:{id:"player-"+e.file.id,crossorigin:"",playsinline:""}},[o("source",{attrs:{src:e.audioUrl,type:e.mimeType}})])])])],1)}),[],!1,null,null,null);t.default=component.exports;installComponents(component,{ClassroomContainerHeader:o(1459).default,ClassroomContainer:o(1389).default})}}]);