exports.ids = [4];
exports.modules = {

/***/ 1381:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1462);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("b9052ed2", content, true, context)
};

/***/ }),

/***/ 1461:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FeedbackTags_vue_vue_type_style_index_0_id_1c41cb86_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1381);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FeedbackTags_vue_vue_type_style_index_0_id_1c41cb86_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FeedbackTags_vue_vue_type_style_index_0_id_1c41cb86_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FeedbackTags_vue_vue_type_style_index_0_id_1c41cb86_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FeedbackTags_vue_vue_type_style_index_0_id_1c41cb86_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1462:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".teacher-profile-feedback-tags[data-v-1c41cb86]{margin-bottom:14px}@media only screen and (max-width:991px){.teacher-profile-feedback-tags[data-v-1c41cb86]{margin-bottom:10px}}.teacher-profile-feedback-tags>*[data-v-1c41cb86]{margin-right:10px}.teacher-profile-feedback-tags-label[data-v-1c41cb86]{font-size:16px;font-weight:700}@media only screen and (max-width:991px){.teacher-profile-feedback-tags-label[data-v-1c41cb86]{font-size:14px;font-weight:600}}.teacher-profile-feedback-tags>.chip[data-v-1c41cb86]{margin-bottom:7px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1504:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/teacher-profile/FeedbackTags.vue?vue&type=template&id=1c41cb86&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.items.length)?_c('div',{staticClass:"teacher-profile-feedback-tags"},[_vm._ssrNode("<span class=\"teacher-profile-feedback-tags-label text--gradient\" data-v-1c41cb86>"+_vm._ssrEscape(_vm._s(_vm.$t('students_say'))+":")+"</span> "),_vm._l(([].concat( _vm.items ).reverse().slice(0, 5)),function(tag){return _c('l-chip',{key:tag.tag.id,attrs:{"label":((tag.tag.name) + " (" + (tag.count) + ")"),"close-btn":false,"light":""}})})],2):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/teacher-profile/FeedbackTags.vue?vue&type=template&id=1c41cb86&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/teacher-profile/FeedbackTags.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var FeedbackTagsvue_type_script_lang_js_ = ({
  name: 'FeedbackTags',
  props: {
    items: {
      type: Array,
      default: () => []
    }
  }
});
// CONCATENATED MODULE: ./components/teacher-profile/FeedbackTags.vue?vue&type=script&lang=js&
 /* harmony default export */ var teacher_profile_FeedbackTagsvue_type_script_lang_js_ = (FeedbackTagsvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/teacher-profile/FeedbackTags.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1461)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  teacher_profile_FeedbackTagsvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "1c41cb86",
  "7f7bfcb4"
  
)

/* harmony default export */ var FeedbackTags = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents(component, {LChip: __webpack_require__(70).default})


/***/ })

};;
//# sourceMappingURL=teacher-profile-feedback-tags.js.map