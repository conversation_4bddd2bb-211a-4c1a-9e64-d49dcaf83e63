{"version": 3, "file": "components/payments-wise-transfer-modal.js", "sources": ["webpack:///./components/payments/WiseTransferModal.vue?b39f", "webpack:///./components/payments/WiseTransferModal.vue?b2d6", "webpack:///./components/payments/WiseTransferModal.vue?ce27", "webpack:///./components/payments/WiseTransferModal.vue?9977", "webpack:///./components/payments/WiseTransferModal.vue", "webpack:///./components/payments/WiseTransferModal.vue?d619", "webpack:///./components/payments/WiseTransferModal.vue?6050"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WiseTransferModal.vue?vue&type=style&index=0&id=0964c1f5&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"9f254164\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WiseTransferModal.vue?vue&type=style&index=0&id=0964c1f5&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".wise-transfer-modal[data-v-0964c1f5]  .v-card{box-shadow:none!important}.wise-transfer-modal .v-text-field .v-input .v-input__control .v-text-field--outlined[data-v-0964c1f5]{border-radius:16px!important}.wise-transfer-modal .input-label[data-v-0964c1f5]{font-size:14px;font-weight:400;color:rgba(0,0,0,.87)}.wise-transfer-modal .currency-info[data-v-0964c1f5],.wise-transfer-modal .wise-transfer-info[data-v-0964c1f5]{color:rgba(0,0,0,.6);font-size:14px;line-height:1.4}@media(max-width:768px){.wise-transfer-modal .v-card[data-v-0964c1f5]{padding:16px!important}.wise-transfer-modal .v-row[data-v-0964c1f5]{margin:0}.wise-transfer-modal .v-col[data-v-0964c1f5]{padding:0;margin-bottom:2px}.wise-transfer-modal .v-col[data-v-0964c1f5]:last-child{margin-bottom:0}.wise-transfer-modal .text-right[data-v-0964c1f5]{display:flex;justify-content:flex-end}.wise-transfer-modal .text-right .v-btn[data-v-0964c1f5]{width:-webkit-max-content!important;width:-moz-max-content!important;width:max-content!important}.wise-transfer-modal .currency-info[data-v-0964c1f5],.wise-transfer-modal .wise-transfer-info[data-v-0964c1f5]{margin-bottom:2px}.wise-transfer-modal .wise-modal[data-v-0964c1f5]{flex-direction:column;margin-bottom:2px;width:100%}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',{attrs:{\"dialog\":_vm.show,\"max-width\":\"800\",\"custom-class\":\"wise-transfer-modal\"},on:{\"close-dialog\":function($event){return _vm.$emit('close')}}},[_c('v-card',{staticClass:\"pa-2\",attrs:{\"flat\":\"\"}},[_c('div',{staticClass:\"d-flex justify-space-between align-center mb-2\"},[_c('h2',{staticClass:\"text-h6 font-weight-medium\"},[_vm._v(\"Wise Transfer\")])]),_vm._v(\" \"),_c('div',{staticClass:\"wise-transfer-info mb-2\"},[_vm._v(\"\\n      Wise payouts are processed each Monday. You will receive a link by email\\n      from <PERSON>, and you will enter your banking details directly with them.\\n      Please enter your email address and your full name below.\\n    \")]),_vm._v(\" \"),_c('v-form',{ref:\"form\",on:{\"submit\":function($event){$event.preventDefault();return _vm.handleSubmit.apply(null, arguments)}}},[_c('v-row',{staticClass:\"wise-modal\",attrs:{\"no-gutters\":\"\"}},[_c('v-col',{staticClass:\"pr-2\",attrs:{\"cols\":\"6\"}},[_c('div',{staticClass:\"input-label mb-1\"},[_vm._v(\"Email address:\")]),_vm._v(\" \"),_c('text-input',{attrs:{\"value\":_vm.form.email,\"type-class\":\"border-gradient\",\"height\":\"44\",\"rules\":[_vm.rules.required, _vm.rules.email]},on:{\"input\":function($event){return _vm.updateValue($event, 'email')}}})],1),_vm._v(\" \"),_c('v-col',{staticClass:\"pl-2\",attrs:{\"cols\":\"6\"}},[_c('div',{staticClass:\"input-label mb-1\"},[_vm._v(\"Full name:\")]),_vm._v(\" \"),_c('text-input',{attrs:{\"value\":_vm.form.fullName,\"type-class\":\"border-gradient\",\"height\":\"44\",\"rules\":[_vm.rules.required]},on:{\"input\":function($event){return _vm.updateValue($event, 'fullName')}}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"currency-info mb-2\"},[_vm._v(\"\\n        In what currency would you like to receive the transfer? Please enter\\n        the 3-letter currency code (i.e. AUD, ZAR, PEN, etc.).\\n      \")]),_vm._v(\" \"),_c('v-row',{attrs:{\"no-gutters\":\"\"}},[_c('v-col',{staticClass:\"wise-modal\",attrs:{\"cols\":\"6\"}},[_c('div',{staticClass:\"input-label mb-1\"},[_vm._v(\"Currency:\")]),_vm._v(\" \"),_c('text-input',{attrs:{\"value\":_vm.form.currency,\"type-class\":\"border-gradient\",\"height\":\"44\",\"rules\":[_vm.rules.required, _vm.rules.currencyCode]},on:{\"input\":function($event){return _vm.updateValue($event, 'currency')}}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"text-right\"},[_c('v-btn',{staticClass:\"px-12\",attrs:{\"color\":\"primary\",\"large\":\"\",\"type\":\"submit\",\"loading\":_vm.loading}},[_vm._v(\"\\n          Confirm payout\\n        \")])],1)],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport LDialog from '~/components/LDialog'\nimport TextInput from '~/components/form/TextInput'\nexport default {\n  name: 'WiseTransferModal',\n  components: {\n    LDialog,\n    TextInput,\n  },\n  props: {\n    show: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  data() {\n    return {\n      loading: false,\n      form: {\n        email: '',\n        fullName: '',\n        currency: '',\n      },\n      rules: {\n        required: (v) => !!v || 'This field is required',\n        email: (v) => /.+@.+\\..+/.test(v) || 'Please enter a valid email',\n        currencyCode: (v) =>\n          !v ||\n          /^[A-Z]{3}$/.test(v) ||\n          'Please enter a valid 3-letter currency code',\n      },\n    }\n  },\n  watch: {\n    show(newVal) {\n      if (newVal) {\n        this.resetForm()\n      }\n    },\n  },\n  methods: {\n    updateValue(value, field) {\n      this.form[field] = value\n    },\n    resetForm() {\n      this.form = {\n        email: '',\n        fullName: '',\n        currency: '',\n      }\n      if (this.$refs.form) {\n        this.$refs.form.resetValidation()\n      }\n    },\n    async handleSubmit() {\n      if (!this.loading && this.$refs.form.validate()) {\n        this.loading = true\n        try {\n          // Call the Wise transfer API\n          const result = await this.$store.dispatch(\n            'payments/requestWiseTransfer',\n            this.form\n          )\n\n          // Show toast notification based on the result\n          if (result.success) {\n            this.$store.dispatch(\n              'snackbar/success',\n              { successMessage: 'Form submitted successfully' },\n              { root: true }\n            )\n            this.$emit('submit', this.form)\n            this.$emit('close')\n          } else {\n            this.$store.dispatch(\n              'snackbar/error',\n              { errorMessage: 'Something went wrong' },\n              { root: true }\n            )\n          }\n        } catch (error) {\n          // Show generic error message if the store action throws\n          // alert('An unexpected error occurred. Please try again.')\n        } finally {\n          this.loading = false\n        }\n      }\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WiseTransferModal.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./WiseTransferModal.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./WiseTransferModal.vue?vue&type=template&id=0964c1f5&scoped=true&\"\nimport script from \"./WiseTransferModal.vue?vue&type=script&lang=js&\"\nexport * from \"./WiseTransferModal.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./WiseTransferModal.vue?vue&type=style&index=0&id=0964c1f5&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"0964c1f5\",\n  \"1511b9a7\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {LDialog: require('D:/languworks/langu-frontend/components/LDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VCard } from 'vuetify/lib/components/VCard';\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VForm } from 'vuetify/lib/components/VForm';\nimport { VRow } from 'vuetify/lib/components/VGrid';\ninstallComponents(component, {VBtn,VCard,VCol,VForm,VRow})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AAHA;AAPA;AAgBA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAOA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAHA;AACA;AAIA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAKA;AACA;AAEA;AAAA;AACA;AAAA;AAEA;AACA;AACA;AACA;AAEA;AAAA;AACA;AAAA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAhDA;AArCA;;AClFA;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}