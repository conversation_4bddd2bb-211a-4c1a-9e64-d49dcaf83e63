{"version": 3, "file": "pages/checkout/stripe-form/_id/index.js", "sources": ["webpack:///./pages/checkout/stripe-form/_id/index.vue?1109", "webpack:///./pages/checkout/stripe-form/_id/index.vue", "webpack:///./pages/checkout/stripe-form/_id/index.vue?b13d", "webpack:///./pages/checkout/stripe-form/_id/index.vue?498d"], "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"min-height\":\"calc(100vh - 300px)\"}},[])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n\nimport { loadStripe } from '@stripe/stripe-js/pure'\nexport default {\n  name: 'StripeForm',\n  async beforeMount() {\n    const sessionId = this.$route.params.id\n    if (!this.$stripe) {\n      this.$stripe = await loadStripe(process.env.STRIPE_PUBLISHABLE_KEY)\n    }\n    await this.$cookiz.set('thank_you_page_allowed', 1, {\n      domain: process.env.NUXT_ENV_COOKIE_DOMAIN,\n      path: '/',\n    })\n\n    if (this.$stripe) {\n      this.$stripe\n        .redirectToCheckout({\n          sessionId,\n        })\n        .then(() => {\n          console.log('success')\n        })\n        .catch((e) => {\n          console.log('error', e)\n        })\n    } else {\n      console.log('stripe not found')\n    }\n  },\n}\n", "import mod from \"-!../../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=0ffed8f2&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"40d25318\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAFA;AACA;AAIA;AACA;AAEA;AADA;AAIA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AA3BA;;ACNA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}