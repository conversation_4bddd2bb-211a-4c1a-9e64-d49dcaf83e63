(window.webpackJsonp=window.webpackJsonp||[]).push([[14,57,58,83,116,140],{1434:function(e,t,n){var content=n(1491);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("a98bb618",content,!0,{sourceMap:!1})},1437:function(e,t,n){"use strict";n(23);var r={methods:{getSrcAvatar:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"avatar.png";return null!=e&&e[t]?e[t]:n(881)("./".concat(r))},getSrcSetAvatar:function(e,t,n){return null!=e&&e[t]&&null!=e&&e[n]?"\n            ".concat(e[t]," 1x,\n            ").concat(e[n]," 2x,\n          "):""}}},o=n(22),component=Object(o.a)(r,undefined,undefined,!1,null,null,null);t.a=component.exports},1445:function(e,t,n){var content=n(1512);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("f203485e",content,!0,{sourceMap:!1})},1450:function(e,t,n){"use strict";n.r(t);var r={name:"ConfirmDialog",props:{isShownConfirmDialog:{type:Boolean,required:!0},cancelTextButton:{type:String,default:"close"},confirmTextButton:{type:String,default:"confirm"}}},o=(n(1511),n(22)),d=n(42),l=n.n(d),c=n(1327),component=Object(o.a)(r,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.isShownConfirmDialog?n("l-dialog",e._g({attrs:{dialog:e.isShownConfirmDialog,"hide-close-button":"","max-width":"418","custom-class":"remove-illustration text-center"}},e.$listeners),[n("div",[n("div",{staticClass:"remove-illustration-title font-weight-medium"},[e._v("\n      "+e._s(e.$t("are_you_sure"))+"\n    ")]),e._v(" "),n("div",{staticClass:"mt-2"},[e._t("default")],2),e._v(" "),n("div",{staticClass:"d-flex justify-space-around justify-sm-space-between flex-wrap mt-2"},[n("v-btn",{staticClass:"gradient font-weight-medium my-1",on:{click:function(t){return e.$emit("close-dialog")}}},[n("div",{staticClass:"text--gradient"},[e._v("\n          "+e._s(e.$t(e.cancelTextButton))+"\n        ")])]),e._v(" "),n("v-btn",{staticClass:"font-weight-medium my-1",attrs:{color:"primary"},on:{click:function(t){return e.$emit("confirm")}}},[e._v("\n        "+e._s(e.$t(e.confirmTextButton))+"\n      ")])],1)])]):e._e()}),[],!1,null,null,null);t.default=component.exports;l()(component,{LDialog:n(149).default}),l()(component,{VBtn:c.a})},1490:function(e,t,n){"use strict";n(1434)},1491:function(e,t,n){var r=n(18)(!1);r.push([e.i,'.text-editor{position:relative}.text-editor-buttons{position:absolute;right:18px;top:8px;z-index:2}.text-editor-buttons button{display:inline-flex;justify-content:center;align-items:center;width:24px;height:24px;margin-left:8px;border-radius:2px;border:1px solid transparent}.text-editor-buttons button.is-active{background-color:var(--v-greyBg-base);border-color:var(--v-greyLight-base)}.text-editor .ProseMirror{min-height:280px;margin-bottom:4px;padding:40px 12px 12px;border:1px solid #bebebe;font-size:13px;border-radius:16px;line-height:1.23}.text-editor .ProseMirror>*{position:relative}.text-editor .ProseMirror p{margin-bottom:0}.text-editor .ProseMirror ul{padding-left:28px}.text-editor .ProseMirror ul>li p{margin-bottom:0}.text-editor .ProseMirror strong{font-weight:700!important}.text-editor .ProseMirror.focus-visible,.text-editor .ProseMirror:focus,.text-editor .ProseMirror:focus-visible{outline:none!important}.text-editor .ProseMirror-focused:before{content:"";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:16px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}.text-editor .v-text-field__details{padding:0 14px}',""]),e.exports=r},1503:function(e,t,n){"use strict";n.r(t);n(31),n(96);var r=n(1586),o=n(1587),d=n(1577),l=n(1589),c={name:"Editor",components:{EditorContent:r.b},props:{value:{type:String,required:!0},counter:{type:Boolean,default:!1},autoLink:{type:Boolean,default:!1},limit:{type:Number,default:null}},data:function(){return{editor:null,text:"",isValid:!0,editorEl:null,keysPressed:{},isDirty:!1}},watch:{value:function(e){this.editor.getHTML()===e||this.editor.commands.setContent(e,!1)}},mounted:function(){var e=this;this.editor=new r.a({content:this.value,extensions:[o.a,d.a.configure({limit:this.limit}),l.a.configure({autolink:!0})]}),this.editor.on("create",(function(t){var n=t.editor;e.text=n.getText(),e.$nextTick((function(){e.editorEl=document.getElementsByClassName("ProseMirror")[0],e.editorEl&&(e.editorEl.addEventListener("keydown",e.keydownHandler),e.editorEl.addEventListener("keyup",e.keyupHandler))})),e.validation()})),this.editor.on("update",(function(t){var n=t.editor;e.isDirty=!0,e.text=n.getText(),e.validation(),e.$emit("update",e.text?n.getHTML():"")}))},beforeDestroy:function(){this.editorEl&&(this.editorEl.removeEventListener("keydown",this.keydownHandler),this.editorEl.removeEventListener("keyup",this.keyupHandler)),this.editor.destroy()},methods:{keydownHandler:function(e){this.keysPressed[e.keyCode]=!0,(e.ctrlKey||this.keysPressed[17]||this.keysPressed[91]||this.keysPressed[93]||this.keysPressed[224])&&this.keysPressed[13]?(e.preventDefault(),this.$emit("submit"),this.keysPressed={}):13!==e.keyCode||e.shiftKey||(e.preventDefault(),this.editor.commands.enter())},keyupHandler:function(e){delete this.keysPressed[e.keyCode]},validation:function(){var e=this.text.trim().length;this.isValid=!!e,e&&this.limit&&(this.isValid=e<=this.limit),this.$emit("validation",this.isValid)}}},m=(n(1490),n(22)),component=Object(m.a)(c,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"text-editor"},[e.editor?r("div",{staticClass:"text-editor-buttons"},[r("button",{class:{"is-active":e.editor.isActive("bold")},on:{click:function(t){t.stopPropagation(),t.preventDefault(),e.editor.chain().focus().toggleBold().run()}}},[r("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16"}},[r("use",{attrs:{"xlink:href":n(91)+"#editor-bold-icon"}})])]),e._v(" "),r("button",{class:{"is-active":e.editor.isActive("bulletList")},on:{click:function(t){t.stopPropagation(),t.preventDefault(),e.editor.chain().focus().toggleBulletList().run()}}},[r("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16"}},[r("use",{attrs:{"xlink:href":n(91)+"#editor-list-icon"}})])])]):e._e(),e._v(" "),r("editor-content",{attrs:{editor:e.editor}}),e._v(" "),e.counter?r("div",{staticClass:"v-text-field__details"},[e._m(0),e._v(" "),r("div",{class:["v-counter theme--light",{"error--text":!e.isValid&&e.isDirty}]},[e._v("\n      "+e._s(e.text.length)+" / "+e._s(e.limit)+"\n    ")])]):e._e()],1)}),[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"v-messages theme--light"},[t("div",{staticClass:"v-messages__wrapper"})])}],!1,null,null,null);t.default=component.exports},1511:function(e,t,n){"use strict";n(1445)},1512:function(e,t,n){var r=n(18)(!1);r.push([e.i,".remove-illustration-title{font-size:20px}",""]),e.exports=r},1522:function(e,t,n){"use strict";n.r(t);n(31),n(20),n(80);var r={name:"UserStatus",props:{userId:{type:Number,default:0},large:{type:Boolean,default:!1},userStatuses:{type:Object,default:function(){return{}}}},computed:{status:function(){var e,t="offline";return Object.prototype.hasOwnProperty.call(this.userStatuses,null===(e=this.userId)||void 0===e?void 0:e.toString())&&(t=this.userStatuses[this.userId]),t}}},o=(n(1597),n(22)),component=Object(o.a)(r,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("div",{class:["user-status","user-status--"+e.status,{"user-status--large":e.large}]})}),[],!1,null,"652352c7",null);t.default=component.exports},1545:function(e,t,n){var content=n(1598);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("006007e9",content,!0,{sourceMap:!1})},1597:function(e,t,n){"use strict";n(1545)},1598:function(e,t,n){var r=n(18)(!1);r.push([e.i,".user-status[data-v-652352c7]{width:16px;height:16px;border-radius:50%;border:2px solid #fff;background:#636363;z-index:2}.user-status--idle[data-v-652352c7]{background:linear-gradient(122.42deg,var(--v-redLight-base),var(--v-orangeLight2-base))}.user-status--online[data-v-652352c7]{background:var(--v-success-base)}.user-status--large[data-v-652352c7]{width:25px;height:25px}@media only screen and (max-width:991px){.user-status--large[data-v-652352c7]{width:23px;height:23px}}@media only screen and (max-width:639px){.user-status--large[data-v-652352c7]{width:21px;height:21px}}@media only screen and (max-width:479px){.user-status--large[data-v-652352c7]{width:19px;height:19px}}",""]),e.exports=r},1632:function(e,t,n){var content=n(1730);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("45e472a6",content,!0,{sourceMap:!1})},1633:function(e,t,n){var content=n(1732);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("7337ad18",content,!0,{sourceMap:!1})},1686:function(e,t,n){"use strict";n.r(t);var r={name:"LoadMoreBtn",props:{large:{type:Boolean,default:!1},textBtn:{type:String,required:!0},fetchFunc:{type:Function,required:!0}}},o=(n(1729),n(22)),d=n(42),l=n.n(d),c=n(1327),component=Object(o.a)(r,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("v-btn",{class:["load-more-btn",{"load-more-btn--large":e.large}],attrs:{text:"",width:"100%"},on:{click:e.fetchFunc}},[r("div",{staticClass:"load-more-btn-icon mr-1"},[r("svg",{attrs:{viewBox:"0 0 17 12"}},[r("use",{attrs:{"xlink:href":n(91)+"#arrow-prev"}})])]),e._v("\n  "+e._s(e.textBtn)+"\n")])}),[],!1,null,"34f0bc91",null);t.default=component.exports;l()(component,{VBtn:c.a})},1729:function(e,t,n){"use strict";n(1632)},1730:function(e,t,n){var r=n(18)(!1);r.push([e.i,".load-more-btn[data-v-34f0bc91]{color:var(--v-greyDark-base)}.load-more-btn-icon[data-v-34f0bc91]{display:flex;align-items:center;justify-content:center;width:38px;height:38px;color:#fff;border-radius:50%;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%)}.load-more-btn-icon svg[data-v-34f0bc91]{width:19px;height:14px;transform:rotate(-90deg)}.load-more-btn--large[data-v-34f0bc91]{height:52px!important}.load-more-btn--large .load-more-btn-icon[data-v-34f0bc91]{width:52px;height:52px}.load-more-btn--large .load-more-btn-icon svg[data-v-34f0bc91]{width:26px;height:16px}",""]),e.exports=r},1731:function(e,t,n){"use strict";n(1633)},1732:function(e,t,n){var r=n(18)(!1);r.push([e.i,".conversation-item{display:flex;width:100%}.conversation-item>div{width:calc(100% - 60px);max-width:462px}@media only screen and (max-width:639px){.conversation-item>div{width:calc(100% - 45px)}}@media only screen and (max-width:479px){.conversation-item>div{width:calc(100% - 20px)}}.conversation-item-header{display:flex;align-items:flex-end;margin-bottom:2px;font-size:13px;color:var(--v-greyLight-darken2);line-height:1.23}@media only screen and (max-width:479px){.conversation-item-header{font-size:12px}}.conversation-item-header-avatar{position:relative;margin-bottom:2px;filter:drop-shadow(0 4px 5px rgba(0,0,0,.2))}@media only screen and (max-width:479px){.conversation-item-header-avatar .v-avatar{width:42px!important;min-width:42px!important;height:42px!important}}.conversation-item-header>div:not(.conversation-item-header-avatar){margin-left:10px}.conversation-item-body{padding:16px 12px;font-size:14px;line-height:1.4;border-radius:16px;color:var(--v-greyLight-darken4);background:linear-gradient(126.15deg,rgba(128,182,34,.18),rgba(60,135,248,.18) 102.93%)}@media only screen and (max-width:1439px){.conversation-item-body{padding:14px 12px}}.conversation-item-body a{color:var(--v-greyLight-darken4)!important;transition:color .3s}.conversation-item-body a span{display:inline-block;font-size:20px}.conversation-item-body a:hover{color:var(--v-success-base)!important}.conversation-item-body.conversation-item-body--file a{text-decoration:none}.conversation-item-body ul{padding-left:20px}.conversation-item-body ul li{margin-bottom:0}.conversation-item-body ul li p{min-height:16px;margin-bottom:0}.conversation-item-body>div{word-wrap:break-word}.conversation-item-body>div>*{min-height:16px;margin-bottom:0}.conversation-item-body>div>:last-child{min-height:0}.conversation-item-footer{display:flex;justify-content:flex-end;align-items:center;margin-top:2px;padding-right:10px;font-size:13px;color:var(--v-greyLight-darken2);line-height:1.23}@media only screen and (max-width:479px){.conversation-item-footer{font-size:12px}}.conversation-item--mine{justify-content:flex-end}.conversation-item--mine .conversation-item-header{flex-direction:row-reverse}.conversation-item--mine .conversation-item-header-avatar{margin-right:0;margin-left:12px}.conversation-item--mine .conversation-item-footer{padding-right:0}.conversation-item--mine+.conversation-item--mine{margin-top:10px}@media only screen and (max-width:479px){.conversation-item--mine+.conversation-item--mine{margin-top:6px}}.conversation-item--mine+.conversation-item--mine .conversation-item-header-avatar{display:none}.conversation-item--other+.conversation-item--other{margin-top:10px}@media only screen and (max-width:479px){.conversation-item--other+.conversation-item--other{margin-top:6px}}.conversation-item--other+.conversation-item--other .conversation-item-header-avatar{display:none}",""]),e.exports=r},1843:function(e,t,n){var content=n(2042);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("c2cacb64",content,!0,{sourceMap:!1})},1919:function(e,t,n){"use strict";n.r(t);var r=n(10),o=(n(62),n(31),n(23),n(20),n(37),n(44),n(151),n(1450)),d=n(1437),l={name:"ConversationItem",components:{UserStatus:n(1522).default,ConfirmDialog:o.default},mixins:[d.a],props:{item:{type:Object,required:!0},threadId:{type:Number,required:!0},recipientName:{type:String,required:!0},recipientAvatars:{type:Object,required:!0},userAvatars:{type:Object,required:!0},userStatuses:{type:Object,default:function(){return{}}}},data:function(){return{isShownMessageConfirmDialog:!1}},computed:{userId:function(){var e;return null===(e=this.$store.state.user.item)||void 0===e?void 0:e.id},isCurrentUser:function(){return this.userId===this.item.authorId},avatars:function(){return this.isCurrentUser?this.userAvatars:this.recipientAvatars},fileUrl:function(){return"".concat("'http://localhost:3000'","/messages/file/").concat(this.item.id)},timeZone:function(){return this.$store.getters["user/timeZone"]}},methods:{downloadClickHandler:function(){var e=this;return Object(r.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$axios({url:e.fileUrl,method:"GET",responseType:"blob"}).then((function(t){var n=window.URL.createObjectURL(new Blob([t.data])),link=document.createElement("a");link.href=n,link.setAttribute("download",e.item.text),document.body.appendChild(link),link.click()})).catch((function(){return console.info("Download error")}));case 2:case"end":return t.stop()}}),t)})))()},removeMessage:function(){this.isShownMessageConfirmDialog=!1,this.$store.dispatch("message/removeMessage",this.item.id)}}},c=(n(1731),n(22)),m=n(42),v=n.n(m),h=n(1343),f=n(1327),x=n(261),component=Object(c.a)(l,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:["conversation-item",{"conversation-item--mine":e.isCurrentUser},{"conversation-item--other":!e.isCurrentUser}]},[n("div",[n("div",{staticClass:"conversation-item-header"},[n("div",{staticClass:"conversation-item-header-avatar"},[n("v-avatar",{attrs:{width:"52",height:"52"}},[n("v-img",{attrs:{src:e.getSrcAvatar(e.avatars,"user_thumb_52x52"),srcset:e.getSrcSetAvatar(e.avatars,"user_thumb_52x52","user_thumb_104x104"),options:{rootMargin:"50%"}}})],1),e._v(" "),n("user-status",{attrs:{"user-id":e.item.authorId,"user-statuses":e.userStatuses}})],1),e._v(" "),n("div",[e.isCurrentUser?[e._v("\n          "+e._s(e.$t("sent_by_me_on"))+"\n        ")]:[e._v("\n          "+e._s(e.$t("sent_by_somebody_on",{username:e.recipientName}))+"\n        ")],e._v("\n        "+e._s(e.$dayjs(e.item.createDate).tz(e.timeZone).format("ll, LT"))+"\n      ")],2)]),e._v(" "),n("div",{class:["conversation-item-body",{"conversation-item-body--file":e.item.isFile}]},[e.item.isFile?[n("a",{attrs:{href:e.fileUrl,download:""},on:{click:function(t){return t.preventDefault(),e.downloadClickHandler.apply(null,arguments)}}},[n("span",{staticClass:"mr-1"},[e._v("📎")]),e._v(e._s(e.item.text)+"\n        ")])]:[n("div",{domProps:{innerHTML:e._s(e.item.text)}})]],2),e._v(" "),n("div",{staticClass:"conversation-item-footer"},[e.item.readDate?[e._v(e._s(e.$t("seen"))+"\n        "+e._s(e.$dayjs(e.item.readDate).tz(e.timeZone).format("ll, LT")))]:[e._v(e._s(e.$t("not_yet_seen")))],e._v(" "),e.isCurrentUser?n("v-btn",{attrs:{text:"","x-small":"",height:"16",color:"grey"},on:{click:function(t){e.isShownMessageConfirmDialog=!0}}},[e._v("\n        ("),n("span",{staticClass:"text-decoration-underline"},[e._v(e._s(e.$t("delete_message")))]),e._v(")")]):e._e()],2)]),e._v(" "),n("confirm-dialog",{attrs:{"is-shown-confirm-dialog":e.isShownMessageConfirmDialog,"cancel-text-button":"no","confirm-text-button":"yes"},on:{confirm:e.removeMessage,"close-dialog":function(t){e.isShownMessageConfirmDialog=!1}}},[e._v("\n    "+e._s(e.$t("are_you_sure_you_want_to_delete_this_message_permanently"))+"\n  ")])],1)}),[],!1,null,null,null);t.default=component.exports;v()(component,{UserStatus:n(1522).default,ConfirmDialog:n(1450).default}),v()(component,{VAvatar:h.a,VBtn:f.a,VImg:x.a})},2041:function(e,t,n){"use strict";n(1843)},2042:function(e,t,n){var r=n(18)(!1);r.push([e.i,".conversation{padding:30px 44px 138px;background-color:#fff;border-radius:20px}@media only screen and (max-width:1439px){.conversation{padding:24px 24px 60px}}@media only screen and (min-width:768px){.conversation{box-shadow:0 8px 17px rgba(17,46,90,.1)}}@media only screen and (max-width:767px){.conversation{padding:0}}.conversation-header{display:flex;justify-content:space-between}.conversation-header .conversation-avatar{filter:drop-shadow(0 4px 5px rgba(0,0,0,.2))}.conversation-header .conversation-avatar>div,.conversation-header .conversation-avatar>div .v-avatar{position:relative}@media only screen and (max-width:991px){.conversation-header .conversation-avatar>div .v-avatar{width:90px!important;height:90px!important}}@media only screen and (max-width:639px){.conversation-header .conversation-avatar>div .v-avatar{width:80px!important;height:80px!important}}@media only screen and (max-width:479px){.conversation-header .conversation-avatar>div .v-avatar{width:70px!important;height:70px!important}}.conversation-header .conversation-avatar>div .v-avatar a{display:block;position:absolute;top:0;left:0;width:100%;height:100%;z-index:2}.conversation-header .conversation-avatar .user-status{right:3px;bottom:3px}@media only screen and (max-width:639px){.conversation-header .conversation-avatar .user-status{right:1px;bottom:1px}}.conversation-header .conversation-title{font-size:24px;line-height:1.333}@media only screen and (max-width:1439px){.conversation-header .conversation-title{font-size:20px}}@media only screen and (max-width:767px){.conversation-header .conversation-title{font-size:18px}}.conversation-header .conversation-details{font-size:13px;line-height:1.23;color:var(--v-greyLight-darken2)}@media only screen and (max-width:767px){.conversation-header .conversation-details{font-size:12px}}@media only screen and (max-width:1439px){.conversation-header .conversation-details>div:not(:last-child){margin-bottom:4px}}.conversation-header .conversation-details a{color:var(--v-grey-base)}.conversation-header .conversation-details a:hover{color:var(--v-green-base);transition:all .3s}.conversation-body .new-message-label{font-size:16px}.conversation-body .new-message .text-editor .ProseMirror{min-height:118px}.conversation-body .new-message .text-editor .text-editor-buttons{left:8px;right:auto}.conversation-body .new-message-notice{margin-bottom:4px;font-size:12px;color:var(--v-greyLight-darken2);line-height:1.2}@media only screen and (max-width:1439px){.conversation-body .new-message-notice{margin-bottom:8px}}.conversation-body .new-message-bottom{display:flex;flex-wrap:wrap;justify-content:space-between;align-items:center}.conversation-body .new-message-bottom .upload-file-name{display:flex;align-items:center}.conversation-body .new-message-bottom-buttons{width:100%;display:flex;flex-wrap:wrap;justify-content:flex-end}@media only screen and (max-width:479px){.conversation-body .new-message-bottom-buttons{justify-content:space-around}}.conversation-body .new-message-bottom-buttons .v-btn .v-btn__content{white-space:nowrap}.conversation-body .messages-list{display:flex;flex-direction:column}.conversation-body .conversation-show-more-btn{max-width:240px;margin-left:auto;margin-right:auto}",""]),e.exports=r},2185:function(e,t,n){"use strict";n.r(t);var r=n(10),o=(n(62),n(20),n(80),n(1503)),d=n(1686),l=n(1919),c=n(1437),m=n(1522),v={name:"Conversation",components:{Editor:o.default,LoadMoreBtn:d.default,ConversationItem:l.default,UserStatus:m.default},mixins:[c.a],props:{item:{type:Object,required:!0},userStatuses:{type:Object,default:function(){return{}}}},data:function(){var e=this;return{newMessage:"",messagesPage:1,isMessageValid:!1,rules:{file:[function(e){return!!e},function(t){return!t||t.size<10485760||e.$t("file_size_should_be_less_than",{value:"10 MB"})}]}}},computed:{threadId:function(){return this.item.threadId},userTimezone:function(){return this.$store.getters["user/timeZone"]},recipientName:function(){return this.item.userIsDeleted?this.$t("deleted_user"):this.item.firstName},recipientLink:function(){return this.item.username?"/teacher/".concat(this.item.username):null},hasNextLesson:function(){var e;return null===(e=this.item.nextLessonDate)||void 0===e?void 0:e.length},status:function(){var e,t="offline";return Object.prototype.hasOwnProperty.call(this.userStatuses,null===(e=this.item.userId)||void 0===e?void 0:e.toString())&&(t=this.userStatuses[this.item.userId]),t},totalPages:function(){return Math.ceil(this.item.countMessages/"20")},messages:function(){var e;return null!==(e=this.item.messages)&&void 0!==e?e:[]},isMoreButtonShown:function(){return this.totalPages>1&&this.messagesPage<this.totalPages}},watch:{threadId:function(){this.messagesPage=1}},beforeDestroy:function(){this.newMessage="",this.file=null},methods:{uploadFile:function(e){this.$store.dispatch("message/uploadFile",{threadId:this.threadId,file:e})},submitNewMessageHandler:function(){var e=this;this.isMessageValid&&this.$store.dispatch("message/sendMessage",{threadId:this.threadId,message:this.newMessage}).then((function(){e.newMessage=""}))},fetchMessages:function(){var e=this;return Object(r.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.messagesPage++,t.next=3,e.$store.dispatch("loadingAllow",!1);case 3:return t.next=5,e.$store.dispatch("message/getConversation",{threadId:e.threadId,page:e.messagesPage});case 5:return t.next=7,e.$store.dispatch("loadingAllow",!0);case 7:case"end":return t.stop()}}),t)})))()},handleKeydown:function(e){"Enter"!==e.key||e.ctrlKey?"Enter"===e.key&&e.ctrlKey&&(e.preventDefault(),this.submitNewMessageHandler()):(e.preventDefault(),this.newMessage+="\n")}}},h=(n(2041),n(22)),f=n(42),x=n.n(f),_=n(1343),y=n(1327),w=n(1614),k=n(261),component=Object(h.a)(v,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"conversation"},[n("div",{staticClass:"conversation-header mb-1"},[n("div",{staticClass:"mr-1 mr-sm-2"},[n("div",{staticClass:"conversation-title font-weight-medium"},[e._v("\n        "+e._s(e.$t("messages_with"))+"\n        "),e.item.userIsDeleted?[e._v("\n          "+e._s(e.$t("deleted_user"))+"\n        ")]:[e._v("\n          "+e._s(e.item.firstName)+"\n          "+e._s(e.item.lastName)+"\n        ")]],2),e._v(" "),e.item.userIsDeleted?e._e():n("div",{staticClass:"conversation-details mt-2"},[n("div",[e._v("\n          "+e._s(e.$t("next_lesson"))+":\n          "),n("span",{staticClass:"text-no-wrap"},[e.hasNextLesson?[n("span",{staticClass:"font-weight-medium"},[e._v("\n                "+e._s(e.$dayjs(e.item.nextLessonDate).tz(e.userTimezone).format("ll, LT"))+"\n              ")]),e._v("\n              ("),n("nuxt-link",{attrs:{to:e.localePath({path:"/user/lessons"})}},[e._v(e._s(e.$t("see_lessons")))]),e._v(")\n            ")]:[n("span",{staticClass:"font-weight-medium"},[e._v("\n                "+e._s(e.$t("none_scheduled"))+"\n              ")])]],2)]),e._v(" "),n("div",[e._v("\n          "+e._s(e.$t("current_local_time_for"))+"\n          "+e._s(e.item.firstName)+":\n          "),n("span",{staticClass:"text-no-wrap"},[n("span",{staticClass:"font-weight-medium"},[e._v("\n              "+e._s(e.$dayjs().tz(e.item.recipientTimeZone).format("LT"))+"\n            ")]),e._v("\n            ("+e._s(e.$dayjs().tz(e.item.recipientTimeZone).format("z"))+")\n          ")])]),e._v(" "),n("div",[e._v("\n          "+e._s(e.$t("last_online"))+":\n          "),n("span",{staticClass:"font-weight-medium text-no-wrap"},["online"===e.status?[e._v("\n              "+e._s(e.$t("online_now"))+"\n            ")]:"idle"===e.status?[e._v("\n              "+e._s(e.$t("online_but_idle"))+"\n            ")]:[e._v("\n              "+e._s(e.$dayjs(e.item.lastLoginDate).tz(e.userTimezone).format("ll, LT"))+"\n            ")]],2)])])]),e._v(" "),e.item.userIsDeleted?e._e():n("div",{staticClass:"conversation-avatar"},[n("div",[n("v-avatar",{attrs:{width:"118",height:"118"}},[n("v-img",{attrs:{src:e.getSrcAvatar(e.item.recipientAvatars,"user_thumb_118x118"),srcset:e.getSrcSetAvatar(e.item.recipientAvatars,"user_thumb_118x118","user_thumb_236x236"),options:{rootMargin:"50%"}}}),e._v(" "),e.recipientLink?n("nuxt-link",{attrs:{to:e.recipientLink}}):e._e()],1),e._v(" "),n("user-status",{attrs:{"user-id":e.item.userId,"user-statuses":e.userStatuses,large:""}})],1)])]),e._v(" "),n("div",{staticClass:"conversation-body"},[e.item.userIsDeleted?e._e():n("div",{staticClass:"new-message"},[n("div",{staticClass:"new-message-label font-weight-medium mb-1"},[e._v("\n        "+e._s(e.$t("write_new_message"))+" 🖋️\n      ")]),e._v(" "),n("editor",{attrs:{value:e.newMessage,limit:6e3,"auto-link":""},on:{update:function(t){e.newMessage=t},validation:function(t){e.isMessageValid=t},submit:e.submitNewMessageHandler,keydown:e.handleKeydown}}),e._v(" "),n("div",{staticClass:"new-message-notice pl-2"},[e._v("\n        "+e._s(e.$t("press_ctrl_enter_to_send"))+"\n      ")]),e._v(" "),n("div",{staticClass:"new-message-bottom"},[n("div",{staticClass:"new-message-attached-file"},[n("v-file-input",{ref:"fileMessage",staticClass:"d-none",attrs:{rules:e.rules.file,"prepend-icon":""},on:{change:e.uploadFile}})],1),e._v(" "),n("div",{staticClass:"new-message-bottom-buttons"},[n("v-btn",{staticClass:"gradient font-weight-medium my-1 ml-1",on:{click:function(t){e.$refs.fileMessage.$el.querySelector("input").click()}}},[n("div",{staticClass:"text--gradient"},[e._v(e._s(e.$t("attach_document"))+" 📎")])]),e._v(" "),n("v-btn",{staticClass:"font-weight-medium my-1 ml-1",attrs:{color:"primary"},on:{click:e.submitNewMessageHandler}},[e._v("\n            "+e._s(e.$t("send"))+" 📬\n          ")])],1)])],1),e._v(" "),n("div",{staticClass:"messages-list mt-2 mt-md-3"},e._l(e.messages,(function(t){return n("conversation-item",{key:t.id,attrs:{item:t,"recipient-name":e.recipientName,"recipient-avatars":e.item.userIsDeleted?{}:e.item.recipientAvatars,"user-avatars":e.item.userAvatars,"thread-id":e.threadId,"user-statuses":e.userStatuses}})})),1),e._v(" "),e.isMoreButtonShown?n("div",{staticClass:"conversation-show-more-btn mt-2 mt-sm-5"},[n("load-more-btn",{attrs:{"text-btn":e.$t("load_more_messages"),"fetch-func":e.fetchMessages}})],1):e._e()])])}),[],!1,null,null,null);t.default=component.exports;x()(component,{UserStatus:n(1522).default,LoadMoreBtn:n(1686).default}),x()(component,{VAvatar:_.a,VBtn:y.a,VFileInput:w.a,VImg:k.a})}}]);