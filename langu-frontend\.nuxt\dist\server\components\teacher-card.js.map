{"version": 3, "file": "components/teacher-card.js", "sources": ["webpack:///./node_modules/vuetify/src/components/VSkeletonLoader/VSkeletonLoader.sass?044b", "webpack:///./node_modules/vuetify/src/components/VSkeletonLoader/VSkeletonLoader.sass", "webpack:///./components/LAvatar.vue?4c01", "webpack:///./components/LAvatar.vue?5e8e", "webpack:///./components/LAvatar.vue", "webpack:///./components/LAvatar.vue?62dd", "webpack:///./components/LAvatar.vue?aaac", "webpack:///./components/StarRating.vue?8931", "webpack:///./components/StarRating.vue?f5be", "webpack:///./components/TeacherCard.vue?2149", "webpack:///./components/TeacherCard.vue?0cd4", "webpack:///./components/LAvatar.vue?32f3", "webpack:///./components/LAvatar.vue?f57f", "webpack:///./components/TeacherCard.vue?9c9f", "webpack:///./components/TeacherCard.vue", "webpack:///./components/TeacherCard.vue?80db", "webpack:///./components/TeacherCard.vue?4833", "webpack:///../../../src/components/VSkeletonLoader/VSkeletonLoader.ts", "webpack:///./components/TeacherCard.vue?4bbc", "webpack:///./components/TeacherCard.vue?8281", "webpack:///./components/TeacherCard.vue?d0fd", "webpack:///./components/TeacherCard.vue?4ea0", "webpack:///./components/StarRating.vue?a800", "webpack:///./components/StarRating.vue?5f4c", "webpack:///./components/StarRating.vue", "webpack:///./components/StarRating.vue?9bca", "webpack:///./components/StarRating.vue?bad1"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./VSkeletonLoader.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"5f757930\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".theme--light.v-skeleton-loader .v-skeleton-loader__bone:after{background:linear-gradient(90deg,hsla(0,0%,100%,0),hsla(0,0%,100%,.3),hsla(0,0%,100%,0))}.theme--light.v-skeleton-loader .v-skeleton-loader__avatar,.theme--light.v-skeleton-loader .v-skeleton-loader__button,.theme--light.v-skeleton-loader .v-skeleton-loader__chip,.theme--light.v-skeleton-loader .v-skeleton-loader__divider,.theme--light.v-skeleton-loader .v-skeleton-loader__heading,.theme--light.v-skeleton-loader .v-skeleton-loader__image,.theme--light.v-skeleton-loader .v-skeleton-loader__text{background:rgba(0,0,0,.12)}.theme--light.v-skeleton-loader .v-skeleton-loader__actions,.theme--light.v-skeleton-loader .v-skeleton-loader__article,.theme--light.v-skeleton-loader .v-skeleton-loader__card-heading,.theme--light.v-skeleton-loader .v-skeleton-loader__card-text,.theme--light.v-skeleton-loader .v-skeleton-loader__date-picker,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-avatar,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-avatar-three-line,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-avatar-two-line,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-text,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-three-line,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-two-line,.theme--light.v-skeleton-loader .v-skeleton-loader__table-heading,.theme--light.v-skeleton-loader .v-skeleton-loader__table-tbody,.theme--light.v-skeleton-loader .v-skeleton-loader__table-tfoot,.theme--light.v-skeleton-loader .v-skeleton-loader__table-thead{background:#fff}.theme--dark.v-skeleton-loader .v-skeleton-loader__bone:after{background:linear-gradient(90deg,hsla(0,0%,100%,0),hsla(0,0%,100%,.05),hsla(0,0%,100%,0))}.theme--dark.v-skeleton-loader .v-skeleton-loader__avatar,.theme--dark.v-skeleton-loader .v-skeleton-loader__button,.theme--dark.v-skeleton-loader .v-skeleton-loader__chip,.theme--dark.v-skeleton-loader .v-skeleton-loader__divider,.theme--dark.v-skeleton-loader .v-skeleton-loader__heading,.theme--dark.v-skeleton-loader .v-skeleton-loader__image,.theme--dark.v-skeleton-loader .v-skeleton-loader__text{background:hsla(0,0%,100%,.12)}.theme--dark.v-skeleton-loader .v-skeleton-loader__actions,.theme--dark.v-skeleton-loader .v-skeleton-loader__article,.theme--dark.v-skeleton-loader .v-skeleton-loader__card-heading,.theme--dark.v-skeleton-loader .v-skeleton-loader__card-text,.theme--dark.v-skeleton-loader .v-skeleton-loader__date-picker,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-avatar,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-avatar-three-line,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-avatar-two-line,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-text,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-three-line,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-two-line,.theme--dark.v-skeleton-loader .v-skeleton-loader__table-heading,.theme--dark.v-skeleton-loader .v-skeleton-loader__table-tbody,.theme--dark.v-skeleton-loader .v-skeleton-loader__table-tfoot,.theme--dark.v-skeleton-loader .v-skeleton-loader__table-thead{background:#1e1e1e}.v-skeleton-loader{border-radius:8px;position:relative;vertical-align:top}.v-skeleton-loader__actions{padding:16px 16px 8px;text-align:right}.v-skeleton-loader__actions .v-skeleton-loader__button{display:inline-block}.v-application--is-ltr .v-skeleton-loader__actions .v-skeleton-loader__button:first-child{margin-right:12px}.v-application--is-rtl .v-skeleton-loader__actions .v-skeleton-loader__button:first-child{margin-left:12px}.v-skeleton-loader .v-skeleton-loader__list-item,.v-skeleton-loader .v-skeleton-loader__list-item-avatar,.v-skeleton-loader .v-skeleton-loader__list-item-avatar-three-line,.v-skeleton-loader .v-skeleton-loader__list-item-avatar-two-line,.v-skeleton-loader .v-skeleton-loader__list-item-text,.v-skeleton-loader .v-skeleton-loader__list-item-three-line,.v-skeleton-loader .v-skeleton-loader__list-item-two-line{border-radius:8px}.v-skeleton-loader .v-skeleton-loader__actions:after,.v-skeleton-loader .v-skeleton-loader__article:after,.v-skeleton-loader .v-skeleton-loader__card-avatar:after,.v-skeleton-loader .v-skeleton-loader__card-heading:after,.v-skeleton-loader .v-skeleton-loader__card-text:after,.v-skeleton-loader .v-skeleton-loader__card:after,.v-skeleton-loader .v-skeleton-loader__date-picker-days:after,.v-skeleton-loader .v-skeleton-loader__date-picker-options:after,.v-skeleton-loader .v-skeleton-loader__date-picker:after,.v-skeleton-loader .v-skeleton-loader__list-item-avatar-three-line:after,.v-skeleton-loader .v-skeleton-loader__list-item-avatar-two-line:after,.v-skeleton-loader .v-skeleton-loader__list-item-avatar:after,.v-skeleton-loader .v-skeleton-loader__list-item-text:after,.v-skeleton-loader .v-skeleton-loader__list-item-three-line:after,.v-skeleton-loader .v-skeleton-loader__list-item-two-line:after,.v-skeleton-loader .v-skeleton-loader__list-item:after,.v-skeleton-loader .v-skeleton-loader__paragraph:after,.v-skeleton-loader .v-skeleton-loader__sentences:after,.v-skeleton-loader .v-skeleton-loader__table-cell:after,.v-skeleton-loader .v-skeleton-loader__table-heading:after,.v-skeleton-loader .v-skeleton-loader__table-row-divider:after,.v-skeleton-loader .v-skeleton-loader__table-row:after,.v-skeleton-loader .v-skeleton-loader__table-tbody:after,.v-skeleton-loader .v-skeleton-loader__table-tfoot:after,.v-skeleton-loader .v-skeleton-loader__table-thead:after,.v-skeleton-loader .v-skeleton-loader__table:after{display:none}.v-application--is-ltr .v-skeleton-loader__article .v-skeleton-loader__heading{margin:16px 0 16px 16px}.v-application--is-rtl .v-skeleton-loader__article .v-skeleton-loader__heading{margin:16px 16px 0}.v-skeleton-loader__article .v-skeleton-loader__paragraph{padding:16px}.v-skeleton-loader__bone{border-radius:inherit;overflow:hidden;position:relative}.v-skeleton-loader__bone:after{-webkit-animation:loading 1.5s infinite;animation:loading 1.5s infinite;content:\\\"\\\";height:100%;left:0;position:absolute;right:0;top:0;transform:translateX(-100%);z-index:1}.v-skeleton-loader__avatar{border-radius:50%;height:48px;width:48px}.v-skeleton-loader__button{border-radius:4px;height:36px;width:64px}.v-skeleton-loader__card .v-skeleton-loader__image{border-radius:0}.v-skeleton-loader__card-heading .v-skeleton-loader__heading{margin:16px}.v-skeleton-loader__card-text{padding:16px}.v-skeleton-loader__chip{border-radius:16px;height:32px;width:96px}.v-skeleton-loader__date-picker{border-radius:inherit}.v-skeleton-loader__date-picker .v-skeleton-loader__list-item:first-child .v-skeleton-loader__text{max-width:88px;width:20%}.v-skeleton-loader__date-picker .v-skeleton-loader__heading{max-width:256px;width:40%}.v-skeleton-loader__date-picker-days{display:flex;flex-wrap:wrap;padding:0 12px;margin:0 auto}.v-skeleton-loader__date-picker-days .v-skeleton-loader__avatar{border-radius:8px;flex:1 1 auto;margin:4px;height:40px;width:40px}.v-skeleton-loader__date-picker-options{align-items:center;display:flex;padding:16px}.v-skeleton-loader__date-picker-options .v-skeleton-loader__avatar{height:40px;width:40px}.v-skeleton-loader__date-picker-options .v-skeleton-loader__avatar:nth-child(2){margin-left:auto}.v-application--is-ltr .v-skeleton-loader__date-picker-options .v-skeleton-loader__avatar:nth-child(2){margin-right:8px}.v-application--is-rtl .v-skeleton-loader__date-picker-options .v-skeleton-loader__avatar:nth-child(2){margin-left:8px}.v-skeleton-loader__date-picker-options .v-skeleton-loader__text.v-skeleton-loader__bone:first-child{margin-bottom:0;max-width:50%;width:456px}.v-skeleton-loader__divider{border-radius:1px;height:2px}.v-skeleton-loader__heading{border-radius:12px;height:24px;width:45%}.v-skeleton-loader__image{height:200px;border-radius:0}.v-skeleton-loader__image~.v-skeleton-loader__card-heading{border-radius:0}.v-skeleton-loader__image::first-child,.v-skeleton-loader__image::last-child{border-radius:inherit}.v-skeleton-loader__list-item{height:48px}.v-skeleton-loader__list-item-three-line{flex-wrap:wrap}.v-skeleton-loader__list-item-three-line>*{flex:1 0 100%;width:100%}.v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__avatar,.v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__avatar,.v-skeleton-loader__list-item-avatar .v-skeleton-loader__avatar{height:40px;width:40px}.v-skeleton-loader__list-item-avatar{height:48px}.v-skeleton-loader__list-item-avatar-two-line,.v-skeleton-loader__list-item-two-line{height:72px}.v-skeleton-loader__list-item-avatar-three-line,.v-skeleton-loader__list-item-three-line{height:88px}.v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__avatar{align-self:flex-start}.v-skeleton-loader__list-item,.v-skeleton-loader__list-item-avatar,.v-skeleton-loader__list-item-avatar-three-line,.v-skeleton-loader__list-item-avatar-two-line,.v-skeleton-loader__list-item-three-line,.v-skeleton-loader__list-item-two-line{align-content:center;align-items:center;display:flex;flex-wrap:wrap;padding:0 16px}.v-application--is-ltr .v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item-avatar .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item-three-line .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item-two-line .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item .v-skeleton-loader__avatar{margin-right:16px}.v-application--is-rtl .v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item-avatar .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item-three-line .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item-two-line .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item .v-skeleton-loader__avatar{margin-left:16px}.v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item-avatar .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-avatar .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item-three-line .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-three-line .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item-two-line .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-two-line .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item .v-skeleton-loader__text:only-child{margin-bottom:0}.v-skeleton-loader__paragraph,.v-skeleton-loader__sentences{flex:1 0 auto}.v-skeleton-loader__paragraph:not(:last-child){margin-bottom:6px}.v-skeleton-loader__paragraph .v-skeleton-loader__text:first-child{max-width:100%}.v-skeleton-loader__paragraph .v-skeleton-loader__text:nth-child(2){max-width:50%}.v-skeleton-loader__paragraph .v-skeleton-loader__text:nth-child(3),.v-skeleton-loader__sentences .v-skeleton-loader__text:nth-child(2){max-width:70%}.v-skeleton-loader__sentences:not(:last-child){margin-bottom:6px}.v-skeleton-loader__table-heading{align-items:center;display:flex;justify-content:space-between;padding:16px}.v-skeleton-loader__table-heading .v-skeleton-loader__heading{max-width:15%}.v-skeleton-loader__table-heading .v-skeleton-loader__text{max-width:40%}.v-skeleton-loader__table-thead{display:flex;justify-content:space-between;padding:16px}.v-skeleton-loader__table-thead .v-skeleton-loader__heading{max-width:5%}.v-skeleton-loader__table-tbody{padding:16px 16px 0}.v-skeleton-loader__table-tfoot{align-items:center;display:flex;justify-content:flex-end;padding:16px}.v-application--is-ltr .v-skeleton-loader__table-tfoot>*{margin-left:8px}.v-application--is-rtl .v-skeleton-loader__table-tfoot>*{margin-right:8px}.v-skeleton-loader__table-tfoot .v-skeleton-loader__avatar{height:40px;width:40px}.v-skeleton-loader__table-tfoot .v-skeleton-loader__text{margin-bottom:0}.v-skeleton-loader__table-tfoot .v-skeleton-loader__text:first-child{max-width:128px}.v-skeleton-loader__table-tfoot .v-skeleton-loader__text:nth-child(2){max-width:64px}.v-skeleton-loader__table-row{display:flex;justify-content:space-between}.v-skeleton-loader__table-cell{align-items:center;display:flex;height:48px;width:88px}.v-skeleton-loader__table-cell .v-skeleton-loader__text{margin-bottom:0}.v-skeleton-loader__text{border-radius:6px;flex:1 0 auto;height:12px;margin-bottom:6px}.v-skeleton-loader--boilerplate .v-skeleton-loader__bone:after{display:none}.v-skeleton-loader--is-loading{overflow:hidden}.v-skeleton-loader--tile,.v-skeleton-loader--tile .v-skeleton-loader__bone{border-radius:0}@-webkit-keyframes loading{to{transform:translateX(100%)}}@keyframes loading{to{transform:translateX(100%)}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./LAvatar.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"15ed23b1\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:['l-avatar', (\"l-avatar--\" + _vm.sizeClass)]},[_c('v-avatar',{class:{\n      'no-avatar': !_vm.clicked && !_vm.avatars[_vm.avatarSizes[0]],\n    },on:{\"click\":function($event){$event.stopPropagation();return (function () { return (_vm.clicked ? _vm.$emit('show-full-avatar') : false); }).apply(null, arguments)}}},[_c('v-img',{attrs:{\"src\":_vm.srcAvatar,\"srcset\":_vm.srcAvatarsSet,\"options\":{ rootMargin: '50%' },\"eager\":_vm.eager},scopedSlots:_vm._u([{key:\"placeholder\",fn:function(){return [_c('v-skeleton-loader',{attrs:{\"type\":\"avatar\"}})]},proxy:true}])})],1),_vm._ssrNode(\" \"),(_vm.languagesTaught.length)?_vm._ssrNode(\"<div class=\\\"flags\\\">\",\"</div>\",_vm._l((_vm.languagesTaught),function(language){return _vm._ssrNode(\"<div class=\\\"flags-item\\\">\",\"</div>\",[_c('v-img',{attrs:{\"src\":require((\"~/assets/images/flags/\" + (language.isoCode) + \".svg\")),\"contain\":\"\",\"options\":{ rootMargin: '50%' }}})],1)}),0):_vm._e()],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'LAvatar',\n  props: {\n    avatars: {\n      type: Object,\n      required: true,\n    },\n    languagesTaught: {\n      type: Array,\n      required: true,\n    },\n    size: {\n      type: String,\n      default: 'lg',\n    },\n    eager: {\n      type: Boolean,\n      default: true,\n    },\n    defaultAvatar: {\n      type: String,\n      default: 'avatar.png',\n    },\n    clicked: {\n      type: <PERSON>olean,\n      default: false,\n    },\n  },\n  computed: {\n    sizeClass() {\n      let size\n\n      switch (this.size) {\n        case 'md':\n          size = 'medium'\n          break\n        case 'lg':\n          size = 'large'\n          break\n        default:\n          size = 'large'\n      }\n\n      return size\n    },\n    avatarSizes() {\n      return Object.keys(this?.avatars?.avatarsResized)\n    },\n    srcAvatar() {\n      const avatarFetchUrl =\n        process.env.NUXT_ENV_NODE_ENV === 'development' ||\n        process.env.NUXT_ENV_NODE_ENV === 'production'\n          ? process?.env?.NUXT_ENV_URL ?? 'https://langu.io'\n          : ''\n\n      // Uncomment above code for pushing to staging and comment below line of code\n      // const avatarFetchUrl = 'https://langu.io'\n\n      // console.log('PhotoURL -> ', `${avatarFetchUrl + this?.avatars?.avatar}`)\n\n      return this.avatars?.avatar\n        ? `${avatarFetchUrl + this?.avatars?.avatar}`\n        : this?.avatars && typeof this?.avatars === 'object'\n        ? this.srcAvatarSingle\n        : require(`~/assets/images/homepage/${this.defaultAvatar}`)\n    },\n    srcAvatarsSet() {\n      let result = ''\n      if (this?.avatars?.avatarsResized) {\n        const avatSizes = Object?.keys(this?.avatars?.avatarsResized)\n        for (let i = 0; i < avatSizes?.length; i++) {\n          if (this.avatars?.avatarsResized[avatSizes[i]]) {\n            result += `${this.avatars?.avatarsResized[avatSizes[i]]} ${i + 1}x`\n            if (i < avatSizes?.length - 1) {\n              result += ', '\n            }\n          }\n        }\n        // console.log('Result -> ', result)\n      }\n      return result?.length > 0 ? result : ''\n    },\n    srcAvatarSingle() {\n      const keySet = Object.keys(this?.avatars)\n      const resIndex = Math.ceil(keySet.length / 2)\n      return this?.avatars[`${keySet[resIndex]}`] ?? ''\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./LAvatar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./LAvatar.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./LAvatar.vue?vue&type=template&id=0838f458&\"\nimport script from \"./LAvatar.vue?vue&type=script&lang=js&\"\nexport * from \"./LAvatar.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./LAvatar.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"6e852b06\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VAvatar } from 'vuetify/lib/components/VAvatar';\nimport { VImg } from 'vuetify/lib/components/VImg';\nimport { VSkeletonLoader } from 'vuetify/lib/components/VSkeletonLoader';\ninstallComponents(component, {VAvatar,VImg,VSkeletonLoader})\n", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StarRating.vue?vue&type=style&index=0&id=1645fb89&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".score[data-v-1645fb89]{display:flex;align-items:center;height:18px;font-size:12px;line-height:.8;font-weight:700;letter-spacing:.1px;color:var(--v-orange-base)}@media only screen and (max-width:1215px){.score[data-v-1645fb89]{justify-content:flex-end}}.score>div[data-v-1645fb89]{width:65px;display:flex;margin-left:2px}@media only screen and (max-width:1215px){.score>div[data-v-1645fb89]{width:auto}}.score svg[data-v-1645fb89]:not(:first-child){margin-left:1px}.score--large[data-v-1645fb89]{font-size:18px}@media only screen and (max-width:1215px){.score--large[data-v-1645fb89]{font-size:16px}}.score--large>div[data-v-1645fb89]{width:112px;margin-left:8px}@media only screen and (max-width:1215px){.score--large>div[data-v-1645fb89]{width:84px}}.score--large svg[data-v-1645fb89]:not(:first-child){margin-left:3px}@media only screen and (max-width:1215px){.score--large svg[data-v-1645fb89]:not(:first-child){margin-left:1px}}@media only screen and (max-width:1215px){.score--large svg[data-v-1645fb89]{width:16px!important;height:16px!important}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TeacherCard.vue?vue&type=style&index=0&id=8c38ed5c&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"6416867f\", content, true, context)\n};", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TeacherCard.vue?vue&type=style&index=1&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"374a6c92\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./LAvatar.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".l-avatar .flags{position:absolute}.l-avatar .flags-item{border-radius:8px;filter:drop-shadow(2px 2px 12px rgba(146,138,138,.2));overflow:hidden}.l-avatar--medium{--avatar-size:96px}@media only screen and (max-width:479px){.l-avatar--medium{--avatar-size:74px}}.l-avatar--medium .flags{right:10px;top:13px}@media only screen and (max-width:1215px){.l-avatar--medium .flags{top:10px;right:6px}}@media only screen and (max-width:479px){.l-avatar--medium .flags{top:6px;right:10px}}.l-avatar--medium .flags-item{margin-bottom:6px}@media only screen and (max-width:1215px){.l-avatar--medium .flags-item{margin-bottom:6px}}.l-avatar--medium .flags-item .v-image{width:45px!important;height:32px!important}@media only screen and (max-width:1215px){.l-avatar--medium .flags-item .v-image{width:39px!important;height:28px!important}}.l-avatar--large{--avatar-size:140px;width:220px}@media only screen and (max-width:1215px){.l-avatar--large{--avatar-size:120px}}@media only screen and (max-width:991px){.l-avatar--large{--avatar-size:80px}}@media only screen and (max-width:1215px){.l-avatar--large{width:190px}}@media only screen and (max-width:991px){.l-avatar--large{width:125px}}.l-avatar--large .flags{right:32px;top:16px}@media only screen and (max-width:1215px){.l-avatar--large .flags{top:12px}}@media only screen and (max-width:991px){.l-avatar--large .flags{top:6px;right:18px}}.l-avatar--large .flags-item{margin-bottom:16px}.l-avatar--large .flags-item .v-image{width:62px!important;height:44px!important}@media only screen and (max-width:1215px){.l-avatar--large .flags-item .v-image{width:50px!important;height:38px!important}}@media only screen and (max-width:991px){.l-avatar--large .flags-item .v-image{width:35px!important;height:26px!important}}.l-avatar .v-avatar{width:var(--avatar-size)!important;height:var(--avatar-size)!important;z-index:2}.l-avatar .v-avatar:not(.no-avatar){cursor:pointer}.l-avatar .v-avatar .v-skeleton-loader>div{width:var(--avatar-size)!important;height:var(--avatar-size)!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"teacher-card\"},[_c('nuxt-link',{attrs:{\"to\":_vm.link}}),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"teacher-card-top\\\" data-v-8c38ed5c>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"teacher-card-avatar\\\" data-v-8c38ed5c>\",\"</div>\",[_c('l-avatar',{staticClass:\"teacher-card-avatar\",attrs:{\"avatars\":_vm.teacher,\"avatars-resized\":_vm.teacher.avatarsResized,\"languages-taught\":_vm.teacher.languagesTaught,\"size\":\"md\",\"eager\":false}})],1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"teacher-card-top-helper\\\" data-v-8c38ed5c>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"teacher-card-name\\\" data-v-8c38ed5c>\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.name)+\"\\n      \")+\"</div> \"),_vm._ssrNode(\"<div class=\\\"teacher-card-rating\\\" data-v-8c38ed5c>\",\"</div>\",[(_vm.teacher.averageRatings === 0)?[_vm._ssrNode(\"<div class=\\\"new-verified-teacher\\\" data-v-8c38ed5c><div class=\\\"new-verified-teacher-icon\\\" data-v-8c38ed5c><svg width=\\\"612\\\" height=\\\"612\\\" viewBox=\\\"0 0 612 612\\\" data-v-8c38ed5c><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#verified-user\")))+\" data-v-8c38ed5c></use></svg></div> <span data-v-8c38ed5c>\"+_vm._ssrEscape(_vm._s(_vm.$t('new_verified_teacher')))+\"</span></div>\")]:[_c('star-rating',{attrs:{\"value\":_vm.teacher.averageRatings}}),_vm._ssrNode(\" <div class=\\\"review\\\" data-v-8c38ed5c>\"+_vm._ssrEscape(\"\\n            (\"+_vm._s(_vm.$tc('review', _vm.teacher.countFeedbacks))+\")\\n          \")+\"</div>\")]],2)],2)],2),_vm._ssrNode(\" <div class=\\\"teacher-card-center\\\" data-v-8c38ed5c><div class=\\\"teacher-card-description\\\" data-v-8c38ed5c>\"+(_vm._s(_vm.formatContentWithHtml(_vm.teacher.description)))+\"</div> \"+((_vm.teacher.specialities.length)?(\"<ul class=\\\"teacher-card-specialities\\\" data-v-8c38ed5c>\"+(_vm._ssrList((_vm.teacher.specialities.slice(0, 3)),function(specialization,index){return (\"<li data-v-8c38ed5c><svg width=\\\"15\\\" height=\\\"15\\\" viewBox=\\\"0 0 15 15\\\" data-v-8c38ed5c><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#\" + (specialization.speciality.icon))))+\" data-v-8c38ed5c></use></svg>\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.getTranslatedName(specialization.speciality))+\"\\n      \")+\"</li>\")}))+\"</ul>\"):\"<!---->\")+\"</div> \"),_vm._ssrNode(\"<div class=\\\"teacher-card-bottom\\\" data-v-8c38ed5c>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"teacher-card-price\\\" data-v-8c38ed5c>\"+_vm._ssrEscape(\"\\n      \"+_vm._s(_vm.$t('from'))+\"\\n      \")+\"<span data-v-8c38ed5c>\"+_vm._ssrEscape(_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.getPrice(_vm.teacher.pricePerHourOfLesson))+\"/\")+\"</span>hr\\n    </div> \"),(_vm.teacher.acceptNewStudents && _vm.teacher.freeSlots)?[(_vm.teacher.bookLesson.freeTrial)?[_c('v-btn',{attrs:{\"to\":_vm.link,\"small\":\"\",\"color\":\"success\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.$t('free_trial'))+\"\\n        \")])]:[(_vm.teacher.bookLesson.price)?_c('v-btn',{attrs:{\"to\":_vm.link,\"small\":\"\",\"color\":\"orange\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.$t('trial'))+\":  \"+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.getPrice(_vm.teacher.bookLesson.price))+\"\\n        \")]):_vm._e()]]:[_c('v-btn',{attrs:{\"to\":_vm.link,\"small\":\"\",\"color\":\"greyDark\"}},[_vm._v(\"\\n        \"+_vm._s(_vm.$t('full_schedule'))+\"\\n      \")])]],2)],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getPrice } from '~/helpers'\n\nimport LAvatar from '~/components/LAvatar'\nimport StarRating from '~/components/StarRating'\n\nexport default {\n  name: 'TeacherCard',\n  components: { LAvatar, StarRating },\n  filters: {\n    specialitiesStr(arr) {\n      let str = ''\n\n      for (let i = 0; i < 3; i++) {\n        str += arr[i]\n\n        if (i < 3 - 1) {\n          str += ', '\n        }\n      }\n\n      return str\n    },\n  },\n  props: {\n    teacher: {\n      type: Object,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      getPrice,\n    }\n  },\n  computed: {\n    currentCurrencySymbol() {\n      return this.$store.getters['currency/currentCurrencySymbol']\n    },\n    link() {\n      return this.teacher.profileLink\n    },\n    name() {\n      // Split the string into words by spaces and set first word as array element\n      return [\n        `${this.teacher.firstName?.split(' ')[0]?.toLowerCase()}`,\n        `${this.teacher.lastName?.split(' ')[0]?.toLowerCase()}`,\n      ]\n        .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize the first letter of each word\n        .join(' ') // Join the words back together with spaces\n    },\n  },\n  methods: {\n    getTranslatedName(speciality) {\n      const currentLocale = this.$i18n.locale\n      const translation = speciality.translations.find(\n        (t) => t.locale === currentLocale && t.field === 'name'\n      )\n      return translation ? translation.content : speciality.name\n    },\n    formatContentWithHtml(content) {\n      if (!content) return null\n\n      const contentArray = content.split(/\\n/)\n      let output = ''\n      let isListStarted = false\n\n      for (let i = 0; i < contentArray.length; i++) {\n        const contentLine = contentArray[i]\n\n        if (!contentLine.trim().length) {\n          if (isListStarted) {\n            isListStarted = false\n            output += '</ul>'\n          }\n          continue\n        }\n\n        if (contentLine.substr(0, 1) !== '*') {\n          if (isListStarted) {\n            isListStarted = false\n            output += '</ul>'\n          }\n\n          output += contentLine + ' '\n          continue\n        }\n\n        if (!isListStarted && contentLine.substr(0, 1) === '*') {\n          output += '<ul>'\n          isListStarted = true\n        }\n\n        output += '<li>' + contentLine.substr(1) + '</li>'\n      }\n\n      if (isListStarted) {\n        output += '</ul>'\n      }\n\n      return output\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TeacherCard.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TeacherCard.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TeacherCard.vue?vue&type=template&id=8c38ed5c&scoped=true&\"\nimport script from \"./TeacherCard.vue?vue&type=script&lang=js&\"\nexport * from \"./TeacherCard.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./TeacherCard.vue?vue&type=style&index=0&id=8c38ed5c&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\nvar style1 = require(\"./TeacherCard.vue?vue&type=style&index=1&lang=scss&\")\nif (style1.__inject__) style1.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"8c38ed5c\",\n  \"0f76992a\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {LAvatar: require('D:/languworks/langu-frontend/components/LAvatar.vue').default,StarRating: require('D:/languworks/langu-frontend/components/StarRating.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\ninstallComponents(component, {VBtn})\n", "// Styles\nimport './VSkeletonLoader.sass'\n\n// Mixins\nimport Elevatable from '../../mixins/elevatable'\nimport Measurable from '../../mixins/measurable'\nimport Themeable from '../../mixins/themeable'\n\n// Utilities\nimport mixins from '../../util/mixins'\n\n// Types\nimport { VNode } from 'vue'\nimport { getSlot } from '../../util/helpers'\nimport { PropValidator } from 'vue/types/options'\n\nexport interface HTMLSkeletonLoaderElement extends HTMLElement {\n  _initialStyle?: {\n    display: string | null\n    transition: string\n  }\n}\n\n/* @vue/component */\nexport default mixins(\n  Elevatable,\n  Measurable,\n  Themeable,\n).extend({\n  name: 'VSkeletonLoader',\n\n  props: {\n    boilerplate: Boolean,\n    loading: Boolean,\n    tile: Boolean,\n    transition: String,\n    type: String,\n    types: {\n      type: Object,\n      default: () => ({}),\n    } as PropValidator<Record<string, string>>,\n  },\n\n  computed: {\n    attrs (): object {\n      if (!this.isLoading) return this.$attrs\n\n      return !this.boilerplate ? {\n        'aria-busy': true,\n        'aria-live': 'polite',\n        role: 'alert',\n        ...this.$attrs,\n      } : {}\n    },\n    classes (): object {\n      return {\n        'v-skeleton-loader--boilerplate': this.boilerplate,\n        'v-skeleton-loader--is-loading': this.isLoading,\n        'v-skeleton-loader--tile': this.tile,\n        ...this.themeClasses,\n        ...this.elevationClasses,\n      }\n    },\n    isLoading (): boolean {\n      return !('default' in this.$scopedSlots) || this.loading\n    },\n    rootTypes (): Record<string, string> {\n      return {\n        actions: 'button@2',\n        article: 'heading, paragraph',\n        avatar: 'avatar',\n        button: 'button',\n        card: 'image, card-heading',\n        'card-avatar': 'image, list-item-avatar',\n        'card-heading': 'heading',\n        chip: 'chip',\n        'date-picker': 'list-item, card-heading, divider, date-picker-options, date-picker-days, actions',\n        'date-picker-options': 'text, avatar@2',\n        'date-picker-days': 'avatar@28',\n        heading: 'heading',\n        image: 'image',\n        'list-item': 'text',\n        'list-item-avatar': 'avatar, text',\n        'list-item-two-line': 'sentences',\n        'list-item-avatar-two-line': 'avatar, sentences',\n        'list-item-three-line': 'paragraph',\n        'list-item-avatar-three-line': 'avatar, paragraph',\n        paragraph: 'text@3',\n        sentences: 'text@2',\n        table: 'table-heading, table-thead, table-tbody, table-tfoot',\n        'table-heading': 'heading, text',\n        'table-thead': 'heading@6',\n        'table-tbody': 'table-row-divider@6',\n        'table-row-divider': 'table-row, divider',\n        'table-row': 'table-cell@6',\n        'table-cell': 'text',\n        'table-tfoot': 'text@2, avatar@2',\n        text: 'text',\n        ...this.types,\n      }\n    },\n  },\n\n  methods: {\n    genBone (text: string, children: VNode[]) {\n      return this.$createElement('div', {\n        staticClass: `v-skeleton-loader__${text} v-skeleton-loader__bone`,\n      }, children)\n    },\n    genBones (bone: string): VNode[] {\n      // e.g. 'text@3'\n      const [type, length] = bone.split('@') as [string, number]\n      const generator = () => this.genStructure(type)\n\n      // Generate a length array based upon\n      // value after @ in the bone string\n      return Array.from({ length }).map(generator)\n    },\n    // Fix type when this is merged\n    // https://github.com/microsoft/TypeScript/pull/33050\n    genStructure (type?: string): any {\n      let children = []\n      type = type || this.type || ''\n      const bone = this.rootTypes[type] || ''\n\n      // End of recursion, do nothing\n      /* eslint-disable-next-line no-empty, brace-style */\n      if (type === bone) {}\n      // Array of values - e.g. 'heading, paragraph, text@2'\n      else if (type.indexOf(',') > -1) return this.mapBones(type)\n      // Array of values - e.g. 'paragraph@4'\n      else if (type.indexOf('@') > -1) return this.genBones(type)\n      // Array of values - e.g. 'card@2'\n      else if (bone.indexOf(',') > -1) children = this.mapBones(bone)\n      // Array of values - e.g. 'list-item@2'\n      else if (bone.indexOf('@') > -1) children = this.genBones(bone)\n      // Single value - e.g. 'card-heading'\n      else if (bone) children.push(this.genStructure(bone))\n\n      return [this.genBone(type, children)]\n    },\n    genSkeleton () {\n      const children = []\n\n      if (!this.isLoading) children.push(getSlot(this))\n      else children.push(this.genStructure())\n\n      /* istanbul ignore else */\n      if (!this.transition) return children\n\n      /* istanbul ignore next */\n      return this.$createElement('transition', {\n        props: {\n          name: this.transition,\n        },\n        // Only show transition when\n        // content has been loaded\n        on: {\n          afterEnter: this.resetStyles,\n          beforeEnter: this.onBeforeEnter,\n          beforeLeave: this.onBeforeLeave,\n          leaveCancelled: this.resetStyles,\n        },\n      }, children)\n    },\n    mapBones (bones: string) {\n      // Remove spaces and return array of structures\n      return bones.replace(/\\s/g, '').split(',').map(this.genStructure)\n    },\n    onBeforeEnter (el: HTMLSkeletonLoaderElement) {\n      this.resetStyles(el)\n\n      if (!this.isLoading) return\n\n      el._initialStyle = {\n        display: el.style.display,\n        transition: el.style.transition,\n      }\n\n      el.style.setProperty('transition', 'none', 'important')\n    },\n    onBeforeLeave (el: HTMLSkeletonLoaderElement) {\n      el.style.setProperty('display', 'none', 'important')\n    },\n    resetStyles (el: HTMLSkeletonLoaderElement) {\n      if (!el._initialStyle) return\n\n      el.style.display = el._initialStyle.display || ''\n      el.style.transition = el._initialStyle.transition\n\n      delete el._initialStyle\n    },\n  },\n\n  render (h): VNode {\n    return h('div', {\n      staticClass: 'v-skeleton-loader',\n      attrs: this.attrs,\n      on: this.$listeners,\n      class: this.classes,\n      style: this.isLoading ? this.measurableStyles : undefined,\n    }, [this.genSkeleton()])\n  },\n})\n", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TeacherCard.vue?vue&type=style&index=0&id=8c38ed5c&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".teacher-card[data-v-8c38ed5c]{position:relative;display:flex;flex-direction:column;justify-content:space-between;width:100%;height:100%;padding:20px 30px;box-shadow:0 8px 17px rgba(17,46,90,.1);border-radius:20px;background-color:#fff}@media only screen and (max-width:767px){.teacher-card[data-v-8c38ed5c]{max-width:478px}}.teacher-card>a[data-v-8c38ed5c]{display:block;position:absolute;top:0;left:0;width:100%;height:100%;border-radius:20px;z-index:3}.teacher-card-top[data-v-8c38ed5c]{display:flex}.teacher-card-top-helper[data-v-8c38ed5c]{display:flex;justify-content:space-between;flex-wrap:wrap;width:calc(100% - 140px);margin-bottom:10px;padding:12px 0;border-bottom:1px solid #ecf3ff}@media only screen and (max-width:1215px){.teacher-card-top-helper[data-v-8c38ed5c]{width:calc(100% - 130px)}}@media only screen and (max-width:479px){.teacher-card-top-helper[data-v-8c38ed5c]{width:calc(100% - 115px)}}@media only screen and (max-width:1099px)and (min-width:991px),screen and (max-width:439px),screen and (max-width:799px)and (min-width:767px){.teacher-card-top-helper[data-v-8c38ed5c]{flex-direction:column;align-items:flex-start}}.teacher-card-center[data-v-8c38ed5c]{display:flex;justify-content:space-between;padding:15px 0 16px}@media only screen and (max-width:1215px){.teacher-card-center[data-v-8c38ed5c]{flex-direction:column}}.teacher-card-bottom[data-v-8c38ed5c]{display:flex;justify-content:space-between;align-items:center;padding-top:16px;border-top:1px solid #ecf3ff}.teacher-card-bottom .v-btn[data-v-8c38ed5c]{z-index:4}.teacher-card-name[data-v-8c38ed5c]{padding-right:20px;font-size:20px;font-weight:700;line-height:1.4}@media only screen and (max-width:1215px){.teacher-card-name[data-v-8c38ed5c]{padding-right:10px;font-size:16px}}@media only screen and (max-width:991px){.teacher-card-name[data-v-8c38ed5c]{padding-right:15px;font-size:18px}}.teacher-card-rating[data-v-8c38ed5c]{padding-top:5px}@media only screen and (max-width:1215px){.teacher-card-rating[data-v-8c38ed5c]{padding-top:3px}}.teacher-card-rating .new-verified-teacher[data-v-8c38ed5c]{position:relative;width:112px;padding-left:18px;font-size:10px;font-weight:500;text-align:left}@media only screen and (max-width:1215px){.teacher-card-rating .new-verified-teacher[data-v-8c38ed5c]{width:80px;font-size:9px}}.teacher-card-rating .new-verified-teacher-icon[data-v-8c38ed5c]{position:absolute;left:0;width:16px;height:16px}.teacher-card-rating .new-verified-teacher-icon svg[data-v-8c38ed5c]{width:100%;height:100%}.teacher-card-rating .review[data-v-8c38ed5c]{margin-top:5px;color:rgba(45,45,45,.7);font-size:12px;font-weight:500;line-height:18px;letter-spacing:.1px;text-align:right}@media only screen and (max-width:1099px)and (min-width:991px),screen and (max-width:439px),screen and (max-width:799px)and (min-width:767px){.teacher-card-rating .review[data-v-8c38ed5c]{margin-top:0;text-align:left}}.teacher-card-description[data-v-8c38ed5c]{width:calc(100% - 150px);font-weight:400;font-size:16px;line-height:1.5;color:var(--v-dark-lighten3)}@media only screen and (max-width:1215px){.teacher-card-description[data-v-8c38ed5c]{width:100%}}@media only screen and (max-width:479px){.teacher-card-description[data-v-8c38ed5c]{font-size:14px}}.teacher-card-specialities[data-v-8c38ed5c]{width:150px;padding-left:0;font-size:13px;font-weight:300;list-style-type:none}@media only screen and (max-width:1215px){.teacher-card-specialities[data-v-8c38ed5c]{display:flex;flex-wrap:wrap;width:100%;margin-top:16px}}@media only screen and (max-width:479px){.teacher-card-specialities[data-v-8c38ed5c]{width:100%;margin-top:16px}}.teacher-card-specialities li[data-v-8c38ed5c]{position:relative;margin-bottom:12px;padding-left:40px;line-height:1.15}@media only screen and (max-width:1215px){.teacher-card-specialities li[data-v-8c38ed5c]{width:50%;padding:0 15px 0 20px}}@media only screen and (max-width:991px){.teacher-card-specialities li[data-v-8c38ed5c]{margin-bottom:10px}}.teacher-card-specialities li[data-v-8c38ed5c]:last-child{margin-bottom:0}.teacher-card-specialities li svg[data-v-8c38ed5c]{position:absolute;left:15px;top:-1px}@media only screen and (max-width:1215px){.teacher-card-specialities li svg[data-v-8c38ed5c]{left:0}}.teacher-card-price[data-v-8c38ed5c]{padding-right:5px;font-size:14px}.teacher-card-price span[data-v-8c38ed5c]{font-size:17px}.teacher-card-specialities[data-v-8c38ed5c]{font-size:16px!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TeacherCard.vue?vue&type=style&index=1&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".teacher-card-avatar{position:relative;left:-4px;width:140px;padding-right:11px}@media only screen and (max-width:1215px){.teacher-card-avatar{width:130px}}@media only screen and (max-width:479px){.teacher-card-avatar{width:110px}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StarRating.vue?vue&type=style&index=0&id=1645fb89&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"1f907d7b\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:['score', { 'score--large': _vm.large }]},[_vm._ssrNode(\"<span data-v-1645fb89>\"+_vm._ssrEscape(_vm._s(_vm.value_.toFixed(1)))+\"</span> <div data-v-1645fb89>\"+(_vm._ssrList((_vm.stars),function(i){return (\"<svg\"+(_vm._ssrAttr(\"width\",_vm.width))+(_vm._ssrAttr(\"height\",_vm.height))+\" viewBox=\\\"0 0 12 12\\\" data-v-1645fb89><use\"+(_vm._ssrAttr(\"xlink:href\",_vm.iconFilledStar))+\" data-v-1645fb89></use></svg>\")}))+\" \"+((_vm.isHasHalf)?(\"<svg\"+(_vm._ssrAttr(\"width\",_vm.width))+(_vm._ssrAttr(\"height\",_vm.height))+\" viewBox=\\\"0 0 12 12\\\" data-v-1645fb89><use\"+(_vm._ssrAttr(\"xlink:href\",_vm.iconFilledHalfStar))+\" data-v-1645fb89></use></svg>\"):\"<!---->\")+\"</div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'StarRating',\n  props: {\n    value: {\n      type: Number,\n      required: true,\n    },\n    large: {\n      type: Boolean,\n      required: false,\n    },\n  },\n  data() {\n    return {\n      iconFilledStar: `${require('~/assets/images/icon-sprite.svg')}#filledStar`,\n      iconFilledHalfStar: `${require('~/assets/images/icon-sprite.svg')}#filledHalfStar`,\n    }\n  },\n  computed: {\n    width() {\n      return this.large ? 20 : 12\n    },\n    height() {\n      return this.large ? 20 : 12\n    },\n    value_() {\n      return Math.round(this.value * 10) / 10\n    },\n    isRoundToLess() {\n      const rest = Math.round((this.value_ % 1) * 10)\n\n      return rest <= 5 && rest !== 0\n    },\n    roundToLessHalf() {\n      return this.isRoundToLess\n        ? Math.floor(this.value_ * 2) / 2\n        : Math.ceil(this.value_ * 2) / 2\n    },\n    stars() {\n      return this.isRoundToLess\n        ? Math.floor(this.roundToLessHalf)\n        : Math.ceil(this.roundToLessHalf)\n    },\n    isHasHalf() {\n      return (this.isRoundToLess && this.value_ !== 5) || this.value_ < 0.5\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StarRating.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StarRating.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./StarRating.vue?vue&type=template&id=1645fb89&scoped=true&\"\nimport script from \"./StarRating.vue?vue&type=script&lang=js&\"\nexport * from \"./StarRating.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./StarRating.vue?vue&type=style&index=0&id=1645fb89&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"1645fb89\",\n  \"743e07b2\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AArBA;AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AARA;AACA;AAUA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAOA;AAEA;AACA;AACA;AAKA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AA3DA;AA5BA;;ACpCA;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;AAeA;AACA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAMA;AACA;AAhBA;AAiBA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlDA;AA9CA;;ACxGA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AClCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAIA;AAUA;AACA;AAAA;AAKA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AANA;AAYA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAJA;AAJA;AACA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAZA;AACA;AAmBA;AACA;AArBA;AACA;AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA;AAiCA;AACA;AA1DA;AA4DA;AACA;AACA;AACA;AADA;AAFA;AACA;AAKA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AADA;AAAA;AAAA;AAbA;AACA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAEA;AAEA;AAEA;AAEA;AACA;AAGA;AApCA;AACA;AAqCA;AACA;AAEA;AAGA;AACA;AAAA;AAEA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AANA;AAhDA;AACA;AA6DA;AACA;AACA;AAhEA;AACA;AAiEA;AACA;AAEA;AAEA;AACA;AACA;AAFA;AAKA;AA5EA;AACA;AA6EA;AACA;AA/EA;AACA;AAgFA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAzFA;AACA;AA0FA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AA/KA;;;;;;;;AC5BA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AACA;AASA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AA5BA;AAlBA;;ACtBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}