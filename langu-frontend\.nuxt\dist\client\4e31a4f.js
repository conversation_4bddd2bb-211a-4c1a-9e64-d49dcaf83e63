(window.webpackJsonp=window.webpackJsonp||[]).push([[174],{2223:function(e,r,t){"use strict";t.r(r);t(7),t(23);var n={middleware:function(e){var r=e.route,t=e.redirect,n=Object.keys(r.query).length,c="/teacher/".concat(r.params.slug),i=0;if(n>0)for(var o in c+="?",r.query)i+=1,c+="".concat(o,"=").concat(r.query[o]),i<n&&(c+="&");return t(c)}},c=t(22),component=Object(c.a)(n,(function(){var e=this.$createElement;return(this._self._c||e)("div")}),[],!1,null,null,null);r.default=component.exports}}]);