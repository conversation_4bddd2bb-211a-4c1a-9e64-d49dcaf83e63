(window.webpackJsonp=window.webpackJsonp||[]).push([[24],{2240:function(t,C,e){"use strict";e.r(C);var n=e(22),component=Object(n.a)({},(function(){var t=this,C=t.$createElement,e=t._self._c||C;return e("svg",{attrs:{width:"19",height:"19",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M7.8667 3.02318L0.224453 6.2827C0.0930162 6.33894 0 6.48666 0 6.63923C0 6.79181 0.0930162 6.93953 0.224453 6.99573L0.890928 7.27735V9.4104C0.578629 9.55984 0.357748 9.89626 0.357748 10.2852C0.357748 10.8101 0.759811 11.2439 1.24638 11.2439C1.73295 11.2439 2.13502 10.8101 2.13502 10.2852C2.13502 9.89626 1.91413 9.5599 1.60184 9.4104V7.58288L2.84592 8.11611V11.0521C2.84592 11.4366 3.06788 11.7264 3.35134 11.9808C3.63477 12.2352 4.00925 12.4635 4.46768 12.6639C5.38454 13.0646 6.62471 13.3529 8 13.3529C9.37529 13.3529 10.6155 13.0646 11.5323 12.6639C11.9908 12.4636 12.3653 12.2352 12.6487 11.9809C12.9321 11.7264 13.1541 11.4366 13.1541 11.0521V8.11614L15.7755 6.99567C15.907 6.93947 16 6.79171 16 6.63914C16 6.48656 15.907 6.33891 15.7755 6.28267L8.13329 3.02311C8.01885 2.98257 7.93814 3.00378 7.8667 3.02318V3.02318ZM8 3.79012L14.6759 6.64221L8 9.48828L1.32415 6.64224L8 3.79012ZM3.55683 8.41578L7.8667 10.2553C7.95228 10.2921 8.04772 10.2921 8.13329 10.2553L12.4432 8.41578V11.0522C12.4432 11.069 12.3941 11.2134 12.1933 11.3937C11.9923 11.574 11.6691 11.7782 11.2602 11.9569C10.4423 12.3143 9.28318 12.586 8 12.586C6.71682 12.586 5.55768 12.3143 4.73982 11.9569C4.33093 11.7783 4.00771 11.574 3.80675 11.3936C3.60585 11.2133 3.55683 11.069 3.55683 11.0521V8.41575V8.41578ZM1.24638 10.0935C1.34875 10.0935 1.42411 10.1748 1.42411 10.2852C1.42411 10.3956 1.34875 10.4769 1.24638 10.4769C1.14401 10.4769 1.06865 10.3956 1.06865 10.2852C1.06865 10.1748 1.14401 10.0935 1.24638 10.0935Z",fill:"url(#paint0_linear)"}}),t._v(" "),e("defs",[e("linearGradient",{attrs:{id:"paint0_linear",x1:"0",y1:"0",x2:"20.1721",y2:"15.9625",gradientUnits:"userSpaceOnUse"}},[e("stop",{attrs:{"stop-color":"#80B622"}}),t._v(" "),e("stop",{attrs:{offset:"1","stop-color":"#3C87F8"}})],1)],1)])}),[],!1,null,null,null);C.default=component.exports}}]);