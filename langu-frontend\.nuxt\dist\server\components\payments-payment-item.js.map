{"version": 3, "file": "components/payments-payment-item.js", "sources": ["webpack:///./components/payments/PaymentItem.vue?6839", "webpack:///./components/payments/PaymentItem.vue?6c02", "webpack:///./components/payments/PaymentItem.vue?56bb", "webpack:///./components/payments/PaymentItem.vue?a5f4", "webpack:///./components/payments/PaymentItem.vue", "webpack:///./components/payments/PaymentItem.vue?3ed7", "webpack:///./components/payments/PaymentItem.vue?9cc4"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PaymentItem.vue?vue&type=style&index=0&id=995c1e74&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"fd0dd7ee\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PaymentItem.vue?vue&type=style&index=0&id=995c1e74&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".payment-item[data-v-995c1e74]{display:flex;background:#fff;border-radius:14px;margin-bottom:12px;overflow:hidden;box-shadow:0 4px 12px rgba(0,0,0,.1)}.payment-item[data-v-995c1e74]:hover{box-shadow:0 4px 14px rgba(217,225,236,.47)}.payment-item-date[data-v-995c1e74]{min-width:100px;padding:11px;display:flex;flex-direction:column;align-items:center;width:142px;border-radius:16px;justify-content:center;background:linear-gradient(126.15deg,rgba(128,182,34,.74),rgba(60,135,248,.74) 102.93%);color:#fff;box-shadow:4px 0 8px rgba(0,0,0,.1);position:relative;z-index:1}.payment-item-date .weekday[data-v-995c1e74]{font-size:13px;font-weight:700;line-height:1;text-transform:capitalize;text-align:center}.payment-item-date .date[data-v-995c1e74]{font-size:24px;font-weight:700;line-height:1.2;margin-bottom:2px}.payment-item-date .time[data-v-995c1e74]{font-size:13px;line-height:1;font-weight:700;margin-bottom:18px;text-align:center}.payment-item-date .duration-icon[data-v-995c1e74]{color:var(--v-dark-lighten3)}.payment-item-date .duration[data-v-995c1e74]{display:flex;align-items:center;font-size:16px}.payment-item-date .duration span[data-v-995c1e74]{color:#e8f1f7}.payment-item-date .duration-icon[data-v-995c1e74]{margin-right:4px;display:flex;align-items:center}.payment-item-content[data-v-995c1e74]{flex:1;padding:16px 24px}.payment-item-content .payment-info .student-name[data-v-995c1e74]{font-size:24px;font-weight:500;color:#333;margin-bottom:12px}.payment-item-content .payment-info .details[data-v-995c1e74]{display:flex;align-items:center;grid-gap:24px;gap:24px;font-size:14px}.payment-item-content .payment-info .details .detail-group[data-v-995c1e74]{align-items:center;grid-gap:6px;gap:6px}.payment-item-content .payment-info .details .detail-group p[data-v-995c1e74]{margin:0}.payment-item-content .payment-info .details .detail-group .label[data-v-995c1e74]{color:#666;font-size:14px}.payment-item-content .payment-info .details .detail-group .value[data-v-995c1e74]{color:#333}.payment-item-content .payment-info .details .detail-group .value.gradient-text[data-v-995c1e74]{background:linear-gradient(126.15deg,#80b622,#3c87f8 102.93%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;font-weight:500;font-size:16px;line-height:18px}.payment-item-content .payment-info .details .detail-group .pdf-download-link[data-v-995c1e74]{cursor:pointer}.payment-item-content .payment-info .details .detail-group .pdf-download-link[data-v-995c1e74]:hover{text-decoration:underline}.d-none[data-v-995c1e74]{display:none}@media screen and (min-width:768px){.d-sm-none[data-v-995c1e74]{display:none}}@media screen and (min-width:768px){.d-sm-block[data-v-995c1e74]{display:block}}@media screen and (max-width:768px){.payment-item[data-v-995c1e74]{flex-direction:column;margin-bottom:16px;box-shadow:none;background:transparent}.payment-item[data-v-995c1e74],.payment-item-date[data-v-995c1e74]{box-shadow:4px 0 8px rgba(0,0,0,.1)}.payment-item-date[data-v-995c1e74]{width:auto;min-height:auto;padding:8px 16px;flex-direction:row;justify-content:flex-start;border-radius:24px;margin-bottom:8px}.payment-item-date .date[data-v-995c1e74]{margin-right:8px;margin-bottom:0}.payment-item-date .time[data-v-995c1e74]{margin-left:0;opacity:1;margin-bottom:0}.payment-item-content[data-v-995c1e74]{background:#fff;border-radius:12px;padding:16px;box-shadow:0 4px 12px rgba(0,0,0,.1)}.payment-item-content .payment-info .student-name[data-v-995c1e74]{font-size:20px;margin-bottom:4px;padding-bottom:12px;border-bottom:1px solid rgba(0,0,0,.1)}.payment-item-content .payment-info .details[data-v-995c1e74]{flex-direction:column;grid-gap:8px;gap:8px}.payment-item-content .payment-info .details .detail-group[data-v-995c1e74]{display:flex;justify-content:space-between;width:100%}.payment-item-content .payment-info .details .detail-group .value[data-v-995c1e74]{font-size:16px;font-weight:500}.payment-item-content .payment-info .details .detail-group[data-v-995c1e74]:first-child{margin-bottom:4px}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"payment-item\"},[_vm._ssrNode(\"<div class=\\\"payment-item-date\\\" data-v-995c1e74><div data-v-995c1e74><div class=\\\"weekday d-none d-sm-block\\\" data-v-995c1e74>\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.formatWeekday(_vm.item.date))+\"\\n      \")+\"</div> <div class=\\\"date d-none d-sm-block\\\" data-v-995c1e74>\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.formatDate(_vm.item.date))+\"\\n      \")+\"</div> <div class=\\\"time d-none d-sm-block\\\" data-v-995c1e74>\"+_vm._ssrEscape(_vm._s(_vm.formatTime(_vm.item.time)))+\"</div> <div class=\\\"d-sm-none\\\" data-v-995c1e74>\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.formatWeekday(_vm.item.date))+\", \"+_vm._s(_vm.formatDate(_vm.item.date))+\" -\\n        \"+_vm._s(_vm.formatTime(_vm.item.time))+\"\\n      \")+\"</div></div> <div class=\\\"duration d-none d-sm-block\\\" data-v-995c1e74><div class=\\\"duration-icon\\\" data-v-995c1e74><svg width=\\\"18\\\" height=\\\"18\\\" viewBox=\\\"0 0 18 18\\\" data-v-995c1e74><use\"+(_vm._ssrAttr(\"xlink:href\",require(\"assets/images/icon-sprite.svg\") + \"#clock-thin\"))+\" data-v-995c1e74></use></svg> <span class=\\\"ml-1\\\" data-v-995c1e74>\"+_vm._ssrEscape(_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.lessonLength })))+\"</span></div></div> <div class=\\\"duration d-sm-none\\\" data-v-995c1e74>\"+_vm._ssrEscape(\"\\n       (\"+_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.lessonLength }))+\")\\n    \")+\"</div></div> <div class=\\\"payment-item-content\\\" data-v-995c1e74><div class=\\\"payment-info\\\" data-v-995c1e74><div class=\\\"student-name\\\" data-v-995c1e74>\"+_vm._ssrEscape(_vm._s(_vm.item.student))+\"</div> <div class=\\\"details\\\" data-v-995c1e74><div class=\\\"detail-group\\\" data-v-995c1e74><p class=\\\"label\\\" data-v-995c1e74>Lesson:</p> <p class=\\\"value gradient-text\\\" data-v-995c1e74>\"+_vm._ssrEscape(_vm._s(_vm.item.lessonType))+\"</p></div> <div class=\\\"detail-group\\\" data-v-995c1e74><p class=\\\"label\\\" data-v-995c1e74>Finished:</p> <p class=\\\"value gradient-text\\\" data-v-995c1e74>\"+_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.formatFinishedAt(_vm.item.finishedAt))+\"\\n          \")+\"</p></div> <div class=\\\"detail-group\\\" data-v-995c1e74><p class=\\\"label\\\" data-v-995c1e74>Invoice no.</p> <p class=\\\"value gradient-text\\\" data-v-995c1e74>\"+_vm._ssrEscape(_vm._s(_vm.item.invoiceNo))+\"</p></div> <div class=\\\"detail-group\\\" data-v-995c1e74><p class=\\\"label\\\" data-v-995c1e74>Lesson no.</p> <p class=\\\"value gradient-text\\\" data-v-995c1e74>\"+_vm._ssrEscape(_vm._s(_vm.item.lessonNo))+\"</p></div> <div class=\\\"detail-group\\\" data-v-995c1e74><p class=\\\"label\\\" data-v-995c1e74>Value</p> <p class=\\\"value gradient-text\\\" data-v-995c1e74>\"+_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.formatCurrencyValue(_vm.item.value))+\"\\n          \")+\"</p></div> \"+((_vm.item.transactionId && _vm.item.invoiceNumber)?(\"<div class=\\\"detail-group\\\" data-v-995c1e74><p class=\\\"label\\\" data-v-995c1e74>PDF</p> <p class=\\\"value gradient-text\\\" data-v-995c1e74><a href=\\\"#\\\" class=\\\"pdf-download-link\\\" data-v-995c1e74>\\n              Download\\n            </a></p></div>\"):\"<!---->\")+\"</div></div></div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { formatCurrencyLocale } from '~/helpers'\n\nconst DEFAULT_PAYMENT_ITEM = {\n  date: '2023-11-18',\n  time: '9:00 AM',\n  student: '<PERSON><PERSON><PERSON>',\n  lessonType: 'Trial',\n  status: 'Finished',\n  completedAt: '18 Nov, 10:02 AM',\n  invoiceNo: '8395',\n  lessonNo: '295032',\n  value: '12.50',\n  lessonLength: 30, // Default lesson length in minutes\n}\n\nexport default {\n  name: 'PaymentItem',\n  props: {\n    item: {\n      type: Object,\n      required: true,\n      default: () => ({ ...DEFAULT_PAYMENT_ITEM }),\n      validator(value) {\n        return [\n          'date',\n          'time',\n          'student',\n          'lessonType',\n          'status',\n          'completedAt',\n          'invoiceNo',\n          'lessonNo',\n          'value',\n        ].every((key) => key in value)\n      },\n    },\n  },\n  computed: {\n    lessonLength() {\n      // If lessonLength is available in the item, use it, otherwise default to 30 minutes\n      return this.item.lessonLength || 30\n    },\n    userLocale() {\n      // Get user's UI language/locale, fallback to browser locale or 'en'\n      return this.$store.getters['user/isUserLogged']\n        ? this.$store.state.user.item?.uiLanguage || this.$i18n.locale\n        : this.$i18n.locale || 'en'\n    },\n    timeZone() {\n      // Get user's timezone, fallback to browser timezone\n      return this.$store.getters['user/timeZone']\n    },\n    currentCurrencySymbol() {\n      return this.$store.getters['currency/currentCurrencySymbol']\n    },\n    currentCurrency() {\n      // Get current currency info from store\n      return this.$store.state.currency.item\n    },\n  },\n  methods: {\n    formatDate(date) {\n      try {\n        return this.$dayjs(date).tz(this.timeZone).format('DD MMM')\n      } catch (e) {\n        // Fallback to default formatting if there's an error\n        return date\n      }\n    },\n    formatWeekday(date) {\n      // Format weekday using user's locale and timezone\n      try {\n        // Use dayjs with timezone support and locale formatting\n        return this.$dayjs(date).tz(this.timeZone).format('dddd')\n      } catch (e) {\n        // Fallback using Intl.DateTimeFormat with user's locale\n        return new Intl.DateTimeFormat(this.userLocale, {\n          weekday: 'long',\n        }).format(new Date(date))\n      }\n    },\n    formatTime(time) {\n      // Format time using user's locale and timezone\n      try {\n        // If time is already in a good format, we can try to parse it with the date\n        // and format it according to user's locale\n        if (time && this.item.date) {\n          // Combine date and time for proper timezone conversion\n          const dateTimeString = `${this.item.date} ${time}`\n          const dateTime = this.$dayjs(dateTimeString).tz(this.timeZone)\n\n          // Format time using locale-aware format (LT = localized time)\n          return dateTime.format('LT')\n        }\n\n        // Fallback: return the original time if we can't parse it\n        return time\n      } catch (e) {\n        // Fallback to original time if there's an error\n        return time\n      }\n    },\n    formatFinishedAt(finishedAt) {\n      // Format finished date/time using user's locale and timezone\n      try {\n        if (!finishedAt) return '-'\n\n        // Use dayjs with timezone support and locale formatting\n        // Format as \"DD MMM, LT\" (e.g., \"18 Nov, 10:02 AM\")\n        return this.$dayjs(finishedAt).tz(this.timeZone).format('DD MMM, LT')\n      } catch (e) {\n        // Fallback to original value if there's an error\n        return finishedAt || '-'\n      }\n    },\n    formatValue(value) {\n      // Format the value with exactly 2 decimal places\n      return Number(value).toFixed(2)\n    },\n    formatCurrencyValue(value) {\n      // Format currency value according to user's locale\n      const currencyCode = this.currentCurrency?.isoCode || 'EUR'\n      return formatCurrencyLocale(value, currencyCode, this.userLocale, true)\n    },\n    openPdf() {\n      try {\n        this.$store.dispatch('payments/openInvoicePdf', {\n          transactionId: this.item.transactionId,\n          invoiceNumber: this.item.invoiceNumber,\n        })\n      } catch (error) {\n        // Handle error - show user-friendly message\n        if (this.$store.dispatch) {\n          this.$store.dispatch('snackbar/error', {\n            errorMessage: 'Failed to open invoice PDF. Please try again.',\n          })\n        }\n      }\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PaymentItem.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PaymentItem.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./PaymentItem.vue?vue&type=template&id=995c1e74&scoped=true&\"\nimport script from \"./PaymentItem.vue?vue&type=script&lang=js&\"\nexport * from \"./PaymentItem.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./PaymentItem.vue?vue&type=style&index=0&id=995c1e74&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"995c1e74\",\n  \"067c283c\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAWA;AACA;AAjBA;AADA;AAoBA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAtBA;AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AA/EA;AA7CA;;AC7FA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}