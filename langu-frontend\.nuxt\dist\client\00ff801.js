(window.webpackJsonp=window.webpackJsonp||[]).push([[166],{2221:function(t,e,r){"use strict";r.r(e);r(20);var n={name:"UserPasswordToken",beforeMount:function(){var t=this,e=this.$route.params.token,r=this.$route.query.redirectUrl;r&&this.$store.dispatch("user/setRedirectUrl",r),this.$store.dispatch("auth/checkPasswordToken",e).finally((function(){t.$router.push("/teacher-listing")}))}},o=r(22),component=Object(o.a)(n,(function(){var t=this.$createElement;return(this._self._c||t)("div",{staticStyle:{"min-height":"calc(100vh - 300px)"}})}),[],!1,null,null,null);e.default=component.exports}}]);