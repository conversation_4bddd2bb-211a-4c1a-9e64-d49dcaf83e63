{"version": 3, "file": "components/teacher-profile-feedback-tags.js", "sources": ["webpack:///./components/teacher-profile/FeedbackTags.vue?a796", "webpack:///./components/teacher-profile/FeedbackTags.vue?0f7d", "webpack:///./components/teacher-profile/FeedbackTags.vue?24e0", "webpack:///./components/teacher-profile/FeedbackTags.vue?b3dc", "webpack:///./components/teacher-profile/FeedbackTags.vue", "webpack:///./components/teacher-profile/FeedbackTags.vue?bd99", "webpack:///./components/teacher-profile/FeedbackTags.vue?a15e"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FeedbackTags.vue?vue&type=style&index=0&id=1c41cb86&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"b9052ed2\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FeedbackTags.vue?vue&type=style&index=0&id=1c41cb86&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".teacher-profile-feedback-tags[data-v-1c41cb86]{margin-bottom:14px}@media only screen and (max-width:991px){.teacher-profile-feedback-tags[data-v-1c41cb86]{margin-bottom:10px}}.teacher-profile-feedback-tags>*[data-v-1c41cb86]{margin-right:10px}.teacher-profile-feedback-tags-label[data-v-1c41cb86]{font-size:16px;font-weight:700}@media only screen and (max-width:991px){.teacher-profile-feedback-tags-label[data-v-1c41cb86]{font-size:14px;font-weight:600}}.teacher-profile-feedback-tags>.chip[data-v-1c41cb86]{margin-bottom:7px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.items.length)?_c('div',{staticClass:\"teacher-profile-feedback-tags\"},[_vm._ssrNode(\"<span class=\\\"teacher-profile-feedback-tags-label text--gradient\\\" data-v-1c41cb86>\"+_vm._ssrEscape(_vm._s(_vm.$t('students_say'))+\":\")+\"</span> \"),_vm._l(([].concat( _vm.items ).reverse().slice(0, 5)),function(tag){return _c('l-chip',{key:tag.tag.id,attrs:{\"label\":((tag.tag.name) + \" (\" + (tag.count) + \")\"),\"close-btn\":false,\"light\":\"\"}})})],2):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'FeedbackTags',\n  props: {\n    items: {\n      type: Array,\n      default: () => [],\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FeedbackTags.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FeedbackTags.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./FeedbackTags.vue?vue&type=template&id=1c41cb86&scoped=true&\"\nimport script from \"./FeedbackTags.vue?vue&type=script&lang=js&\"\nexport * from \"./FeedbackTags.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./FeedbackTags.vue?vue&type=style&index=0&id=1c41cb86&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"1c41cb86\",\n  \"7f7bfcb4\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {LChip: require('D:/languworks/langu-frontend/components/LChip.vue').default})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AADA;AAFA;;AChBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}