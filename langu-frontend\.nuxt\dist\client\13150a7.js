(window.webpackJsonp=window.webpackJsonp||[]).push([[49],{1585:function(t,e,o){var content=o(1683);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("77e2f3f6",content,!0,{sourceMap:!1})},1682:function(t,e,o){"use strict";o(1585)},1683:function(t,e,o){var n=o(18)(!1);n.push([t.i,".stream-controls{position:absolute;left:50%;bottom:-38px;z-index:10;transform:translateX(-50%)}.stream-controls .toolbar-button-wrapper{width:40px;height:40px}.stream-controls .toolbar-button-wrapper button svg{margin:0 auto}.stream-controls .toolbar-button-wrapper button:focus{outline:none}.stream-controls .toolbar-button-wrapper-full-screen button{padding:9px}.stream-controls .hover-btn-info{left:50%;right:auto;top:auto;bottom:-36px;transform:translateX(-50%)}.stream-controls .hover-btn-info:after{left:50%;top:-9px;right:auto;border:5px solid transparent;border-bottom-color:#444;transform:translateX(-50%)}.stream-controls .stream-controls-switch{display:flex;justify-content:center;align-items:center;margin-top:-6px;padding:10px 6px 3px;background:#fff;border:none;border-radius:0 0 6px 6px;font-size:13px;line-height:1;outline:none;box-shadow:0 2px 10px rgba(0,0,0,.25)}.stream-controls .stream-controls-switch .toolbar-button-wrapper{width:18px;height:18px}.stream-controls .stream-controls-switch .toolbar-button-wrapper:not(:last-child){margin-right:4px}.stream-controls .stream-controls-switch .toolbar-button-item{min-width:18px!important;font-size:13px!important;border-radius:50%!important;overflow:hidden}.stream-controls .stream-controls-switch .toolbar-button-item--selected{color:#fff;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%)!important}.toolbar-button-icon.hand-raised{fill:#ffc107!important}.toolbar-button-icon.chat-enabled,.toolbar-button-icon.participants-enabled{fill:#2196f3!important}",""]),t.exports=n},1704:function(t,e,o){"use strict";o.r(e);var n={name:"VideoActions",props:{settings:{type:Object,required:!0},isJoined:{type:Boolean,required:!0},isScreenShareDisabled:{type:Boolean,default:!1},type:{type:String,required:!0}},computed:{role:function(){return this.$store.getters["classroom/role"]}}},r=(o(1682),o(22)),component=Object(r.a)(n,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"stream-controls"},[n("div",{staticClass:"video-window-buttons-wrap",attrs:{id:"video-window-buttons"}},[n("div",{staticClass:"stream-controls-wrapper cursor-auto"},[n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-video":"",type:"button",disabled:!t.isJoined},on:{click:function(e){return t.$emit("toggle-video")}}},[t.settings.isVideoEnabled?n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"51",height:"33",viewBox:"0 0 51 33"}},[n("use",{attrs:{"xlink:href":o(884)+"#videocam"}})]):t.settings.isVideoEnabled?t._e():n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"39",height:"33",viewBox:"0 0 39 33"}},[n("use",{attrs:{"xlink:href":o(884)+"#videocam_off"}})])]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.settings.isVideoEnabled?[t._v("\n            "+t._s(t.$t("turn_off_camera"))+"\n          ")]:[t._v("\n            "+t._s(t.$t("turn_on_camera"))+"\n          ")]],2)]),t._v(" "),n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-audio":"",type:"button",disabled:!t.isJoined},on:{click:function(e){return t.$emit("toggle-audio")}}},[t.settings.isMuted?t._e():n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"23",height:"33",viewBox:"0 0 23 33"}},[n("use",{attrs:{"xlink:href":o(883)+"#microphone"}})]),t._v(" "),t.settings.isMuted?n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"31",height:"34",viewBox:"0 0 31 34"}},[n("use",{attrs:{"xlink:href":o(883)+"#microphone-off"}})]):t._e()]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.settings.isMuted?[t._v("\n            "+t._s(t.$t("unmute_microphone"))+"\n          ")]:[t._v("\n            "+t._s(t.$t("mute_microphone"))+"\n          ")]],2)]),t._v(" "),n("div",{staticClass:"toolbar-button-wrapper toolbar-button-wrapper-full-screen"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-full-screen":"",type:"button",disabled:!t.isJoined},on:{click:function(e){return t.$emit("toggle-full-screen")}}},[t.settings.isFullscreenEnabled?t._e():n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"31",height:"31",viewBox:"0 0 31 31"}},[n("use",{attrs:{"xlink:href":o(882)+"#full_screen"}})]),t._v(" "),t.settings.isFullscreenEnabled?n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"31",height:"31",viewBox:"0 0 31 31"}},[n("use",{attrs:{"xlink:href":o(882)+"#window_screen"}})]):t._e()]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.settings.isFullscreenEnabled?[t._v("\n            "+t._s(t.$t("leave_full_screen_video"))+"\n          ")]:[t._v("\n            "+t._s(t.$t("full_screen_video"))+"\n          ")]],2)]),t._v(" "),n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-screen-share":"",type:"button",disabled:t.isScreenShareDisabled||!t.isJoined&&"twilio"===t.type},on:{click:function(e){return t.$emit("toggle-screen-share")}}},[t.settings.isScreenShareEnabled?n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"38",height:"35",viewBox:"0 0 38 35"}},[n("use",{attrs:{"xlink:href":o(875)+"#not_share"}})]):t._e(),t._v(" "),t.settings.isScreenShareEnabled?t._e():n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"37",height:"28",viewBox:"0 0 37 28"}},[n("use",{attrs:{"xlink:href":o(875)+"#share"}})])]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.isScreenShareDisabled?[t._v("\n            "+t._s(t.$t("share_is_disabled"))+"\n          ")]:[t.settings.isScreenShareEnabled?[t._v("\n              "+t._s(t.$t("stop_screenshare"))+"\n            ")]:[t._v("\n              "+t._s(t.$t("share_my_screen"))+"\n            ")]]],2)]),t._v(" "),"whereby"===t.type?n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-hand-raise":"",type:"button",disabled:!t.isJoined},on:{click:function(e){return t.$emit("toggle-hand-raise")}}},[n("svg",{class:["toolbar-button-icon",{"hand-raised":t.settings.isHandRaised}],attrs:{width:"24",height:"33",viewBox:"0 0 24 33"}},[n("use",{attrs:{"xlink:href":o(933)+"#hand"}})])]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.settings.isHandRaised?[t._v("\n            "+t._s(t.$t("lower_hand"))+"\n          ")]:[t._v("\n            "+t._s(t.$t("raise_hand"))+"\n          ")]],2)]):t._e(),t._v(" "),"whereby"===t.type?n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-chat":"",type:"button",disabled:!t.isJoined},on:{click:function(e){return t.$emit("toggle-chat")}}},[n("svg",{class:["toolbar-button-icon",{"chat-enabled":t.settings.isChatEnabled}],attrs:{width:"28",height:"28",viewBox:"0 0 28 28"}},[n("use",{attrs:{"xlink:href":o(932)+"#chat"}})])]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.settings.isChatEnabled?[t._v("\n            "+t._s(t.$t("hide_chat"))+"\n          ")]:[t._v("\n            "+t._s(t.$t("show_chat"))+"\n          ")]],2)]):t._e(),t._v(" "),"whereby"===t.type?n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-participants":"",type:"button",disabled:!t.isJoined},on:{click:function(e){return t.$emit("toggle-participants")}}},[n("svg",{class:["toolbar-button-icon",{"participants-enabled":t.settings.isParticipantsEnabled}],attrs:{width:"32",height:"28",viewBox:"0 0 32 28"}},[n("use",{attrs:{"xlink:href":o(934)+"#participants"}})])]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.settings.isParticipantsEnabled?[t._v("\n            "+t._s(t.$t("hide_participants"))+"\n          ")]:[t._v("\n            "+t._s(t.$t("show_participants"))+"\n          ")]],2)]):t._e()])])])}),[],!1,null,null,null);e.default=component.exports}}]);