exports.ids = [44];
exports.modules = {

/***/ 1071:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1105);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("20c2c1c7", content, true)

/***/ }),

/***/ 1105:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".slick-track[data-v-e4caeaf8]{position:relative;top:0;left:0;display:block;transform:translateZ(0)}.slick-track.slick-center[data-v-e4caeaf8]{margin-left:auto;margin-right:auto}.slick-track[data-v-e4caeaf8]:after,.slick-track[data-v-e4caeaf8]:before{display:table;content:\"\"}.slick-track[data-v-e4caeaf8]:after{clear:both}.slick-loading .slick-track[data-v-e4caeaf8]{visibility:hidden}.slick-slide[data-v-e4caeaf8]{display:none;float:left;height:100%;min-height:1px}[dir=rtl] .slick-slide[data-v-e4caeaf8]{float:right}.slick-slide img[data-v-e4caeaf8]{display:block}.slick-slide.slick-loading img[data-v-e4caeaf8]{display:none}.slick-slide.dragging img[data-v-e4caeaf8]{pointer-events:none}.slick-initialized .slick-slide[data-v-e4caeaf8]{display:block}.slick-loading .slick-slide[data-v-e4caeaf8]{visibility:hidden}.slick-vertical .slick-slide[data-v-e4caeaf8]{display:block;height:auto;border:1px solid transparent}.slick-arrow.slick-hidden[data-v-21137603]{display:none}.slick-slider[data-v-3d1a4f76]{position:relative;display:block;box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;touch-action:pan-y;-webkit-tap-highlight-color:transparent}.slick-list[data-v-3d1a4f76]{position:relative;display:block;overflow:hidden;margin:0;padding:0;transform:translateZ(0)}.slick-list[data-v-3d1a4f76]:focus{outline:none}.slick-list.dragging[data-v-3d1a4f76]{cursor:pointer;cursor:hand}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1260:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1353);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("71b1d682", content, true, context)
};

/***/ }),

/***/ 1352:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReviewSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1260);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReviewSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReviewSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReviewSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReviewSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1353:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(68);
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(620);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".home-page-reviews{position:relative;margin-top:157px;padding-bottom:45px}@media only screen and (max-width:1439px){.home-page-reviews{padding-bottom:110px}}@media only screen and (max-width:991px){.home-page-reviews{margin-top:130px;padding-bottom:100px}.home-page-reviews .section-bg{top:50%;height:84%;transform:translateY(-50%)}.home-page-reviews .section-bg .v-image__image{background-position:85% bottom!important}}@media only screen and (max-width:479px){.home-page-reviews{margin-top:80px}}.home-page-reviews-carousel{max-width:898px;margin-top:30px}@media only screen and (max-width:1439px){.home-page-reviews-carousel{max-width:100%;padding-left:90px}}@media only screen and (max-width:991px){.home-page-reviews-carousel{max-width:620px;margin-left:auto;margin-right:auto;padding-left:0}}@media only screen and (max-width:639px){.home-page-reviews-carousel{margin-top:15px}}@media only screen and (max-width:479px){.home-page-reviews-carousel{width:calc(100% + 15px)}}.home-page-reviews-carousel-item{position:relative;max-width:680px;padding:16px 0 38px 23px}@media only screen and (max-width:991px){.home-page-reviews-carousel-item{padding:16px 15px 0}}@media only screen and (max-width:639px){.home-page-reviews-carousel-item{max-width:100%;padding-top:25px}.home-page-reviews-carousel-item:before{content:\"\";position:absolute;top:43px;right:48px;width:40px;height:36px;background-size:cover;background-repeat:no-repeat;background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");opacity:.3}}@media only screen and (min-width:640px){.home-page-reviews-carousel-item:before{display:none}}.home-page-reviews-carousel-item-helper{position:relative;display:block;height:100%;padding:24px 36px 105px 105px;background-color:var(--v-darkLight-base);border-radius:24px;color:#fff!important;text-decoration:none}@media only screen and (max-width:991px){.home-page-reviews-carousel-item-helper{padding:95px 32px 105px;box-shadow:0 4px 24px rgba(0,0,0,.1)}.home-page-reviews-carousel-item-helper:before{content:\"\";position:absolute;top:18px;right:32px;width:40px;height:36px;background-size:cover;background-repeat:no-repeat;background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");opacity:.3}}@media only screen and (max-width:479px){.home-page-reviews-carousel-item-helper{padding:90px 15px;box-shadow:none}.home-page-reviews-carousel-item-helper:before{right:18px}}.home-page-reviews-carousel-item-info{position:absolute;left:-15px;top:-15px}.home-page-reviews-carousel-item-info .flag{position:absolute;bottom:4px;right:-16px;border-radius:6px;overflow:hidden}.home-page-reviews-carousel-item-info .rating{position:absolute;left:30px;bottom:-18px;width:60px}@media only screen and (max-width:991px){.home-page-reviews-carousel-item-info .rating{left:102px;bottom:38px;width:88px}}.home-page-reviews-carousel-item-text{min-height:80px;font-size:16px;font-weight:300;line-height:1.6}.home-page-reviews-carousel-item-bottom{position:absolute;width:100%;left:0;bottom:40px;padding:0 36px 0 105px;font-size:20px}@media only screen and (max-width:991px){.home-page-reviews-carousel-item-bottom{padding:0 32px}}@media only screen and (max-width:479px){.home-page-reviews-carousel-item-bottom{padding:0 15px}}.home-page-reviews-carousel-item-bottom-helper{position:relative;padding:0 50px 0 60px}@media only screen and (max-width:479px){.home-page-reviews-carousel-item-bottom-helper{padding:0 0 0 60px;font-size:14px}}.home-page-reviews-carousel-item-bottom-helper:before{content:\"\";position:absolute;top:-16px;right:0;width:40px;height:36px;background-size:cover;background-repeat:no-repeat;background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");opacity:.3}@media only screen and (max-width:991px){.home-page-reviews-carousel-item-bottom-helper:before{display:none}}.home-page-reviews-carousel-item-bottom-helper .v-image{position:absolute;left:0;top:50%;transform:translateY(-50%)}.home-page-reviews .slick-slide:nth-child(odd)>div{display:flex!important;justify-content:flex-end}.home-page-reviews .slick-arrow{position:absolute;top:27%;transform:translateY(-50%)}@media only screen and (max-width:991px){.home-page-reviews .slick-arrow{transform:translateX(-50%)}}.home-page-reviews .slick-next,.home-page-reviews .slick-prev{left:-696px}@media only screen and (max-width:1643px){.home-page-reviews .slick-next,.home-page-reviews .slick-prev{left:-192px}}@media only screen and (max-width:1439px){.home-page-reviews .slick-next,.home-page-reviews .slick-prev{left:-115px}}@media only screen and (max-width:991px){.home-page-reviews .slick-next,.home-page-reviews .slick-prev{top:auto;bottom:-100px}}@media only screen and (min-width:992px){.home-page-reviews .slick-prev{transform:translateY(calc(-50% - 45px))}}@media only screen and (max-width:991px){.home-page-reviews .slick-prev{left:calc(50% - 50px)}}.home-page-reviews .slick-prev:before{top:50%;left:calc(50% - 2px);transform:translate(-50%,-50%)}@media only screen and (min-width:992px){.home-page-reviews .slick-prev:before{top:calc(50% - 2px);left:50%;transform:translate(-50%,-50%) rotate(90deg)}}@media only screen and (min-width:992px){.home-page-reviews .slick-next{transform:translateY(calc(-50% + 45px))}}@media only screen and (max-width:991px){.home-page-reviews .slick-next{left:calc(50% + 50px)}}.home-page-reviews .slick-next:before{top:50%;left:calc(50% + 2px);transform:translate(-50%,-50%) rotate(180deg)}@media only screen and (min-width:992px){.home-page-reviews .slick-next:before{top:calc(50% + 2px);left:50%;transform:translate(-50%,-50%) rotate(270deg)}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1416:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/ReviewSection.vue?vue&type=template&id=7074f6fe&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"home-page-reviews"},[_vm._ssrNode("<div class=\"section-bg\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(435),"position":"center bottom","options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('v-container',[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{staticClass:"section-head-title",staticStyle:{"color":"#262626","-webkit-text-fill-color":"#262626"}},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.reviews_section_title'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"section-head-subtitle",staticStyle:{"max-width":"900px"},domProps:{"innerHTML":_vm._s(_vm.$t('home_page.reviews_section_subtitle'))}})])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 py-0 d-md-flex justify-end"},[_c('div',{staticClass:"home-page-reviews-carousel"},[_c('client-only',[_c('VueSlickCarousel',_vm._b({},'VueSlickCarousel',_vm.reviewsCarouselSettings,false),_vm._l((_vm.reviews),function(r,idx){return _c('div',{key:idx,staticClass:"home-page-reviews-carousel-item"},[_c('nuxt-link',{staticClass:"home-page-reviews-carousel-item-helper",attrs:{"to":r.teacherProfileUrl}},[_c('div',{staticClass:"home-page-reviews-carousel-item-info"},[_c('v-avatar',{attrs:{"width":"87","height":"87"}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(r.avatars, 'user_thumb_87x87'),"srcset":_vm.getSrcSetAvatar(
                            r.avatars,
                            'user_thumb_87x87',
                            'user_thumb_174x174'
                          ),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"flag"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (r.language.isoCode) + ".svg"),"width":"36","contain":"","options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"rating"},[_c('v-img',{attrs:{"src":__webpack_require__(119),"width":"100%","contain":"","options":{ rootMargin: '50%' }}})],1)],1),_vm._v(" "),_c('div',{staticClass:"home-page-reviews-carousel-item-text"},[_vm._v("\n                    “"+_vm._s(_vm._f("reviewText")(r.description))+"”\n                  ")]),_vm._v(" "),_c('div',{staticClass:"home-page-reviews-carousel-item-bottom"},[_c('div',{staticClass:"home-page-reviews-carousel-item-bottom-helper"},[_c('v-img',{attrs:{"src":__webpack_require__(120),"width":"45","options":{ rootMargin: '50%' }}}),_vm._v("\n                      "+_vm._s(r.studentFirstName)+",\n                      "+_vm._s(_vm.$tc('review_lessons_count', r.countFinishedLesson, {
                          language: r.language.name,
                        }))+"\n                    ")],1)])])],1)}),0)],1)],1)])],1)],1)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/ReviewSection.vue?vue&type=template&id=7074f6fe&

// EXTERNAL MODULE: external "vue-slick-carousel"
var external_vue_slick_carousel_ = __webpack_require__(859);
var external_vue_slick_carousel_default = /*#__PURE__*/__webpack_require__.n(external_vue_slick_carousel_);

// EXTERNAL MODULE: ./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css
var vue_slick_carousel = __webpack_require__(1071);

// EXTERNAL MODULE: ./mixins/Avatars.vue + 2 modules
var Avatars = __webpack_require__(932);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/ReviewSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



/* harmony default export */ var ReviewSectionvue_type_script_lang_js_ = ({
  name: 'ReviewSection',
  components: {
    VueSlickCarousel: external_vue_slick_carousel_default.a
  },
  filters: {
    reviewText(val) {
      let str = val;

      if (str.length > 322) {
        const arr = str.substring(0, 322).split(' ');
        arr.pop();
        str = arr.join(' ') + ' ...';
      }

      return str;
    }

  },
  mixins: [Avatars["a" /* default */]],
  props: {
    reviews: {
      type: Array,
      required: true
    }
  },

  data() {
    return {
      reviewsCarouselSettings: {
        dots: false,
        focusOnSelect: true,
        infinite: true,
        slidesToShow: 3,
        slidesToScroll: 1,
        vertical: true,
        verticalSwiping: true,
        speed: 800,
        responsive: [{
          breakpoint: 992,
          settings: {
            vertical: false,
            verticalSwiping: false,
            slidesToShow: 1
          }
        }]
      }
    };
  }

});
// CONCATENATED MODULE: ./components/homepage/ReviewSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_ReviewSectionvue_type_script_lang_js_ = (ReviewSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/VAvatar.js
var VAvatar = __webpack_require__(830);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/ReviewSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1352)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_ReviewSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "36678257"
  
)

/* harmony default export */ var ReviewSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */






installComponents_default()(component, {VAvatar: VAvatar["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 932:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/Avatars.vue?vue&type=script&lang=js&
/* harmony default export */ var Avatarsvue_type_script_lang_js_ = ({
  methods: {
    getSrcAvatar(images, property, defaultImage = 'avatar.png') {
      return images !== null && images !== void 0 && images[property] ? images[property] : __webpack_require__(511)(`./${defaultImage}`);
    },

    getSrcSetAvatar(images, property1, property2) {
      return images !== null && images !== void 0 && images[property1] && images !== null && images !== void 0 && images[property2] ? `
            ${images[property1]} 1x,
            ${images[property2]} 2x,
          ` : '';
    }

  }
});
// CONCATENATED MODULE: ./mixins/Avatars.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_Avatarsvue_type_script_lang_js_ = (Avatarsvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/Avatars.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_Avatarsvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "0af9ff4e"
  
)

/* harmony default export */ var Avatars = __webpack_exports__["a"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=homepage-review-section.js.map