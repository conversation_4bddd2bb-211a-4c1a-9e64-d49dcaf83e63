{"version": 3, "file": "pages/blog/index.js", "sources": ["webpack:///./pages/blog/index.vue?2947", "webpack:///./pages/blog/index.vue?204b", "webpack:///./pages/blog/index.vue?0512", "webpack:///./pages/blog/index.vue?7d1f", "webpack:///./pages/blog/index.vue", "webpack:///./pages/blog/index.vue?8cf8", "webpack:///./pages/blog/index.vue?cc2b"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"3daf26fe\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".faq-page{--sidebar-width:100%}@media only screen and (min-width:992px){.faq-page{--sidebar-width:260px}}.faq-page-wrap{max-width:1030px}@media only screen and (max-width:991px){.faq-page-wrap{max-width:720px}}.faq-page-title{font-size:32px;line-height:1.333}@media only screen and (max-width:991px){.faq-page-title{font-size:26px}}@media only screen and (max-width:639px){.faq-page-title{font-size:24px}}.faq-page-content{width:100%}@media only screen and (min-width:992px){.faq-page-content{width:calc(100% - var(--sidebar-width));padding-right:36px}}.faq-page-content h2{font-size:26px}@media only screen and (max-width:991px){.faq-page-content h2{font-size:22px}}@media only screen and (max-width:639px){.faq-page-content h2{font-size:20px}}.faq-page-content .v-expansion-panel-content__wrap p{margin-bottom:16px!important}.faq-page-content .v-expansion-panel-content__wrap a{color:var(--v-orange-base);text-decoration:none}.faq-page-content .v-expansion-panel-content__wrap img{max-width:100%;margin-top:20px}.faq-page-content .v-expansion-panel-content__wrap .iframe-wrapper{position:relative;max-width:560px;margin-left:auto;margin-right:auto}.faq-page-content .v-expansion-panel-content__wrap .iframe-wrapper:before{content:\\\"\\\";position:relative;display:block;width:100%;padding-bottom:56.25%}.faq-page-content .v-expansion-panel-content__wrap .iframe-wrapper iframe{position:absolute;top:0;left:0;width:100%;height:100%}.faq-page-sidebar{width:var(--sidebar-width)}@media only screen and (min-width:992px){.faq-page-sidebar-sticky{position:sticky;top:70px}}.faq-page-sidebar-item:after{content:\\\"\\\";display:table;clear:both}.faq-page-sidebar-item .item-title{font-size:24px}@media only screen and (min-width:992px){.faq-page-sidebar-item .item-title{font-size:22px}}@media only screen and (max-width:639px){.faq-page-sidebar-item .item-title{font-size:20px}}.faq-page-sidebar-item .item-content a{color:var(--v-darkLight-base);text-decoration:none}.faq-page-sidebar-item .item-content a:hover{color:var(--v-orange-base);transition:color .2s}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{attrs:{\"id\":\"dib-posts\"}},[])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n\nexport default {\n  head() {\n    return {\n      title:\n        this.$i18n.locale === 'pl'\n          ? 'Langu | Blog: Artykuły i zasoby do efektywnej nauki języków online'\n          : 'Langu | Blog: Articles and Resources for Effective Online Language Learning',\n      script: [\n        {\n          src:\n            'https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js',\n          async: true,\n          defer: true,\n        },\n      ],\n    }\n  },\n  mounted() {\n    this.loadDropInBlogScript()\n  },\n  methods: {\n    loadDropInBlogScript() {\n      if (typeof window.main === 'function') {\n        // Call the main function directly if it exists\n        window.main()\n      } else {\n        const existingScript = document.querySelector(\n          'script[src=\"https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js\"]'\n        )\n        if (!existingScript) {\n          const script = document.createElement('script')\n          script.src =\n            'https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js'\n          script.async = true\n          script.defer = true\n          script.onload = () => {\n            // Ensure the main function is called after the script is loaded\n            if (typeof window.main === 'function') {\n              window.main()\n            }\n          }\n          document.head.appendChild(script)\n        }\n      }\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=58e3d554&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./index.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"f4bfd6a4\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAIA;AAEA;AAEA;AACA;AAJA;AANA;AAcA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAzBA;AApBA;;ACLA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}