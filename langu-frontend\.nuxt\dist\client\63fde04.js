(window.webpackJsonp=window.webpackJsonp||[]).push([[131],{1523:function(e,t,r){"use strict";r.r(t);r(31);var n=r(370),o=/^\d+\.?\d{0,2}$/,c={name:"LessonPrice",components:{TextInput:n.default},props:{value:{type:[String,Number],required:!0},rules:{type:Array,default:function(){return[]}},length:{type:Number,required:!0,default:30},freeTrial:{type:Boolean,required:!1,default:!1}},data:function(){return{key:1,keyCode:null}},computed:{currentCurrencySymbol:function(){return this.$store.getters["currency/currentCurrencySymbol"]},currencyCode:function(){return{$:"USD","€":"EUR","£":"GBP","zł":"PLN",A$:"AUD",C$:"CAD"}[this.currentCurrencySymbol]||"USD"},value_:function(){return this.value||null},minimumPrice:function(){var e;return(null===(e={EUR:{30:7,60:11,90:16,120:21},GBP:{30:6,60:10,90:15,120:20},PLN:{30:30,60:50,90:70,120:85},USD:{30:8,60:12,90:17,120:22},AUD:{30:12,60:20,90:28,120:36},CAD:{30:11,60:18,90:25,120:32}}[this.currencyCode])||void 0===e?void 0:e[this.length])||10},minimumValidation:function(e){return 60===Number(length)||Number(e)>0?this.minimumPrice:0}},mounted:function(){var e;this.validation(null!==(e=this.value)&&void 0!==e?e:0,this.freeTrial)},methods:{updateValue:function(e){var t,r=this;o.test(e)||"Backspace"===this.keyCode||"Delete"===this.keyCode?t=e:(t=this.value,this.key++,this.$nextTick((function(){r.$refs.priceInput.focus()}))),this.keyCode=null,this.validation(t),this.$emit("input",t)},validation:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.minimumPrice;this.$emit("validation",!(!t&&(60===Number(length)&&Number(e)<r||Number(e)>0&&Number(e)<r)))},validatePrice:function(e){var t=this.minimumPrice;return!!this.$props.freeTrial||(60===Number(length)&&Number(e)<t?"Error: Minimum price is ".concat(t):!(Number(e)>0&&Number(e)<t)||"Error: Minimum price is ".concat(t))}}},l=r(22),component=Object(l.a)(c,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("text-input",{key:e.key,ref:"priceInput",attrs:{value:e.value_,"type-class":"border-gradient",height:"32","hide-details":"",placeholder:"0.00",rules:e.rules.concat([e.validatePrice]),prefix:e.currentCurrencySymbol},on:{keydown:function(t){e.keyCode=t.code},input:function(t){return e.updateValue(t)}}})}),[],!1,null,null,null);t.default=component.exports}}]);