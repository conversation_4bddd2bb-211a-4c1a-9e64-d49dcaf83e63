exports.ids = [92,34,63,124];
exports.modules = {

/***/ 915:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(938);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("a98bb618", content, true, context)
};

/***/ }),

/***/ 916:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/UserStatus.vue?vue&type=template&id=652352c7&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[
    'user-status',
    ("user-status--" + _vm.status),
    { 'user-status--large': _vm.large } ]},[])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/UserStatus.vue?vue&type=template&id=652352c7&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/UserStatus.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var UserStatusvue_type_script_lang_js_ = ({
  name: 'UserStatus',
  props: {
    userId: {
      type: Number,
      default: 0
    },
    large: {
      type: Boolean,
      default: false
    },
    userStatuses: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    status() {
      var _this$userId;

      let status = 'offline';

      if (Object.prototype.hasOwnProperty.call(this.userStatuses, (_this$userId = this.userId) === null || _this$userId === void 0 ? void 0 : _this$userId.toString())) {
        status = this.userStatuses[this.userId];
      }

      return status;
    }

  }
});
// CONCATENATED MODULE: ./components/UserStatus.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_UserStatusvue_type_script_lang_js_ = (UserStatusvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/UserStatus.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(958)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_UserStatusvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "652352c7",
  "4c070a35"
  
)

/* harmony default export */ var UserStatus = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 932:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/Avatars.vue?vue&type=script&lang=js&
/* harmony default export */ var Avatarsvue_type_script_lang_js_ = ({
  methods: {
    getSrcAvatar(images, property, defaultImage = 'avatar.png') {
      return images !== null && images !== void 0 && images[property] ? images[property] : __webpack_require__(511)(`./${defaultImage}`);
    },

    getSrcSetAvatar(images, property1, property2) {
      return images !== null && images !== void 0 && images[property1] && images !== null && images !== void 0 && images[property2] ? `
            ${images[property1]} 1x,
            ${images[property2]} 2x,
          ` : '';
    }

  }
});
// CONCATENATED MODULE: ./mixins/Avatars.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_Avatarsvue_type_script_lang_js_ = (Avatarsvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/Avatars.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_Avatarsvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "0af9ff4e"
  
)

/* harmony default export */ var Avatars = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 933:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(959);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("006007e9", content, true, context)
};

/***/ }),

/***/ 937:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(915);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 938:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".text-editor{position:relative}.text-editor-buttons{position:absolute;right:18px;top:8px;z-index:2}.text-editor-buttons button{display:inline-flex;justify-content:center;align-items:center;width:24px;height:24px;margin-left:8px;border-radius:2px;border:1px solid transparent}.text-editor-buttons button.is-active{background-color:var(--v-greyBg-base);border-color:var(--v-greyLight-base)}.text-editor .ProseMirror{min-height:280px;margin-bottom:4px;padding:40px 12px 12px;border:1px solid #bebebe;font-size:13px;border-radius:16px;line-height:1.23}.text-editor .ProseMirror>*{position:relative}.text-editor .ProseMirror p{margin-bottom:0}.text-editor .ProseMirror ul{padding-left:28px}.text-editor .ProseMirror ul>li p{margin-bottom:0}.text-editor .ProseMirror strong{font-weight:700!important}.text-editor .ProseMirror.focus-visible,.text-editor .ProseMirror:focus,.text-editor .ProseMirror:focus-visible{outline:none!important}.text-editor .ProseMirror-focused:before{content:\"\";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:16px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}.text-editor .v-text-field__details{padding:0 14px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 939:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(979);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("0f94d031", content, true, context)
};

/***/ }),

/***/ 942:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/form/Editor.vue?vue&type=template&id=23b137ee&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"text-editor"},[_vm._ssrNode(((_vm.editor)?("<div class=\"text-editor-buttons\"><button"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bold') }))+"><svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#editor-bold-icon")))+"></use></svg></button> <button"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bulletList') }))+"><svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#editor-list-icon")))+"></use></svg></button></div>"):"<!---->")+" "),_c('editor-content',{attrs:{"editor":_vm.editor}}),_vm._ssrNode(" "+((_vm.counter)?("<div class=\"v-text-field__details\"><div class=\"v-messages theme--light\"><div class=\"v-messages__wrapper\"></div></div> <div"+(_vm._ssrClass(null,[
        'v-counter theme--light',
        { 'error--text': !_vm.isValid && _vm.isDirty } ]))+">"+_vm._ssrEscape("\n      "+_vm._s(_vm.text.length)+" / "+_vm._s(_vm.limit)+"\n    ")+"</div></div>"):"<!---->"))],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/form/Editor.vue?vue&type=template&id=23b137ee&

// EXTERNAL MODULE: external "@tiptap/vue-2"
var vue_2_ = __webpack_require__(854);

// EXTERNAL MODULE: external "@tiptap/starter-kit"
var starter_kit_ = __webpack_require__(855);
var starter_kit_default = /*#__PURE__*/__webpack_require__.n(starter_kit_);

// EXTERNAL MODULE: external "@tiptap/extension-character-count"
var extension_character_count_ = __webpack_require__(856);
var extension_character_count_default = /*#__PURE__*/__webpack_require__.n(extension_character_count_);

// EXTERNAL MODULE: external "@tiptap/extension-link"
var extension_link_ = __webpack_require__(857);
var extension_link_default = /*#__PURE__*/__webpack_require__.n(extension_link_);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/form/Editor.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var Editorvue_type_script_lang_js_ = ({
  name: 'Editor',
  components: {
    EditorContent: vue_2_["EditorContent"]
  },
  props: {
    value: {
      type: String,
      required: true
    },
    counter: {
      type: Boolean,
      default: false
    },
    autoLink: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: null
    }
  },

  data() {
    return {
      editor: null,
      text: '',
      isValid: true,
      editorEl: null,
      keysPressed: {},
      isDirty: false
    };
  },

  watch: {
    value(value) {
      const isSame = this.editor.getHTML() === value;

      if (isSame) {
        return;
      }

      this.editor.commands.setContent(value, false);
    }

  },

  mounted() {
    this.editor = new vue_2_["Editor"]({
      content: this.value,
      extensions: [starter_kit_default.a, extension_character_count_default.a.configure({
        limit: this.limit
      }), extension_link_default.a.configure({
        autolink: true
      })]
    });
    this.editor.on('create', ({
      editor
    }) => {
      this.text = editor.getText();
      this.$nextTick(() => {
        this.editorEl = document.getElementsByClassName('ProseMirror')[0];

        if (this.editorEl) {
          this.editorEl.addEventListener('keydown', this.keydownHandler);
          this.editorEl.addEventListener('keyup', this.keyupHandler);
        }
      });
      this.validation();
    });
    this.editor.on('update', ({
      editor
    }) => {
      this.isDirty = true;
      this.text = editor.getText();
      this.validation();
      this.$emit('update', this.text ? editor.getHTML() : '');
    });
  },

  beforeDestroy() {
    if (this.editorEl) {
      this.editorEl.removeEventListener('keydown', this.keydownHandler);
      this.editorEl.removeEventListener('keyup', this.keyupHandler);
    }

    this.editor.destroy();
  },

  methods: {
    keydownHandler(e) {
      this.keysPressed[e.keyCode] = true;

      if ((e.ctrlKey || this.keysPressed[17] || this.keysPressed[91] || this.keysPressed[93] || this.keysPressed[224]) && this.keysPressed[13]) {
        e.preventDefault();
        this.$emit('submit');
        this.keysPressed = {};
      } else if (e.keyCode === 13 && !e.shiftKey) {
        e.preventDefault();
        this.editor.commands.enter();
      }
    },

    keyupHandler(e) {
      delete this.keysPressed[e.keyCode];
    },

    validation() {
      const strLength = this.text.trim().length;
      this.isValid = !!strLength;

      if (!!strLength && this.limit) {
        this.isValid = strLength <= this.limit;
      }

      this.$emit('validation', this.isValid);
    }

  }
});
// CONCATENATED MODULE: ./components/form/Editor.vue?vue&type=script&lang=js&
 /* harmony default export */ var form_Editorvue_type_script_lang_js_ = (Editorvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/form/Editor.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(937)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  form_Editorvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "0bb70d5d"
  
)

/* harmony default export */ var Editor = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 946:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/LessonItem.vue?vue&type=template&id=1f692612&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"lesson d-sm-flex"},[_vm._ssrNode("<div class=\"lesson-date d-flex text-center\">","</div>",[_vm._ssrNode("<div>","</div>",[(_vm.$slots.date)?[_vm._t("date")]:_vm._ssrNode(((_vm.$vuetify.breakpoint.smAndUp)?("<div class=\"weekday d-none d-sm-block\">"+_vm._ssrEscape("\n            "+_vm._s(_vm.$dayjs(_vm.startDate).format('dddd'))+"\n          ")+"</div> <div class=\"date d-none d-sm-block\">"+_vm._ssrEscape("\n            "+_vm._s(_vm.$dayjs(_vm.startDate).format('DD MMM'))+"\n          ")+"</div> <div class=\"time d-none d-sm-block\">"+_vm._ssrEscape(_vm._s(_vm.startTime))+"</div>"):("<div class=\"d-sm-none\">"+_vm._ssrEscape("\n            "+_vm._s(_vm.$dayjs(_vm.startDate).format('dddd'))+",\n            "+_vm._s(_vm.$dayjs(_vm.startDate).format('DD MMM'))+" - "+_vm._s(_vm.startTime)+"\n          ")+"</div>")))],2),_vm._ssrNode(" "+((_vm.$vuetify.breakpoint.smAndUp)?("<div class=\"duration d-none d-sm-block\"><div class=\"duration-icon\"><svg width=\"18\" height=\"18\" viewBox=\"0 0 18 18\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#clock-thin")))+"></use></svg></div>"+_vm._ssrEscape("\n      "+_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.item.lessonLength }))+"\n    ")+"</div>"):("<div class=\"duration d-sm-none\">"+_vm._ssrEscape("\n       ("+_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.item.lessonLength }))+")\n    ")+"</div>")))],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"lesson-content\">","</div>",[_vm._ssrNode("<div class=\"lesson-details\">","</div>",[_vm._ssrNode("<div class=\"avatar mr-2\">","</div>",[_c('v-avatar',{attrs:{"width":"110","height":"110"}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(_vm.item.userAvatars, 'user_thumb_110x110'),"srcset":_vm.getSrcSetAvatar(
                _vm.item.userAvatars,
                'user_thumb_110x110',
                'user_thumb_220x220'
              ),"options":{ rootMargin: '50%' }}}),_vm._v(" "),(_vm.teacherLink)?_c('nuxt-link',{attrs:{"to":_vm.teacherLink}}):_vm._e()],1),_vm._ssrNode(" "),_c('user-status',{attrs:{"user-id":_vm.userId,"user-statuses":_vm.userStatuses,"large":""}})],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"details\">","</div>",[_vm._ssrNode("<div class=\"user-info\"><div class=\"user-info-name\">"+_vm._ssrEscape("\n            "+_vm._s(_vm.userName)+"\n            ")+((_vm.isTeacher)?("<div><svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#info")))+"></use></svg></div>"):"<!---->")+"</div> <div"+(_vm._ssrClass(null,[
              'user-info-status',
              ("user-info-status--" + _vm.status),
              { 'text--red-gradient': _vm.status === 'idle' } ]))+">"+((_vm.status === 'online')?(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('online_now'))+"\n            ")):(_vm.status === 'idle')?(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('online_but_idle'))+"\n            ")):(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('offline_now'))+"\n            ")))+"</div></div> "),_vm._ssrNode("<div class=\"lesson-info\">","</div>",[_vm._ssrNode("<div class=\"avatar mr-2\">","</div>",[_c('v-avatar',{attrs:{"width":"85","height":"85"}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(_vm.item.userAvatars, 'user_thumb_110x110'),"srcset":_vm.getSrcSetAvatar(
                    _vm.item.userAvatars,
                    'user_thumb_110x110',
                    'user_thumb_220x220'
                  ),"options":{ rootMargin: '50%' }}}),_vm._v(" "),(_vm.teacherLink)?_c('nuxt-link',{attrs:{"to":_vm.teacherLink}}):_vm._e()],1),_vm._ssrNode(" "),_c('user-status',{attrs:{"user-id":_vm.userId,"user-statuses":_vm.userStatuses,"large":""}})],2),_vm._ssrNode(" "),_vm._ssrNode("<div>","</div>",[_vm._t("lessonInfo"),_vm._ssrNode(" "+((!_vm.isUnscheduled)?("<div><span class=\"text-capitalize\">"+_vm._ssrEscape(_vm._s(_vm.$t('lesson'))+":")+"</span> <span class=\"text--gradient font-weight-bold text-no-wrap\">"+_vm._ssrEscape("\n                "+_vm._s(_vm.item.lessonType)+"\n              ")+"</span></div>"):"<!---->")+" <div><span class=\"text-capitalize\">"+_vm._ssrEscape(_vm._s(_vm.$t('language'))+":")+"</span> <span class=\"text--gradient font-weight-bold text-no-wrap\">"+_vm._ssrEscape("\n                "+_vm._s(_vm.item.language.name)+"\n              ")+"</span></div> "+((_vm.item.courseName && _vm.item.courseName.length)?("<div><span class=\"text-capitalize\">"+_vm._ssrEscape(_vm._s(_vm.$t('course'))+":")+"</span> <span class=\"text--gradient font-weight-bold\">"+_vm._ssrEscape("\n                "+_vm._s(_vm.item.courseName)+"\n              ")+"</span></div>"):"<!---->")+" "),_vm._ssrNode("<div class=\"lesson-actions-additional\">","</div>",[_vm._t("lessonAdditionalActionsTop"),_vm._ssrNode(" "),(!_vm.isUnscheduleButtonHidden)?[_vm._ssrNode("<div>","</div>",[_vm._ssrNode("<span class=\"action\">","</span>",[_c('v-img',{staticStyle:{"left":"3px"},attrs:{"src":__webpack_require__(507),"width":"11","height":"11"}}),_vm._ssrNode(_vm._ssrEscape("\n                    "+_vm._s(_vm.$t('unschedule_lesson'))+"\n                  "))],2)])]:_vm._e(),_vm._ssrNode(" "),_vm._t("lessonAdditionalActionsBottom",null,{"showDialog":_vm.showDialog})],2)],2)],2)],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"lesson-actions\">","</div>",[_vm._ssrNode("<div class=\"lesson-actions-buttons\">","</div>",[(!_vm.item.userIsDeleted)?_c('v-btn',{staticClass:"btn-add gradient font-weight-medium ml-1 mb-1",attrs:{"width":"158"},on:{"click":_vm.showMessageDialog}},[_c('div',{staticClass:"mr-1"},[_c('v-img',{attrs:{"src":__webpack_require__(514),"width":"20","height":"20"}})],1),_vm._v(" "),_c('div',{staticClass:"text--gradient"},[_vm._v("\n            "+_vm._s(_vm.$t('message'))+"\n          ")])]):_vm._e(),_vm._ssrNode(" "),_vm._t("lessonActions"),_vm._ssrNode(" "),(!_vm.isUnscheduled)?[(_vm.isTeacher || (_vm.isStudent && !_vm.item.isCreated))?[_c('v-btn',{staticClass:"go-to-class-btn font-weight-medium ml-1 mb-1",attrs:{"href":("/lesson/" + (_vm.item.lessonId) + "/classroom"),"color":"primary","width":"158"}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"18","height":"18","viewBox":"0 0 18 18"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#save-icon")}})]),_vm._v("\n              "+_vm._s(_vm.$t('go_to_class'))+"\n            ")])]:[_c('v-btn',{staticClass:"go-to-class-btn go-to-class-btn--disabled primary--light font-weight-medium ml-1 mb-1",attrs:{"color":"primary","width":"158"},on:{"click":_vm.showInitializeDialog}},[_c('svg',{staticClass:"icon--rotated mr-1",attrs:{"width":"18","height":"18","viewBox":"0 0 18 18"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#save-icon")}})]),_vm._v("\n              "+_vm._s(_vm.$t('go_to_class'))+"\n              "),_c('svg',{staticClass:"icon--right",attrs:{"width":"18","height":"18","viewBox":"0 0 16 16"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#info")}})])])]]:_vm._e()],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"lesson-actions-additional\">","</div>",[_vm._t("lessonAdditionalActionsTop"),_vm._ssrNode(" "),(!_vm.isUnscheduleButtonHidden)?[_vm._ssrNode("<div>","</div>",[_vm._ssrNode("<span class=\"action\">","</span>",[_c('v-img',{staticStyle:{"left":"3px"},attrs:{"src":__webpack_require__(507),"width":"11","height":"11"}}),_vm._ssrNode(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('unschedule_lesson'))+"\n            "))],2)])]:_vm._e(),_vm._ssrNode(" "),_vm._t("lessonAdditionalActionsBottom",null,{"showDialog":_vm.showDialog})],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div"+(_vm._ssrClass(null,[
        'lesson-dialog d-flex flex-column justify-center',
        {
          'lesson-dialog--shown': _vm.isLessonDialogShown,
        },
        {
          'lesson-dialog--student-info': _vm.dialogType === 'studentInfoDialog',
        } ]))+">","</div>",[_vm._t("dialog",null,{"closeDialog":_vm.closeDialog}),_vm._ssrNode(" "),(_vm.dialogType === 'unscheduledDialog')?[_vm._ssrNode("<div class=\"lesson-dialog-title font-weight-medium text--red-gradient\">"+_vm._ssrEscape("\n          "+_vm._s(_vm.$t('are_you_sure_you_want_to_unschedule_this_lesson_with', {
              name: _vm.item.userFirstName,
            }))+"\n        ")+"</div> <div class=\"lesson-dialog-content l-scroll l-scroll--grey\">"+((_vm.isStudent)?(((_vm.item.isFreeTrial)?(_vm._ssrEscape("\n              "+_vm._s(_vm.$t(
                  'you_will_be_able_to_reschedule_this_lesson_from_your_teachers_profile_page'
                ))+"\n            ")):(_vm._ssrEscape("\n              "+_vm._s(_vm.$t(
                  'you_will_receive_credit_and_can_reschedule_lesson_for_anytime_your_teacher_is_available'
                ))+"\n            ")))):(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('student_will_be_given_credit_for_lesson'))+"\n          ")))+"</div> "),_vm._ssrNode("<div class=\"lesson-dialog-buttons\">","</div>",[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"greyDark","outlined":""},on:{"click":_vm.closeDialog}},[_vm._v("\n            "+_vm._s(_vm.$t('do_not_cancel_lesson'))+"\n          ")]),_vm._ssrNode(" "),_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"error"},on:{"click":_vm.cancelClickHandler}},[_vm._v("\n            "+_vm._s(_vm.$t('cancel_lesson'))+"\n          ")])],2)]:_vm._e(),_vm._ssrNode(" "),(_vm.dialogType === 'initializeDialog')?[_vm._ssrNode("<div class=\"lesson-dialog-title font-weight-medium text--gradient\"><div class=\"lesson-dialog-title-icon\"><svg width=\"20\" height=\"20\" viewBox=\"0 0 12 12\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#attention")))+"></use></svg></div>"+_vm._ssrEscape("\n          "+_vm._s(_vm.$t('your_teacher_will_enter_classroom_first'))+"\n        ")+"</div> <div class=\"lesson-dialog-content l-scroll l-scroll--grey\">"+(_vm._s(
            _vm.$t(
              'after_your_teacher_enters_go_to_class_button_will_become_clickable_so_you_can_enter_as_well'
            )
          ))+"</div> "),_vm._ssrNode("<div class=\"lesson-dialog-buttons\">","</div>",[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"primary","max-width":"158"},on:{"click":_vm.closeDialog}},[_vm._v("\n            OK!\n          ")])],1)]:_vm._e(),_vm._ssrNode(" "),(_vm.dialogType === 'studentInfoDialog')?[_vm._ssrNode("<div class=\"lesson-dialog-title font-weight-medium text--gradient\"><div class=\"lesson-dialog-title-icon\"><svg width=\"20\" height=\"20\" viewBox=\"0 0 16 16\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#info")))+"></use></svg></div>"+_vm._ssrEscape("\n          "+_vm._s(_vm.$t('student_info'))+"\n        ")+"</div> <div class=\"lesson-dialog-content l-scroll l-scroll--grey\"><div><div><ul><li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('name'))+":")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.studentInfo.name)+"\n                ")+"</li> <li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('lifetime_free_trials_scheduled'))+":")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.studentInfo.freeTrialScheduled)+"\n                ")+"</li> <li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('lifetime_lessons_purchased'))+":")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.studentInfo.lessonsPurchased)+"\n                ")+"</li> <li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('lifetime_teachers_booked_with'))+":")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.studentInfo.teachersBookedWith)+"\n                ")+"</li></ul></div> <div class=\"pl-1\"><ul><li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('current_time'))+":")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.$dayjs().tz(_vm.studentInfo.timezone).format('LT'))+"\n                  ("+_vm._s(_vm.$dayjs().tz(_vm.studentInfo.timezone).format('z'))+",\n                  "+_vm._s(_vm.studentInfo.timezone)+")\n                ")+"</li> <li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('total_spent_with_you'))+" ("+_vm._s(_vm.currencyIsoCode)+"):")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.currencySymbol)+_vm._s(_vm.getPrice(_vm.studentInfo.totalSpendWithTeacher))+"\n                ")+"</li> <li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('date_registered_on_langu'))+":")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.$dayjs(_vm.studentInfo.dateRegistered)
                      .tz(_vm.timeZone)
                      .format('ll, LT'))+"\n                ")+"</li> <li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('last_online'))+":")+"</span> "+((_vm.status === 'online')?(_vm._ssrEscape("\n                    "+_vm._s(_vm.$t('online_now'))+"\n                  ")):(_vm.status === 'idle')?(_vm._ssrEscape("\n                    "+_vm._s(_vm.$t('online_but_idle'))+"\n                  ")):(_vm._ssrEscape("\n                    "+_vm._s(_vm.$dayjs(_vm.studentInfo.lastLoginDate)
                        .tz(_vm.timeZone)
                        .format('ll, LT'))+"\n                  ")))+"</li></ul></div></div></div> "),_vm._ssrNode("<div class=\"lesson-dialog-buttons\">","</div>",[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"primary","max-width":"158"},on:{"click":_vm.closeDialog}},[_vm._v("\n            OK\n          ")])],1)]:_vm._e()],2)],2),_vm._ssrNode(" "),(_vm.isShownMessageDialog)?_c('message-dialog',{attrs:{"recipient-id":_vm.userId,"recipient-name":((_vm.item.userFirstName) + " " + (_vm.item.userLastName)),"is-shown-message-dialog":_vm.isShownMessageDialog},on:{"close-dialog":function($event){_vm.isShownMessageDialog = false}}}):_vm._e(),_vm._ssrNode(" "),_vm._t("default")],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-lessons/LessonItem.vue?vue&type=template&id=1f692612&

// EXTERNAL MODULE: ./helpers/index.js
var helpers = __webpack_require__(501);

// EXTERNAL MODULE: ./mixins/Avatars.vue + 2 modules
var Avatars = __webpack_require__(932);

// EXTERNAL MODULE: ./components/UserStatus.vue + 4 modules
var UserStatus = __webpack_require__(916);

// EXTERNAL MODULE: ./components/MessageDialog.vue + 4 modules
var MessageDialog = __webpack_require__(952);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/LessonItem.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var LessonItemvue_type_script_lang_js_ = ({
  name: 'LessonItem',
  components: {
    UserStatus: UserStatus["default"],
    MessageDialog: MessageDialog["default"]
  },
  mixins: [Avatars["a" /* default */]],
  props: {
    item: {
      type: Object,
      required: true
    },
    userStatuses: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      getPrice: helpers["getPrice"],
      isLessonDialogShown: false,
      dialogType: null,
      isShownMessageDialog: false,
      studentInfo: null
    };
  },

  computed: {
    isTeacher() {
      return this.$store.getters['user/isTeacher'];
    },

    isStudent() {
      return this.$store.getters['user/isStudent'];
    },

    userId() {
      return this.item[this.isTeacher ? 'studentId' : 'teacherId'];
    },

    userIsDeleted() {
      return this.item.userIsDeleted;
    },

    userName() {
      return this.userIsDeleted ? this.$t('deleted_user') : `${this.item.userFirstName} ${this.item.userLastName}`;
    },

    currencyIsoCode() {
      return this.$store.state.currency.item.isoCode;
    },

    currencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    },

    status() {
      var _this$userId;

      let status = 'offline';

      if (Object.prototype.hasOwnProperty.call(this.userStatuses, (_this$userId = this.userId) === null || _this$userId === void 0 ? void 0 : _this$userId.toString())) {
        status = this.userStatuses[this.userId];
      }

      return status;
    },

    isPast() {
      return this.item.type === 'past';
    },

    isUnscheduled() {
      return this.item.type === 'unscheduled';
    },

    timeZone() {
      return this.$store.getters['user/timeZone'];
    },

    startDate() {
      return this.$dayjs(this.item.startDate).tz(this.timeZone);
    },

    startTime() {
      return this.startDate.format('LT');
    },

    isUnscheduleButtonHidden() {
      return this.isUnscheduled || this.item.isFinished || this.isStudent && (this.isPast || this.$dayjs().add(1, 'day').isAfter(this.startDate, 'minute'));
    },

    teacherLink() {
      return this.isStudent && !this.item.userIsDeleted && this.item.teacherUsername ? `/teacher/${this.item.teacherUsername}` : null;
    }

  },
  methods: {
    showMessageDialog() {
      this.$store.dispatch('message/checkConversation', this.userId).then(res => {
        if (res.threadId) {
          this.$store.dispatch('loadingStart');
          this.$router.push({
            path: `/user/messages/${res.threadId}/view`
          });
        } else {
          this.isShownMessageDialog = true;
        }
      });
    },

    cancelClickHandler() {
      this.$store.dispatch('loadingStart');
      this.$store.dispatch('lesson/cancelLesson', this.item.lessonId).then(() => {
        location.reload();
      }).catch(e => {
        this.$store.dispatch('loadingStop');
        this.$store.dispatch('snackbar/error');
        console.info(e);
      });
    },

    studentInfoClickHandler() {
      this.$store.dispatch('lesson/getStudentInfo', this.item.studentId).then(res => {
        this.studentInfo = res;
        this.dialogType = 'studentInfoDialog';
        this.showDialog();
      });
    },

    showInitializeDialog() {
      this.dialogType = 'initializeDialog';
      this.showDialog();
    },

    showUnscheduledDialog() {
      this.dialogType = 'unscheduledDialog';
      this.showDialog();
    },

    showDialog() {
      this.isLessonDialogShown = true;
    },

    closeDialog() {
      this.isLessonDialogShown = false;
      setTimeout(() => {
        this.dialogType = null;
      }, 500);
    }

  }
});
// CONCATENATED MODULE: ./components/user-lessons/LessonItem.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_lessons_LessonItemvue_type_script_lang_js_ = (LessonItemvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/VAvatar.js
var VAvatar = __webpack_require__(830);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-lessons/LessonItem.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(997)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_lessons_LessonItemvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "7dfe9174"
  
)

/* harmony default export */ var LessonItem = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserStatus: __webpack_require__(916).default,MessageDialog: __webpack_require__(952).default})


/* vuetify-loader */




installComponents_default()(component, {VAvatar: VAvatar["a" /* default */],VBtn: VBtn["a" /* default */],VImg: VImg["a" /* default */]})


/***/ }),

/***/ 952:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/MessageDialog.vue?vue&type=template&id=01f70911&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{"dialog":_vm.isShownMessageDialog,"max-width":"725","custom-class":"message-dialog","persistent":"","fullscreen":_vm.$vuetify.breakpoint.xsOnly}},_vm.$listeners),[_c('v-form',{on:{"submit":function($event){$event.preventDefault();return _vm.submitHandler.apply(null, arguments)}}},[_c('div',{staticClass:"message-dialog-header text--gradient"},[_vm._v("\n      "+_vm._s(_vm.$t(
          _vm.$vuetify.breakpoint.xsOnly
            ? 'ask_question'
            : 'questions_ask_teacher_directly'
        ))+"\n    ")]),_vm._v(" "),_c('div',{class:[
        'message-dialog-body pt-0 pt-sm-3',
        {
          'l-scroll l-scroll--grey l-scroll--large':
            _vm.$vuetify.breakpoint.xsOnly,
        } ]},[_c('v-row',{attrs:{"no-gutters":""}},[_c('v-col',{staticClass:"col-12 col-sm-5 col-md-6"},[_c('div',{staticClass:"message-dialog-text pr-0 pr-sm-2"},[_c('div',{directives:[{name:"show",rawName:"v-show",value:(_vm.$vuetify.breakpoint.xsOnly),expression:"$vuetify.breakpoint.xsOnly"}]},[_c('span',{staticClass:"text-capitalize"},[_vm._v(_vm._s(_vm.$t('teacher')))]),_vm._v(": "+_vm._s(_vm.recipientName)),_c('br'),_vm._v(" "),_c('br')]),_vm._v(" "),_c('div',{domProps:{"innerHTML":_vm._s(_vm.$t('message_dialog_text', { recipientName: _vm.recipientName }))}})])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-7 col-md-6 mt-3 mt-sm-0"},[_c('editor',{attrs:{"value":_vm.newMessage,"limit":6000},on:{"update":function($event){_vm.newMessage = $event},"validation":function($event){_vm.isMessageValid = $event}}})],1)],1)],1),_vm._v(" "),_c('div',{staticClass:"message-dialog-footer d-flex justify-space-between align-center"},[_c('div',{staticClass:"prev-button body-1",on:{"click":function($event){return _vm.$emit('close-dialog')}}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"17","height":"12","viewBox":"0 0 17 12"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#arrow-prev")}})]),_vm._v(_vm._s(_vm.$t('go_back'))+"\n      ")]),_vm._v(" "),_c('div',[_c('v-btn',{attrs:{"small":"","color":"primary","type":"submit"}},[_vm._v("\n          "+_vm._s(_vm.$t('send_message'))+"\n        ")])],1)])])],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/MessageDialog.vue?vue&type=template&id=01f70911&

// EXTERNAL MODULE: ./components/LDialog.vue + 5 modules
var LDialog = __webpack_require__(28);

// EXTERNAL MODULE: ./components/form/Editor.vue + 4 modules
var Editor = __webpack_require__(942);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/MessageDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var MessageDialogvue_type_script_lang_js_ = ({
  name: 'MessageDialog',
  components: {
    LDialog: LDialog["default"],
    Editor: Editor["default"]
  },
  props: {
    recipientId: {
      type: Number,
      required: true
    },
    recipientName: {
      type: String,
      required: true
    },
    isShownMessageDialog: {
      type: Boolean,
      required: true
    }
  },

  data() {
    return {
      newMessage: '',
      isMessageValid: false
    };
  },

  beforeDestroy() {
    this.newMessage = '';
    this.isMessageValid = false;
  },

  methods: {
    submitHandler() {
      if (this.isMessageValid) {
        this.$store.dispatch('message/sendNewMessage', {
          recipientId: this.recipientId,
          message: this.newMessage
        }).then(res => {
          this.newMessage = '';
          this.$router.push({
            path: `/user/messages/${res.id}/view`
          });
        }).finally(() => this.$emit('close-dialog'));
      }
    }

  }
});
// CONCATENATED MODULE: ./components/MessageDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_MessageDialogvue_type_script_lang_js_ = (MessageDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(896);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/MessageDialog.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(978)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_MessageDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "06ad70c7"
  
)

/* harmony default export */ var MessageDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LDialog: __webpack_require__(28).default})


/* vuetify-loader */





installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VForm: VForm["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 956:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(998);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("a8b919c2", content, true, context)
};

/***/ }),

/***/ 958:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(933);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 959:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-status[data-v-652352c7]{width:16px;height:16px;border-radius:50%;border:2px solid #fff;background:#636363;z-index:2}.user-status--idle[data-v-652352c7]{background:linear-gradient(122.42deg,var(--v-redLight-base),var(--v-orangeLight2-base))}.user-status--online[data-v-652352c7]{background:var(--v-success-base)}.user-status--large[data-v-652352c7]{width:25px;height:25px}@media only screen and (max-width:991px){.user-status--large[data-v-652352c7]{width:23px;height:23px}}@media only screen and (max-width:639px){.user-status--large[data-v-652352c7]{width:21px;height:21px}}@media only screen and (max-width:479px){.user-status--large[data-v-652352c7]{width:19px;height:19px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 978:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessageDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(939);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessageDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessageDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessageDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessageDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 979:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-application .v-dialog.message-dialog>.v-card{padding:32px 40px!important}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog>.v-card{padding:50px 18px 74px!important}.v-application .v-dialog.message-dialog>.v-card .dialog-content,.v-application .v-dialog.message-dialog>.v-card .message-dialog-body,.v-application .v-dialog.message-dialog>.v-card .v-form{height:100%}.v-application .v-dialog.message-dialog>.v-card .message-dialog-body{overflow-y:auto}}.v-application .v-dialog.message-dialog .message-dialog-header{display:inline-block;padding-right:60px;font-size:20px;font-weight:700;line-height:1.1}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-header{position:absolute;top:0;left:0;width:100%;height:50px;display:flex;align-items:center;padding-left:18px;font-size:18px}}.v-application .v-dialog.message-dialog .message-dialog-body .row .col:first-child{padding-right:20px}.v-application .v-dialog.message-dialog .message-dialog-body .row .col:last-child{padding-left:20px}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-text div:first-child{font-size:16px;font-weight:600}}.v-application .v-dialog.message-dialog .message-dialog-text ul{padding-left:20px}@media only screen and (min-width:992px){.v-application .v-dialog.message-dialog .message-dialog-footer{margin-top:28px}}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-footer{position:absolute;bottom:0;left:0;width:100%;height:74px;padding:0 18px}}.v-application .v-dialog.message-dialog .message-dialog-footer .prev-button{color:var(--v-orange-base);cursor:pointer}.v-application .v-dialog.message-dialog .text-editor .ProseMirror{min-height:248px}.v-application .v-dialog.message-dialog .text-editor-buttons{left:8px;right:auto}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 997:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(956);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 998:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".lesson{position:relative;min-height:128px;padding-left:142px}@media only screen and (max-width:1215px){.lesson{min-height:130px;padding-left:85px}}@media only screen and (min-width:768px){.lesson{background:#fff;border-radius:16px;box-shadow:0 6px 10px rgba(17,46,90,.08)}}@media only screen and (max-width:991px){.lesson{padding-left:112px}}@media only screen and (max-width:767px){.lesson{padding-left:0}}.lesson-date{justify-content:center;align-items:center;width:142px;padding:11px;font-size:16px;font-weight:600;background:linear-gradient(126.15deg,rgba(128,182,34,.74),rgba(60,135,248,.74) 102.93%);border-radius:16px;color:#fff}@media only screen and (min-width:768px){.lesson-date{position:absolute;left:0;top:0;height:100%;flex-direction:column;justify-content:space-between;box-shadow:4px 5px 8px hsla(0,0%,40%,.25);z-index:3}}@media only screen and (max-width:1215px){.lesson-date{width:85px;padding:14px 6px;font-size:15px}}@media only screen and (max-width:991px){.lesson-date{width:112px;padding:10px 4px;font-size:13px}}@media only screen and (max-width:767px){.lesson-date{width:100%;padding:7px 10px 32px;font-size:16px!important;font-weight:600!important;border-radius:10px 10px 0 0}.lesson-date>div{display:flex;align-items:center}}@media only screen and (max-width:480px){.lesson-date{font-size:14px!important;font-weight:400!important}}.lesson-date .time,.lesson-date .weekday{font-size:13px;font-weight:700;line-height:1}.lesson-date .date{font-weight:700;font-size:24px;line-height:1.2;white-space:nowrap}@media only screen and (max-width:1215px){.lesson-date .date{margin-bottom:4px;font-size:20px}}@media only screen and (max-width:767px){.lesson-date .date{margin-bottom:0;font-weight:inherit;font-size:inherit}}.lesson-date .remaining{line-height:1.23}@media only screen and (min-width:768px){.lesson-date .remaining{font-size:13px}}@media only screen and (max-width:767px){.lesson-date .remaining{padding:0 3px}}.lesson-date .duration{white-space:nowrap}@media only screen and (min-width:1216px){.lesson-date .duration{padding-left:22px}}@media only screen and (min-width:768px){.lesson-date .duration{position:relative;font-weight:400;color:#e8f1f7}}.lesson-date .duration-icon{position:absolute;left:0;top:50%;width:18px;height:18px;margin-top:-9px;color:var(--v-dark-lighten3)}@media only screen and (max-width:1215px){.lesson-date .duration-icon{display:none}}.lesson-content{position:relative;display:flex;justify-content:space-between;width:100%;border-radius:0 10px 10px 0;overflow:hidden}@media only screen and (max-width:767px){.lesson-content{min-height:128px;margin-top:-25px;border-radius:10px;background:#fff;box-shadow:0 1px 5px hsla(0,0%,51%,.3);z-index:2}}@media only screen and (max-width:639px){.lesson-content{flex-direction:column;padding:12px 12px 4px}}.lesson-details{display:flex;padding:8px 10px 8px 18px}@media only screen and (max-width:1215px){.lesson-details{padding:12px 4px 8px 12px}.lesson-details>.avatar{display:none}}@media only screen and (max-width:767px){.lesson-details{padding-top:10px}}@media only screen and (max-width:639px){.lesson-details{position:relative;padding:0 0 0 100px}}.lesson-details .avatar{position:relative}@media only screen and (max-width:639px){.lesson-details .avatar{position:absolute;left:0;top:0}}.lesson-details .avatar .user-status{position:absolute;top:0;right:0}@media only screen and (max-width:479px){.lesson-details .avatar .user-status{top:1px;right:1px}}.lesson-details .avatar a{position:absolute;top:0;left:0;width:100%;height:100%}.lesson-details .details{display:flex;flex-direction:column;justify-content:space-between}.lesson-details .details .user-info{line-height:1.1}@media only screen and (max-width:1439px){.lesson-details .details .user-info{margin-bottom:8px}}@media only screen and (min-width:1216px){.lesson-details .details .user-info{padding-top:2px}}.lesson-details .details .user-info-name{display:inline;font-size:24px}@media only screen and (min-width:1440px){.lesson-details .details .user-info-name{margin-right:6px}}@media only screen and (max-width:1439px){.lesson-details .details .user-info-name{font-size:22px}}@media only screen and (max-width:1215px){.lesson-details .details .user-info-name{font-size:20px}}@media only screen and (max-width:479px){.lesson-details .details .user-info-name{font-size:18px}}.lesson-details .details .user-info-name div{position:relative;top:-2px;display:inline-block;height:16px;color:var(--v-dark-lighten3);cursor:pointer}.lesson-details .details .user-info-status{display:inline;font-size:13px;color:var(--v-dark-lighten3);text-transform:lowercase}.lesson-details .details .user-info-status--online{color:var(--v-green-lighten1)}.lesson-details .details .lesson-info{font-size:16px;color:var(--v-dark-lighten3);line-height:1.1}@media only screen and (min-width:768px){.lesson-details .details .lesson-info{padding-bottom:4px}}@media only screen and (min-width:1216px){.lesson-details .details .lesson-info .avatar{display:none!important}}@media only screen and (max-width:1215px){.lesson-details .details .lesson-info{display:flex;align-items:flex-end;font-size:14px}}.lesson-details .details .lesson-info>div:not(.avatar)>div:not(:last-child){margin-bottom:3px}@media only screen and (max-width:639px){.lesson-details .details .lesson-info>div:not(.avatar)>div:not(:last-child){margin-bottom:5px}}@media only screen and (min-width:640px){.lesson-details .details .lesson-info .lesson-actions-additional{display:none!important}}.lesson-actions .v-btn:not(.v-btn--icon).v-size--default,.lesson-dialog .v-btn:not(.v-btn--icon).v-size--default{min-width:158px!important;padding:0 10px!important}.lesson-actions{display:flex;width:350px;padding:18px 18px 10px 0}@media only screen and (max-width:1215px){.lesson-actions{padding:12px 12px 8px 0}}@media only screen and (max-width:767px){.lesson-actions{padding-top:10px}}@media only screen and (min-width:640px){.lesson-actions{flex-direction:column;align-items:flex-end;justify-content:space-between;flex-grow:1}}@media only screen and (max-width:639px){.lesson-actions{width:100%;margin-top:8px;padding:8px 0 0;border-top:1px solid rgba(0,0,0,.08)}}.lesson-actions-buttons{display:flex;justify-content:flex-end}@media only screen and (max-width:639px){.lesson-actions-buttons{justify-content:space-between;width:100%}.lesson-actions-buttons .v-btn{width:158px!important;margin-left:0!important;margin-right:0!important}}.lesson-actions-buttons .go-to-class-btn .icon--right{margin-left:3px}.lesson-actions-buttons .go-to-class-btn .icon--rotated{transform:rotate(-90deg)}.lesson-actions-additional{min-width:158px;font-size:13px;line-height:1.333}@media only screen and (max-width:639px){.lesson-actions-additional{font-size:inherit;line-height:inherit}}@media only screen and (max-width:639px){.lesson-actions-additional>div{margin-top:5px}}.lesson-actions-additional>div>div,.lesson-actions-additional a,.lesson-actions-additional span.action{position:relative;display:inline-block;padding-left:20px;color:var(--v-darkLight-lighten3)!important;text-decoration:none;transition:color .3s}.lesson-actions-additional>div>div .v-image,.lesson-actions-additional a .v-image,.lesson-actions-additional span.action .v-image{position:absolute;left:0;top:50%;transform:translateY(-50%)}.lesson-actions-additional a,.lesson-actions-additional span.action{cursor:pointer}.lesson-actions-additional a:hover,.lesson-actions-additional span.action:hover{color:var(--v-success-base)!important}@media only screen and (max-width:639px){.lesson-actions .lesson-actions-additional{display:none!important}}.lesson-dialog{position:absolute;top:0;left:100%;width:100%;height:100%;padding:10px 18px 12px;font-size:13px;line-height:1.2;background-color:#fff;border-radius:0 16px 16px 0;z-index:2;transition:all .3s ease-in}@media only screen and (max-width:1215px){.lesson-dialog{padding:8px 12px}}@media only screen and (max-width:991px){.lesson-dialog{left:0;top:100%}}@media only screen and (max-width:639px){.lesson-dialog.justify-center{justify-content:space-between!important}}.lesson-dialog-title{display:flex;margin-bottom:5px;font-size:20px}@media only screen and (max-width:639px){.lesson-dialog-title{margin-bottom:12px;font-size:18px}}.lesson-dialog-title-icon{display:flex;align-items:center;padding-right:4px}.lesson-dialog-content{color:var(--v-dark-lighten3);overflow-y:auto}@media only screen and (max-width:639px){.lesson-dialog-buttons{display:flex;justify-content:space-between;margin-top:8px;border-top:1px solid rgba(0,0,0,.08)}}.lesson-dialog-buttons .v-btn{max-width:246px;margin-top:8px}@media only screen and (min-width:640px){.lesson-dialog-buttons .v-btn{width:100%}}.lesson-dialog-buttons>:first-child{margin-right:16px}@media only screen and (max-width:639px){.lesson-dialog-buttons>:first-child{margin-left:4px}}.lesson-dialog--student-info .lesson-dialog-content{display:flex}@media only screen and (min-width:640px){.lesson-dialog--student-info .lesson-dialog-content{margin-right:168px}}.lesson-dialog--student-info .lesson-dialog-content>div{display:flex;flex-grow:1}@media only screen and (max-width:639px){.lesson-dialog--student-info .lesson-dialog-content>div{width:100%;max-width:100%}}@media only screen and (max-width:479px){.lesson-dialog--student-info .lesson-dialog-content>div{flex-wrap:wrap}}@media only screen and (max-width:479px){.lesson-dialog--student-info .lesson-dialog-content>div>div{padding:0!important}}@media only screen and (min-width:480px){.lesson-dialog--student-info .lesson-dialog-content>div>div:first-child{padding-right:45px;border-right:1px solid var(--v-dark-lighten3)}}.lesson-dialog--student-info .lesson-dialog-content>div>div ul{padding:0;list-style-type:none;line-height:1.2}@media only screen and (min-width:1216px){.lesson-dialog--student-info .lesson-dialog-buttons{right:18px;bottom:12px}}@media only screen and (min-width:640px){.lesson-dialog--student-info .lesson-dialog-buttons{position:absolute;right:12px;bottom:8px}}@media only screen and (max-width:639px){.lesson-dialog--student-info .lesson-dialog-buttons{display:flex;justify-content:flex-end;align-items:flex-end}}.lesson-dialog--student-info .lesson-dialog-buttons .v-btn{margin-right:0!important}.lesson-dialog--shown{left:0;transition:all .3s ease-out}@media only screen and (max-width:991px){.lesson-dialog--shown{top:0}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

};;
//# sourceMappingURL=user-lessons-lesson-item.js.map