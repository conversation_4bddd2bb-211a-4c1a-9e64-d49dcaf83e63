(window.webpackJsonp=window.webpackJsonp||[]).push([[89,88],{1435:function(t,e,n){var content=n(1493);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("fd0dd7ee",content,!0,{sourceMap:!1})},1446:function(t,e,n){var content=n(1516);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("20cd0fe8",content,!0,{sourceMap:!1})},1486:function(t,e,n){var content=n(1487);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("197fcea4",content,!0,{sourceMap:!1})},1487:function(t,e,n){var o=n(18)(!1);o.push([t.i,'.v-chip:not(.v-chip--outlined).accent,.v-chip:not(.v-chip--outlined).error,.v-chip:not(.v-chip--outlined).info,.v-chip:not(.v-chip--outlined).primary,.v-chip:not(.v-chip--outlined).secondary,.v-chip:not(.v-chip--outlined).success,.v-chip:not(.v-chip--outlined).warning{color:#fff}.theme--light.v-chip{border-color:rgba(0,0,0,.12);color:rgba(0,0,0,.87)}.theme--light.v-chip:not(.v-chip--active){background:#e0e0e0}.theme--light.v-chip:hover:before{opacity:.04}.theme--light.v-chip--active:before,.theme--light.v-chip--active:hover:before,.theme--light.v-chip:focus:before{opacity:.12}.theme--light.v-chip--active:focus:before{opacity:.16}.theme--dark.v-chip{border-color:hsla(0,0%,100%,.12);color:#fff}.theme--dark.v-chip:not(.v-chip--active){background:#555}.theme--dark.v-chip:hover:before{opacity:.08}.theme--dark.v-chip--active:before,.theme--dark.v-chip--active:hover:before,.theme--dark.v-chip:focus:before{opacity:.24}.theme--dark.v-chip--active:focus:before{opacity:.32}.v-chip{align-items:center;cursor:default;display:inline-flex;line-height:20px;max-width:100%;outline:none;overflow:hidden;padding:0 12px;position:relative;text-decoration:none;transition-duration:.28s;transition-property:box-shadow,opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);vertical-align:middle;white-space:nowrap}.v-chip:before{background-color:currentColor;bottom:0;border-radius:inherit;content:"";left:0;opacity:0;position:absolute;pointer-events:none;right:0;top:0}.v-chip .v-avatar{height:24px!important;min-width:24px!important;width:24px!important}.v-chip .v-icon{font-size:24px}.v-application--is-ltr .v-chip .v-avatar--left,.v-application--is-ltr .v-chip .v-icon--left{margin-left:-6px;margin-right:6px}.v-application--is-ltr .v-chip .v-avatar--right,.v-application--is-ltr .v-chip .v-icon--right,.v-application--is-rtl .v-chip .v-avatar--left,.v-application--is-rtl .v-chip .v-icon--left{margin-left:6px;margin-right:-6px}.v-application--is-rtl .v-chip .v-avatar--right,.v-application--is-rtl .v-chip .v-icon--right{margin-left:-6px;margin-right:6px}.v-chip:not(.v-chip--no-color) .v-icon{color:inherit}.v-chip .v-chip__close.v-icon{font-size:18px;max-height:18px;max-width:18px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right{margin-right:-4px}.v-application--is-rtl .v-chip .v-chip__close.v-icon.v-icon--right{margin-left:-4px}.v-chip .v-chip__close.v-icon:active,.v-chip .v-chip__close.v-icon:focus,.v-chip .v-chip__close.v-icon:hover{opacity:.72}.v-chip .v-chip__content{align-items:center;display:inline-flex;height:100%;max-width:100%}.v-chip--active .v-icon{color:inherit}.v-chip--link:before{transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-chip--link:focus:before{opacity:.32}.v-chip--clickable{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip--clickable:active{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-chip--disabled{opacity:.4;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip__filter{max-width:24px}.v-chip__filter.v-icon{color:inherit}.v-chip__filter.expand-x-transition-enter,.v-chip__filter.expand-x-transition-leave-active{margin:0}.v-chip--pill .v-chip__filter{margin-right:0 16px 0 0}.v-chip--pill .v-avatar{height:32px!important;width:32px!important}.v-application--is-ltr .v-chip--pill .v-avatar--left{margin-left:-12px}.v-application--is-ltr .v-chip--pill .v-avatar--right,.v-application--is-rtl .v-chip--pill .v-avatar--left{margin-right:-12px}.v-application--is-rtl .v-chip--pill .v-avatar--right{margin-left:-12px}.v-chip--label{border-radius:4px!important}.v-chip.v-chip--outlined{border-width:thin;border-style:solid}.v-chip.v-chip--outlined.v-chip--active:before{opacity:.08}.v-chip.v-chip--outlined .v-icon{color:inherit}.v-chip.v-chip--outlined.v-chip.v-chip{background-color:transparent!important}.v-chip.v-chip--selected{background:transparent}.v-chip.v-chip--selected:after{opacity:.28}.v-chip.v-size--x-small{border-radius:8px;font-size:10px;height:16px}.v-chip.v-size--small{border-radius:12px;font-size:12px;height:24px}.v-chip.v-size--default{border-radius:16px;font-size:14px;height:32px}.v-chip.v-size--large{border-radius:27px;font-size:16px;height:54px}.v-chip.v-size--x-large{border-radius:33px;font-size:18px;height:66px}',""]),t.exports=o},1492:function(t,e,n){"use strict";n(1435)},1493:function(t,e,n){var o=n(18)(!1);o.push([t.i,".payment-item[data-v-995c1e74]{display:flex;background:#fff;border-radius:14px;margin-bottom:12px;overflow:hidden;box-shadow:0 4px 12px rgba(0,0,0,.1)}.payment-item[data-v-995c1e74]:hover{box-shadow:0 4px 14px rgba(217,225,236,.47)}.payment-item-date[data-v-995c1e74]{min-width:100px;padding:11px;display:flex;flex-direction:column;align-items:center;width:142px;border-radius:16px;justify-content:center;background:linear-gradient(126.15deg,rgba(128,182,34,.74),rgba(60,135,248,.74) 102.93%);color:#fff;box-shadow:4px 0 8px rgba(0,0,0,.1);position:relative;z-index:1}.payment-item-date .weekday[data-v-995c1e74]{font-size:13px;font-weight:700;line-height:1;text-transform:capitalize;text-align:center}.payment-item-date .date[data-v-995c1e74]{font-size:24px;font-weight:700;line-height:1.2;margin-bottom:2px}.payment-item-date .time[data-v-995c1e74]{font-size:13px;line-height:1;font-weight:700;margin-bottom:18px;text-align:center}.payment-item-date .duration-icon[data-v-995c1e74]{color:var(--v-dark-lighten3)}.payment-item-date .duration[data-v-995c1e74]{display:flex;align-items:center;font-size:16px}.payment-item-date .duration span[data-v-995c1e74]{color:#e8f1f7}.payment-item-date .duration-icon[data-v-995c1e74]{margin-right:4px;display:flex;align-items:center}.payment-item-content[data-v-995c1e74]{flex:1;padding:16px 24px}.payment-item-content .payment-info .student-name[data-v-995c1e74]{font-size:24px;font-weight:500;color:#333;margin-bottom:12px}.payment-item-content .payment-info .details[data-v-995c1e74]{display:flex;align-items:center;grid-gap:24px;gap:24px;font-size:14px}.payment-item-content .payment-info .details .detail-group[data-v-995c1e74]{align-items:center;grid-gap:6px;gap:6px}.payment-item-content .payment-info .details .detail-group p[data-v-995c1e74]{margin:0}.payment-item-content .payment-info .details .detail-group .label[data-v-995c1e74]{color:#666;font-size:14px}.payment-item-content .payment-info .details .detail-group .value[data-v-995c1e74]{color:#333}.payment-item-content .payment-info .details .detail-group .value.gradient-text[data-v-995c1e74]{background:linear-gradient(126.15deg,#80b622,#3c87f8 102.93%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;font-weight:500;font-size:16px;line-height:18px}.payment-item-content .payment-info .details .detail-group .pdf-download-link[data-v-995c1e74]{cursor:pointer}.payment-item-content .payment-info .details .detail-group .pdf-download-link[data-v-995c1e74]:hover{text-decoration:underline}.d-none[data-v-995c1e74]{display:none}@media screen and (min-width:768px){.d-sm-none[data-v-995c1e74]{display:none}}@media screen and (min-width:768px){.d-sm-block[data-v-995c1e74]{display:block}}@media screen and (max-width:768px){.payment-item[data-v-995c1e74]{flex-direction:column;margin-bottom:16px;box-shadow:none;background:transparent}.payment-item[data-v-995c1e74],.payment-item-date[data-v-995c1e74]{box-shadow:4px 0 8px rgba(0,0,0,.1)}.payment-item-date[data-v-995c1e74]{width:auto;min-height:auto;padding:8px 16px;flex-direction:row;justify-content:flex-start;border-radius:24px;margin-bottom:8px}.payment-item-date .date[data-v-995c1e74]{margin-right:8px;margin-bottom:0}.payment-item-date .time[data-v-995c1e74]{margin-left:0;opacity:1;margin-bottom:0}.payment-item-content[data-v-995c1e74]{background:#fff;border-radius:12px;padding:16px;box-shadow:0 4px 12px rgba(0,0,0,.1)}.payment-item-content .payment-info .student-name[data-v-995c1e74]{font-size:20px;margin-bottom:4px;padding-bottom:12px;border-bottom:1px solid rgba(0,0,0,.1)}.payment-item-content .payment-info .details[data-v-995c1e74]{flex-direction:column;grid-gap:8px;gap:8px}.payment-item-content .payment-info .details .detail-group[data-v-995c1e74]{display:flex;justify-content:space-between;width:100%}.payment-item-content .payment-info .details .detail-group .value[data-v-995c1e74]{font-size:16px;font-weight:500}.payment-item-content .payment-info .details .detail-group[data-v-995c1e74]:first-child{margin-bottom:4px}}",""]),t.exports=o},1504:function(t,e,n){"use strict";n.r(e);n(7),n(8),n(9),n(14),n(6),n(15);var o=n(2),r=(n(23),n(174),n(31),n(859));function c(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var l={date:"2023-11-18",time:"9:00 AM",student:"Kathrin Donaldson",lessonType:"Trial",status:"Finished",completedAt:"18 Nov, 10:02 AM",invoiceNo:"8395",lessonNo:"295032",value:"12.50",lessonLength:30},d={name:"PaymentItem",props:{item:{type:Object,required:!0,default:function(){return function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?c(Object(source),!0).forEach((function(e){Object(o.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):c(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({},l)},validator:function(t){return["date","time","student","lessonType","status","completedAt","invoiceNo","lessonNo","value"].every((function(e){return e in t}))}}},computed:{lessonLength:function(){return this.item.lessonLength||30},userLocale:function(){var t;return this.$store.getters["user/isUserLogged"]?(null===(t=this.$store.state.user.item)||void 0===t?void 0:t.uiLanguage)||this.$i18n.locale:this.$i18n.locale||"en"},timeZone:function(){return this.$store.getters["user/timeZone"]},currentCurrencySymbol:function(){return this.$store.getters["currency/currentCurrencySymbol"]},currentCurrency:function(){return this.$store.state.currency.item}},methods:{formatDate:function(t){try{return this.$dayjs(t).tz(this.timeZone).format("DD MMM")}catch(e){return t}},formatWeekday:function(t){try{return this.$dayjs(t).tz(this.timeZone).format("dddd")}catch(e){return new Intl.DateTimeFormat(this.userLocale,{weekday:"long"}).format(new Date(t))}},formatTime:function(time){try{if(time&&this.item.date){var t="".concat(this.item.date," ").concat(time);return this.$dayjs(t).tz(this.timeZone).format("LT")}return time}catch(t){return time}},formatFinishedAt:function(t){try{return t?this.$dayjs(t).tz(this.timeZone).format("DD MMM, LT"):"-"}catch(e){return t||"-"}},formatValue:function(t){return Number(t).toFixed(2)},formatCurrencyValue:function(t){var e,n=(null===(e=this.currentCurrency)||void 0===e?void 0:e.isoCode)||"EUR";return Object(r.formatCurrencyLocale)(t,n,this.userLocale,!0)},openPdf:function(){try{this.$store.dispatch("payments/openInvoicePdf",{transactionId:this.item.transactionId,invoiceNumber:this.item.invoiceNumber})}catch(t){this.$store.dispatch&&this.$store.dispatch("snackbar/error",{errorMessage:"Failed to open invoice PDF. Please try again."})}}}},v=(n(1492),n(22)),component=Object(v.a)(d,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"payment-item"},[o("div",{staticClass:"payment-item-date"},[o("div",[o("div",{staticClass:"weekday d-none d-sm-block"},[t._v("\n        "+t._s(t.formatWeekday(t.item.date))+"\n      ")]),t._v(" "),o("div",{staticClass:"date d-none d-sm-block"},[t._v("\n        "+t._s(t.formatDate(t.item.date))+"\n      ")]),t._v(" "),o("div",{staticClass:"time d-none d-sm-block"},[t._v(t._s(t.formatTime(t.item.time)))]),t._v(" "),o("div",{staticClass:"d-sm-none"},[t._v("\n        "+t._s(t.formatWeekday(t.item.date))+", "+t._s(t.formatDate(t.item.date))+" -\n        "+t._s(t.formatTime(t.item.time))+"\n      ")])]),t._v(" "),o("div",{staticClass:"duration d-none d-sm-block"},[o("div",{staticClass:"duration-icon"},[o("svg",{attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[o("use",{attrs:{"xlink:href":n(91)+"#clock-thin"}})]),t._v(" "),o("span",{staticClass:"ml-1"},[t._v(t._s(t.$t("lessonLength_mins",{lessonLength:t.lessonLength})))])])]),t._v(" "),o("div",{staticClass:"duration d-sm-none"},[t._v("\n       ("+t._s(t.$t("lessonLength_mins",{lessonLength:t.lessonLength}))+")\n    ")])]),t._v(" "),o("div",{staticClass:"payment-item-content"},[o("div",{staticClass:"payment-info"},[o("div",{staticClass:"student-name"},[t._v(t._s(t.item.student))]),t._v(" "),o("div",{staticClass:"details"},[o("div",{staticClass:"detail-group"},[o("p",{staticClass:"label"},[t._v("Lesson:")]),t._v(" "),o("p",{staticClass:"value gradient-text"},[t._v(t._s(t.item.lessonType))])]),t._v(" "),o("div",{staticClass:"detail-group"},[o("p",{staticClass:"label"},[t._v("Finished:")]),t._v(" "),o("p",{staticClass:"value gradient-text"},[t._v("\n            "+t._s(t.formatFinishedAt(t.item.finishedAt))+"\n          ")])]),t._v(" "),o("div",{staticClass:"detail-group"},[o("p",{staticClass:"label"},[t._v("Invoice no.")]),t._v(" "),o("p",{staticClass:"value gradient-text"},[t._v(t._s(t.item.invoiceNo))])]),t._v(" "),o("div",{staticClass:"detail-group"},[o("p",{staticClass:"label"},[t._v("Lesson no.")]),t._v(" "),o("p",{staticClass:"value gradient-text"},[t._v(t._s(t.item.lessonNo))])]),t._v(" "),o("div",{staticClass:"detail-group"},[o("p",{staticClass:"label"},[t._v("Value")]),t._v(" "),o("p",{staticClass:"value gradient-text"},[t._v("\n            "+t._s(t.formatCurrencyValue(t.item.value))+"\n          ")])]),t._v(" "),t.item.transactionId&&t.item.invoiceNumber?o("div",{staticClass:"detail-group"},[o("p",{staticClass:"label"},[t._v("PDF")]),t._v(" "),o("p",{staticClass:"value gradient-text"},[o("a",{staticClass:"pdf-download-link",attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.openPdf.apply(null,arguments)}}},[t._v("\n              Download\n            ")])])]):t._e()])])])])}),[],!1,null,"995c1e74",null);e.default=component.exports},1515:function(t,e,n){"use strict";n(1446)},1516:function(t,e,n){var o=n(18)(!1);o.push([t.i,".payment-item[data-v-ec37933a]:hover{box-shadow:0 4px 14px rgba(217,225,236,.47)}",""]),t.exports=o},1526:function(t,e,n){"use strict";n.r(e);n(174),n(31);var o={name:"PaymentLesson",components:{PaymentItem:n(1504).default},props:{item:{type:Object,required:!0}},computed:{paymentData:function(){return{day:this.item.day,date:this.item.date,time:this.item.time,student:this.item.student,lessonType:this.item.lessonType,status:this.item.status,invoiceNo:this.item.invoiceNo,lessonNo:this.item.lessonNo,value:this.item.value,finishedAt:this.item.finishedAt,transactionId:this.item.transactionId,invoiceNumber:this.item.invoiceNumber,lessonLength:this.item.lessonLength}},currentCurrencySymbol:function(){return this.$store.getters["currency/currentCurrencySymbol"]}},methods:{formatValue:function(t){return Number(t).toFixed(2)}}},r=(n(1515),n(22)),c=n(42),l=n.n(c),d=n(1563),component=Object(r.a)(o,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("payment-item",{attrs:{item:t.paymentData},scopedSlots:t._u([{key:"additionalActionsTop",fn:function(){return[n("div",{staticClass:"d-flex align-center"},[n("v-chip",{staticClass:"mr-2",attrs:{small:"",label:"",color:"completed"===t.item.status?"success":"warning"}},[t._v("\n        "+t._s(t.item.status)+"\n      ")]),t._v(" "),n("span",{staticClass:"caption grey--text"},[t._v("\n        "+t._s(t.$t("invoice_no"))+": "+t._s(t.item.invoiceNo)+"\n      ")])],1)]},proxy:!0},{key:"additionalActionsBottom",fn:function(){return[n("div",{staticClass:"d-flex align-center justify-space-between w-100"},[n("div",{staticClass:"caption grey--text"},[t._v("\n        "+t._s(t.$t("lesson_no"))+": "+t._s(t.item.lessonNo)+"\n      ")]),t._v(" "),n("div",{staticClass:"text-h6 primary--text"},[t._v("\n        "+t._s(t.currentCurrencySymbol)+t._s(t.formatValue(t.item.value))+"\n      ")])])]},proxy:!0}])})}),[],!1,null,"ec37933a",null);e.default=component.exports;l()(component,{VChip:d.a})},1563:function(t,e,n){"use strict";n(7),n(8),n(14),n(6),n(15);var o=n(13),r=n(2),c=(n(9),n(1486),n(12)),l=n(267),d=n(263),v=n(51),h=n(210),m=n(36),f=n(72),x=n(108),y=n(213),_=n(16);function w(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function C(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?w(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):w(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}e.a=Object(c.a)(v.a,y.a,x.a,m.a,Object(h.a)("chipGroup"),Object(f.b)("inputValue")).extend({name:"v-chip",props:{active:{type:Boolean,default:!0},activeClass:{type:String,default:function(){return this.chipGroup?this.chipGroup.activeClass:""}},close:Boolean,closeIcon:{type:String,default:"$delete"},closeLabel:{type:String,default:"$vuetify.close"},disabled:Boolean,draggable:Boolean,filter:Boolean,filterIcon:{type:String,default:"$complete"},label:Boolean,link:Boolean,outlined:Boolean,pill:Boolean,tag:{type:String,default:"span"},textColor:String,value:null},data:function(){return{proxyClass:"v-chip--active"}},computed:{classes:function(){return C(C(C(C({"v-chip":!0},x.a.options.computed.classes.call(this)),{},{"v-chip--clickable":this.isClickable,"v-chip--disabled":this.disabled,"v-chip--draggable":this.draggable,"v-chip--label":this.label,"v-chip--link":this.isLink,"v-chip--no-color":!this.color,"v-chip--outlined":this.outlined,"v-chip--pill":this.pill,"v-chip--removable":this.hasClose},this.themeClasses),this.sizeableClasses),this.groupClasses)},hasClose:function(){return Boolean(this.close)},isClickable:function(){return Boolean(x.a.options.computed.isClickable.call(this)||this.chipGroup)}},created:function(){var t=this;[["outline","outlined"],["selected","input-value"],["value","active"],["@input","@active.sync"]].forEach((function(e){var n=Object(o.a)(e,2),r=n[0],c=n[1];t.$attrs.hasOwnProperty(r)&&Object(_.a)(r,c,t)}))},methods:{click:function(t){this.$emit("click",t),this.chipGroup&&this.toggle()},genFilter:function(){var t=[];return this.isActive&&t.push(this.$createElement(d.a,{staticClass:"v-chip__filter",props:{left:!0}},this.filterIcon)),this.$createElement(l.b,t)},genClose:function(){var t=this;return this.$createElement(d.a,{staticClass:"v-chip__close",props:{right:!0,size:18},attrs:{"aria-label":this.$vuetify.lang.t(this.closeLabel)},on:{click:function(e){e.stopPropagation(),e.preventDefault(),t.$emit("click:close"),t.$emit("update:active",!1)}}},this.closeIcon)},genContent:function(){return this.$createElement("span",{staticClass:"v-chip__content"},[this.filter&&this.genFilter(),this.$slots.default,this.hasClose&&this.genClose()])}},render:function(t){var e=[this.genContent()],n=this.generateRouteLink(),o=n.tag,data=n.data;data.attrs=C(C({},data.attrs),{},{draggable:this.draggable?"true":void 0,tabindex:this.chipGroup&&!this.disabled?0:data.attrs.tabindex}),data.directives.push({name:"show",value:this.active}),data=this.setBackgroundColor(this.color,data);var r=this.textColor||this.outlined&&this.color;return t(o,this.setTextColor(r,data),e)}})}}]);