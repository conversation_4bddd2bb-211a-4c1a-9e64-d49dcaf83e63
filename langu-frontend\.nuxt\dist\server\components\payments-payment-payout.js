exports.ids = [70,71];
exports.modules = {

/***/ 1012:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1086);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("63c0973a", content, true, context)
};

/***/ }),

/***/ 1024:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1111);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("57f8db63", content, true, context)
};

/***/ }),

/***/ 1085:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PayoutItem_vue_vue_type_style_index_0_id_09b10226_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1012);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PayoutItem_vue_vue_type_style_index_0_id_09b10226_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PayoutItem_vue_vue_type_style_index_0_id_09b10226_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PayoutItem_vue_vue_type_style_index_0_id_09b10226_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PayoutItem_vue_vue_type_style_index_0_id_09b10226_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1086:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".payment-item[data-v-09b10226]{display:flex;background:#fff;border-radius:12px;margin-bottom:12px;overflow:hidden;box-shadow:0 4px 12px rgba(0,0,0,.1);height:94px}.payment-item-date[data-v-09b10226]{width:142px;display:flex;flex-direction:column;align-items:center;justify-content:center;background:linear-gradient(126.15deg,rgba(128,182,34,.74),rgba(60,135,248,.74) 102.93%);color:#fff;border-radius:16px;box-shadow:4px 0 8px rgba(0,0,0,.1);position:relative;z-index:1}.payment-item-date .date[data-v-09b10226]{font-size:20px;font-weight:600;line-height:1.2;margin-bottom:2px}.payment-item-date .time[data-v-09b10226]{font-size:13px;margin-top:2px;line-height:1}.payment-item-content[data-v-09b10226]{flex:1;padding:16px 24px}.payment-item-content .payment-info[data-v-09b10226]{display:flex;flex-direction:column;justify-content:space-between;height:100%}.payment-item-content .payment-info .details[data-v-09b10226]{display:flex;align-items:center;grid-gap:24px;gap:24px;font-size:14px}.payment-item[data-v-09b10226]:hover{box-shadow:0 4px 14px rgba(217,225,236,.47)}@media screen and (max-width:768px){.payment-item[data-v-09b10226]{flex-direction:column;margin-bottom:16px;box-shadow:none;background:transparent;height:auto}.payment-item[data-v-09b10226],.payment-item-date[data-v-09b10226]{box-shadow:4px 0 8px rgba(0,0,0,.1)}.payment-item-date[data-v-09b10226]{width:auto;min-height:auto;padding:8px 16px;flex-direction:row;justify-content:flex-start;border-radius:24px;margin-bottom:8px}.payment-item-date .date[data-v-09b10226]{margin-right:8px;margin-bottom:0}.payment-item-date .time[data-v-09b10226]{margin-left:0;opacity:1}.payment-item-content[data-v-09b10226]{background:#fff;border-radius:12px;padding:16px;box-shadow:0 4px 12px rgba(0,0,0,.1)}.payment-item-content .payment-info[data-v-09b10226]{height:auto}.payment-item-content .payment-info .details[data-v-09b10226]{flex-direction:column;grid-gap:8px;gap:8px;width:100%}.payment-item-content .payment-info .details[data-v-09b10226]  .caption{width:100%}.payment-item-content .payment-info .details[data-v-09b10226]  .caption p{font-size:16px;line-height:18px}.payment-item-content .payment-info .details[data-v-09b10226]  .caption .gradient-text{font-size:16px;font-weight:500}.payment-item-content .payment-info .details[data-v-09b10226]  .d-flex{width:100%}.payment-item-content .payment-info .details[data-v-09b10226]  .d-flex .caption{width:100%;display:flex;justify-content:space-between;padding:8px 0;border-bottom:1px solid rgba(0,0,0,.1)}.payment-item-content .payment-info .details[data-v-09b10226]  .d-flex .caption:last-child{border-bottom:none}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1103:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/payments/PayoutItem.vue?vue&type=template&id=09b10226&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"payment-item"},[_vm._ssrNode("<div class=\"payment-item-date\" data-v-09b10226><div class=\"date\" data-v-09b10226>"+_vm._ssrEscape(_vm._s(_vm.formatDate(_vm.item.date) || '-'))+"</div> <div class=\"time\" data-v-09b10226>"+_vm._ssrEscape(_vm._s(_vm.formatTime(_vm.item.time) || '-'))+"</div></div> "),_vm._ssrNode("<div class=\"payment-item-content\" data-v-09b10226>","</div>",[_vm._ssrNode("<div class=\"payment-info\" data-v-09b10226>","</div>",[_vm._t("additionalActionsTop"),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"details\" data-v-09b10226>","</div>",[_vm._t("additionalActionsBottom")],2)],2)])],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/payments/PayoutItem.vue?vue&type=template&id=09b10226&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/payments/PayoutItem.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var PayoutItemvue_type_script_lang_js_ = ({
  name: 'PayoutItem',
  props: {
    item: {
      type: Object,
      required: true,
      default: () => ({
        date: '',
        time: '',
        status: '',
        method: '',
        value: ''
      })
    }
  },
  computed: {
    userLocale() {
      var _this$$store$state$us;

      // Get user's UI language/locale, fallback to browser locale or 'en'
      return this.$store.getters['user/isUserLogged'] ? ((_this$$store$state$us = this.$store.state.user.item) === null || _this$$store$state$us === void 0 ? void 0 : _this$$store$state$us.uiLanguage) || this.$i18n.locale : this.$i18n.locale || 'en';
    },

    timeZone() {
      // Get user's timezone, fallback to browser timezone
      return this.$store.getters['user/timeZone'];
    }

  },
  methods: {
    formatDate(date) {
      if (!date) return null;

      try {
        // Use dayjs with timezone support like the lessons page
        return this.$dayjs(date).tz(this.timeZone).format('DD MMM');
      } catch (e) {
        return null;
      }
    },

    formatTime(time) {
      // Format time using user's locale and timezone
      if (!time) return null;

      try {
        // If time is already in a good format, we can try to parse it with the date
        // and format it according to user's locale
        if (time && this.item.date) {
          // Combine date and time for proper timezone conversion
          const dateTimeString = `${this.item.date} ${time}`;
          const dateTime = this.$dayjs(dateTimeString).tz(this.timeZone); // Format time using locale-aware format (LT = localized time)

          return dateTime.format('LT');
        } // Fallback: return the original time if we can't parse it


        return time;
      } catch (e) {
        // Fallback to original time if there's an error
        return time;
      }
    }

  }
});
// CONCATENATED MODULE: ./components/payments/PayoutItem.vue?vue&type=script&lang=js&
 /* harmony default export */ var payments_PayoutItemvue_type_script_lang_js_ = (PayoutItemvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/payments/PayoutItem.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1085)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  payments_PayoutItemvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "09b10226",
  "8b55bb80"
  
)

/* harmony default export */ var PayoutItem = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1110:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentPayout_vue_vue_type_style_index_0_id_927a72e2_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1024);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentPayout_vue_vue_type_style_index_0_id_927a72e2_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentPayout_vue_vue_type_style_index_0_id_927a72e2_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentPayout_vue_vue_type_style_index_0_id_927a72e2_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentPayout_vue_vue_type_style_index_0_id_927a72e2_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1111:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".gradient-text[data-v-927a72e2]{background:linear-gradient(126.15deg,#80b622,#3c87f8 102.93%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;font-weight:500}.payout-status span[data-v-927a72e2]{font-size:24px;font-weight:400;line-height:32px}.caption p[data-v-927a72e2]{font-size:16px;line-height:18px;margin:0!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1118:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/payments/PaymentPayout.vue?vue&type=template&id=927a72e2&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('payout-item',{attrs:{"item":_vm.payoutData},scopedSlots:_vm._u([{key:"additionalActionsTop",fn:function(){return [_c('div',{staticClass:"d-flex align-center payout-status"},[_c('span',{staticClass:"mr-2"},[_vm._v("\n        "+_vm._s(_vm.formattedAmount)+"\n      ")])])]},proxy:true},{key:"additionalActionsBottom",fn:function(){return [_c('div',{staticClass:"d-flex align-center justify-space-between w-100"},[_c('div',{staticClass:"caption grey--text"},[_c('p',[_vm._v(_vm._s(_vm.$t('payout_method')))]),_vm._v(" "),_c('p',{staticClass:"gradient-text"},[_vm._v(_vm._s(_vm.item.counterPartyType || '-'))])])])]},proxy:true}])})}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/payments/PaymentPayout.vue?vue&type=template&id=927a72e2&scoped=true&

// EXTERNAL MODULE: ./components/payments/PayoutItem.vue + 4 modules
var PayoutItem = __webpack_require__(1103);

// EXTERNAL MODULE: ./store/payments.js
var payments = __webpack_require__(52);

// EXTERNAL MODULE: ./helpers/index.js
var helpers = __webpack_require__(501);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/payments/PaymentPayout.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



/* harmony default export */ var PaymentPayoutvue_type_script_lang_js_ = ({
  name: 'PaymentPayout',
  components: {
    PayoutItem: PayoutItem["default"]
  },
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  computed: {
    payoutData() {
      return {
        day: this.item.day,
        date: this.item.date,
        time: this.item.time,
        status: this.item.status,
        counterPartyType: this.item.counterPartyType,
        amount: this.item.amount,
        currency: this.item.currency
      };
    },

    userCurrency() {
      return this.$store.getters['user/currency'];
    },

    userLocale() {
      var _this$$store$state$us;

      // Get user's UI language/locale, fallback to browser locale or 'en'
      return this.$store.getters['user/isUserLogged'] ? ((_this$$store$state$us = this.$store.state.user.item) === null || _this$$store$state$us === void 0 ? void 0 : _this$$store$state$us.uiLanguage) || this.$i18n.locale : this.$i18n.locale || 'en';
    },

    formattedAmount() {
      // Always use the user's currency for payouts
      if (this.userCurrency && this.userCurrency.isoCode) {
        return Object(helpers["formatCurrencyLocale"])(this.item.amount, this.userCurrency.isoCode, this.userLocale, true);
      } // Fallback to the original formatter if user currency is not available


      return Object(payments["currencyFormatter"])(this.item.amount, this.item.currency);
    }

  },
  methods: {
    getCurrencySymbol(isoCode) {
      const currencySymbols = {
        EUR: '€',
        USD: '$',
        GBP: '£',
        PLN: 'zł',
        CAD: 'C$',
        AUD: 'A$'
      };
      return currencySymbols[isoCode] || isoCode;
    }

  }
});
// CONCATENATED MODULE: ./components/payments/PaymentPayout.vue?vue&type=script&lang=js&
 /* harmony default export */ var payments_PaymentPayoutvue_type_script_lang_js_ = (PaymentPayoutvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/payments/PaymentPayout.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1110)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  payments_PaymentPayoutvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "927a72e2",
  "fd978996"
  
)

/* harmony default export */ var PaymentPayout = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=payments-payment-payout.js.map