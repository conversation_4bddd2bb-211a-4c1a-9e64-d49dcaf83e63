(window.webpackJsonp=window.webpackJsonp||[]).push([[34],{1622:function(t,o,e){var content=e(1708);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,e(19).default)("39bc3f36",content,!0,{sourceMap:!1})},1707:function(t,o,e){"use strict";e(1622)},1708:function(t,o,e){var n=e(18)(!1);n.push([t.i,".top-left[data-v-03a54868]{top:-32px;left:-40px}.bottom-right[data-v-03a54868]{bottom:-40px;right:-40px}.top-right[data-v-03a54868]{top:-32px;right:-40px}.bottom-left[data-v-03a54868]{bottom:-40px;left:-40px}",""]),t.exports=n},1915:function(t,o,e){"use strict";e.r(o);var n={props:{width:{type:String,default:"180"}}},r=(e(1707),e(22)),l=e(42),c=e.n(l),f=e(261),component=Object(r.a)(n,(function(){var t=this,o=t.$createElement,n=t._self._c||o;return n("div",[n("v-img",{attrs:{src:e(964),contain:"",width:t.width,options:{rootMargin:"20%"}}})],1)}),[],!1,null,"03a54868",null);o.default=component.exports;c()(component,{VImg:f.a})}}]);