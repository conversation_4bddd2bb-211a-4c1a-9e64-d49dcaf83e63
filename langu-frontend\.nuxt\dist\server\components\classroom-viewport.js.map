{"version": 3, "file": "components/classroom-viewport.js", "sources": ["webpack:///./components/classroom/Viewport.vue?f274", "webpack:///./components/classroom/Viewport.vue?64bf", "webpack:///./components/classroom/Viewport.vue?a23c", "webpack:///./components/classroom/Viewport.vue?67bf", "webpack:///./components/classroom/Viewport.vue", "webpack:///./components/classroom/Viewport.vue?814d", "webpack:///./components/classroom/Viewport.vue?bebc"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Viewport.vue?vue&type=style&index=0&id=4a5272da&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"ab02e688\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Viewport.vue?vue&type=style&index=0&id=4a5272da&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".viewport-component[data-v-4a5272da]{position:absolute;border-width:4px;border-style:solid}.user-name[data-v-4a5272da]{position:absolute;top:0;right:0;padding:4px 13px;line-height:1;background:#fff;font-size:12px;font-weight:500;box-shadow:0 0 5px rgba(0,0,0,.2);border:none;border-bottom-left-radius:6px;z-index:2}.user-name--tl[data-v-4a5272da]{top:0;right:auto;left:0;border-bottom-left-radius:0;border-bottom-right-radius:6px}.user-name--br[data-v-4a5272da]{right:0;border-top-left-radius:6px}.user-name--bl[data-v-4a5272da],.user-name--br[data-v-4a5272da]{top:auto;bottom:0;border-bottom-left-radius:0}.user-name--bl[data-v-4a5272da]{right:auto;left:0;border-top-right-radius:6px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.isOtherScreenAllowed)?_c('div',{staticClass:\"viewport-component\",style:(_vm.styles)},[_vm._ssrNode(\"<div\"+(_vm._ssrClass(null,[\n      'user-name',\n      {\n        'user-name--br': _vm.isTopOffset && !_vm.isRightOffset,\n        'user-name--tl': _vm.isRightOffset && !_vm.isTopOffset,\n        'user-name--bl': _vm.isTopOffset && _vm.isRightOffset,\n      } ]))+\" data-v-4a5272da>\"+_vm._ssrEscape(\"\\n    \"+_vm._s(_vm.$t('classroom_user_screen', {\n        username: _vm.username,\n      }))+\"\\n  \")+\"</div>\")]):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { isDevice } from '~/helpers/check_device'\n\nexport default {\n  name: 'Viewport',\n  props: {\n    zoomAsset: {\n      type: Object,\n      required: true,\n    },\n    zoomOtherAsset: {\n      type: Object,\n      required: true,\n    },\n    viewportWidth: {\n      type: Number,\n      required: true,\n    },\n    viewportHeight: {\n      type: Number,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      isDevice: isDevice(),\n    }\n  },\n  computed: {\n    role() {\n      return this.$store.getters['classroom/role']\n    },\n    color() {\n      return this.role === 'teacher'\n        ? 'rgba(60, 135, 248, 0.4)'\n        : 'rgba(127, 184, 2, 0.4)'\n    },\n    isOtherUserJoinedClassroom() {\n      return this.$store.getters['classroom/isOtherUserJoinedClassroom']\n    },\n    otherScreenTop() {\n      return (\n        (this.zoomOtherAsset.asset.y - this.zoomAsset.asset.y) *\n        this.zoomAsset.asset.zoomIndex\n      )\n    },\n    otherScreenLeft() {\n      return (\n        (this.zoomOtherAsset.asset.x - this.zoomAsset.asset.x) *\n        this.zoomAsset.asset.zoomIndex\n      )\n    },\n    otherScreenWidth() {\n      return (\n        (this.zoomOtherAsset.asset.screen.width /\n          this.zoomOtherAsset.asset.zoomIndex) *\n        this.zoomAsset.asset.zoomIndex\n      )\n    },\n    otherScreenHeight() {\n      return (\n        (this.zoomOtherAsset.asset.screen.height /\n          this.zoomOtherAsset.asset.zoomIndex) *\n        this.zoomAsset.asset.zoomIndex\n      )\n    },\n    isOtherScreenAllowed() {\n      return (\n        !this.isDevice &&\n        !!this.zoomOtherAsset?.asset?.screen &&\n        this.isOtherUserJoinedClassroom &&\n        this.zoomAsset.asset.screen.width >\n          this.zoomOtherAsset.asset.screen.width\n      )\n    },\n    styles() {\n      return {\n        top: `${this.otherScreenTop}px`,\n        left: `${this.otherScreenLeft}px`,\n        width: `${this.otherScreenWidth}px`,\n        height: `${this.otherScreenHeight}px`,\n        borderColor: this.color,\n      }\n    },\n    username() {\n      return this.zoomOtherAsset.asset.username\n    },\n    isTopOffset() {\n      return this.otherScreenTop < 0\n    },\n    isLeftOffset() {\n      return this.otherScreenLeft < 0\n    },\n    isBottomOffset() {\n      return (\n        this.viewportHeight + this.zoomAsset.asset.y <\n        this.otherScreenHeight + this.zoomOtherAsset.asset.y\n      )\n    },\n    isRightOffset() {\n      return (\n        this.viewportWidth + this.zoomAsset.asset.x <\n        this.otherScreenWidth + this.zoomOtherAsset.asset.x\n      )\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Viewport.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Viewport.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Viewport.vue?vue&type=template&id=4a5272da&scoped=true&\"\nimport script from \"./Viewport.vue?vue&type=script&lang=js&\"\nexport * from \"./Viewport.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./Viewport.vue?vue&type=style&index=0&id=4a5272da&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"4a5272da\",\n  \"332e3cb2\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AACA;AAiBA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAIA;AACA;AAAA;AACA;AAIA;AACA;AAAA;AACA;AAKA;AACA;AAAA;AACA;AAKA;AACA;AAAA;AAAA;AACA;AAAA;AAOA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAIA;AACA;AAAA;AACA;AAIA;AACA;AA7EA;AAzBA;;ACxBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}