(window.webpackJsonp=window.webpackJsonp||[]).push([[84,58],{1434:function(e,t,o){var content=o(1491);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,o(19).default)("a98bb618",content,!0,{sourceMap:!1})},1490:function(e,t,o){"use strict";o(1434)},1491:function(e,t,o){var r=o(18)(!1);r.push([e.i,'.text-editor{position:relative}.text-editor-buttons{position:absolute;right:18px;top:8px;z-index:2}.text-editor-buttons button{display:inline-flex;justify-content:center;align-items:center;width:24px;height:24px;margin-left:8px;border-radius:2px;border:1px solid transparent}.text-editor-buttons button.is-active{background-color:var(--v-greyBg-base);border-color:var(--v-greyLight-base)}.text-editor .ProseMirror{min-height:280px;margin-bottom:4px;padding:40px 12px 12px;border:1px solid #bebebe;font-size:13px;border-radius:16px;line-height:1.23}.text-editor .ProseMirror>*{position:relative}.text-editor .ProseMirror p{margin-bottom:0}.text-editor .ProseMirror ul{padding-left:28px}.text-editor .ProseMirror ul>li p{margin-bottom:0}.text-editor .ProseMirror strong{font-weight:700!important}.text-editor .ProseMirror.focus-visible,.text-editor .ProseMirror:focus,.text-editor .ProseMirror:focus-visible{outline:none!important}.text-editor .ProseMirror-focused:before{content:"";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:16px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}.text-editor .v-text-field__details{padding:0 14px}',""]),e.exports=r},1503:function(e,t,o){"use strict";o.r(t);o(31),o(96);var r=o(1586),n=o(1587),l=o(1577),d=o(1589),c={name:"Editor",components:{EditorContent:r.b},props:{value:{type:String,required:!0},counter:{type:Boolean,default:!1},autoLink:{type:Boolean,default:!1},limit:{type:Number,default:null}},data:function(){return{editor:null,text:"",isValid:!0,editorEl:null,keysPressed:{},isDirty:!1}},watch:{value:function(e){this.editor.getHTML()===e||this.editor.commands.setContent(e,!1)}},mounted:function(){var e=this;this.editor=new r.a({content:this.value,extensions:[n.a,l.a.configure({limit:this.limit}),d.a.configure({autolink:!0})]}),this.editor.on("create",(function(t){var o=t.editor;e.text=o.getText(),e.$nextTick((function(){e.editorEl=document.getElementsByClassName("ProseMirror")[0],e.editorEl&&(e.editorEl.addEventListener("keydown",e.keydownHandler),e.editorEl.addEventListener("keyup",e.keyupHandler))})),e.validation()})),this.editor.on("update",(function(t){var o=t.editor;e.isDirty=!0,e.text=o.getText(),e.validation(),e.$emit("update",e.text?o.getHTML():"")}))},beforeDestroy:function(){this.editorEl&&(this.editorEl.removeEventListener("keydown",this.keydownHandler),this.editorEl.removeEventListener("keyup",this.keyupHandler)),this.editor.destroy()},methods:{keydownHandler:function(e){this.keysPressed[e.keyCode]=!0,(e.ctrlKey||this.keysPressed[17]||this.keysPressed[91]||this.keysPressed[93]||this.keysPressed[224])&&this.keysPressed[13]?(e.preventDefault(),this.$emit("submit"),this.keysPressed={}):13!==e.keyCode||e.shiftKey||(e.preventDefault(),this.editor.commands.enter())},keyupHandler:function(e){delete this.keysPressed[e.keyCode]},validation:function(){var e=this.text.trim().length;this.isValid=!!e,e&&this.limit&&(this.isValid=e<=this.limit),this.$emit("validation",this.isValid)}}},v=(o(1490),o(22)),component=Object(v.a)(c,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"text-editor"},[e.editor?r("div",{staticClass:"text-editor-buttons"},[r("button",{class:{"is-active":e.editor.isActive("bold")},on:{click:function(t){t.stopPropagation(),t.preventDefault(),e.editor.chain().focus().toggleBold().run()}}},[r("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16"}},[r("use",{attrs:{"xlink:href":o(91)+"#editor-bold-icon"}})])]),e._v(" "),r("button",{class:{"is-active":e.editor.isActive("bulletList")},on:{click:function(t){t.stopPropagation(),t.preventDefault(),e.editor.chain().focus().toggleBulletList().run()}}},[r("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16"}},[r("use",{attrs:{"xlink:href":o(91)+"#editor-list-icon"}})])])]):e._e(),e._v(" "),r("editor-content",{attrs:{editor:e.editor}}),e._v(" "),e.counter?r("div",{staticClass:"v-text-field__details"},[e._m(0),e._v(" "),r("div",{class:["v-counter theme--light",{"error--text":!e.isValid&&e.isDirty}]},[e._v("\n      "+e._s(e.text.length)+" / "+e._s(e.limit)+"\n    ")])]):e._e()],1)}),[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"v-messages theme--light"},[t("div",{staticClass:"v-messages__wrapper"})])}],!1,null,null,null);t.default=component.exports},1578:function(e,t,o){var content=o(1629);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,o(19).default)("0f94d031",content,!0,{sourceMap:!1})},1628:function(e,t,o){"use strict";o(1578)},1629:function(e,t,o){var r=o(18)(!1);r.push([e.i,".v-application .v-dialog.message-dialog>.v-card{padding:32px 40px!important}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog>.v-card{padding:50px 18px 74px!important}.v-application .v-dialog.message-dialog>.v-card .dialog-content,.v-application .v-dialog.message-dialog>.v-card .message-dialog-body,.v-application .v-dialog.message-dialog>.v-card .v-form{height:100%}.v-application .v-dialog.message-dialog>.v-card .message-dialog-body{overflow-y:auto}}.v-application .v-dialog.message-dialog .message-dialog-header{display:inline-block;padding-right:60px;font-size:20px;font-weight:700;line-height:1.1}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-header{position:absolute;top:0;left:0;width:100%;height:50px;display:flex;align-items:center;padding-left:18px;font-size:18px}}.v-application .v-dialog.message-dialog .message-dialog-body .row .col:first-child{padding-right:20px}.v-application .v-dialog.message-dialog .message-dialog-body .row .col:last-child{padding-left:20px}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-text div:first-child{font-size:16px;font-weight:600}}.v-application .v-dialog.message-dialog .message-dialog-text ul{padding-left:20px}@media only screen and (min-width:992px){.v-application .v-dialog.message-dialog .message-dialog-footer{margin-top:28px}}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-footer{position:absolute;bottom:0;left:0;width:100%;height:74px;padding:0 18px}}.v-application .v-dialog.message-dialog .message-dialog-footer .prev-button{color:var(--v-orange-base);cursor:pointer}.v-application .v-dialog.message-dialog .text-editor .ProseMirror{min-height:248px}.v-application .v-dialog.message-dialog .text-editor-buttons{left:8px;right:auto}",""]),e.exports=r},1685:function(e,t,o){"use strict";o.r(t);o(31),o(20);var r=o(149),n=o(1503),l={name:"MessageDialog",components:{LDialog:r.default,Editor:n.default},props:{recipientId:{type:Number,required:!0},recipientName:{type:String,required:!0},isShownMessageDialog:{type:Boolean,required:!0}},data:function(){return{newMessage:"",isMessageValid:!1}},beforeDestroy:function(){this.newMessage="",this.isMessageValid=!1},methods:{submitHandler:function(){var e=this;this.isMessageValid&&this.$store.dispatch("message/sendNewMessage",{recipientId:this.recipientId,message:this.newMessage}).then((function(t){e.newMessage="",e.$router.push({path:"/user/messages/".concat(t.id,"/view")})})).finally((function(){return e.$emit("close-dialog")}))}}},d=(o(1628),o(22)),c=o(42),v=o.n(c),m=o(1327),f=o(1360),h=o(1363),x=o(1361),component=Object(d.a)(l,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("l-dialog",e._g({attrs:{dialog:e.isShownMessageDialog,"max-width":"725","custom-class":"message-dialog",persistent:"",fullscreen:e.$vuetify.breakpoint.xsOnly}},e.$listeners),[r("v-form",{on:{submit:function(t){return t.preventDefault(),e.submitHandler.apply(null,arguments)}}},[r("div",{staticClass:"message-dialog-header text--gradient"},[e._v("\n      "+e._s(e.$t(e.$vuetify.breakpoint.xsOnly?"ask_question":"questions_ask_teacher_directly"))+"\n    ")]),e._v(" "),r("div",{class:["message-dialog-body pt-0 pt-sm-3",{"l-scroll l-scroll--grey l-scroll--large":e.$vuetify.breakpoint.xsOnly}]},[r("v-row",{attrs:{"no-gutters":""}},[r("v-col",{staticClass:"col-12 col-sm-5 col-md-6"},[r("div",{staticClass:"message-dialog-text pr-0 pr-sm-2"},[r("div",{directives:[{name:"show",rawName:"v-show",value:e.$vuetify.breakpoint.xsOnly,expression:"$vuetify.breakpoint.xsOnly"}]},[r("span",{staticClass:"text-capitalize"},[e._v(e._s(e.$t("teacher")))]),e._v(": "+e._s(e.recipientName)),r("br"),e._v(" "),r("br")]),e._v(" "),r("div",{domProps:{innerHTML:e._s(e.$t("message_dialog_text",{recipientName:e.recipientName}))}})])]),e._v(" "),r("v-col",{staticClass:"col-12 col-sm-7 col-md-6 mt-3 mt-sm-0"},[r("editor",{attrs:{value:e.newMessage,limit:6e3},on:{update:function(t){e.newMessage=t},validation:function(t){e.isMessageValid=t}}})],1)],1)],1),e._v(" "),r("div",{staticClass:"message-dialog-footer d-flex justify-space-between align-center"},[r("div",{staticClass:"prev-button body-1",on:{click:function(t){return e.$emit("close-dialog")}}},[r("svg",{staticClass:"mr-1",attrs:{width:"17",height:"12",viewBox:"0 0 17 12"}},[r("use",{attrs:{"xlink:href":o(91)+"#arrow-prev"}})]),e._v(e._s(e.$t("go_back"))+"\n      ")]),e._v(" "),r("div",[r("v-btn",{attrs:{small:"",color:"primary",type:"submit"}},[e._v("\n          "+e._s(e.$t("send_message"))+"\n        ")])],1)])])],1)}),[],!1,null,null,null);t.default=component.exports;v()(component,{LDialog:o(149).default}),v()(component,{VBtn:m.a,VCol:f.a,VForm:h.a,VRow:x.a})}}]);