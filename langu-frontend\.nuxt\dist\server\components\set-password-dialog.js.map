{"version": 3, "file": "components/set-password-dialog.js", "sources": ["webpack:///./components/SetPasswordDialog.vue?c76e", "webpack:///./components/SetPasswordDialog.vue?0577", "webpack:///./components/SetPasswordDialog.vue?ac81", "webpack:///./components/SetPasswordDialog.vue?af8c", "webpack:///./components/SetPasswordDialog.vue", "webpack:///./components/SetPasswordDialog.vue?0bad", "webpack:///./components/SetPasswordDialog.vue?4728"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./SetPasswordDialog.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"36fed771\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./SetPasswordDialog.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".set-password .set-password-title{color:var(--v-dark-base)!important;font-size:20px;font-weight:600}@media only screen and (max-width:639px){.set-password .set-password-title{text-align:center;font-size:18px}}.set-password .set-password-text{color:var(--v-dark-base)!important;font-size:16px}@media only screen and (max-width:639px){.set-password .set-password-text{font-size:15px}}@media only screen and (max-width:479px){.set-password .set-password-text{font-size:14px}}.set-password .set-password-button{text-align:right}@media only screen and (max-width:639px){.set-password .set-password-button{text-align:center}}.set-password .set-password-button .v-btn{border-radius:16px!important}.set-password .set-password-button .v-btn .v-btn__content{font-weight:600!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',{attrs:{\"dialog\":_vm.showSetPasswordDialog,\"hide-close-button\":_vm.isValid,\"max-width\":\"388\",\"custom-class\":\"set-password\"},on:{\"close-dialog\":_vm.resetData}},[_c('div',[(_vm.isValid)?[_c('div',{staticClass:\"set-password-title\"},[_vm._v(\"\\n        \"+_vm._s(_vm.$t('thank_you_for_confirming_your_email_address'))+\"\\n      \")]),_vm._v(\" \"),_c('div',{staticClass:\"set-password-text mt-3 mt-md-5\"},[_vm._v(\"\\n        \"+_vm._s(_vm.$t('please_create_your_langu_password'))+\"\\n      \")]),_vm._v(\" \"),_c('form',{staticClass:\"mt-2\",on:{\"submit\":function($event){$event.preventDefault();return _vm.setPasswordSubmitHandler.apply(null, arguments)}}},[_c('div',{staticClass:\"mb-3\"},[_c('text-input',{attrs:{\"type\":\"password\",\"placeholder\":_vm.$t('password'),\"name\":\"password\",\"hide-details\":\"\",\"autocomplete\":\"new-password\"},scopedSlots:_vm._u([{key:\"append\",fn:function(){return [_c('div',{staticStyle:{\"margin-top\":\"5px\"}},[_c('v-img',{attrs:{\"src\":require('~/assets/images/lock-icon.svg'),\"width\":\"14\",\"height\":\"21\"}})],1)]},proxy:true}],null,false,3250624211),model:{value:(_vm.password),callback:function ($$v) {_vm.password=$$v},expression:\"password\"}})],1),_vm._v(\" \"),_c('div',[_c('text-input',{attrs:{\"type\":\"password\",\"placeholder\":_vm.$t('repeat_password'),\"name\":\"confirmPassword\",\"hide-details\":\"\",\"autocomplete\":\"new-password\"},scopedSlots:_vm._u([{key:\"append\",fn:function(){return [_c('div',{staticStyle:{\"margin-top\":\"5px\"}},[_c('v-img',{attrs:{\"src\":require('~/assets/images/lock-icon.svg'),\"width\":\"14\",\"height\":\"21\"}})],1)]},proxy:true}],null,false,3250624211),model:{value:(_vm.passwordRepeat),callback:function ($$v) {_vm.passwordRepeat=$$v},expression:\"passwordRepeat\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"form-message\"},[(_vm.passwordLengthError)?[_c('div',{staticClass:\"form-message-wrap form-message-wrap--error\"},[_c('div',{staticClass:\"form-message-icon\"},[_c('svg',{attrs:{\"width\":\"12\",\"height\":\"12\",\"viewBox\":\"0 0 12 12\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#attention\")}})])]),_vm._v(\"\\n              \"+_vm._s(_vm.passwordTextError)+\"\\n            \")])]:_vm._e()],2),_vm._v(\" \"),_c('div',{staticClass:\"set-password-button mt-1\"},[_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"orange\",\"x-large\":\"\",\"type\":\"submit\"}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('save'))+\"\\n          \")])],1)])]:[(_vm.token)?[_c('div',{staticClass:\"set-password-title\"},[_vm._v(\"\\n          \"+_vm._s(_vm.$t('sorry_this_link_has_expired'))+\"\\n        \")]),_vm._v(\" \"),_c('div',{staticClass:\"set-password-text mt-3 mt-md-5\"},[_vm._v(\"\\n          \"+_vm._s(_vm.$t('please_request_new_link_by_clicking_forgot_password_button'))+\"\\n        \")])]:[_c('div',{staticClass:\"set-password-title\"},[_vm._v(\"Something went wrong!\")])]]],2)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport LDialog from '~/components/LDialog'\nimport TextInput from '~/components/form/TextInput'\nimport { applyNavigationState } from '@/helpers/navigationState'\n\nexport default {\n  name: 'SetPasswordDialog',\n  components: { LDialog, TextInput },\n  props: {\n    showSetPasswordDialog: {\n      type: Boolean,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      password: '',\n      passwordRepeat: '',\n      passwordTextError: '',\n    }\n  },\n  computed: {\n    passwordLengthError() {\n      return !!this.passwordTextError.length\n    },\n    token() {\n      return this.$store.state.auth.passwordTokenItem?.token\n    },\n    is<PERSON>alid() {\n      return this.token\n        ? !this.$store.state.auth.passwordTokenItem?.isExpired ?? false\n        : false\n    },\n  },\n  watch: {\n    password() {\n      this.passwordTextError = ''\n    },\n    passwordRepeat() {\n      this.passwordTextError = ''\n    },\n  },\n  beforeDestroy() {\n    this.resetData()\n  },\n  methods: {\n    setPasswordSubmitHandler() {\n      this.passwordTextError = ''\n\n      const regex = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[^\\w\\s]).{8,}$/\n\n      if (this.password !== this.passwordRepeat) {\n        this.passwordTextError = this.$t('passwords_are_different')\n\n        return\n      }\n\n      if (!regex.test(this.password)) {\n        this.passwordTextError = this.$t('password_error')\n\n        return\n      }\n\n      this.$store\n        .dispatch('auth/setPassword', {\n          token: this.token,\n          password: this.password,\n          confirmPassword: this.passwordRepeat,\n        })\n        .then(() => {\n          this.$store.dispatch('user/getUserStatus').then(() => {\n            this.resetData()\n\n            // Try to apply the saved navigation state\n            const navigationStateApplied = applyNavigationState(this.$router, true)\n\n            // If navigation state was applied, we're done\n            if (navigationStateApplied) {\n              return\n            }\n\n            // Otherwise, use the redirectUrl or default path\n            const redirectUrl = this.$store.getters['user/redirectUrl']\n            if (redirectUrl) {\n              this.$router.push(redirectUrl)\n              this.$store.dispatch('user/clearRedirectUrl')\n            } else if (\n              this.$store.getters['user/isStudent'] &&\n              !this.$store.getters['user/registrationConfirmed']\n            ) {\n              this.$router.push({ path: '/teacher-listing/welcome' })\n            }\n          })\n        })\n        .catch((e) => {\n          if (e.response) {\n            if (e.response.status === 400) {\n              this.passwordTextError = this.$t('password_error')\n            }\n\n            if (e.response.status === 404) {\n              this.$store.commit('SET_IS_PASSWORD_LINK_EXPIRED', true)\n              this.$store.commit('auth/SET_PASSWORD_TOKEN_ITEM', null)\n              this.$store.commit('SET_IS_LOGIN_SIDEBAR', true)\n            }\n          }\n        })\n    },\n    resetData() {\n      this.password = ''\n      this.passwordRepeat = ''\n\n      this.$store.commit('auth/SET_PASSWORD_TOKEN_ITEM', null)\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./SetPasswordDialog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./SetPasswordDialog.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./SetPasswordDialog.vue?vue&type=template&id=2c70e6ce&\"\nimport script from \"./SetPasswordDialog.vue?vue&type=script&lang=js&\"\nexport * from \"./SetPasswordDialog.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./SetPasswordDialog.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"0707b346\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {LDialog: require('D:/languworks/langu-frontend/components/LDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VBtn,VImg})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAGA;AACA;AAZA;AAaA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;AACA;AAOA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAMA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAIA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AArEA;AAxCA;;AC3EA;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}