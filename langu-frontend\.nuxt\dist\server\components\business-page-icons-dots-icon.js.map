{"version": 3, "file": "components/business-page-icons-dots-icon.js", "sources": ["webpack:///./components/business-page/icons/DotsIcon.vue?26f7", "webpack:///./components/business-page/icons/DotsIcon.vue?8042", "webpack:///./components/business-page/icons/DotsIcon.vue?7eed", "webpack:///./components/business-page/icons/DotsIcon.vue?c9c7", "webpack:///./components/business-page/icons/DotsIcon.vue", "webpack:///./components/business-page/icons/DotsIcon.vue?7ada", "webpack:///./components/business-page/icons/DotsIcon.vue?6b60"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DotsIcon.vue?vue&type=style&index=0&id=03a54868&scoped=true&lang=css&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"39bc3f36\", content, true, context)\n};", "export * from \"-!../../../node_modules/vue-style-loader/index.js??ref--3-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DotsIcon.vue?vue&type=style&index=0&id=03a54868&scoped=true&lang=css&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".top-left[data-v-03a54868]{top:-32px;left:-40px}.bottom-right[data-v-03a54868]{bottom:-40px;right:-40px}.top-right[data-v-03a54868]{top:-32px;right:-40px}.bottom-left[data-v-03a54868]{bottom:-40px;left:-40px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('v-img',{attrs:{\"src\":require('~/assets/images/business-page/dots.svg'),\"contain\":\"\",\"width\":_vm.width,\"options\":{ rootMargin: '20%' }}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  props: {\n    width: {\n      type: String,\n      default: '180',\n    },\n  },\n}\n", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DotsIcon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DotsIcon.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./DotsIcon.vue?vue&type=template&id=03a54868&scoped=true&\"\nimport script from \"./DotsIcon.vue?vue&type=script&lang=js&\"\nexport * from \"./DotsIcon.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./DotsIcon.vue?vue&type=style&index=0&id=03a54868&scoped=true&lang=css&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"03a54868\",\n  \"49289c5d\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VImg})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAFA;AADA;AADA;;ACZA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}