(window.webpackJsonp=window.webpackJsonp||[]).push([[44],{1668:function(e,t,o){var content=o(1792);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,o(19).default)("d534cc50",content,!0,{sourceMap:!1})},1779:function(e,t){},1780:function(e,t){},1781:function(e,t){},1782:function(e,t){},1791:function(e,t,o){"use strict";o(1668)},1792:function(e,t,o){var r=o(18)(!1);r.push([e.i,".popup-load-files-item-img .preview-fluid[data-v-6a1d2341]{max-width:75%;max-height:75%}.popup-load-files-header.inactive[data-v-6a1d2341]{display:none}",""]),e.exports=r},1945:function(e,t,o){"use strict";o.r(t);var r=o(28),n=o(10),l=(o(62),o(31),o(39),o(64),o(37),o(20),o(44),o(71),o(9),o(6),o(40),o(174),o(24),o(38),o(23),o(55),o(8),o(66),o(82),o(859)),c=o(266);function d(e,t){var o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!o){if(Array.isArray(e)||(o=function(e,t){if(!e)return;if("string"==typeof e)return f(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);"Object"===o&&e.constructor&&(o=e.constructor.name);if("Map"===o||"Set"===o)return Array.from(e);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return f(e,t)}(e))||t&&e&&"number"==typeof e.length){o&&(e=o);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,l=!0,c=!1;return{s:function(){o=o.call(e)},n:function(){var e=o.next();return l=e.done,e},e:function(e){c=!0,n=e},f:function(){try{l||null==o.return||o.return()}finally{if(c)throw n}}}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,o=new Array(t);i<t;i++)o[i]=e[i];return o}var h={name:"Library",props:{viewportWidth:{type:Number,required:!0}},data:function(){return{pdfjsLib:{},rootUrl:"'http://localhost:3000'",files:[],totalPages:1,sortListDisplay:!1,queryStr:"",requestBody:{query:"",sort_direction:"DESC",page:1,sort_type:3},sortOptions:[{label:this.$t("last_added"),value:3},{label:this.$t("last_opened"),value:"last_opened"},{label:this.$t("file_name_a_z"),value:2},{label:this.$t("file_name_z_a"),value:1}],tickedFiles:[],arrayPages:[],uploadPercentage:0,isDragging:!1,uploadingFiles:[]}},computed:{zIndex:function(){return this.$store.state.classroom.maxIndex+1},getZoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},lessonId:function(){return this.$store.state.classroom.lessonId},role:function(){return this.$store.getters["classroom/role"]},acceptedFiles:function(){return this.$store.state.classroom.acceptedFiles},acceptedFilesStr:function(){return this.$store.getters["classroom/acceptedFilesStr"]}},watch:{queryStr:Object(l.debounce)((function(e){this.requestBody.query=e,this.getListOfFiles()}),1e3)},beforeMount:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,o(1691);case 2:return e.pdfjsLib=t.sent,t.next=5,o(1692);case 5:e.pdfjsLib.GlobalWorkerOptions.workerSrc=t.sent,e.getListOfFiles();case 7:case"end":return t.stop()}}),t)})))()},methods:{getPager:function(){var e,t,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,n=this.totalPages;if(o<1?o=1:o>n&&(o=n),n<=r)e=1,t=n;else{var l=Math.floor(r/2),c=Math.ceil(r/2)-1;o<=l?(e=1,t=r):o+c>=n?(e=n-r+1,t=n):(e=o-l,t=o+c)}return Array.from(Array(t+1-e).keys()).map((function(i){return e+i}))},uncheckFiles:function(){this.tickedFiles=[]},toggleFileMark:function(e){this.tickedFiles.find((function(t){return t.id===e.id}))?this.tickedFiles=this.tickedFiles.filter((function(t){return t.id!==e.id})):this.tickedFiles.push(e)},downloadFiles:function(){this.tickedFiles.forEach((function(e){var link=document.createElement("a");link.href=e.path,link.download=e.displayName,link.click()}))},deleteFiles:function(){var e=this,data=this.tickedFiles.map((function(e){return e.id}));this.$store.dispatch("classroom/deleteFiles",data).then((function(t){e.getListOfFiles(),e.tickedFiles=[]}))},toggleSortOptionsList:function(){return this.sortListDisplay=!this.sortListDisplay},changeSortType:function(option){return this.requestBody.sort_type=option.value,this.getListOfFiles()},nextPage:function(){return this.requestBody.page!==this.totalPages&&(this.requestBody.page++,this.getListOfFiles())},prevPage:function(){return 1!==this.requestBody.page&&(this.requestBody.page--,this.getListOfFiles())},goToPage:function(e){return this.requestBody.page=e,this.getListOfFiles()},isPdf:function(e){return"pdf"===Object(l.getFileExtension)(e.path)},isAudio:function(e){var t=Object(l.getFileExtension)(e.path);return"mp3"===t||"wav"===t},getListOfFiles:function(){var e=this,t=new FormData;for(var o in this.requestBody)t.append(o,this.requestBody[o]);this.$store.dispatch("classroom/getListOfFiles",{formData:t,page:this.requestBody.page}).then((function(t){e.totalPages=t.length?t[0].pages:1,e.files=t,e.arrayPages=e.getPager(e.requestBody.page,18,5),e.files.forEach((function(t){e.isPdf(t)&&e.makePdfThumb(t)}))}))},makePdfThumb:function(e){this.pdfjsLib.getDocument(this.rootUrl+e.path).promise.then((function(t){t.getPage(1).then((function(t){if(t){var o=t.getViewport({scale:1}),canvas=document.querySelector("#pdf-thumb--".concat(e.id));if(canvas&&o.width>0&&o.height>0){var r=o.width/o.height;canvas.height=canvas.width=78,o.width<o.height?canvas.width=78*r:canvas.height=78/r;var n=Math.min(canvas.width/o.width,canvas.height/o.height);t.render({canvasContext:canvas.getContext("2d"),viewport:t.getViewport({scale:n})})}}}))})).catch((function(e){console.log(e.message)}))},handleFileDrop:function(e){var t=e.dataTransfer.files;(null==t?void 0:t.length)>0&&this.uploadFiles(t)},addFiles:function(){var e,t=null===(e=this.$refs.file)||void 0===e?void 0:e.files;(null==t?void 0:t.length)>0&&this.uploadFiles(t)},cancelUpload:function(e){e.source.cancel("Operation canceled by user."),this.uploadingFiles=this.uploadingFiles.filter((function(t){return t.id!==e.id}))},uploadFiles:function(e){var t=this;return Object(n.a)(regeneratorRuntime.mark((function o(){var n,i;return regeneratorRuntime.wrap((function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,t.$store.dispatch("loadingAllow",!1);case 2:e=Object(r.a)(e),t.isDragging=!1,n=regeneratorRuntime.mark((function o(i){var n,d,f,h,source,v,m,y,data,_;return regeneratorRuntime.wrap((function(o){for(;;)switch(o.prev=o.next){case 0:if(n=Math.floor(999999*Math.random()),d=e[i],f=Object(l.getFileExtension)(d.name),h=t.$axios.CancelToken,source=h.source(),v={id:n,source:source,name:d.name,uploadPercentage:0},m=new FormData,!(d.size>c.a)){o.next=11;break}return o.next=10,t.$store.dispatch("snackbar/error",{errorMessage:t.$t("filename_size_should_be_less_than",{fileName:d.name,value:"".concat((c.a/8/1e3).toFixed(0)," Mb")}),timeout:5e3});case 10:return o.abrupt("return","continue");case 11:if(t.uploadingFiles.push(v),!t.acceptedFiles.officeTypes.includes(f)){o.next=21;break}return o.next=15,t.$store.dispatch("classroom/convertOfficeToPdf",d);case 15:y=o.sent,data=y.data,_=y.fileName,m.append("file",new Blob([data]),_),o.next=22;break;case 21:m.append("file",d);case 22:return o.next=24,t.$axios.post("".concat("/api/proxy","/lesson/classroom/upload/library/").concat(t.lessonId),m,{headers:{"Content-Type":"multipart/form-data"},cancelToken:source.token,onUploadProgress:function(e){v.uploadPercentage=parseInt(Math.round(e.loaded/e.total*100))},progress:!1}).then((function(e){var o;t.uploadingFiles=t.uploadingFiles.filter((function(e){return e.id!==n})),t.files=[].concat(Object(r.a)(e.data),Object(r.a)(t.files)),(o=t.tickedFiles).push.apply(o,Object(r.a)(e.data)),e.data.forEach((function(e){t.isPdf(e)&&t.makePdfThumb(e)}))})).catch((function(e){return console.log(e)}));case 24:return o.next=26,t.$store.dispatch("loadingAllow",!0);case 26:case"end":return o.stop()}}),o)})),i=0;case 6:if(!(i<=e.length-1)){o.next=14;break}return o.delegateYield(n(i),"t0",8);case 8:if("continue"!==o.t0){o.next=11;break}return o.abrupt("continue",11);case 11:i++,o.next=6;break;case 14:case"end":return o.stop()}}),o)})))()},closeLibrary:function(){this.$store.commit("classroom/toggleLibrary")},addToClassroom:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var o,r,n,c,f,h,v,m;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:o=0,r=0,n=d(e.tickedFiles),t.prev=3,n.s();case 5:if((c=n.n()).done){t.next=30;break}if(f=c.value,h=e.$store.state.classroom.maxIndex+1,v=Object(l.getFileExtension)(f.path),m=void 0,!e.acceptedFiles.pdfTypes.includes(v)){t.next=14;break}m="pdf",t.next=23;break;case 14:if(!e.acceptedFiles.imageTypes.includes(v)){t.next=18;break}m="image",t.next=23;break;case 18:if(!e.acceptedFiles.audioTypes.includes(v)){t.next=22;break}m="audio",t.next=23;break;case 22:return t.abrupt("return");case 23:return t.next=25,e.$store.dispatch("classroom/createAsset",{type:m,index:h,path:f.path,displayName:f.displayName,owner:e.role,top:e.$store.getters["classroom/zoomAsset"].asset.y+r+100,left:e.viewportWidth/2+e.getZoom.x+o-250});case 25:e.$store.commit("classroom/setMaxIndex",h),o+=50,r+=50;case 28:t.next=5;break;case 30:t.next=35;break;case 32:t.prev=32,t.t0=t.catch(3),n.e(t.t0);case 35:return t.prev=35,n.f(),t.finish(35);case 38:e.uncheckFiles(),e.closeLibrary();case 40:case"end":return t.stop()}}),t,null,[[3,32,35,38]])})))()}}},v=(o(1791),o(22)),m=o(42),y=o.n(m),_=o(1327),k=o(261),w=o(269),component=Object(v.a)(h,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{class:["popup-load-files elevation-3",e.role],style:{zIndex:e.zIndex}},[r("div",{ref:"loadFilesHeader",staticClass:"popup-load-files-header",class:{inactive:e.tickedFiles.length>0}},[r("div",{staticClass:"popup-load-files-title"},[e._v("\n      "+e._s(e.$t("library"))+"\n    ")]),e._v(" "),r("div",{staticClass:"popup-load-files-header-buttons"},[r("div",{staticClass:"popup-load-files-search-wrap"},[r("input",{directives:[{name:"model",rawName:"v-model",value:e.queryStr,expression:"queryStr"}],staticClass:"popup-load-files-search popup-load-files-input",attrs:{placeholder:"Search"},domProps:{value:e.queryStr},on:{input:function(t){t.target.composing||(e.queryStr=t.target.value)}}}),e._v(" "),e._m(0)]),e._v(" "),r("div",{staticClass:"popup-load-files-select-wrap"},[r("div",{staticClass:"popup-load-files-input popup-load-files-select cursor-pointer",on:{click:e.toggleSortOptionsList}},[e._v("\n          "+e._s(e.$t("sort_by"))+"\n        ")]),e._v(" "),r("div",{staticClass:"popup-load-files-select-options",class:{active:e.sortListDisplay}},e._l(e.sortOptions,(function(option,t){return r("div",{key:t,staticClass:"cursor-pointer",class:{"popup-load-files-select-option":e.requestBody.sort_type===option.value},on:{click:function(t){return e.changeSortType(option)}}},[e._v("\n            "+e._s(option.label)+"\n          ")])})),0)])])]),e._v(" "),r("div",{ref:"headerSelectedFiles",staticClass:"popup-load-files-header-selected-files",class:{active:e.tickedFiles.length>0}},[r("button",{staticClass:"popup-load-files-input cursor-pointer",attrs:{id:"add-to-classroom"},on:{click:e.addToClassroom}},[e._v("\n      "+e._s(e.$t("add_to_classroom"))+"\n    ")]),e._v(" "),r("div",{staticClass:"popup-load-files-buttons-wrap"},[r("button",{staticClass:"popup-load-files-input cursor-pointer",attrs:{id:"popup-load-files-download"},on:{click:e.downloadFiles}},[e._v("\n        "+e._s(e.$t("download"))+"\n      ")]),e._v(" "),r("button",{staticClass:"popup-load-files-input cursor-pointer",on:{click:e.deleteFiles}},[e._v("\n        "+e._s(e.$t("delete"))+"\n      ")]),e._v(" "),r("v-btn",{staticClass:"popup-load-files-header-cross cursor-pointer",attrs:{icon:"",color:"white"},on:{click:e.uncheckFiles}},[r("svg",{attrs:{width:"28",height:"28",viewBox:"0 0 21 20"}},[r("use",{attrs:{"xlink:href":o(91)+"#close"}})])])],1)]),e._v(" "),r("div",{staticClass:"popup-load-files-wrap",on:{dragover:function(t){t.stopPropagation(),t.preventDefault(),e.isDragging=!0}}},[r("div",{staticClass:"popup-load-files-body"},[r("div",{directives:[{name:"show",rawName:"v-show",value:e.isDragging,expression:"isDragging"}],staticClass:"popup-load-files-drop-wrap active",on:{dragleave:function(t){t.stopPropagation(),t.preventDefault(),e.isDragging=!1},drop:function(t){return t.stopPropagation(),t.preventDefault(),e.handleFileDrop.apply(null,arguments)}}},[r("div",{staticClass:"drop-area--wrapper"},[r("img",{staticClass:"drop-area--wrapper__dropbox-img",attrs:{src:o(908),alt:""}})])]),e._v(" "),r("div",[r("div",{staticClass:"popup-load-files-list",attrs:{id:"popup-load-files-list"}},[e.uploadingFiles?e._l(e.uploadingFiles,(function(t,n){return r("div",{key:n,staticClass:"popup-load-files-item popup-load-files-item--loading"},[r("div",{staticClass:"popup-load-files-item-helper"},[r("div",{staticClass:"popup-load-files-item-img"},[r("v-progress-circular",{attrs:{rotate:360,size:75,width:8,indeterminate:0===t.uploadPercentage,value:t.uploadPercentage,color:"success"}},[t.uploadPercentage>0?[e._v("\n                      "+e._s(t.uploadPercentage)+"%\n                    ")]:e._e()],2),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:t.uploadPercentage>0,expression:"loadingFile.uploadPercentage > 0"}],staticClass:"popup-load-files-item-cancel cursor-pointer",on:{click:function(o){return e.cancelUpload(t)}}},[r("div",{staticClass:"popup-load-files-tick-icon"},[r("svg",{attrs:{width:"15",height:"15",viewBox:"0 0 21 20"}},[r("use",{attrs:{"xlink:href":o(91)+"#close"}})])])])],1)]),e._v(" "),r("div",{staticClass:"popup-load-files-item-name"},[r("p",[e._v(e._s(t.name))])])])})):e._e(),e._v(" "),e._l(e.files,(function(t){return r("div",{key:t.id,staticClass:"popup-load-files-item"},[r("div",{staticClass:"popup-load-files-item-helper"},[r("div",{staticClass:"popup-load-files-item-img cursor-pointer",on:{click:function(o){return e.toggleFileMark(t)}}},[e.isPdf(t)?[r("canvas",{attrs:{id:"pdf-thumb--"+t.id,title:t.displayName}})]:e.isAudio(t)?[r("v-img",{attrs:{src:o(937),"max-width":"60",title:t.displayName}})]:[r("v-img",{attrs:{src:""+e.rootUrl+t.path,"max-width":"75%","max-height":"75%",title:t.displayName}})],e._v(" "),r("div",{staticClass:"popup-load-files-item-tick",class:{active:e.tickedFiles.find((function(e){return e.id===t.id}))}},[r("div",{staticClass:"popup-load-files-tick-icon"},[r("img",{attrs:{src:o(936),alt:""}})])])],2)]),e._v(" "),r("div",{staticClass:"popup-load-files-item-name"},[r("p",{attrs:{title:t.displayName}},[e._v(e._s(t.displayName))])])])}))],2)])]),e._v(" "),r("div",{staticClass:"popup-load-files-footer"},[e.arrayPages.length>1?r("div",{staticClass:"popup-load-files-footer-pagination"},[r("button",{staticClass:"popup-load-files-btn-nav popup-load-files-btn-nav-prev cursor-pointer",attrs:{disabled:1===e.requestBody.page},on:{click:e.prevPage}},[r("span",{staticClass:"d-block popup-load-files-nav-icon popup-load-files-nav-icon-prev"}),e._v(" "),r("span",[e._v(e._s(e.$t("previous")))])]),e._v(" "),r("div",{staticClass:"popup-load-files-nav-wrap",attrs:{id:"popup-load-files-navigation"}},e._l(e.arrayPages,(function(t,o){return r("span",{key:o,staticClass:"popup-load-files-nav-number",class:{active:e.requestBody.page===t},on:{click:function(o){return e.goToPage(t)}}},[e._v("\n            "+e._s(t)+"\n          ")])})),0),e._v(" "),r("button",{staticClass:"popup-load-files-btn-nav popup-load-files-btn-nav-next cursor-pointer",attrs:{disabled:e.requestBody.page===e.arrayPages.length},on:{click:e.nextPage}},[r("span",[e._v(e._s(e.$t("next")))]),e._v(" "),r("span",{staticClass:"d-block popup-load-files-nav-icon popup-load-files-nav-icon-next"})])]):e._e(),e._v(" "),r("div",{staticClass:"popup-load-files-footer-buttons"},[r("label",{staticClass:"popup-load-files-label-upload cursor-pointer font-weight-medium"},[e._v("\n          "+e._s(e.$t("upload_new_file"))+"\n          "),r("input",{ref:"file",staticClass:"popup-load-files-btn-upload",attrs:{id:"upload-library-files",type:"file",accept:e.acceptedFilesStr,multiple:""},on:{change:e.addFiles}})]),e._v(" "),r("v-btn",{staticClass:"font-weight-medium popup-load-files-close cursor-pointer",attrs:{color:"primary",small:""},on:{click:e.closeLibrary}},[e._v("\n          "+e._s(e.$t("cancel"))+"\n        ")])],1)])])])}),[function(){var e=this.$createElement,t=this._self._c||e;return t("button",{staticClass:"popup-load-files-search-icon",attrs:{id:"library-add-search"}},[t("span",{staticClass:"d-block library-add-search-img"})])}],!1,null,"6a1d2341",null);t.default=component.exports;y()(component,{VBtn:_.a,VImg:k.a,VProgressCircular:w.a})}}]);