(window.webpackJsonp=window.webpackJsonp||[]).push([[153],{1326:function(t,e,r){"use strict";r.d(e,"a",(function(){return d})),r.d(e,"b",(function(){return v}));var n=r(1329),o=r(1),l=Object(o.g)("v-card__actions"),c=Object(o.g)("v-card__subtitle"),d=Object(o.g)("v-card__text"),v=Object(o.g)("v-card__title");n.a},1380:function(t,e,r){var content=r(1381);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(19).default)("73707fd0",content,!0,{sourceMap:!1})},1381:function(t,e,r){var n=r(18)(!1);n.push([t.i,".v-item-group{flex:0 1 auto;position:relative;max-width:100%;transition:.3s cubic-bezier(.25,.8,.5,1)}",""]),t.exports=n},1411:function(t,e,r){"use strict";r.d(e,"a",(function(){return h}));r(7),r(8),r(14),r(15);var n=r(2),o=(r(31),r(9),r(24),r(38),r(126),r(6),r(55),r(71),r(371),r(1380),r(372)),l=r(36),c=r(12),d=r(16);function v(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,r)}return e}var h=Object(c.a)(o.a,l.a).extend({name:"base-item-group",props:{activeClass:{type:String,default:"v-item--active"},mandatory:Boolean,max:{type:[Number,String],default:null},multiple:Boolean,tag:{type:String,default:"div"}},data:function(){return{internalLazyValue:void 0!==this.value?this.value:this.multiple?[]:void 0,items:[]}},computed:{classes:function(){return function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?v(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):v(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({"v-item-group":!0},this.themeClasses)},selectedIndex:function(){return this.selectedItem&&this.items.indexOf(this.selectedItem)||-1},selectedItem:function(){if(!this.multiple)return this.selectedItems[0]},selectedItems:function(){var t=this;return this.items.filter((function(e,r){return t.toggleMethod(t.getValue(e,r))}))},selectedValues:function(){return null==this.internalValue?[]:Array.isArray(this.internalValue)?this.internalValue:[this.internalValue]},toggleMethod:function(){var t=this;if(!this.multiple)return function(e){return t.internalValue===e};var e=this.internalValue;return Array.isArray(e)?function(t){return e.includes(t)}:function(){return!1}}},watch:{internalValue:"updateItemsState",items:"updateItemsState"},created:function(){this.multiple&&!Array.isArray(this.internalValue)&&Object(d.c)("Model must be bound to an array if the multiple property is true.",this)},methods:{genData:function(){return{class:this.classes}},getValue:function(t,i){return null==t.value||""===t.value?i:t.value},onClick:function(t){this.updateInternalValue(this.getValue(t,this.items.indexOf(t)))},register:function(t){var e=this,r=this.items.push(t)-1;t.$on("change",(function(){return e.onClick(t)})),this.mandatory&&!this.selectedValues.length&&this.updateMandatory(),this.updateItem(t,r)},unregister:function(t){if(!this._isDestroyed){var e=this.items.indexOf(t),r=this.getValue(t,e);if(this.items.splice(e,1),!(this.selectedValues.indexOf(r)<0)){if(!this.mandatory)return this.updateInternalValue(r);this.multiple&&Array.isArray(this.internalValue)?this.internalValue=this.internalValue.filter((function(t){return t!==r})):this.internalValue=void 0,this.selectedItems.length||this.updateMandatory(!0)}}},updateItem:function(t,e){var r=this.getValue(t,e);t.isActive=this.toggleMethod(r)},updateItemsState:function(){var t=this;this.$nextTick((function(){if(t.mandatory&&!t.selectedItems.length)return t.updateMandatory();t.items.forEach(t.updateItem)}))},updateInternalValue:function(t){this.multiple?this.updateMultiple(t):this.updateSingle(t)},updateMandatory:function(t){if(this.items.length){var e=this.items.slice();t&&e.reverse();var r=e.find((function(t){return!t.disabled}));if(r){var n=this.items.indexOf(r);this.updateInternalValue(this.getValue(r,n))}}},updateMultiple:function(t){var e=(Array.isArray(this.internalValue)?this.internalValue:[]).slice(),r=e.findIndex((function(e){return e===t}));this.mandatory&&r>-1&&e.length-1<1||null!=this.max&&r<0&&e.length+1>this.max||(r>-1?e.splice(r,1):e.push(t),this.internalValue=e)},updateSingle:function(t){var e=t===this.internalValue;this.mandatory&&e||(this.internalValue=e?void 0:t)}},render:function(t){return t(this.tag,this.genData(),this.$slots.default)}});h.extend({name:"v-item-group",provide:function(){return{itemGroup:this}}})},148:function(t,e,r){"use strict";var n=r(1327);e.a=n.a},1720:function(t,e,r){"use strict";(function(t){r(55),r(40);e.a={name:"SentryTestPage",data:function(){return{testResults:[]}},head:function(){return{title:"Sentry Test - langu-frontend-7b",meta:[{hid:"description",name:"description",content:"Test page for Sentry integration in langu-frontend-7b"}]}},computed:{sentryStatus:function(){var e="development";return{isConfigured:!!this.$sentry,environment:e,dsn:!0,release:"langu-frontend-7b@"+(t.env.npm_package_version||"1.0.0"),isEnabled:!1,shouldWork:!!this.$sentry&&!0}}},methods:{addTestResult:function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];this.testResults.unshift({type:t,message:e,success:r,timestamp:(new Date).toLocaleTimeString()}),this.testResults.length>10&&(this.testResults=this.testResults.slice(0,10))},testMessage:function(){try{this.$sentry?(this.$sentry.captureMessage("Test info message from langu-frontend-7b","info"),this.addTestResult("Info Message","Test message sent successfully")):this.addTestResult("Info Message","Sentry not available",!1)}catch(t){this.addTestResult("Info Message","Error: ".concat(t.message),!1)}},testWarning:function(){try{this.$sentry?(this.$sentry.captureMessage("Test warning from langu-frontend-7b","warning"),this.addTestResult("Warning","Warning message sent successfully")):this.addTestResult("Warning","Sentry not available",!1)}catch(t){this.addTestResult("Warning","Error: ".concat(t.message),!1)}},testError:function(){try{this.$sentry?(this.$sentry.captureMessage("Test error from langu-frontend-7b","error"),this.addTestResult("Error Message","Error message sent successfully")):this.addTestResult("Error Message","Sentry not available",!1)}catch(t){this.addTestResult("Error Message","Error: ".concat(t.message),!1)}},testException:function(){try{if(this.$sentry){var t=new Error("Test exception from langu-frontend-7b");t.name="TestError",this.$sentry.captureException(t),this.addTestResult("Exception","Exception sent successfully")}else this.addTestResult("Exception","Sentry not available",!1)}catch(t){this.addTestResult("Exception","Error: ".concat(t.message),!1)}}}}}).call(this,r(107))},1721:function(t,e,r){var content=r(1722);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(19).default)("5db1c400",content,!0,{sourceMap:!1})},1722:function(t,e,r){var n=r(18)(!1);n.push([t.i,'.theme--light.v-alert .v-alert--prominent .v-alert__icon:after{background:rgba(0,0,0,.12)}.theme--dark.v-alert .v-alert--prominent .v-alert__icon:after{background:hsla(0,0%,100%,.12)}.v-sheet.v-alert{border-radius:16px}.v-sheet.v-alert:not(.v-sheet--outlined){box-shadow:0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)}.v-sheet.v-alert.v-sheet--shaped{border-radius:24px 16px}.v-alert{display:block;font-size:14px;margin-bottom:16px;padding:4px 10px;position:relative;transition:.3s cubic-bezier(.25,.8,.5,1)}.v-alert:not(.v-sheet--tile){border-radius:16px}.v-application--is-ltr .v-alert>.v-alert__content,.v-application--is-ltr .v-alert>.v-icon{margin-right:16px}.v-application--is-rtl .v-alert>.v-alert__content,.v-application--is-rtl .v-alert>.v-icon{margin-left:16px}.v-application--is-ltr .v-alert>.v-icon+.v-alert__content{margin-right:0}.v-application--is-rtl .v-alert>.v-icon+.v-alert__content{margin-left:0}.v-application--is-ltr .v-alert>.v-alert__content+.v-icon{margin-right:0}.v-application--is-rtl .v-alert>.v-alert__content+.v-icon{margin-left:0}.v-alert__border{border-style:solid;border-width:0;content:"";position:absolute}.v-alert__border:not(.v-alert__border--has-color){opacity:.26}.v-alert__border--left,.v-alert__border--right{bottom:0;top:0}.v-alert__border--bottom,.v-alert__border--top{left:0;right:0}.v-alert__border--bottom{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit;bottom:0}.v-application--is-ltr .v-alert__border--left{border-top-left-radius:inherit;border-bottom-left-radius:inherit;left:0}.v-application--is-ltr .v-alert__border--right,.v-application--is-rtl .v-alert__border--left{border-top-right-radius:inherit;border-bottom-right-radius:inherit;right:0}.v-application--is-rtl .v-alert__border--right{border-top-left-radius:inherit;border-bottom-left-radius:inherit;left:0}.v-alert__border--top{border-top-left-radius:inherit;border-top-right-radius:inherit;top:0}.v-alert__content{flex:1 1 auto}.v-application--is-ltr .v-alert__dismissible{margin:-16px -8px -16px 8px}.v-application--is-rtl .v-alert__dismissible{margin:-16px 8px -16px -8px}.v-alert__icon{align-self:flex-start;border-radius:50%;height:24px;min-width:24px;position:relative}.v-application--is-ltr .v-alert__icon{margin-right:16px}.v-application--is-rtl .v-alert__icon{margin-left:16px}.v-alert__icon.v-icon{font-size:24px}.v-alert__wrapper{align-items:center;border-radius:inherit;display:flex}.v-alert--dense{padding-top:4px 10px/2;padding-bottom:4px 10px/2}.v-alert--dense .v-alert__border{border-width:medium}.v-alert--outlined{background:transparent!important;border:thin solid!important}.v-alert--outlined .v-alert__icon{color:inherit!important}.v-alert--prominent .v-alert__icon{align-self:center;height:48px;min-width:48px}.v-alert--prominent .v-alert__icon:after{background:currentColor!important;border-radius:50%;bottom:0;content:"";left:0;opacity:.16;position:absolute;right:0;top:0}.v-alert--prominent .v-alert__icon.v-icon{font-size:32px}.v-alert--text{background:transparent!important}.v-alert--text:before{background-color:currentColor;border-radius:inherit;bottom:0;content:"";left:0;opacity:.12;position:absolute;pointer-events:none;right:0;top:0}',""]),t.exports=n},1835:function(t,e,r){var content=r(1987);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(19).default)("025ff8fa",content,!0,{sourceMap:!1})},1986:function(t,e,r){"use strict";r(1835)},1987:function(t,e,r){var n=r(18)(!1);n.push([t.i,".sentry-test-page[data-v-03418bb0]{min-height:100vh;background:#f5f5f5;padding:20px 0}.gap-2>*[data-v-03418bb0]{margin-right:8px;margin-bottom:8px}",""]),t.exports=n},2190:function(t,e,r){"use strict";r(7),r(8),r(9),r(14),r(6),r(15);var n=r(2),o=(r(24),r(1721),r(159)),l=r(148),c=r(263),d=r(72),v=r(36),h=r(3).default.extend({name:"transitionable",props:{mode:String,origin:String,transition:String}}),f=r(12),m=r(16);function _(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,r)}return e}function y(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?_(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):_(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}e.a=Object(f.a)(o.a,d.a,h).extend({name:"v-alert",props:{border:{type:String,validator:function(t){return["top","right","bottom","left"].includes(t)}},closeLabel:{type:String,default:"$vuetify.close"},coloredBorder:Boolean,dense:Boolean,dismissible:Boolean,closeIcon:{type:String,default:"$cancel"},icon:{default:"",type:[Boolean,String],validator:function(t){return"string"==typeof t||!1===t}},outlined:Boolean,prominent:Boolean,text:Boolean,type:{type:String,validator:function(t){return["info","error","success","warning"].includes(t)}},value:{type:Boolean,default:!0}},computed:{__cachedBorder:function(){if(!this.border)return null;var data={staticClass:"v-alert__border",class:Object(n.a)({},"v-alert__border--".concat(this.border),!0)};return this.coloredBorder&&((data=this.setBackgroundColor(this.computedColor,data)).class["v-alert__border--has-color"]=!0),this.$createElement("div",data)},__cachedDismissible:function(){var t=this;if(!this.dismissible)return null;var e=this.iconColor;return this.$createElement(l.a,{staticClass:"v-alert__dismissible",props:{color:e,icon:!0,small:!0},attrs:{"aria-label":this.$vuetify.lang.t(this.closeLabel)},on:{click:function(){return t.isActive=!1}}},[this.$createElement(c.a,{props:{color:e}},this.closeIcon)])},__cachedIcon:function(){return this.computedIcon?this.$createElement(c.a,{staticClass:"v-alert__icon",props:{color:this.iconColor}},this.computedIcon):null},classes:function(){var t=y(y({},o.a.options.computed.classes.call(this)),{},{"v-alert--border":Boolean(this.border),"v-alert--dense":this.dense,"v-alert--outlined":this.outlined,"v-alert--prominent":this.prominent,"v-alert--text":this.text});return this.border&&(t["v-alert--border-".concat(this.border)]=!0),t},computedColor:function(){return this.color||this.type},computedIcon:function(){return!1!==this.icon&&("string"==typeof this.icon&&this.icon?this.icon:!!["error","info","success","warning"].includes(this.type)&&"$".concat(this.type))},hasColoredIcon:function(){return this.hasText||Boolean(this.border)&&this.coloredBorder},hasText:function(){return this.text||this.outlined},iconColor:function(){return this.hasColoredIcon?this.computedColor:void 0},isDark:function(){return!(!this.type||this.coloredBorder||this.outlined)||v.a.options.computed.isDark.call(this)}},created:function(){this.$attrs.hasOwnProperty("outline")&&Object(m.a)("outline","outlined",this)},methods:{genWrapper:function(){var t=[this.$slots.prepend||this.__cachedIcon,this.genContent(),this.__cachedBorder,this.$slots.append,this.$scopedSlots.close?this.$scopedSlots.close({toggle:this.toggle}):this.__cachedDismissible];return this.$createElement("div",{staticClass:"v-alert__wrapper"},t)},genContent:function(){return this.$createElement("div",{staticClass:"v-alert__content"},this.$slots.default)},genAlert:function(){var data={staticClass:"v-alert",attrs:{role:"alert"},on:this.listeners$,class:this.classes,style:this.styles,directives:[{name:"show",value:this.isActive}]};this.coloredBorder||(data=(this.hasText?this.setTextColor:this.setBackgroundColor)(this.computedColor,data));return this.$createElement("div",data,[this.genWrapper()])},toggle:function(){this.isActive=!this.isActive}},render:function(t){var e=this.genAlert();return this.transition?t("transition",{props:{name:this.transition,origin:this.origin,mode:this.mode}},[e]):e}})},2236:function(t,e,r){"use strict";r.r(e);var n=r(1720).a,o=(r(1986),r(22)),l=r(42),c=r.n(l),d=r(2190),v=r(1327),h=r(1329),f=r(1326),m=r(1563),_=r(1360),y=r(1370),x=r(1717),S=r(1344),C=r(1330),O=r(1836),w=r(866),V=r(1361),component=Object(o.a)(n,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"sentry-test-page"},[r("v-container",[r("v-row",{attrs:{justify:"center"}},[r("v-col",{attrs:{cols:"12",md:"8"}},[r("v-card",{staticClass:"pa-6"},[r("v-card-title",{staticClass:"text-h4 mb-4"},[t._v("\n            Sentry Integration Test - langu-frontend-7b\n          ")]),t._v(" "),r("v-card-text",[r("div",{staticClass:"mb-4"},[r("h3",[t._v("Sentry Configuration Status")]),t._v(" "),r("v-chip",{staticClass:"mb-2",attrs:{color:t.sentryStatus.shouldWork?"success":"error","text-color":"white"}},[t._v("\n                "+t._s(t.sentryStatus.shouldWork?"Active":"Inactive")+"\n              ")]),t._v(" "),t.sentryStatus.isEnabled?t._e():r("v-chip",{staticClass:"mb-2 ml-2",attrs:{color:"warning","text-color":"white"}},[t._v("\n                Development Mode\n              ")]),t._v(" "),r("div",{staticClass:"mt-2"},[r("strong",[t._v("Project:")]),t._v(" langu-frontend-7b"),r("br"),t._v(" "),r("strong",[t._v("Environment:")]),t._v(" "+t._s(t.sentryStatus.environment)),r("br"),t._v(" "),r("strong",[t._v("DSN:")]),t._v(" "+t._s(t.sentryStatus.dsn?"Configured":"Not Set")),r("br"),t._v(" "),r("strong",[t._v("Release:")]),t._v(" "+t._s(t.sentryStatus.release)),r("br"),t._v(" "),r("strong",[t._v("Status:")]),t._v(" "+t._s(t.sentryStatus.isEnabled?"Enabled":"Disabled (Dev Mode)")+"\n              ")])],1),t._v(" "),r("v-divider",{staticClass:"my-4"}),t._v(" "),r("div",{staticClass:"mb-4"},[r("h3",[t._v("Test Sentry Integration")]),t._v(" "),r("p",{staticClass:"text-body-2 mb-3"},[t._v("\n                Use these buttons to test different types of Sentry events:\n              ")]),t._v(" "),r("div",{staticClass:"d-flex flex-wrap gap-2"},[r("v-btn",{attrs:{color:"info"},on:{click:t.testMessage}},[t._v("\n                  Test Message\n                ")]),t._v(" "),r("v-btn",{attrs:{color:"warning"},on:{click:t.testWarning}},[t._v("\n                  Test Warning\n                ")]),t._v(" "),r("v-btn",{attrs:{color:"error"},on:{click:t.testError}},[t._v("\n                  Test Error\n                ")]),t._v(" "),r("v-btn",{attrs:{color:"purple"},on:{click:t.testException}},[t._v("\n                  Test Exception\n                ")])],1)]),t._v(" "),r("v-divider",{staticClass:"my-4"}),t._v(" "),r("div",{staticClass:"mb-4"},[r("h3",[t._v("Recent Test Results")]),t._v(" "),t.testResults.length>0?r("v-list",t._l(t.testResults,(function(e,n){return r("v-list-item",{key:n},[r("v-list-item-content",[r("v-list-item-title",[t._v(t._s(e.type))]),t._v(" "),r("v-list-item-subtitle",[t._v("\n                      "+t._s(e.message)+" - "+t._s(e.timestamp)+"\n                    ")])],1),t._v(" "),r("v-list-item-action",[r("v-chip",{attrs:{color:e.success?"success":"error",small:"","text-color":"white"}},[t._v("\n                      "+t._s(e.success?"Sent":"Failed")+"\n                    ")])],1)],1)})),1):r("p",{staticClass:"text-body-2 text--secondary"},[t._v("\n                No tests run yet. Click the buttons above to test Sentry integration.\n              ")])],1),t._v(" "),t.sentryStatus.isEnabled?"staging"===t.sentryStatus.environment?r("v-alert",{staticClass:"mt-4",attrs:{type:"info"}},[r("strong",[t._v("Staging Mode:")]),t._v("\n              Sentry is active. All events are sent and logged to console for debugging.\n              Use "),r("code",[t._v("window.sentryTest")]),t._v(" helpers for testing.\n            ")]):"production"===t.sentryStatus.environment?r("v-alert",{staticClass:"mt-4",attrs:{type:"success"}},[r("strong",[t._v("Production Mode:")]),t._v("\n              Sentry is fully active. All errors are tracked with 10% performance sampling.\n            ")]):t._e():r("v-alert",{staticClass:"mt-4",attrs:{type:"warning"}},[r("strong",[t._v("Development Mode:")]),t._v("\n              Sentry is disabled in development environment.\n              Only staging and production environments will send events to Sentry.\n            ")])],1)],1)],1)],1)],1)],1)}),[],!1,null,"03418bb0",null);e.default=component.exports;c()(component,{VAlert:d.a,VBtn:v.a,VCard:h.a,VCardText:f.a,VCardTitle:f.b,VChip:m.a,VCol:_.a,VContainer:y.a,VDivider:x.a,VList:S.a,VListItem:C.a,VListItemAction:O.a,VListItemContent:w.a,VListItemSubtitle:w.b,VListItemTitle:w.c,VRow:V.a})}}]);