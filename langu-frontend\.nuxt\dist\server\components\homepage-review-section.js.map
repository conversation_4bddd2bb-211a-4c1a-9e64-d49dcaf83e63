{"version": 3, "file": "components/homepage-review-section.js", "sources": ["webpack:///./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css?06df", "webpack:///./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css", "webpack:///./components/homepage/ReviewSection.vue?8e29", "webpack:///./components/homepage/ReviewSection.vue?e9cc", "webpack:///./components/homepage/ReviewSection.vue?a185", "webpack:///./components/homepage/ReviewSection.vue?f029", "webpack:///./components/homepage/ReviewSection.vue", "webpack:///./components/homepage/ReviewSection.vue?a42e", "webpack:///./components/homepage/ReviewSection.vue?da73", "webpack:///./mixins/Avatars.vue", "webpack:///./mixins/Avatars.vue?9044", "webpack:///./mixins/Avatars.vue?7fa3"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../css-loader/dist/cjs.js??ref--3-oneOf-1-1!../../postcss-loader/src/index.js??ref--3-oneOf-1-2!./vue-slick-carousel.css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../vue-style-loader/lib/addStylesServer.js\").default(\"20c2c1c7\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".slick-track[data-v-e4caeaf8]{position:relative;top:0;left:0;display:block;transform:translateZ(0)}.slick-track.slick-center[data-v-e4caeaf8]{margin-left:auto;margin-right:auto}.slick-track[data-v-e4caeaf8]:after,.slick-track[data-v-e4caeaf8]:before{display:table;content:\\\"\\\"}.slick-track[data-v-e4caeaf8]:after{clear:both}.slick-loading .slick-track[data-v-e4caeaf8]{visibility:hidden}.slick-slide[data-v-e4caeaf8]{display:none;float:left;height:100%;min-height:1px}[dir=rtl] .slick-slide[data-v-e4caeaf8]{float:right}.slick-slide img[data-v-e4caeaf8]{display:block}.slick-slide.slick-loading img[data-v-e4caeaf8]{display:none}.slick-slide.dragging img[data-v-e4caeaf8]{pointer-events:none}.slick-initialized .slick-slide[data-v-e4caeaf8]{display:block}.slick-loading .slick-slide[data-v-e4caeaf8]{visibility:hidden}.slick-vertical .slick-slide[data-v-e4caeaf8]{display:block;height:auto;border:1px solid transparent}.slick-arrow.slick-hidden[data-v-21137603]{display:none}.slick-slider[data-v-3d1a4f76]{position:relative;display:block;box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;touch-action:pan-y;-webkit-tap-highlight-color:transparent}.slick-list[data-v-3d1a4f76]{position:relative;display:block;overflow:hidden;margin:0;padding:0;transform:translateZ(0)}.slick-list[data-v-3d1a4f76]:focus{outline:none}.slick-list.dragging[data-v-3d1a4f76]{cursor:pointer;cursor:hand}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReviewSection.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"71b1d682\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReviewSection.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_GET_URL_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/getUrl.js\");\nvar ___CSS_LOADER_URL_IMPORT_0___ = require(\"../../assets/images/quotes.svg\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".home-page-reviews{position:relative;margin-top:157px;padding-bottom:45px}@media only screen and (max-width:1439px){.home-page-reviews{padding-bottom:110px}}@media only screen and (max-width:991px){.home-page-reviews{margin-top:130px;padding-bottom:100px}.home-page-reviews .section-bg{top:50%;height:84%;transform:translateY(-50%)}.home-page-reviews .section-bg .v-image__image{background-position:85% bottom!important}}@media only screen and (max-width:479px){.home-page-reviews{margin-top:80px}}.home-page-reviews-carousel{max-width:898px;margin-top:30px}@media only screen and (max-width:1439px){.home-page-reviews-carousel{max-width:100%;padding-left:90px}}@media only screen and (max-width:991px){.home-page-reviews-carousel{max-width:620px;margin-left:auto;margin-right:auto;padding-left:0}}@media only screen and (max-width:639px){.home-page-reviews-carousel{margin-top:15px}}@media only screen and (max-width:479px){.home-page-reviews-carousel{width:calc(100% + 15px)}}.home-page-reviews-carousel-item{position:relative;max-width:680px;padding:16px 0 38px 23px}@media only screen and (max-width:991px){.home-page-reviews-carousel-item{padding:16px 15px 0}}@media only screen and (max-width:639px){.home-page-reviews-carousel-item{max-width:100%;padding-top:25px}.home-page-reviews-carousel-item:before{content:\\\"\\\";position:absolute;top:43px;right:48px;width:40px;height:36px;background-size:cover;background-repeat:no-repeat;background-image:url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \");opacity:.3}}@media only screen and (min-width:640px){.home-page-reviews-carousel-item:before{display:none}}.home-page-reviews-carousel-item-helper{position:relative;display:block;height:100%;padding:24px 36px 105px 105px;background-color:var(--v-darkLight-base);border-radius:24px;color:#fff!important;text-decoration:none}@media only screen and (max-width:991px){.home-page-reviews-carousel-item-helper{padding:95px 32px 105px;box-shadow:0 4px 24px rgba(0,0,0,.1)}.home-page-reviews-carousel-item-helper:before{content:\\\"\\\";position:absolute;top:18px;right:32px;width:40px;height:36px;background-size:cover;background-repeat:no-repeat;background-image:url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \");opacity:.3}}@media only screen and (max-width:479px){.home-page-reviews-carousel-item-helper{padding:90px 15px;box-shadow:none}.home-page-reviews-carousel-item-helper:before{right:18px}}.home-page-reviews-carousel-item-info{position:absolute;left:-15px;top:-15px}.home-page-reviews-carousel-item-info .flag{position:absolute;bottom:4px;right:-16px;border-radius:6px;overflow:hidden}.home-page-reviews-carousel-item-info .rating{position:absolute;left:30px;bottom:-18px;width:60px}@media only screen and (max-width:991px){.home-page-reviews-carousel-item-info .rating{left:102px;bottom:38px;width:88px}}.home-page-reviews-carousel-item-text{min-height:80px;font-size:16px;font-weight:300;line-height:1.6}.home-page-reviews-carousel-item-bottom{position:absolute;width:100%;left:0;bottom:40px;padding:0 36px 0 105px;font-size:20px}@media only screen and (max-width:991px){.home-page-reviews-carousel-item-bottom{padding:0 32px}}@media only screen and (max-width:479px){.home-page-reviews-carousel-item-bottom{padding:0 15px}}.home-page-reviews-carousel-item-bottom-helper{position:relative;padding:0 50px 0 60px}@media only screen and (max-width:479px){.home-page-reviews-carousel-item-bottom-helper{padding:0 0 0 60px;font-size:14px}}.home-page-reviews-carousel-item-bottom-helper:before{content:\\\"\\\";position:absolute;top:-16px;right:0;width:40px;height:36px;background-size:cover;background-repeat:no-repeat;background-image:url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \");opacity:.3}@media only screen and (max-width:991px){.home-page-reviews-carousel-item-bottom-helper:before{display:none}}.home-page-reviews-carousel-item-bottom-helper .v-image{position:absolute;left:0;top:50%;transform:translateY(-50%)}.home-page-reviews .slick-slide:nth-child(odd)>div{display:flex!important;justify-content:flex-end}.home-page-reviews .slick-arrow{position:absolute;top:27%;transform:translateY(-50%)}@media only screen and (max-width:991px){.home-page-reviews .slick-arrow{transform:translateX(-50%)}}.home-page-reviews .slick-next,.home-page-reviews .slick-prev{left:-696px}@media only screen and (max-width:1643px){.home-page-reviews .slick-next,.home-page-reviews .slick-prev{left:-192px}}@media only screen and (max-width:1439px){.home-page-reviews .slick-next,.home-page-reviews .slick-prev{left:-115px}}@media only screen and (max-width:991px){.home-page-reviews .slick-next,.home-page-reviews .slick-prev{top:auto;bottom:-100px}}@media only screen and (min-width:992px){.home-page-reviews .slick-prev{transform:translateY(calc(-50% - 45px))}}@media only screen and (max-width:991px){.home-page-reviews .slick-prev{left:calc(50% - 50px)}}.home-page-reviews .slick-prev:before{top:50%;left:calc(50% - 2px);transform:translate(-50%,-50%)}@media only screen and (min-width:992px){.home-page-reviews .slick-prev:before{top:calc(50% - 2px);left:50%;transform:translate(-50%,-50%) rotate(90deg)}}@media only screen and (min-width:992px){.home-page-reviews .slick-next{transform:translateY(calc(-50% + 45px))}}@media only screen and (max-width:991px){.home-page-reviews .slick-next{left:calc(50% + 50px)}}.home-page-reviews .slick-next:before{top:50%;left:calc(50% + 2px);transform:translate(-50%,-50%) rotate(180deg)}@media only screen and (min-width:992px){.home-page-reviews .slick-next:before{top:calc(50% + 2px);left:50%;transform:translate(-50%,-50%) rotate(270deg)}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"home-page-reviews\"},[_vm._ssrNode(\"<div class=\\\"section-bg\\\">\",\"</div>\",[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/reviews-bg.png'),\"position\":\"center bottom\",\"options\":{ rootMargin: '50%' }}})],1),_vm._ssrNode(\" \"),_c('v-container',[_c('v-row',[_c('v-col',{staticClass:\"col-12 py-0\"},[_c('div',{staticClass:\"section-head section-head--decorated\"},[_c('h3',{staticClass:\"section-head-title\",staticStyle:{\"color\":\"#262626\",\"-webkit-text-fill-color\":\"#262626\"}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('home_page.reviews_section_title'))+\"\\n          \")]),_vm._v(\" \"),_c('div',{staticClass:\"section-head-subtitle\",staticStyle:{\"max-width\":\"900px\"},domProps:{\"innerHTML\":_vm._s(_vm.$t('home_page.reviews_section_subtitle'))}})])])],1),_vm._v(\" \"),_c('v-row',[_c('v-col',{staticClass:\"col-12 py-0 d-md-flex justify-end\"},[_c('div',{staticClass:\"home-page-reviews-carousel\"},[_c('client-only',[_c('VueSlickCarousel',_vm._b({},'VueSlickCarousel',_vm.reviewsCarouselSettings,false),_vm._l((_vm.reviews),function(r,idx){return _c('div',{key:idx,staticClass:\"home-page-reviews-carousel-item\"},[_c('nuxt-link',{staticClass:\"home-page-reviews-carousel-item-helper\",attrs:{\"to\":r.teacherProfileUrl}},[_c('div',{staticClass:\"home-page-reviews-carousel-item-info\"},[_c('v-avatar',{attrs:{\"width\":\"87\",\"height\":\"87\"}},[_c('v-img',{attrs:{\"src\":_vm.getSrcAvatar(r.avatars, 'user_thumb_87x87'),\"srcset\":_vm.getSrcSetAvatar(\n                            r.avatars,\n                            'user_thumb_87x87',\n                            'user_thumb_174x174'\n                          ),\"options\":{ rootMargin: '50%' }}})],1),_vm._v(\" \"),_c('div',{staticClass:\"flag\"},[_c('v-img',{attrs:{\"src\":require((\"~/assets/images/flags/\" + (r.language.isoCode) + \".svg\")),\"width\":\"36\",\"contain\":\"\",\"options\":{ rootMargin: '50%' }}})],1),_vm._v(\" \"),_c('div',{staticClass:\"rating\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/stars.svg'),\"width\":\"100%\",\"contain\":\"\",\"options\":{ rootMargin: '50%' }}})],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"home-page-reviews-carousel-item-text\"},[_vm._v(\"\\n                    “\"+_vm._s(_vm._f(\"reviewText\")(r.description))+\"”\\n                  \")]),_vm._v(\" \"),_c('div',{staticClass:\"home-page-reviews-carousel-item-bottom\"},[_c('div',{staticClass:\"home-page-reviews-carousel-item-bottom-helper\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/user-icon-4.svg'),\"width\":\"45\",\"options\":{ rootMargin: '50%' }}}),_vm._v(\"\\n                      \"+_vm._s(r.studentFirstName)+\",\\n                      \"+_vm._s(_vm.$tc('review_lessons_count', r.countFinishedLesson, {\n                          language: r.language.name,\n                        }))+\"\\n                    \")],1)])])],1)}),0)],1)],1)])],1)],1)],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport VueSlickCarousel from 'vue-slick-carousel'\nimport 'vue-slick-carousel/dist/vue-slick-carousel.css'\n\nimport Avatars from '~/mixins/Avatars.vue'\n\nexport default {\n  name: 'ReviewSection',\n  components: {\n    VueSlickCarousel,\n  },\n  filters: {\n    reviewText(val) {\n      let str = val\n\n      if (str.length > 322) {\n        const arr = str.substring(0, 322).split(' ')\n\n        arr.pop()\n\n        str = arr.join(' ') + ' ...'\n      }\n\n      return str\n    },\n  },\n  mixins: [Avatars],\n  props: {\n    reviews: {\n      type: Array,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      reviewsCarouselSettings: {\n        dots: false,\n        focusOnSelect: true,\n        infinite: true,\n        slidesToShow: 3,\n        slidesToScroll: 1,\n        vertical: true,\n        verticalSwiping: true,\n        speed: 800,\n        responsive: [\n          {\n            breakpoint: 992,\n            settings: {\n              vertical: false,\n              verticalSwiping: false,\n              slidesToShow: 1,\n            },\n          },\n        ],\n      },\n    }\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReviewSection.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ReviewSection.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ReviewSection.vue?vue&type=template&id=7074f6fe&\"\nimport script from \"./ReviewSection.vue?vue&type=script&lang=js&\"\nexport * from \"./ReviewSection.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./ReviewSection.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"36678257\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VAvatar } from 'vuetify/lib/components/VAvatar';\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VContainer } from 'vuetify/lib/components/VGrid';\nimport { VImg } from 'vuetify/lib/components/VImg';\nimport { VRow } from 'vuetify/lib/components/VGrid';\ninstallComponents(component, {VAvatar,VCol,VContainer,VImg,VRow})\n", "\nexport default {\n  methods: {\n    getSrcAvatar(images, property, defaultImage = 'avatar.png') {\n      return images?.[property]\n        ? images[property]\n        : require(`~/assets/images/homepage/${defaultImage}`)\n    },\n    getSrcSetAvatar(images, property1, property2) {\n      return images?.[property1] && images?.[property2]\n        ? `\n            ${images[property1]} 1x,\n            ${images[property2]} 2x,\n          `\n        : ''\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Avatars.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Avatars.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./Avatars.vue?vue&type=script&lang=js&\"\nexport * from \"./Avatars.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"0af9ff4e\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAdA;AAeA;AACA;AACA;AACA;AACA;AAFA;AADA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAHA;AAFA;AAVA;AADA;AAsBA;AACA;AAnDA;;ACjHA;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AC/BA;AACA;AACA;AACA;AAGA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAdA;AADA;;ACDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}