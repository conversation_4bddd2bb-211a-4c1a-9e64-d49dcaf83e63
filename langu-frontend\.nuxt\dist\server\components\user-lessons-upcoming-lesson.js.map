{"version": 3, "file": "components/user-lessons-upcoming-lesson.js", "sources": ["webpack:///./components/user-lessons/UpcomingLesson.vue?317b", "webpack:///./components/user-lessons/UpcomingLesson.vue", "webpack:///./components/user-lessons/UpcomingLesson.vue?58ef", "webpack:///./components/user-lessons/UpcomingLesson.vue?1a32", "webpack:///./components/form/Editor.vue?b6fd", "webpack:///./components/UserStatus.vue?1220", "webpack:///./components/UserStatus.vue", "webpack:///./components/UserStatus.vue?34a5", "webpack:///./components/UserStatus.vue?d7af", "webpack:///./mixins/Avatars.vue", "webpack:///./mixins/Avatars.vue?9044", "webpack:///./mixins/Avatars.vue?7fa3", "webpack:///./components/UserStatus.vue?669a", "webpack:///./components/form/Editor.vue?03ad", "webpack:///./components/form/Editor.vue?6e6d", "webpack:///./components/MessageDialog.vue?1cf5", "webpack:///./components/form/Editor.vue?3107", "webpack:///./components/form/Editor.vue", "webpack:///./components/form/Editor.vue?13f3", "webpack:///./components/form/Editor.vue?9998", "webpack:///./components/user-lessons/LessonItem.vue?48ff", "webpack:///./components/user-lessons/LessonItem.vue", "webpack:///./components/user-lessons/LessonItem.vue?8e8d", "webpack:///./components/user-lessons/LessonItem.vue?eb62", "webpack:///./components/MessageDialog.vue?d1e2", "webpack:///./components/MessageDialog.vue", "webpack:///./components/MessageDialog.vue?1807", "webpack:///./components/MessageDialog.vue?3fce", "webpack:///./components/user-lessons/LessonItem.vue?3c78", "webpack:///./components/UserStatus.vue?80fc", "webpack:///./components/UserStatus.vue?41dd", "webpack:///./components/MessageDialog.vue?974f", "webpack:///./components/MessageDialog.vue?219a", "webpack:///./components/user-lessons/LessonItem.vue?0c19", "webpack:///./components/user-lessons/LessonItem.vue?c122"], "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('lesson-item',{attrs:{\"item\":_vm.item,\"user-statuses\":_vm.userStatuses},scopedSlots:_vm._u([{key:\"lessonAdditionalActionsTop\",fn:function(){return [_c('div',[_c('nuxt-link',{attrs:{\"to\":\"/user/settings#calendar\"}},[(_vm.item.isSyncedWithCalendar)?[_c('v-img',{attrs:{\"src\":require('~/assets/images/check-gradient.svg'),\"width\":\"12\",\"height\":\"13\"}}),_vm._v(\"\\n          \"+_vm._s(_vm.$t('synced_with_my_calendar'))+\"\\n        \")]:[_c('v-img',{attrs:{\"src\":require('~/assets/images/gear-icon-gradient.svg'),\"width\":\"13\",\"height\":\"14\"}}),_vm._v(\"\\n          \"+_vm._s(_vm.$t('configure_calendar_sync'))+\"\\n        \")]],2)],1),_vm._v(\" \"),_c('div',[_c('a',{attrs:{\"href\":(\"/lesson/\" + (_vm.item.lessonId) + \"/icalendar/get\")}},[_c('v-img',{attrs:{\"src\":require('~/assets/images/download-icon-gradient.svg'),\"width\":\"14\",\"height\":\"14\"}}),_vm._v(\"\\n        \"+_vm._s(_vm.$t('download_ics_calendar_file'))+\"\\n      \")],1)])]},proxy:true}])})}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport LessonItem from '~/components/user-lessons/LessonItem'\n\nexport default {\n  name: 'UpcomingLesson',\n  components: { LessonItem },\n  props: {\n    item: {\n      type: Object,\n      required: true,\n    },\n    userStatuses: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UpcomingLesson.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UpcomingLesson.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./UpcomingLesson.vue?vue&type=template&id=bc3603d2&\"\nimport script from \"./UpcomingLesson.vue?vue&type=script&lang=js&\"\nexport * from \"./UpcomingLesson.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"32560e4f\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VImg})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Editor.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"a98bb618\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[\n    'user-status',\n    (\"user-status--\" + _vm.status),\n    { 'user-status--large': _vm.large } ]},[])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'UserStatus',\n  props: {\n    userId: {\n      type: Number,\n      default: 0,\n    },\n    large: {\n      type: Boolean,\n      default: false,\n    },\n    userStatuses: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  computed: {\n    status() {\n      let status = 'offline'\n\n      if (\n        Object.prototype.hasOwnProperty.call(\n          this.userStatuses,\n          this.userId?.toString()\n        )\n      ) {\n        status = this.userStatuses[this.userId]\n      }\n\n      return status\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserStatus.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserStatus.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./UserStatus.vue?vue&type=template&id=652352c7&scoped=true&\"\nimport script from \"./UserStatus.vue?vue&type=script&lang=js&\"\nexport * from \"./UserStatus.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./UserStatus.vue?vue&type=style&index=0&id=652352c7&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"652352c7\",\n  \"4c070a35\"\n  \n)\n\nexport default component.exports", "\nexport default {\n  methods: {\n    getSrcAvatar(images, property, defaultImage = 'avatar.png') {\n      return images?.[property]\n        ? images[property]\n        : require(`~/assets/images/homepage/${defaultImage}`)\n    },\n    getSrcSetAvatar(images, property1, property2) {\n      return images?.[property1] && images?.[property2]\n        ? `\n            ${images[property1]} 1x,\n            ${images[property2]} 2x,\n          `\n        : ''\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Avatars.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Avatars.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./Avatars.vue?vue&type=script&lang=js&\"\nexport * from \"./Avatars.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"0af9ff4e\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserStatus.vue?vue&type=style&index=0&id=652352c7&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"006007e9\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Editor.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".text-editor{position:relative}.text-editor-buttons{position:absolute;right:18px;top:8px;z-index:2}.text-editor-buttons button{display:inline-flex;justify-content:center;align-items:center;width:24px;height:24px;margin-left:8px;border-radius:2px;border:1px solid transparent}.text-editor-buttons button.is-active{background-color:var(--v-greyBg-base);border-color:var(--v-greyLight-base)}.text-editor .ProseMirror{min-height:280px;margin-bottom:4px;padding:40px 12px 12px;border:1px solid #bebebe;font-size:13px;border-radius:16px;line-height:1.23}.text-editor .ProseMirror>*{position:relative}.text-editor .ProseMirror p{margin-bottom:0}.text-editor .ProseMirror ul{padding-left:28px}.text-editor .ProseMirror ul>li p{margin-bottom:0}.text-editor .ProseMirror strong{font-weight:700!important}.text-editor .ProseMirror.focus-visible,.text-editor .ProseMirror:focus,.text-editor .ProseMirror:focus-visible{outline:none!important}.text-editor .ProseMirror-focused:before{content:\\\"\\\";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:16px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}.text-editor .v-text-field__details{padding:0 14px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./MessageDialog.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"0f94d031\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"text-editor\"},[_vm._ssrNode(((_vm.editor)?(\"<div class=\\\"text-editor-buttons\\\"><button\"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bold') }))+\"><svg width=\\\"16\\\" height=\\\"16\\\" viewBox=\\\"0 0 16 16\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#editor-bold-icon\")))+\"></use></svg></button> <button\"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bulletList') }))+\"><svg width=\\\"16\\\" height=\\\"16\\\" viewBox=\\\"0 0 16 16\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#editor-list-icon\")))+\"></use></svg></button></div>\"):\"<!---->\")+\" \"),_c('editor-content',{attrs:{\"editor\":_vm.editor}}),_vm._ssrNode(\" \"+((_vm.counter)?(\"<div class=\\\"v-text-field__details\\\"><div class=\\\"v-messages theme--light\\\"><div class=\\\"v-messages__wrapper\\\"></div></div> <div\"+(_vm._ssrClass(null,[\n        'v-counter theme--light',\n        { 'error--text': !_vm.isValid && _vm.isDirty } ]))+\">\"+_vm._ssrEscape(\"\\n      \"+_vm._s(_vm.text.length)+\" / \"+_vm._s(_vm.limit)+\"\\n    \")+\"</div></div>\"):\"<!---->\"))],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { Editor, EditorContent } from '@tiptap/vue-2'\nimport StarterKit from '@tiptap/starter-kit'\nimport CharacterCount from '@tiptap/extension-character-count'\nimport Link from '@tiptap/extension-link'\n\nexport default {\n  name: 'Editor',\n  components: {\n    EditorContent,\n  },\n  props: {\n    value: {\n      type: String,\n      required: true,\n    },\n    counter: {\n      type: Boolean,\n      default: false,\n    },\n    autoLink: {\n      type: Boolean,\n      default: false,\n    },\n    limit: {\n      type: Number,\n      default: null,\n    },\n  },\n  data() {\n    return {\n      editor: null,\n      text: '',\n      isValid: true,\n      editorEl: null,\n      keysPressed: {},\n      isDirty: false,\n    }\n  },\n  watch: {\n    value(value) {\n      const isSame = this.editor.getHTML() === value\n\n      if (isSame) {\n        return\n      }\n\n      this.editor.commands.setContent(value, false)\n    },\n  },\n  mounted() {\n    this.editor = new Editor({\n      content: this.value,\n      extensions: [\n        StarterKit,\n        CharacterCount.configure({\n          limit: this.limit,\n        }),\n        Link.configure({\n          autolink: true,\n        }),\n      ],\n    })\n\n    this.editor.on('create', ({ editor }) => {\n      this.text = editor.getText()\n\n      this.$nextTick(() => {\n        this.editorEl = document.getElementsByClassName('ProseMirror')[0]\n\n        if (this.editorEl) {\n          this.editorEl.addEventListener('keydown', this.keydownHandler)\n          this.editorEl.addEventListener('keyup', this.keyupHandler)\n        }\n      })\n\n      this.validation()\n    })\n\n    this.editor.on('update', ({ editor }) => {\n      this.isDirty = true\n      this.text = editor.getText()\n\n      this.validation()\n      this.$emit('update', this.text ? editor.getHTML() : '')\n    })\n  },\n  beforeDestroy() {\n    if (this.editorEl) {\n      this.editorEl.removeEventListener('keydown', this.keydownHandler)\n      this.editorEl.removeEventListener('keyup', this.keyupHandler)\n    }\n\n    this.editor.destroy()\n  },\n  methods: {\n    keydownHandler(e) {\n      this.keysPressed[e.keyCode] = true\n\n      if (\n        (e.ctrlKey ||\n          this.keysPressed[17] ||\n          this.keysPressed[91] ||\n          this.keysPressed[93] ||\n          this.keysPressed[224]) &&\n        this.keysPressed[13]\n      ) {\n        e.preventDefault()\n\n        this.$emit('submit')\n\n        this.keysPressed = {}\n      } else if (e.keyCode === 13 && !e.shiftKey) {\n        e.preventDefault()\n        this.editor.commands.enter()\n      }\n    },\n    keyupHandler(e) {\n      delete this.keysPressed[e.keyCode]\n    },\n    validation() {\n      const strLength = this.text.trim().length\n\n      this.isValid = !!strLength\n\n      if (!!strLength && this.limit) {\n        this.isValid = strLength <= this.limit\n      }\n\n      this.$emit('validation', this.isValid)\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Editor.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Editor.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Editor.vue?vue&type=template&id=23b137ee&\"\nimport script from \"./Editor.vue?vue&type=script&lang=js&\"\nexport * from \"./Editor.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./Editor.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"0bb70d5d\"\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"lesson d-sm-flex\"},[_vm._ssrNode(\"<div class=\\\"lesson-date d-flex text-center\\\">\",\"</div>\",[_vm._ssrNode(\"<div>\",\"</div>\",[(_vm.$slots.date)?[_vm._t(\"date\")]:_vm._ssrNode(((_vm.$vuetify.breakpoint.smAndUp)?(\"<div class=\\\"weekday d-none d-sm-block\\\">\"+_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$dayjs(_vm.startDate).format('dddd'))+\"\\n          \")+\"</div> <div class=\\\"date d-none d-sm-block\\\">\"+_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$dayjs(_vm.startDate).format('DD MMM'))+\"\\n          \")+\"</div> <div class=\\\"time d-none d-sm-block\\\">\"+_vm._ssrEscape(_vm._s(_vm.startTime))+\"</div>\"):(\"<div class=\\\"d-sm-none\\\">\"+_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$dayjs(_vm.startDate).format('dddd'))+\",\\n            \"+_vm._s(_vm.$dayjs(_vm.startDate).format('DD MMM'))+\" - \"+_vm._s(_vm.startTime)+\"\\n          \")+\"</div>\")))],2),_vm._ssrNode(\" \"+((_vm.$vuetify.breakpoint.smAndUp)?(\"<div class=\\\"duration d-none d-sm-block\\\"><div class=\\\"duration-icon\\\"><svg width=\\\"18\\\" height=\\\"18\\\" viewBox=\\\"0 0 18 18\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#clock-thin\")))+\"></use></svg></div>\"+_vm._ssrEscape(\"\\n      \"+_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.item.lessonLength }))+\"\\n    \")+\"</div>\"):(\"<div class=\\\"duration d-sm-none\\\">\"+_vm._ssrEscape(\"\\n       (\"+_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.item.lessonLength }))+\")\\n    \")+\"</div>\")))],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"lesson-content\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"lesson-details\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"avatar mr-2\\\">\",\"</div>\",[_c('v-avatar',{attrs:{\"width\":\"110\",\"height\":\"110\"}},[_c('v-img',{attrs:{\"src\":_vm.getSrcAvatar(_vm.item.userAvatars, 'user_thumb_110x110'),\"srcset\":_vm.getSrcSetAvatar(\n                _vm.item.userAvatars,\n                'user_thumb_110x110',\n                'user_thumb_220x220'\n              ),\"options\":{ rootMargin: '50%' }}}),_vm._v(\" \"),(_vm.teacherLink)?_c('nuxt-link',{attrs:{\"to\":_vm.teacherLink}}):_vm._e()],1),_vm._ssrNode(\" \"),_c('user-status',{attrs:{\"user-id\":_vm.userId,\"user-statuses\":_vm.userStatuses,\"large\":\"\"}})],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"details\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"user-info\\\"><div class=\\\"user-info-name\\\">\"+_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.userName)+\"\\n            \")+((_vm.isTeacher)?(\"<div><svg width=\\\"16\\\" height=\\\"16\\\" viewBox=\\\"0 0 16 16\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#info\")))+\"></use></svg></div>\"):\"<!---->\")+\"</div> <div\"+(_vm._ssrClass(null,[\n              'user-info-status',\n              (\"user-info-status--\" + _vm.status),\n              { 'text--red-gradient': _vm.status === 'idle' } ]))+\">\"+((_vm.status === 'online')?(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('online_now'))+\"\\n            \")):(_vm.status === 'idle')?(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('online_but_idle'))+\"\\n            \")):(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('offline_now'))+\"\\n            \")))+\"</div></div> \"),_vm._ssrNode(\"<div class=\\\"lesson-info\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"avatar mr-2\\\">\",\"</div>\",[_c('v-avatar',{attrs:{\"width\":\"85\",\"height\":\"85\"}},[_c('v-img',{attrs:{\"src\":_vm.getSrcAvatar(_vm.item.userAvatars, 'user_thumb_110x110'),\"srcset\":_vm.getSrcSetAvatar(\n                    _vm.item.userAvatars,\n                    'user_thumb_110x110',\n                    'user_thumb_220x220'\n                  ),\"options\":{ rootMargin: '50%' }}}),_vm._v(\" \"),(_vm.teacherLink)?_c('nuxt-link',{attrs:{\"to\":_vm.teacherLink}}):_vm._e()],1),_vm._ssrNode(\" \"),_c('user-status',{attrs:{\"user-id\":_vm.userId,\"user-statuses\":_vm.userStatuses,\"large\":\"\"}})],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div>\",\"</div>\",[_vm._t(\"lessonInfo\"),_vm._ssrNode(\" \"+((!_vm.isUnscheduled)?(\"<div><span class=\\\"text-capitalize\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('lesson'))+\":\")+\"</span> <span class=\\\"text--gradient font-weight-bold text-no-wrap\\\">\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.item.lessonType)+\"\\n              \")+\"</span></div>\"):\"<!---->\")+\" <div><span class=\\\"text-capitalize\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('language'))+\":\")+\"</span> <span class=\\\"text--gradient font-weight-bold text-no-wrap\\\">\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.item.language.name)+\"\\n              \")+\"</span></div> \"+((_vm.item.courseName && _vm.item.courseName.length)?(\"<div><span class=\\\"text-capitalize\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('course'))+\":\")+\"</span> <span class=\\\"text--gradient font-weight-bold\\\">\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.item.courseName)+\"\\n              \")+\"</span></div>\"):\"<!---->\")+\" \"),_vm._ssrNode(\"<div class=\\\"lesson-actions-additional\\\">\",\"</div>\",[_vm._t(\"lessonAdditionalActionsTop\"),_vm._ssrNode(\" \"),(!_vm.isUnscheduleButtonHidden)?[_vm._ssrNode(\"<div>\",\"</div>\",[_vm._ssrNode(\"<span class=\\\"action\\\">\",\"</span>\",[_c('v-img',{staticStyle:{\"left\":\"3px\"},attrs:{\"src\":require('~/assets/images/close-gradient-2.svg'),\"width\":\"11\",\"height\":\"11\"}}),_vm._ssrNode(_vm._ssrEscape(\"\\n                    \"+_vm._s(_vm.$t('unschedule_lesson'))+\"\\n                  \"))],2)])]:_vm._e(),_vm._ssrNode(\" \"),_vm._t(\"lessonAdditionalActionsBottom\",null,{\"showDialog\":_vm.showDialog})],2)],2)],2)],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"lesson-actions\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"lesson-actions-buttons\\\">\",\"</div>\",[(!_vm.item.userIsDeleted)?_c('v-btn',{staticClass:\"btn-add gradient font-weight-medium ml-1 mb-1\",attrs:{\"width\":\"158\"},on:{\"click\":_vm.showMessageDialog}},[_c('div',{staticClass:\"mr-1\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/message-icon-gradient.svg'),\"width\":\"20\",\"height\":\"20\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"text--gradient\"},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('message'))+\"\\n          \")])]):_vm._e(),_vm._ssrNode(\" \"),_vm._t(\"lessonActions\"),_vm._ssrNode(\" \"),(!_vm.isUnscheduled)?[(_vm.isTeacher || (_vm.isStudent && !_vm.item.isCreated))?[_c('v-btn',{staticClass:\"go-to-class-btn font-weight-medium ml-1 mb-1\",attrs:{\"href\":(\"/lesson/\" + (_vm.item.lessonId) + \"/classroom\"),\"color\":\"primary\",\"width\":\"158\"}},[_c('svg',{staticClass:\"mr-1\",attrs:{\"width\":\"18\",\"height\":\"18\",\"viewBox\":\"0 0 18 18\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#save-icon\")}})]),_vm._v(\"\\n              \"+_vm._s(_vm.$t('go_to_class'))+\"\\n            \")])]:[_c('v-btn',{staticClass:\"go-to-class-btn go-to-class-btn--disabled primary--light font-weight-medium ml-1 mb-1\",attrs:{\"color\":\"primary\",\"width\":\"158\"},on:{\"click\":_vm.showInitializeDialog}},[_c('svg',{staticClass:\"icon--rotated mr-1\",attrs:{\"width\":\"18\",\"height\":\"18\",\"viewBox\":\"0 0 18 18\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#save-icon\")}})]),_vm._v(\"\\n              \"+_vm._s(_vm.$t('go_to_class'))+\"\\n              \"),_c('svg',{staticClass:\"icon--right\",attrs:{\"width\":\"18\",\"height\":\"18\",\"viewBox\":\"0 0 16 16\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#info\")}})])])]]:_vm._e()],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"lesson-actions-additional\\\">\",\"</div>\",[_vm._t(\"lessonAdditionalActionsTop\"),_vm._ssrNode(\" \"),(!_vm.isUnscheduleButtonHidden)?[_vm._ssrNode(\"<div>\",\"</div>\",[_vm._ssrNode(\"<span class=\\\"action\\\">\",\"</span>\",[_c('v-img',{staticStyle:{\"left\":\"3px\"},attrs:{\"src\":require('~/assets/images/close-gradient-2.svg'),\"width\":\"11\",\"height\":\"11\"}}),_vm._ssrNode(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('unschedule_lesson'))+\"\\n            \"))],2)])]:_vm._e(),_vm._ssrNode(\" \"),_vm._t(\"lessonAdditionalActionsBottom\",null,{\"showDialog\":_vm.showDialog})],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div\"+(_vm._ssrClass(null,[\n        'lesson-dialog d-flex flex-column justify-center',\n        {\n          'lesson-dialog--shown': _vm.isLessonDialogShown,\n        },\n        {\n          'lesson-dialog--student-info': _vm.dialogType === 'studentInfoDialog',\n        } ]))+\">\",\"</div>\",[_vm._t(\"dialog\",null,{\"closeDialog\":_vm.closeDialog}),_vm._ssrNode(\" \"),(_vm.dialogType === 'unscheduledDialog')?[_vm._ssrNode(\"<div class=\\\"lesson-dialog-title font-weight-medium text--red-gradient\\\">\"+_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('are_you_sure_you_want_to_unschedule_this_lesson_with', {\n              name: _vm.item.userFirstName,\n            }))+\"\\n        \")+\"</div> <div class=\\\"lesson-dialog-content l-scroll l-scroll--grey\\\">\"+((_vm.isStudent)?(((_vm.item.isFreeTrial)?(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t(\n                  'you_will_be_able_to_reschedule_this_lesson_from_your_teachers_profile_page'\n                ))+\"\\n            \")):(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t(\n                  'you_will_receive_credit_and_can_reschedule_lesson_for_anytime_your_teacher_is_available'\n                ))+\"\\n            \")))):(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('student_will_be_given_credit_for_lesson'))+\"\\n          \")))+\"</div> \"),_vm._ssrNode(\"<div class=\\\"lesson-dialog-buttons\\\">\",\"</div>\",[_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"greyDark\",\"outlined\":\"\"},on:{\"click\":_vm.closeDialog}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('do_not_cancel_lesson'))+\"\\n          \")]),_vm._ssrNode(\" \"),_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"error\"},on:{\"click\":_vm.cancelClickHandler}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('cancel_lesson'))+\"\\n          \")])],2)]:_vm._e(),_vm._ssrNode(\" \"),(_vm.dialogType === 'initializeDialog')?[_vm._ssrNode(\"<div class=\\\"lesson-dialog-title font-weight-medium text--gradient\\\"><div class=\\\"lesson-dialog-title-icon\\\"><svg width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 12 12\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#attention\")))+\"></use></svg></div>\"+_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('your_teacher_will_enter_classroom_first'))+\"\\n        \")+\"</div> <div class=\\\"lesson-dialog-content l-scroll l-scroll--grey\\\">\"+(_vm._s(\n            _vm.$t(\n              'after_your_teacher_enters_go_to_class_button_will_become_clickable_so_you_can_enter_as_well'\n            )\n          ))+\"</div> \"),_vm._ssrNode(\"<div class=\\\"lesson-dialog-buttons\\\">\",\"</div>\",[_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"primary\",\"max-width\":\"158\"},on:{\"click\":_vm.closeDialog}},[_vm._v(\"\\n            OK!\\n          \")])],1)]:_vm._e(),_vm._ssrNode(\" \"),(_vm.dialogType === 'studentInfoDialog')?[_vm._ssrNode(\"<div class=\\\"lesson-dialog-title font-weight-medium text--gradient\\\"><div class=\\\"lesson-dialog-title-icon\\\"><svg width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 16 16\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#info\")))+\"></use></svg></div>\"+_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('student_info'))+\"\\n        \")+\"</div> <div class=\\\"lesson-dialog-content l-scroll l-scroll--grey\\\"><div><div><ul><li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('name'))+\":\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.studentInfo.name)+\"\\n                \")+\"</li> <li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('lifetime_free_trials_scheduled'))+\":\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.studentInfo.freeTrialScheduled)+\"\\n                \")+\"</li> <li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('lifetime_lessons_purchased'))+\":\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.studentInfo.lessonsPurchased)+\"\\n                \")+\"</li> <li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('lifetime_teachers_booked_with'))+\":\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.studentInfo.teachersBookedWith)+\"\\n                \")+\"</li></ul></div> <div class=\\\"pl-1\\\"><ul><li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('current_time'))+\":\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.$dayjs().tz(_vm.studentInfo.timezone).format('LT'))+\"\\n                  (\"+_vm._s(_vm.$dayjs().tz(_vm.studentInfo.timezone).format('z'))+\",\\n                  \"+_vm._s(_vm.studentInfo.timezone)+\")\\n                \")+\"</li> <li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('total_spent_with_you'))+\" (\"+_vm._s(_vm.currencyIsoCode)+\"):\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.currencySymbol)+_vm._s(_vm.getPrice(_vm.studentInfo.totalSpendWithTeacher))+\"\\n                \")+\"</li> <li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('date_registered_on_langu'))+\":\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.$dayjs(_vm.studentInfo.dateRegistered)\n                      .tz(_vm.timeZone)\n                      .format('ll, LT'))+\"\\n                \")+\"</li> <li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('last_online'))+\":\")+\"</span> \"+((_vm.status === 'online')?(_vm._ssrEscape(\"\\n                    \"+_vm._s(_vm.$t('online_now'))+\"\\n                  \")):(_vm.status === 'idle')?(_vm._ssrEscape(\"\\n                    \"+_vm._s(_vm.$t('online_but_idle'))+\"\\n                  \")):(_vm._ssrEscape(\"\\n                    \"+_vm._s(_vm.$dayjs(_vm.studentInfo.lastLoginDate)\n                        .tz(_vm.timeZone)\n                        .format('ll, LT'))+\"\\n                  \")))+\"</li></ul></div></div></div> \"),_vm._ssrNode(\"<div class=\\\"lesson-dialog-buttons\\\">\",\"</div>\",[_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"primary\",\"max-width\":\"158\"},on:{\"click\":_vm.closeDialog}},[_vm._v(\"\\n            OK\\n          \")])],1)]:_vm._e()],2)],2),_vm._ssrNode(\" \"),(_vm.isShownMessageDialog)?_c('message-dialog',{attrs:{\"recipient-id\":_vm.userId,\"recipient-name\":((_vm.item.userFirstName) + \" \" + (_vm.item.userLastName)),\"is-shown-message-dialog\":_vm.isShownMessageDialog},on:{\"close-dialog\":function($event){_vm.isShownMessageDialog = false}}}):_vm._e(),_vm._ssrNode(\" \"),_vm._t(\"default\")],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getPrice } from '~/helpers'\n\nimport Avatars from '~/mixins/Avatars'\nimport UserStatus from '~/components/UserStatus'\nimport MessageDialog from '~/components/MessageDialog'\n\nexport default {\n  name: 'LessonItem',\n  components: { UserStatus, MessageDialog },\n  mixins: [Avatars],\n  props: {\n    item: {\n      type: Object,\n      required: true,\n    },\n    userStatuses: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  data() {\n    return {\n      getPrice,\n      isLessonDialogShown: false,\n      dialogType: null,\n      isShownMessageDialog: false,\n      studentInfo: null,\n    }\n  },\n  computed: {\n    isTeacher() {\n      return this.$store.getters['user/isTeacher']\n    },\n    isStudent() {\n      return this.$store.getters['user/isStudent']\n    },\n    userId() {\n      return this.item[this.isTeacher ? 'studentId' : 'teacherId']\n    },\n    userIsDeleted() {\n      return this.item.userIsDeleted\n    },\n    userName() {\n      return this.userIsDeleted\n        ? this.$t('deleted_user')\n        : `${this.item.userFirstName} ${this.item.userLastName}`\n    },\n    currencyIsoCode() {\n      return this.$store.state.currency.item.isoCode\n    },\n    currencySymbol() {\n      return this.$store.getters['currency/currentCurrencySymbol']\n    },\n    status() {\n      let status = 'offline'\n\n      if (\n        Object.prototype.hasOwnProperty.call(\n          this.userStatuses,\n          this.userId?.toString()\n        )\n      ) {\n        status = this.userStatuses[this.userId]\n      }\n\n      return status\n    },\n    isPast() {\n      return this.item.type === 'past'\n    },\n    isUnscheduled() {\n      return this.item.type === 'unscheduled'\n    },\n    timeZone() {\n      return this.$store.getters['user/timeZone']\n    },\n    startDate() {\n      return this.$dayjs(this.item.startDate).tz(this.timeZone)\n    },\n    startTime() {\n      return this.startDate.format('LT')\n    },\n    isUnscheduleButtonHidden() {\n      return (\n        this.isUnscheduled ||\n        this.item.isFinished ||\n        (this.isStudent &&\n          (this.isPast ||\n            this.$dayjs().add(1, 'day').isAfter(this.startDate, 'minute')))\n      )\n    },\n    teacherLink() {\n      return this.isStudent &&\n        !this.item.userIsDeleted &&\n        this.item.teacherUsername\n        ? `/teacher/${this.item.teacherUsername}`\n        : null\n    },\n  },\n  methods: {\n    showMessageDialog() {\n      this.$store\n        .dispatch('message/checkConversation', this.userId)\n        .then((res) => {\n          if (res.threadId) {\n            this.$store.dispatch('loadingStart')\n            this.$router.push({ path: `/user/messages/${res.threadId}/view` })\n          } else {\n            this.isShownMessageDialog = true\n          }\n        })\n    },\n    cancelClickHandler() {\n      this.$store.dispatch('loadingStart')\n      this.$store\n        .dispatch('lesson/cancelLesson', this.item.lessonId)\n        .then(() => {\n          location.reload()\n        })\n        .catch((e) => {\n          this.$store.dispatch('loadingStop')\n          this.$store.dispatch('snackbar/error')\n\n          console.info(e)\n        })\n    },\n    studentInfoClickHandler() {\n      this.$store\n        .dispatch('lesson/getStudentInfo', this.item.studentId)\n        .then((res) => {\n          this.studentInfo = res\n          this.dialogType = 'studentInfoDialog'\n\n          this.showDialog()\n        })\n    },\n    showInitializeDialog() {\n      this.dialogType = 'initializeDialog'\n\n      this.showDialog()\n    },\n    showUnscheduledDialog() {\n      this.dialogType = 'unscheduledDialog'\n\n      this.showDialog()\n    },\n    showDialog() {\n      this.isLessonDialogShown = true\n    },\n    closeDialog() {\n      this.isLessonDialogShown = false\n\n      setTimeout(() => {\n        this.dialogType = null\n      }, 500)\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonItem.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonItem.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./LessonItem.vue?vue&type=template&id=1f692612&\"\nimport script from \"./LessonItem.vue?vue&type=script&lang=js&\"\nexport * from \"./LessonItem.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./LessonItem.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"7dfe9174\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {UserStatus: require('D:/languworks/langu-frontend/components/UserStatus.vue').default,MessageDialog: require('D:/languworks/langu-frontend/components/MessageDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VAvatar } from 'vuetify/lib/components/VAvatar';\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VAvatar,VBtn,VImg})\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{\"dialog\":_vm.isShownMessageDialog,\"max-width\":\"725\",\"custom-class\":\"message-dialog\",\"persistent\":\"\",\"fullscreen\":_vm.$vuetify.breakpoint.xsOnly}},_vm.$listeners),[_c('v-form',{on:{\"submit\":function($event){$event.preventDefault();return _vm.submitHandler.apply(null, arguments)}}},[_c('div',{staticClass:\"message-dialog-header text--gradient\"},[_vm._v(\"\\n      \"+_vm._s(_vm.$t(\n          _vm.$vuetify.breakpoint.xsOnly\n            ? 'ask_question'\n            : 'questions_ask_teacher_directly'\n        ))+\"\\n    \")]),_vm._v(\" \"),_c('div',{class:[\n        'message-dialog-body pt-0 pt-sm-3',\n        {\n          'l-scroll l-scroll--grey l-scroll--large':\n            _vm.$vuetify.breakpoint.xsOnly,\n        } ]},[_c('v-row',{attrs:{\"no-gutters\":\"\"}},[_c('v-col',{staticClass:\"col-12 col-sm-5 col-md-6\"},[_c('div',{staticClass:\"message-dialog-text pr-0 pr-sm-2\"},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.$vuetify.breakpoint.xsOnly),expression:\"$vuetify.breakpoint.xsOnly\"}]},[_c('span',{staticClass:\"text-capitalize\"},[_vm._v(_vm._s(_vm.$t('teacher')))]),_vm._v(\": \"+_vm._s(_vm.recipientName)),_c('br'),_vm._v(\" \"),_c('br')]),_vm._v(\" \"),_c('div',{domProps:{\"innerHTML\":_vm._s(_vm.$t('message_dialog_text', { recipientName: _vm.recipientName }))}})])]),_vm._v(\" \"),_c('v-col',{staticClass:\"col-12 col-sm-7 col-md-6 mt-3 mt-sm-0\"},[_c('editor',{attrs:{\"value\":_vm.newMessage,\"limit\":6000},on:{\"update\":function($event){_vm.newMessage = $event},\"validation\":function($event){_vm.isMessageValid = $event}}})],1)],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"message-dialog-footer d-flex justify-space-between align-center\"},[_c('div',{staticClass:\"prev-button body-1\",on:{\"click\":function($event){return _vm.$emit('close-dialog')}}},[_c('svg',{staticClass:\"mr-1\",attrs:{\"width\":\"17\",\"height\":\"12\",\"viewBox\":\"0 0 17 12\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#arrow-prev\")}})]),_vm._v(_vm._s(_vm.$t('go_back'))+\"\\n      \")]),_vm._v(\" \"),_c('div',[_c('v-btn',{attrs:{\"small\":\"\",\"color\":\"primary\",\"type\":\"submit\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.$t('send_message'))+\"\\n        \")])],1)])])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport LDialog from '~/components/LDialog'\nimport Editor from '~/components/form/Editor'\n\nexport default {\n  name: 'MessageDialog',\n  components: { LDialog, Editor },\n  props: {\n    recipientId: {\n      type: Number,\n      required: true,\n    },\n    recipientName: {\n      type: String,\n      required: true,\n    },\n    isShownMessageDialog: {\n      type: Boolean,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      newMessage: '',\n      isMessageValid: false,\n    }\n  },\n  beforeDestroy() {\n    this.newMessage = ''\n    this.isMessageValid = false\n  },\n  methods: {\n    submitHandler() {\n      if (this.isMessageValid) {\n        this.$store\n          .dispatch('message/sendNewMessage', {\n            recipientId: this.recipientId,\n            message: this.newMessage,\n          })\n          .then((res) => {\n            this.newMessage = ''\n\n            this.$router.push({ path: `/user/messages/${res.id}/view` })\n          })\n          .finally(() => this.$emit('close-dialog'))\n      }\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./MessageDialog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./MessageDialog.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./MessageDialog.vue?vue&type=template&id=01f70911&\"\nimport script from \"./MessageDialog.vue?vue&type=script&lang=js&\"\nexport * from \"./MessageDialog.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./MessageDialog.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"06ad70c7\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {LDialog: require('D:/languworks/langu-frontend/components/LDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VForm } from 'vuetify/lib/components/VForm';\nimport { VRow } from 'vuetify/lib/components/VGrid';\ninstallComponents(component, {VBtn,VCol,VForm,VRow})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonItem.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"a8b919c2\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserStatus.vue?vue&type=style&index=0&id=652352c7&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".user-status[data-v-652352c7]{width:16px;height:16px;border-radius:50%;border:2px solid #fff;background:#636363;z-index:2}.user-status--idle[data-v-652352c7]{background:linear-gradient(122.42deg,var(--v-redLight-base),var(--v-orangeLight2-base))}.user-status--online[data-v-652352c7]{background:var(--v-success-base)}.user-status--large[data-v-652352c7]{width:25px;height:25px}@media only screen and (max-width:991px){.user-status--large[data-v-652352c7]{width:23px;height:23px}}@media only screen and (max-width:639px){.user-status--large[data-v-652352c7]{width:21px;height:21px}}@media only screen and (max-width:479px){.user-status--large[data-v-652352c7]{width:19px;height:19px}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./MessageDialog.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v-application .v-dialog.message-dialog>.v-card{padding:32px 40px!important}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog>.v-card{padding:50px 18px 74px!important}.v-application .v-dialog.message-dialog>.v-card .dialog-content,.v-application .v-dialog.message-dialog>.v-card .message-dialog-body,.v-application .v-dialog.message-dialog>.v-card .v-form{height:100%}.v-application .v-dialog.message-dialog>.v-card .message-dialog-body{overflow-y:auto}}.v-application .v-dialog.message-dialog .message-dialog-header{display:inline-block;padding-right:60px;font-size:20px;font-weight:700;line-height:1.1}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-header{position:absolute;top:0;left:0;width:100%;height:50px;display:flex;align-items:center;padding-left:18px;font-size:18px}}.v-application .v-dialog.message-dialog .message-dialog-body .row .col:first-child{padding-right:20px}.v-application .v-dialog.message-dialog .message-dialog-body .row .col:last-child{padding-left:20px}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-text div:first-child{font-size:16px;font-weight:600}}.v-application .v-dialog.message-dialog .message-dialog-text ul{padding-left:20px}@media only screen and (min-width:992px){.v-application .v-dialog.message-dialog .message-dialog-footer{margin-top:28px}}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-footer{position:absolute;bottom:0;left:0;width:100%;height:74px;padding:0 18px}}.v-application .v-dialog.message-dialog .message-dialog-footer .prev-button{color:var(--v-orange-base);cursor:pointer}.v-application .v-dialog.message-dialog .text-editor .ProseMirror{min-height:248px}.v-application .v-dialog.message-dialog .text-editor-buttons{left:8px;right:auto}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonItem.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".lesson{position:relative;min-height:128px;padding-left:142px}@media only screen and (max-width:1215px){.lesson{min-height:130px;padding-left:85px}}@media only screen and (min-width:768px){.lesson{background:#fff;border-radius:16px;box-shadow:0 6px 10px rgba(17,46,90,.08)}}@media only screen and (max-width:991px){.lesson{padding-left:112px}}@media only screen and (max-width:767px){.lesson{padding-left:0}}.lesson-date{justify-content:center;align-items:center;width:142px;padding:11px;font-size:16px;font-weight:600;background:linear-gradient(126.15deg,rgba(128,182,34,.74),rgba(60,135,248,.74) 102.93%);border-radius:16px;color:#fff}@media only screen and (min-width:768px){.lesson-date{position:absolute;left:0;top:0;height:100%;flex-direction:column;justify-content:space-between;box-shadow:4px 5px 8px hsla(0,0%,40%,.25);z-index:3}}@media only screen and (max-width:1215px){.lesson-date{width:85px;padding:14px 6px;font-size:15px}}@media only screen and (max-width:991px){.lesson-date{width:112px;padding:10px 4px;font-size:13px}}@media only screen and (max-width:767px){.lesson-date{width:100%;padding:7px 10px 32px;font-size:16px!important;font-weight:600!important;border-radius:10px 10px 0 0}.lesson-date>div{display:flex;align-items:center}}@media only screen and (max-width:480px){.lesson-date{font-size:14px!important;font-weight:400!important}}.lesson-date .time,.lesson-date .weekday{font-size:13px;font-weight:700;line-height:1}.lesson-date .date{font-weight:700;font-size:24px;line-height:1.2;white-space:nowrap}@media only screen and (max-width:1215px){.lesson-date .date{margin-bottom:4px;font-size:20px}}@media only screen and (max-width:767px){.lesson-date .date{margin-bottom:0;font-weight:inherit;font-size:inherit}}.lesson-date .remaining{line-height:1.23}@media only screen and (min-width:768px){.lesson-date .remaining{font-size:13px}}@media only screen and (max-width:767px){.lesson-date .remaining{padding:0 3px}}.lesson-date .duration{white-space:nowrap}@media only screen and (min-width:1216px){.lesson-date .duration{padding-left:22px}}@media only screen and (min-width:768px){.lesson-date .duration{position:relative;font-weight:400;color:#e8f1f7}}.lesson-date .duration-icon{position:absolute;left:0;top:50%;width:18px;height:18px;margin-top:-9px;color:var(--v-dark-lighten3)}@media only screen and (max-width:1215px){.lesson-date .duration-icon{display:none}}.lesson-content{position:relative;display:flex;justify-content:space-between;width:100%;border-radius:0 10px 10px 0;overflow:hidden}@media only screen and (max-width:767px){.lesson-content{min-height:128px;margin-top:-25px;border-radius:10px;background:#fff;box-shadow:0 1px 5px hsla(0,0%,51%,.3);z-index:2}}@media only screen and (max-width:639px){.lesson-content{flex-direction:column;padding:12px 12px 4px}}.lesson-details{display:flex;padding:8px 10px 8px 18px}@media only screen and (max-width:1215px){.lesson-details{padding:12px 4px 8px 12px}.lesson-details>.avatar{display:none}}@media only screen and (max-width:767px){.lesson-details{padding-top:10px}}@media only screen and (max-width:639px){.lesson-details{position:relative;padding:0 0 0 100px}}.lesson-details .avatar{position:relative}@media only screen and (max-width:639px){.lesson-details .avatar{position:absolute;left:0;top:0}}.lesson-details .avatar .user-status{position:absolute;top:0;right:0}@media only screen and (max-width:479px){.lesson-details .avatar .user-status{top:1px;right:1px}}.lesson-details .avatar a{position:absolute;top:0;left:0;width:100%;height:100%}.lesson-details .details{display:flex;flex-direction:column;justify-content:space-between}.lesson-details .details .user-info{line-height:1.1}@media only screen and (max-width:1439px){.lesson-details .details .user-info{margin-bottom:8px}}@media only screen and (min-width:1216px){.lesson-details .details .user-info{padding-top:2px}}.lesson-details .details .user-info-name{display:inline;font-size:24px}@media only screen and (min-width:1440px){.lesson-details .details .user-info-name{margin-right:6px}}@media only screen and (max-width:1439px){.lesson-details .details .user-info-name{font-size:22px}}@media only screen and (max-width:1215px){.lesson-details .details .user-info-name{font-size:20px}}@media only screen and (max-width:479px){.lesson-details .details .user-info-name{font-size:18px}}.lesson-details .details .user-info-name div{position:relative;top:-2px;display:inline-block;height:16px;color:var(--v-dark-lighten3);cursor:pointer}.lesson-details .details .user-info-status{display:inline;font-size:13px;color:var(--v-dark-lighten3);text-transform:lowercase}.lesson-details .details .user-info-status--online{color:var(--v-green-lighten1)}.lesson-details .details .lesson-info{font-size:16px;color:var(--v-dark-lighten3);line-height:1.1}@media only screen and (min-width:768px){.lesson-details .details .lesson-info{padding-bottom:4px}}@media only screen and (min-width:1216px){.lesson-details .details .lesson-info .avatar{display:none!important}}@media only screen and (max-width:1215px){.lesson-details .details .lesson-info{display:flex;align-items:flex-end;font-size:14px}}.lesson-details .details .lesson-info>div:not(.avatar)>div:not(:last-child){margin-bottom:3px}@media only screen and (max-width:639px){.lesson-details .details .lesson-info>div:not(.avatar)>div:not(:last-child){margin-bottom:5px}}@media only screen and (min-width:640px){.lesson-details .details .lesson-info .lesson-actions-additional{display:none!important}}.lesson-actions .v-btn:not(.v-btn--icon).v-size--default,.lesson-dialog .v-btn:not(.v-btn--icon).v-size--default{min-width:158px!important;padding:0 10px!important}.lesson-actions{display:flex;width:350px;padding:18px 18px 10px 0}@media only screen and (max-width:1215px){.lesson-actions{padding:12px 12px 8px 0}}@media only screen and (max-width:767px){.lesson-actions{padding-top:10px}}@media only screen and (min-width:640px){.lesson-actions{flex-direction:column;align-items:flex-end;justify-content:space-between;flex-grow:1}}@media only screen and (max-width:639px){.lesson-actions{width:100%;margin-top:8px;padding:8px 0 0;border-top:1px solid rgba(0,0,0,.08)}}.lesson-actions-buttons{display:flex;justify-content:flex-end}@media only screen and (max-width:639px){.lesson-actions-buttons{justify-content:space-between;width:100%}.lesson-actions-buttons .v-btn{width:158px!important;margin-left:0!important;margin-right:0!important}}.lesson-actions-buttons .go-to-class-btn .icon--right{margin-left:3px}.lesson-actions-buttons .go-to-class-btn .icon--rotated{transform:rotate(-90deg)}.lesson-actions-additional{min-width:158px;font-size:13px;line-height:1.333}@media only screen and (max-width:639px){.lesson-actions-additional{font-size:inherit;line-height:inherit}}@media only screen and (max-width:639px){.lesson-actions-additional>div{margin-top:5px}}.lesson-actions-additional>div>div,.lesson-actions-additional a,.lesson-actions-additional span.action{position:relative;display:inline-block;padding-left:20px;color:var(--v-darkLight-lighten3)!important;text-decoration:none;transition:color .3s}.lesson-actions-additional>div>div .v-image,.lesson-actions-additional a .v-image,.lesson-actions-additional span.action .v-image{position:absolute;left:0;top:50%;transform:translateY(-50%)}.lesson-actions-additional a,.lesson-actions-additional span.action{cursor:pointer}.lesson-actions-additional a:hover,.lesson-actions-additional span.action:hover{color:var(--v-success-base)!important}@media only screen and (max-width:639px){.lesson-actions .lesson-actions-additional{display:none!important}}.lesson-dialog{position:absolute;top:0;left:100%;width:100%;height:100%;padding:10px 18px 12px;font-size:13px;line-height:1.2;background-color:#fff;border-radius:0 16px 16px 0;z-index:2;transition:all .3s ease-in}@media only screen and (max-width:1215px){.lesson-dialog{padding:8px 12px}}@media only screen and (max-width:991px){.lesson-dialog{left:0;top:100%}}@media only screen and (max-width:639px){.lesson-dialog.justify-center{justify-content:space-between!important}}.lesson-dialog-title{display:flex;margin-bottom:5px;font-size:20px}@media only screen and (max-width:639px){.lesson-dialog-title{margin-bottom:12px;font-size:18px}}.lesson-dialog-title-icon{display:flex;align-items:center;padding-right:4px}.lesson-dialog-content{color:var(--v-dark-lighten3);overflow-y:auto}@media only screen and (max-width:639px){.lesson-dialog-buttons{display:flex;justify-content:space-between;margin-top:8px;border-top:1px solid rgba(0,0,0,.08)}}.lesson-dialog-buttons .v-btn{max-width:246px;margin-top:8px}@media only screen and (min-width:640px){.lesson-dialog-buttons .v-btn{width:100%}}.lesson-dialog-buttons>:first-child{margin-right:16px}@media only screen and (max-width:639px){.lesson-dialog-buttons>:first-child{margin-left:4px}}.lesson-dialog--student-info .lesson-dialog-content{display:flex}@media only screen and (min-width:640px){.lesson-dialog--student-info .lesson-dialog-content{margin-right:168px}}.lesson-dialog--student-info .lesson-dialog-content>div{display:flex;flex-grow:1}@media only screen and (max-width:639px){.lesson-dialog--student-info .lesson-dialog-content>div{width:100%;max-width:100%}}@media only screen and (max-width:479px){.lesson-dialog--student-info .lesson-dialog-content>div{flex-wrap:wrap}}@media only screen and (max-width:479px){.lesson-dialog--student-info .lesson-dialog-content>div>div{padding:0!important}}@media only screen and (min-width:480px){.lesson-dialog--student-info .lesson-dialog-content>div>div:first-child{padding-right:45px;border-right:1px solid var(--v-dark-lighten3)}}.lesson-dialog--student-info .lesson-dialog-content>div>div ul{padding:0;list-style-type:none;line-height:1.2}@media only screen and (min-width:1216px){.lesson-dialog--student-info .lesson-dialog-buttons{right:18px;bottom:12px}}@media only screen and (min-width:640px){.lesson-dialog--student-info .lesson-dialog-buttons{position:absolute;right:12px;bottom:8px}}@media only screen and (max-width:639px){.lesson-dialog--student-info .lesson-dialog-buttons{display:flex;justify-content:flex-end;align-items:flex-end}}.lesson-dialog--student-info .lesson-dialog-buttons .v-btn{margin-right:0!important}.lesson-dialog--shown{left:0;transition:all .3s ease-out}@media only screen and (max-width:991px){.lesson-dialog--shown{top:0}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n"], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AAHA;;ACxCA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AATA;AAcA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AAfA;AAhBA;;ACXA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;ACtBA;AACA;AACA;AACA;AAGA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAdA;AADA;;ACDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AACA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAQA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;AACA;AAUA;AACA;AACA;AACA;AAGA;AADA;AAIA;AADA;AAPA;AAaA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAQA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApCA;AAzFA;;AC/CA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AClCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAOA;AACA;AAAA;AACA;AAKA;AACA;AArEA;AAsEA;AACA;AACA;AAGA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAzDA;AA7FA;;AC/dA;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AClCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AATA;AACA;AAaA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAFA;AAKA;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAhBA;AA3BA;;ACzEA;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACnCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}