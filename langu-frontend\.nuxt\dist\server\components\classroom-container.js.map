{"version": 3, "file": "components/classroom-container.js", "sources": ["webpack:///./components/classroom/vue-draggable-resizable/vue-draggable-resizable.css", "webpack:///./components/classroom/ClassroomContainer.vue?2f00", "webpack:///./components/classroom/ClassroomContainer.vue", "webpack:///./components/classroom/ClassroomContainer.vue?ecad", "webpack:///./components/classroom/ClassroomContainer.vue?cf0e", "webpack:///./helpers/fns.js", "webpack:///./mixins/SetTool.vue", "webpack:///./mixins/SetTool.vue?42f1", "webpack:///./mixins/SetTool.vue?6f86", "webpack:///./components/classroom/vue-draggable-resizable/VueDraggableResizable.vue?a82c", "webpack:///./components/classroom/vue-draggable-resizable/VueDraggableResizable.vue", "webpack:///./components/classroom/vue-draggable-resizable/VueDraggableResizable.vue?8b19", "webpack:///./components/classroom/vue-draggable-resizable/VueDraggableResizable.vue?aa5b", "webpack:///./components/classroom/vue-draggable-resizable/vue-draggable-resizable.css?1c03", "webpack:///./helpers/dom.js"], "sourcesContent": ["// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_GET_URL_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/getUrl.js\");\nvar ___CSS_LOADER_URL_IMPORT_0___ = require(\"../../../assets/images/classroom/corner-resize-marker.svg\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".vdr{touch-action:none;position:absolute;box-sizing:border-box;border:none}.handle{display:block!important}.resizable.vdr:not(.hide-resize-icon):after{content:\\\"\\\";position:absolute;bottom:4px;right:2px;width:8px;height:8px;background-image:url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \");background-size:contain;background-repeat:no-repeat;background-position:50%;opacity:.9;z-index:99}.vdr .handle{box-sizing:border-box;position:absolute;width:12px;height:12px;border:none;background-color:transparent;z-index:10}.vdr .handle-tl{top:-7px;left:-7px}.vdr .handle-tm{top:-7px;margin-left:-5px}.vdr .handle-tr{top:-7px;right:-7px}.vdr .handle-ml{margin-top:-5px;left:-7px}.vdr .handle-mr{margin-top:-5px;right:-7px}.vdr .handle-bl{bottom:-7px;left:-7px}.vdr .handle-bm{bottom:-7px;margin-left:-5px}.vdr .handle-br{bottom:-7px;right:-7px}@media only screen and (max-width:768px){[class*=handle-]:before{content:\\\"\\\";left:-10px;right:-10px;bottom:-10px;top:-10px;position:absolute}}.vdr .handle-bm,.vdr .handle-tm{width:100%;left:5px}.vdr .handle-ml,.vdr .handle-mr{height:100%;top:5px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:14px;height:14px;z-index:11}@media only screen and (max-width:1199px){.vdr .handle-bm,.vdr .handle-tm{height:15px}.vdr .handle-ml,.vdr .handle-mr{width:15px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:15px;height:15px}.vdr .handle-tl{top:-10px;left:-10px}.vdr .handle-tm{top:-10px;margin-left:-5px}.vdr .handle-tr{top:-10px;right:-10px}.vdr .handle-ml{margin-top:-5px;left:-10px}.vdr .handle-mr{margin-top:-5px;right:-10px}.vdr .handle-bl{bottom:-10px;left:-10px}.vdr .handle-bm{bottom:-10px;margin-left:-5px}.vdr .handle-br{bottom:-10px;right:-10px}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{on:{\"mouseenter\":_vm.mouseenter,\"mouseleave\":_vm.mouseleave}},[_c('vue-draggable-resizable',{ref:\"vueDraggableResizable\",class:{\n      student: _vm.role === 'student',\n      teacher: _vm.role === 'teacher',\n      'hide-resize-icon': _vm.hideResizeIcon,\n    },style:({\n      outline:\n        _vm.isHoveredByAsset || _vm.isHovered\n          ? (\"3px solid \" + _vm.getRoleHoverColor)\n          : 'none',\n    }),attrs:{\"draggable\":_vm.enabledDraggable,\"resizable\":_vm.enabledResizeable,\"w\":_vm.width,\"h\":_vm.height + _vm.childHeaderHeight,\"x\":_vm.leftScale,\"y\":_vm.topScale,\"z\":_vm.index,\"zoom-index\":_vm.scalable ? _vm.zoom.zoomIndex : 1,\"zoom-x\":_vm.zoom.x,\"zoom-y\":_vm.zoom.y,\"child-header-height\":_vm.childHeaderHeight,\"lock-aspect-ratio\":_vm.lockAspectRatio,\"handles\":_vm.handles},on:{\"dragging\":_vm.onDrag,\"resizing\":_vm.onResize,\"dragstop\":_vm.onDragStop,\"resizestop\":_vm.onResizeStop,\"update-asset\":_vm.updateAsset},nativeOn:{\"click\":function($event){return _vm.onIndex.apply(null, arguments)}}},[_vm._t(\"default\",null,{\"onIndex\":_vm.onIndex})],2)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport VueDraggableResizable from '~/components/classroom/vue-draggable-resizable/VueDraggableResizable'\nimport {\n  defaultWidth,\n  defaultHeight,\n  mainCanvasWidth,\n  mainCanvasHeight,\n} from '~/helpers/constants'\n\nimport SetTool from '~/mixins/SetTool'\n\nimport '~/components/classroom/vue-draggable-resizable/vue-draggable-resizable.css'\n\nexport default {\n  components: { VueDraggableResizable },\n  mixins: [SetTool],\n  props: {\n    asset: {\n      type: Object,\n      required: true,\n    },\n    childHeaderHeight: {\n      type: Number,\n      default: 0,\n    },\n    lockAspectRatio: {\n      type: Boolean,\n      default: true,\n    },\n    scalable: {\n      type: Boolean,\n      default: true,\n    },\n    hoverEnabled: {\n      type: Boolean,\n      default: true,\n    },\n    isDraggableProp: {\n      type: Boolean,\n      default: true,\n    },\n    hideResizeIcon: {\n      type: Boolean,\n      default: false,\n    },\n    handles: {\n      type: Array,\n      default: () => ['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml'],\n    },\n  },\n  data() {\n    return {\n      width: defaultWidth,\n      height: defaultHeight,\n      top: 50,\n      left: 500,\n      isHovered: false,\n      isHoveredByAsset: false,\n      index: 5,\n      eventBodyClass: null,\n      allowIndexChange: true,\n      resizable: true,\n      draggable: true,\n      synchronizeable: true,\n      viewportWidth: window.innerWidth,\n      viewportHeight: window.innerHeight,\n      offset: 5,\n    }\n  },\n  computed: {\n    isCanvasOversizeX() {\n      return mainCanvasWidth > this.viewportWidth\n    },\n    isCanvasOversizeY() {\n      return mainCanvasHeight > this.viewportHeight\n    },\n    isScaledCanvasOversizeY() {\n      return mainCanvasHeight * this.zoomIndex > this.viewportHeight\n    },\n    getRoleHoverColor() {\n      return this.role === 'teacher'\n        ? 'var(--v-teacherColor-base)'\n        : 'var(--v-studentColor-base)'\n    },\n    enabledResizeable() {\n      return (\n        this.resizable &&\n        this.$store.state.classroom.containerComponentEnabled &&\n        !this.isLockedForStudent\n      )\n    },\n    enabledDraggable() {\n      return (\n        this.isDraggableProp &&\n        this.draggable &&\n        this.$store.state.classroom.containerComponentEnabled &&\n        !this.isLockedForStudent\n      )\n    },\n    maxIndex() {\n      return this.$store.state.classroom.maxIndex\n    },\n    zoom() {\n      return this.$store.getters['classroom/zoomAsset'].asset\n    },\n    zoomIndex() {\n      return this.zoom?.zoomIndex ?? 1\n    },\n    type() {\n      return this.asset?.asset?.type\n    },\n    topScale() {\n      if (this.type === 'toolbar') {\n        return this.top < 0 || this.top + this.height > this.viewportHeight\n          ? this.viewportHeight - this.height - 70\n          : this.top\n      }\n\n      return this.synchronizeable ? this.top - this.zoom.y : this.top\n    },\n    leftScale() {\n      if (this.type === 'toolbar') {\n        return this.left + this.width > this.viewportWidth || this.left < 0\n          ? this.viewportWidth - this.width - 15\n          : this.left\n      }\n\n      return this.synchronizeable ? this.left - this.zoom.x : this.left\n    },\n    isLockedForStudent() {\n      return (\n        this.$store.getters['classroom/isLocked'] && this.role === 'student'\n      )\n    },\n    isSocketConnected() {\n      return this.$store.state.socket.isConnected\n    },\n  },\n  watch: {\n    'asset.asset'(asset) {\n      this.move(asset)\n    },\n  },\n  mounted() {\n    this.move(this.asset.asset)\n  },\n  methods: {\n    mouseenter() {\n      if (this.hoverEnabled && this.synchronizeable) {\n        this.isHovered = true\n\n        this.socketAssetMoved({ isHovered: true })\n      }\n\n      if (\n        this.type === 'twilio' ||\n        this.type === 'tokbox' ||\n        this.type === 'editor'\n      ) {\n        this.$store.commit(\n          'classroom/setCursorNameBeforeChange',\n          this.$store.getters['classroom/userParams']?.cursor ||\n            'cursor-pointer'\n        )\n        this.$store.commit(\n          'classroom/setToolNameBeforeChange',\n          this.$store.getters['classroom/userParams']?.tool || 'pointer'\n        )\n\n        this.setTool('pointer', 'cursor-pointer')\n      }\n    },\n    mouseleave() {\n      if (this.hoverEnabled && this.synchronizeable) {\n        this.isHovered = false\n\n        this.socketAssetMoved({ isHovered: false })\n      }\n\n      if (\n        this.type === 'twilio' ||\n        this.type === 'tokbox' ||\n        this.type === 'editor'\n      ) {\n        this.setTool(\n          this.$store.state.classroom.toolNameBeforeChange,\n          this.$store.state.classroom.cursorNameBeforeChange\n        )\n      }\n    },\n    onIndex() {\n      this.index = this.maxIndex + 1\n\n      this.$store.commit('classroom/setMaxIndex', this.index)\n      this.$store.commit('classroom/moveAsset', {\n        id: this.asset.id,\n        asset: { index: this.index },\n      })\n      this.$store.dispatch('classroom/moveAsset', {\n        id: this.asset.id,\n        lessonId: this.asset.lessonId,\n        asset: {\n          index: this.index,\n        },\n      })\n    },\n    updateAsset(left, top) {\n      this.$store.commit('classroom/moveAsset', {\n        id: this.asset.id,\n        asset: {\n          left: this.synchronizeable ? left + this.zoom.x : left,\n          top: this.synchronizeable ? top + this.zoom.y : top,\n          index: this.index,\n        },\n      })\n    },\n    onDrag(left, top) {\n      if (this.synchronizeable) {\n        this.left = left + this.zoom.x\n        this.top = top + this.zoom.y\n\n        if (this.allowIndexChange) {\n          const el = document.body\n\n          if (!el.classList.contains(this.eventBodyClass)) {\n            this.eventBodyClass = 'dragging'\n\n            el.classList.add(this.eventBodyClass)\n          }\n\n          this.allowIndexChange = false\n          this.index = this.maxIndex + 1\n\n          this.$store.commit('classroom/setMaxIndex', this.index)\n        }\n\n        const asset = {\n          left: this.left,\n          top: this.top,\n          index: this.index,\n        }\n\n        this.$store.commit('classroom/moveAsset', {\n          id: this.asset.id,\n          asset,\n        })\n\n        this.socketAssetMoved(asset)\n      }\n    },\n    onDragStop(left, top) {\n      const el = document.body\n\n      if (el.classList.contains(this.eventBodyClass)) {\n        el.classList.remove(this.eventBodyClass)\n\n        this.eventBodyClass = null\n      }\n\n      this.allowIndexChange = true\n\n      this.$store.commit('classroom/setMaxIndex', this.index)\n      this.$store.dispatch('classroom/moveAsset', {\n        id: this.asset.id,\n        lessonId: this.asset.lessonId,\n        asset: {\n          left: this.synchronizeable ? left + this.zoom.x : left,\n          top: this.synchronizeable ? top + this.zoom.y : top,\n          index: this.index,\n        },\n      })\n    },\n    onResize(left, top, width, height, className) {\n      if (this.synchronizeable) {\n        this.left = left + this.zoom.x\n        this.top = top + this.zoom.y\n        this.width = width\n        this.height = height - this.childHeaderHeight\n\n        if (this.allowIndexChange) {\n          const el = document.body\n\n          if (!el.classList.contains(this.eventBodyClass)) {\n            this.eventBodyClass = className.split(' ')[1]\n\n            el.classList.add(this.eventBodyClass)\n          }\n\n          this.allowIndexChange = false\n          this.index = this.maxIndex + 1\n\n          this.$store.commit('classroom/setMaxIndex', this.index)\n        }\n\n        const asset = {\n          left: this.left,\n          top: this.top,\n          width: this.width,\n          height: this.height,\n          index: this.index,\n        }\n\n        this.$store.commit('classroom/moveAsset', {\n          id: this.asset.id,\n          asset,\n        })\n\n        this.socketAssetMoved(asset)\n      }\n    },\n    onResizeStop(left, top, width, height) {\n      if (this.eventBodyClass) {\n        document.body.classList.remove(this.eventBodyClass)\n      }\n\n      this.allowIndexChange = true\n\n      this.$store.dispatch('classroom/moveAsset', {\n        id: this.asset.id,\n        lessonId: this.asset.lessonId,\n        asset: {\n          left: this.synchronizeable ? left + this.zoom.x : left,\n          top: this.synchronizeable ? top + this.zoom.y : top,\n          width,\n          height: height - this.childHeaderHeight,\n          index: this.index,\n        },\n      })\n    },\n    socketAssetMoved(asset) {\n      if (this.isSocketConnected) {\n        this.$socket.emit('asset-moved', {\n          id: this.asset.id,\n          lessonId: this.asset.lessonId,\n          asset,\n        })\n      }\n    },\n    move(asset) {\n      if (asset.width !== undefined) {\n        this.width = asset.width\n      } else {\n        // if (this.type === 'toolbar') {\n        //   this.width = this.$refs.vueDraggableResizable.$el.clientWidth\n        // }\n\n        // eslint-disable-next-line no-lonely-if\n        if (this.type === 'editor') {\n          this.width =\n            (this.isCanvasOversizeX ? this.viewportWidth : mainCanvasWidth) *\n            0.66\n        }\n      }\n\n      if (asset.height !== undefined) {\n        this.height = asset.height\n      } else {\n        // if (this.type === 'toolbar') {\n        //   this.height = this.$refs.vueDraggableResizable.$el.clientHeight\n        // }\n\n        // eslint-disable-next-line no-lonely-if\n        if (this.type === 'editor') {\n          let height =\n            (this.isCanvasOversizeY ? this.viewportHeight : mainCanvasHeight) *\n            0.8\n\n          if (height > 1200) {\n            height = 1200\n          }\n\n          if (height < 400) {\n            height = 400\n          }\n\n          this.height = height - this.offset * 2\n        }\n\n        if (this.type === 'audio') {\n          this.height = 0\n        }\n      }\n\n      if (asset.top !== undefined) {\n        this.top = asset.top\n      } else {\n        // if (this.type === 'toolbar') {\n        //   this.top = (this.isScaledCanvasOversizeY ? this.viewportHeight : mainCanvasHeight * this.zoomIndex) - this.height - this.offset\n        // }\n\n        // eslint-disable-next-line no-lonely-if\n        if (\n          this.type === 'twilio' ||\n          this.type === 'tokbox' ||\n          this.type === 'editor'\n        ) {\n          this.top = this.offset\n        }\n      }\n\n      if (asset.left !== undefined) {\n        this.left = asset.left\n      } else {\n        // if (this.type === 'toolbar') {\n        //   this.left = (this.isScaledCanvasOversizeX ? this.viewportWidth : mainCanvasWidth ) - this.width - this.offset\n        // }\n\n        if (this.type === 'twilio' || this.type === 'tokbox') {\n          this.left =\n            (this.isCanvasOversizeX ? this.viewportWidth : mainCanvasWidth) -\n            this.width -\n            this.offset\n        }\n\n        if (this.type === 'editor') {\n          this.left = this.offset\n        }\n      }\n\n      if (asset.index !== undefined) {\n        this.index = asset.index\n      }\n\n      if (asset.resizable !== undefined) {\n        this.resizable = asset.resizable\n      }\n\n      if (asset.draggable !== undefined) {\n        this.draggable = asset.draggable\n      }\n\n      if (asset.synchronizeable !== undefined) {\n        this.synchronizeable = asset.synchronizeable\n      }\n\n      if (asset.isHovered !== undefined) {\n        this.isHoveredByAsset = asset.isHovered\n      }\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ClassroomContainer.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ClassroomContainer.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ClassroomContainer.vue?vue&type=template&id=5f513aff&\"\nimport script from \"./ClassroomContainer.vue?vue&type=script&lang=js&\"\nexport * from \"./ClassroomContainer.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"5ca5812f\"\n  \n)\n\nexport default component.exports", "export function isFunction(func) {\n  return (\n    typeof func === 'function' ||\n    Object.prototype.toString.call(func) === '[object Function]'\n  )\n}\n\nexport function snapToGrid(grid, pendingX, pendingY, scale = 1) {\n  const [scaleX, scaleY] = typeof scale === 'number' ? [scale, scale] : scale\n  const x = Math.round(pendingX / scaleX / grid[0]) * grid[0]\n  const y = Math.round(pendingY / scaleY / grid[1]) * grid[1]\n  return [x, y]\n}\n\nexport function getSize(el) {\n  const rect = el.getBoundingClientRect()\n\n  return [parseInt(rect.width), parseInt(rect.height)]\n}\n\nexport function computeWidth(parentWidth, left, right) {\n  return parentWidth - left - right\n}\n\nexport function computeHeight(parentHeight, top, bottom) {\n  return parentHeight - top - bottom\n}\n\nexport function restrictToBounds(value, min, max) {\n  if (min !== null && value < min) {\n    return min\n  }\n\n  if (max !== null && max < value) {\n    return max\n  }\n\n  return value\n}\n", "\nexport default {\n  computed: {\n    role() {\n      return this.$store.getters['classroom/role']\n    },\n  },\n  methods: {\n    setTool(toolName, cursorName) {\n      this.$store.commit(\n        'classroom/enableContainerComponent',\n        toolName === 'pointer'\n      )\n      this.$socket.emit('cursor-moved', {\n        tool: toolName,\n        cursor: cursorName.replace(/(-cursor|cursor-)/i, ''),\n        lessonId: this.$store.state.classroom.lessonId,\n      })\n\n      this.$store.commit('classroom/setUserTool', toolName)\n      this.$store.commit('classroom/setUserCursor', cursorName)\n\n      const el = document.body\n      const classList = el.classList\n\n      this.removeCursors(classList)\n      el.classList.add(`${this.role}-${cursorName}`)\n\n      this.classList = el.classList\n    },\n    removeCursors(classList) {\n      classList.forEach((item) => {\n        if (item.includes('cursor')) {\n          document.body.classList.remove(item)\n        }\n      })\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./SetTool.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./SetTool.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./SetTool.vue?vue&type=script&lang=js&\"\nexport * from \"./SetTool.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"2e32be2e\"\n  \n)\n\nexport default component.exports", "var render = function () {\nvar _obj;\nvar _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[\n    ( _obj = {}, _obj[_vm.classNameActive] = _vm.enabled, _obj[_vm.classNameDragging] = _vm.dragging, _obj[_vm.classNameResizing] = _vm.resizing, _obj[_vm.classNameDraggable] = _vm.draggable, _obj[_vm.classNameResizable] = _vm.resizable, _obj ),\n    _vm.className ],style:(_vm.style),on:{\"mousedown\":_vm.elementMouseDown,\"touchstart\":_vm.elementTouchDown}},[_vm._l((_vm.actualHandles),function(handle){return _vm._ssrNode(\"<div\"+(_vm._ssrClass(null,[_vm.classNameHandle, _vm.classNameHandle + '-' + handle]))+(_vm._ssrStyle(null,{ display: _vm.enabled ? 'block' : 'none' }, null))+\">\",\"</div>\",[_vm._t(handle)],2)}),_vm._ssrNode(\" \"),_vm._t(\"default\")],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {\n  matchesSelectorToParentElements,\n  getComputedSize,\n  addEvent,\n  removeEvent,\n} from '~/helpers/dom'\nimport {\n  computeWidth,\n  computeHeight,\n  restrictToBounds,\n  snapToGrid,\n} from '~/helpers/fns'\nimport {\n  mainCanvasWidth,\n  mainCanvasHeight,\n  mainCanvasOffsetX,\n  mainCanvasOffsetY,\n} from '~/helpers/constants'\n\nconst events = {\n  mouse: {\n    start: 'mousedown',\n    move: 'mousemove',\n    stop: 'mouseup',\n  },\n  touch: {\n    start: 'touchstart',\n    move: 'touchmove',\n    stop: 'touchend',\n  },\n}\n\nconst userSelectNone = {\n  userSelect: 'none',\n  MozUserSelect: 'none',\n  WebkitUserSelect: 'none',\n  MsUserSelect: 'none',\n}\n\nconst userSelectAuto = {\n  userSelect: 'auto',\n  MozUserSelect: 'auto',\n  WebkitUserSelect: 'auto',\n  MsUserSelect: 'auto',\n}\n\nlet eventsFor = events.mouse\n\nexport default {\n  replace: true,\n  name: 'VueDraggableResizable',\n  props: {\n    childHeaderHeight: {\n      type: Number,\n      default: 0,\n    },\n    zoomIndex: {\n      type: Number,\n      required: true,\n    },\n    zoomX: {\n      type: Number,\n      required: true,\n    },\n    zoomY: {\n      type: Number,\n      required: true,\n    },\n    className: {\n      type: String,\n      default: 'vdr',\n    },\n    classNameDraggable: {\n      type: String,\n      default: 'draggable',\n    },\n    classNameResizable: {\n      type: String,\n      default: 'resizable',\n    },\n    classNameDragging: {\n      type: String,\n      default: 'dragging',\n    },\n    classNameResizing: {\n      type: String,\n      default: 'resizing',\n    },\n    classNameActive: {\n      type: String,\n      default: 'active',\n    },\n    classNameHandle: {\n      type: String,\n      default: 'handle',\n    },\n    disableUserSelect: {\n      type: Boolean,\n      default: true,\n    },\n    enableNativeDrag: {\n      type: Boolean,\n      default: false,\n    },\n    preventDeactivation: {\n      type: Boolean,\n      default: false,\n    },\n    active: {\n      type: Boolean,\n      default: false,\n    },\n    draggable: {\n      type: Boolean,\n      default: true,\n    },\n    resizable: {\n      type: Boolean,\n      default: true,\n    },\n    lockAspectRatio: {\n      type: Boolean,\n      default: false,\n    },\n    w: {\n      type: [Number, String],\n      default: 200,\n      validator: (val) => {\n        if (typeof val === 'number') {\n          return val > 0\n        }\n\n        return val === 'auto'\n      },\n    },\n    h: {\n      type: [Number, String],\n      default: 200,\n      validator: (val) => {\n        if (typeof val === 'number') {\n          return val > 0\n        }\n\n        return val === 'auto'\n      },\n    },\n    minWidth: {\n      type: Number,\n      default: 320,\n      validator: (val) => val >= 0,\n    },\n    minHeight: {\n      type: Number,\n      default: 200,\n      validator: (val) => val >= 0,\n    },\n    maxWidth: {\n      type: Number,\n      default: null,\n      validator: (val) => val >= 0,\n    },\n    maxHeight: {\n      type: Number,\n      default: null,\n      validator: (val) => val >= 0,\n    },\n    x: {\n      type: Number,\n      default: 0,\n    },\n    y: {\n      type: Number,\n      default: 0,\n    },\n    z: {\n      type: [String, Number],\n      default: 'auto',\n      validator: (val) => (typeof val === 'string' ? val === 'auto' : val >= 0),\n    },\n    handles: {\n      type: Array,\n      default: () => ['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml'],\n      validator: (val) => {\n        const s = new Set(['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml'])\n\n        return new Set(val.filter((h) => s.has(h))).size === val.length\n      },\n    },\n    dragHandle: {\n      type: String,\n      default: null,\n    },\n    dragCancel: {\n      type: String,\n      default: null,\n    },\n    axis: {\n      type: String,\n      default: 'both',\n      validator: (val) => ['x', 'y', 'both'].includes(val),\n    },\n    grid: {\n      type: Array,\n      default: () => [1, 1],\n    },\n    parent: {\n      type: Boolean,\n      default: true,\n    },\n    scale: {\n      type: [Number, Array],\n      default: 1,\n      validator: (val) => {\n        if (typeof val === 'number') {\n          return val > 0\n        }\n\n        return val.length === 2 && val[0] > 0 && val[1] > 0\n      },\n    },\n    onDragStart: {\n      type: Function,\n      default: () => true,\n    },\n    onDrag: {\n      type: Function,\n      default: () => true,\n    },\n    onResizeStart: {\n      type: Function,\n      default: () => true,\n    },\n    onResize: {\n      type: Function,\n      default: () => true,\n    },\n  },\n  data() {\n    return {\n      left: null,\n      top: null,\n      right: null,\n      bottom: null,\n\n      width: null,\n      height: null,\n\n      widthTouched: false,\n      heightTouched: false,\n\n      aspectFactor: null,\n\n      parentWidth: null,\n      parentHeight: null,\n\n      minW: this.minWidth,\n      minH: this.minHeight,\n\n      maxW: this.maxWidth,\n      maxH: this.maxHeight,\n\n      handle: null,\n      enabled: this.active,\n      resizing: false,\n      dragging: false,\n      dragEnable: false,\n      resizeEnable: false,\n      zIndex: this.z,\n      originalHandle: null,\n    }\n  },\n  computed: {\n    computedLeft() {\n      return (\n        this.left * this.zoomIndex - (this.width * (1 - this.zoomIndex)) / 2\n      )\n    },\n    computedTop() {\n      return (\n        this.top * this.zoomIndex - (this.height * (1 - this.zoomIndex)) / 2\n      )\n    },\n    style() {\n      return {\n        transform: `translate(${this.computedLeft}px, ${this.computedTop}px) scale(${this.zoomIndex})`,\n        width: this.computedWidth,\n        height: this.computedHeight,\n        zIndex: this.zIndex,\n        ...(this.dragging && this.disableUserSelect\n          ? userSelectNone\n          : userSelectAuto),\n      }\n    },\n    actualHandles() {\n      if (!this.resizable) return []\n\n      return this.handles\n    },\n    computedWidth() {\n      if (this.w === 'auto') {\n        if (!this.widthTouched) {\n          return 'auto'\n        }\n      }\n\n      return this.width + 'px'\n    },\n    computedHeight() {\n      if (this.h === 'auto') {\n        if (!this.heightTouched) {\n          return 'auto'\n        }\n      }\n\n      return this.height + 'px'\n    },\n    resizingOnX() {\n      return (\n        Boolean(this.handle) &&\n        (this.handle.includes('l') || this.handle.includes('r'))\n      )\n    },\n    resizingOnY() {\n      return (\n        Boolean(this.handle) &&\n        (this.handle.includes('t') || this.handle.includes('b'))\n      )\n    },\n    isCornerHandle() {\n      return (\n        Boolean(this.handle) && ['tl', 'tr', 'br', 'bl'].includes(this.handle)\n      )\n    },\n  },\n  watch: {\n    active(val) {\n      this.enabled = val\n\n      if (val) {\n        this.$emit('activated')\n      } else {\n        this.$emit('deactivated')\n      }\n    },\n    z(val) {\n      if (val >= 0 || val === 'auto') {\n        this.zIndex = val\n      }\n    },\n    w(val) {\n      if (this.resizing || this.dragging) {\n        return\n      }\n\n      if (this.parent) {\n        this.bounds = this.calcResizeLimits()\n      }\n\n      this.changeWidth(val)\n    },\n    h(val) {\n      if (this.resizing || this.dragging) {\n        return\n      }\n      if (this.parent) {\n        this.bounds = this.calcResizeLimits()\n      }\n\n      this.changeHeight(val)\n    },\n    x(val) {\n      if (this.resizing || this.dragging) {\n        return\n      }\n\n      if (this.parent) {\n        this.bounds = this.calcDragLimits()\n      }\n\n      this.moveHorizontally(val)\n    },\n    y(val) {\n      if (this.resizing || this.dragging) {\n        return\n      }\n\n      if (this.parent) {\n        this.bounds = this.calcDragLimits()\n      }\n\n      this.moveVertically(val)\n    },\n    lockAspectRatio(val) {\n      if (val) {\n        this.aspectFactor = this.width / (this.height - this.childHeaderHeight)\n      } else {\n        this.aspectFactor = undefined\n      }\n    },\n    minWidth(val) {\n      if (val > 0 && val <= this.width) {\n        this.minW = val\n      }\n    },\n    minHeight(val) {\n      if (val > 0 && val <= this.height) {\n        this.minH = val\n      }\n    },\n    maxWidth(val) {\n      this.maxW = val\n    },\n    maxHeight(val) {\n      this.maxH = val\n    },\n  },\n  created() {\n    // eslint-disable-next-line\n    if (this.maxWidth && this.minWidth > this.maxWidth) console.warn('[Vdr warn]: Invalid prop: minWidth cannot be greater than maxWidth')\n    // eslint-disable-next-line\n    if (this.maxWidth && this.minHeight > this.maxHeight) console.warn('[Vdr warn]: Invalid prop: minHeight cannot be greater than maxHeight')\n\n    this.resetBoundsAndMouseState()\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.left = this.x\n      this.top = this.y\n\n      if (!this.enableNativeDrag) {\n        this.$el.ondragstart = () => false\n      }\n\n      const [parentWidth, parentHeight] = this.getParentSize()\n\n      this.parentWidth = parentWidth\n      this.parentHeight = parentHeight\n\n      const [width, height] = getComputedSize(this.$el)\n\n      this.aspectFactor =\n        (this.w !== 'auto' ? this.w : width) /\n        ((this.h !== 'auto' ? this.h : height) - this.childHeaderHeight)\n\n      this.width = this.w !== 'auto' ? this.w : width\n      this.height = this.h !== 'auto' ? this.h : height\n\n      this.right = this.parentWidth - this.width - this.left\n      this.bottom = this.parentHeight - this.height - this.top\n\n      if (this.active) {\n        this.$emit('activated')\n      }\n\n      if (\n        this.computedLeft + this.width >\n        mainCanvasWidth + mainCanvasOffsetX + this.zoomX\n      ) {\n        this.$emit(\n          'update-asset',\n          mainCanvasWidth - mainCanvasOffsetX + this.zoomX - this.width,\n          this.top\n        )\n      }\n\n      addEvent(document.documentElement, 'mousedown', this.deselect)\n      addEvent(document.documentElement, 'touchend touchcancel', this.deselect)\n\n      addEvent(window, 'resize', this.checkParentSize)\n    })\n  },\n  beforeDestroy() {\n    removeEvent(document.documentElement, 'mousedown', this.deselect)\n    removeEvent(document.documentElement, 'touchstart', this.handleUp)\n    removeEvent(document.documentElement, 'mousemove', this.move)\n    removeEvent(document.documentElement, 'touchmove', this.move)\n    removeEvent(document.documentElement, 'mouseup', this.handleUp)\n    removeEvent(document.documentElement, 'touchend touchcancel', this.deselect)\n\n    removeEvent(window, 'resize', this.checkParentSize)\n  },\n  methods: {\n    resetBoundsAndMouseState() {\n      this.mouseClickPosition = { mouseX: 0, mouseY: 0, x: 0, y: 0, w: 0, h: 0 }\n\n      this.bounds = {\n        minLeft: null,\n        maxLeft: null,\n        minRight: null,\n        maxRight: null,\n        minTop: null,\n        maxTop: null,\n        minBottom: null,\n        maxBottom: null,\n      }\n    },\n    checkParentSize() {\n      if (this.parent) {\n        const [newParentWidth, newParentHeight] = this.getParentSize()\n\n        this.parentWidth = newParentWidth\n        this.parentHeight = newParentHeight\n        this.right = this.parentWidth - this.width - this.left\n        this.bottom = this.parentHeight - this.height - this.top\n      }\n    },\n    getParentSize() {\n      if (this.parent) {\n        return [mainCanvasWidth, mainCanvasHeight]\n      }\n\n      return [null, null]\n    },\n    elementTouchDown(e) {\n      eventsFor = events.touch\n\n      this.elementDown(e)\n    },\n    elementMouseDown(e) {\n      eventsFor = events.mouse\n\n      this.elementDown(e)\n    },\n    elementDown(e) {\n      if (e instanceof MouseEvent && e.which !== 1) {\n        return\n      }\n\n      const target = e.target || e.srcElement\n\n      if (this.$el.contains(target)) {\n        if (this.onDragStart(e) === false) {\n          return\n        }\n\n        if (\n          (this.dragHandle &&\n            !matchesSelectorToParentElements(\n              target,\n              this.dragHandle,\n              this.$el\n            )) ||\n          (this.dragCancel &&\n            matchesSelectorToParentElements(target, this.dragCancel, this.$el))\n        ) {\n          this.dragging = false\n\n          return\n        }\n\n        if (!this.enabled) {\n          this.enabled = true\n\n          this.$emit('activated')\n          this.$emit('update:active', true)\n        }\n\n        if (this.draggable) {\n          this.dragEnable = true\n        }\n\n        this.mouseClickPosition.mouseX = e.touches\n          ? e.touches[0].pageX\n          : e.pageX\n        this.mouseClickPosition.mouseY = e.touches\n          ? e.touches[0].pageY\n          : e.pageY\n\n        this.mouseClickPosition.left = this.left\n        this.mouseClickPosition.right = this.right\n        this.mouseClickPosition.top = this.top\n        this.mouseClickPosition.bottom = this.bottom\n\n        if (this.parent) {\n          this.bounds = this.calcDragLimits()\n        }\n\n        addEvent(document.documentElement, eventsFor.move, this.move)\n        addEvent(document.documentElement, eventsFor.stop, this.handleUp)\n      }\n    },\n    calcDragLimits() {\n      return {\n        minLeft: mainCanvasOffsetX - this.zoomX,\n        maxLeft: this.parentWidth + mainCanvasOffsetX - this.zoomX - this.width,\n        minRight: this.right % this.grid[0],\n        maxRight:\n          Math.floor(\n            (this.parentWidth - this.width - this.right) / this.grid[0]\n          ) *\n            this.grid[0] +\n          this.right,\n        minTop: mainCanvasOffsetY - this.zoomY,\n        maxTop:\n          this.parentHeight + mainCanvasOffsetY - this.zoomY - this.height,\n        minBottom: this.bottom % this.grid[1],\n        maxBottom:\n          Math.floor(\n            (this.parentHeight - this.height - this.bottom) / this.grid[1]\n          ) *\n            this.grid[1] +\n          this.bottom,\n      }\n    },\n    deselect(e) {\n      const target = e.target || e.srcElement\n      const regex = new RegExp(this.className + '-([trmbl]{2})', '')\n\n      if (!this.$el.contains(target) && !regex.test(target.className)) {\n        if (this.enabled && !this.preventDeactivation) {\n          this.enabled = false\n\n          this.$emit('deactivated')\n          this.$emit('update:active', false)\n        }\n\n        removeEvent(document.documentElement, eventsFor.move, this.handleResize)\n      }\n\n      this.resetBoundsAndMouseState()\n    },\n    handleTouchDown(handle, e) {\n      eventsFor = events.touch\n\n      this.handleDown(handle, e)\n    },\n    handleDown(handle, e) {\n      if (e instanceof MouseEvent && e.which !== 1) {\n        return\n      }\n\n      if (this.onResizeStart(handle, e) === false) {\n        return\n      }\n\n      if (e.stopPropagation) e.stopPropagation()\n\n      // Here we avoid a dangerous recursion by faking\n      // corner handles as middle handles\n      this.originalHandle = handle\n      if (this.lockAspectRatio && !handle.includes('m')) {\n        this.handle = 'm' + handle.substring(1)\n      } else {\n        this.handle = handle\n      }\n\n      this.resizeEnable = true\n\n      this.mouseClickPosition.mouseX = e.touches ? e.touches[0].pageX : e.pageX\n      this.mouseClickPosition.mouseY = e.touches ? e.touches[0].pageY : e.pageY\n      this.mouseClickPosition.left = this.left\n      this.mouseClickPosition.right = this.right\n      this.mouseClickPosition.top = this.top\n      this.mouseClickPosition.bottom = this.bottom\n\n      this.bounds = this.calcResizeLimits()\n\n      addEvent(document.documentElement, eventsFor.move, this.handleResize)\n      addEvent(document.documentElement, eventsFor.stop, this.handleUp)\n    },\n    calcResizeLimits() {\n      const minW = this.minW\n      let minH = this.minH\n      let maxW = this.maxW\n      let maxH = this.maxH\n\n      const aspectFactor = this.aspectFactor\n      const [gridX, gridY] = this.grid\n      const width = this.width\n      const height = this.height\n      const left = this.left\n      const top = this.top\n      const right = this.right\n      const bottom = this.bottom\n\n      if (this.lockAspectRatio) {\n        minH = minW / aspectFactor + this.childHeaderHeight\n\n        // if (minW / minH > aspectFactor) {\n        //   minH = minW / aspectFactor\n        // } else {\n        //   minW = aspectFactor * minH\n        // }\n\n        if (maxW && maxH) {\n          maxW = Math.min(maxW, aspectFactor * maxH)\n          maxH = Math.min(maxH, maxW / aspectFactor)\n        } else if (maxW) {\n          maxH = maxW / aspectFactor\n        } else if (maxH) {\n          maxW = aspectFactor * maxH\n        }\n      }\n\n      maxW = maxW - (maxW % gridX)\n      maxH = maxH - (maxH % gridY)\n\n      const limits = {\n        minLeft: null,\n        maxLeft: null,\n        minTop: null,\n        maxTop: null,\n        minRight: null,\n        maxRight: null,\n        minBottom: null,\n        maxBottom: null,\n      }\n\n      if (this.parent) {\n        limits.minLeft = left % gridX\n        limits.maxLeft = left + Math.floor((width - minW) / gridX) * gridX\n        limits.minTop = top % gridY\n        limits.maxTop = top + Math.floor((height - minH) / gridY) * gridY\n        limits.minRight = right % gridX\n        limits.maxRight = right + Math.floor((width - minW) / gridX) * gridX\n        limits.minBottom = bottom % gridY\n        limits.maxBottom = bottom + Math.floor((height - minH) / gridY) * gridY\n\n        if (maxW) {\n          limits.minLeft = Math.max(\n            limits.minLeft,\n            this.parentWidth - right - maxW\n          )\n          limits.minRight = Math.max(\n            limits.minRight,\n            this.parentWidth - left - maxW\n          )\n        }\n\n        if (maxH) {\n          limits.minTop = Math.max(\n            limits.minTop,\n            this.parentHeight - bottom - maxH\n          )\n          limits.minBottom = Math.max(\n            limits.minBottom,\n            this.parentHeight - top - maxH\n          )\n        }\n\n        // if (this.lockAspectRatio) {\n        //   limits.minLeft = Math.max(limits.minLeft, left - top * aspectFactor)\n        //   limits.minTop = Math.max(limits.minTop, top - left / aspectFactor)\n        //   limits.minRight = Math.max(limits.minRight, right - bottom * aspectFactor)\n        //   limits.minBottom = Math.max(limits.minBottom, bottom - right / aspectFactor)\n        // }\n      } else {\n        limits.minLeft = null\n        limits.maxLeft = left + Math.floor((width - minW) / gridX) * gridX\n        limits.minTop = null\n        limits.maxTop = top + Math.floor((height - minH) / gridY) * gridY\n        limits.minRight = null\n        limits.maxRight = right + Math.floor((width - minW) / gridX) * gridX\n        limits.minBottom = null\n        limits.maxBottom = bottom + Math.floor((height - minH) / gridY) * gridY\n\n        if (maxW) {\n          limits.minLeft = -(right + maxW)\n          limits.minRight = -(left + maxW)\n        }\n\n        if (maxH) {\n          limits.minTop = -(bottom + maxH)\n          limits.minBottom = -(top + maxH)\n        }\n\n        if (this.lockAspectRatio && maxW && maxH) {\n          limits.minLeft = Math.min(limits.minLeft, -(right + maxW))\n          limits.minTop = Math.min(limits.minTop, -(maxH + bottom))\n          limits.minRight = Math.min(limits.minRight, -left - maxW)\n          limits.minBottom = Math.min(limits.minBottom, -top - maxH)\n        }\n      }\n\n      return limits\n    },\n    move(e) {\n      if (this.resizing) {\n        this.handleResize(e)\n      } else if (this.dragEnable) {\n        this.handleDrag(e)\n      }\n    },\n    handleDrag(e) {\n      const axis = this.axis\n      const grid = this.grid\n      const bounds = this.bounds\n      const mouseClickPosition = this.mouseClickPosition\n\n      const tmpDeltaX =\n        axis && axis !== 'y'\n          ? (mouseClickPosition.mouseX -\n              (e.touches ? e.touches[0].pageX : e.pageX)) /\n            this.zoomIndex\n          : 0\n      const tmpDeltaY =\n        axis && axis !== 'x'\n          ? (mouseClickPosition.mouseY -\n              (e.touches ? e.touches[0].pageY : e.pageY)) /\n            this.zoomIndex\n          : 0\n\n      const [deltaX, deltaY] = snapToGrid(\n        grid,\n        tmpDeltaX,\n        tmpDeltaY,\n        this.scale\n      )\n\n      const left = restrictToBounds(\n        mouseClickPosition.left - deltaX,\n        bounds.minLeft,\n        bounds.maxLeft\n      )\n      const top = restrictToBounds(\n        mouseClickPosition.top - deltaY,\n        bounds.minTop,\n        bounds.maxTop\n      )\n\n      if (this.onDrag(left, top) === false) {\n        return\n      }\n\n      const right = restrictToBounds(\n        mouseClickPosition.right + deltaX,\n        bounds.minRight,\n        bounds.maxRight\n      )\n      const bottom = restrictToBounds(\n        mouseClickPosition.bottom + deltaY,\n        bounds.minBottom,\n        bounds.maxBottom\n      )\n\n      this.left = left\n      this.top = top\n      this.right = right\n      this.bottom = bottom\n\n      this.$emit('dragging', this.left, this.top)\n\n      this.dragging = true\n    },\n    moveHorizontally(val) {\n      // should calculate with scale 1.\n      const [deltaX, _] = snapToGrid(this.grid, val, this.top, 1)\n\n      const left = restrictToBounds(\n        deltaX,\n        this.bounds.minLeft,\n        this.bounds.maxLeft\n      )\n\n      this.left = left\n      this.right = this.parentWidth - this.width - left\n    },\n    moveVertically(val) {\n      // should calculate with scale 1.\n      const [_, deltaY] = snapToGrid(this.grid, this.left, val, 1)\n\n      const top = restrictToBounds(\n        deltaY,\n        this.bounds.minTop,\n        this.bounds.maxTop\n      )\n\n      this.top = top\n      this.bottom = this.parentHeight - this.height - top\n    },\n    handleResize(e) {\n      let left = this.left\n      let top = this.top\n      let right = this.right\n      let bottom = this.bottom\n\n      const mouseClickPosition = this.mouseClickPosition\n      const aspectFactor = this.aspectFactor\n\n      const tmpDeltaX =\n        (mouseClickPosition.mouseX -\n          (e.touches ? e.touches[0].pageX : e.pageX)) /\n        this.zoomIndex\n      const tmpDeltaY =\n        (mouseClickPosition.mouseY -\n          (e.touches ? e.touches[0].pageY : e.pageY)) /\n        this.zoomIndex\n\n      if (!this.widthTouched && tmpDeltaX) {\n        this.widthTouched = true\n      }\n\n      if (!this.heightTouched && tmpDeltaY) {\n        this.heightTouched = true\n      }\n\n      const [deltaX, deltaY] = snapToGrid(\n        this.grid,\n        tmpDeltaX,\n        tmpDeltaY,\n        this.scale\n      )\n\n      if (this.handle.includes('b')) {\n        bottom = restrictToBounds(\n          mouseClickPosition.bottom + deltaY,\n          this.bounds.minBottom,\n          this.bounds.maxBottom\n        )\n\n        if (this.lockAspectRatio && this.resizingOnY) {\n          if (this.originalHandle === 'bm') {\n            right = this.right - (this.bottom - bottom) * (aspectFactor / 2)\n            left = this.left - (this.bottom - bottom) * (aspectFactor / 2)\n          } else if (this.originalHandle === 'br') {\n            right = this.right - (this.bottom - bottom) * aspectFactor\n          } else {\n            left = this.left - (this.bottom - bottom) * aspectFactor\n          }\n        }\n      } else if (this.handle.includes('t')) {\n        top = restrictToBounds(\n          mouseClickPosition.top - deltaY,\n          this.bounds.minTop,\n          this.bounds.maxTop\n        )\n\n        if (this.lockAspectRatio && this.resizingOnY) {\n          if (this.originalHandle === 'tm') {\n            left = this.left - (this.top - top) * (aspectFactor / 2)\n            right = this.right - (this.top - top) * (aspectFactor / 2)\n          } else if (this.originalHandle === 'tr') {\n            right = this.right - (this.top - top) * aspectFactor\n          } else {\n            left = this.left - (this.top - top) * aspectFactor\n          }\n        }\n      }\n\n      if (this.handle.includes('r')) {\n        right = restrictToBounds(\n          mouseClickPosition.right + deltaX,\n          this.bounds.minRight,\n          this.bounds.maxRight\n        )\n\n        if (this.lockAspectRatio && this.resizingOnX) {\n          if (this.originalHandle === 'mr') {\n            bottom = this.bottom - (this.right - right) / (aspectFactor * 2)\n            top = this.top - (this.right - right) / (aspectFactor * 2)\n          } else if (this.originalHandle === 'br') {\n            bottom = this.bottom - (this.right - right) / aspectFactor\n          } else {\n            top = this.top - (this.right - right) / aspectFactor\n          }\n        }\n      } else if (this.handle.includes('l')) {\n        left = restrictToBounds(\n          mouseClickPosition.left - deltaX,\n          this.bounds.minLeft,\n          this.bounds.maxLeft\n        )\n        if (this.lockAspectRatio && this.resizingOnX) {\n          if (this.originalHandle === 'ml') {\n            top = this.top - (this.left - left) / (aspectFactor * 2)\n            bottom = this.bottom - (this.left - left) / (aspectFactor * 2)\n          } else if (this.originalHandle === 'tl') {\n            top = this.top - (this.left - left) / aspectFactor\n          } else {\n            bottom = this.bottom - (this.left - left) / aspectFactor\n          }\n        }\n      }\n\n      const width = computeWidth(this.parentWidth, left, right)\n      const height = computeHeight(this.parentHeight, top, bottom)\n\n      if (this.onResize(this.handle, left, top, width, height) === false) {\n        return\n      }\n\n      this.left = left\n      this.top = top\n      this.right = right\n      this.bottom = bottom\n      this.width = width\n      this.height = height\n\n      this.$emit(\n        'resizing',\n        this.left,\n        this.top,\n        this.width,\n        this.height,\n        e.target.className\n      )\n\n      this.resizing = true\n    },\n    changeWidth(val) {\n      // should calculate with scale 1.\n      const [newWidth, _] = snapToGrid(this.grid, val, 0, 1)\n\n      const right = restrictToBounds(\n        this.parentWidth - newWidth - this.left,\n        this.bounds.minRight,\n        this.bounds.maxRight\n      )\n      let bottom = this.bottom\n\n      if (this.lockAspectRatio) {\n        bottom = this.bottom - (this.right - right) / this.aspectFactor\n      }\n\n      const width = computeWidth(this.parentWidth, this.left, right)\n      const height = computeHeight(this.parentHeight, this.top, bottom)\n\n      this.right = right\n      this.bottom = bottom\n      this.width = width\n      this.height = height\n    },\n    changeHeight(val) {\n      // should calculate with scale 1.\n      // const [_, newHeight] = snapToGrid(this.grid, 0, val, 1)\n\n      // let bottom = restrictToBounds(\n      //     (this.parentHeight - newHeight - this.top),\n      //     this.bounds.minBottom,\n      //     this.bounds.maxBottom\n      // )\n      // let right = this.right\n\n      // if (this.lockAspectRatio) {\n      //   right = this.right - (this.bottom - bottom) * this.aspectFactor\n      // }\n\n      // const width = computeWidth(this.parentWidth, this.left, right)\n      // const height = computeHeight(this.parentHeight, this.top, bottom)\n\n      // this.right = right\n      // this.bottom = bottom\n      this.height = val\n      this.bottom = this.parentHeight - this.height - this.top\n      this.aspectFactor = this.width / (this.height - this.childHeaderHeight)\n\n      // this.width = width\n\n      // console.log('valvalval', val, this.height, this.bottom, this.aspectFactor)\n    },\n    handleUp(e) {\n      this.handle = null\n\n      this.resetBoundsAndMouseState()\n\n      this.dragEnable = false\n      this.resizeEnable = false\n\n      if (this.resizing) {\n        this.resizing = false\n        this.$emit('resizestop', this.left, this.top, this.width, this.height)\n      }\n\n      if (this.dragging) {\n        this.dragging = false\n        this.$emit('dragstop', this.left, this.top)\n      }\n\n      removeEvent(document.documentElement, eventsFor.move, this.handleResize)\n    },\n  },\n}\n", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./VueDraggableResizable.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./VueDraggableResizable.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./VueDraggableResizable.vue?vue&type=template&id=3affd017&\"\nimport script from \"./VueDraggableResizable.vue?vue&type=script&lang=js&\"\nexport * from \"./VueDraggableResizable.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"c2bf3b4a\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??ref--3-oneOf-1-1!../../../node_modules/postcss-loader/src/index.js??ref--3-oneOf-1-2!./vue-draggable-resizable.css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../node_modules/vue-style-loader/lib/addStylesServer.js\").default(\"07a1f444\", content, true)", "import { isFunction } from './fns'\n\nexport function matchesSelectorToParentElements (el, selector, baseNode) {\n  let node = el\n\n  const matchesSelectorFunc = [\n    'matches',\n    'webkitMatchesSelector',\n    'mozMatchesSelector',\n    'msMatchesSelector',\n    'oMatchesSelector'\n  ].find(func => isFunction(node[func]))\n\n  if (!isFunction(node[matchesSelectorFunc])) return false\n\n  do {\n    if (node[matchesSelectorFunc](selector)) return true\n    if (node === baseNode) return false\n    node = node.parentNode\n  } while (node)\n\n  return false\n}\n\nexport function getComputedSize ($el) {\n  const style = window.getComputedStyle($el)\n\n  return [\n    parseFloat(style.getPropertyValue('width'), 10),\n    parseFloat(style.getPropertyValue('height'), 10)\n  ]\n}\n\nexport function addEvent (el, event, handler) {\n  if (!el) {\n    return\n  }\n  if (el.attachEvent) {\n    el.attachEvent('on' + event, handler)\n  } else if (el.addEventListener) {\n    el.addEventListener(event, handler, true)\n  } else {\n    el['on' + event] = handler\n  }\n}\n\nexport function removeEvent (el, event, handler) {\n  if (!el) {\n    return\n  }\n  if (el.detachEvent) {\n    el.detachEvent('on' + event, handler)\n  } else if (el.removeEventListener) {\n    el.removeEventListener(event, handler, true)\n  } else {\n    el['on' + event] = null\n  }\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAOA;AAEA;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA7BA;AACA;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;AAiBA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AAKA;AACA;AAAA;AACA;AAMA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AApEA;AAqEA;AACA;AACA;AACA;AACA;AAJA;AACA;AAIA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AAIA;AACA;AAAA;AAKA;AAKA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AAKA;AAIA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAFA;AAIA;AACA;AACA;AACA;AACA;AADA;AAHA;AAOA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAFA;AAQA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAMA;AACA;AACA;AAFA;AAKA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAHA;AASA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAQA;AACA;AACA;AAFA;AAKA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAHA;AAWA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArSA;AArIA;;ACrDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AClBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;ACrCA;AACA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AAIA;AACA;AACA;AACA;AAHA;AAMA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA9BA;AANA;;ACDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAMA;AAMA;AAOA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AAHA;AANA;AAaA;AACA;AACA;AACA;AACA;AAJA;AAOA;AACA;AACA;AACA;AACA;AAJA;AAOA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;AAWA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AACA;AAEA;AACA;AAPA;AASA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;AAWA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AArLA;AACA;AAyLA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA9BA;AAgCA;AACA;AAAA;AACA;AACA;AAGA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AASA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAIA;AACA;AAAA;AACA;AAIA;AACA;AAAA;AACA;AAGA;AACA;AA9DA;AA+DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAjFA;AACA;AAiFA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AAIA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AAUA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAUA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAEA;AACA;AAdA;AAqBA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AACA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAIA;AACA;AACA;AACA;AAIA;AAIA;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAMA;AAOA;AAOA;AAKA;AACA;AAKA;AACA;AACA;AACA;AACA;AAKA;AAMA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AACA;AACA;AAEA;AAMA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AAMA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAIA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AASA;AACA;AACA;AAAA;AACA;AACA;AAEA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AAAA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA7kBA;AAjbA;;AC/EA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAEA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAIA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}