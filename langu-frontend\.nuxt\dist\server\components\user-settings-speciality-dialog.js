exports.ids = [120];
exports.modules = {

/***/ 1166:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1226);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("16b2336a", content, true, context)
};

/***/ }),

/***/ 1225:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SpecialityDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1166);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SpecialityDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SpecialityDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SpecialityDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SpecialityDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1226:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".speciality-picker{height:calc(100vh - 10%)}.speciality-picker .dialog-content,.speciality-picker .v-card{height:100%}@media only screen and (max-width:1439px){.speciality-picker .v-card{padding:32px 28px}}.speciality-picker .dialog-content{position:relative}.speciality-picker-content{padding-bottom:88px}.speciality-picker-content>.row{height:100%}.speciality-picker-content>.row>.col,.speciality-picker-content>.row>.col>.column{height:inherit}.speciality-picker-title{font-size:20px}.speciality-picker-text{color:var(--v-grey-base);letter-spacing:.3px}.speciality-picker-text ul{padding-left:0;list-style-type:none}.speciality-picker-text ul>li:not(:last-child){margin-bottom:4px}.speciality-picker-bottom{position:absolute;left:0;bottom:0;width:100%}.speciality-picker .column{padding:20px 15px;border:1px solid #00a500;border-radius:24px}.speciality-picker .column-helper{height:100%;overflow-y:auto;overflow-x:hidden}.speciality-picker .column-helper>div,.speciality-picker .column-helper>div>div{height:100%}.speciality-picker .column .list-group-item{cursor:move}.speciality-picker .column .list-group-item:not(:last-child){margin-bottom:8px}.speciality-picker .column .list-group-item.highest-priority{color:var(--v-success-base)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1273:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/SpecialityDialog.vue?vue&type=template&id=437fbe4b&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({directives:[{name:"resize",rawName:"v-resize",value:(_vm.onResize),expression:"onResize"}],attrs:{"dialog":_vm.isShownSpecialitiesDialog,"max-width":"820","custom-class":"speciality-picker"}},_vm.$listeners),[_c('div',{ref:"header",staticClass:"header"},[_c('div',{staticClass:"speciality-picker-title font-weight-medium"},[_vm._v("\n      "+_vm._s(_vm.$t('manage_specialties'))+":\n    ")]),_vm._v(" "),_c('div',{staticClass:"speciality-picker-text body-2 mt-2"},[_c('ul',[_c('li',[_vm._v("1. "+_vm._s(_vm.$t('drag_your_teaching_specialities')))]),_vm._v(" "),_c('li',[_vm._v("\n          2.\n          "+_vm._s(_vm.$t(
              'top_specialities_will_be_featured_on_teacher_search_results_page'
            ))+"\n        ")])])]),_vm._v(" "),_c('v-row',{staticClass:"my-0"},[_c('v-col',{staticClass:"col-6 py-0"},[_c('div',{staticClass:"text--gradient text-center subtitle-2 font-weight-medium mt-3 mb-1"},[_vm._v("\n          "+_vm._s(_vm.$t('available_specialties'))+":\n        ")])]),_vm._v(" "),_c('v-col',{staticClass:"col-6 py-0"},[_c('div',{staticClass:"text--gradient text-center subtitle-2 font-weight-medium mt-3 mb-1"},[_vm._v("\n          "+_vm._s(_vm.$t('selected_specialties'))+":\n        ")])])],1)],1),_vm._v(" "),_c('div',{staticClass:"speciality-picker-content",style:({ height: _vm.contentElHeight })},[_c('v-row',{staticClass:"my-0"},[_c('v-col',{staticClass:"col-6 py-0"},[_c('div',{staticClass:"column"},[_c('div',{staticClass:"column-helper l-scroll l-scroll--dark-grey l-scroll--large"},[_c('div',{staticClass:"column-content"},[_c('draggable',{staticClass:"list-group",attrs:{"group":"specialities"},on:{"end":_vm.onEnd},model:{value:(_vm.availableSpecializations),callback:function ($$v) {_vm.availableSpecializations=$$v},expression:"availableSpecializations"}},_vm._l((_vm.availableSpecializations),function(element){return _c('div',{key:element.name,staticClass:"list-group-item body-1"},[_vm._v("\n                  "+_vm._s(element.name)+"\n                ")])}),0)],1)])])]),_vm._v(" "),_c('v-col',{staticClass:"col-6 py-0"},[_c('div',{staticClass:"column l-scroll"},[_c('div',{staticClass:"column-helper l-scroll--dark-grey l-scroll--large"},[_c('div',{staticClass:"column-content"},[_c('draggable',{staticClass:"list-group",attrs:{"group":"specialities"},model:{value:(_vm.teacherSpecialities),callback:function ($$v) {_vm.teacherSpecialities=$$v},expression:"teacherSpecialities"}},_vm._l((_vm.teacherSpecialities),function(element,idx){return _c('div',{key:element.name,class:[
                    'list-group-item body-1',
                    { 'highest-priority': idx < 3 } ]},[_vm._v("\n                  "+_vm._s(element.name)+"\n                ")])}),0)],1)])])])],1),_vm._v(" "),_c('div',{staticClass:"speciality-picker-bottom d-flex justify-end"},[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"primary"},on:{"click":_vm.submitData}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"18","height":"18","viewBox":"0 0 18 18"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#save-icon")}})]),_vm._v("\n        "+_vm._s(_vm.$t('save_changes'))+"\n      ")])],1)],1)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/SpecialityDialog.vue?vue&type=template&id=437fbe4b&

// EXTERNAL MODULE: external "vuedraggable"
var external_vuedraggable_ = __webpack_require__(865);
var external_vuedraggable_default = /*#__PURE__*/__webpack_require__.n(external_vuedraggable_);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/SpecialityDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var SpecialityDialogvue_type_script_lang_js_ = ({
  name: 'SpecialityDialog',
  components: {
    draggable: external_vuedraggable_default.a
  },
  props: {
    isShownSpecialitiesDialog: {
      type: Boolean,
      required: true
    }
  },

  data() {
    return {
      contentElHeight: 'auto'
    };
  },

  computed: {
    availableSpecializations: {
      get() {
        return this.$store.getters['settings/availableSpecializations'];
      },

      set(value) {
        this.$store.commit('settings/UPDATE_AVAILABLE_SPECIALIZATIONS', value);
      }

    },
    teacherSpecialities: {
      get() {
        return this.$store.getters['settings/teacherSpecialities'];
      },

      set(value) {
        this.$store.commit('settings/UPDATE_TEACHER_SPECIALITIES', value);
      }

    }
  },
  watch: {
    isShownSpecialitiesDialog(newValue, oldValue) {
      if (newValue) {
        this.$nextTick(() => {
          window.setTimeout(() => this.setContentElHeight());
        });
      }
    }

  },
  methods: {
    onEnd(e) {
      if (this.teacherSpecialities.length > 8) {
        const _availableSpecializations = [...this.availableSpecializations];
        const _teacherSpecialities = [...this.teacherSpecialities];

        _availableSpecializations.splice(e.oldIndex, 0, _teacherSpecialities[e.newIndex]);

        _teacherSpecialities.splice(e.newIndex, 1);

        this.$store.commit('settings/UPDATE_AVAILABLE_SPECIALIZATIONS', _availableSpecializations);
        this.$store.commit('settings/UPDATE_TEACHER_SPECIALITIES', _teacherSpecialities);
      }
    },

    setContentElHeight() {
      var _this$$refs$header$cl, _this$$refs$header;

      this.contentElHeight = `calc(100% - ${(_this$$refs$header$cl = (_this$$refs$header = this.$refs.header) === null || _this$$refs$header === void 0 ? void 0 : _this$$refs$header.clientHeight) !== null && _this$$refs$header$cl !== void 0 ? _this$$refs$header$cl : 0}px)`;
    },

    onResize() {
      this.setContentElHeight();
    },

    submitData() {
      this.$store.dispatch('settings/updateSpecialities').then(() => this.$emit('close-dialog'));
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/SpecialityDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_SpecialityDialogvue_type_script_lang_js_ = (SpecialityDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installDirectives.js
var installDirectives = __webpack_require__(430);
var installDirectives_default = /*#__PURE__*/__webpack_require__.n(installDirectives);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/resize/index.js
var resize = __webpack_require__(32);

// CONCATENATED MODULE: ./components/user-settings/SpecialityDialog.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1225)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_SpecialityDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "3bb9d92c"
  
)

/* harmony default export */ var SpecialityDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LDialog: __webpack_require__(28).default})


/* vuetify-loader */




installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VRow: VRow["a" /* default */]})


/* vuetify-loader */


installDirectives_default()(component, {Resize: resize["a" /* default */]})


/***/ })

};;
//# sourceMappingURL=user-settings-speciality-dialog.js.map