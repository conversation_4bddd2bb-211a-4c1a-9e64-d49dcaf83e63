{"version": 3, "file": "pages/meet/index.js", "sources": ["webpack:///./helpers/whereby-api.js", "webpack:///./pages/meet/index.vue", "webpack:///./pages/meet/index.vue?0a28", "webpack:///./pages/meet/index.vue?5791", "webpack:///./pages/meet/index.vue?861f", "webpack:///./pages/meet/index.vue?7d14", "webpack:///./pages/meet/index.vue?c032", "webpack:///./pages/meet/index.vue?6d0f", "webpack:///../../../src/components/VCard/index.ts", "webpack:///../../../src/components/VChip/VChip.ts", "webpack:///./node_modules/vuetify/src/components/VChip/VChip.sass?005d", "webpack:///./node_modules/vuetify/src/components/VChip/VChip.sass"], "sourcesContent": ["/**\n * Whereby API helper functions for classroom integration\n */\n\n/**\n * Create a new Whereby room for a classroom session\n * @param {Object} params - Room creation parameters\n * @param {string} params.lessonId - The lesson ID\n * @param {string} params.teacherId - The teacher ID\n * @param {string} params.studentId - The student ID\n * @param {boolean} params.isRecurring - Whether this is a recurring lesson\n * @returns {Promise<Object>} Room information\n */\nexport async function createWherebyRoom({\n  lessonId,\n  teacherId,\n  studentId,\n  isRecurring = false,\n}) {\n  try {\n    const response = await fetch('/_nuxt/api/whereby/create-room', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        lessonId,\n        teacherId,\n        studentId,\n        isRecurring,\n      }),\n    })\n\n    if (!response.ok) {\n      const errorData = await response.json()\n      throw new Error(errorData.error || 'Failed to create room')\n    }\n\n    const data = await response.json()\n    return data.room\n  } catch (error) {\n    console.error('Error creating Whereby room:', error)\n    throw error\n  }\n}\n\n/**\n * Get room information for a lesson\n * @param {string} lessonId - The lesson ID\n * @returns {Promise<Object>} Room information\n */\nexport async function getWherebyRoom(lessonId) {\n  try {\n    const response = await fetch(`/api/whereby/room/${lessonId}`)\n\n    if (!response.ok) {\n      const errorData = await response.json()\n      throw new Error(errorData.error || 'Failed to get room info')\n    }\n\n    const data = await response.json()\n    return data\n  } catch (error) {\n    console.error('Error fetching Whereby room:', error)\n    throw error\n  }\n}\n\n/**\n * End a Whereby room\n * @param {string} meetingId - The Whereby meeting ID\n * @returns {Promise<Object>} Success response\n */\nexport async function endWherebyRoom(meetingId) {\n  try {\n    const response = await fetch(`/api/whereby/room/${meetingId}`, {\n      method: 'DELETE',\n    })\n\n    if (!response.ok) {\n      const errorData = await response.json()\n      throw new Error(errorData.error || 'Failed to end room')\n    }\n\n    const data = await response.json()\n    return data\n  } catch (error) {\n    console.error('Error ending Whereby room:', error)\n    throw error\n  }\n}\n\n/**\n * Generate room URLs with parameters for embedding\n * @param {Object} room - Room information from createWherebyRoom\n * @param {string} role - 'host' or 'participant'\n * @param {Object} options - Additional options\n * @returns {Object} URLs and parameters\n */\nexport function generateWherebyUrls(room, role = 'participant', options = {}) {\n  const {\n    displayName = 'User',\n    audio = 'on',\n    video = 'on',\n    chat = 'off', // Explicitly disabled\n    people = 'off', // Explicitly disabled\n    screenshare = 'on',\n    reactions = 'on',\n    handRaise = 'on',\n    leaveButton = 'off', // Explicitly disabled\n    background = 'on',\n    recording = 'off', // Explicitly disabled\n    breakoutRooms = 'on',\n    whiteboard = 'on',\n    minimal = 'false',\n  } = options\n\n  // Use appropriate URL based on role\n  const baseUrl = role === 'host' ? room.hostRoomUrl : room.roomUrl\n\n  // Build embed parameters - explicitly disable requested features\n  const embedParams = new URLSearchParams({\n    displayName,\n    audio,\n    video,\n    chat,\n    people,\n    screenshare,\n    reactions,\n    handRaise,\n    leaveButton,\n    background,\n    recording,\n    breakoutRooms,\n    whiteboard,\n    minimal,\n  })\n\n  // Determine if we need to add & or ? for parameters\n  const separator = baseUrl.includes('?') ? '&' : '?'\n  const fullUrl = `${baseUrl}${separator}${embedParams.toString()}`\n\n  return {\n    baseUrl,\n    fullUrl,\n    embedParams: embedParams.toString(),\n    role,\n    meetingId: room.meetingId,\n  }\n}\n\n/**\n * Open Whereby room in a new window\n * @param {Object} room - Room information\n * @param {string} role - 'host' or 'participant'\n * @param {Object} options - Display options\n * @returns {Window|null} Reference to opened window\n */\nexport function openWherebyWindow(room, role = 'participant', options = {}) {\n  const {\n    displayName = 'User',\n    windowFeatures = 'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no',\n  } = options\n\n  try {\n    const { fullUrl } = generateWherebyUrls(room, role, {\n      displayName,\n      ...options,\n    })\n\n    const windowName = `whereby-${room.lessonId}-${role}`\n    const newWindow = window.open(fullUrl, windowName, windowFeatures)\n\n    if (newWindow) {\n      newWindow.focus()\n      return newWindow\n    } else {\n      throw new Error('Failed to open window - popup blocked?')\n    }\n  } catch (error) {\n    console.error('Error opening Whereby window:', error)\n    throw error\n  }\n}\n\n/**\n * Check if a room is still active\n * @param {Object} room - Room information\n * @returns {boolean} Whether the room is still active\n */\nexport function isRoomActive(room) {\n  if (!room || !room.endDate) return false\n\n  const now = new Date()\n  const endDate = new Date(room.endDate)\n\n  return now < endDate\n}\n\n/**\n * Get time remaining for a room\n * @param {Object} room - Room information\n * @returns {number} Minutes remaining (0 if expired)\n */\nexport function getRoomTimeRemaining(room) {\n  if (!room || !room.endDate) return 0\n\n  const now = new Date()\n  const endDate = new Date(room.endDate)\n\n  if (now >= endDate) return 0\n\n  const diffMs = endDate - now\n  return Math.floor(diffMs / (1000 * 60)) // Convert to minutes\n}\n", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { createWherebyRoom } from '~/helpers/whereby-api'\n\nexport default {\n  name: 'MeetPage',\n  data() {\n    return {\n      currentProvider: 'whereby',\n      isConnected: false,\n      isVideoEnabled: true,\n      isMuted: false,\n      roomName: 'test-meet-room',\n      wherebyClient: null,\n      localStreamContainer: null,\n      currentRole: 'participant', // 'host' or 'participant'\n      wherebyRoom: null, // Store the created room information\n      isCreatingRoom: false, // Loading state for room creation\n    }\n  },\n  head() {\n    return {\n      title: 'Video Call Test - Langu',\n      meta: [\n        {\n          hid: 'description',\n          name: 'description',\n          content: 'Test video call functionality with Whereby integration',\n        },\n      ],\n    }\n  },\n  mounted() {\n    this.$nextTick(() => {\n      this.localStreamContainer = document.getElementById(\n        'whereby-video-container'\n      )\n      if (this.currentProvider === 'whereby') {\n        this.initializeWhereby()\n      }\n    })\n\n    window.addEventListener('beforeunload', this.cleanup)\n  },\n  beforeDestroy() {\n    this.cleanup()\n  },\n  methods: {\n    switchProvider(provider) {\n      this.cleanup()\n      this.currentProvider = provider\n      this.isConnected = false\n\n      if (provider === 'whereby') {\n        this.$nextTick(() => {\n          this.localStreamContainer = document.getElementById(\n            'whereby-video-container'\n          )\n          this.initializeWhereby()\n        })\n      }\n    },\n    initializeWhereby() {\n      try {\n        // For CSP compliance, we need to open Whereby in a new window instead of iframe\n        // The CSP error indicates that Whereby doesn't allow embedding from localhost\n\n        // Create embedded iframe interface like existing Whereby component\n        const container = this.localStreamContainer\n        if (container) {\n          // Create role selection interface first\n          container.innerHTML = `\n            <div style=\"display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center;\">\n              <div style=\"margin-bottom: 20px;\">\n                <h3 style=\"color: #333; margin-bottom: 10px;\">Whereby Video Call</h3>\n                <p style=\"color: #666; margin-bottom: 20px;\">Choose your role to join the embedded video room</p>\n              </div>\n\n              <div style=\"display: flex; gap: 10px; margin-bottom: 15px;\">\n                <button\n                  id=\"whereby-host-btn\"\n                  style=\"\n                    background: #28a745;\n                    color: white;\n                    border: none;\n                    padding: 12px 20px;\n                    border-radius: 6px;\n                    font-size: 14px;\n                    cursor: pointer;\n                    font-weight: 600;\n                  \"\n                  onmouseover=\"this.style.background='#218838'\"\n                  onmouseout=\"this.style.background='#28a745'\"\n                >\n                  Join as Host\n                </button>\n\n                <button\n                  id=\"whereby-participant-btn\"\n                  style=\"\n                    background: #5E72E4;\n                    color: white;\n                    border: none;\n                    padding: 12px 20px;\n                    border-radius: 6px;\n                    font-size: 14px;\n                    cursor: pointer;\n                    font-weight: 600;\n                  \"\n                  onmouseover=\"this.style.background='#4C63D2'\"\n                  onmouseout=\"this.style.background='#5E72E4'\"\n                >\n                  Join as Participant\n                </button>\n              </div>\n\n              <p style=\"color: #888; font-size: 14px;\">\n                ${\n                  process.env.NUXT_ENV_URL === 'https://langu.io'\n                    ? 'Using static room for staging'\n                    : 'Dynamic room will be created'\n                }\n              </p>\n              <p style=\"color: #999; font-size: 12px;\">Embedded interface with all paid features</p>\n            </div>\n          `\n\n          // Add click handlers for role selection\n          const hostBtn = container.querySelector('#whereby-host-btn')\n          const participantBtn = container.querySelector(\n            '#whereby-participant-btn'\n          )\n\n          if (hostBtn && participantBtn) {\n            // Host button click handler\n            hostBtn.addEventListener('click', () => {\n              this.currentRole = 'host'\n              this.createEmbeddedWhereby('host')\n            })\n\n            // Participant button click handler\n            participantBtn.addEventListener('click', () => {\n              this.currentRole = 'participant'\n              this.createEmbeddedWhereby('participant')\n            })\n          }\n        }\n      } catch (error) {\n        // eslint-disable-next-line no-console\n        console.error('Failed to initialize Whereby:', error)\n        this.isConnected = false\n      }\n    },\n\n    async createEmbeddedWhereby(role) {\n      try {\n        const container = this.localStreamContainer\n        if (!container) return\n\n        // Show loading state\n        this.isCreatingRoom = true\n        container.innerHTML = `\n          <div style=\"display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;\">\n            <div style=\"margin-bottom: 20px;\">\n              <h3 style=\"color: #333; margin-bottom: 10px;\">Creating Whereby Room...</h3>\n              <p style=\"color: #666; margin-bottom: 20px;\">Please wait while we set up your video call</p>\n            </div>\n            <div style=\"width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #5E72E4; border-radius: 50%; animation: spin 1s linear infinite;\"></div>\n            <style>\n              @keyframes spin {\n                0% { transform: rotate(0deg); }\n                100% { transform: rotate(360deg); }\n              }\n            </style>\n          </div>\n        `\n\n        // Create or get existing room\n        if (!this.wherebyRoom) {\n          // Use static room for staging environment\n          if (process.env.NUXT_ENV_URL === 'https://langu.io') {\n            // Static room for staging - comment out dynamic creation for now\n            this.wherebyRoom = {\n              meetingId: '105680708',\n              hostRoomUrl:\n                'https://langu.whereby.com/rwrre2ae7c7d2-8906-4ceb-90fd-f4571e5e75e6?roomKey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************.IwPYfeEJnGNcIMP3kaWv93artRQogQqTlWUxPW1egoE',\n              roomUrl:\n                'https://langu.whereby.com/rwrre2ae7c7d2-8906-4ceb-90fd-f4571e5e75e6',\n              startDate: new Date().toISOString(),\n              endDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),\n              createdAt: new Date().toISOString(),\n            }\n            console.log('Using static Whereby room for staging')\n          } else {\n            // Dynamic room creation for other environments\n            this.wherebyRoom = await createWherebyRoom({\n              lessonId: `meet-test-${Date.now()}`, // Generate unique ID for meet page\n              teacherId: 'meet-teacher',\n              studentId: 'meet-student',\n              isRecurring: false,\n            })\n          }\n        }\n\n        // Hide loading state\n        this.isCreatingRoom = false\n\n        // Determine the URL based on role\n        const roomUrl =\n          role === 'host'\n            ? this.wherebyRoom.hostRoomUrl\n            : this.wherebyRoom.roomUrl\n\n        // Build embed parameters with all paid features enabled\n        const embedParams = new URLSearchParams({\n          embed: '',\n          displayName: role === 'host' ? 'Test Host' : 'Test Participant',\n          audio: !this.isMuted ? 'on' : 'off',\n          video: this.isVideoEnabled ? 'on' : 'off',\n          // Enable all paid features\n          chat: 'on', // Enable chat\n          people: 'on', // Enable participants panel\n          screenshare: 'on', // Enable screen sharing\n          reactions: 'on', // Enable reactions\n          handRaise: 'on', // Enable hand raising\n          leaveButton: 'on', // Show leave button\n          background: 'on', // Enable background effects\n          recording: 'on', // Enable recording (if available)\n          breakoutRooms: 'on', // Enable breakout rooms (if available)\n          whiteboard: 'on', // Enable whiteboard (if available)\n          minimal: 'false', // Full interface\n          // Direct entry without knocking\n          skipMediaPermissionPrompt: 'true',\n          autoJoin: 'true',\n        })\n\n        // Create iframe with all features\n        const iframe = document.createElement('iframe')\n\n        // Determine if we need to add & or ? for parameters\n        const separator = roomUrl.includes('?') ? '&' : '?'\n        iframe.src = `${roomUrl}${separator}${embedParams.toString()}`\n\n        iframe.style.width = '100%'\n        iframe.style.height = '100%'\n        iframe.style.border = 'none'\n        iframe.style.borderRadius = '8px'\n        iframe.allow =\n          'camera; microphone; fullscreen; display-capture; autoplay'\n        iframe.allowFullscreen = true\n\n        // Clear container and add iframe\n        container.innerHTML = ''\n        container.appendChild(iframe)\n\n        // Mark as connected\n        this.isConnected = true\n\n        // eslint-disable-next-line no-console\n        console.log('Whereby room created for meet page:', this.wherebyRoom)\n      } catch (error) {\n        // eslint-disable-next-line no-console\n        console.error('Failed to create embedded Whereby:', error)\n\n        this.isCreatingRoom = false\n        this.isConnected = false\n\n        // Show error message\n        if (this.localStreamContainer) {\n          this.localStreamContainer.innerHTML = `\n            <div style=\"display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;\">\n              <div style=\"margin-bottom: 20px;\">\n                <h3 style=\"color: #d32f2f; margin-bottom: 10px;\">Failed to Create Room</h3>\n                <p style=\"color: #666; margin-bottom: 20px;\">Unable to create Whereby room. Please try again.</p>\n              </div>\n              <button\n                onclick=\"location.reload()\"\n                style=\"\n                  background: #5E72E4;\n                  color: white;\n                  border: none;\n                  padding: 12px 20px;\n                  border-radius: 6px;\n                  font-size: 14px;\n                  cursor: pointer;\n                  font-weight: 600;\n                \"\n              >\n                Retry\n              </button>\n            </div>\n          `\n        }\n      }\n    },\n\n    toggleScreenShare() {\n      this.isScreenShareEnabled = !this.isScreenShareEnabled\n      // eslint-disable-next-line no-console\n      console.log('Screen share toggled:', this.isScreenShareEnabled)\n      // Note: Actual screen sharing is handled by Whereby iframe\n    },\n\n    toggleHandRaise() {\n      this.isHandRaised = !this.isHandRaised\n      // eslint-disable-next-line no-console\n      console.log('Hand raise toggled:', this.isHandRaised)\n      // Note: Actual hand raising is handled by Whereby iframe\n    },\n\n    toggleChat() {\n      this.isChatEnabled = !this.isChatEnabled\n      // eslint-disable-next-line no-console\n      console.log('Chat toggled:', this.isChatEnabled)\n      // Note: Actual chat toggle is handled by Whereby iframe\n    },\n\n    toggleParticipants() {\n      this.isParticipantsEnabled = !this.isParticipantsEnabled\n      // eslint-disable-next-line no-console\n      console.log('Participants panel toggled:', this.isParticipantsEnabled)\n      // Note: Actual participants panel is handled by Whereby iframe\n    },\n\n    sendReaction(emoji) {\n      this.reactions.push({\n        emoji,\n        timestamp: Date.now(),\n      })\n      // eslint-disable-next-line no-console\n      console.log('Reaction sent:', emoji)\n      // Note: Actual reactions are handled by Whereby iframe\n\n      // Remove reaction after 3 seconds\n      setTimeout(() => {\n        this.reactions = this.reactions.filter(\n          (r) => Date.now() - r.timestamp < 3000\n        )\n      }, 3000)\n    },\n\n    toggleVideo() {\n      this.isVideoEnabled = !this.isVideoEnabled\n      // Note: With iframe approach, video controls are handled within Whereby interface\n      // eslint-disable-next-line no-console\n      console.log('Video toggled:', this.isVideoEnabled)\n    },\n    toggleAudio() {\n      this.isMuted = !this.isMuted\n      // Note: With iframe approach, audio controls are handled within Whereby interface\n      // eslint-disable-next-line no-console\n      console.log('Audio toggled:', !this.isMuted)\n    },\n    toggleFullScreen() {\n      if (this.localStreamContainer) {\n        if (document.fullscreenElement) {\n          document.exitFullscreen()\n        } else {\n          this.localStreamContainer.requestFullscreen()\n        }\n      }\n    },\n    cleanup() {\n      if (this.localStreamContainer) {\n        this.localStreamContainer.innerHTML = ''\n      }\n      this.isConnected = false\n    },\n  },\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=510802b6&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"cdff56e4\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=510802b6&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".meet-page[data-v-510802b6]{min-height:100vh;background:#f5f5f5}.meet-header[data-v-510802b6]{text-align:center}.meet-video-card .video-container[data-v-510802b6]{background:#000;border-radius:8px;overflow:hidden}.meet-video-card .whereby-video-container[data-v-510802b6]{background:#000}.video-provider-buttons .v-btn[data-v-510802b6]{min-width:auto}.video-controls .v-btn[data-v-510802b6]{color:#fff!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"meet-page\"},[_c('v-container',{staticClass:\"pa-4\",attrs:{\"fluid\":\"\"}},[_c('v-row',[_c('v-col',{attrs:{\"cols\":\"12\"}},[_c('div',{staticClass:\"meet-header mb-4\"},[_c('h1',{staticClass:\"text-h4 font-weight-bold mb-2\"},[_vm._v(\"Video Call Test\")]),_vm._v(\" \"),_c('p',{staticClass:\"text-subtitle-1 grey--text\"},[_vm._v(\"\\n            Test the Whereby video call integration\\n          \")])])])],1),_vm._v(\" \"),_c('v-row',[_c('v-col',{attrs:{\"cols\":\"12\",\"md\":\"8\"}},[_c('v-card',{staticClass:\"meet-video-card\",attrs:{\"elevation\":\"2\"}},[_c('v-card-title',{staticClass:\"d-flex align-center justify-space-between\"},[_c('span',[_vm._v(\"Video Call\")]),_vm._v(\" \"),_c('div',{staticClass:\"video-provider-buttons\"},[_c('v-btn',{class:[{ primary: _vm.currentProvider === 'whereby' }],attrs:{\"small\":\"\"},on:{\"click\":function($event){return _vm.switchProvider('whereby')}}},[_vm._v(\"\\n                C - Whereby\\n              \")])],1)]),_vm._v(\" \"),_c('v-card-text',{staticClass:\"pa-0\"},[_c('div',{staticClass:\"video-container\",staticStyle:{\"height\":\"500px\",\"position\":\"relative\"}},[(_vm.currentProvider === 'whereby')?_c('div',{staticClass:\"whereby-video-container\",staticStyle:{\"width\":\"100%\",\"height\":\"100%\"},attrs:{\"id\":\"whereby-video-container\"}}):_vm._e(),_vm._v(\" \"),(_vm.currentProvider === 'whereby')?_c('div',{staticClass:\"video-controls\",staticStyle:{\"position\":\"absolute\",\"bottom\":\"16px\",\"left\":\"16px\",\"right\":\"16px\"}},[_c('v-card',{staticClass:\"pa-2\",staticStyle:{\"background\":\"rgba(0, 0, 0, 0.8)\"}},[_c('div',{staticClass:\"d-flex align-center justify-space-between\"},[_c('div',{staticClass:\"d-flex align-center\"},[_c('v-btn',{attrs:{\"color\":_vm.isVideoEnabled ? 'success' : 'error',\"icon\":\"\",\"small\":\"\"},on:{\"click\":_vm.toggleVideo}},[_c('v-icon',[_vm._v(_vm._s(_vm.isVideoEnabled ? 'mdi-video' : 'mdi-video-off'))])],1),_vm._v(\" \"),_c('v-btn',{staticClass:\"mx-1\",attrs:{\"color\":!_vm.isMuted ? 'success' : 'error',\"icon\":\"\",\"small\":\"\"},on:{\"click\":_vm.toggleAudio}},[_c('v-icon',[_vm._v(_vm._s(!_vm.isMuted ? 'mdi-microphone' : 'mdi-microphone-off'))])],1),_vm._v(\" \"),_c('v-btn',{staticClass:\"mx-1\",attrs:{\"color\":_vm.isScreenShareEnabled ? 'primary' : 'default',\"icon\":\"\",\"small\":\"\"},on:{\"click\":_vm.toggleScreenShare}},[_c('v-icon',[_vm._v(\"mdi-monitor-share\")])],1),_vm._v(\" \"),_c('v-btn',{staticClass:\"mx-1\",attrs:{\"color\":_vm.isHandRaised ? 'warning' : 'default',\"icon\":\"\",\"small\":\"\"},on:{\"click\":_vm.toggleHandRaise}},[_c('v-icon',[_vm._v(\"mdi-hand-back-right\")])],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"d-flex align-center\"},[_c('v-btn',{staticClass:\"mx-1\",attrs:{\"icon\":\"\",\"small\":\"\"},on:{\"click\":function($event){return _vm.sendReaction('👍')}}},[_c('span',{staticStyle:{\"font-size\":\"16px\"}},[_vm._v(\"👍\")])]),_vm._v(\" \"),_c('v-btn',{staticClass:\"mx-1\",attrs:{\"icon\":\"\",\"small\":\"\"},on:{\"click\":function($event){return _vm.sendReaction('👏')}}},[_c('span',{staticStyle:{\"font-size\":\"16px\"}},[_vm._v(\"👏\")])]),_vm._v(\" \"),_c('v-btn',{staticClass:\"mx-1\",attrs:{\"icon\":\"\",\"small\":\"\"},on:{\"click\":function($event){return _vm.sendReaction('❤️')}}},[_c('span',{staticStyle:{\"font-size\":\"16px\"}},[_vm._v(\"❤️\")])]),_vm._v(\" \"),_c('v-btn',{staticClass:\"mx-1\",attrs:{\"icon\":\"\",\"small\":\"\"},on:{\"click\":function($event){return _vm.sendReaction('😂')}}},[_c('span',{staticStyle:{\"font-size\":\"16px\"}},[_vm._v(\"😂\")])])],1),_vm._v(\" \"),_c('div',{staticClass:\"d-flex align-center\"},[_c('v-btn',{staticClass:\"mx-1\",attrs:{\"color\":_vm.isChatEnabled ? 'primary' : 'default',\"icon\":\"\",\"small\":\"\"},on:{\"click\":_vm.toggleChat}},[_c('v-icon',[_vm._v(\"mdi-chat\")])],1),_vm._v(\" \"),_c('v-btn',{staticClass:\"mx-1\",attrs:{\"color\":_vm.isParticipantsEnabled ? 'primary' : 'default',\"icon\":\"\",\"small\":\"\"},on:{\"click\":_vm.toggleParticipants}},[_c('v-icon',[_vm._v(\"mdi-account-group\")])],1),_vm._v(\" \"),_c('v-btn',{staticClass:\"mx-1\",attrs:{\"color\":\"primary\",\"icon\":\"\",\"small\":\"\"},on:{\"click\":_vm.toggleFullScreen}},[_c('v-icon',[_vm._v(\"mdi-fullscreen\")])],1),_vm._v(\" \"),_c('v-chip',{staticClass:\"ml-2\",attrs:{\"color\":_vm.currentRole === 'host' ? 'success' : 'primary',\"small\":\"\",\"text-color\":\"white\"}},[_vm._v(\"\\n                        \"+_vm._s(_vm.currentRole === 'host' ? 'Host' : 'Participant')+\"\\n                      \")])],1)])])],1):_vm._e()])])],1)],1),_vm._v(\" \"),_c('v-col',{attrs:{\"cols\":\"12\",\"md\":\"4\"}},[_c('v-card',{attrs:{\"elevation\":\"2\"}},[_c('v-card-title',[_vm._v(\"Connection Info\")]),_vm._v(\" \"),_c('v-card-text',[_c('div',{staticClass:\"mb-3\"},[_c('strong',[_vm._v(\"Provider:\")]),_vm._v(\" \"+_vm._s(_vm.currentProvider.toUpperCase())+\"\\n            \")]),_vm._v(\" \"),_c('div',{staticClass:\"mb-3\"},[_c('strong',[_vm._v(\"Status:\")]),_vm._v(\" \"),_c('v-chip',{attrs:{\"color\":_vm.isConnected ? 'success' : 'warning',\"small\":\"\",\"text-color\":\"white\"}},[_vm._v(\"\\n                \"+_vm._s(_vm.isConnected ? 'Connected' : 'Connecting...')+\"\\n              \")])],1),_vm._v(\" \"),_c('div',{staticClass:\"mb-3\"},[_c('strong',[_vm._v(\"Role:\")]),_vm._v(\" \"),_c('v-chip',{attrs:{\"color\":_vm.currentRole === 'host' ? 'success' : 'primary',\"small\":\"\",\"text-color\":\"white\"}},[_vm._v(\"\\n                \"+_vm._s(_vm.currentRole === 'host' ? 'Host' : 'Participant')+\"\\n              \")])],1),_vm._v(\" \"),_c('div',{staticClass:\"mb-3\"},[_c('strong',[_vm._v(\"Room:\")]),_vm._v(\" \"),(_vm.wherebyRoom)?_c('span',[_vm._v(_vm._s(_vm.wherebyRoom.roomName))]):_c('span',[_vm._v(\"Creating room...\")])]),_vm._v(\" \"),(_vm.wherebyRoom)?_c('div',{staticClass:\"mb-3\"},[_c('strong',[_vm._v(\"Meeting ID:\")]),_vm._v(\" \"+_vm._s(_vm.wherebyRoom.meetingId)+\"\\n            \")]):_vm._e(),_vm._v(\" \"),_c('div',{staticClass:\"mb-3\"},[_c('strong',[_vm._v(\"Video:\")]),_vm._v(\" \"+_vm._s(_vm.isVideoEnabled ? 'On' : 'Off')+\"\\n            \")]),_vm._v(\" \"),_c('div',{staticClass:\"mb-3\"},[_c('strong',[_vm._v(\"Audio:\")]),_vm._v(\" \"+_vm._s(!_vm.isMuted ? 'On' : 'Off')+\"\\n            \")]),_vm._v(\" \"),_c('div',{staticClass:\"mb-3\"},[_c('strong',[_vm._v(\"Features:\")]),_vm._v(\" All controls available in Whereby\\n              interface\\n            \")])])],1),_vm._v(\" \"),_c('v-card',{staticClass:\"mt-4\",attrs:{\"elevation\":\"2\"}},[_c('v-card-title',[_vm._v(\"Instructions\")]),_vm._v(\" \"),_c('v-card-text',[_c('ol',{staticClass:\"pl-4\"},[_c('li',[_vm._v(\"\\n                Click on provider buttons (A, B, C) to switch between video\\n                providers\\n              \")]),_vm._v(\" \"),_c('li',[_vm._v(\"\\n                Whereby (C) creates dynamic rooms with all paid features:\\n                reactions, hand raising, screen sharing, chat, etc.\\n              \")]),_vm._v(\" \"),_c('li',[_vm._v(\"\\n                Choose \\\"Host\\\" for full control or \\\"Participant\\\" for standard\\n                access\\n              \")]),_vm._v(\" \"),_c('li',[_vm._v(\"Each session creates a unique room automatically\")]),_vm._v(\" \"),_c('li',[_vm._v(\"\\n                All controls are built into the Whereby interface - no\\n                external controls needed\\n              \")]),_vm._v(\" \"),_c('li',[_vm._v(\"Multiple users can join the same room simultaneously\")]),_vm._v(\" \"),_c('li',[_vm._v(\"Host has additional controls and moderator features\")])])])],1)],1)],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=510802b6&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./index.vue?vue&type=style&index=0&id=510802b6&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"510802b6\",\n  \"1181a1da\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VCard } from 'vuetify/lib/components/VCard';\nimport { VCardText } from 'vuetify/lib/components/VCard';\nimport { VCardTitle } from 'vuetify/lib/components/VCard';\nimport { VChip } from 'vuetify/lib/components/VChip';\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VContainer } from 'vuetify/lib/components/VGrid';\nimport { VIcon } from 'vuetify/lib/components/VIcon';\nimport { VRow } from 'vuetify/lib/components/VGrid';\ninstallComponents(component, {VBtn,VCard,VCardText,VCardTitle,VChip,VCol,VContainer,VIcon,VRow})\n", "import VCard from './VCard'\nimport { createSimpleFunctional } from '../../util/helpers'\n\nconst VCardActions = createSimpleFunctional('v-card__actions')\nconst VCardSubtitle = createSimpleFunctional('v-card__subtitle')\nconst VCardText = createSimpleFunctional('v-card__text')\nconst VCardTitle = createSimpleFunctional('v-card__title')\n\nexport {\n  VCard,\n  VCardActions,\n  VCardSubtitle,\n  VCardText,\n  VCardTitle,\n}\n\nexport default {\n  $_vuetify_subcomponents: {\n    VCard,\n    VCardActions,\n    VCardSubtitle,\n    VCardText,\n    VCardTitle,\n  },\n}\n", "// Styles\nimport './VChip.sass'\n\n// Types\nimport { VNode } from 'vue'\nimport mixins from '../../util/mixins'\n\n// Components\nimport { VExpandXTransition } from '../transitions'\nimport VIcon from '../VIcon'\n\n// Mixins\nimport Colorable from '../../mixins/colorable'\nimport { factory as GroupableFactory } from '../../mixins/groupable'\nimport Themeable from '../../mixins/themeable'\nimport { factory as ToggleableFactory } from '../../mixins/toggleable'\nimport Routable from '../../mixins/routable'\nimport Sizeable from '../../mixins/sizeable'\n\n// Utilities\nimport { breaking } from '../../util/console'\n\n// Types\nimport { PropValidator, PropType } from 'vue/types/options'\n\n/* @vue/component */\nexport default mixins(\n  Colorable,\n  Sizeable,\n  Routable,\n  Themeable,\n  GroupableFactory('chipGroup'),\n  ToggleableFactory('inputValue')\n).extend({\n  name: 'v-chip',\n\n  props: {\n    active: {\n      type: Boolean,\n      default: true,\n    },\n    activeClass: {\n      type: String,\n      default (): string | undefined {\n        if (!this.chipGroup) return ''\n\n        return this.chipGroup.activeClass\n      },\n    } as any as PropValidator<string>,\n    close: Boolean,\n    closeIcon: {\n      type: String,\n      default: '$delete',\n    },\n    closeLabel: {\n      type: String,\n      default: '$vuetify.close',\n    },\n    disabled: Boolean,\n    draggable: Boolean,\n    filter: Boolean,\n    filterIcon: {\n      type: String,\n      default: '$complete',\n    },\n    label: Boolean,\n    link: Boolean,\n    outlined: Boolean,\n    pill: Boolean,\n    tag: {\n      type: String,\n      default: 'span',\n    },\n    textColor: String,\n    value: null as any as PropType<any>,\n  },\n\n  data: () => ({\n    proxyClass: 'v-chip--active',\n  }),\n\n  computed: {\n    classes (): object {\n      return {\n        'v-chip': true,\n        ...Routable.options.computed.classes.call(this),\n        'v-chip--clickable': this.isClickable,\n        'v-chip--disabled': this.disabled,\n        'v-chip--draggable': this.draggable,\n        'v-chip--label': this.label,\n        'v-chip--link': this.isLink,\n        'v-chip--no-color': !this.color,\n        'v-chip--outlined': this.outlined,\n        'v-chip--pill': this.pill,\n        'v-chip--removable': this.hasClose,\n        ...this.themeClasses,\n        ...this.sizeableClasses,\n        ...this.groupClasses,\n      }\n    },\n    hasClose (): boolean {\n      return Boolean(this.close)\n    },\n    isClickable (): boolean {\n      return Boolean(\n        Routable.options.computed.isClickable.call(this) ||\n        this.chipGroup\n      )\n    },\n  },\n\n  created () {\n    const breakingProps = [\n      ['outline', 'outlined'],\n      ['selected', 'input-value'],\n      ['value', 'active'],\n      ['@input', '@active.sync'],\n    ]\n\n    /* istanbul ignore next */\n    breakingProps.forEach(([original, replacement]) => {\n      if (this.$attrs.hasOwnProperty(original)) breaking(original, replacement, this)\n    })\n  },\n\n  methods: {\n    click (e: MouseEvent): void {\n      this.$emit('click', e)\n\n      this.chipGroup && this.toggle()\n    },\n    genFilter (): VNode {\n      const children = []\n\n      if (this.isActive) {\n        children.push(\n          this.$createElement(VIcon, {\n            staticClass: 'v-chip__filter',\n            props: { left: true },\n          }, this.filterIcon)\n        )\n      }\n\n      return this.$createElement(VExpandXTransition, children)\n    },\n    genClose (): VNode {\n      return this.$createElement(VIcon, {\n        staticClass: 'v-chip__close',\n        props: {\n          right: true,\n          size: 18,\n        },\n        attrs: {\n          'aria-label': this.$vuetify.lang.t(this.closeLabel),\n        },\n        on: {\n          click: (e: Event) => {\n            e.stopPropagation()\n            e.preventDefault()\n\n            this.$emit('click:close')\n            this.$emit('update:active', false)\n          },\n        },\n      }, this.closeIcon)\n    },\n    genContent (): VNode {\n      return this.$createElement('span', {\n        staticClass: 'v-chip__content',\n      }, [\n        this.filter && this.genFilter(),\n        this.$slots.default,\n        this.hasClose && this.genClose(),\n      ])\n    },\n  },\n\n  render (h): VNode {\n    const children = [this.genContent()]\n    let { tag, data } = this.generateRouteLink()\n\n    data.attrs = {\n      ...data.attrs,\n      draggable: this.draggable ? 'true' : undefined,\n      tabindex: this.chipGroup && !this.disabled ? 0 : data.attrs!.tabindex,\n    }\n    data.directives!.push({\n      name: 'show',\n      value: this.active,\n    })\n    data = this.setBackgroundColor(this.color, data)\n\n    const color = this.textColor || (this.outlined && this.color)\n\n    return h(tag, this.setTextColor(color, data), children)\n  },\n})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./VChip.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"197fcea4\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v-chip:not(.v-chip--outlined).accent,.v-chip:not(.v-chip--outlined).error,.v-chip:not(.v-chip--outlined).info,.v-chip:not(.v-chip--outlined).primary,.v-chip:not(.v-chip--outlined).secondary,.v-chip:not(.v-chip--outlined).success,.v-chip:not(.v-chip--outlined).warning{color:#fff}.theme--light.v-chip{border-color:rgba(0,0,0,.12);color:rgba(0,0,0,.87)}.theme--light.v-chip:not(.v-chip--active){background:#e0e0e0}.theme--light.v-chip:hover:before{opacity:.04}.theme--light.v-chip--active:before,.theme--light.v-chip--active:hover:before,.theme--light.v-chip:focus:before{opacity:.12}.theme--light.v-chip--active:focus:before{opacity:.16}.theme--dark.v-chip{border-color:hsla(0,0%,100%,.12);color:#fff}.theme--dark.v-chip:not(.v-chip--active){background:#555}.theme--dark.v-chip:hover:before{opacity:.08}.theme--dark.v-chip--active:before,.theme--dark.v-chip--active:hover:before,.theme--dark.v-chip:focus:before{opacity:.24}.theme--dark.v-chip--active:focus:before{opacity:.32}.v-chip{align-items:center;cursor:default;display:inline-flex;line-height:20px;max-width:100%;outline:none;overflow:hidden;padding:0 12px;position:relative;text-decoration:none;transition-duration:.28s;transition-property:box-shadow,opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);vertical-align:middle;white-space:nowrap}.v-chip:before{background-color:currentColor;bottom:0;border-radius:inherit;content:\\\"\\\";left:0;opacity:0;position:absolute;pointer-events:none;right:0;top:0}.v-chip .v-avatar{height:24px!important;min-width:24px!important;width:24px!important}.v-chip .v-icon{font-size:24px}.v-application--is-ltr .v-chip .v-avatar--left,.v-application--is-ltr .v-chip .v-icon--left{margin-left:-6px;margin-right:6px}.v-application--is-ltr .v-chip .v-avatar--right,.v-application--is-ltr .v-chip .v-icon--right,.v-application--is-rtl .v-chip .v-avatar--left,.v-application--is-rtl .v-chip .v-icon--left{margin-left:6px;margin-right:-6px}.v-application--is-rtl .v-chip .v-avatar--right,.v-application--is-rtl .v-chip .v-icon--right{margin-left:-6px;margin-right:6px}.v-chip:not(.v-chip--no-color) .v-icon{color:inherit}.v-chip .v-chip__close.v-icon{font-size:18px;max-height:18px;max-width:18px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right{margin-right:-4px}.v-application--is-rtl .v-chip .v-chip__close.v-icon.v-icon--right{margin-left:-4px}.v-chip .v-chip__close.v-icon:active,.v-chip .v-chip__close.v-icon:focus,.v-chip .v-chip__close.v-icon:hover{opacity:.72}.v-chip .v-chip__content{align-items:center;display:inline-flex;height:100%;max-width:100%}.v-chip--active .v-icon{color:inherit}.v-chip--link:before{transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-chip--link:focus:before{opacity:.32}.v-chip--clickable{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip--clickable:active{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-chip--disabled{opacity:.4;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip__filter{max-width:24px}.v-chip__filter.v-icon{color:inherit}.v-chip__filter.expand-x-transition-enter,.v-chip__filter.expand-x-transition-leave-active{margin:0}.v-chip--pill .v-chip__filter{margin-right:0 16px 0 0}.v-chip--pill .v-avatar{height:32px!important;width:32px!important}.v-application--is-ltr .v-chip--pill .v-avatar--left{margin-left:-12px}.v-application--is-ltr .v-chip--pill .v-avatar--right,.v-application--is-rtl .v-chip--pill .v-avatar--left{margin-right:-12px}.v-application--is-rtl .v-chip--pill .v-avatar--right{margin-left:-12px}.v-chip--label{border-radius:4px!important}.v-chip.v-chip--outlined{border-width:thin;border-style:solid}.v-chip.v-chip--outlined.v-chip--active:before{opacity:.08}.v-chip.v-chip--outlined .v-icon{color:inherit}.v-chip.v-chip--outlined.v-chip.v-chip{background-color:transparent!important}.v-chip.v-chip--selected{background:transparent}.v-chip.v-chip--selected:after{opacity:.28}.v-chip.v-size--x-small{border-radius:8px;font-size:10px;height:16px}.v-chip.v-size--small{border-radius:12px;font-size:12px;height:24px}.v-chip.v-size--default{border-radius:16px;font-size:14px;height:32px}.v-chip.v-size--large{border-radius:27px;font-size:16px;height:54px}.v-chip.v-size--x-large{border-radius:33px;font-size:18px;height:66px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AACA;AAJA;AALA;AACA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AADA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAdA;AACA;AAiBA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;AACA;AAiBA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAFA;AACA;AAIA;AACA;AAAA;AAAA;AACA;AACA;AAFA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;;;;;;;;;ACt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uBAaA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAnBA;AACA;AAsBA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AACA;AAIA;AAGA;AACA;AAAA;AACA;AAGA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAjUA;AA3CA;;;;;;;;AC5TA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;ACFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACpCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AAKA;AALA;AADA;;;;;;;;AChBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAIA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAKA;AACA;AAAA;AAQA;AAEA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAPA;AAQA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAtCA;AAyCA;AACA;AADA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;AAFA;AACA;AAkBA;AACA;AApBA;AACA;AAqBA;AACA;AAIA;AACA;AA5BA;AACA;AA6BA;AACA;AAOA;AACA;AAAA;AACA;AADA;AAvFA;AACA;AA2FA;AACA;AACA;AAEA;AAJA;AACA;AAKA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAFA;AAKA;AACA;AACA;AAlBA;AACA;AAmBA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AAPA;AATA;AArBA;AACA;AAwCA;AACA;AACA;AADA;AAOA;AACA;AAlDA;AACA;AAmDA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAEA;AACA;AAHA;AAKA;AACA;AACA;AAFA;AAIA;AAEA;AAEA;AACA;AACA;AAnKA;;;;;;;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}