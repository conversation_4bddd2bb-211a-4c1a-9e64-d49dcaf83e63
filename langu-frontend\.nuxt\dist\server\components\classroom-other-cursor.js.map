{"version": 3, "file": "components/classroom-other-cursor.js", "sources": ["webpack:///./components/classroom/OtherCursor.vue?c205", "webpack:///./assets/images/classroom sync ^\\.\\/.*\\-.*\\.svg$", "webpack:///./components/classroom/OtherCursor.vue?a2f8", "webpack:///./components/classroom/OtherCursor.vue?efbb", "webpack:///./components/classroom/OtherCursor.vue?0a7c", "webpack:///./components/classroom/OtherCursor.vue", "webpack:///./components/classroom/OtherCursor.vue?54d3", "webpack:///./components/classroom/OtherCursor.vue?d611"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OtherCursor.vue?vue&type=style&index=0&id=b743e11e&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"194819e0\", content, true, context)\n};", "var map = {\n\t\"./arrow-left.svg\": 522,\n\t\"./arrow-right.svg\": 623,\n\t\"./corner-resize-marker.svg\": 513,\n\t\"./cursor-student-down.svg\": 596,\n\t\"./cursor-student-right.svg\": 597,\n\t\"./cursor-teacher-down.svg\": 598,\n\t\"./cursor-teacher-right.svg\": 599,\n\t\"./student-arrow-2.svg\": 602,\n\t\"./student-arrow.svg\": 603,\n\t\"./student-beforeGrab.svg\": 604,\n\t\"./student-cursor-link.svg\": 605,\n\t\"./student-dragging.svg\": 606,\n\t\"./student-eraser.svg\": 607,\n\t\"./student-pencil.svg\": 608,\n\t\"./student-pointer.svg\": 609,\n\t\"./student-text-cursor.svg\": 610,\n\t\"./teacher-arrow-2.svg\": 611,\n\t\"./teacher-arrow.svg\": 612,\n\t\"./teacher-beforeGrab.svg\": 613,\n\t\"./teacher-cursor-link.svg\": 614,\n\t\"./teacher-dragging.svg\": 615,\n\t\"./teacher-eraser.svg\": 616,\n\t\"./teacher-pencil.svg\": 617,\n\t\"./teacher-pointer.svg\": 618,\n\t\"./teacher-text-cursor.svg\": 619,\n\t\"./volume-high.svg\": 578\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 1319;", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OtherCursor.vue?vue&type=style&index=0&id=b743e11e&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".other_cursor[data-v-b743e11e]{width:100%;position:absolute;z-index:99998;background-size:contain!important;background-position:50%!important;background-repeat:no-repeat!important}.other_cursor.pointer[data-v-b743e11e]{width:22px;height:23px}.other_cursor.pencil[data-v-b743e11e]{width:23px;height:23px;margin:-22px 0 0}.other_cursor.eraser[data-v-b743e11e]{width:25px;height:23px;margin:-20px 0 0 -10px}.other_cursor .cursor-name[data-v-b743e11e]{position:absolute;left:35px;bottom:-20px;max-width:180px;height:20px;padding:0 8px;white-space:nowrap;text-overflow:ellipsis;color:#fff;font-size:13px;line-height:20px;border-radius:3px;font-weight:600;overflow:hidden}@media only screen and (max-width:767px){.other_cursor .cursor-name[data-v-b743e11e]{left:25px;bottom:-15px;max-width:80px;height:16px;font-weight:400;font-size:11px;line-height:16px}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isVisible),expression:\"isVisible\"}],ref:\"other_cursor\",class:['other_cursor', _vm.otherCursor.cursor],style:(_vm.styles),attrs:{\"id\":\"other_cursor\"}},[_vm._ssrNode(\"<div class=\\\"cursor-name\\\"\"+(_vm._ssrStyle(null,{ backgroundColor: _vm.otherCursor.bgColorTooltip }, null))+\" data-v-b743e11e>\"+_vm._ssrEscape(\"\\n    \"+_vm._s(_vm.otherCursor.username)+\"\\n  \")+\"</div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'OtherCursor',\n  data() {\n    return {\n      left: 0,\n      top: 0,\n    }\n  },\n  computed: {\n    otherCursor() {\n      return this.$store.getters['classroom/otherCursor']\n    },\n    otherUserRole() {\n      return this.$store.getters['classroom/otherUserRole']\n    },\n    isVisible() {\n      return !!this.otherCursor.username\n    },\n    zoom() {\n      return this.$store.getters['classroom/zoomAsset']?.asset\n    },\n    styles() {\n      return {\n        top: `${this.top}px`,\n        left: `${this.left}px`,\n        backgroundImage:\n          'url(' +\n          require(`~/assets/images/classroom/${this.otherUserRole}-${this.otherCursor.cursor}.svg`) +\n          ')',\n      }\n    },\n  },\n  watch: {\n    otherCursor: {\n      handler(data) {\n        this.left = (data.coords.x - this.zoom.x) * this.zoom.zoomIndex\n        this.top = (data.coords.y - this.zoom.y) * this.zoom.zoomIndex\n      },\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OtherCursor.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./OtherCursor.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./OtherCursor.vue?vue&type=template&id=b743e11e&scoped=true&\"\nimport script from \"./OtherCursor.vue?vue&type=script&lang=js&\"\nexport * from \"./OtherCursor.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./OtherCursor.vue?vue&type=style&index=0&id=b743e11e&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"b743e11e\",\n  \"5063d7d6\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AC/CA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAHA;AAQA;AACA;AAvBA;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AADA;AAhCA;;AClBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}