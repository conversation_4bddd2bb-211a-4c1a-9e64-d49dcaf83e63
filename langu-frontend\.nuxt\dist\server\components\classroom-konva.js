exports.ids = [19];
exports.modules = {

/***/ 1148:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/Konva.vue?vue&type=template&id=4fc3dbd7&lang=html&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-stage',{ref:"stage",attrs:{"config":_vm.config},on:{"mousedown":_vm.mousedown,"touchstart":_vm.mousedown,"touchmove":_vm.move,"touchend":_vm.mouseup,"mouseup":_vm.mouseup,"mousemove":_vm.move}},[_c('v-layer',{ref:"globalLayer"},[_vm._l((_vm.shapes),function(shape,index){return [(
          (!shape.hasOwnProperty('time') && !shape.hasOwnProperty('page')) ||
          (shape.hasOwnProperty('time') &&
            _vm.currentTime + 1 >= shape.time &&
            shape.time >= _vm.currentTime - 1) ||
          (shape.hasOwnProperty('page') && _vm.currentPage === shape.page)
        )?_c(shape.type,{key:index,tag:"component",attrs:{"config":shape}}):_vm._e()]})],2)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/classroom/Konva.vue?vue&type=template&id=4fc3dbd7&lang=html&

// EXTERNAL MODULE: external "konva"
var external_konva_ = __webpack_require__(861);
var external_konva_default = /*#__PURE__*/__webpack_require__.n(external_konva_);

// EXTERNAL MODULE: ./helpers/constants.js
var constants = __webpack_require__(69);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/Konva.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var Konvavue_type_script_lang_js_ = ({
  name: 'Konva',
  props: {
    file: {
      type: Object,
      required: true
    },
    width: {
      type: Number,
      required: true
    },
    height: {
      type: Number,
      required: true
    },
    scale: {
      type: Number,
      default: 1
    },
    currentTime: {
      type: Number,
      default: null
    },
    currentPage: {
      type: Number,
      default: null
    },
    isMainKonva: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      shapeData: null,
      beginPoint: null,
      konvaEl: null,
      konvaOverlayREl: null,
      konvaOverlayBEl: null
    };
  },

  computed: {
    isCanvasOversizeX() {
      return constants["n" /* mainCanvasWidth */] > this.width;
    },

    isScaledCanvasOversizeX() {
      return constants["n" /* mainCanvasWidth */] * this.scale > this.width;
    },

    isCanvasOversizeY() {
      return constants["k" /* mainCanvasHeight */] > this.viewportHeight;
    },

    isScaledCanvasOversizeY() {
      return constants["k" /* mainCanvasHeight */] * this.scale > this.height;
    },

    assetType() {
      return this.file.asset.type;
    },

    userParams() {
      return this.$store.getters['classroom/userParams'];
    },

    assetShapes() {
      var _this$file$asset$shap, _this$file$asset;

      return (_this$file$asset$shap = (_this$file$asset = this.file.asset) === null || _this$file$asset === void 0 ? void 0 : _this$file$asset.shapes) !== null && _this$file$asset$shap !== void 0 ? _this$file$asset$shap : [];
    },

    shapes() {
      const assetShapes = [...this.assetShapes];
      const _shapes = [];

      if (this.shapeData) {
        assetShapes.push(this.shapeData);
      }

      assetShapes.forEach(shape => {
        var _this$file$asset2;

        const _shape = { ...shape
        };
        _shape.x = shape.x - this.zoomX;
        _shape.y = shape.y - this.zoomY;

        if ((_this$file$asset2 = this.file.asset) !== null && _this$file$asset2 !== void 0 && _this$file$asset2.originalWidth) {
          _shape.strokeWidth = shape.strokeWidth * this.file.asset.originalWidth / this.file.asset.width;
        }

        _shapes.push(_shape);
      }); // if (this.isMainKonva) {
      //   _shapes.push({
      //     type: 'v-line',
      //     stroke: '#f8ae3c',
      //     strokeWidth: 5,
      //     x: 0,
      //     y: 0,
      //     points: [
      //       mainCanvasOffsetX, mainCanvasOffsetY,
      //       mainCanvasOffsetX, mainCanvasHeight + mainCanvasOffsetY,
      //       mainCanvasWidth + mainCanvasOffsetX, mainCanvasHeight + mainCanvasOffsetY,
      //       mainCanvasWidth + mainCanvasOffsetX, mainCanvasOffsetY,
      //       mainCanvasOffsetX, mainCanvasOffsetY,
      //     ],
      //   })
      // }

      return _shapes;
    },

    zoomX() {
      return this.isMainKonva ? this.zoom.x : 0;
    },

    zoomY() {
      return this.isMainKonva ? this.zoom.y : 0;
    },

    zoom() {
      return this.$store.getters['classroom/zoomAsset'].asset;
    },

    config() {
      return {
        scale: {
          x: this.scale,
          y: this.scale
        },
        width: this.width,
        height: this.height,
        draggable: false
      };
    }

  },
  watch: {
    width(width) {
      this.stage.setWidth(width);

      if (this.isMainKonva) {
        if (this.konvaOverlayREl) {
          this.setStyleForHorizontalMainKonvaOverlays();
        }

        if (this.konvaOverlayBEl) {
          this.setStyleForVerticalMainKonvaOverlays();
        }
      }
    },

    height(height) {
      this.stage.setHeight(height);

      if (this.isMainKonva) {
        if (this.konvaOverlayREl) {
          this.setStyleForHorizontalMainKonvaOverlays();
        }

        if (this.konvaOverlayBEl) {
          this.setStyleForVerticalMainKonvaOverlays();
        }
      }
    },

    scale(scale) {
      this.stage.setScale({
        x: scale,
        y: scale
      });

      if (this.isMainKonva) {
        if (this.konvaOverlayREl) {
          this.setStyleForHorizontalMainKonvaOverlays();
        }

        if (this.konvaOverlayBEl) {
          this.setStyleForVerticalMainKonvaOverlays();
        }
      }
    },

    isScaledCanvasOversizeX(newValue) {
      if (this.isMainKonva) {
        if (newValue) {
          this.konvaEl.removeChild(this.konvaOverlayREl);
          this.konvaOverlayREl = null;
        } else {
          this.addHorizontalMainKonvaOverlays();
        }
      }
    },

    isScaledCanvasOversizeY(newValue) {
      if (this.isMainKonva) {
        if (newValue) {
          this.konvaEl.removeChild(this.konvaOverlayBEl);
          this.konvaOverlayBEl = null;
        } else {
          this.addVerticalMainKonvaOverlays();
        }
      }
    }

  },

  mounted() {
    this.stage = this.$refs.stage.getStage();
    this.$nextTick(() => {
      this.move();
    });
    this.konvaEl = document.getElementById('konva');

    if (this.isMainKonva) {
      if (!this.isScaledCanvasOversizeX) {
        this.addHorizontalMainKonvaOverlays();
      }

      if (!this.isScaledCanvasOversizeY) {
        this.addVerticalMainKonvaOverlays();
      }
    }
  },

  methods: {
    addHorizontalMainKonvaOverlays() {
      if (!this.konvaOverlayREl) {
        this.konvaOverlayREl = document.createElement('div');
        this.konvaOverlayREl.classList.add('konva-overlay-r');
        this.setStyleForHorizontalMainKonvaOverlays();
        this.konvaOverlayREl.addEventListener('mouseenter', this.mouseup);
        this.konvaEl.appendChild(this.konvaOverlayREl);
      }
    },

    addVerticalMainKonvaOverlays() {
      if (!this.konvaOverlayBEl) {
        this.konvaOverlayBEl = document.createElement('div');
        this.konvaOverlayBEl.classList.add('konva-overlay-b');
        this.setStyleForVerticalMainKonvaOverlays();
        this.konvaOverlayBEl.addEventListener('mouseenter', this.mouseup);
        this.konvaEl.appendChild(this.konvaOverlayBEl);
      }
    },

    setStyleForHorizontalMainKonvaOverlays() {
      this.konvaOverlayREl.style.position = 'absolute';
      this.konvaOverlayREl.style.top = '0';
      this.konvaOverlayREl.style.right = '0';
      this.konvaOverlayREl.style.width = `${this.width - constants["n" /* mainCanvasWidth */] * this.scale}px`;
      this.konvaOverlayREl.style.height = `${this.height}px`;
      this.konvaOverlayREl.style.backgroundColor = '#DCDCDD';
    },

    setStyleForVerticalMainKonvaOverlays() {
      this.konvaOverlayBEl.style.position = 'absolute';
      this.konvaOverlayBEl.style.bottom = '0';
      this.konvaOverlayBEl.style.left = '0';
      this.konvaOverlayBEl.style.width = `${this.width}px`;
      this.konvaOverlayBEl.style.height = `${this.height - constants["k" /* mainCanvasHeight */] * this.scale}px`;
      this.konvaOverlayBEl.style.backgroundColor = '#DCDCDD';
    },

    mousedown(event) {
      const layer = this.$refs.globalLayer.getNode();
      const position = event.target.getStage().getPointerPosition();
      this.beginPoint = {
        x: position.x / this.scale + this.zoomX,
        y: position.y / this.scale + this.zoomY
      };

      switch (this.userParams.tool) {
        case constants["f" /* TOOL_POINTER */]:
          {
            const ripple = new external_konva_default.a.Circle({
              x: position.x / this.scale,
              y: position.y / this.scale,
              radius: 2,
              stroke: this.userParams.color,
              strokeWidth: 1
            });
            layer.add(ripple);
            new external_konva_default.a.Tween({
              node: ripple,
              duration: 1,
              radius: 24,
              onFinish: () => ripple.destroy()
            }).play();
            return;
          }

        case constants["e" /* TOOL_PEN */]:
          this.shapeData = {
            type: 'v-line',
            stroke: this.userParams.color,
            strokeWidth: 5,
            x: 0,
            y: 0,
            points: [position.x / this.scale + this.zoomX, position.y / this.scale + this.zoomY],
            lineCap: 'round',
            lineJoin: 'round',
            tension: 0,
            bezier: true,
            perfectDrawEnabled: false
          };
          break;

        case constants["g" /* TOOL_SQUARE */]:
          this.shapeData = {
            type: 'v-rect',
            x: position.x / this.scale + this.zoomX,
            y: position.y / this.scale + this.zoomY,
            width: 1,
            height: 1,
            stroke: this.userParams.color,
            strokeWidth: 5
          };
          break;

        case constants["b" /* TOOL_CIRCLE */]:
          this.shapeData = {
            type: 'v-circle',
            x: position.x / this.scale + this.zoomX,
            y: position.y / this.scale + this.zoomY,
            radius: 1,
            stroke: this.userParams.color,
            strokeWidth: 5
          };
          break;

        case constants["h" /* TOOL_TRIANGLE */]:
          this.shapeData = {
            type: 'v-line',
            stroke: this.userParams.color,
            strokeWidth: 5,
            x: position.x / this.scale + this.zoomX,
            y: position.y / this.scale + this.zoomY,
            points: [0, 0, 0, 0, 0, 0],
            tension: 0,
            closed: true
          };
          break;

        case constants["d" /* TOOL_LINE */]:
          this.shapeData = {
            type: 'v-line',
            stroke: this.userParams.color,
            strokeWidth: 5,
            x: 0,
            y: 0,
            points: [position.x / this.scale + this.zoomX, position.y / this.scale + this.zoomY]
          };
          break;

        case constants["c" /* TOOL_ERASER */]:
          this.shapeData = {
            type: 'v-line',
            stroke: '#f2f2f2',
            strokeWidth: 30,
            x: 0,
            y: 0,
            points: [position.x / this.scale + this.zoomX, position.y / this.scale + this.zoomY],
            globalCompositeOperation: 'destination-out'
          };
          break;

        default:
          console.warn('Requested action doesnt found for selected cursor - ' + this.userParams.tool);
      }

      if (this.userParams.tool !== constants["f" /* TOOL_POINTER */]) {
        if (this.assetType === 'video') {
          this.shapeData.time = this.currentTime;
        }

        if (this.assetType === 'pdf') {
          this.shapeData.page = this.currentPage;
        }
      }
    },

    mouseup() {
      if (!this.shapeData || !this.shapeData.type) return;
      const asset = {
        shapes: [...this.assetShapes, this.shapeData]
      };
      this.$store.commit('classroom/moveAsset', {
        id: this.file.id,
        asset
      });
      this.$store.dispatch('classroom/moveAsset', {
        id: this.file.id,
        lessonId: this.file.lessonId,
        asset
      });
      this.beginPoint = null;
      this.shapeData = null;
    },

    move(event) {
      if (this.shapeData) {
        const position = event.target.getStage().getPointerPosition();
        this.drawing(position);
      }
    },

    drawing(position) {
      if (this.shapeData) {
        let offsetWidth;
        let offsetHeight;

        switch (this.userParams.tool) {
          case constants["e" /* TOOL_PEN */]:
          case constants["c" /* TOOL_ERASER */]:
            this.shapeData.points = [...this.shapeData.points, position.x / this.scale + this.zoomX, position.y / this.scale + this.zoomY];
            break;

          case constants["g" /* TOOL_SQUARE */]:
            this.shapeData.width = position.x / this.scale + this.zoomX - this.beginPoint.x;
            this.shapeData.height = position.y / this.scale + this.zoomY - this.beginPoint.y;
            break;

          case constants["b" /* TOOL_CIRCLE */]:
            offsetWidth = Math.abs(position.x / this.scale + this.zoomX - this.beginPoint.x);
            offsetHeight = Math.abs(position.y / this.scale + this.zoomY - this.beginPoint.y);
            this.shapeData.radius = Math.max(offsetWidth, offsetHeight);
            break;

          case constants["h" /* TOOL_TRIANGLE */]:
            this.shapeData.points = [-(position.x / this.scale + this.zoomX - this.beginPoint.x), position.y / this.scale + this.zoomY - this.beginPoint.y, 0, 0, position.x / this.scale + this.zoomX - this.beginPoint.x, position.y / this.scale + this.zoomY - this.beginPoint.y];
            break;

          case constants["d" /* TOOL_LINE */]:
            this.shapeData.points = [this.beginPoint.x, this.beginPoint.y, position.x / this.scale + this.zoomX, position.y / this.scale + this.zoomY];
            break;

          default:
            console.warn('Requested action doesnt found for selected cursor');
        }
      }
    }

  }
});
// CONCATENATED MODULE: ./components/classroom/Konva.vue?vue&type=script&lang=js&
 /* harmony default export */ var classroom_Konvavue_type_script_lang_js_ = (Konvavue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/classroom/Konva.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  classroom_Konvavue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "70e06b04"
  
)

/* harmony default export */ var Konva = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=classroom-konva.js.map