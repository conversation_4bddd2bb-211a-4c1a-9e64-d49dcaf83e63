{"version": 3, "file": "pages/about-us/index.js", "sources": ["webpack:///./node_modules/vuetify/src/components/VSkeletonLoader/VSkeletonLoader.sass?044b", "webpack:///./node_modules/vuetify/src/components/VSkeletonLoader/VSkeletonLoader.sass", "webpack:///../../../src/components/VSkeletonLoader/VSkeletonLoader.ts", "webpack:///./pages/about-us/index.vue?ffba", "webpack:///./assets/images/about-us-page sync ^\\.\\/box\\-icon\\-.*\\.svg$", "webpack:///./pages/about-us/index.vue?1afd", "webpack:///./pages/about-us/index.vue?bbd6", "webpack:///./pages/about-us/index.vue?f35b", "webpack:///./pages/about-us/index.vue", "webpack:///./pages/about-us/index.vue?3879", "webpack:///./pages/about-us/index.vue?2f2d"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./VSkeletonLoader.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"5f757930\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".theme--light.v-skeleton-loader .v-skeleton-loader__bone:after{background:linear-gradient(90deg,hsla(0,0%,100%,0),hsla(0,0%,100%,.3),hsla(0,0%,100%,0))}.theme--light.v-skeleton-loader .v-skeleton-loader__avatar,.theme--light.v-skeleton-loader .v-skeleton-loader__button,.theme--light.v-skeleton-loader .v-skeleton-loader__chip,.theme--light.v-skeleton-loader .v-skeleton-loader__divider,.theme--light.v-skeleton-loader .v-skeleton-loader__heading,.theme--light.v-skeleton-loader .v-skeleton-loader__image,.theme--light.v-skeleton-loader .v-skeleton-loader__text{background:rgba(0,0,0,.12)}.theme--light.v-skeleton-loader .v-skeleton-loader__actions,.theme--light.v-skeleton-loader .v-skeleton-loader__article,.theme--light.v-skeleton-loader .v-skeleton-loader__card-heading,.theme--light.v-skeleton-loader .v-skeleton-loader__card-text,.theme--light.v-skeleton-loader .v-skeleton-loader__date-picker,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-avatar,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-avatar-three-line,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-avatar-two-line,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-text,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-three-line,.theme--light.v-skeleton-loader .v-skeleton-loader__list-item-two-line,.theme--light.v-skeleton-loader .v-skeleton-loader__table-heading,.theme--light.v-skeleton-loader .v-skeleton-loader__table-tbody,.theme--light.v-skeleton-loader .v-skeleton-loader__table-tfoot,.theme--light.v-skeleton-loader .v-skeleton-loader__table-thead{background:#fff}.theme--dark.v-skeleton-loader .v-skeleton-loader__bone:after{background:linear-gradient(90deg,hsla(0,0%,100%,0),hsla(0,0%,100%,.05),hsla(0,0%,100%,0))}.theme--dark.v-skeleton-loader .v-skeleton-loader__avatar,.theme--dark.v-skeleton-loader .v-skeleton-loader__button,.theme--dark.v-skeleton-loader .v-skeleton-loader__chip,.theme--dark.v-skeleton-loader .v-skeleton-loader__divider,.theme--dark.v-skeleton-loader .v-skeleton-loader__heading,.theme--dark.v-skeleton-loader .v-skeleton-loader__image,.theme--dark.v-skeleton-loader .v-skeleton-loader__text{background:hsla(0,0%,100%,.12)}.theme--dark.v-skeleton-loader .v-skeleton-loader__actions,.theme--dark.v-skeleton-loader .v-skeleton-loader__article,.theme--dark.v-skeleton-loader .v-skeleton-loader__card-heading,.theme--dark.v-skeleton-loader .v-skeleton-loader__card-text,.theme--dark.v-skeleton-loader .v-skeleton-loader__date-picker,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-avatar,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-avatar-three-line,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-avatar-two-line,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-text,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-three-line,.theme--dark.v-skeleton-loader .v-skeleton-loader__list-item-two-line,.theme--dark.v-skeleton-loader .v-skeleton-loader__table-heading,.theme--dark.v-skeleton-loader .v-skeleton-loader__table-tbody,.theme--dark.v-skeleton-loader .v-skeleton-loader__table-tfoot,.theme--dark.v-skeleton-loader .v-skeleton-loader__table-thead{background:#1e1e1e}.v-skeleton-loader{border-radius:8px;position:relative;vertical-align:top}.v-skeleton-loader__actions{padding:16px 16px 8px;text-align:right}.v-skeleton-loader__actions .v-skeleton-loader__button{display:inline-block}.v-application--is-ltr .v-skeleton-loader__actions .v-skeleton-loader__button:first-child{margin-right:12px}.v-application--is-rtl .v-skeleton-loader__actions .v-skeleton-loader__button:first-child{margin-left:12px}.v-skeleton-loader .v-skeleton-loader__list-item,.v-skeleton-loader .v-skeleton-loader__list-item-avatar,.v-skeleton-loader .v-skeleton-loader__list-item-avatar-three-line,.v-skeleton-loader .v-skeleton-loader__list-item-avatar-two-line,.v-skeleton-loader .v-skeleton-loader__list-item-text,.v-skeleton-loader .v-skeleton-loader__list-item-three-line,.v-skeleton-loader .v-skeleton-loader__list-item-two-line{border-radius:8px}.v-skeleton-loader .v-skeleton-loader__actions:after,.v-skeleton-loader .v-skeleton-loader__article:after,.v-skeleton-loader .v-skeleton-loader__card-avatar:after,.v-skeleton-loader .v-skeleton-loader__card-heading:after,.v-skeleton-loader .v-skeleton-loader__card-text:after,.v-skeleton-loader .v-skeleton-loader__card:after,.v-skeleton-loader .v-skeleton-loader__date-picker-days:after,.v-skeleton-loader .v-skeleton-loader__date-picker-options:after,.v-skeleton-loader .v-skeleton-loader__date-picker:after,.v-skeleton-loader .v-skeleton-loader__list-item-avatar-three-line:after,.v-skeleton-loader .v-skeleton-loader__list-item-avatar-two-line:after,.v-skeleton-loader .v-skeleton-loader__list-item-avatar:after,.v-skeleton-loader .v-skeleton-loader__list-item-text:after,.v-skeleton-loader .v-skeleton-loader__list-item-three-line:after,.v-skeleton-loader .v-skeleton-loader__list-item-two-line:after,.v-skeleton-loader .v-skeleton-loader__list-item:after,.v-skeleton-loader .v-skeleton-loader__paragraph:after,.v-skeleton-loader .v-skeleton-loader__sentences:after,.v-skeleton-loader .v-skeleton-loader__table-cell:after,.v-skeleton-loader .v-skeleton-loader__table-heading:after,.v-skeleton-loader .v-skeleton-loader__table-row-divider:after,.v-skeleton-loader .v-skeleton-loader__table-row:after,.v-skeleton-loader .v-skeleton-loader__table-tbody:after,.v-skeleton-loader .v-skeleton-loader__table-tfoot:after,.v-skeleton-loader .v-skeleton-loader__table-thead:after,.v-skeleton-loader .v-skeleton-loader__table:after{display:none}.v-application--is-ltr .v-skeleton-loader__article .v-skeleton-loader__heading{margin:16px 0 16px 16px}.v-application--is-rtl .v-skeleton-loader__article .v-skeleton-loader__heading{margin:16px 16px 0}.v-skeleton-loader__article .v-skeleton-loader__paragraph{padding:16px}.v-skeleton-loader__bone{border-radius:inherit;overflow:hidden;position:relative}.v-skeleton-loader__bone:after{-webkit-animation:loading 1.5s infinite;animation:loading 1.5s infinite;content:\\\"\\\";height:100%;left:0;position:absolute;right:0;top:0;transform:translateX(-100%);z-index:1}.v-skeleton-loader__avatar{border-radius:50%;height:48px;width:48px}.v-skeleton-loader__button{border-radius:4px;height:36px;width:64px}.v-skeleton-loader__card .v-skeleton-loader__image{border-radius:0}.v-skeleton-loader__card-heading .v-skeleton-loader__heading{margin:16px}.v-skeleton-loader__card-text{padding:16px}.v-skeleton-loader__chip{border-radius:16px;height:32px;width:96px}.v-skeleton-loader__date-picker{border-radius:inherit}.v-skeleton-loader__date-picker .v-skeleton-loader__list-item:first-child .v-skeleton-loader__text{max-width:88px;width:20%}.v-skeleton-loader__date-picker .v-skeleton-loader__heading{max-width:256px;width:40%}.v-skeleton-loader__date-picker-days{display:flex;flex-wrap:wrap;padding:0 12px;margin:0 auto}.v-skeleton-loader__date-picker-days .v-skeleton-loader__avatar{border-radius:8px;flex:1 1 auto;margin:4px;height:40px;width:40px}.v-skeleton-loader__date-picker-options{align-items:center;display:flex;padding:16px}.v-skeleton-loader__date-picker-options .v-skeleton-loader__avatar{height:40px;width:40px}.v-skeleton-loader__date-picker-options .v-skeleton-loader__avatar:nth-child(2){margin-left:auto}.v-application--is-ltr .v-skeleton-loader__date-picker-options .v-skeleton-loader__avatar:nth-child(2){margin-right:8px}.v-application--is-rtl .v-skeleton-loader__date-picker-options .v-skeleton-loader__avatar:nth-child(2){margin-left:8px}.v-skeleton-loader__date-picker-options .v-skeleton-loader__text.v-skeleton-loader__bone:first-child{margin-bottom:0;max-width:50%;width:456px}.v-skeleton-loader__divider{border-radius:1px;height:2px}.v-skeleton-loader__heading{border-radius:12px;height:24px;width:45%}.v-skeleton-loader__image{height:200px;border-radius:0}.v-skeleton-loader__image~.v-skeleton-loader__card-heading{border-radius:0}.v-skeleton-loader__image::first-child,.v-skeleton-loader__image::last-child{border-radius:inherit}.v-skeleton-loader__list-item{height:48px}.v-skeleton-loader__list-item-three-line{flex-wrap:wrap}.v-skeleton-loader__list-item-three-line>*{flex:1 0 100%;width:100%}.v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__avatar,.v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__avatar,.v-skeleton-loader__list-item-avatar .v-skeleton-loader__avatar{height:40px;width:40px}.v-skeleton-loader__list-item-avatar{height:48px}.v-skeleton-loader__list-item-avatar-two-line,.v-skeleton-loader__list-item-two-line{height:72px}.v-skeleton-loader__list-item-avatar-three-line,.v-skeleton-loader__list-item-three-line{height:88px}.v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__avatar{align-self:flex-start}.v-skeleton-loader__list-item,.v-skeleton-loader__list-item-avatar,.v-skeleton-loader__list-item-avatar-three-line,.v-skeleton-loader__list-item-avatar-two-line,.v-skeleton-loader__list-item-three-line,.v-skeleton-loader__list-item-two-line{align-content:center;align-items:center;display:flex;flex-wrap:wrap;padding:0 16px}.v-application--is-ltr .v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item-avatar .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item-three-line .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item-two-line .v-skeleton-loader__avatar,.v-application--is-ltr .v-skeleton-loader__list-item .v-skeleton-loader__avatar{margin-right:16px}.v-application--is-rtl .v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item-avatar .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item-three-line .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item-two-line .v-skeleton-loader__avatar,.v-application--is-rtl .v-skeleton-loader__list-item .v-skeleton-loader__avatar{margin-left:16px}.v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-avatar-three-line .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-avatar-two-line .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item-avatar .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-avatar .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item-three-line .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-three-line .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item-two-line .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item-two-line .v-skeleton-loader__text:only-child,.v-skeleton-loader__list-item .v-skeleton-loader__text:last-child,.v-skeleton-loader__list-item .v-skeleton-loader__text:only-child{margin-bottom:0}.v-skeleton-loader__paragraph,.v-skeleton-loader__sentences{flex:1 0 auto}.v-skeleton-loader__paragraph:not(:last-child){margin-bottom:6px}.v-skeleton-loader__paragraph .v-skeleton-loader__text:first-child{max-width:100%}.v-skeleton-loader__paragraph .v-skeleton-loader__text:nth-child(2){max-width:50%}.v-skeleton-loader__paragraph .v-skeleton-loader__text:nth-child(3),.v-skeleton-loader__sentences .v-skeleton-loader__text:nth-child(2){max-width:70%}.v-skeleton-loader__sentences:not(:last-child){margin-bottom:6px}.v-skeleton-loader__table-heading{align-items:center;display:flex;justify-content:space-between;padding:16px}.v-skeleton-loader__table-heading .v-skeleton-loader__heading{max-width:15%}.v-skeleton-loader__table-heading .v-skeleton-loader__text{max-width:40%}.v-skeleton-loader__table-thead{display:flex;justify-content:space-between;padding:16px}.v-skeleton-loader__table-thead .v-skeleton-loader__heading{max-width:5%}.v-skeleton-loader__table-tbody{padding:16px 16px 0}.v-skeleton-loader__table-tfoot{align-items:center;display:flex;justify-content:flex-end;padding:16px}.v-application--is-ltr .v-skeleton-loader__table-tfoot>*{margin-left:8px}.v-application--is-rtl .v-skeleton-loader__table-tfoot>*{margin-right:8px}.v-skeleton-loader__table-tfoot .v-skeleton-loader__avatar{height:40px;width:40px}.v-skeleton-loader__table-tfoot .v-skeleton-loader__text{margin-bottom:0}.v-skeleton-loader__table-tfoot .v-skeleton-loader__text:first-child{max-width:128px}.v-skeleton-loader__table-tfoot .v-skeleton-loader__text:nth-child(2){max-width:64px}.v-skeleton-loader__table-row{display:flex;justify-content:space-between}.v-skeleton-loader__table-cell{align-items:center;display:flex;height:48px;width:88px}.v-skeleton-loader__table-cell .v-skeleton-loader__text{margin-bottom:0}.v-skeleton-loader__text{border-radius:6px;flex:1 0 auto;height:12px;margin-bottom:6px}.v-skeleton-loader--boilerplate .v-skeleton-loader__bone:after{display:none}.v-skeleton-loader--is-loading{overflow:hidden}.v-skeleton-loader--tile,.v-skeleton-loader--tile .v-skeleton-loader__bone{border-radius:0}@-webkit-keyframes loading{to{transform:translateX(100%)}}@keyframes loading{to{transform:translateX(100%)}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// Styles\nimport './VSkeletonLoader.sass'\n\n// Mixins\nimport Elevatable from '../../mixins/elevatable'\nimport Measurable from '../../mixins/measurable'\nimport Themeable from '../../mixins/themeable'\n\n// Utilities\nimport mixins from '../../util/mixins'\n\n// Types\nimport { VNode } from 'vue'\nimport { getSlot } from '../../util/helpers'\nimport { PropValidator } from 'vue/types/options'\n\nexport interface HTMLSkeletonLoaderElement extends HTMLElement {\n  _initialStyle?: {\n    display: string | null\n    transition: string\n  }\n}\n\n/* @vue/component */\nexport default mixins(\n  Elevatable,\n  Measurable,\n  Themeable,\n).extend({\n  name: 'VSkeletonLoader',\n\n  props: {\n    boilerplate: Boolean,\n    loading: Boolean,\n    tile: Boolean,\n    transition: String,\n    type: String,\n    types: {\n      type: Object,\n      default: () => ({}),\n    } as PropValidator<Record<string, string>>,\n  },\n\n  computed: {\n    attrs (): object {\n      if (!this.isLoading) return this.$attrs\n\n      return !this.boilerplate ? {\n        'aria-busy': true,\n        'aria-live': 'polite',\n        role: 'alert',\n        ...this.$attrs,\n      } : {}\n    },\n    classes (): object {\n      return {\n        'v-skeleton-loader--boilerplate': this.boilerplate,\n        'v-skeleton-loader--is-loading': this.isLoading,\n        'v-skeleton-loader--tile': this.tile,\n        ...this.themeClasses,\n        ...this.elevationClasses,\n      }\n    },\n    isLoading (): boolean {\n      return !('default' in this.$scopedSlots) || this.loading\n    },\n    rootTypes (): Record<string, string> {\n      return {\n        actions: 'button@2',\n        article: 'heading, paragraph',\n        avatar: 'avatar',\n        button: 'button',\n        card: 'image, card-heading',\n        'card-avatar': 'image, list-item-avatar',\n        'card-heading': 'heading',\n        chip: 'chip',\n        'date-picker': 'list-item, card-heading, divider, date-picker-options, date-picker-days, actions',\n        'date-picker-options': 'text, avatar@2',\n        'date-picker-days': 'avatar@28',\n        heading: 'heading',\n        image: 'image',\n        'list-item': 'text',\n        'list-item-avatar': 'avatar, text',\n        'list-item-two-line': 'sentences',\n        'list-item-avatar-two-line': 'avatar, sentences',\n        'list-item-three-line': 'paragraph',\n        'list-item-avatar-three-line': 'avatar, paragraph',\n        paragraph: 'text@3',\n        sentences: 'text@2',\n        table: 'table-heading, table-thead, table-tbody, table-tfoot',\n        'table-heading': 'heading, text',\n        'table-thead': 'heading@6',\n        'table-tbody': 'table-row-divider@6',\n        'table-row-divider': 'table-row, divider',\n        'table-row': 'table-cell@6',\n        'table-cell': 'text',\n        'table-tfoot': 'text@2, avatar@2',\n        text: 'text',\n        ...this.types,\n      }\n    },\n  },\n\n  methods: {\n    genBone (text: string, children: VNode[]) {\n      return this.$createElement('div', {\n        staticClass: `v-skeleton-loader__${text} v-skeleton-loader__bone`,\n      }, children)\n    },\n    genBones (bone: string): VNode[] {\n      // e.g. 'text@3'\n      const [type, length] = bone.split('@') as [string, number]\n      const generator = () => this.genStructure(type)\n\n      // Generate a length array based upon\n      // value after @ in the bone string\n      return Array.from({ length }).map(generator)\n    },\n    // Fix type when this is merged\n    // https://github.com/microsoft/TypeScript/pull/33050\n    genStructure (type?: string): any {\n      let children = []\n      type = type || this.type || ''\n      const bone = this.rootTypes[type] || ''\n\n      // End of recursion, do nothing\n      /* eslint-disable-next-line no-empty, brace-style */\n      if (type === bone) {}\n      // Array of values - e.g. 'heading, paragraph, text@2'\n      else if (type.indexOf(',') > -1) return this.mapBones(type)\n      // Array of values - e.g. 'paragraph@4'\n      else if (type.indexOf('@') > -1) return this.genBones(type)\n      // Array of values - e.g. 'card@2'\n      else if (bone.indexOf(',') > -1) children = this.mapBones(bone)\n      // Array of values - e.g. 'list-item@2'\n      else if (bone.indexOf('@') > -1) children = this.genBones(bone)\n      // Single value - e.g. 'card-heading'\n      else if (bone) children.push(this.genStructure(bone))\n\n      return [this.genBone(type, children)]\n    },\n    genSkeleton () {\n      const children = []\n\n      if (!this.isLoading) children.push(getSlot(this))\n      else children.push(this.genStructure())\n\n      /* istanbul ignore else */\n      if (!this.transition) return children\n\n      /* istanbul ignore next */\n      return this.$createElement('transition', {\n        props: {\n          name: this.transition,\n        },\n        // Only show transition when\n        // content has been loaded\n        on: {\n          afterEnter: this.resetStyles,\n          beforeEnter: this.onBeforeEnter,\n          beforeLeave: this.onBeforeLeave,\n          leaveCancelled: this.resetStyles,\n        },\n      }, children)\n    },\n    mapBones (bones: string) {\n      // Remove spaces and return array of structures\n      return bones.replace(/\\s/g, '').split(',').map(this.genStructure)\n    },\n    onBeforeEnter (el: HTMLSkeletonLoaderElement) {\n      this.resetStyles(el)\n\n      if (!this.isLoading) return\n\n      el._initialStyle = {\n        display: el.style.display,\n        transition: el.style.transition,\n      }\n\n      el.style.setProperty('transition', 'none', 'important')\n    },\n    onBeforeLeave (el: HTMLSkeletonLoaderElement) {\n      el.style.setProperty('display', 'none', 'important')\n    },\n    resetStyles (el: HTMLSkeletonLoaderElement) {\n      if (!el._initialStyle) return\n\n      el.style.display = el._initialStyle.display || ''\n      el.style.transition = el._initialStyle.transition\n\n      delete el._initialStyle\n    },\n  },\n\n  render (h): VNode {\n    return h('div', {\n      staticClass: 'v-skeleton-loader',\n      attrs: this.attrs,\n      on: this.$listeners,\n      class: this.classes,\n      style: this.isLoading ? this.measurableStyles : undefined,\n    }, [this.genSkeleton()])\n  },\n})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"6b93be8c\", content, true, context)\n};", "var map = {\n\t\"./box-icon-1.svg\": 626,\n\t\"./box-icon-2.svg\": 627,\n\t\"./box-icon-3.svg\": 628\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 1422;", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".about-us-page{font-size:16px}@media only screen and (max-width:1215px){.about-us-page{font-size:14px}}@media only screen and (max-width:991px){.about-us-page{font-size:12px}}@media only screen and (max-width:479px){.about-us-page{font-size:10px}}.about-us-page .heading{max-width:1920px;height:64vh;max-height:758px;min-height:450px;background-color:var(--v-dark-base);background-size:cover;background-repeat:no-repeat;background-position:top}@media only screen and (max-width:1439px){.about-us-page .heading{min-height:350px}}@media only screen and (max-width:991px){.about-us-page .heading{max-height:450px;min-height:300px}}@media only screen and (max-width:479px){.about-us-page .heading{max-height:300px;min-height:250px}}.about-us-page .heading-text{padding:.75em 15px;font-size:1.75em;line-height:1.3;background-color:rgba(0,0,0,.6)}.about-us-page-intro{max-width:786px;padding:0 0 3.5rem;font-size:1.75em;line-height:1.25}.about-us-page-content{max-width:1100px;padding:3.5rem 0 2rem;background-color:#fff}.about-us-page-content .button-container{margin:0 0 6em}.about-us-page-content .boxes{margin:4rem 0 2rem}@media only screen and (min-width:640px){.about-us-page-content .boxes{margin:4rem 0;display:flex;justify-content:space-between;margin:4em 0}}.about-us-page-content .boxes .box{padding:.5rem 1.25rem 1.5rem;font-size:1.2em;line-height:1.15}@media only screen and (min-width:640px){.about-us-page-content .boxes .box{width:33.3333%;padding:0 1.25rem}}.about-us-page-content .boxes .box .icon{height:3em}.about-us-page-content .boxes .box .headline{font-size:inherit!important}.about-us-page-content .members{max-width:950px;padding:4.2em 0 6em}.about-us-page-content .members .member .image{width:200px;max-width:200px;height:200px;margin-bottom:15px}@media only screen and (min-width:768px){.about-us-page-content .members .member .image{width:250px;max-width:250px;height:250px;margin-bottom:0}}.about-us-page-content .members .member .image .v-skeleton-loader__image{height:100%}.about-us-page-content .members .member-content{flex:0 1 100%}.about-us-page-content .members .member .top{font-size:1.2em;color:inherit;line-height:1.15}@media(min-width:768px){.about-us-page-content .members .member .top>.name{height:35px;font-size:28px;overflow:hidden}}@media only screen and (min-width:768px){.about-us-page-content .members .member .top>.occupation{height:25px;font-size:19px!important;overflow:hidden}}@media only screen and (min-width:768px){.about-us-page-content .members .member .top{height:60px;line-height:1}}.about-us-page-content .members .member .bio{width:100%;padding:25px 40px 25px 25px;line-height:1.15;background-color:#eee}@media only screen and (max-width:479px){.about-us-page-content .members .member .bio{padding:20px 15px}}.about-us-page-content .members .member .bio-inner{margin:0;font-size:1.2em}@media only screen and (min-width:768px){.about-us-page-content .members .member .bio{height:190px;padding:18px 40px 18px 10px}.about-us-page-content .members .member .bio-inner{max-height:100%;font-size:1.2em;overflow:hidden}}@media only screen and (min-width:480px){.about-us-page-content .section{max-width:800px}}.about-us-page-content .section .headline{position:relative;line-height:1;font-size:1em!important;color:inherit;text-transform:none}.about-us-page-content .section .headline:after{content:\\\"\\\";display:block;width:100%;height:4px;margin:2px auto 0;background-color:var(--v-orange-base)}.about-us-page-content .section .headline .regular{padding:0 .12em;font-size:3.4em;font-weight:700}.about-us-page-content .section .headline .super{position:absolute;top:0;left:1.6em;color:var(--v-orange-base);font-size:1.105em;font-weight:700;text-transform:uppercase}.about-us-page-content .section .text{margin:2rem 0 0;font-size:1.2em;line-height:1.15}.about-us-page-content .section-team.en .headline .regular{padding-left:.75em}.about-us-page-content .section-team.en .headline .super{left:.3em}.about-us-page-content .section-team.pl .headline .regular{padding-left:.4em}.about-us-page-content .section-team.pl .headline .super{left:.3em}.about-us-page-content .section-teachers.pl .headline .super{left:3.25em}.about-us-page-content .section-methodology.pl .headline .regular{padding-left:.75em}.about-us-page-content .section-methodology.pl .headline .super{left:.3em}.about-us-page-content .section-methodology.es .headline .super{left:0;top:-.2em}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-row',[_c('v-col',{staticClass:\"col-12 px-0\"},[_c('div',{staticClass:\"about-us-page-container\"},[_c('div',{staticClass:\"heading d-flex justify-end flex-column mx-auto text-center\",style:({\n          'background-image':\n            'url(' +\n            require('~/assets/images/about-us-page/aga-and-travis-cambridge-offsite.jpg') +\n            ')',\n        })},[_c('h1',{staticClass:\"heading-text font-weight-regular white--text text-center\",domProps:{\"innerHTML\":_vm._s(_vm.$t('about_us_page.title'))}})]),_vm._v(\" \"),_c('v-container',{staticClass:\"py-0\",attrs:{\"fluid\":\"\"}},[_c('v-row',[_c('v-col',{staticClass:\"col-12\"},[_c('div',{staticClass:\"about-us-page-content mx-auto\"},[_c('div',{staticClass:\"about-us-page-intro mx-auto text-center\"},[_vm._v(\"\\n                \"+_vm._s(_vm.$t('about_us_page.intro'))+\"\\n              \")]),_vm._v(\" \"),_c('section',{class:[\n                  'section section-teachers mx-auto text-center',\n                  _vm.locale ]},[_c('h2',{staticClass:\"headline d-inline-block\",domProps:{\"innerHTML\":_vm._s(_vm.$t('about_us_page.section_title_1'))}}),_vm._v(\" \"),_c('p',{staticClass:\"text font-weight-bold\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('about_us_page.teachers_intro'))+\"\\n                \")]),_vm._v(\" \"),_c('p',{staticClass:\"text\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('about_us_page.teachers_text'))+\"\\n                \")])]),_vm._v(\" \"),_c('div',{staticClass:\"boxes text-center\"},_vm._l((3),function(i){return _c('div',{key:i,staticClass:\"box\"},[_c('div',{staticClass:\"icon d-flex justify-center\"},[_c('v-img',{attrs:{\"src\":require((\"~/assets/images/about-us-page/box-icon-\" + i + \".svg\")),\"max-width\":\"132\",\"height\":\"100%\",\"contain\":\"\",\"center\":\"\",\"alt\":_vm.$t((\"about_us_page.box_title_\" + i))}})],1),_vm._v(\" \"),_c('h3',{staticClass:\"headline d-block font-weight-bold\"},[_vm._v(\"\\n                    \"+_vm._s(_vm.$t((\"about_us_page.box_title_\" + i)))+\"\\n                  \")]),_vm._v(\" \"),_c('p',{staticClass:\"ma-0\"},[_vm._v(\"\\n                    \"+_vm._s(_vm.$t((\"about_us_page.box_text_\" + i)))+\"\\n                  \")])])}),0),_vm._v(\" \"),_c('div',{staticClass:\"button-container text-center\"},[_c('v-btn',{attrs:{\"large\":\"\",\"color\":\"primary\",\"to\":\"/teacher-listing\"}},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('about_us_page.meet_our_teachers'))+\"\\n                \")])],1),_vm._v(\" \"),_c('section',{class:['section section-team mx-auto text-center', _vm.locale]},[_c('h2',{staticClass:\"headline d-inline-block\",domProps:{\"innerHTML\":_vm._s(_vm.$t('about_us_page.section_title_2'))}}),_vm._v(\" \"),_c('p',{staticClass:\"text\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('about_us_page.about_team'))+\"\\n                \")])]),_vm._v(\" \"),_c('section',{staticClass:\"members mx-auto\"},_vm._l((_vm.members),function(member,i){return _c('div',{key:i,class:['d-sm-flex member', { 'mt-2': i !== 0 }]},[_c('div',{staticClass:\"d-flex d-sm-block\"},[_c('v-img',{staticClass:\"image mr-1 mr-sm-2 elevation-3 rounded-lg\",attrs:{\"src\":member.photo,\"alt\":member.name},scopedSlots:_vm._u([{key:\"placeholder\",fn:function(){return [_c('v-skeleton-loader',{attrs:{\"type\":\"image\",\"height\":\"100%\",\"width\":\"100%\"}})]},proxy:true}],null,true)}),_vm._v(\" \"),_c('h3',{staticClass:\"top font-weight-regular d-sm-none\"},[_c('span',{staticClass:\"d-block name orange--text\"},[_vm._v(\"\\n                        \"+_vm._s(member.name)+\"\\n                      \")]),_vm._v(\" \"),_c('span',{staticClass:\"d-block occupation\"},[_vm._v(\"\\n                        \"+_vm._s(member.title)+\"\\n                      \")])])],1),_vm._v(\" \"),_c('div',{staticClass:\"member-content\"},[_c('h3',{staticClass:\"top font-weight-regular d-none d-sm-block\"},[_c('span',{staticClass:\"d-block name orange--text\"},[_vm._v(\"\\n                        \"+_vm._s(member.name)+\"\\n                      \")]),_vm._v(\" \"),_c('span',{staticClass:\"d-block occupation\"},[_vm._v(\"\\n                        \"+_vm._s(member.title)+\"\\n                      \")])]),_vm._v(\" \"),_c('div',{staticClass:\"bio\"},[_c('p',{staticClass:\"bio-inner\"},[_vm._v(\"\\n                        \"+_vm._s(member.content)+\"\\n                      \")])])])])}),0),_vm._v(\" \"),_c('section',{class:[\n                  'section section-methodology text-center mx-auto',\n                  _vm.locale ]},[_c('h2',{staticClass:\"headline d-inline-block\",domProps:{\"innerHTML\":_vm._s(_vm.$t('about_us_page.section_title_3'))}}),_vm._v(\" \"),_c('p',{staticClass:\"text\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('about_us_page.methodology'))+\"\\n                \")])])])])],1)],1)],1)])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'AboutUsPage',\n  data() {\n    return {\n      members: [\n        {\n          name: this.$t('about_us_page.member_travis.name'),\n          photo: require('~/assets/images/about-us-page/travis.jpg'),\n          title: this.$t('about_us_page.member_travis.title'),\n          content: this.$t('about_us_page.member_travis.content'),\n        },\n        {\n          name: this.$t('about_us_page.member_aga.name'),\n          photo: require('~/assets/images/about-us-page/aga-nowicka.jpg'),\n          title: this.$t('about_us_page.member_aga.title'),\n          content: this.$t('about_us_page.member_aga.content'),\n        },\n        {\n          name: this.$t('about_us_page.member_marta.name'),\n          photo: require('~/assets/images/about-us-page/marta.jpg'),\n          title: this.$t('about_us_page.member_marta.title'),\n          content: this.$t('about_us_page.member_marta.content'),\n        },\n        {\n          name: this.$t('about_us_page.member_adrianna.name'),\n          photo: require('~/assets/images/about-us-page/adrianna-lembicz.jpg'),\n          title: this.$t('about_us_page.member_adrianna.title'),\n          content: this.$t('about_us_page.member_adrianna.content'),\n        },\n        {\n          name: this.$t('about_us_page.member_julia.name'),\n          photo: require('~/assets/images/about-us-page/julia.jpg'),\n          title: this.$t('about_us_page.member_julia.title'),\n          content: this.$t('about_us_page.member_julia.content'),\n        },\n      ],\n    }\n  },\n  head() {\n    return {\n      title: this.$t('about_us_page.seo_title'),\n      meta: [\n        {\n          hid: 'description',\n          name: 'description',\n          content: this.$t('about_us_page.seo_description'),\n        },\n        {\n          hid: 'og:title',\n          name: 'og:title',\n          property: 'og:title',\n          content: this.$t('about_us_page.seo_title'),\n        },\n        {\n          property: 'og:description',\n          content: this.$t('about_us_page.seo_description'),\n        },\n        { hid: 'og:image', property: 'og:image', content: this.previewImage },\n        { hid: 'og:image:width', property: 'og:image:width', content: 800 },\n        { hid: 'og:image:height', property: 'og:image:height', content: 396 },\n        {\n          hid: 'og:image:type',\n          property: 'og:image:type',\n          content: 'image/jpeg',\n        },\n      ],\n      bodyAttrs: {\n        class: 'about-us-page',\n      },\n    }\n  },\n  computed: {\n    locale() {\n      return this.$i18n.locale\n    },\n    previewImage() {\n      return (\n        process.env.NUXT_ENV_URL +\n        require(`~/assets/images/about-us-page/aga-and-travis-cambridge-offsite.jpg`)\n      )\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=868df7b8&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./index.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"26f5b02c\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VContainer } from 'vuetify/lib/components/VGrid';\nimport { VImg } from 'vuetify/lib/components/VImg';\nimport { VRow } from 'vuetify/lib/components/VGrid';\nimport { VSkeletonLoader } from 'vuetify/lib/components/VSkeletonLoader';\ninstallComponents(component, {VBtn,VCol,VContainer,VImg,VRow,VSkeletonLoader})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAIA;AAUA;AACA;AAAA;AAKA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AANA;AAYA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAJA;AAJA;AACA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAZA;AACA;AAmBA;AACA;AArBA;AACA;AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA;AAiCA;AACA;AA1DA;AA4DA;AACA;AACA;AACA;AADA;AAFA;AACA;AAKA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AADA;AAAA;AAAA;AAbA;AACA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAEA;AAEA;AAEA;AAEA;AACA;AAGA;AApCA;AACA;AAqCA;AACA;AAEA;AAGA;AACA;AAAA;AAEA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AANA;AAhDA;AACA;AA6DA;AACA;AACA;AAhEA;AACA;AAiEA;AACA;AAEA;AAEA;AACA;AACA;AAFA;AAKA;AA5EA;AACA;AA6EA;AACA;AA/EA;AACA;AAgFA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAzFA;AACA;AA0FA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AA/KA;;;;;;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACxBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AAJA;AAOA;AACA;AACA;AACA;AAJA;AAOA;AACA;AACA;AACA;AAJA;AAOA;AACA;AACA;AACA;AAJA;AAOA;AACA;AACA;AACA;AAJA;AA1BA;AAkCA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAMA;AACA;AACA;AACA;AAJA;AAOA;AACA;AAFA;AAIA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAHA;AAMA;AACA;AADA;AA3BA;AA+BA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAIA;AACA;AAVA;AAvEA;;ACjKA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}