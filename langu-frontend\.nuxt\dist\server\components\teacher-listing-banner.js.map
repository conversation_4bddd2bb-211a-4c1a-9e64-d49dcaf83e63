{"version": 3, "file": "components/teacher-listing-banner.js", "sources": ["webpack:///./assets/images/banners sync ^\\.\\/.*$", "webpack:///./components/teacher-listing/TeacherListingBanner.vue?057f", "webpack:///./components/teacher-listing/TeacherListingBanner.vue?1fa5", "webpack:///./components/teacher-listing/TeacherListingBanner.vue", "webpack:///./components/teacher-listing/TeacherListingBanner.vue?6b1a", "webpack:///./components/teacher-listing/TeacherListingBanner.vue?413e", "webpack:///./components/teacher-listing/TeacherListingBanner.vue?d8e3", "webpack:///./components/teacher-listing/TeacherListingBanner.vue?216d"], "sourcesContent": ["var map = {\n\t\"./business.svg\": 523,\n\t\"./career.svg\": 524,\n\t\"./conversation.svg\": 525,\n\t\"./default.svg\": 510,\n\t\"./diplomacy.svg\": 526,\n\t\"./education.svg\": 527,\n\t\"./engineering.svg\": 528,\n\t\"./exam-preparation.svg\": 529,\n\t\"./finance-banking.svg\": 530,\n\t\"./grammar.svg\": 531,\n\t\"./interview-prep.svg\": 532,\n\t\"./it.svg\": 533,\n\t\"./law.svg\": 534,\n\t\"./life.svg\": 535,\n\t\"./marketing.svg\": 536,\n\t\"./medicine.svg\": 537,\n\t\"./science.svg\": 538,\n\t\"./tourism.svg\": 539,\n\t\"./travel.svg\": 540,\n\t\"./university-preparation.svg\": 541,\n\t\"./vocabulary.svg\": 542,\n\t\"./writing.svg\": 543,\n\t\"./young-learner.svg\": 544\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 1053;", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TeacherListingBanner.vue?vue&type=style&index=0&id=5a0a35ec&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"f30475ea\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"banner\"},[_vm._ssrNode(\"<div class=\\\"banner-content\\\" data-v-5a0a35ec>\"+((_vm.banner.name)?(\"<div class=\\\"banner-title\\\" data-v-5a0a35ec>\"+((_vm.banner.id)?(((_vm.userTag)?(_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.banner.name)+\"\\n        \")):(((_vm.locale === 'es')?(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.selectedLanguage)+\" Para\\n            \"+_vm._s(_vm.banner.name)+\"\\n          \")):(_vm.locale === 'pl')?(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.selectedLanguage)+\": \"+_vm._s(_vm.banner.name.toLowerCase())+\"\\n          \")):(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.selectedLanguage)+\" for\\n            \"+_vm._s(_vm.banner.name)+\"\\n          \")))))):(_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.banner.name)+\"\\n      \")))+\"</div>\"):\"<!---->\")+\" <div class=\\\"banner-text\\\" data-v-5a0a35ec>\"+_vm._ssrEscape(\"\\n      \"+_vm._s(_vm.banner.description)+\"\\n    \")+\"</div></div> \"),(_vm.banner.image)?_vm._ssrNode(\"<div\"+(_vm._ssrClass(null,['banner-image d-flex', _vm.userTag ? 'align-center' : 'align-end']))+\" data-v-5a0a35ec>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"banner-image-helper\\\" data-v-5a0a35ec>\",\"</div>\",[_c('v-img',{attrs:{\"src\":_vm.banner.image,\"contain\":\"\",\"max-height\":\"120\",\"eager\":\"\"}})],1)]):_vm._e()],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'TeacherListingBanner',\n  data() {\n    return {\n      banner: {},\n    }\n  },\n  computed: {\n    locale() {\n      return this.$i18n.locale\n    },\n    activeFilters() {\n      return this.$store.getters['teacher_filter/activeFilters']\n    },\n    selectedLanguage() {\n      const language = this.$store.getters['teacher_filter/selectedLanguage']\n\n      return language\n        ? language.name.charAt(0).toUpperCase() + language.name.slice(1)\n        : this.$t('learning')\n    },\n    selectedMotivation() {\n      return this.activeFilters.find((item) => item.type === 'motivation')\n    },\n    selectedSpecialities() {\n      return this.activeFilters.filter((item) => item.type === 'speciality')\n    },\n    motivationBanners() {\n      return this.$store.state.teacher_filter.motivationBanners\n    },\n    specialityBanners() {\n      return this.$store.state.teacher_filter.specialityBanners\n    },\n    isUserLogged() {\n      return this.$store.getters['user/isUserLogged']\n    },\n    userTag() {\n      return this.$store.getters['user/userTag']\n    },\n  },\n  watch: {\n    isUserLogged(newValue, oldValue) {\n      if (newValue) {\n        this.setBanner()\n      }\n    },\n  },\n  created() {\n    this.setBanner()\n  },\n  methods: {\n    setBanner() {\n      let image = require('~/assets/images/banners/default.svg')\n      if (this.userTag) {\n        this.banner = {\n          ...this.banner,\n          name: this.userTag.headLine,\n          description: this.userTag.description,\n        }\n\n        if (this.userTag?.logo && this.userTag?.logo.length > 0) {\n          this.banner.image = this.userTag.logo\n        }\n\n        return\n      }\n\n      if (this.selectedMotivation) {\n        const motivationBanner = this.motivationBanners.find(\n          (item) => item.id === this.selectedMotivation.id\n        )\n\n        if (motivationBanner) {\n          image = motivationBanner?.image\n            ? require(`~/assets/images/banners/${motivationBanner.image}`)\n            : image\n\n          this.banner = {\n            ...this.selectedMotivation,\n            image,\n            name: this.selectedMotivation.motivationName,\n            description: this.$t(motivationBanner.description),\n          }\n        }\n\n        if (this.selectedSpecialities.length === 1) {\n          const speciality = this.selectedMotivation.specialities.find(\n            (item) => item.id === this.selectedSpecialities[0].id\n          )\n          const specialityBanner = this.specialityBanners.find(\n            (item) => item.id === speciality.id\n          )\n\n          if (speciality) {\n            this.banner = {\n              ...speciality,\n              image: specialityBanner?.image\n                ? require(`~/assets/images/banners/${specialityBanner.image}`)\n                : image,\n              props: specialityBanner?.props,\n            }\n          }\n        }\n        return\n      }\n      return (this.banner = { image })\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TeacherListingBanner.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TeacherListingBanner.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TeacherListingBanner.vue?vue&type=template&id=5a0a35ec&scoped=true&\"\nimport script from \"./TeacherListingBanner.vue?vue&type=script&lang=js&\"\nexport * from \"./TeacherListingBanner.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./TeacherListingBanner.vue?vue&type=style&index=0&id=5a0a35ec&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"5a0a35ec\",\n  \"d13121ee\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VImg})\n", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TeacherListingBanner.vue?vue&type=style&index=0&id=5a0a35ec&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".banner[data-v-5a0a35ec]{position:relative;display:flex;justify-content:space-between;min-height:125px;padding:8px 8px 0 32px;line-height:1.333}@media only screen and (min-width:992px)and (max-width:1439px){.banner[data-v-5a0a35ec]{padding:5px 15px 0 20px}}@media only screen and (max-width:767px){.banner[data-v-5a0a35ec]{flex-direction:column}}@media only screen and (max-width:639px){.banner[data-v-5a0a35ec]{padding:16px 16px 0}}.banner[data-v-5a0a35ec]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;opacity:.1;border-radius:16px}.banner-content[data-v-5a0a35ec]{position:relative;display:flex;flex-direction:column;justify-content:center;padding:15px 10px 20px 0}@media only screen and (min-width:768px){.banner-content[data-v-5a0a35ec]{max-width:600px;min-width:296px}}@media only screen and (max-width:639px){.banner-content[data-v-5a0a35ec]{padding:0 0 15px}}.banner-title[data-v-5a0a35ec]{margin-bottom:8px;font-size:24px;font-weight:700;color:var(--v-success-base);background:linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));background:-webkit-linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));-webkit-background-clip:text;-webkit-text-fill-color:transparent}@media only screen and (min-width:992px)and (max-width:1439px){.banner-title[data-v-5a0a35ec]{font-size:22px}}@media only screen and (max-width:639px){.banner-title[data-v-5a0a35ec]{font-size:20px}}.banner-text[data-v-5a0a35ec]{font-weight:300;font-size:14px;letter-spacing:-.002em}@media only screen and (max-width:767px){.banner-image[data-v-5a0a35ec]{justify-content:center}.banner-image .v-image[data-v-5a0a35ec]{max-height:90px!important}}.banner-image-helper[data-v-5a0a35ec]{width:362px}@media only screen and (max-width:1439px){.banner-image-helper[data-v-5a0a35ec]{width:250px}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AC5CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAEA;AAGA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAhCA;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AACA;AAMA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAEA;AACA;AAHA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAIA;AAEA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AAGA;AACA;AAGA;AACA;AAEA;AAGA;AALA;AAOA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAzDA;AAlDA;;AC1CA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}