(window.webpackJsonp=window.webpackJsonp||[]).push([[80,95],{1414:function(e,t,r){var content=r(1453);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(19).default)("1f907d7b",content,!0,{sourceMap:!1})},1427:function(e,t,r){"use strict";r.r(t);r(31);var n={name:"StarRating",props:{value:{type:Number,required:!0},large:{type:Boolean,required:!1}},data:function(){return{iconFilledStar:"".concat(r(91),"#filledStar"),iconFilledHalfStar:"".concat(r(91),"#filledHalfStar")}},computed:{width:function(){return this.large?20:12},height:function(){return this.large?20:12},value_:function(){return Math.round(10*this.value)/10},isRoundToLess:function(){var e=Math.round(this.value_%1*10);return e<=5&&0!==e},roundToLessHalf:function(){return this.isRoundToLess?Math.floor(2*this.value_)/2:Math.ceil(2*this.value_)/2},stars:function(){return this.isRoundToLess?Math.floor(this.roundToLessHalf):Math.ceil(this.roundToLessHalf)},isHasHalf:function(){return this.isRoundToLess&&5!==this.value_||this.value_<.5}}},l=(r(1452),r(22)),component=Object(l.a)(n,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{class:["score",{"score--large":e.large}]},[r("span",[e._v(e._s(e.value_.toFixed(1)))]),e._v(" "),r("div",[e._l(e.stars,(function(i){return r("svg",{key:i,attrs:{width:e.width,height:e.height,viewBox:"0 0 12 12"}},[r("use",{attrs:{"xlink:href":e.iconFilledStar}})])})),e._v(" "),e.isHasHalf?r("svg",{attrs:{width:e.width,height:e.height,viewBox:"0 0 12 12"}},[r("use",{attrs:{"xlink:href":e.iconFilledHalfStar}})]):e._e()],2)])}),[],!1,null,"1645fb89",null);t.default=component.exports},1452:function(e,t,r){"use strict";r(1414)},1453:function(e,t,r){var n=r(18)(!1);n.push([e.i,".score[data-v-1645fb89]{display:flex;align-items:center;height:18px;font-size:12px;line-height:.8;font-weight:700;letter-spacing:.1px;color:var(--v-orange-base)}@media only screen and (max-width:1215px){.score[data-v-1645fb89]{justify-content:flex-end}}.score>div[data-v-1645fb89]{width:65px;display:flex;margin-left:2px}@media only screen and (max-width:1215px){.score>div[data-v-1645fb89]{width:auto}}.score svg[data-v-1645fb89]:not(:first-child){margin-left:1px}.score--large[data-v-1645fb89]{font-size:18px}@media only screen and (max-width:1215px){.score--large[data-v-1645fb89]{font-size:16px}}.score--large>div[data-v-1645fb89]{width:112px;margin-left:8px}@media only screen and (max-width:1215px){.score--large>div[data-v-1645fb89]{width:84px}}.score--large svg[data-v-1645fb89]:not(:first-child){margin-left:3px}@media only screen and (max-width:1215px){.score--large svg[data-v-1645fb89]:not(:first-child){margin-left:1px}}@media only screen and (max-width:1215px){.score--large svg[data-v-1645fb89]{width:16px!important;height:16px!important}}",""]),e.exports=n},1624:function(e,t,r){var content=r(1716);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(19).default)("66d4ff73",content,!0,{sourceMap:!1})},1715:function(e,t,r){"use strict";r(1624)},1716:function(e,t,r){var n=r(18)(!1);n.push([e.i,"@media(max-width:850px){.lp-teachers-slider .slick-slide{padding:0 15px;transition:all .5s ease-in}}@media only screen and (max-width:850px)and (max-width:639px){.lp-teachers-slider .slick-slide{padding:0}}@media(max-width:850px){.lp-teachers-slider .slick-slide:not(.slick-center){transform:translateZ(0) scale(.8)!important;opacity:.7}}@media only screen and (max-width:850px)and (max-width:991px){.lp-teachers-slider .slick-slide:not(.slick-center){transform:translateZ(0) scale(.9)!important;opacity:.6}}.lp-teachers-slider .slider{padding:15px 30px;margin-top:50px;display:flex;align-items:center;justify-content:space-between}.lp-teachers-slider .slider-card{width:100%;max-width:450px;margin:0 auto;position:relative;box-shadow:0 0 20px rgba(0,0,0,.15);border-radius:20px;display:flex;flex-direction:column;overflow:hidden}.lp-teachers-slider .slider-card__image{width:100%;height:430px;max-height:330px}.lp-teachers-slider .slider-card__img-point{position:absolute;top:20px;right:20px}.lp-teachers-slider .slider-card__content{color:#fff;padding:20px;background:linear-gradient(180.39deg,rgba(171,19,92,.8) -80.41%,rgba(247,173,72,.8) 86.01%)}.lp-teachers-slider .slider-card__content-star{color:#fff}.lp-teachers-slider .slider-card__content-star p{font-size:14px}.lp-teachers-slider .slider-card__content-star #text{display:inline}.lp-teachers-slider .slider-card__content-name{font-size:20px;font-weight:700;line-height:1.3;color:#fff!important;text-decoration:none}.lp-teachers-slider .slider-card__content-text{font-size:18px;line-height:20px;font-weight:300;margin:15px 0}@media only screen and (max-width:639px){.lp-teachers-slider .slider-card__content-text{font-size:16px}}.lp-teachers-slider .slider-card__content-tlabel{color:#fff;font-size:18px}@media only screen and (max-width:639px){.lp-teachers-slider .slider-card__content-tlabel{font-size:16px}}@media only screen and (max-width:479px){.lp-teachers-slider .slider-card__content-tlabel{font-size:14px}}.lp-teachers-slider .slider-card__content-ttext{color:#fff;font-size:18px;font-weight:300;padding-left:10px}@media only screen and (max-width:639px){.lp-teachers-slider .slider-card__content-ttext{font-size:16px}}@media only screen and (max-width:479px){.lp-teachers-slider .slider-card__content-ttext{font-size:14px}}.lp-teachers-slider .slider-card td{padding-top:10px;vertical-align:baseline}.lp-teachers-slider .slider-card .flags-area{position:absolute;right:0;text-align:right;top:0;width:100%}.lp-teachers-slider .slider-card .flag-icon{display:inline-block;font-size:30px;margin:10px}@media(max-width:1439px){.lp-teachers-slider .slider{display:flex;justify-content:center;padding:15px}.lp-teachers-slider .slider-card{max-width:480px;margin:0 auto}.lp-teachers-slider .slider-card__content-text{line-height:20px}}@media(max-width:1170px){.lp-teachers-slider .slider-card{max-width:400px}.lp-teachers-slider .slider-card__image{width:inherit;background-position:0 15%}}@media(max-width:900px){.lp-teachers-slider .slider-card{max-width:480px}}@media only screen and (max-width:639px){.lp-teachers-slider .slider{flex-direction:column;padding:0}}@media(max-width:480px){.lp-teachers-slider .slider-card__image{background-position:50%}}.lp-teachers-slider .slick-arrow{position:absolute;top:50%;background-color:#000;transform:translateY(-50%)}.lp-teachers-slider .slick-arrow.slick-next{right:30px}.lp-teachers-slider .slick-arrow.slick-prev{left:30px}.lp-teachers-slider .slick-dots{margin-top:10px!important}.lp-teachers-slider--dark .slider-card__content{color:#fff;background:var(--v-darkLight-base)}.lp-teachers-slider--dark .slider-card__content-star{color:var(--v-orangeLight-base)}",""]),e.exports=n},1917:function(e,t,r){"use strict";r.r(t);var n=r(1561),l=r.n(n),d=(r(1562),r(1427)),o={name:"TeachersSlider",components:{VueSlickCarousel:l.a,StarRating:d.default},props:{data:{type:Array,default:function(){return[]}},dark:{type:Boolean,default:!0}},data:function(){return{slider:null,sliderSettings:{dots:!1,focusOnSelect:!0,infinite:!0,speed:800,slidesToShow:1,slidesToScroll:1,responsive:[{breakpoint:900,settings:{arrows:!1,dots:!0}},{breakpoint:850,settings:{arrows:!1,dots:!0,centerMode:!0,centerPadding:"100px"}},{breakpoint:639,settings:{arrows:!1,dots:!0,centerMode:!0,centerPadding:"45px"}},{breakpoint:479,settings:{arrows:!1,dots:!0,centerMode:!0,centerPadding:"30px"}}]}}},methods:{getSrcAvatar:function(image){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"avatar.png";return image||r(881)("./".concat(e))}}},c=(r(1715),r(22)),h=r(42),x=r.n(h),f=r(261),component=Object(c.a)(o,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.data.length?n("div",{class:["lp-teachers-slider",{"lp-teachers-slider--dark":e.dark}]},[n("client-only",[n("VueSlickCarousel",e._b({},"VueSlickCarousel",e.sliderSettings,!1),e._l(e.data,(function(t,l){return n("div",{key:l},[n("div",{staticClass:"slider"},[n("div",{staticClass:"slider-card"},[n("nuxt-link",{attrs:{to:{path:"/teacher/"+t.username},target:"_blank"}},[n("v-img",{staticClass:"slider-card__image",attrs:{src:e.getSrcAvatar(t.picture),position:"50% 30%"}})],1),e._v(" "),t.languagesTaught.length?n("div",{staticClass:"flags-area d-flex justify-end"},e._l(t.languagesTaught,(function(e){return n("div",{key:e.isoCode,staticClass:"flags-item ma-1 elevation-2 rounded overflow-hidden"},[n("v-img",{attrs:{src:r(369)("./"+e.isoCode+".svg"),width:"40",height:"30",contain:"",options:{rootMargin:"50%"}}})],1)})),0):e._e(),e._v(" "),n("div",{staticClass:"slider-card__content"},[n("div",{staticClass:"d-flex justify-space-between align-center"},[n("nuxt-link",{staticClass:"slider-card__content-name text-uppercase",attrs:{to:{path:"/teacher/"+t.username},target:"_blank"}},[e._v("\n                  "+e._s(t.firstName)+" "+e._s(t.lastName)+"\n                ")]),e._v(" "),n("div",{staticClass:"slider-card__content-star"},[n("star-rating",{staticClass:"mr-1 mr-sm-2 mr-md-0",attrs:{value:t.averageRatings}}),e._v(" "),n("p",{staticClass:"mb-0"},[e._v("\n                    ("+e._s(e.$tc("review",t.countFeedbacks))+")\n                  ")])],1)],1),e._v(" "),n("div",{staticClass:"slider-card__content-text"},[e._v("\n                "+e._s(t.shortSummary)+"\n              ")]),e._v(" "),n("table",[n("tr",[n("td",{class:["slider-card__content-tlabel",{"dark--text":!e.dark}]},[e._v("\n                    "+e._s(e.$t("teaches"))+":\n                  ")]),e._v(" "),n("td",{class:["slider-card__content-ttext",{"dark--text":!e.dark}]},[e._v("\n                    "+e._s(t.languagesTaught.map((function(i){return i.name})).join(", "))+"\n                  ")])]),e._v(" "),n("tr",[n("td",{class:["slider-card__content-tlabel",{"dark--text":!e.dark}]},[e._v("\n                    "+e._s(e.$t("specialities"))+":\n                  ")]),e._v(" "),n("td",{class:["slider-card__content-ttext",{"dark--text":!e.dark}]},[e._v("\n                    "+e._s(t.specialities)+"\n                  ")])])])])],1)])])})),0)],1)],1):e._e()}),[],!1,null,null,null);t.default=component.exports;x()(component,{StarRating:r(1427).default}),x()(component,{VImg:f.a})}}]);