(window.webpackJsonp=window.webpackJsonp||[]).push([[55],{1659:function(e,t,o){var content=o(1768);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,o(19).default)("ab02e688",content,!0,{sourceMap:!1})},1767:function(e,t,o){"use strict";o(1659)},1768:function(e,t,o){var r=o(18)(!1);r.push([e.i,".viewport-component[data-v-4a5272da]{position:absolute;border-width:4px;border-style:solid}.user-name[data-v-4a5272da]{position:absolute;top:0;right:0;padding:4px 13px;line-height:1;background:#fff;font-size:12px;font-weight:500;box-shadow:0 0 5px rgba(0,0,0,.2);border:none;border-bottom-left-radius:6px;z-index:2}.user-name--tl[data-v-4a5272da]{top:0;right:auto;left:0;border-bottom-left-radius:0;border-bottom-right-radius:6px}.user-name--br[data-v-4a5272da]{right:0;border-top-left-radius:6px}.user-name--bl[data-v-4a5272da],.user-name--br[data-v-4a5272da]{top:auto;bottom:0;border-bottom-left-radius:0}.user-name--bl[data-v-4a5272da]{right:auto;left:0;border-top-right-radius:6px}",""]),e.exports=r},1937:function(e,t,o){"use strict";o.r(t);o(31);var r=o(695),n={name:"Viewport",props:{zoomAsset:{type:Object,required:!0},zoomOtherAsset:{type:Object,required:!0},viewportWidth:{type:Number,required:!0},viewportHeight:{type:Number,required:!0}},data:function(){return{isDevice:Object(r.a)()}},computed:{role:function(){return this.$store.getters["classroom/role"]},color:function(){return"teacher"===this.role?"rgba(60, 135, 248, 0.4)":"rgba(127, 184, 2, 0.4)"},isOtherUserJoinedClassroom:function(){return this.$store.getters["classroom/isOtherUserJoinedClassroom"]},otherScreenTop:function(){return(this.zoomOtherAsset.asset.y-this.zoomAsset.asset.y)*this.zoomAsset.asset.zoomIndex},otherScreenLeft:function(){return(this.zoomOtherAsset.asset.x-this.zoomAsset.asset.x)*this.zoomAsset.asset.zoomIndex},otherScreenWidth:function(){return this.zoomOtherAsset.asset.screen.width/this.zoomOtherAsset.asset.zoomIndex*this.zoomAsset.asset.zoomIndex},otherScreenHeight:function(){return this.zoomOtherAsset.asset.screen.height/this.zoomOtherAsset.asset.zoomIndex*this.zoomAsset.asset.zoomIndex},isOtherScreenAllowed:function(){var e,t;return!this.isDevice&&!(null===(e=this.zoomOtherAsset)||void 0===e||null===(t=e.asset)||void 0===t||!t.screen)&&this.isOtherUserJoinedClassroom&&this.zoomAsset.asset.screen.width>this.zoomOtherAsset.asset.screen.width},styles:function(){return{top:"".concat(this.otherScreenTop,"px"),left:"".concat(this.otherScreenLeft,"px"),width:"".concat(this.otherScreenWidth,"px"),height:"".concat(this.otherScreenHeight,"px"),borderColor:this.color}},username:function(){return this.zoomOtherAsset.asset.username},isTopOffset:function(){return this.otherScreenTop<0},isLeftOffset:function(){return this.otherScreenLeft<0},isBottomOffset:function(){return this.viewportHeight+this.zoomAsset.asset.y<this.otherScreenHeight+this.zoomOtherAsset.asset.y},isRightOffset:function(){return this.viewportWidth+this.zoomAsset.asset.x<this.otherScreenWidth+this.zoomOtherAsset.asset.x}}},h=(o(1767),o(22)),component=Object(h.a)(n,(function(){var e=this,t=e.$createElement,o=e._self._c||t;return e.isOtherScreenAllowed?o("div",{staticClass:"viewport-component",style:e.styles},[o("div",{class:["user-name",{"user-name--br":e.isTopOffset&&!e.isRightOffset,"user-name--tl":e.isRightOffset&&!e.isTopOffset,"user-name--bl":e.isTopOffset&&e.isRightOffset}]},[e._v("\n    "+e._s(e.$t("classroom_user_screen",{username:e.username}))+"\n  ")])]):e._e()}),[],!1,null,"4a5272da",null);t.default=component.exports}}]);