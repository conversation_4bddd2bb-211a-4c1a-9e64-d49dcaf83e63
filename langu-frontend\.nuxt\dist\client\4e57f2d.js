(window.webpackJsonp=window.webpackJsonp||[]).push([[52,49],{1389:function(e,t,o){"use strict";o.r(t);o(31),o(35),o(60);var n=o(1528),r=o(266),l=o(1428),d=(o(1449),{components:{VueDraggableResizable:n.default},mixins:[l.a],props:{asset:{type:Object,required:!0},childHeaderHeight:{type:Number,default:0},lockAspectRatio:{type:Boolean,default:!0},scalable:{type:Boolean,default:!0},hoverEnabled:{type:Boolean,default:!0},isDraggableProp:{type:Boolean,default:!0},hideResizeIcon:{type:Boolean,default:!1},handles:{type:Array,default:function(){return["tl","tm","tr","mr","br","bm","bl","ml"]}}},data:function(){return{width:r.j,height:r.i,top:50,left:500,isHovered:!1,isHoveredByAsset:!1,index:5,eventBodyClass:null,allowIndexChange:!0,resizable:!0,draggable:!0,synchronizeable:!0,viewportWidth:window.innerWidth,viewportHeight:window.innerHeight,offset:5}},computed:{isCanvasOversizeX:function(){return r.n>this.viewportWidth},isCanvasOversizeY:function(){return r.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return r.k*this.zoomIndex>this.viewportHeight},getRoleHoverColor:function(){return"teacher"===this.role?"var(--v-teacherColor-base)":"var(--v-studentColor-base)"},enabledResizeable:function(){return this.resizable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},enabledDraggable:function(){return this.isDraggableProp&&this.draggable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},maxIndex:function(){return this.$store.state.classroom.maxIndex},zoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},zoomIndex:function(){var e,t;return null!==(e=null===(t=this.zoom)||void 0===t?void 0:t.zoomIndex)&&void 0!==e?e:1},type:function(){var e,t;return null===(e=this.asset)||void 0===e||null===(t=e.asset)||void 0===t?void 0:t.type},topScale:function(){return"toolbar"===this.type?this.top<0||this.top+this.height>this.viewportHeight?this.viewportHeight-this.height-70:this.top:this.synchronizeable?this.top-this.zoom.y:this.top},leftScale:function(){return"toolbar"===this.type?this.left+this.width>this.viewportWidth||this.left<0?this.viewportWidth-this.width-15:this.left:this.synchronizeable?this.left-this.zoom.x:this.left},isLockedForStudent:function(){return this.$store.getters["classroom/isLocked"]&&"student"===this.role},isSocketConnected:function(){return this.$store.state.socket.isConnected}},watch:{"asset.asset":function(e){this.move(e)}},mounted:function(){this.move(this.asset.asset)},methods:{mouseenter:function(){var e,t;(this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!0,this.socketAssetMoved({isHovered:!0})),"twilio"===this.type||"tokbox"===this.type||"editor"===this.type)&&(this.$store.commit("classroom/setCursorNameBeforeChange",(null===(e=this.$store.getters["classroom/userParams"])||void 0===e?void 0:e.cursor)||"cursor-pointer"),this.$store.commit("classroom/setToolNameBeforeChange",(null===(t=this.$store.getters["classroom/userParams"])||void 0===t?void 0:t.tool)||"pointer"),this.setTool("pointer","cursor-pointer"))},mouseleave:function(){this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!1,this.socketAssetMoved({isHovered:!1})),"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||this.setTool(this.$store.state.classroom.toolNameBeforeChange,this.$store.state.classroom.cursorNameBeforeChange)},onIndex:function(){this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{index:this.index}}),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{index:this.index}})},updateAsset:function(e,t){this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{left:this.synchronizeable?e+this.zoom.x:e,top:this.synchronizeable?t+this.zoom.y:t,index:this.index}})},onDrag:function(e,t){if(this.synchronizeable){if(this.left=e+this.zoom.x,this.top=t+this.zoom.y,this.allowIndexChange){var o=document.body;o.classList.contains(this.eventBodyClass)||(this.eventBodyClass="dragging",o.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var n={left:this.left,top:this.top,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:n}),this.socketAssetMoved(n)}},onDragStop:function(e,t){var o=document.body;o.classList.contains(this.eventBodyClass)&&(o.classList.remove(this.eventBodyClass),this.eventBodyClass=null),this.allowIndexChange=!0,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?e+this.zoom.x:e,top:this.synchronizeable?t+this.zoom.y:t,index:this.index}})},onResize:function(e,t,o,n,r){if(this.synchronizeable){if(this.left=e+this.zoom.x,this.top=t+this.zoom.y,this.width=o,this.height=n-this.childHeaderHeight,this.allowIndexChange){var l=document.body;l.classList.contains(this.eventBodyClass)||(this.eventBodyClass=r.split(" ")[1],l.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var d={left:this.left,top:this.top,width:this.width,height:this.height,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:d}),this.socketAssetMoved(d)}},onResizeStop:function(e,t,o,n){this.eventBodyClass&&document.body.classList.remove(this.eventBodyClass),this.allowIndexChange=!0,this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?e+this.zoom.x:e,top:this.synchronizeable?t+this.zoom.y:t,width:o,height:n-this.childHeaderHeight,index:this.index}})},socketAssetMoved:function(e){this.isSocketConnected&&this.$socket.emit("asset-moved",{id:this.asset.id,lessonId:this.asset.lessonId,asset:e})},move:function(e){if(void 0!==e.width?this.width=e.width:"editor"===this.type&&(this.width=.66*(this.isCanvasOversizeX?this.viewportWidth:r.n)),void 0!==e.height)this.height=e.height;else{if("editor"===this.type){var t=.8*(this.isCanvasOversizeY?this.viewportHeight:r.k);t>1200&&(t=1200),t<400&&(t=400),this.height=t-2*this.offset}"audio"===this.type&&(this.height=0)}void 0!==e.top?this.top=e.top:"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||(this.top=this.offset),void 0!==e.left?this.left=e.left:("twilio"!==this.type&&"tokbox"!==this.type||(this.left=(this.isCanvasOversizeX?this.viewportWidth:r.n)-this.width-this.offset),"editor"===this.type&&(this.left=this.offset)),void 0!==e.index&&(this.index=e.index),void 0!==e.resizable&&(this.resizable=e.resizable),void 0!==e.draggable&&(this.draggable=e.draggable),void 0!==e.synchronizeable&&(this.synchronizeable=e.synchronizeable),void 0!==e.isHovered&&(this.isHoveredByAsset=e.isHovered)}}}),h=o(22),component=Object(h.a)(d,(function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{on:{mouseenter:e.mouseenter,mouseleave:e.mouseleave}},[o("vue-draggable-resizable",{ref:"vueDraggableResizable",class:{student:"student"===e.role,teacher:"teacher"===e.role,"hide-resize-icon":e.hideResizeIcon},style:{outline:e.isHoveredByAsset||e.isHovered?"3px solid "+e.getRoleHoverColor:"none"},attrs:{draggable:e.enabledDraggable,resizable:e.enabledResizeable,w:e.width,h:e.height+e.childHeaderHeight,x:e.leftScale,y:e.topScale,z:e.index,"zoom-index":e.scalable?e.zoom.zoomIndex:1,"zoom-x":e.zoom.x,"zoom-y":e.zoom.y,"child-header-height":e.childHeaderHeight,"lock-aspect-ratio":e.lockAspectRatio,handles:e.handles},on:{dragging:e.onDrag,resizing:e.onResize,dragstop:e.onDragStop,resizestop:e.onResizeStop,"update-asset":e.updateAsset},nativeOn:{click:function(t){return e.onIndex.apply(null,arguments)}}},[e._t("default",null,{onIndex:e.onIndex})],2)],1)}),[],!1,null,null,null);t.default=component.exports},1428:function(e,t,o){"use strict";o(35),o(81),o(23),o(6),o(24),o(38);var n={computed:{role:function(){return this.$store.getters["classroom/role"]}},methods:{setTool:function(e,t){this.$store.commit("classroom/enableContainerComponent","pointer"===e),this.$socket.emit("cursor-moved",{tool:e,cursor:t.replace(/(-cursor|cursor-)/i,""),lessonId:this.$store.state.classroom.lessonId}),this.$store.commit("classroom/setUserTool",e),this.$store.commit("classroom/setUserCursor",t);var o=document.body,n=o.classList;this.removeCursors(n),o.classList.add("".concat(this.role,"-").concat(t)),this.classList=o.classList},removeCursors:function(e){e.forEach((function(e){e.includes("cursor")&&document.body.classList.remove(e)}))}}},r=o(22),component=Object(r.a)(n,undefined,undefined,!1,null,null,null);t.a=component.exports},1449:function(e,t,o){var content=o(1477);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,o(19).default)("07a1f444",content,!0,{sourceMap:!1})},1477:function(e,t,o){var n=o(18),r=o(265),l=o(870),d=n(!1),h=r(l);d.push([e.i,'.vdr{touch-action:none;position:absolute;box-sizing:border-box;border:none}.handle{display:block!important}.resizable.vdr:not(.hide-resize-icon):after{content:"";position:absolute;bottom:4px;right:2px;width:8px;height:8px;background-image:url('+h+');background-size:contain;background-repeat:no-repeat;background-position:50%;opacity:.9;z-index:99}.vdr .handle{box-sizing:border-box;position:absolute;width:12px;height:12px;border:none;background-color:transparent;z-index:10}.vdr .handle-tl{top:-7px;left:-7px}.vdr .handle-tm{top:-7px;margin-left:-5px}.vdr .handle-tr{top:-7px;right:-7px}.vdr .handle-ml{margin-top:-5px;left:-7px}.vdr .handle-mr{margin-top:-5px;right:-7px}.vdr .handle-bl{bottom:-7px;left:-7px}.vdr .handle-bm{bottom:-7px;margin-left:-5px}.vdr .handle-br{bottom:-7px;right:-7px}@media only screen and (max-width:768px){[class*=handle-]:before{content:"";left:-10px;right:-10px;bottom:-10px;top:-10px;position:absolute}}.vdr .handle-bm,.vdr .handle-tm{width:100%;left:5px}.vdr .handle-ml,.vdr .handle-mr{height:100%;top:5px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:14px;height:14px;z-index:11}@media only screen and (max-width:1199px){.vdr .handle-bm,.vdr .handle-tm{height:15px}.vdr .handle-ml,.vdr .handle-mr{width:15px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:15px;height:15px}.vdr .handle-tl{top:-10px;left:-10px}.vdr .handle-tm{top:-10px;margin-left:-5px}.vdr .handle-tr{top:-10px;right:-10px}.vdr .handle-ml{margin-top:-5px;left:-10px}.vdr .handle-mr{margin-top:-5px;right:-10px}.vdr .handle-bl{bottom:-10px;left:-10px}.vdr .handle-bm{bottom:-10px;margin-left:-5px}.vdr .handle-br{bottom:-10px;right:-10px}}',""]),e.exports=d},1585:function(e,t,o){var content=o(1683);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,o(19).default)("77e2f3f6",content,!0,{sourceMap:!1})},1682:function(e,t,o){"use strict";o(1585)},1683:function(e,t,o){var n=o(18)(!1);n.push([e.i,".stream-controls{position:absolute;left:50%;bottom:-38px;z-index:10;transform:translateX(-50%)}.stream-controls .toolbar-button-wrapper{width:40px;height:40px}.stream-controls .toolbar-button-wrapper button svg{margin:0 auto}.stream-controls .toolbar-button-wrapper button:focus{outline:none}.stream-controls .toolbar-button-wrapper-full-screen button{padding:9px}.stream-controls .hover-btn-info{left:50%;right:auto;top:auto;bottom:-36px;transform:translateX(-50%)}.stream-controls .hover-btn-info:after{left:50%;top:-9px;right:auto;border:5px solid transparent;border-bottom-color:#444;transform:translateX(-50%)}.stream-controls .stream-controls-switch{display:flex;justify-content:center;align-items:center;margin-top:-6px;padding:10px 6px 3px;background:#fff;border:none;border-radius:0 0 6px 6px;font-size:13px;line-height:1;outline:none;box-shadow:0 2px 10px rgba(0,0,0,.25)}.stream-controls .stream-controls-switch .toolbar-button-wrapper{width:18px;height:18px}.stream-controls .stream-controls-switch .toolbar-button-wrapper:not(:last-child){margin-right:4px}.stream-controls .stream-controls-switch .toolbar-button-item{min-width:18px!important;font-size:13px!important;border-radius:50%!important;overflow:hidden}.stream-controls .stream-controls-switch .toolbar-button-item--selected{color:#fff;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%)!important}.toolbar-button-icon.hand-raised{fill:#ffc107!important}.toolbar-button-icon.chat-enabled,.toolbar-button-icon.participants-enabled{fill:#2196f3!important}",""]),e.exports=n},1704:function(e,t,o){"use strict";o.r(t);var n={name:"VideoActions",props:{settings:{type:Object,required:!0},isJoined:{type:Boolean,required:!0},isScreenShareDisabled:{type:Boolean,default:!1},type:{type:String,required:!0}},computed:{role:function(){return this.$store.getters["classroom/role"]}}},r=(o(1682),o(22)),component=Object(r.a)(n,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"stream-controls"},[n("div",{staticClass:"video-window-buttons-wrap",attrs:{id:"video-window-buttons"}},[n("div",{staticClass:"stream-controls-wrapper cursor-auto"},[n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-video":"",type:"button",disabled:!e.isJoined},on:{click:function(t){return e.$emit("toggle-video")}}},[e.settings.isVideoEnabled?n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"51",height:"33",viewBox:"0 0 51 33"}},[n("use",{attrs:{"xlink:href":o(884)+"#videocam"}})]):e.settings.isVideoEnabled?e._e():n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"39",height:"33",viewBox:"0 0 39 33"}},[n("use",{attrs:{"xlink:href":o(884)+"#videocam_off"}})])]),e._v(" "),n("div",{staticClass:"hover-btn-info"},[e.settings.isVideoEnabled?[e._v("\n            "+e._s(e.$t("turn_off_camera"))+"\n          ")]:[e._v("\n            "+e._s(e.$t("turn_on_camera"))+"\n          ")]],2)]),e._v(" "),n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-audio":"",type:"button",disabled:!e.isJoined},on:{click:function(t){return e.$emit("toggle-audio")}}},[e.settings.isMuted?e._e():n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"23",height:"33",viewBox:"0 0 23 33"}},[n("use",{attrs:{"xlink:href":o(883)+"#microphone"}})]),e._v(" "),e.settings.isMuted?n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"31",height:"34",viewBox:"0 0 31 34"}},[n("use",{attrs:{"xlink:href":o(883)+"#microphone-off"}})]):e._e()]),e._v(" "),n("div",{staticClass:"hover-btn-info"},[e.settings.isMuted?[e._v("\n            "+e._s(e.$t("unmute_microphone"))+"\n          ")]:[e._v("\n            "+e._s(e.$t("mute_microphone"))+"\n          ")]],2)]),e._v(" "),n("div",{staticClass:"toolbar-button-wrapper toolbar-button-wrapper-full-screen"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-full-screen":"",type:"button",disabled:!e.isJoined},on:{click:function(t){return e.$emit("toggle-full-screen")}}},[e.settings.isFullscreenEnabled?e._e():n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"31",height:"31",viewBox:"0 0 31 31"}},[n("use",{attrs:{"xlink:href":o(882)+"#full_screen"}})]),e._v(" "),e.settings.isFullscreenEnabled?n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"31",height:"31",viewBox:"0 0 31 31"}},[n("use",{attrs:{"xlink:href":o(882)+"#window_screen"}})]):e._e()]),e._v(" "),n("div",{staticClass:"hover-btn-info"},[e.settings.isFullscreenEnabled?[e._v("\n            "+e._s(e.$t("leave_full_screen_video"))+"\n          ")]:[e._v("\n            "+e._s(e.$t("full_screen_video"))+"\n          ")]],2)]),e._v(" "),n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-screen-share":"",type:"button",disabled:e.isScreenShareDisabled||!e.isJoined&&"twilio"===e.type},on:{click:function(t){return e.$emit("toggle-screen-share")}}},[e.settings.isScreenShareEnabled?n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"38",height:"35",viewBox:"0 0 38 35"}},[n("use",{attrs:{"xlink:href":o(875)+"#not_share"}})]):e._e(),e._v(" "),e.settings.isScreenShareEnabled?e._e():n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"37",height:"28",viewBox:"0 0 37 28"}},[n("use",{attrs:{"xlink:href":o(875)+"#share"}})])]),e._v(" "),n("div",{staticClass:"hover-btn-info"},[e.isScreenShareDisabled?[e._v("\n            "+e._s(e.$t("share_is_disabled"))+"\n          ")]:[e.settings.isScreenShareEnabled?[e._v("\n              "+e._s(e.$t("stop_screenshare"))+"\n            ")]:[e._v("\n              "+e._s(e.$t("share_my_screen"))+"\n            ")]]],2)]),e._v(" "),"whereby"===e.type?n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-hand-raise":"",type:"button",disabled:!e.isJoined},on:{click:function(t){return e.$emit("toggle-hand-raise")}}},[n("svg",{class:["toolbar-button-icon",{"hand-raised":e.settings.isHandRaised}],attrs:{width:"24",height:"33",viewBox:"0 0 24 33"}},[n("use",{attrs:{"xlink:href":o(933)+"#hand"}})])]),e._v(" "),n("div",{staticClass:"hover-btn-info"},[e.settings.isHandRaised?[e._v("\n            "+e._s(e.$t("lower_hand"))+"\n          ")]:[e._v("\n            "+e._s(e.$t("raise_hand"))+"\n          ")]],2)]):e._e(),e._v(" "),"whereby"===e.type?n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-chat":"",type:"button",disabled:!e.isJoined},on:{click:function(t){return e.$emit("toggle-chat")}}},[n("svg",{class:["toolbar-button-icon",{"chat-enabled":e.settings.isChatEnabled}],attrs:{width:"28",height:"28",viewBox:"0 0 28 28"}},[n("use",{attrs:{"xlink:href":o(932)+"#chat"}})])]),e._v(" "),n("div",{staticClass:"hover-btn-info"},[e.settings.isChatEnabled?[e._v("\n            "+e._s(e.$t("hide_chat"))+"\n          ")]:[e._v("\n            "+e._s(e.$t("show_chat"))+"\n          ")]],2)]):e._e(),e._v(" "),"whereby"===e.type?n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-participants":"",type:"button",disabled:!e.isJoined},on:{click:function(t){return e.$emit("toggle-participants")}}},[n("svg",{class:["toolbar-button-icon",{"participants-enabled":e.settings.isParticipantsEnabled}],attrs:{width:"32",height:"28",viewBox:"0 0 32 28"}},[n("use",{attrs:{"xlink:href":o(934)+"#participants"}})])]),e._v(" "),n("div",{staticClass:"hover-btn-info"},[e.settings.isParticipantsEnabled?[e._v("\n            "+e._s(e.$t("hide_participants"))+"\n          ")]:[e._v("\n            "+e._s(e.$t("show_participants"))+"\n          ")]],2)]):e._e()])])])}),[],!1,null,null,null);t.default=component.exports},2234:function(e,t,o){"use strict";o.r(t);o(7),o(8),o(9),o(14),o(6),o(15);var n=o(2),r=o(10),l=(o(20),o(23),o(62),o(859)),d=o(1389),h=o(1704);function c(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,o)}return t}function m(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?c(Object(source),!0).forEach((function(t){Object(n.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):c(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var v={name:"Tokbox",components:{ClassroomContainer:d.default,VideoActions:h.default},props:{file:{type:Object,required:!0},screenShareAsset:{type:Object,required:!0},zoomOtherAsset:{type:Object,required:!0}},data:function(){return{OT:{},isJoined:!1,isLocalScreenShareEnabled:!1,isRemoteScreenShareEnabled:!1,settings:{isScreenShareEnabled:!1,isFullscreenEnabled:!1},session:null,publisher:null,screenSharingPublisher:null,screenSharingNotSupported:!1,isMediaChanging:!1}},computed:{apiKey:function(){return this.$store.getters["classroom/tokboxApiKey"]},sessionId:function(){return this.$store.getters["classroom/tokboxSessionId"]},token:function(){return this.$store.getters["classroom/tokboxToken"]},role:function(){return this.$store.getters["classroom/role"]},pubOptions:function(){return{insertMode:"append",width:"100%",height:"100%",usePreviousDeviceSelection:!0,name:this.$store.getters["classroom/userName"],style:{nameDisplayMode:"off"},fitMode:"contain",showControls:!1,frameRate:15}},isVideoEnabled:function(){var e,t,o,n;return null===(e=null===(t=this.file.asset)||void 0===t||null===(o=t.settings)||void 0===o||null===(n=o[this.role])||void 0===n?void 0:n.isVideoEnabled)||void 0===e||e},isMuted:function(){var e,t,o,n;return null!==(e=null===(t=this.file.asset)||void 0===t||null===(o=t.settings)||void 0===o||null===(n=o[this.role])||void 0===n?void 0:n.isMuted)&&void 0!==e&&e},maxIndex:function(){return this.$store.state.classroom.maxIndex}},beforeMount:function(){var e=this;return Object(r.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,o(2073);case 2:return e.OT=t.sent,t.next=5,e.init();case 5:window.addEventListener("beforeunload",e.closeStream),window.addEventListener("pagehide",e.closeStream),document.addEventListener("fullscreenchange",e.fullscreenChangeHandler);case 8:case"end":return t.stop()}}),t)})))()},beforeDestroy:function(){document.removeEventListener("fullscreenchange",this.fullscreenChangeHandler),this.closeStream()},methods:{init:function(){var e=this;return new Promise((function(t){if(!e.OT.checkSystemRequirements())return alert("Sorry. Your browser does not support our streaming protocols. Please try latest version of Google Chrome or Mozilla Firefox.");e.OT.checkScreenSharingCapability((function(t){t.supported&&!1!==t.extensionRegistered||(e.screenSharingNotSupported=!0)})),e.session=e.OT.initSession(e.apiKey,e.sessionId),e.session.connect(e.token,(function(t){if(t)return e.errorHandler(t);e.isJoined=!0,e.publisher=e.OT.initPublisher("tokbox-local-stream-placeholder",m(m({},e.pubOptions),{},{publishAudio:!e.isMuted,publishVideo:e.isVideoEnabled})),e.session.publish(e.publisher,e.errorHandler),e.publisher.on({streamDestroyed:function(t){t.preventDefault(),e.session&&e.publisher&&(e.session.disconnect(),e.session.unpublish(e.publisher),e.session.off())}}),e.session.on({streamCreated:function(t){var o="tokbox-remote-stream-placeholder";"screen"===t.stream.videoType&&(o="tokbox-remote-screenshare-placeholder",e.isRemoteScreenShareEnabled=!0,e.updateData(e.screenShareAsset.id,{index:e.maxIndex+1})),e.session.subscribe(t.stream,o,{insertMode:"append",fitMode:"contain",style:{audioLevelDisplayMode:"off",buttonDisplayMode:"off",nameDisplayMode:"off"},mirror:!1},(function(e){e&&alert("Sorry, we encountered an error trying to connect you to the remote stream. Please report to Langu Team. Error code: ".concat(e.code,", details: ").concat(e.message))}))},streamPropertyChanged:function(t){var o={settings:m(m({},e.file.asset.settings),{},Object(n.a)({},e.role,{isVideoEnabled:e.isVideoEnabled,isMuted:e.isMuted}))};!e.isMediaChanging||"hasVideo"!==t.changedProperty&&"hasAudio"!==t.changedProperty||("hasVideo"===t.changedProperty&&(o.settings[e.role].isVideoEnabled=t.newValue),"hasAudio"===t.changedProperty&&(o.settings[e.role].isMuted=!t.newValue),e.updateData(e.file.id,o))},streamDestroyed:function(t){"screen"===t.stream.videoType&&(e.isRemoteScreenShareEnabled=!1)}})})),t()}))},errorHandler:function(e){if(e){this.isJoined=!1;var t=1500===e.code?"We are unable to access your video camera. Either the browser has not been given permissions or the camera is in use by another program.":e.message;alert(t)}},toggleVideo:function(){this.isMediaChanging=!0,this.publisher.publishVideo(!this.isVideoEnabled)},toggleAudio:function(){this.isMediaChanging=!0,this.publisher.publishAudio(this.isMuted)},toggleFullScreen:function(){this.settings.isFullscreenEnabled=!this.settings.isFullscreenEnabled,Object(l.switchFullScreen)(this.settings.isFullscreenEnabled)},toggleScreenShare:function(){var e=this;this.settings.isScreenShareEnabled=!this.settings.isScreenShareEnabled,this.isLocalScreenShareEnabled=!this.isLocalScreenShareEnabled,this.settings.isScreenShareEnabled?(this.screenSharingPublisher=this.OT.initPublisher("tokbox-local-screenshare-placeholder",{insertMode:"append",videoSource:"screen",showControls:!1,resolution:"640x480",frameRate:7}),this.session.publish(this.screenSharingPublisher,this.errorHandler),this.screenSharingPublisher.on({streamDestroyed:function(t){t.preventDefault(),e.screenSharingPublisher.disconnect(),e.session.unpublish(e.screenSharingPublisher)}})):(this.screenSharingPublisher.destroy(),this.screenSharingPublisher=null)},closeStream:function(){var e=this;return Object(r.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.publisher){t.next=3;break}return t.next=3,e.publisher.destroy();case 3:if(!e.screenSharingPublisher){t.next=6;break}return t.next=6,e.screenSharingPublisher.destroy();case 6:case"end":return t.stop()}}),t)})))()},switchVideoPlayer:function(e){this.$store.dispatch("classroom/deleteAsset",this.file),this.$store.dispatch("classroom/createAsset",m(m({},this.file.asset),{},{type:e}))},fullscreenChangeHandler:function(){document.fullscreenElement||(this.settings.isFullscreenEnabled=!1)},updateData:function(e,t){var o=this;this.$store.commit("classroom/moveAsset",{id:e,asset:t}),this.$store.dispatch("classroom/moveAsset",{id:e,lessonId:this.file.lessonId,asset:t}).finally((function(){return o.isMediaChanging=!1}))}}},f=o(22),component=Object(f.a)(v,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("classroom-container",{attrs:{asset:e.file,"hover-enabled":!1}},[n("div",{class:["tokbox-component cursor-before-grab",{"video-window--is-fullscreen":e.settings.isFullscreenEnabled}],attrs:{id:"video-window"}},[n("div",{staticClass:"local-stream",attrs:{id:"tokbox-local-stream-placeholder"}}),e._v(" "),n("div",{staticClass:"remote-stream",attrs:{id:"tokbox-remote-stream-placeholder"}}),e._v(" "),n("video-actions",{attrs:{"is-joined":e.isJoined,settings:Object.assign({},e.settings,{isMuted:e.isMuted,isVideoEnabled:e.isVideoEnabled}),"is-screen-share-disabled":e.isRemoteScreenShareEnabled||e.screenSharingNotSupported,type:e.file.asset.type},on:{"switch-video-player":e.switchVideoPlayer,"toggle-video":e.toggleVideo,"toggle-audio":e.toggleAudio,"toggle-full-screen":e.toggleFullScreen,"toggle-screen-share":e.toggleScreenShare}})],1)]),e._v(" "),n("classroom-container",{directives:[{name:"show",rawName:"v-show",value:e.isLocalScreenShareEnabled||e.isRemoteScreenShareEnabled,expression:"isLocalScreenShareEnabled || isRemoteScreenShareEnabled"}],attrs:{asset:e.screenShareAsset,"hover-enabled":!1}},[n("div",{staticClass:"tokbox-component screenshare-component cursor-before-grab"},[n("div",{staticClass:"user-name"},[e.isLocalScreenShareEnabled?[e._v("\n          "+e._s(e.$t("my_screen"))+"\n        ")]:e._e(),e._v(" "),e.isRemoteScreenShareEnabled?[e._v("\n          "+e._s(e.$t("classroom_user_screen",{username:e.zoomOtherAsset.asset.username}))+"\n        ")]:e._e()],2),e._v(" "),n("div",{staticClass:"remote-stream",attrs:{id:"tokbox-remote-screenshare-placeholder"}}),e._v(" "),n("div",{staticClass:"remote-stream",attrs:{id:"tokbox-local-screenshare-placeholder"}}),e._v(" "),e.isLocalScreenShareEnabled?n("div",{staticClass:"stream-controls stream-controls--screenshare"},[n("div",{staticClass:"video-window-buttons-wrap"},[n("div",{staticClass:"stream-controls-wrapper cursor-auto"},[n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-stop-screen-share":"",type:"button"},on:{click:e.toggleScreenShare}},[n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"38",height:"35",viewBox:"0 0 38 35"}},[n("use",{attrs:{"xlink:href":o(875)+"#not_share"}})])]),e._v(" "),n("div",{staticClass:"hover-btn-info"},[e._v("\n                "+e._s(e.$t("stop_screenshare"))+"\n              ")])])])])]):e._e()])])],1)}),[],!1,null,null,null);t.default=component.exports;installComponents(component,{ClassroomContainer:o(1389).default})}}]);