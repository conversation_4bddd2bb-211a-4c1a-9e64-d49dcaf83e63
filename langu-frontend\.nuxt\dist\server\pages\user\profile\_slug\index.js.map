{"version": 3, "file": "pages/user/profile/_slug/index.js", "sources": ["webpack:///./pages/user/profile/_slug/index.vue?5bb8", "webpack:///./pages/user/profile/_slug/index.vue", "webpack:///./pages/user/profile/_slug/index.vue?9664", "webpack:///./pages/user/profile/_slug/index.vue?92c9"], "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n\nexport default {\n  middleware({ route, redirect }) {\n    const queryArrLength = Object.keys(route.query).length\n\n    let url = `/teacher/${route.params.slug}`\n    let i = 0\n\n    if (queryArrLength > 0) {\n      url += '?'\n\n      for (const property in route.query) {\n        i += 1\n        url += `${property}=${route.query[property]}`\n\n        if (i < queryArrLength) {\n          url += '&'\n        }\n      }\n    }\n\n    return redirect(url)\n  },\n}\n", "import mod from \"-!../../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=727c329f&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"320398d2\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA;;ACLA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}