exports.ids = [26];
exports.modules = {

/***/ 1249:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1335);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("67f245fe", content, true, context)
};

/***/ }),

/***/ 1334:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_VideoInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1249);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_VideoInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_VideoInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_VideoInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_VideoInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1335:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".video-component,.video-component *{cursor:auto!important}.video-component .v-card{padding:70px 15px 20px}.video-component .v-input .v-input__slot{background-color:#fff}.video-component .input-wrap{position:relative}.video-component .input-wrap-error{position:absolute;bottom:8px;left:0;padding-left:16px}.video-component .video-buttons-wrap{display:flex;justify-content:center;align-items:center;width:100%}.video-component button,.video-component button *{cursor:pointer!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1407:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/VideoInput.vue?vue&type=template&id=2169d7bb&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-form',{on:{"submit":function($event){$event.preventDefault();return _vm.submit.apply(null, arguments)}}},[_c('div',{staticClass:"input-wrap"},[_c('text-input',{ref:"input",attrs:{"value":_vm.inputUrl,"type-class":"border-gradient","height":"44","rules":[_vm.rules.required],"placeholder":_vm.$t('enter_youtube_url')},on:{"input":function($event){_vm.inputUrl = $event}}}),_vm._v(" "),(!_vm.isValid)?_c('div',{staticClass:"input-wrap-error v-text-field__details"},[_c('div',{staticClass:"v-messages theme--light error--text",attrs:{"role":"alert"}},[_c('div',{staticClass:"v-messages__wrapper"},[_c('div',{staticClass:"v-messages__message"},[_vm._v("\n            "+_vm._s(_vm.$t('invalid_url_or_video_id'))+"\n          ")])])])]):_vm._e()],1),_vm._v(" "),_c('div',{staticClass:"video-buttons-wrap"},[_c('v-btn',{attrs:{"color":"primary","small":"","type":"submit"}},[_vm._v("\n      "+_vm._s(_vm.$t('load_video'))+"\n    ")])],1)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/classroom/VideoInput.vue?vue&type=template&id=2169d7bb&

// EXTERNAL MODULE: ./components/form/TextInput.vue + 4 modules
var TextInput = __webpack_require__(102);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/VideoInput.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var VideoInputvue_type_script_lang_js_ = ({
  name: 'VideoInput',
  components: {
    TextInput: TextInput["default"]
  },

  data() {
    return {
      inputUrl: null,
      isValid: true,
      videoId: null,
      rules: {
        required: v => !!v || this.$t('field_required')
      }
    };
  },

  computed: {
    role() {
      return this.$store.getters['classroom/role'];
    },

    isVideoInputOpened() {
      return this.$store.state.classroom.isVideoInputOpened;
    }

  },
  watch: {
    inputUrl(newValue, oldValue) {
      this.isValid = true;
      let videoId;

      if (newValue !== null && newValue !== void 0 && newValue.includes('v=')) {
        videoId = newValue.split('v=')[1];
        const ampersandPosition = videoId.indexOf('&');

        if (ampersandPosition !== -1) {
          videoId = videoId.substring(0, ampersandPosition);
        }
      }

      if (newValue.length && (!videoId || !videoId.match(/[^"&=\s?]{11}/))) {
        this.isValid = false;
      }

      this.videoId = videoId;
    }

  },

  mounted() {
    this.$refs.input.focus();
  },

  methods: {
    async submit() {
      if (!this.isValid || !this.videoId) return;
      await this.$store.dispatch('classroom/createAsset', {
        type: 'video',
        videoId: this.videoId,
        index: this.$store.state.classroom.maxIndex + 1,
        shapes: [],
        owner: this.role
      });
      this.$store.commit('classroom/closeVideoInput');
    }

  }
});
// CONCATENATED MODULE: ./components/classroom/VideoInput.vue?vue&type=script&lang=js&
 /* harmony default export */ var classroom_VideoInputvue_type_script_lang_js_ = (VideoInputvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(896);

// CONCATENATED MODULE: ./components/classroom/VideoInput.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1334)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  classroom_VideoInputvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "7f4d3e50"
  
)

/* harmony default export */ var VideoInput = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */



installComponents_default()(component, {VBtn: VBtn["a" /* default */],VForm: VForm["a" /* default */]})


/***/ })

};;
//# sourceMappingURL=classroom-video-input.js.map