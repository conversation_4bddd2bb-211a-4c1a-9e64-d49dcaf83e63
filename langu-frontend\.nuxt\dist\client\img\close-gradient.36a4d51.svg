<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="27" height="27" viewBox="237 237 27 27" xml:space="preserve">
<desc>Created with Fabric.js 4.6.0</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 250 250)" id="_IsH7zP8sOFNWlbaEpmO5"  >
<linearGradient id="SVGID_2" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1 0 0 1 0 0)"  x1="6.57617" y1="6.5752" x2="23.8233" y2="19.1733">
<stop offset="0%" style="stop-color:rgb(128,182,34);stop-opacity: 1"/>
<stop offset="100%" style="stop-color:rgb(60,135,248);stop-opacity: 1"/>
</linearGradient>
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: url(#SVGID_2); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-14, -14)" d="M 14.0008 12.3502 L 19.7758 6.5752 L 21.4255 8.22486 L 15.6505 13.9999 L 21.4255 19.7749 L 19.7758 21.4245 L 14.0008 15.6495 L 8.22584 21.4245 L 6.57617 19.7749 L 12.3512 13.9999 L 6.57617 8.22486 L 8.22584 6.5752 L 14.0008 12.3502 Z" stroke-linecap="round" />
</g>
</svg>