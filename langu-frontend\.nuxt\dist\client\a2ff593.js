(window.webpackJsonp=window.webpackJsonp||[]).push([[182,56],{1372:function(e,t,n){var r=n(43);e.exports=function(e){return r(Set.prototype.values,e)}},1384:function(e,t,n){"use strict";var r=n(43),o=n(79),c=n(32);e.exports=function(){for(var e=c(this),t=o(e.add),n=0,l=arguments.length;n<l;n++)r(t,e,arguments[n]);return e}},1390:function(e,t,n){"use strict";n(872)("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),n(873))},1391:function(e,t,n){"use strict";n(11)({target:"Set",proto:!0,real:!0,forced:!0},{addAll:n(1384)})},1392:function(e,t,n){"use strict";n(11)({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:n(874)})},1393:function(e,t,n){"use strict";var r=n(11),o=n(87),c=n(43),l=n(79),d=n(32),f=n(125),h=n(86);r({target:"Set",proto:!0,real:!0,forced:!0},{difference:function(e){var t=d(this),n=new(f(t,o("Set")))(t),r=l(n.delete);return h(e,(function(e){c(r,n,e)})),n}})},1394:function(e,t,n){"use strict";var r=n(11),o=n(32),c=n(69),l=n(1372),d=n(86);r({target:"Set",proto:!0,real:!0,forced:!0},{every:function(e){var t=o(this),n=l(t),r=c(e,arguments.length>1?arguments[1]:void 0);return!d(n,(function(e,n){if(!r(e,e,t))return n()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1395:function(e,t,n){"use strict";var r=n(11),o=n(87),c=n(43),l=n(79),d=n(32),f=n(69),h=n(125),v=n(1372),_=n(86);r({target:"Set",proto:!0,real:!0,forced:!0},{filter:function(e){var t=d(this),n=v(t),r=f(e,arguments.length>1?arguments[1]:void 0),m=new(h(t,o("Set"))),y=l(m.add);return _(n,(function(e){r(e,e,t)&&c(y,m,e)}),{IS_ITERATOR:!0}),m}})},1396:function(e,t,n){"use strict";var r=n(11),o=n(32),c=n(69),l=n(1372),d=n(86);r({target:"Set",proto:!0,real:!0,forced:!0},{find:function(e){var t=o(this),n=l(t),r=c(e,arguments.length>1?arguments[1]:void 0);return d(n,(function(e,n){if(r(e,e,t))return n(e)}),{IS_ITERATOR:!0,INTERRUPTED:!0}).result}})},1397:function(e,t,n){"use strict";var r=n(11),o=n(87),c=n(43),l=n(79),d=n(32),f=n(125),h=n(86);r({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function(e){var t=d(this),n=new(f(t,o("Set"))),r=l(t.has),v=l(n.add);return h(e,(function(e){c(r,t,e)&&c(v,n,e)})),n}})},1398:function(e,t,n){"use strict";var r=n(11),o=n(43),c=n(79),l=n(32),d=n(86);r({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function(e){var t=l(this),n=c(t.has);return!d(e,(function(e,r){if(!0===o(n,t,e))return r()}),{INTERRUPTED:!0}).stopped}})},1399:function(e,t,n){"use strict";var r=n(11),o=n(87),c=n(43),l=n(79),d=n(45),f=n(32),h=n(209),v=n(86);r({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function(e){var t=h(this),n=f(e),r=n.has;return d(r)||(n=new(o("Set"))(e),r=l(n.has)),!v(t,(function(e,t){if(!1===c(r,n,e))return t()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1400:function(e,t,n){"use strict";var r=n(11),o=n(43),c=n(79),l=n(32),d=n(86);r({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function(e){var t=l(this),n=c(t.has);return!d(e,(function(e,r){if(!1===o(n,t,e))return r()}),{INTERRUPTED:!0}).stopped}})},1401:function(e,t,n){"use strict";var r=n(11),o=n(17),c=n(32),l=n(61),d=n(1372),f=n(86),h=o([].join),v=[].push;r({target:"Set",proto:!0,real:!0,forced:!0},{join:function(e){var t=c(this),n=d(t),r=void 0===e?",":l(e),o=[];return f(n,v,{that:o,IS_ITERATOR:!0}),h(o,r)}})},1402:function(e,t,n){"use strict";var r=n(11),o=n(87),c=n(69),l=n(43),d=n(79),f=n(32),h=n(125),v=n(1372),_=n(86);r({target:"Set",proto:!0,real:!0,forced:!0},{map:function(e){var t=f(this),n=v(t),r=c(e,arguments.length>1?arguments[1]:void 0),m=new(h(t,o("Set"))),y=d(m.add);return _(n,(function(e){l(y,m,r(e,e,t))}),{IS_ITERATOR:!0}),m}})},1403:function(e,t,n){"use strict";var r=n(11),o=n(5),c=n(79),l=n(32),d=n(1372),f=n(86),h=o.TypeError;r({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=l(this),n=d(t),r=arguments.length<2,o=r?void 0:arguments[1];if(c(e),f(n,(function(n){r?(r=!1,o=n):o=e(o,n,n,t)}),{IS_ITERATOR:!0}),r)throw h("Reduce of empty set with no initial value");return o}})},1404:function(e,t,n){"use strict";var r=n(11),o=n(32),c=n(69),l=n(1372),d=n(86);r({target:"Set",proto:!0,real:!0,forced:!0},{some:function(e){var t=o(this),n=l(t),r=c(e,arguments.length>1?arguments[1]:void 0);return d(n,(function(e,n){if(r(e,e,t))return n()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1405:function(e,t,n){"use strict";var r=n(11),o=n(87),c=n(43),l=n(79),d=n(32),f=n(125),h=n(86);r({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function(e){var t=d(this),n=new(f(t,o("Set")))(t),r=l(n.delete),v=l(n.add);return h(e,(function(e){c(r,n,e)||c(v,n,e)})),n}})},1406:function(e,t,n){"use strict";var r=n(11),o=n(87),c=n(79),l=n(32),d=n(125),f=n(86);r({target:"Set",proto:!0,real:!0,forced:!0},{union:function(e){var t=l(this),n=new(d(t,o("Set")))(t);return f(e,c(n.add),{that:n}),n}})},1410:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},d=n(1425),f=d.typeErrors,h=d.trackPriority,v=n(1439),_=n(1864).sessionSID;function m(){return"undefined"!=typeof navigator&&navigator.userAgent?navigator.userAgent:"Unknown"}function y(source,e,t,n){if(!(n in e)&&!n.match(/^on[a-z]+$/)){var r;try{r=typeof source[n]}catch(e){}"function"===r&&(e[n]=function(){for(var e,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];return(e=this[t])[n].apply(e,l([],c(r)))})}}function w(object){return"object"==typeof object&&!Array.isArray(object)}function k(object){Object.getOwnPropertyNames(object).forEach((function(e){e.startsWith("_")&&S(object,e)}))}function S(object,e){var t=Object.getOwnPropertyDescriptor(object,e);t.enumerable=!1,Object.defineProperty(object,e,t)}function T(object,e){void 0===e&&(e=[]),e.forEach((function(e){object.hasOwnProperty(e)&&S(object,e)}))}function O(e){return e.map(P)}function P(e){return Array.isArray(e)?O(e):e instanceof Set?O(l([],c(e))):e instanceof Map?l([],c(e.entries())).reduce((function(e,t){var n,r=c(t,2),o=r[0],l=r[1];return Object.assign(((n={})[o]=P(l),n),e)}),{}):e&&"object"==typeof e?(object=e,Object.entries(object).reduce((function(e,t){var n,r=c(t,2),o=r[0],l=r[1];return Object.assign(((n={})[o]=P(l),n),e)}),{})):e;var object}function C(e){return j(e,[{prop:"dominantSpeakerPriority",type:"string",payloadProp:"active_speaker_priority"},{prop:"maxSubscriptionBitrate",type:"number",payloadProp:"max_subscription_bandwidth"},{prop:"maxTracks",type:"number",payloadProp:"max_tracks"},{prop:"mode",type:"string"},{prop:"renderDimensions",type:"object",payloadProp:"render_dimensions",transform:R},{prop:"trackSwitchOffMode",type:"string",payloadProp:"track_switch_off"}])}function E(e){return j(e,[{prop:"height",type:"number"},{prop:"width",type:"number"}])}function R(e){return j(e,[{prop:h.PRIORITY_HIGH,type:"object",transform:E},{prop:h.PRIORITY_LOW,type:"object",transform:E},{prop:h.PRIORITY_STANDARD,type:"object",transform:E}])}function j(object,e){return e.reduce((function(e,t){var n,r=t.prop,o=t.type,c=t.payloadProp,l=void 0===c?r:c,d=t.transform,f=void 0===d?function(e){return e}:d;return typeof object[r]===o?Object.assign(((n={})[l]=f(object[r]),n),e):e}),{})}t.constants=d,t.createBandwidthProfilePayload=function(e){return j(e,[{prop:"video",type:"object",transform:C}])},t.createMediaSignalingPayload=function(e,t,n,r,o,c){var l={transports:[{type:"data-channel"}]};return Object.assign(e?{active_speaker:l}:{},t?{network_quality:l}:{},c?{render_hints:l}:{},o?{publisher_hints:l}:{},n?{track_priority:l}:{},r?{track_switch_off:l}:{})},t.createRoomConnectEventPayload=function(e){function t(e){return e?"true":"false"}var n={sessionSID:_,iceServers:(e.iceServers||[]).length,audioTracks:(e.tracks||[]).filter((function(track){return"audio"===track.kind})).length,videoTracks:(e.tracks||[]).filter((function(track){return"video"===track.kind})).length,dataTracks:(e.tracks||[]).filter((function(track){return"data"===track.kind})).length};if([["audio"],["automaticSubscription"],["enableDscp"],["eventListener"],["preflight"],["video"],["dominantSpeaker","enableDominantSpeaker"]].forEach((function(r){var o=c(r,2),l=o[0],d=o[1];n[d=d||l]=t(!!e[l])})),[["maxVideoBitrate"],["maxAudioBitrate"]].forEach((function(t){var r=c(t,2),o=r[0],l=r[1];l=l||o,"number"==typeof e[o]?n[l]=e[o]:isNaN(Number(e[o]))||(n[l]=Number(e[o]))})),[["iceTransportPolicy"],["region"],["name","roomName"]].forEach((function(t){var r=c(t,2),o=r[0],l=r[1];l=l||o,"string"==typeof e[o]?n[l]=e[o]:"number"==typeof e[o]&&"name"===o&&(n[l]=e[o].toString())})),["preferredAudioCodecs","preferredVideoCodecs"].forEach((function(t){t in e&&(n[t]=JSON.stringify(e[t]))})),"networkQuality"in e&&(n.networkQualityConfiguration={},w(e.networkQuality)?["local","remote"].forEach((function(t){"number"==typeof e.networkQuality[t]&&(n.networkQualityConfiguration[t]=e.networkQuality[t])})):(n.networkQualityConfiguration.remote=0,n.networkQualityConfiguration.local=e.networkQuality?1:0)),e.bandwidthProfile&&e.bandwidthProfile.video){var r=e.bandwidthProfile.video||{};n.bandwidthProfileOptions={},["mode","maxTracks","trackSwitchOffMode","dominantSpeakerPriority","maxSubscriptionBitrate","renderDimensions","contentPreferencesMode","clientTrackSwitchOffControl"].forEach((function(e){"number"==typeof r[e]||"string"==typeof r[e]?n.bandwidthProfileOptions[e]=r[e]:"boolean"==typeof r[e]?n.bandwidthProfileOptions[e]=t(r[e]):"object"==typeof r[e]&&(n.bandwidthProfileOptions[e]=JSON.stringify(r[e]))}))}return{group:"room",name:"connect",level:"info",payload:n}},t.createSubscribePayload=function(e){return{rules:[{type:e?"include":"exclude",all:!0}],revision:1}},t.asLocalTrack=function(track,e){if(track instanceof e.LocalAudioTrack||track instanceof e.LocalVideoTrack||track instanceof e.LocalDataTrack)return track;if(track instanceof e.MediaStreamTrack)return"audio"===track.kind?new e.LocalAudioTrack(track,e):new e.LocalVideoTrack(track,e);throw f.INVALID_TYPE("track","LocalAudioTrack, LocalVideoTrack, LocalDataTrack, or MediaStreamTrack")},t.asLocalTrackPublication=function(track,e,t,n){return new(0,{audio:n.LocalAudioTrackPublication,video:n.LocalVideoTrackPublication,data:n.LocalDataTrackPublication}[track.kind])(e,track,t,n)},t.capitalize=function(e){return e[0].toUpperCase()+e.slice(1)},t.deprecateEvents=function(e,t,n,r){var o=new Set;t.on("newListener",(function c(l){n.has(l)&&!o.has(l)&&(r.deprecated(e+"#"+l+" has been deprecated and scheduled for removal in twilio-video.js@2.0.0."+(n.get(l)?" Use "+e+"#"+n.get(l)+" instead.":"")),o.add(l)),o.size>=n.size&&t.removeListener("newListener",c)}))},t.difference=function(e,t){e=Array.isArray(e)?new Set(e):new Set(e.values()),t=Array.isArray(t)?new Set(t):new Set(t.values());var n=new Set;return e.forEach((function(e){t.has(e)||n.add(e)})),n},t.filterObject=function(object,e){return Object.keys(object).reduce((function(t,n){return object[n]!==e&&(t[n]=object[n]),t}),{})},t.flatMap=function(e,t){var n=e instanceof Map||e instanceof Set?Array.from(e.values()):e;return t=t||function(e){return e},n.reduce((function(e,n){var r=t(n);return e.concat(r)}),[])},t.getPlatform=function(){var e=m(),t=c(e.match(/\(([^)]+)\)/)||[],2)[1];return c((void 0===t?"unknown":t).split(";").map((function(e){return e.trim()})),1)[0].toLowerCase()},t.getUserAgent=m,t.hidePrivateProperties=k,t.hidePrivateAndCertainPublicPropertiesInClass=function(e,t){return function(e){function n(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var o=e.apply(this,l([],c(n)))||this;return k(o),T(o,t),o}return o(n,e),n}(e)},t.isDeepEqual=function e(t,n){if(t===n)return!0;if(typeof t!=typeof n)return!1;if(null===t)return null===n;if(null===n)return!1;if(Array.isArray(t))return Array.isArray(n)&&t.length===n.length&&t.every((function(t,i){return e(t,n[i])}));if("object"==typeof t){var r=Object.keys(t).sort(),o=Object.keys(n).sort();return!Array.isArray(n)&&e(r,o)&&r.every((function(r){return e(t[r],n[r])}))}return!1},t.isNonArrayObject=w,t.inRange=function(e,t,n){return t<=e&&e<=n},t.makeUUID=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))},t.oncePerTick=function(e){var t=null;function n(){t=null,e()}return function(){t&&clearTimeout(t),t=setTimeout(n)}},t.promiseFromEvents=function(e,t,n,r){return new Promise((function(o,d){function f(){var e=[].slice.call(arguments);r&&t.removeListener(r,h),o.apply(void 0,l([],c(e)))}function h(){var e=[].slice.call(arguments);t.removeListener(n,f),d.apply(void 0,l([],c(e)))}t.once(n,f),r&&t.once(r,h),e()}))},t.getOrNull=function(e,path){return path.split(".").reduce((function(output,e){return output?output[e]:null}),e)},t.defer=function(){var e={};return e.promise=new Promise((function(t,n){e.resolve=t,e.reject=n})),e},t.delegateMethods=function(source,e,t){for(var n in source)y(source,e,t,n)},t.proxyProperties=function(source,e,t){Object.getOwnPropertyNames(source).forEach((function(n){!function(source,e,t,n){if(n in e)return;if(n.match(/^on[a-z]+$/))return Object.defineProperty(e,n,{value:null,writable:!0}),void t.addEventListener(n.slice(2),(function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];e.dispatchEvent.apply(e,l([],c(t)))}));Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[n]}})}(0,e,t,n)}))},t.legacyPromise=function(e,t,n){return t?e.then((function(e){t(e)}),(function(e){n(e)})):e},t.buildLogLevels=function(e){return"string"==typeof e?{default:e,media:e,signaling:e,webrtc:e}:e},t.trackClass=function(track,e){return(e=e?"Local":"")+(track.kind||"").replace(/\w{1}/,(function(e){return e.toUpperCase()}))+"Track"},t.trackPublicationClass=function(e,t){return(t=t?"Local":"")+(e.kind||"").replace(/\w{1}/,(function(e){return e.toUpperCase()}))+"TrackPublication"},t.valueToJSON=P,t.withJitter=function(e,t){var n=Math.random();return e-t+Math.floor(2*t*n+.5)},t.isChromeScreenShareTrack=function(track){return"chrome"===v.guessBrowser()&&"video"===track.kind&&"displaySurface"in track.getSettings()},t.isUserMediaTrack=function(track){return"string"==typeof track.getSettings().deviceId},t.waitForSometime=function(e){return void 0===e&&(e=10),new Promise((function(t){return setTimeout(t,e)}))},t.waitForEvent=function(e,t){return new Promise((function(n){e.addEventListener(t,(function r(o){e.removeEventListener(t,r),n(o)}))}))}},1425:function(e,t,n){"use strict";var r=n(1863);e.exports.SDK_NAME=r.name+".js",e.exports.SDK_VERSION=r.version,e.exports.SDP_FORMAT="unified",e.exports.DEFAULT_ENVIRONMENT="prod",e.exports.DEFAULT_REALM="us1",e.exports.DEFAULT_REGION="gll",e.exports.DEFAULT_LOG_LEVEL="warn",e.exports.DEFAULT_LOGGER_NAME="twilio-video",e.exports.WS_SERVER=function(e,t){return t="gll"===t?"global":encodeURIComponent(t),"prod"===e?"wss://"+t+".vss.twilio.com/signaling":"wss://"+t+".vss."+e+".twilio.com/signaling"},e.exports.PUBLISH_MAX_ATTEMPTS=5,e.exports.PUBLISH_BACKOFF_JITTER=10,e.exports.PUBLISH_BACKOFF_MS=20,e.exports.typeErrors={ILLEGAL_INVOKE:function(e,t){return new TypeError("Illegal call to "+e+": "+t)},INVALID_TYPE:function(e,t){return new TypeError(e+" must be "+((["a","e","i","o","u"].includes(t.toLowerCase()[0])?"an":"a")+" ")+t)},INVALID_VALUE:function(e,t){return new RangeError(e+" must be one of "+t.join(", "))},REQUIRED_ARGUMENT:function(e){return new TypeError(e+" must be specified")}},e.exports.DEFAULT_FRAME_RATE=24,e.exports.DEFAULT_VIDEO_PROCESSOR_STATS_INTERVAL_MS=1e4,e.exports.DEFAULT_ICE_GATHERING_TIMEOUT_MS=15e3,e.exports.DEFAULT_SESSION_TIMEOUT_SEC=30,e.exports.DEFAULT_NQ_LEVEL_LOCAL=1,e.exports.DEFAULT_NQ_LEVEL_REMOTE=0,e.exports.MAX_NQ_LEVEL=3,e.exports.ICE_ACTIVITY_CHECK_PERIOD_MS=1e3,e.exports.ICE_INACTIVITY_THRESHOLD_MS=3e3,e.exports.iceRestartBackoffConfig={factor:1.1,initialDelay:1,maxDelay:1e3*e.exports.DEFAULT_SESSION_TIMEOUT_SEC,randomisationFactor:.5},e.exports.reconnectBackoffConfig={factor:1.5,initialDelay:80,randomisationFactor:.5},e.exports.subscriptionMode={MODE_COLLABORATION:"collaboration",MODE_GRID:"grid",MODE_PRESENTATION:"presentation"},e.exports.trackSwitchOffMode={MODE_DISABLED:"disabled",MODE_DETECTED:"detected",MODE_PREDICTED:"predicted"},e.exports.trackPriority={PRIORITY_HIGH:"high",PRIORITY_LOW:"low",PRIORITY_STANDARD:"standard"},e.exports.clientTrackSwitchOffControl={MODE_AUTO:"auto",MODE_MANUAL:"manual"},e.exports.videoContentPreferencesMode={MODE_AUTO:"auto",MODE_MANUAL:"manual"}},1439:function(e,t,n){"use strict";function r(source,e,t,n){if(!(n in e)&&!n.match(/^on[a-z]+$/)){var r=!1;try{var o=Object.getOwnPropertyDescriptor(source,n);r=o&&!!o.get}catch(e){}if(!r){var c;try{c=typeof source[n]}catch(e){}"function"===c&&(e[n]=function(){return this[t][n].apply(this[t],arguments)})}}}function o(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:null}function c(e){return void 0===e&&(e=o()),/Chrome|CriOS/.test(e)?"chrome":/Firefox|FxiOS/.test(e)?"firefox":/Safari|iPhone|iPad|iPod/.test(e)?"safari":null}t.defer=function(){var e={};return e.promise=new Promise((function(t,n){e.resolve=t,e.reject=n})),e},t.delegateMethods=function(source,e,t){for(var n in source)r(source,e,t,n)},t.difference=function(e,t){e=Array.isArray(e)?new Set(e):new Set(e.values()),t=Array.isArray(t)?new Set(t):new Set(t.values());var n=new Set;return e.forEach((function(e){t.has(e)||n.add(e)})),n},t.flatMap=function(e,t){return(e instanceof Map||e instanceof Set?Array.from(e.values()):e).reduce((function(e,n){var r=t(n);return e.concat(r)}),[])},t.guessBrowser=c,t.guessBrowserVersion=function(e){void 0===e&&(e=o());var t={chrome:"Chrome|CriOS",firefox:"Firefox|FxiOS",safari:"Version"}[c(e)];if(!t)return null;var n=new RegExp("("+t+")/([^\\s]+)"),r=(e.match(n)||[])[2];if(!r)return null;var l=r.split(".").map(Number);return{major:isNaN(l[0])?null:l[0],minor:isNaN(l[1])?null:l[1]}},t.isIOSChrome=function(e){return void 0===e&&(e=o()),/Mobi/.test(e)&&"chrome"===c()&&/iPad|iPhone|iPod/.test(e)},t.interceptEvent=function(e,t){var n=null;Object.defineProperty(e,"on"+t,{get:function(){return n},set:function(e){n&&this.removeEventListener(t,n),"function"==typeof e?(n=e,this.addEventListener(t,n)):n=null}})},t.legacyPromise=function(e,t,n){return t?e.then((function(e){t(e)}),(function(e){n(e)})):e},t.makeUUID=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))},t.proxyProperties=function(source,e,t){Object.getOwnPropertyNames(source).forEach((function(n){!function(source,e,t,n){if(n in e)return;if(n.match(/^on[a-z]+$/))return Object.defineProperty(e,n,{value:null,writable:!0}),void t.addEventListener(n.slice(2),(function(){e.dispatchEvent.apply(e,arguments)}));Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[n]}})}(0,e,t,n)}))},t.support=function(){return"object"==typeof navigator&&"object"==typeof navigator.mediaDevices&&"function"==typeof navigator.mediaDevices.getUserMedia&&"function"==typeof RTCPeerConnection}},1521:function(e,t,n){(function(e){var r=Object.getOwnPropertyDescriptors||function(e){for(var t=Object.keys(e),n={},i=0;i<t.length;i++)n[t[i]]=Object.getOwnPropertyDescriptor(e,t[i]);return n},o=/%[sdj%]/g;t.format=function(e){if(!T(e)){for(var t=[],i=0;i<arguments.length;i++)t.push(d(arguments[i]));return t.join(" ")}i=1;for(var n=arguments,r=n.length,c=String(e).replace(o,(function(e){if("%%"===e)return"%";if(i>=r)return e;switch(e){case"%s":return String(n[i++]);case"%d":return Number(n[i++]);case"%j":try{return JSON.stringify(n[i++])}catch(e){return"[Circular]"}default:return e}})),l=n[i];i<r;l=n[++i])k(l)||!C(l)?c+=" "+l:c+=" "+d(l);return c},t.deprecate=function(n,r){if(void 0!==e&&!0===e.noDeprecation)return n;if(void 0===e)return function(){return t.deprecate(n,r).apply(this,arguments)};var o=!1;return function(){if(!o){if(e.throwDeprecation)throw new Error(r);e.traceDeprecation?console.trace(r):console.error(r),o=!0}return n.apply(this,arguments)}};var c,l={};function d(e,n){var r={seen:[],stylize:h};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),w(n)?r.showHidden=n:n&&t._extend(r,n),O(r.showHidden)&&(r.showHidden=!1),O(r.depth)&&(r.depth=2),O(r.colors)&&(r.colors=!1),O(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=f),v(r,e,r.depth)}function f(e,t){var style=d.styles[t];return style?"["+d.colors[style][0]+"m"+e+"["+d.colors[style][1]+"m":e}function h(e,t){return e}function v(e,n,r){if(e.customInspect&&n&&j(n.inspect)&&n.inspect!==t.inspect&&(!n.constructor||n.constructor.prototype!==n)){var o=n.inspect(r,e);return T(o)||(o=v(e,o,r)),o}var c=function(e,t){if(O(t))return e.stylize("undefined","undefined");if(T(t)){var n="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(n,"string")}if(S(t))return e.stylize(""+t,"number");if(w(t))return e.stylize(""+t,"boolean");if(k(t))return e.stylize("null","null")}(e,n);if(c)return c;var l=Object.keys(n),d=function(e){var t={};return e.forEach((function(e,n){t[e]=!0})),t}(l);if(e.showHidden&&(l=Object.getOwnPropertyNames(n)),R(n)&&(l.indexOf("message")>=0||l.indexOf("description")>=0))return _(n);if(0===l.length){if(j(n)){var f=n.name?": "+n.name:"";return e.stylize("[Function"+f+"]","special")}if(P(n))return e.stylize(RegExp.prototype.toString.call(n),"regexp");if(E(n))return e.stylize(Date.prototype.toString.call(n),"date");if(R(n))return _(n)}var output,base="",h=!1,C=["{","}"];(y(n)&&(h=!0,C=["[","]"]),j(n))&&(base=" [Function"+(n.name?": "+n.name:"")+"]");return P(n)&&(base=" "+RegExp.prototype.toString.call(n)),E(n)&&(base=" "+Date.prototype.toUTCString.call(n)),R(n)&&(base=" "+_(n)),0!==l.length||h&&0!=n.length?r<0?P(n)?e.stylize(RegExp.prototype.toString.call(n),"regexp"):e.stylize("[Object]","special"):(e.seen.push(n),output=h?function(e,t,n,r,o){for(var output=[],i=0,c=t.length;i<c;++i)I(t,String(i))?output.push(m(e,t,n,r,String(i),!0)):output.push("");return o.forEach((function(o){o.match(/^\d+$/)||output.push(m(e,t,n,r,o,!0))})),output}(e,n,r,d,l):l.map((function(t){return m(e,n,r,d,t,h)})),e.seen.pop(),function(output,base,e){if(output.reduce((function(e,t){return t.indexOf("\n")>=0&&0,e+t.replace(/\u001b\[\d\d?m/g,"").length+1}),0)>60)return e[0]+(""===base?"":base+"\n ")+" "+output.join(",\n  ")+" "+e[1];return e[0]+base+" "+output.join(", ")+" "+e[1]}(output,base,C)):C[0]+base+C[1]}function _(e){return"["+Error.prototype.toString.call(e)+"]"}function m(e,t,n,r,o,c){var l,d,desc;if((desc=Object.getOwnPropertyDescriptor(t,o)||{value:t[o]}).get?d=desc.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):desc.set&&(d=e.stylize("[Setter]","special")),I(r,o)||(l="["+o+"]"),d||(e.seen.indexOf(desc.value)<0?(d=k(n)?v(e,desc.value,null):v(e,desc.value,n-1)).indexOf("\n")>-1&&(d=c?d.split("\n").map((function(line){return"  "+line})).join("\n").substr(2):"\n"+d.split("\n").map((function(line){return"   "+line})).join("\n")):d=e.stylize("[Circular]","special")),O(l)){if(c&&o.match(/^\d+$/))return d;(l=JSON.stringify(""+o)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(l=l.substr(1,l.length-2),l=e.stylize(l,"name")):(l=l.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),l=e.stylize(l,"string"))}return l+": "+d}function y(e){return Array.isArray(e)}function w(e){return"boolean"==typeof e}function k(e){return null===e}function S(e){return"number"==typeof e}function T(e){return"string"==typeof e}function O(e){return void 0===e}function P(e){return C(e)&&"[object RegExp]"===x(e)}function C(e){return"object"==typeof e&&null!==e}function E(e){return C(e)&&"[object Date]"===x(e)}function R(e){return C(e)&&("[object Error]"===x(e)||e instanceof Error)}function j(e){return"function"==typeof e}function x(e){return Object.prototype.toString.call(e)}function L(e){return e<10?"0"+e.toString(10):e.toString(10)}t.debuglog=function(n){if(O(c)&&(c=e.env.NODE_DEBUG||""),n=n.toUpperCase(),!l[n])if(new RegExp("\\b"+n+"\\b","i").test(c)){var r=e.pid;l[n]=function(){var e=t.format.apply(t,arguments);console.error("%s %d: %s",n,r,e)}}else l[n]=function(){};return l[n]},t.inspect=d,d.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},d.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},t.isArray=y,t.isBoolean=w,t.isNull=k,t.isNullOrUndefined=function(e){return null==e},t.isNumber=S,t.isString=T,t.isSymbol=function(e){return"symbol"==typeof e},t.isUndefined=O,t.isRegExp=P,t.isObject=C,t.isDate=E,t.isError=R,t.isFunction=j,t.isPrimitive=function(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e},t.isBuffer=n(2082);var A=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function D(){var e=new Date,time=[L(e.getHours()),L(e.getMinutes()),L(e.getSeconds())].join(":");return[e.getDate(),A[e.getMonth()],time].join(" ")}function I(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.log=function(){console.log("%s - %s",D(),t.format.apply(t,arguments))},t.inherits=n(2083),t._extend=function(e,t){if(!t||!C(t))return e;for(var n=Object.keys(t),i=n.length;i--;)e[n[i]]=t[n[i]];return e};var M="undefined"!=typeof Symbol?Symbol("util.promisify.custom"):void 0;function N(e,t){if(!e){var n=new Error("Promise was rejected with a falsy value");n.reason=e,e=n}return t(e)}t.promisify=function(e){if("function"!=typeof e)throw new TypeError('The "original" argument must be of type Function');if(M&&e[M]){var t;if("function"!=typeof(t=e[M]))throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(t,M,{value:t,enumerable:!1,writable:!1,configurable:!0}),t}function t(){for(var t,n,r=new Promise((function(e,r){t=e,n=r})),o=[],i=0;i<arguments.length;i++)o.push(arguments[i]);o.push((function(e,r){e?n(e):t(r)}));try{e.apply(this,o)}catch(e){n(e)}return r}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),M&&Object.defineProperty(t,M,{value:t,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(t,r(e))},t.promisify.custom=M,t.callbackify=function(t){if("function"!=typeof t)throw new TypeError('The "original" argument must be of type Function');function n(){for(var n=[],i=0;i<arguments.length;i++)n.push(arguments[i]);var r=n.pop();if("function"!=typeof r)throw new TypeError("The last argument must be of type Function");var o=this,c=function(){return r.apply(o,arguments)};t.apply(this,n).then((function(t){e.nextTick(c,null,t)}),(function(t){e.nextTick(N,t,c)}))}return Object.setPrototypeOf(n,Object.getPrototypeOf(t)),Object.defineProperties(n,r(t)),n}}).call(this,n(107))},1570:function(e,t,n){"use strict";var r,o=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},c=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},l=n(1872).getLogger,d=n(1425),f=d.DEFAULT_LOG_LEVEL,h=d.DEFAULT_LOGGER_NAME,v=n(1425).typeErrors;var _=function(){function e(t,component,n,r,o){if("string"!=typeof t)throw v.INVALID_TYPE("moduleName","string");if(!component)throw v.REQUIRED_ARGUMENT("component");"object"!=typeof n&&(n={}),o=o||l,S(n),Object.defineProperties(this,{_component:{value:component},_logLevels:{value:n},_warnings:{value:new Set},_loggerName:{get:function(){var e=r&&"string"==typeof r?r:h;return this._logLevelsEqual||(e=e+"-"+t),e}},_logger:{get:function(){var e=o(this._loggerName),n=this._logLevels[t]||f;return n="off"===n?"silent":n,e.setDefaultLevel(n),e}},_logLevelsEqual:{get:function(){return 1===new Set(Object.values(this._logLevels)).size}},logLevel:{get:function(){return e.getLevelByName(n[t]||f)}},name:{get:component.toString.bind(component)}})}return e.getLevelByName=function(t){return isNaN(t)?(k(t=t.toUpperCase()),e[t]):parseInt(t,10)},e.prototype.createLog=function(t,component){var n=this._loggerName;return this._logLevelsEqual||(n=n.substring(0,n.lastIndexOf("-"))),new e(t,component,this._logLevels,n)},e.prototype.setLevels=function(e){return S(e),Object.assign(this._logLevels,e),this},e.prototype.log=function(t,n){var r=e._levels[t];if(!r)throw v.INVALID_VALUE("logLevel",y);r=r.toLowerCase();var l=[(new Date).toISOString(),r,this.name];return(this._logger[r]||function(){}).apply(void 0,c([],o(l.concat(n)))),this},e.prototype.debug=function(){return this.log(e.DEBUG,[].slice.call(arguments))},e.prototype.deprecated=function(e){var t=function(e){if((r=r||new Map).has(e))return r.get(e);var t=new Set;return r.set(e,t),t}(this._component.constructor);return t.has(e)?this:(t.add(e),this.warn(e))},e.prototype.info=function(){return this.log(e.INFO,[].slice.call(arguments))},e.prototype.warn=function(){return this.log(e.WARN,[].slice.call(arguments))},e.prototype.warnOnce=function(e){return this._warnings.has(e)?this:(this._warnings.add(e),this.warn(e))},e.prototype.error=function(){return this.log(e.ERROR,[].slice.call(arguments))},e.prototype.throw=function(t,n){throw t.clone&&(t=t.clone(n)),this.log(e.ERROR,t),t},e}();Object.defineProperties(_,{DEBUG:{value:0},INFO:{value:1},WARN:{value:2},ERROR:{value:3},OFF:{value:4},_levels:{value:["DEBUG","INFO","WARN","ERROR","OFF"]}});var m={},y=[],w=_._levels.map((function(e,i){return m[e]=!0,y.push(i),e}));function k(e){if(!(e in m))throw v.INVALID_VALUE("level",w)}function S(e){Object.keys(e).forEach((function(t){k(e[t].toUpperCase())}))}e.exports=_},1571:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(2103),l={};t.createTwilioError=function(code,e){return e="string"==typeof e&&e?e:"Unknown error",l[code="number"==typeof code?code:0]?new l[code]:new c(code,e)};var d=function(e){function t(){var n=e.call(this,20101,"Invalid Access Token")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.AccessTokenInvalidError=d,Object.defineProperty(l,20101,{value:d});var f=function(e){function t(){var n=e.call(this,20102,"Invalid Access Token header")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.AccessTokenHeaderInvalidError=f,Object.defineProperty(l,20102,{value:f});var h=function(e){function t(){var n=e.call(this,20103,"Invalid Access Token issuer/subject")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.AccessTokenIssuerInvalidError=h,Object.defineProperty(l,20103,{value:h});var v=function(e){function t(){var n=e.call(this,20104,"Access Token expired or expiration date invalid")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.AccessTokenExpiredError=v,Object.defineProperty(l,20104,{value:v});var _=function(e){function t(){var n=e.call(this,20105,"Access Token not yet valid")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.AccessTokenNotYetValidError=_,Object.defineProperty(l,20105,{value:_});var m=function(e){function t(){var n=e.call(this,20106,"Invalid Access Token grants")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.AccessTokenGrantsInvalidError=m,Object.defineProperty(l,20106,{value:m});var y=function(e){function t(){var n=e.call(this,20107,"Invalid Access Token signature")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.AccessTokenSignatureInvalidError=y,Object.defineProperty(l,20107,{value:y});var w=function(e){function t(){var n=e.call(this,53e3,"Signaling connection error")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.SignalingConnectionError=w,Object.defineProperty(l,53e3,{value:w});var k=function(e){function t(){var n=e.call(this,53001,"Signaling connection disconnected")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.SignalingConnectionDisconnectedError=k,Object.defineProperty(l,53001,{value:k});var S=function(e){function t(){var n=e.call(this,53002,"Signaling connection timed out")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.SignalingConnectionTimeoutError=S,Object.defineProperty(l,53002,{value:S});var T=function(e){function t(){var n=e.call(this,53003,"Client received an invalid signaling message")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.SignalingIncomingMessageInvalidError=T,Object.defineProperty(l,53003,{value:T});var O=function(e){function t(){var n=e.call(this,53004,"Client sent an invalid signaling message")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.SignalingOutgoingMessageInvalidError=O,Object.defineProperty(l,53004,{value:O});var P=function(e){function t(){var n=e.call(this,53006,"Video server is busy")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.SignalingServerBusyError=P,Object.defineProperty(l,53006,{value:P});var C=function(e){function t(){var n=e.call(this,53100,"Room name is invalid")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomNameInvalidError=C,Object.defineProperty(l,53100,{value:C});var E=function(e){function t(){var n=e.call(this,53101,"Room name is too long")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomNameTooLongError=E,Object.defineProperty(l,53101,{value:E});var R=function(e){function t(){var n=e.call(this,53102,"Room name contains invalid characters")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomNameCharsInvalidError=R,Object.defineProperty(l,53102,{value:R});var j=function(e){function t(){var n=e.call(this,53103,"Unable to create Room")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomCreateFailedError=j,Object.defineProperty(l,53103,{value:j});var x=function(e){function t(){var n=e.call(this,53104,"Unable to connect to Room")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomConnectFailedError=x,Object.defineProperty(l,53104,{value:x});var L=function(e){function t(){var n=e.call(this,53105,"Room contains too many Participants")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomMaxParticipantsExceededError=L,Object.defineProperty(l,53105,{value:L});var A=function(e){function t(){var n=e.call(this,53106,"Room not found")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomNotFoundError=A,Object.defineProperty(l,53106,{value:A});var D=function(e){function t(){var n=e.call(this,53107,"MaxParticipants is out of range")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomMaxParticipantsOutOfRangeError=D,Object.defineProperty(l,53107,{value:D});var I=function(e){function t(){var n=e.call(this,53108,"RoomType is not valid")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomTypeInvalidError=I,Object.defineProperty(l,53108,{value:I});var M=function(e){function t(){var n=e.call(this,53109,"Timeout is out of range")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomTimeoutOutOfRangeError=M,Object.defineProperty(l,53109,{value:M});var N=function(e){function t(){var n=e.call(this,53110,"StatusCallbackMethod is invalid")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomStatusCallbackMethodInvalidError=N,Object.defineProperty(l,53110,{value:N});var V=function(e){function t(){var n=e.call(this,53111,"StatusCallback is invalid")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomStatusCallbackInvalidError=V,Object.defineProperty(l,53111,{value:V});var F=function(e){function t(){var n=e.call(this,53112,"Status is invalid")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomStatusInvalidError=F,Object.defineProperty(l,53112,{value:F});var B=function(e){function t(){var n=e.call(this,53113,"Room exists")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomRoomExistsError=B,Object.defineProperty(l,53113,{value:B});var U=function(e){function t(){var n=e.call(this,53114,"Room creation parameter(s) incompatible with the Room type")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomInvalidParametersError=U,Object.defineProperty(l,53114,{value:U});var H=function(e){function t(){var n=e.call(this,53115,"MediaRegion is invalid")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomMediaRegionInvalidError=H,Object.defineProperty(l,53115,{value:H});var W=function(e){function t(){var n=e.call(this,53116,"There are no media servers available in the MediaRegion")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomMediaRegionUnavailableError=W,Object.defineProperty(l,53116,{value:W});var Q=function(e){function t(){var n=e.call(this,53117,"The subscription operation requested is not supported for the Room type")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomSubscriptionOperationNotSupportedError=Q,Object.defineProperty(l,53117,{value:Q});var G=function(e){function t(){var n=e.call(this,53118,"Room completed")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomCompletedError=G,Object.defineProperty(l,53118,{value:G});var z=function(e){function t(){var n=e.call(this,53124,"The AudioOnly flag is not supported for the Room type")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomAudioOnlyFlagNotSupportedError=z,Object.defineProperty(l,53124,{value:z});var K=function(e){function t(){var n=e.call(this,53125,"The track kind is not supported by the Room")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.RoomTrackKindNotSupportedError=K,Object.defineProperty(l,53125,{value:K});var Y=function(e){function t(){var n=e.call(this,53200,"Participant identity is invalid")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.ParticipantIdentityInvalidError=Y,Object.defineProperty(l,53200,{value:Y});var J=function(e){function t(){var n=e.call(this,53201,"Participant identity is too long")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.ParticipantIdentityTooLongError=J,Object.defineProperty(l,53201,{value:J});var $=function(e){function t(){var n=e.call(this,53202,"Participant identity contains invalid characters")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.ParticipantIdentityCharsInvalidError=$,Object.defineProperty(l,53202,{value:$});var X=function(e){function t(){var n=e.call(this,53203,"The maximum number of published tracks allowed in the Room at the same time has been reached")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.ParticipantMaxTracksExceededError=X,Object.defineProperty(l,53203,{value:X});var Z=function(e){function t(){var n=e.call(this,53204,"Participant not found")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.ParticipantNotFoundError=Z,Object.defineProperty(l,53204,{value:Z});var ee=function(e){function t(){var n=e.call(this,53205,"Participant disconnected because of duplicate identity")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.ParticipantDuplicateIdentityError=ee,Object.defineProperty(l,53205,{value:ee});var te=function(e){function t(){var n=e.call(this,53300,"Track is invalid")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.TrackInvalidError=te,Object.defineProperty(l,53300,{value:te});var ne=function(e){function t(){var n=e.call(this,53301,"Track name is invalid")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.TrackNameInvalidError=ne,Object.defineProperty(l,53301,{value:ne});var re=function(e){function t(){var n=e.call(this,53302,"Track name is too long")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.TrackNameTooLongError=re,Object.defineProperty(l,53302,{value:re});var ie=function(e){function t(){var n=e.call(this,53303,"Track name contains invalid characters")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.TrackNameCharsInvalidError=ie,Object.defineProperty(l,53303,{value:ie});var oe=function(e){function t(){var n=e.call(this,53304,"Track name is duplicated")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.TrackNameIsDuplicatedError=oe,Object.defineProperty(l,53304,{value:oe});var ae=function(e){function t(){var n=e.call(this,53305,"The server has reached capacity and cannot fulfill this request")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.TrackServerTrackCapacityReachedError=ae,Object.defineProperty(l,53305,{value:ae});var se=function(e){function t(){var n=e.call(this,53400,"Client is unable to create or apply a local media description")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.MediaClientLocalDescFailedError=se,Object.defineProperty(l,53400,{value:se});var ce=function(e){function t(){var n=e.call(this,53401,"Server is unable to create or apply a local media description")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.MediaServerLocalDescFailedError=ce,Object.defineProperty(l,53401,{value:ce});var ue=function(e){function t(){var n=e.call(this,53402,"Client is unable to apply a remote media description")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.MediaClientRemoteDescFailedError=ue,Object.defineProperty(l,53402,{value:ue});var le=function(e){function t(){var n=e.call(this,53403,"Server is unable to apply a remote media description")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.MediaServerRemoteDescFailedError=le,Object.defineProperty(l,53403,{value:le});var de=function(e){function t(){var n=e.call(this,53404,"No supported codec")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.MediaNoSupportedCodecError=de,Object.defineProperty(l,53404,{value:de});var pe=function(e){function t(){var n=e.call(this,53405,"Media connection failed or Media activity ceased")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.MediaConnectionError=pe,Object.defineProperty(l,53405,{value:pe});var fe=function(e){function t(){var n=e.call(this,53407,"Media connection failed due to DTLS handshake failure")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.MediaDTLSTransportFailedError=fe,Object.defineProperty(l,53407,{value:fe});var he=function(e){function t(){var n=e.call(this,53500,"Unable to acquire configuration")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.ConfigurationAcquireFailedError=he,Object.defineProperty(l,53500,{value:he});var ve=function(e){function t(){var n=e.call(this,53501,"Unable to acquire TURN credentials")||this;return Object.setPrototypeOf(n,t.prototype),n}return o(t,e),t}(c);t.ConfigurationAcquireTurnFailedError=ve,Object.defineProperty(l,53501,{value:ve})},1608:function(e,t,n){"use strict";var r={};Object.defineProperties(r,{getStats:{enumerable:!0,value:n(2076)},getUserMedia:{enumerable:!0,value:n(2077)},MediaStream:{enumerable:!0,value:n(1865)},MediaStreamTrack:{enumerable:!0,value:n(2078)},RTCIceCandidate:{enumerable:!0,value:n(2079)},RTCPeerConnection:{enumerable:!0,value:n(2080)},RTCSessionDescription:{enumerable:!0,value:n(2087)}}),e.exports=r},1609:function(e,t,n){"use strict";var r=function(){function e(e,t,n){void 0===n&&(n=!0),Object.defineProperties(this,{_delay:{value:t,writable:!0},_fn:{value:e},_timeout:{value:null,writable:!0}}),n&&this.start()}return Object.defineProperty(e.prototype,"delay",{get:function(){return this._delay},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isSet",{get:function(){return!!this._timeout},enumerable:!1,configurable:!0}),e.prototype.setDelay=function(e){this._delay=e},e.prototype.start=function(){var e=this;this.isSet||(this._timeout=setTimeout((function(){var t=e._fn;e.clear(),t()}),this._delay))},e.prototype.clear=function(){clearTimeout(this._timeout),this._timeout=null},e.prototype.reset=function(){this.clear(),this.start()},e}();e.exports=r},1615:function(e,t,n){"use strict";e.exports={LocalAudioTrack:n(2088),LocalVideoTrack:n(2091),LocalDataTrack:n(2094)}},1616:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},d=n(373).EventEmitter,f=n(1410),h=function(e){function t(t,n){var r,o=e.call(this)||this,c=null,l=t;return n=function(e){var t=new Map;for(var n in e)t.set(n,new Set(e[n]));return t}(n),Object.defineProperties(o,{_lock:{get:function(){return c},set:function(e){c=e}},_reachableStates:{value:(r=n,Array.from(r.keys()).reduce((function(e,t){return e.set(t,_(r,t))}),new Map))},_state:{get:function(){return l},set:function(e){l=e}},_states:{value:n},_whenDeferreds:{value:new Set},isLocked:{enumerable:!0,get:function(){return null!==c}},state:{enumerable:!0,get:function(){return l}}}),o.on("stateChanged",(function(e){o._whenDeferreds.forEach((function(t){t.when(e,t.resolve,t.reject)}))})),o}return o(t,e),t.prototype._whenPromise=function(e){var t=this;if("function"!=typeof e)return Promise.reject(new Error("when() executor must be a function"));var n=f.defer();return n.when=e,this._whenDeferreds.add(n),n.promise.then((function(e){return t._whenDeferreds.delete(n),e}),(function(e){throw t._whenDeferreds.delete(n),e}))},t.prototype.bracket=function(e,t){var n,r=this;function o(e){if(r.hasLock(n)&&r.releaseLockCompletely(n),e)throw e}return this.takeLock(e).then((function(e){return t(n=e)})).then((function(e){return o(),e}),o)},t.prototype.hasLock=function(e){return this._lock===e},t.prototype.preempt=function(e,t,n){if(!v(this._states,this.state,e))throw new Error('Cannot transition from "'+this.state+'" to "'+e+'"');var r;this.isLocked&&(r=this._lock,this._lock=null);var o=null;t&&(o=this.takeLockSync(t));var c=o?null:this.takeLockSync("preemption");return this.transition(e,o||c,n),r&&r.resolve(),c&&this.releaseLock(c),o},t.prototype.releaseLock=function(e){if(!this.isLocked)throw new Error("Could not release the lock for "+e.name+" because the StateMachine is not locked");if(!this.hasLock(e))throw new Error("Could not release the lock for "+e.name+" because "+this._lock.name+" has the lock");0===e.depth?(this._lock=null,e.resolve()):e.depth--},t.prototype.releaseLockCompletely=function(e){if(!this.isLocked)throw new Error("Could not release the lock for "+e.name+" because the StateMachine is not locked");if(!this.hasLock(e))throw new Error("Could not release the lock for "+e.name+" because "+this._lock.name+" has the lock");e.depth=0,this._lock=null,e.resolve()},t.prototype.takeLock=function(e){var t=this;if("object"==typeof e){var n=e;return new Promise((function(e){e(t.takeLockSync(n))}))}var r=e;if(this.isLocked){var o=this.takeLock.bind(this,r);return this._lock.promise.then(o)}return Promise.resolve(this.takeLockSync(r))},t.prototype.takeLockSync=function(e){var t="string"==typeof e?null:e,n=t?t.name:e;if(t&&!this.hasLock(t)||!t&&this.isLocked)throw new Error("Could not take the lock for "+n+" because the lock for "+this._lock.name+" was not released");if(t)return t.depth++,t;var r=function(e){var t=f.defer();return t.name=e,t.depth=0,t}(n);return this._lock=r,r},t.prototype.transition=function(e,t,n){if(n=n||[],this.isLocked){if(!t)throw new Error("You must provide the key in order to transition");if(!this.hasLock(t))throw new Error("Could not transition using the key for "+t.name+" because "+this._lock.name+" has the lock")}else if(t)throw new Error("Key provided for "+t.name+", but the StateMachine was not locked (possibly due to preemption)");if(!v(this._states,this.state,e))throw new Error('Cannot transition from "'+this.state+'" to "'+e+'"');this._state=e,this.emit.apply(this,l([],c(["stateChanged",e].concat(n))))},t.prototype.tryTransition=function(e,t,n){try{this.transition(e,t,n)}catch(e){return!1}return!0},t.prototype.when=function(e){var t=this;return this.state===e?Promise.resolve(this):v(this._reachableStates,this.state,e)?this._whenPromise((function(n,r,o){n===e?r(t):v(t._reachableStates,n,e)||o(m(n,e))})):Promise.reject(m(this.state,e))},t}(d);function v(e,t,n){return e.get(t).has(n)}function _(e,t,n){return n=n||new Set,e.get(t).forEach((function(t){n.has(t)||(n.add(t),_(e,t,n).forEach(n.add,n))})),n}function m(e,t){return new Error('"'+t+'" cannot be reached from "'+e+'"')}e.exports=h},1617:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(373),l=0,d=function(e){function t(t,n,r){var o=e.call(this)||this;return Object.defineProperties(o,{_instanceId:{value:l++},channel:{value:n},_log:{value:r.log.createLog("default",o)},_getReceiver:{value:t},_receiverPromise:{value:null,writable:!0},_transport:{value:null,writable:!0}}),o}return o(t,e),Object.defineProperty(t.prototype,"isSetup",{get:function(){return!!this._receiverPromise},enumerable:!1,configurable:!0}),t.prototype.toString=function(){return"[MediaSignaling #"+this._instanceId+":"+this.channel+"]"},t.prototype.setup=function(e){var t=this;this._teardown(),this._log.info("setting up msp transport for id:",e);var n=this._getReceiver(e).then((function(e){if("data"!==e.kind&&t._log.error("Expected a DataTrackReceiver"),t._receiverPromise===n){try{t._transport=e.toDataTransport(),t.emit("ready",t._transport)}catch(e){t._log.error("Failed to toDataTransport: "+e.message)}e.once("close",(function(){return t._teardown()}))}}));this._receiverPromise=n},t.prototype._teardown=function(){this._transport&&(this._log.info("Tearing down"),this._transport=null,this._receiverPromise=null,this.emit("teardown"))},t}(c);e.exports=d},1684:function(e,t,n){"use strict";var r=n(373).EventEmitter,o=n(1410).hidePrivateAndCertainPublicPropertiesInClass;e.exports=o(r,["domain"])},1694:function(e,t,n){"use strict";var r=n(1439).flatMap,o=n(1439).guessBrowser,c=null;var l=null;function d(e){return e&&function(){if("boolean"==typeof c)return c;if("undefined"==typeof RTCPeerConnection)return c=!1;try{new RTCPeerConnection({sdpSemantics:"foo"}),c=!1}catch(e){c=!0}return c}()?{"plan-b":"planb","unified-plan":"unified"}[e]:function(){if(!l)if("undefined"!=typeof RTCPeerConnection&&"addTransceiver"in RTCPeerConnection.prototype)try{(new RTCPeerConnection).addTransceiver("audio"),l="unified"}catch(e){l="planb"}else l="planb";return l}()}function f(pattern,e){return(e.match(new RegExp(pattern,"gm"))||[]).reduce((function(e,line){var t=line.match(new RegExp(pattern));return t?e.add(t[1]):e}),new Set)}function h(pattern,e){return f(pattern,e)}function v(e){return h("^a=ssrc:[0-9]+ +msid:.+ +(.+) *$",e)}function _(e){return h("^a=msid:.+ +(.+) *$",e)}function m(e,t){return f("^a=ssrc:([0-9]+) +msid:[^ ]+ +"+t+" *$",e)}function y(e,t,n){return t=t||".*",n=n||".*",e.split("\r\nm=").slice(1).map((function(e){return"m="+e})).filter((function(e){var r=new RegExp("m="+t,"gm"),o=new RegExp("a="+n,"gm");return r.test(e)&&o.test(e)}))}function w(e){return Array.from(f("^a=ssrc:([0-9]+) +.*$",e))}function k(e,t){var n=y(e),o=new RegExp("^a=msid:[^ ]+ +"+t+" *$","gm"),c=n.filter((function(e){return e.match(o)}));return new Set(r(c,w))}function S(e,t,n){return new Map(Array.from(e(n)).map((function(e){return[e,t(n,e)]})))}function T(e){return S(v,m,e)}function O(e){return S(_,k,e)}function P(e,t,n){var r=e(n),o=new Map;r.forEach((function(e,r){if(t.has(r)){var c=Array.from(t.get(r)),l=Array.from(e);c.forEach((function(e,i){var t=l[i];o.set(t,e);var pattern="^a=ssrc:"+t+" (.*)$",r="a=ssrc:"+e+" $1";n=n.replace(new RegExp(pattern,"gm"),r)}))}else t.set(r,e)}));var pattern="^(a=ssrc-group:[^ ]+ +)(.*)$";return(n.match(new RegExp(pattern,"gm"))||[]).forEach((function(line){var e=line.match(new RegExp(pattern));if(e){var t=e[1],r=e[2].split(" ").map((function(e){var t=o.get(e);return t||e})).join(" ");n=n.replace(e[0],t+r)}})),n}t.getSdpFormat=function(e){return{chrome:d(e),firefox:"unified",safari:"undefined"!=typeof RTCRtpTransceiver&&"currentDirection"in RTCRtpTransceiver.prototype?"unified":"planb"}[o()]||null},t.getMediaSections=y,t.getPlanBTrackIds=v,t.getUnifiedPlanTrackIds=_,t.getPlanBSSRCs=m,t.getUnifiedPlanSSRCs=k,t.updatePlanBTrackIdsToSSRCs=function(e,t){return P(T,e,t)},t.updateUnifiedPlanTrackIdsToSSRCs=function(e,t){return P(O,e,t)}},1695:function(e,t,n){"use strict";var r="undefined"!=typeof AudioContext?AudioContext:"undefined"!=typeof webkitAudioContext?webkitAudioContext:null,o=function(){function e(t){t=Object.assign({AudioContext:r},t),Object.defineProperties(this,{_AudioContext:{value:t.AudioContext},_audioContext:{value:null,writable:!0},_holders:{value:new Set},AudioContextFactory:{enumerable:!0,value:e}})}return e.prototype.getOrCreate=function(e){if(!this._holders.has(e)&&(this._holders.add(e),this._AudioContext&&!this._audioContext))try{this._audioContext=new this._AudioContext}catch(e){}return this._audioContext},e.prototype.release=function(e){this._holders.has(e)&&(this._holders.delete(e),!this._holders.size&&this._audioContext&&(this._audioContext.close(),this._audioContext=null))},e}();e.exports=new o},1816:function(e,t,n){"use strict";var r=n(373).EventEmitter;function o(){Object.defineProperties(this,{_eventEmitter:{value:new r}})}o.prototype.dispatchEvent=function(e){return this._eventEmitter.emit(e.type,e)},o.prototype.addEventListener=function(){return this._eventEmitter.addListener.apply(this._eventEmitter,arguments)},o.prototype.removeEventListener=function(){return this._eventEmitter.removeListener.apply(this._eventEmitter,arguments)},e.exports=o},1817:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1684),l=n(1410),d=l.buildLogLevels,f=l.valueToJSON,h=n(1425).DEFAULT_LOG_LEVEL,v=n(1570),_=0,m=function(e){function t(t,n,r){var o;r=Object.assign({name:t,log:null,logLevel:h},r),o=e.call(this)||this;var c=String(r.name),l=d(r.logLevel),f=r.log?r.log.createLog("media",o):new v("media",o,l,r.loggerName);return Object.defineProperties(o,{_instanceId:{value:++_},_log:{value:f},kind:{enumerable:!0,value:n},name:{enumerable:!0,value:c}}),o}return o(t,e),t.prototype.toJSON=function(){return f(this)},t}(c);e.exports=m},1818:function(e,t,n){"use strict";var r=function(){function e(e){var t=this;void 0===e&&(e=1),Object.defineProperties(this,{_listeners:{value:[]},_onVisibilityChange:{value:function(){t._emitVisible("visible"===document.visibilityState)}}});for(var i=0;i<e;i++)this._listeners.push([])}return e.prototype.clear=function(){for(var e=this._listeners.length,i=0;i<e;i++)this._listeners[i]=[]},e.prototype._listenerCount=function(){return this._listeners.reduce((function(e,t){return e+t.length}),0)},e.prototype._emitVisible=function(e){for(var t=this,n=Promise.resolve(),r=function(r){n=n.then((function(){return t._emitVisiblePhase(r,e)}))},o=1;o<=this._listeners.length;o++)r(o);return n},e.prototype._emitVisiblePhase=function(e,t){var n=this._listeners[e-1];return Promise.all(n.map((function(e){var n=e(t);return n instanceof Promise?n:Promise.resolve(n)})))},e.prototype._start=function(){document.addEventListener("visibilitychange",this._onVisibilityChange)},e.prototype._stop=function(){document.removeEventListener("visibilitychange",this._onVisibilityChange)},e.prototype.onVisibilityChange=function(e,t){if("number"!=typeof e||e<=0||e>this._listeners.length)throw new Error("invalid phase: ",e);return this._listeners[e-1].push(t),1===this._listenerCount()&&this._start(),this},e.prototype.offVisibilityChange=function(e,t){if("number"!=typeof e||e<=0||e>this._listeners.length)throw new Error("invalid phase: ",e);var n=this._listeners[e-1],r=n.indexOf(t);return-1!==r&&(n.splice(r,1),0===this._listenerCount()&&this._stop()),this},e}();e.exports=new r(2)},1819:function(e,t,n){"use strict";var r=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},o=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},c=function(){function e(e,t){var n=this;Object.defineProperties(this,{_isCancelable:{writable:!0,value:!0},_isCanceled:{writable:!0,value:!1},_onCancel:{value:t}}),Object.defineProperty(this,"_promise",{value:new Promise((function(t,r){e((function(e){n._isCancelable=!1,t(e)}),(function(e){n._isCancelable=!1,r(e)}),(function(){return n._isCanceled}))}))})}return e.reject=function(t){return new e((function(e,n){n(t)}),(function(){}))},e.resolve=function(t){return new e((function(e){e(t)}),(function(){}))},e.prototype.cancel=function(){return this._isCancelable&&(this._isCanceled=!0,this._onCancel()),this},e.prototype.catch=function(){var t=[].slice.call(arguments),n=this._promise;return new e((function(e,c){n.catch.apply(n,o([],r(t))).then(e,c)}),this._onCancel)},e.prototype.then=function(){var t=[].slice.call(arguments),n=this._promise;return new e((function(e,c){n.then.apply(n,o([],r(t))).then(e,c)}),this._onCancel)},e.prototype.finally=function(){var t=[].slice.call(arguments),n=this._promise;return new e((function(e,c){n.finally.apply(n,o([],r(t))).then(e,c)}),this._onCancel)},e}();e.exports=c},1820:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1888),l=n(1425),d=l.typeErrors,f=l.trackPriority,h=function(e){function t(t,track,n,r){var o=e.call(this,track.name,t.sid,r)||this;return Object.defineProperties(o,{_reemitTrackEvent:{value:function(){return o.emit(o.isTrackEnabled?"trackEnabled":"trackDisabled")}},_signaling:{value:t},_unpublish:{value:n},isTrackEnabled:{enumerable:!0,get:function(){return"data"===this.track.kind||this.track.isEnabled}},kind:{enumerable:!0,value:track.kind},priority:{enumerable:!0,get:function(){return t.updatedPriority}},track:{enumerable:!0,value:track}}),track.on("disabled",o._reemitTrackEvent),track.on("enabled",o._reemitTrackEvent),o}return o(t,e),t.prototype.toString=function(){return"[LocalTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t.prototype.setPriority=function(e){var t=Object.values(f);if(!t.includes(e))throw d.INVALID_VALUE("priority",t);return this._signaling.setPriority(e),this},t.prototype.unpublish=function(){return this.track.removeListener("disabled",this._reemitTrackEvent),this.track.removeListener("enabled",this._reemitTrackEvent),this._unpublish(this),this},t}(c);e.exports=h},1821:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){var r=e.call(this,t.name,t.sid,n)||this;Object.defineProperties(r,{_signaling:{value:t},_track:{value:null,writable:!0},isSubscribed:{enumerable:!0,get:function(){return!!this._track}},isTrackEnabled:{enumerable:!0,get:function(){return t.isEnabled}},kind:{enumerable:!0,value:t.kind},publishPriority:{enumerable:!0,get:function(){return t.priority}},track:{enumerable:!0,get:function(){return this._track}}});var o=t.error,c=t.isEnabled,l=t.isSwitchedOff,d=t.priority;return t.on("updated",(function(){if(o!==t.error)return o=t.error,void r.emit("subscriptionFailed",t.error);c!==t.isEnabled&&(c=t.isEnabled,r.track&&r.track._setEnabled(t.isEnabled),r.emit(t.isEnabled?"trackEnabled":"trackDisabled")),l!==t.isSwitchedOff&&(r._log.debug(r.trackSid+": "+(l?"OFF":"ON")+" => "+(t.isSwitchedOff?"OFF":"ON")),l=t.isSwitchedOff,r.track?(r.track._setSwitchedOff(t.isSwitchedOff),r.emit(l?"trackSwitchedOff":"trackSwitchedOn",r.track)):l&&r._log.warn("Track was not subscribed when switched Off.")),d!==t.priority&&(d=t.priority,r.emit("publishPriorityChanged",d))})),r}return o(t,e),t.prototype.toString=function(){return"[RemoteTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t.prototype._subscribed=function(track){!this._track&&track&&(this._track=track,this.emit("subscribed",track))},t.prototype._unsubscribe=function(){if(this._track){var track=this._track;this._track=null,this.emit("unsubscribed",track)}},t}(n(1888));e.exports=c},1822:function(e,t,n){e.exports=n(2133)},1823:function(e,t,n){"use strict";var r=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},o=n(1410),c=o.difference,l=o.flatMap,d=n(2137),f={0:"PCMU",8:"PCMA"};function h(section){return Array.from(_(section)).reduce((function(e,t){var n=t[0],r=t[1],o=e.get(r)||[];return e.set(r,o.concat(n))}),new Map)}function v(e){return w(e).reduce((function(e,t){var n=y(t);return n?e.set(n,t):e}),new Map)}function _(e){return k(e).reduce((function(t,n){var r=new RegExp("a=rtpmap:"+n+" ([^/]+)"),o=e.match(r),c=o?o[1].toLowerCase():f[n]?f[n].toLowerCase():"";return t.set(n,c)}),new Map)}function m(e,t){var n=new RegExp("^a=fmtp:"+e+" (.+)$","m"),o=t.match(n);return o&&o[1].split(";").reduce((function(e,t){var n=r(t.split("="),2),o=n[0],c=n[1];return e[o]=isNaN(c)?c:parseInt(c,10),e}),{})}function y(e){var t=e.match(/^a=mid:(.+)$/m);return t&&t[1]}function w(e,t,n){return e.replace(/\r\n\r\n$/,"\r\n").split("\r\nm=").slice(1).map((function(e){return"m="+e})).filter((function(e){var r=new RegExp("m="+(t||".*"),"gm"),o=new RegExp("a="+(n||".*"),"gm");return r.test(e)&&o.test(e)}))}function k(section){var e=section.split("\r\n")[0].match(/([0-9]+)/g);return e?e.slice(1).map((function(e){return parseInt(e,10)})):[]}function S(e,section){var t=section.split("\r\n"),n=t[0],r=t.slice(1);return[n=n.replace(/([0-9]+\s?)+$/,e.join(" "))].concat(r).join("\r\n")}function T(section,e,t){if(!/^m=(audio|video)/.test(section))return section;var n=y(section),o=n&&e.get(n);if(!o)return section;var c=_(o),d=h(section),f=l(Array.from(c),(function(e){var n=r(e,2),c=n[0],l=n[1];return"rtx"===l||t.includes(l)?[]:function(e,t,n,section,r){var o=n.get(e)||[];if(o.length<=1)return o;var c=m(t,r);if(!c)return o;var l=o.find((function(e){var t=m(e,section);return t&&Object.keys(c).every((function(e){return c[e]===t[e]}))}));return"number"==typeof l?[l]:o}(l,c,d,section,o)})),v=d.get("rtx")||[];f=f.concat(v.filter((function(e){var t=m(e,section);return t&&f.includes(t.apt)})));var w=section.split("\r\n").filter((function(line){var e=line.match(/^a=(rtpmap|fmtp|rtcp-fb):(.+) .+$/),t=e&&e[2];return!e||t&&f.includes(parseInt(t,10))}));return S(k(section).filter((function(e){return f.includes(e)})),w.join("\r\n"))}function O(e,t){var n=w(e);return[e.split("\r\nm=")[0]].concat(n.map((function(e){if(!/^m=(audio|video)/.test(e))return e;var n=y(e);if(!n)return e;var o=t.get(n);if(!o)return e;var c=(e.match(/^a=msid:(.+)$/m)||[])[1];if(!c)return e;var l=r(c.split(" "),2),d=l[0],f=l[1],h=new RegExp("msid:"+d+(f?" "+f:"")+"$","gm");return e.replace(h,"msid:"+d+" "+o)}))).join("\r\n")}function P(e,t){return"a=fmtp:"+e+" "+Object.entries(t).map((function(e){var t=r(e,2);return t[0]+"="+t[1]})).join(";")}t.addOrRewriteNewTrackIds=function(e,t,n){var o=Array.from(n).reduce((function(n,o){var c=r(o,2),l=c[0],d=c[1];return w(e,l,"send(only|recv)").map(y).filter((function(e){return!t.has(e)})).forEach((function(e,i){return n.set(e,d[i])})),n}),new Map);return O(e,o)},t.addOrRewriteTrackIds=O,t.createCodecMapForMediaSection=h,t.createPtToCodecName=_,t.disableRtx=function(e){var t=w(e);return[e.split("\r\nm=")[0]].concat(t.map((function(e){if(!/^m=video/.test(e))return e;var t=h(e).get("rtx");if(!t)return e;var n=new Set(k(e));t.forEach((function(e){return n.delete(e)}));var r=e.match(/a=ssrc-group:FID [0-9]+ ([0-9]+)/),o=r&&r[1],c=[/^a=fmtp:.+ apt=.+$/,/^a=rtpmap:.+ rtx\/.+$/,/^a=ssrc-group:.+$/].concat(o?[new RegExp("^a=ssrc:"+o+" .+$")]:[]);return e=e.split("\r\n").filter((function(line){return c.every((function(e){return!e.test(line)}))})).join("\r\n"),S(Array.from(n),e)}))).join("\r\n")},t.enableDtxForOpus=function(e,t){var n=w(e),r=e.split("\r\nm=")[0];return t=t||n.filter((function(section){return/^m=audio/.test(section)})).map(y),[r].concat(n.map((function(section){if(!/^m=audio/.test(section))return section;var e=h(section).get("opus");if(!e)return section;var n=m(e,section);if(!n)return section;var r=P(e,n),o=new RegExp(r),c=y(section);t.includes(c)?n.usedtx=1:delete n.usedtx;var l=P(e,n);return section.replace(o,l)}))).join("\r\n")},t.filterLocalCodecs=function(e,t){var n=w(e),r=e.split("\r\nm=")[0],o=v(t);return[r].concat(n.map((function(e){return T(e,o,[])}))).join("\r\n")},t.getMediaSections=w,t.removeSSRCAttributes=function(e,t){return e.split("\r\n").filter((function(line){return!t.find((function(e){return new RegExp("a=ssrc:.*"+e+":","g").test(line)}))})).join("\r\n")},t.revertSimulcast=function(e,t,n,r){void 0===r&&(r=!1);var o=v(n),c=v(t),l=w(e);return[e.split("\r\nm=")[0]].concat(l.map((function(section){if(section=section.replace(/\r\n$/,""),!/^m=video/.test(section))return section;var e=section.match(/^a=mid:(.+)$/m),t=e&&e[1];if(!t)return section;var n=o.get(t),l=_(n),d=k(n),f=d.length&&"vp8"===l.get(d[0]);return r||!f?c.get(t).replace(/\r\n$/,""):section}))).concat("").join("\r\n")},t.setCodecPreferences=function(e,t,n){var r=w(e);return[e.split("\r\nm=")[0]].concat(r.map((function(section){if(!/^m=(audio|video)/.test(section))return section;var e=section.match(/^m=(audio|video)/)[1],r=h(section),o=function(e,t){t=t.map((function(e){return e.codec.toLowerCase()}));var n=l(t,(function(t){return e.get(t)||[]})),r=c(Array.from(e.keys()),t),o=l(r,(function(t){return e.get(t)}));return n.concat(o)}(r,"audio"===e?t:n),d=S(o,section),f=r.get("pcma")||[],v=r.get("pcmu")||[];return("audio"===e?new Set(f.concat(v)):new Set).has(o[0])?d.replace(/\r\nb=(AS|TIAS):([0-9]+)/g,""):d}))).join("\r\n")},t.setSimulcast=function(e,t){var n=w(e);return[e.split("\r\nm=")[0]].concat(n.map((function(section){if(section=section.replace(/\r\n$/,""),!/^m=video/.test(section))return section;var e=h(section),n=k(section),r=new Set(e.get("vp8")||[]);return n.some((function(e){return r.has(e)}))?d(section,t):section}))).concat("").join("\r\n")}},1824:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1616),l=n(2161),d={connecting:["connected"],connected:["disconnected","reconnecting"],reconnecting:["connected","disconnected"],disconnected:[]},f=function(e){function t(){var t=e.call(this,"connecting",d)||this;return Object.defineProperties(t,{_identity:{writable:!0,value:null},_networkQualityLevel:{value:null,writable:!0},_networkQualityStats:{value:null,writable:!0},_sid:{writable:!0,value:null},identity:{enumerable:!0,get:function(){return this._identity}},sid:{enumerable:!0,get:function(){return this._sid}},tracks:{enumerable:!0,value:new Map}}),t}return o(t,e),Object.defineProperty(t.prototype,"networkQualityLevel",{get:function(){return this._networkQualityLevel},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"networkQualityStats",{get:function(){return this._networkQualityStats},enumerable:!1,configurable:!0}),t.prototype.addTrack=function(track){return this.tracks.set(track.id||track.sid,track),this.emit("trackAdded",track),this},t.prototype.disconnect=function(){return"disconnected"!==this.state&&(this.preempt("disconnected"),!0)},t.prototype.removeTrack=function(track){var e=this.tracks.get(track.id||track.sid);return this.tracks.delete(track.id||track.sid),e&&this.emit("trackRemoved",track),e||null},t.prototype.setNetworkQualityLevel=function(e,t){this._networkQualityLevel!==e&&(this._networkQualityLevel=e,this._networkQualityStats=t&&(t.audio||t.video)?new l(t):null,this.emit("networkQualityLevelChanged"))},t.prototype.connect=function(e,t){return("connecting"===this.state||"reconnecting"===this.state)&&(this._sid||(this._sid=e),this._identity||(this._identity=t),this.preempt("connected"),!0)},t.prototype.reconnecting=function(){return("connecting"===this.state||"connected"===this.state)&&(this.preempt("reconnecting"),!0)},t}(c);e.exports=f},1863:function(e){e.exports=JSON.parse('{"_args":[["twilio-video@2.21.1","D:\\\\languworks\\\\langu-frontend"]],"_from":"twilio-video@2.21.1","_id":"twilio-video@2.21.1","_inBundle":false,"_integrity":"sha512-odVq6tBF2OGwtOncvGuQjyuv6fAIkIWVzZZxlYnFkbUen6eq3A9fCtNuKLb+GnXFwXLzzRHByi3yJzQgkWbJow==","_location":"/twilio-video","_phantomChildren":{},"_requested":{"type":"version","registry":true,"raw":"twilio-video@2.21.1","name":"twilio-video","escapedName":"twilio-video","rawSpec":"2.21.1","saveSpec":null,"fetchSpec":"2.21.1"},"_requiredBy":["/"],"_resolved":"https://registry.npmjs.org/twilio-video/-/twilio-video-2.21.1.tgz","_spec":"2.21.1","_where":"D:\\\\languworks\\\\langu-frontend","author":{"name":"Mark Andrus Roberts","email":"<EMAIL>"},"browser":{"ws":"./src/ws.js","xmlhttprequest":"./src/xmlhttprequest.js"},"bugs":{"url":"https://github.com/twilio/twilio-video.js/issues"},"contributors":[{"name":"Ryan Rowland","email":"<EMAIL>"},{"name":"Manjesh Malavalli","email":"<EMAIL>"},{"name":"Makarand Patwardhan","email":"<EMAIL>"}],"dependencies":{"backoff":"^2.5.0","events":"^3.3.0","util":"^0.12.4","ws":"^7.4.6","xmlhttprequest":"^1.8.0"},"description":"Twilio Video JavaScript Library","devDependencies":{"@babel/core":"^7.14.2","@babel/preset-env":"^7.14.2","@babel/preset-typescript":"^7.13.0","@types/express":"^4.11.0","@types/node":"^8.5.1","@types/selenium-webdriver":"^3.0.8","@types/ws":"^3.2.1","@typescript-eslint/eslint-plugin":"^4.13.0","@typescript-eslint/parser":"^4.0.0","babel-cli":"^6.26.0","babel-preset-es2015":"^6.24.1","browserify":"^17.0.0","cheerio":"^0.22.0","cors":"^2.8.5","electron":"^9.1.0","envify":"^4.0.0","eslint":"^6.2.1","eslint-config-standard":"^14.0.0","eslint-plugin-import":"^2.18.2","eslint-plugin-node":"^9.1.0","eslint-plugin-promise":"^4.2.1","eslint-plugin-standard":"^4.0.1","express":"^4.16.2","glob":"^7.1.7","ink-docstrap":"^1.3.2","inquirer":"^7.0.0","is-docker":"^2.0.0","jsdoc":"^3.5.5","jsdoc-babel":"^0.5.0","json-loader":"^0.5.7","karma":"^5.0.2","karma-browserify":"^8.0.0","karma-chrome-launcher":"^2.0.0","karma-edgium-launcher":"^4.0.0-0","karma-electron":"^6.1.0","karma-firefox-launcher":"^1.3.0","karma-htmlfile-reporter":"^0.3.8","karma-junit-reporter":"^1.2.0","karma-mocha":"^1.3.0","karma-safari-launcher":"^1.0.0","karma-spec-reporter":"0.0.32","karma-typescript":"^5.5.1","karma-typescript-es6-transform":"^5.5.1","mocha":"^3.2.0","mock-require":"^3.0.3","ncp":"^2.0.0","node-http-server":"^8.1.2","npm-run-all":"^4.0.2","nyc":"^15.1.0","requirejs":"^2.3.3","rimraf":"^2.6.1","simple-git":"^1.126.0","sinon":"^4.0.1","ts-node":"4.0.1","tslint":"5.8.0","twilio":"^3.49.0","twilio-release-tool":"^1.0.2","typescript":"4.2.2","uglify-js":"^2.8.22","vinyl-fs":"^2.4.4","vinyl-source-stream":"^1.1.0","watchify":"^3.11.1","webrtc-adapter":"^7.7.1"},"engines":{"node":">=0.12"},"homepage":"https://twilio.com","keywords":["twilio","webrtc","library","javascript","video","rooms"],"license":"BSD-3-Clause","main":"./es5/index.js","name":"twilio-video","repository":{"type":"git","url":"git+https://github.com/twilio/twilio-video.js.git"},"scripts":{"build":"npm-run-all clean lint docs test:unit test:integration build:es5 build:js build:min.js test:umd","build:es5":"rimraf ./es5 && tsc tsdef/twilio-video-tests.ts --noEmit --lib es2018,dom && tsc ","build:js":"node ./scripts/build.js ./src/twilio-video.js ./LICENSE.md ./dist/twilio-video.js","build:min.js":"uglifyjs ./dist/twilio-video.js -o ./dist/twilio-video.min.js --comments \\"/^! twilio-video.js/\\" -b beautify=false,ascii_only=true","build:quick":"npm-run-all clean lint docs build:es5 build:js build:min.js","clean":"rimraf ./coverage ./es5 ./dist","docs":"node ./scripts/docs.js ./dist/docs","lint":"npm-run-all lint:js lint:ts","lint:js":"eslint ./lib ./test/*.js ./docker/**/*.js ./test/framework/*.js ./test/lib/*.js ./test/integration/** ./test/unit/** ","lint:ts":"eslint ./tsdef/*.ts ./lib/**/*.ts","printVersion":"node --version && npm --version","test":"npm-run-all test:unit test:integration","test:crossbrowser":"npm-run-all test:crossbrowser:*","test:crossbrowser:build":"npm-run-all test:crossbrowser:build:*","test:crossbrowser:build:browser":"cd ./test/crossbrowser && browserify lib/crossbrowser/src/browser/index.js > src/browser/index.js","test:crossbrowser:build:clean":"rimraf ./test/crossbrowser/lib ./test/crossbrowser/src/browser/index.js","test:crossbrowser:build:lint":"cd ./test/crossbrowser && tslint --project tsconfig.json","test:crossbrowser:build:tsc":"cd ./test/crossbrowser && tsc","test:crossbrowser:test":"cd ./test/crossbrowser && mocha --compilers ts:ts-node/register test/integration/spec/**/*.ts","test:framework":"npm-run-all test:framework:install test:framework:no-framework test:framework:react","test:framework:angular":"npm-run-all test:framework:angular:*","test:framework:angular:install":"cd ./test/framework/twilio-video-angular && rimraf ./node_modules package-lock.json && npm install","test:framework:angular:run":"mocha ./test/framework/twilio-video-angular.js","test:framework:install":"npm install chromedriver && npm install selenium-webdriver && npm install geckodriver && npm install puppeteer","test:framework:no-framework":"npm-run-all test:framework:no-framework:*","test:framework:no-framework:run":"mocha ./test/framework/twilio-video-no-framework.js","test:framework:react":"npm-run-all test:framework:react:*","test:framework:react:build":"cd ./test/framework/twilio-video-react && npm run build","test:framework:react:install":"cd ./test/framework/twilio-video-react && rimraf ./node_modules package-lock.json && npm install","test:framework:react:run":"mocha ./test/framework/twilio-video-react.js","test:framework:react:test":"node ./scripts/framework.js twilio-video-react","test:integration":"npm run build:es5 && node ./scripts/karma.js karma/integration.conf.js","test:integration:adapter":"node ./scripts/karma.js karma/integration.adapter.conf.js","test:sdkdriver":"npm-run-all test:sdkdriver:*","test:sdkdriver:build":"npm-run-all test:sdkdriver:build:*","test:sdkdriver:build:clean":"rimraf ./test/lib/sdkdriver/lib ./test/lib/sdkdriver/test/integration/browser/index.js","test:sdkdriver:build:lint":"cd ./test/lib/sdkdriver && tslint --project tsconfig.json","test:sdkdriver:build:tsc":"cd ./test/lib/sdkdriver && tsc --rootDir src","test:sdkdriver:test":"npm-run-all test:sdkdriver:test:*","test:sdkdriver:test:integration":"npm-run-all test:sdkdriver:test:integration:*","test:sdkdriver:test:integration:browser":"cd ./test/lib/sdkdriver/test/integration && browserify browser/browser.js > browser/index.js","test:sdkdriver:test:integration:run":"cd ./test/lib/sdkdriver && mocha --compilers ts:ts-node/register test/integration/spec/**/*.ts","test:sdkdriver:test:unit":"cd ./test/lib/sdkdriver && mocha --compilers ts:ts-node/register test/unit/spec/**/*.ts","test:serversiderender":"mocha ./test/serversiderender/index.js","test:umd":"mocha ./test/umd/index.js","test:umd:install":"npm install puppeteer@5.5.0","test:unit":"npm-run-all printVersion build:es5 && nyc --report-dir=./coverage --include=lib/**/* --reporter=html --reporter=lcov --reporter=text mocha -r ts-node/register ./test/unit/*","watch":"tsc -w"},"title":"Twilio Video","types":"./tsdef/index.d.ts","version":"2.21.1"}')},1864:function(e,t){var n="1234567890abcdef";function r(e){for(var t="",i=0;i<32;i++)t+=n.charAt(Math.floor(Math.random()*n.length));return""+e+t}t.sessionSID=r("SS"),t.createSID=r},1865:function(e,t,n){"use strict";"function"==typeof MediaStream?e.exports=MediaStream:e.exports=function(){throw new Error("MediaStream is not supported")}},1866:function(e,t,n){"use strict";e.exports=function e(t){if(!(this instanceof e))return new e(t);var n=t&&"rollback"===t.type?null:new RTCSessionDescription(t);Object.defineProperties(this,{_description:{get:function(){return n}},sdp:{enumerable:!0,value:n?n.sdp:t.sdp},type:{enumerable:!0,value:n?n.type:t.type}})}},1867:function(e,t,n){"use strict";var r=n(1439).defer,o={high:new Set(["low"]),low:new Set(["high"])};function c(e){if(!(this instanceof c))return new c(e);var t=e||"low";Object.defineProperties(this,{_state:{set:function(e){if(t!==e){t=e;var n=this._whenDeferreds.get(t);n.forEach((function(e){e.resolve(this)}),this),n.clear()}},get:function(){return t}},_whenDeferreds:{value:new Map([["high",new Set],["low",new Set]])},state:{enumerable:!0,get:function(){return this._state}}})}function l(e,t){return new Error('Cannot transition from "'+e+'" to "'+t+'"')}c.prototype.lower=function(){return this.transition("low")},c.prototype.raise=function(){return this.transition("high")},c.prototype.transition=function(e){if(!o[this.state].has(e))throw l(this.state,e);return this._state=e,this},c.prototype.when=function(e){if(this.state===e)return Promise.resolve(this);if(!o[this.state].has(e))return Promise.reject(l(this.state,e));var t=r();return this._whenDeferreds.get(e).add(t),t.promise},e.exports=c},1868:function(e,t,n){"use strict";e.exports=RTCSessionDescription},1869:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){return e.call(this,t,n)||this}return o(t,e),t.prototype._start=function(){e.prototype._start.call(this),this._dummyEl&&(this._dummyEl.srcObject=null,this._dummyEl=null)},t.prototype.attach=function(){return e.prototype.attach.apply(this,arguments)},t.prototype.detach=function(){return e.prototype.detach.apply(this,arguments)},t}(n(1870));e.exports=c},1870:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1439),l=c.guessBrowser,d=c.isIOSChrome,f=n(1608).MediaStream,h=n(1410),v=h.waitForEvent,_=h.waitForSometime,m=n(1871),y=function(e){function t(t,n){var r=this;n=Object.assign({playPausedElementsIfNotBackgrounded:("safari"===l()||d())&&"object"==typeof document&&"function"==typeof document.addEventListener&&"string"==typeof document.visibilityState},n),r=e.call(this,t.id,t.kind,n)||this;var o=!1;return n=Object.assign({MediaStream:f},n),Object.defineProperties(r,{_attachments:{value:new Set},_dummyEl:{value:null,writable:!0},_elShims:{value:new WeakMap},_isStarted:{get:function(){return o},set:function(e){o=e}},_playPausedElementsIfNotBackgrounded:{value:n.playPausedElementsIfNotBackgrounded},_shouldShimAttachedElements:{value:n.workaroundWebKitBug212780||n.playPausedElementsIfNotBackgrounded},_unprocessedTrack:{value:null,writable:!0},_MediaStream:{value:n.MediaStream},isStarted:{enumerable:!0,get:function(){return o}},mediaStreamTrack:{enumerable:!0,get:function(){return this._unprocessedTrack||t.track}},processedTrack:{enumerable:!0,value:null,writable:!0}}),r._initialize(),r}return o(t,e),t.prototype._start=function(){this._log.debug("Started"),this._isStarted=!0,this._dummyEl&&(this._dummyEl.oncanplay=null),this.emit("started",this)},t.prototype._initialize=function(){var e=this;this._log.debug("Initializing"),this._dummyEl=this._createElement(),this.mediaStreamTrack.addEventListener("ended",(function t(){e._end(),e.mediaStreamTrack.removeEventListener("ended",t)})),this._dummyEl&&(this._dummyEl.muted=!0,this._dummyEl.oncanplay=this._start.bind(this,this._dummyEl),this._attach(this._dummyEl,this.mediaStreamTrack),this._attachments.delete(this._dummyEl))},t.prototype._end=function(){this._log.debug("Ended"),this._dummyEl&&(this._dummyEl.remove(),this._dummyEl.srcObject=null,this._dummyEl.oncanplay=null,this._dummyEl=null)},t.prototype.attach=function(e){var t=this;if("string"==typeof e?e=this._selectElement(e):e||(e=this._createElement()),this._log.debug("Attempting to attach to element:",e),e=this._attach(e),this._shouldShimAttachedElements&&!this._elShims.has(e)){var n=this._playPausedElementsIfNotBackgrounded?function(){return function(e,t){var n=e.tagName.toLowerCase();t.warn("Unintentionally paused:",e),Promise.race([v(document,"visibilitychange"),_(1e3)]).then((function(){"visible"===document.visibilityState&&m.whenResolved("audio").then((function(){return t.info("Playing unintentionally paused <"+n+"> element"),t.debug("Element:",e),e.play()})).then((function(){t.info("Successfully played unintentionally paused <"+n+"> element"),t.debug("Element:",e)})).catch((function(r){t.warn("Error while playing unintentionally paused <"+n+"> element:",{error:r,el:e})}))}))}(e,t._log)}:null;this._elShims.set(e,function(e,t){void 0===t&&(t=null);var n=e.pause,r=e.play,o=!1;e.pause=function(){return o=!0,n.call(e)},e.play=function(){return o=!1,r.call(e)};var c=t?function(){o||t()}:null;c&&e.addEventListener("pause",c);return{pausedIntentionally:function(){return o},unShim:function(){e.pause=n,e.play=r,c&&e.removeEventListener("pause",c)}}}(e,n))}return e},t.prototype._attach=function(e,t){void 0===t&&(t=this.processedTrack||this.mediaStreamTrack);var n=e.srcObject;n instanceof this._MediaStream||(n=new this._MediaStream);var r="audio"===t.kind?"getAudioTracks":"getVideoTracks";return n[r]().forEach((function(track){n.removeTrack(track)})),n.addTrack(t),e.srcObject=n,e.autoplay=!0,e.playsInline=!0,this._attachments.has(e)||this._attachments.add(e),e},t.prototype._selectElement=function(e){var t=document.querySelector(e);if(!t)throw new Error("Selector matched no element: "+e);return t},t.prototype._updateElementsMediaStreamTrack=function(){var e=this;this._log.debug("Reattaching all elements to update mediaStreamTrack"),this._getAllAttachedElements().forEach((function(t){return e._attach(t)}))},t.prototype._createElement=function(){return"undefined"!=typeof document?document.createElement(this.kind):null},t.prototype.detach=function(e){var t;return t="string"==typeof e?[this._selectElement(e)]:e?[e]:this._getAllAttachedElements(),this._log.debug("Attempting to detach from elements:",t),this._detachElements(t),e?t[0]:t},t.prototype._detachElements=function(e){return e.map(this._detachElement.bind(this))},t.prototype._detachElement=function(e){if(!this._attachments.has(e))return e;var t=e.srcObject;(t instanceof this._MediaStream&&t.removeTrack(this.processedTrack||this.mediaStreamTrack),this._attachments.delete(e),this._shouldShimAttachedElements&&this._elShims.has(e))&&(this._elShims.get(e).unShim(),this._elShims.delete(e));return e},t.prototype._getAllAttachedElements=function(){var e=[];return this._attachments.forEach((function(t){e.push(t)})),e},t}(n(1817));e.exports=y},1871:function(e,t,n){"use strict";var r=n(1410).defer,o=function(){function e(){Object.defineProperties(this,{_audio:{value:r(),writable:!0},_video:{value:r(),writable:!0}}),this._audio.resolve(),this._video.resolve()}return e.prototype.resolveDeferred=function(e){"audio"===e?this._audio.resolve():this._video.resolve()},e.prototype.startDeferred=function(e){"audio"===e?this._audio=r():this._video=r()},e.prototype.whenResolved=function(e){return"audio"===e?this._audio.promise:this._video.promise},e}();e.exports=new o},1872:function(e,t){var n=function(){},r="undefined",o=typeof window!==r&&typeof window.navigator!==r&&/Trident\/|MSIE /.test(window.navigator.userAgent),c=["trace","debug","info","warn","error"];function l(e,t){var n=e[t];if("function"==typeof n.bind)return n.bind(e);try{return Function.prototype.bind.call(n,e)}catch(t){return function(){return Function.prototype.apply.apply(n,[e,arguments])}}}function d(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function f(e){return"debug"===e&&(e="log"),typeof console!==r&&("trace"===e&&o?d:void 0!==console[e]?l(console,e):void 0!==console.log?l(console,"log"):n)}function h(e,t){for(var i=0;i<c.length;i++){var r=c[i];this[r]=i<e?n:this.methodFactory(r,e,t)}this.log=this.debug}function v(e,t,n){return function(){typeof console!==r&&(h.call(this,t,n),this[e].apply(this,arguments))}}function _(e,t,n){return f(e)||v.apply(this,arguments)}function m(e,t,n){var o,l=this,d="loglevel";function f(){var e;if(typeof window!==r&&d){try{e=window.localStorage[d]}catch(e){}if(typeof e===r)try{var t=window.document.cookie,n=t.indexOf(encodeURIComponent(d)+"=");-1!==n&&(e=/^([^;]+)/.exec(t.slice(n))[1])}catch(e){}return void 0===l.levels[e]&&(e=void 0),e}}"string"==typeof e?d+=":"+e:"symbol"==typeof e&&(d=void 0),l.name=e,l.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},l.methodFactory=n||_,l.getLevel=function(){return o},l.setLevel=function(t,n){if("string"==typeof t&&void 0!==l.levels[t.toUpperCase()]&&(t=l.levels[t.toUpperCase()]),!("number"==typeof t&&t>=0&&t<=l.levels.SILENT))throw"log.setLevel() called with invalid level: "+t;if(o=t,!1!==n&&function(e){var t=(c[e]||"silent").toUpperCase();if(typeof window!==r&&d){try{return void(window.localStorage[d]=t)}catch(e){}try{window.document.cookie=encodeURIComponent(d)+"="+t+";"}catch(e){}}}(t),h.call(l,t,e),typeof console===r&&t<l.levels.SILENT)return"No console available for logging"},l.setDefaultLevel=function(e){f()||l.setLevel(e,!1)},l.enableAll=function(e){l.setLevel(l.levels.TRACE,e)},l.disableAll=function(e){l.setLevel(l.levels.SILENT,e)};var v=f();null==v&&(v=null==t?"WARN":t),l.setLevel(v,!1)}var y=new m,w={};y.getLogger=function(e){if("symbol"!=typeof e&&"string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");var t=w[e];return t||(t=w[e]=new m(e,y.getLevel(),y.methodFactory)),t};var k=typeof window!==r?window.log:void 0;y.noConflict=function(){return typeof window!==r&&window.log===y&&(window.log=k),y},y.getLoggers=function(){return w},y.default=y,e.exports=y},1873:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1608).getUserMedia,l=n(1439),d=l.guessBrowser,f=l.isIOSChrome,h=n(1410),v=h.capitalize,_=h.defer,m=h.isUserMediaTrack,y=h.waitForSometime,w=h.waitForEvent,k=n(1425).typeErrors.ILLEGAL_INVOKE,S=n(2090),T=n(1875),O=n(1818),P=n(1871),C=n(1876),E=n(1877);function R(e){var t=e._log,n=e.kind,r={audio:S,video:T}[n],o=e._dummyEl,c=e.mediaStreamTrack,l=null;function d(){var n=e._workaroundWebKitBug1208516Cleanup,c=e.isStopped,d=e.mediaStreamTrack.muted,f=c&&!!n;return Promise.resolve().then((function(){return"visible"===document.visibilityState&&!l&&(d||f||o.play().then((function(){return r(o)})).then((function(e){return e?t.warn("Silence detected"):t.info("Non-silence detected"),e})).catch((function(e){t.warn("Failed to detect silence:",e)})).finally((function(){o.pause()})))}))}function f(){return Promise.race([w(c,"unmute"),y(50)]).then((function(){return d()})).then((function(r){return r&&!l&&(l=_(),e._restart().finally((function(){o=e._dummyEl,m(),c=e.mediaStreamTrack,v(),l.resolve(),l=null})).catch((function(e){t.error("failed to restart track: ",e)}))),(l&&l.promise||Promise.resolve()).finally((function(){return P.resolveDeferred(n)}))})).catch((function(e){t.error("error in maybeRestart: "+e.message)}))}function h(){var t=e._log,n=e.kind;t.info("Muted"),t.debug("LocalMediaTrack:",e),P.startDeferred(n)}function v(){c.addEventListener("ended",f),c.addEventListener("mute",h),c.addEventListener("unmute",f)}function m(){c.removeEventListener("ended",f),c.removeEventListener("mute",h),c.removeEventListener("unmute",f)}var k=function(e){return!!e&&f()};return O.onVisibilityChange(1,k),v(),function(){O.offVisibilityChange(1,k),m()}}e.exports=function(e){return function(e){function t(t,n){var r=this,o=("safari"===d()||f())&&m(t)&&"object"==typeof document&&"function"==typeof document.addEventListener&&"string"==typeof document.visibilityState;n=Object.assign({getUserMedia:c,isCreatedByCreateLocalTracks:!1,workaroundWebKitBug1208516:o,gUMSilentTrackWorkaround:C},n);var l=new E(t),h=l.kind;return r=e.call(this,l,n)||this,Object.defineProperties(r,{_constraints:{value:"object"==typeof n[h]?n[h]:{},writable:!0},_getUserMedia:{value:n.getUserMedia},_gUMSilentTrackWorkaround:{value:n.gUMSilentTrackWorkaround},_workaroundWebKitBug1208516:{value:n.workaroundWebKitBug1208516},_workaroundWebKitBug1208516Cleanup:{value:null,writable:!0},_didCallEnd:{value:!1,writable:!0},_isCreatedByCreateLocalTracks:{value:n.isCreatedByCreateLocalTracks},_trackSender:{value:l},id:{enumerable:!0,value:l.id},isEnabled:{enumerable:!0,get:function(){return l.enabled}},isStopped:{enumerable:!0,get:function(){return"ended"===l.readyState}}}),r._workaroundWebKitBug1208516&&(r._workaroundWebKitBug1208516Cleanup=R(r)),r}return o(t,e),t.prototype._end=function(){this._didCallEnd||(e.prototype._end.call(this),this._didCallEnd=!0,this.emit("stopped",this))},t.prototype._initialize=function(){this._didCallEnd&&(this._didCallEnd=!1),e.prototype._initialize.call(this)},t.prototype._reacquireTrack=function(e){var t,n=this,r=n._getUserMedia,o=n._gUMSilentTrackWorkaround,c=n._log,l=n.mediaStreamTrack.kind;c.info("Re-acquiring the MediaStreamTrack"),c.debug("Constraints:",e);var d=Object.assign({audio:!1,video:!1},((t={})[l]=e,t));return(this._workaroundWebKitBug1208516Cleanup?o(c,r,d):r(d)).then((function(e){return e.getTracks()[0]}))},t.prototype._restart=function(e){var t=this,n=this._log;return e=e||this._constraints,this._stop(),this._reacquireTrack(e).catch((function(t){throw n.error("Failed to re-acquire the MediaStreamTrack:",{error:t,constraints:e}),t})).then((function(r){return n.info("Re-acquired the MediaStreamTrack"),n.debug("MediaStreamTrack:",r),t._constraints=Object.assign({},e),t._setMediaStreamTrack(r)}))},t.prototype._setMediaStreamTrack=function(e){var t=this;return e.enabled=this.mediaStreamTrack.enabled,this._stop(),(this._unprocessedTrack?Promise.resolve().then((function(){t._unprocessedTrack=e})):this._trackSender.setMediaStreamTrack(e).catch((function(n){t._log.warn("setMediaStreamTrack failed:",{error:n,mediaStreamTrack:e})}))).then((function(){t._initialize(),t._getAllAttachedElements().forEach((function(e){return t._attach(e)}))}))},t.prototype._stop=function(){return this.mediaStreamTrack.stop(),this._end(),this},t.prototype.enable=function(e){return(e="boolean"!=typeof e||e)!==this.mediaStreamTrack.enabled&&(this._log.info((e?"En":"Dis")+"abling"),this.mediaStreamTrack.enabled=e,this.emit(e?"enabled":"disabled",this)),this},t.prototype.disable=function(){return this.enable(!1)},t.prototype.restart=function(e){var t=this,n=this.kind;if(!this._isCreatedByCreateLocalTracks)return Promise.reject(k("restart","can only be called on a Local"+v(n)+"Track that is created using createLocalTracks or createLocal"+v(n)+"Track."));this._workaroundWebKitBug1208516Cleanup&&(this._workaroundWebKitBug1208516Cleanup(),this._workaroundWebKitBug1208516Cleanup=null);var r=this._restart(e);return this._workaroundWebKitBug1208516&&(r=r.finally((function(){t._workaroundWebKitBug1208516Cleanup=R(t)}))),r},t.prototype.stop=function(){return this._log.info("Stopping"),this._workaroundWebKitBug1208516Cleanup&&(this._workaroundWebKitBug1208516Cleanup(),this._workaroundWebKitBug1208516Cleanup=null),this._stop()},t}(e)}},1874:function(e,t,n){"use strict";e.exports=function(e,t,n){n="number"==typeof n?n:250;var source=e.createMediaStreamSource(t),r=e.createAnalyser();r.fftSize=2048,source.connect(r);var o=new Uint8Array(r.fftSize),c=!1;return setTimeout((function(){c=!0}),n),function e(){return c?Promise.resolve(!0):(r.getByteTimeDomainData(o),o.some((function(e){return 128!==e&&0!==e}))?Promise.resolve(!1):function(e){return e="number"==typeof e?e:0,new Promise((function(t){return setTimeout(t,e)}))}().then(e))}().then((function(e){return source.disconnect(),e}),(function(e){throw source.disconnect(),e}))}},1875:function(e,t,n){"use strict";var canvas=null;e.exports=function(e){return canvas=canvas||document.createElement("canvas"),new Promise((function(t){var n=3;setTimeout((function r(){return n--,function(e){try{var t=canvas.getContext("2d");t.drawImage(e,0,0,50,50);var n=t.getImageData(0,0,50,50).data.filter((function(e,i){return(i+1)%4}));return 0===Math.max.apply(Math,n)}catch(e){return console.log("Error checking silence: ",e),!1}}(e)?n>0?setTimeout(r,250):t(!0):t(!1)}),250)}))}},1876:function(e,t,n){"use strict";var r=n(1874);e.exports=function(e,t,o,c,l){c="number"==typeof c?c:3;var d=0,f=n(1695),h={},v=f.getOrCreate(h);return function n(){return t(o).then((function(t){return(o.audio?r(v,t,l).catch((function(t){return e.warn("Encountered an error while detecting silence",t),!0})):Promise.resolve(!1)).then((function(r){return r?c<=0?(e.warn("Got a silent audio MediaStreamTrack. Normally we would try to get a new one, but we've run out of retries; returning it anyway."),t):(e.warn("Got a silent audio MediaStreamTrack. Stopping all MediaStreamTracks and calling getUserMedia again. This is retry #"+ ++d+"."),t.getTracks().forEach((function(track){return track.stop()})),c--,n()):(e.info("Got a non-silent audio MediaStreamTrack; returning it."),t)}))}))}().then((function(e){return f.release(h),e}),(function(e){throw f.release(h),e}))}},1877:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=function(e){function t(t){var n=e.call(this,t.id,t)||this;return Object.defineProperties(n,{_clones:{value:new Set},_senders:{value:new Set},_senderToPublisherHintCallbacks:{value:new Map},isPublishing:{get:function(){return!!this._clones.size}}}),n}return o(t,e),t.prototype.clone=function(){var e=new t(this.track.clone());return this._clones.add(e),e},t.prototype.removeClone=function(e){this._clones.delete(e)},t.prototype.setMediaStreamTrack=function(e){var t=this,n=Array.from(this._clones),r=Array.from(this._senders);return Promise.all(n.map((function(t){return t.setMediaStreamTrack(e.clone())})).concat(r.map((function(n){return t._replaceTrack(n,e)})))).finally((function(){t._track=e}))},t.prototype.addSender=function(e,t){return this._senders.add(e),t&&this._senderToPublisherHintCallbacks.set(e,t),this},t.prototype.removeSender=function(e){return this._senders.delete(e),this._senderToPublisherHintCallbacks.delete(e),this},t.prototype.setPublisherHint=function(e){var t=c(Array.from(this._senderToPublisherHintCallbacks.values()),1)[0];return t?t(e):Promise.resolve("COULD_NOT_APPLY_HINT")},t.prototype._replaceTrack=function(e,t){var n=this;return e.replaceTrack(t).then((function(e){return n.setPublisherHint(null).catch((function(){})),n.emit("replaced"),e}))},t}(n(1878));e.exports=l},1878:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){var r=e.call(this,t,n.kind)||this;return Object.defineProperties(r,{_track:{value:n,writable:!0},enabled:{enumerable:!0,get:function(){return this._track.enabled}},readyState:{enumerable:!0,get:function(){return this._track.readyState}},track:{enumerable:!0,get:function(){return this._track}}}),r}return o(t,e),t.prototype.stop=function(){this.track.stop(),e.prototype.stop.call(this)},t}(n(1879));e.exports=c},1879:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){var r=e.call(this)||this;return Object.defineProperties(r,{id:{enumerable:!0,value:t},kind:{enumerable:!0,value:n}}),r}return o(t,e),t.prototype.stop=function(){this.emit("stopped")},t}(n(373).EventEmitter);e.exports=c},1880:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1870),l=n(2093),d=n(1425).DEFAULT_FRAME_RATE,f=function(e){function t(t,n){var r=e.call(this,t,n)||this;return Object.defineProperties(r,{_captureTimeoutId:{value:null,writable:!0},_isCapturing:{value:!1,writable:!0},_inputFrame:{value:null,writable:!0},_outputFrame:{value:null,writable:!0},_processorEventObserver:{value:null,writable:!0},_unmuteHandler:{value:null,writable:!0},dimensions:{enumerable:!0,value:{width:null,height:null}},processor:{enumerable:!0,value:null,writable:!0}}),r._processorEventObserver=new(n.VideoProcessorEventObserver||l)(r._log),r}return o(t,e),t.prototype._checkIfCanCaptureFrames=function(e){void 0===e&&(e=!1);var t=!0,n="",r=this.mediaStreamTrack;return r.enabled||(t=!1,n="MediaStreamTrack is disabled"),"ended"===r.readyState&&(t=!1,n="MediaStreamTrack is ended"),this.processor||(t=!1,n="VideoProcessor not detected."),this._attachments.size||e||(t=!1,n="VideoTrack is not publishing and there is no attached element."),n&&this._log.debug(n),{canCaptureFrames:t,message:n}},t.prototype._captureFrames=function(){var e=this;if(this._isCapturing)this._log.debug("Ignoring captureFrames call. Capture is already in progress");else{if(!this._checkIfCanCaptureFrames().canCaptureFrames)return this._isCapturing=!1,void this._log.debug("Cannot capture frames. Ignoring captureFrames call.");this._isCapturing=!0,this._processorEventObserver.emit("start"),this._log.debug("Start capturing frames");var t,n=Date.now();this._dummyEl.play().then((function(){var r=function(n){clearTimeout(e._captureTimeoutId);var r=e.mediaStreamTrack.getSettings().frameRate,o=void 0===r?d:r,c=Math.floor(1e3/o)-t;(c<0||"number"!=typeof t)&&(c=0),e._captureTimeoutId=setTimeout(n,c)},o=function(){var c=e._checkIfCanCaptureFrames();if(!c.canCaptureFrames)return e._isCapturing=!1,e._processorEventObserver.emit("stop",c.message),void e._log.debug("Cannot capture frames. Stopping capturing frames.");n=Date.now();var l=e.mediaStreamTrack.getSettings(),d=l.width,f=void 0===d?0:d,h=l.height,v=void 0===h?0:h;e._inputFrame.width!==f&&(e._inputFrame.width=f,e._inputFrame.height=v,e._outputFrame&&(e._outputFrame.width=f,e._outputFrame.height=v)),e._inputFrame.getContext("2d").drawImage(e._dummyEl,0,0,f,v);var _=null;try{_=e.processor.processFrame(e._inputFrame,e._outputFrame)}catch(t){e._log.debug("Exception detected after calling processFrame.",t)}(_ instanceof Promise?_:Promise.resolve(_)).then((function(){e._outputFrame&&(e.processedTrack.requestFrame(),e._processorEventObserver.emit("stats"))})).finally((function(){t=Date.now()-n,r(o)}))};r(o)})).catch((function(t){return e._log.error("Video element cannot be played",{error:t,track:e})}))}},t.prototype._initialize=function(){var n=this;e.prototype._initialize.call(this),this._dummyEl&&(this._dummyEl.onloadedmetadata=function(){h(n,n._dummyEl)&&(n.dimensions.width=n._dummyEl.videoWidth,n.dimensions.height=n._dummyEl.videoHeight)},this._dummyEl.onresize=function(){h(n,n._dummyEl)&&(n.dimensions.width=n._dummyEl.videoWidth,n.dimensions.height=n._dummyEl.videoHeight,n.isStarted&&(n._log.debug("Dimensions changed:",n.dimensions),n.emit(t.DIMENSIONS_CHANGED,n)))})},t.prototype._restartProcessor=function(){var e=this.processor;e&&(this.removeProcessor(e),this.addProcessor(e))},t.prototype._start=function(t){return this.dimensions.width=t.videoWidth,this.dimensions.height=t.videoHeight,this._log.debug("Dimensions:",this.dimensions),e.prototype._start.call(this,t)},t.prototype.addProcessor=function(e){var t=this;if("function"!=typeof OffscreenCanvas)return this._log.warn("Adding a VideoProcessor is not supported in this browser.");if(!e||"function"!=typeof e.processFrame)throw new Error("Received an invalid VideoProcessor from addProcessor.");if(this.processor)throw new Error("A VideoProcessor has already been added.");if(!this._dummyEl)throw new Error("VideoTrack has not been initialized.");this._log.debug("Adding VideoProcessor to the VideoTrack",e),this._unmuteHandler||(this._unmuteHandler=function(){t._log.debug("mediaStreamTrack unmuted"),t.processedTrack.muted&&(t._log.debug("mediaStreamTrack is unmuted but processedTrack is muted. Restarting processor."),t._restartProcessor())},this.mediaStreamTrack.addEventListener("unmute",this._unmuteHandler));var n=this.mediaStreamTrack.getSettings(),r=n.width,o=void 0===r?0:r,c=n.height,l=void 0===c?0:c,f=n.frameRate,h=void 0===f?d:f;return this._inputFrame=new OffscreenCanvas(o,l),this._outputFrame=document.createElement("canvas"),this._outputFrame.width=o,this._outputFrame.height=l,this.processedTrack=this._outputFrame.captureStream(0).getTracks()[0],this.processedTrack.enabled=this.mediaStreamTrack.enabled,this.processor=e,this._processorEventObserver.emit("add",{processor:e,captureHeight:l,captureWidth:o,inputFrameRate:h,isRemoteVideoTrack:this.toString().includes("RemoteVideoTrack")}),this._updateElementsMediaStreamTrack(),this._captureFrames(),this},t.prototype.attach=function(){var t=e.prototype.attach.apply(this,arguments);return this.processor&&this._captureFrames(),t},t.prototype.detach=function(){return e.prototype.detach.apply(this,arguments)},t.prototype.removeProcessor=function(e){if(!e)throw new Error("Received an invalid VideoProcessor from removeProcessor.");if(!this.processor)throw new Error("No existing VideoProcessor detected.");if(e!==this.processor)throw new Error("The provided VideoProcessor is different than the existing one.");return this._processorEventObserver.emit("remove"),this._log.debug("Removing VideoProcessor from the VideoTrack",e),clearTimeout(this._captureTimeoutId),this.mediaStreamTrack.removeEventListener("unmute",this._unmuteHandler),this._unmuteHandler=null,this._isCapturing=!1,this.processor=null,this.processedTrack=null,this._inputFrame.getContext("2d").clearRect(0,0,this._inputFrame.width,this._inputFrame.height),this._outputFrame.getContext("2d").clearRect(0,0,this._outputFrame.width,this._outputFrame.height),this._inputFrame=null,this._outputFrame=null,this._updateElementsMediaStreamTrack(),this},t}(c);function h(track,e){return track.dimensions.width!==e.videoWidth||track.dimensions.height!==e.videoHeight}f.DIMENSIONS_CHANGED="dimensionsChanged",e.exports=f},1881:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n,r,o){var c=e.call(this,t,"data")||this;return Object.defineProperties(c,{maxPacketLifeTime:{enumerable:!0,value:n},maxRetransmits:{enumerable:!0,value:r},ordered:{enumerable:!0,value:o}}),c}return o(t,e),t}(n(1879));e.exports=c},1882:function(e,t,n){"use strict";(function(t){var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},d=n(1616),f=n(1410),h=f.buildLogLevels,v=f.makeUUID,_=n(1570),m=n(2102),y=n(1609),w=0,k={closed:[],connecting:["closed","open","waiting"],early:["closed","connecting"],open:["closed"],waiting:["closed","connecting","early","open"]},S={closed:"close",open:"open",waiting:"waiting"},T=3e3,O=3001,P=3002,C=3003,E=3005,R=3006,j=t.window||t,x=j.WebSocket?j.WebSocket:n(1883),L={BUSY:"busy",FAILED:"failed",LOCAL:"local",REMOTE:"remote",TIMEOUT:"timeout"},A=new Map([[T,L.TIMEOUT],[O,L.TIMEOUT],[P,L.FAILED],[C,L.FAILED],[3004,L.TIMEOUT],[R,L.BUSY],[3007,L.TIMEOUT]]),D=function(e){function t(t,n){var r=e.call(this,"early",k)||this;n=Object.assign({helloBody:null,maxConsecutiveFailedHellos:3,maxConsecutiveMissedHeartbeats:3,requestedHeartbeatTimeout:5e3,openTimeout:15e3,welcomeTimeout:5e3,Log:_,WebSocket:x},n);var o=h(n.logLevel),d=new n.Log("default",r,o,n.loggerName),f=n.networkMonitor?new m((function(){var e=f.type,t="Network changed"+(e?" to "+e:"");d.debug(t),r._close({code:3004,reason:t})})):null;Object.defineProperties(r,{_busyWaitTimeout:{value:null,writable:!0},_consecutiveHeartbeatsMissed:{value:0,writable:!0},_cookie:{value:null,writable:!0},_eventObserver:{value:n.eventObserver},_heartbeatTimeout:{value:null,writable:!0},_hellosLeft:{value:n.maxConsecutiveFailedHellos,writable:!0},_instanceId:{value:++w},_log:{value:d},_messageQueue:{value:[]},_networkMonitor:{value:f},_options:{value:n},_openTimeout:{value:null,writable:!0},_sendHeartbeatTimeout:{value:null,writable:!0},_serverUrl:{value:t},_welcomeTimeout:{value:null,writable:!0},_ws:{value:null,writable:!0}});var v={connecting:"info",early:"info",open:"info",waiting:"warning",closed:"info"};return r.on("stateChanged",(function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];e in S&&r.emit.apply(r,l([S[e]],c(t)));var o={name:e,group:"signaling",level:v[r.state]};if("closed"===e){var d=c(t,1),f=d[0];o.payload={reason:f},o.level=f===L.LOCAL?"info":"error"}r._eventObserver.emit("event",o)})),r._eventObserver.emit("event",{name:r.state,group:"signaling",level:v[r.state]}),r._connect(),r}return o(t,e),t.prototype.toString=function(){return"[TwilioConnection #"+this._instanceId+": "+this._ws.url+"]"},t.prototype._close=function(e){var code=e.code,t=e.reason;if("closed"!==this.state){this._openTimeout&&this._openTimeout.clear(),this._welcomeTimeout&&this._welcomeTimeout.clear(),this._heartbeatTimeout&&this._heartbeatTimeout.clear(),this._sendHeartbeatTimeout&&this._sendHeartbeatTimeout.clear(),this._networkMonitor&&this._networkMonitor.stop(),this._busyWaitTimeout&&code!==E&&this._busyWaitTimeout.clear(),this._messageQueue.splice(0);var n=this._log;1e3===code?(n.debug("Closed"),this.transition("closed",null,[L.LOCAL])):(n.warn("Closed: "+code+" - "+t),code!==E&&this.transition("closed",null,[A.get(code)||L.REMOTE]));var r=this._ws.readyState,o=this._options.WebSocket;r!==o.CLOSING&&r!==o.CLOSED&&this._ws.close(code,t)}},t.prototype._connect=function(){var e=this,t=this._log;if("waiting"===this.state)this.transition("early");else if("early"!==this.state)return void t.warn('Unexpected state "'+this.state+'" for connecting to the TCMP server.');this._ws=new this._options.WebSocket(this._serverUrl);var n=this._ws;t.debug("Created a new WebSocket:",n),n.addEventListener("close",(function(t){return e._close(t)}));var r=this._options.openTimeout;this._openTimeout=new y((function(){var t="Failed to open in "+r+" ms";e._close({code:3007,reason:t})}),r),n.addEventListener("open",(function(){t.debug("WebSocket opened:",n),e._openTimeout.clear(),e._startHandshake(),e._networkMonitor&&e._networkMonitor.start()})),n.addEventListener("message",(function(n){t.debug("Incoming: "+n.data);try{n=JSON.parse(n.data)}catch(t){return void e.emit("error",t)}switch(n.type){case"bad":e._handleBad(n);break;case"busy":e._handleBusy(n);break;case"bye":break;case"msg":e._handleMessage(n);case"heartbeat":e._handleHeartbeat();break;case"welcome":e._handleWelcome(n);break;default:e._log.debug("Unknown message type: "+n.type),e.emit("error",new Error("Unknown message type: "+n.type))}}))},t.prototype._handleBad=function(e){var t=e.reason,n=this._log;if(["connecting","open"].includes(this.state)){if("connecting"===this.state)return n.warn("Closing: 3002 - "+t),void this._close({code:P,reason:t});n.debug("Error: "+t),this.emit("error",new Error(t))}else n.warn('Unexpected state "'+this.state+'" for handling a "bad" message from the TCMP server.')},t.prototype._handleBusy=function(e){var t=this,n=e.cookie,r=e.keepAlive,o=e.retryAfter,c=this._log;if(["connecting","waiting"].includes(this.state)){this._busyWaitTimeout&&this._busyWaitTimeout.clear(),this._welcomeTimeout&&this._welcomeTimeout.clear();var l=o<0?'Received terminal "busy" message':'Received "busy" message, retrying after '+o+" ms";if(o<0)return c.warn("Closing: 3006 - "+l),void this._close({code:R,reason:l});var d=this._options.maxConsecutiveFailedHellos;this._hellosLeft=d,this._cookie=n||null,r?(c.warn(l),this._busyWaitTimeout=new y((function(){return t._startHandshake()}),o)):(c.warn("Closing: 3005 - "+l),this._close({code:E,reason:l}),this._busyWaitTimeout=new y((function(){return t._connect()}),o)),this.transition("waiting",null,[r,o])}else c.warn('Unexpected state "'+this.state+'" for handling a "busy" message from the TCMP server.')},t.prototype._handleHeartbeat=function(){"open"===this.state?this._heartbeatTimeout.reset():this._log.warn('Unexpected state "'+this.state+'" for handling a "heartbeat" message from the TCMP server.')},t.prototype._handleHeartbeatTimeout=function(){if("open"===this.state){var e=this._log,t=this._options.maxConsecutiveMissedHeartbeats;e.debug("Consecutive heartbeats missed: "+t);var n="Missed "+t+' "heartbeat" messages';e.warn("Closing: 3001 - "+n),this._close({code:O,reason:n})}},t.prototype._handleMessage=function(e){var body=e.body;"open"===this.state?this.emit("message",body):this._log.warn('Unexpected state "'+this.state+'" for handling a "msg" message from the TCMP server.')},t.prototype._handleWelcome=function(e){var t=this,n=e.negotiatedTimeout,r=this._log;if(["connecting","waiting"].includes(this.state)){"waiting"===this.state&&(r.debug('Received "welcome" message, no need to retry connection.'),this._busyWaitTimeout.clear());var o=n*this._options.maxConsecutiveMissedHeartbeats,c=n-200;this._welcomeTimeout.clear(),this._heartbeatTimeout=new y((function(){return t._handleHeartbeatTimeout()}),o),this._messageQueue.splice(0).forEach((function(e){return t._send(e)})),this._sendHeartbeatTimeout=new y((function(){return t._sendHeartbeat()}),c),this.transition("open")}else r.warn('Unexpected state "'+this.state+'" for handling a "welcome" message from the TCMP server.')},t.prototype._handleWelcomeTimeout=function(){if("connecting"===this.state){var e=this._log;if(this._hellosLeft<=0){var t="All handshake attempts failed";return e.warn("Closing: 3000 - "+t),void this._close({code:T,reason:t})}var n=this._options.maxConsecutiveFailedHellos;e.warn("Handshake attempt "+(n-this._hellosLeft)+" failed"),this._startHandshake()}},t.prototype._send=function(e){if(this._ws.readyState===this._options.WebSocket.OPEN){var data=JSON.stringify(e);this._log.debug("Outgoing: "+data);try{this._ws.send(data),this._sendHeartbeatTimeout&&this._sendHeartbeatTimeout.reset()}catch(e){var t="Failed to send message";this._log.warn("Closing: 3003 - "+t),this._close({code:C,reason:t})}}},t.prototype._sendHeartbeat=function(){"closed"!==this.state&&this._send({type:"heartbeat"})},t.prototype._sendHello=function(){var e=this._options,t=e.helloBody,n=e.requestedHeartbeatTimeout,r={id:v(),timeout:n,type:"hello",version:2};this._cookie&&(r.cookie=this._cookie),t&&(r.body=t),this._send(r)},t.prototype._sendOrEnqueue=function(e){var t=this;"closed"!==this.state&&("open"===this.state?function(e){return t._send(e)}:function(e){return t._messageQueue.push(e)})(e)},t.prototype._startHandshake=function(){var e=this;if(["early","waiting"].includes(this.state)&&this.transition("connecting"),"connecting"===this.state){this._hellosLeft--,this._sendHello();var t=this._options.welcomeTimeout;this._welcomeTimeout=new y((function(){return e._handleWelcomeTimeout()}),t)}},t.prototype.close=function(){"closed"!==this.state&&(this._sendOrEnqueue({type:"bye"}),this._close({code:1e3,reason:"Normal"}))},t.prototype.sendMessage=function(body){this._sendOrEnqueue({body:body,type:"msg"})},t}(d);D.CloseReason=L,e.exports=D}).call(this,n(56))},1883:function(e,t){e.exports=WebSocket},1884:function(e,t,n){"use strict";var r=function(){function e(){Object.defineProperties(this,{_samples:{value:[{denominator:0,numerator:0},{denominator:0,numerator:0}]}})}return e.prototype.get=function(){var e=this._samples,t=e[1].denominator-e[0].denominator||1/0;return(e[1].numerator-e[0].numerator)/t},e.prototype.putSample=function(e,t){var n=this._samples;n.shift(),n.push({denominator:t,numerator:e})},e}();e.exports=r},1885:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(373).EventEmitter,l=["signaling","room","media","quality","video-processor","preflight"],d=["debug","error","info","warning"],f=function(e){function t(t,n,r,o){void 0===o&&(o=null);var c=e.call(this)||this;return c.on("event",(function(e){var c=e.name,f=e.group,h=e.level,v=e.payload;if("string"!=typeof c)throw r.error("Unexpected name: ",c),new Error("Unexpected name: ",c);if(!l.includes(f))throw r.error("Unexpected group: ",f),new Error("Unexpected group: ",f);if(!d.includes(h))throw r.error("Unexpected level: ",h),new Error("Unexpected level: ",h);var _=Date.now(),m=_-n,y=Object.assign({elapsedTime:m,level:h},v||{});t.publish(f,c,y);var w=Object.assign({elapsedTime:m,group:f,level:h,name:c,timestamp:_},v?{payload:v}:{});r[{debug:"debug",error:"error",info:"info",warning:"warn"}[h]]("event",w),o&&"signaling"===f&&o.emit("event",w)})),c}return o(t,e),t}(c);e.exports=f},1886:function(e,t,n){"use strict";(function(t){var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(373).EventEmitter,l=n(1410).getUserAgent,d=t.window||t,f=d.WebSocket?d.WebSocket:n(1883),h=n(1410),v=function(e){function t(t,n,r,o,c,d){var v=e.call(this)||this;return d=Object.assign({gateway:m(o,c)+"/v1/VideoEvents",maxReconnectAttempts:5,reconnectIntervalMs:50,userAgent:l(),WebSocket:f},d),Object.defineProperties(v,{_connectTimestamp:{value:0,writable:!0},_eventQueue:{value:[]},_readyToConnect:{value:h.defer()},_reconnectAttemptsLeft:{value:d.maxReconnectAttempts,writable:!0},_ws:{value:null,writable:!0},_WebSocket:{value:d.WebSocket}}),v._readyToConnect.promise.then((function(e){var o=e.roomSid,c=e.participantSid,l=v;v.on("disconnected",(function e(f){if(l._session=null,f&&l._reconnectAttemptsLeft>0)return l.emit("reconnecting"),void function(e,t,n,r,o,c,l){var d=Date.now()-e._connectTimestamp,f=l.reconnectIntervalMs-d;if(f>0)return void setTimeout((function(){_(e,t,n,r,o,c,l)}),f);_(e,t,n,r,o,c,l)}(l,t,n,r,o,c,d);l.removeListener("disconnected",e)})),_(v,t,n,r,o,c,d)})).catch((function(){})),v}return o(t,e),t.prototype.connect=function(e,t){this._readyToConnect.resolve({roomSid:e,participantSid:t})},t.prototype._publish=function(e){e.session=this._session,this._ws.send(JSON.stringify(e))},t.prototype.disconnect=function(){if(null===this._ws||this._ws.readyState===this._WebSocket.CLOSING||this._ws.readyState===this._WebSocket.CLOSED)return!1;try{this._ws.close()}catch(e){}return this.emit("disconnected"),!0},t.prototype.publish=function(e,t,n){return(null===this._ws||this._ws.readyState!==this._WebSocket.CLOSING&&this._ws.readyState!==this._WebSocket.CLOSED)&&(("string"==typeof this._session?this._publish.bind(this):this._eventQueue.push.bind(this._eventQueue))({group:e,name:t,payload:n,timestamp:Date.now(),type:"event",version:1}),!0)},t}(c);function _(e,t,n,r,o,c,l){e._connectTimestamp=Date.now(),e._reconnectAttemptsLeft--,e._ws=new l.WebSocket(l.gateway);var d=e._ws;d.addEventListener("close",(function(t){1e3!==t.code?e.emit("disconnected",new Error("WebSocket Error "+t.code+": "+t.reason)):e.emit("disconnected")})),d.addEventListener("message",(function(t){!function(e,t,n){switch(t.type){case"connected":e._session=t.session,e._reconnectAttemptsLeft=n.maxReconnectAttempts,e._eventQueue.splice(0).forEach(e._publish,e),e.emit("connected");break;case"error":e._ws.close(),e.emit("disconnected",new Error(t.message))}}(e,JSON.parse(t.data),l)})),d.addEventListener("open",(function(){var e={type:"connect",token:t,version:1};e.publisher={name:n,sdkVersion:r,userAgent:l.userAgent,participantSid:c,roomSid:o},d.send(JSON.stringify(e))}))}function m(e,t){return"prod"===e?"wss://sdkgw."+t+".twilio.com":"wss://sdkgw."+e+"-"+t+".twilio.com"}e.exports=v}).call(this,n(56))},1887:function(e,t,n){"use strict";var r=n(1410).isNonArrayObject,o=n(1425),c=o.typeErrors,l=o.clientTrackSwitchOffControl,d=o.videoContentPreferencesMode,f=o.subscriptionMode,h=o.trackPriority,v=o.trackSwitchOffMode;function _(object,e,t){return void 0===t&&(t=[]),void 0===object?null:null!==object&&r(object)?t.reduce((function(t,n){var r=n.prop,o=n.type,l=n.values;if(t||!(r in object))return t;var d=object[r];return o&&typeof d!==o||"number"===o&&isNaN(d)?c.INVALID_TYPE(e+"."+r,o):Array.isArray(l)&&!l.includes(d)?c.INVALID_VALUE(e+"."+r,l):t}),null):c.INVALID_TYPE(e,"object")}t.validateBandwidthProfile=function(e){var t=_(e,"options.bandwidthProfile");return!e||t?t:(t=_(e.video,"options.bandwidthProfile.video",[{prop:"contentPreferencesMode",values:Object.values(d)},{prop:"dominantSpeakerPriority",values:Object.values(h)},{prop:"maxSubscriptionBitrate",type:"number"},{prop:"maxTracks",type:"number"},{prop:"mode",values:Object.values(f)},{prop:"clientTrackSwitchOffControl",values:Object.values(l)},{prop:"trackSwitchOffMode",values:Object.values(v)}]))||(e.video?"maxTracks"in e.video&&"clientTrackSwitchOffControl"in e.video?new TypeError("options.bandwidthProfile.video.maxTracks is deprecated. Use options.bandwidthProfile.video.clientTrackSwitchOffControl instead."):"renderDimensions"in e.video&&"contentPreferencesMode"in e.video?new TypeError("options.bandwidthProfile.video.renderDimensions is deprecated. Use options.bandwidthProfile.video.contentPreferencesMode instead."):function(e){var t="options.bandwidthProfile.video.renderDimensions",n=_(e,t);return e?n||Object.values(h).reduce((function(n,r){return n||_(e[r],t+"."+r,[{prop:"height",type:"number"},{prop:"width",type:"number"}])}),null):n}(e.video.renderDimensions):null)},t.validateLocalTrack=function(track,e){if(!(track instanceof e.LocalAudioTrack||track instanceof e.LocalDataTrack||track instanceof e.LocalVideoTrack||track instanceof e.MediaStreamTrack))throw c.INVALID_TYPE("track","LocalAudioTrack, LocalVideoTrack, LocalDataTrack, or MediaStreamTrack")},t.validateObject=_},1888:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1684),l=n(1410),d=l.buildLogLevels,f=l.valueToJSON,h=n(1425).DEFAULT_LOG_LEVEL,v=n(1570),_=0,m=function(e){function t(t,n,r){var o=e.call(this)||this;r=Object.assign({logLevel:h},r);var c=d(r.logLevel);return Object.defineProperties(o,{_instanceId:{value:_++},_log:{value:r.log?r.log.createLog("default",o):new v("default",o,c,r.loggerName)},trackName:{enumerable:!0,value:t},trackSid:{enumerable:!0,value:n}}),o}return o(t,e),t.prototype.toJSON=function(){return f(this)},t.prototype.toString=function(){return"[TrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(c);e.exports=m},1889:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},d=n(1684),f=n(2114),h=n(2115),v=n(2116),_=n(2117),m=n(2118),y=n(2120),w=n(1410),k=0,S=function(e){function t(t,n){var r=e.call(this)||this,o=function(e){var t=e.map((function(track){return[track.id,track]})),n=t.filter((function(e){return"audio"===e[1].kind})),r=t.filter((function(e){return"video"===e[1].kind})),o=t.filter((function(e){return"data"===e[1].kind}));return{audioTracks:n,dataTracks:o,tracks:t,videoTracks:r}}((n=Object.assign({RemoteAudioTrack:f,RemoteAudioTrackPublication:h,RemoteDataTrack:v,RemoteDataTrackPublication:_,RemoteVideoTrack:m,RemoteVideoTrackPublication:y,tracks:[]},n)).tracks),c=n.log.createLog("default",r),l=new Map(o.audioTracks),d=new Map(o.dataTracks),w=new Map(o.tracks),S=new Map(o.videoTracks);return Object.defineProperties(r,{_RemoteAudioTrack:{value:n.RemoteAudioTrack},_RemoteAudioTrackPublication:{value:n.RemoteAudioTrackPublication},_RemoteDataTrack:{value:n.RemoteDataTrack},_RemoteDataTrackPublication:{value:n.RemoteDataTrackPublication},_RemoteVideoTrack:{value:n.RemoteVideoTrack},_RemoteVideoTrackPublication:{value:n.RemoteVideoTrackPublication},_audioTracks:{value:l},_dataTracks:{value:d},_instanceId:{value:++k},_clientTrackSwitchOffControl:{value:n.clientTrackSwitchOffControl},_contentPreferencesMode:{value:n.contentPreferencesMode},_log:{value:c},_signaling:{value:t},_tracks:{value:w},_trackEventReemitters:{value:new Map},_trackPublicationEventReemitters:{value:new Map},_trackSignalingUpdatedEventCallbacks:{value:new Map},_videoTracks:{value:S},audioTracks:{enumerable:!0,value:new Map},dataTracks:{enumerable:!0,value:new Map},identity:{enumerable:!0,get:function(){return t.identity}},networkQualityLevel:{enumerable:!0,get:function(){return t.networkQualityLevel}},networkQualityStats:{enumerable:!0,get:function(){return t.networkQualityStats}},sid:{enumerable:!0,get:function(){return t.sid}},state:{enumerable:!0,get:function(){return t.state}},tracks:{enumerable:!0,value:new Map},videoTracks:{enumerable:!0,value:new Map}}),r._tracks.forEach(T.bind(null,r)),t.on("networkQualityLevelChanged",(function(){return r.emit("networkQualityLevelChanged",r.networkQualityLevel,r.networkQualityStats&&(r.networkQualityStats.audio||r.networkQualityStats.video)?r.networkQualityStats:null)})),function(e,t){var n=e._log;if("disconnected"===e.state)return;t.on("stateChanged",(function r(o){n.debug("Transitioned to state:",o),e.emit(o,e),"disconnected"===o&&(n.debug("Removing Track event reemitters"),t.removeListener("stateChanged",r),e._tracks.forEach((function(track){var t=e._trackEventReemitters.get(track.id);track&&t&&t.forEach((function(e,t){track.removeListener(t,e)}))})),t.tracks.forEach((function(t){var track=e._tracks.get(t.id),n=e._trackEventReemitters.get(t.id);track&&n&&n.forEach((function(e,t){track.removeListener(t,e)}))})),e._trackEventReemitters.clear(),e.tracks.forEach((function(t){e._trackPublicationEventReemitters.get(t.trackSid).forEach((function(e,n){t.removeListener(n,e)}))})),e._trackPublicationEventReemitters.clear())}))}(r,t),c.info("Created a new Participant"+(r.identity?": "+r.identity:"")),r}return o(t,e),t.prototype._getTrackEvents=function(){return[["dimensionsChanged","trackDimensionsChanged"],["message","trackMessage"],["started","trackStarted"]]},t.prototype._getTrackPublicationEvents=function(){return[]},t.prototype.toString=function(){return"[Participant #"+this._instanceId+": "+this.sid+"]"},t.prototype._addTrack=function(track,e){var t=this._log;return this._tracks.has(e)?null:(this._tracks.set(e,track),{audio:this._audioTracks,video:this._videoTracks,data:this._dataTracks}[track.kind].set(e,track),T(this,track,e),t.info("Added a new "+w.trackClass(track)+":",e),t.debug(w.trackClass(track)+":",track),track)},t.prototype._addTrackPublication=function(e){var t=this._log;return this.tracks.has(e.trackSid)?null:(this.tracks.set(e.trackSid,e),{audio:this.audioTracks,data:this.dataTracks,video:this.videoTracks}[e.kind].set(e.trackSid,e),function(e,t){var n=new Map;if("disconnected"===e.state)return;e._getTrackPublicationEvents().forEach((function(r){var o=c(r,2),d=o[0],f=o[1];n.set(d,(function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];e.emit.apply(e,l(l([f],c(n)),[t]))})),t.on(d,n.get(d))})),e._trackPublicationEventReemitters.set(t.trackSid,n)}(this,e),t.info("Added a new "+w.trackPublicationClass(e)+":",e.trackSid),t.debug(w.trackPublicationClass(e)+":",e),e)},t.prototype._handleTrackSignalingEvents=function(){var e=this,t=e._log,n=e._clientTrackSwitchOffControl,r=e._contentPreferencesMode,o=this;if("disconnected"!==this.state){var l=this._RemoteAudioTrack,d=this._RemoteAudioTrackPublication,f=this._RemoteVideoTrack,h=this._RemoteVideoTrackPublication,v=this._RemoteDataTrack,_=this._RemoteDataTrackPublication,m=this._signaling;m.on("trackAdded",y),m.on("trackRemoved",w),m.tracks.forEach(y),m.on("stateChanged",(function e(n){"disconnected"===n?(t.debug("Removing event listeners"),m.removeListener("stateChanged",e),m.removeListener("trackAdded",y),m.removeListener("trackRemoved",w)):"connected"===n&&(t.info("reconnected"),setTimeout((function(){return o.emit("reconnected")}),0))}))}function y(e){var n=new(0,{audio:d,data:_,video:h}[e.kind])(e,{log:t});o._addTrackPublication(n);var r=e.isSubscribed;r&&k(e),o._trackSignalingUpdatedEventCallbacks.set(e.sid,(function(){if(r!==e.isSubscribed){if(r=e.isSubscribed)return void k(e);!function(e){var t=c(Array.from(o._tracks.entries()).find((function(t){return c(t,2)[1].sid===e.sid})),2),n=t[0],track=t[1],r=o.tracks.get(e.sid);track&&o._removeTrack(track,r,n)}(e)}})),e.on("updated",o._trackSignalingUpdatedEventCallbacks.get(e.sid))}function w(e){e.isSubscribed&&e.setTrackTransceiver(null);var t=o._trackSignalingUpdatedEventCallbacks.get(e.sid);t&&(e.removeListener("updated",t),o._trackSignalingUpdatedEventCallbacks.delete(e.sid));var n=o.tracks.get(e.sid);n&&o._removeTrackPublication(n)}function k(e){var c=e.isEnabled,d=e.name,h=e.kind,_=e.sid,y=e.trackTransceiver,w=e.isSwitchedOff,k={audio:l,video:f,data:v}[h],S=o.tracks.get(_);if(k&&h===y.kind){var T={log:t,name:d,clientTrackSwitchOffControl:n,contentPreferencesMode:r},track="data"===h?new k(_,y,T):new k(_,y,c,w,(function(e){return m.updateSubscriberTrackPriority(_,e)}),(function(t){e.isSubscribed&&m.updateTrackRenderHint(_,t)}),T);o._addTrack(track,S,y.id)}}},t.prototype._removeTrack=function(track,e){if(!this._tracks.has(e))return null;this._tracks.delete(e),{audio:this._audioTracks,video:this._videoTracks,data:this._dataTracks}[track.kind].delete(e),(this._trackEventReemitters.get(e)||new Map).forEach((function(e,t){track.removeListener(t,e)}));var t=this._log;return t.info("Removed a "+w.trackClass(track)+":",e),t.debug(w.trackClass(track)+":",track),track},t.prototype._removeTrackPublication=function(e){if(!(e=this.tracks.get(e.trackSid)))return null;this.tracks.delete(e.trackSid),{audio:this.audioTracks,data:this.dataTracks,video:this.videoTracks}[e.kind].delete(e.trackSid),(this._trackPublicationEventReemitters.get(e.trackSid)||new Map).forEach((function(t,n){e.removeListener(n,t)}));var t=this._log;return t.info("Removed a "+w.trackPublicationClass(e)+":",e.trackSid),t.debug(w.trackPublicationClass(e)+":",e),e},t.prototype.toJSON=function(){return w.valueToJSON(this)},t}(d);function T(e,track,t){var n=new Map;"disconnected"!==e.state&&(e._getTrackEvents().forEach((function(t){var r=t[0],o=t[1];n.set(r,(function(){var t=[o].concat([].slice.call(arguments));return e.emit.apply(e,l([],c(t)))})),track.on(r,n.get(r))})),e._trackEventReemitters.set(t,n))}e.exports=S},1890:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},d=n(1425),f=d.typeErrors,h=d.trackPriority,v=n(1439),_=v.guessBrowser,m=v.isIOSChrome,y=n(1818);e.exports=function(e){return function(e){function t(t,n,r,o,c,l,d){var f=this;return d=Object.assign({workaroundWebKitBug212780:("safari"===_()||m())&&"object"==typeof document&&"function"==typeof document.addEventListener&&"string"==typeof document.visibilityState},d),f=e.call(this,n,d)||this,Object.defineProperties(f,{_isEnabled:{value:r,writable:!0},_isSwitchedOff:{value:o,writable:!0},_priority:{value:null,writable:!0},_setPriority:{value:c},_setRenderHint:{value:function(e){f._log.debug("updating render hint:",e),l(e)}},isEnabled:{enumerable:!0,get:function(){return this._isEnabled}},isSwitchedOff:{enumerable:!0,get:function(){return this._isSwitchedOff}},priority:{enumerable:!0,get:function(){return this._priority}},sid:{enumerable:!0,value:t},_workaroundWebKitBug212780:{value:d.workaroundWebKitBug212780},_workaroundWebKitBug212780Cleanup:{value:null,writable:!0}}),f}return o(t,e),t.prototype.setPriority=function(e){var t=l([null],c(Object.values(h)));if(!t.includes(e))throw f.INVALID_VALUE("priority",t);return this._priority!==e&&(this._priority=e,this._setPriority(e)),this},t.prototype._setEnabled=function(e){this._isEnabled!==e&&(this._isEnabled=e,this.emit(this._isEnabled?"enabled":"disabled",this))},t.prototype._setSwitchedOff=function(e){this._isSwitchedOff!==e&&(this._isSwitchedOff=e,this.emit(e?"switchedOff":"switchedOn",this))},t.prototype.attach=function(t){var n=e.prototype.attach.call(this,t);return!0!==this.mediaStreamTrack.enabled&&(this.mediaStreamTrack.enabled=!0,this.processedTrack&&(this.processedTrack.enabled=!0),this.processor&&this._captureFrames()),this._workaroundWebKitBug212780&&(this._workaroundWebKitBug212780Cleanup=this._workaroundWebKitBug212780Cleanup||function(e){var t=e._log,n=e.kind;function r(r){r&&e._attachments.forEach((function(r){var o=e._elShims.get(r);r.paused&&o&&!o.pausedIntentionally()&&(t.info("Playing inadvertently paused <"+n+"> element"),t.debug("Element:",r),t.debug("RemoteMediaTrack:",e),r.play().then((function(){t.info("Successfully played inadvertently paused <"+n+"> element"),t.debug("Element:",r),t.debug("RemoteMediaTrack:",e)})).catch((function(o){t.warn("Error while playing inadvertently paused <"+n+"> element:",{err:o,el:r,remoteMediaTrack:e})})))}))}return y.onVisibilityChange(2,r),function(){y.offVisibilityChange(2,r)}}(this)),n},t.prototype.detach=function(t){var n=e.prototype.detach.call(this,t);return 0===this._attachments.size&&(this.mediaStreamTrack.enabled=!1,this.processedTrack&&(this.processedTrack.enabled=!1),this._workaroundWebKitBug212780Cleanup&&(this._workaroundWebKitBug212780Cleanup(),this._workaroundWebKitBug212780Cleanup=null)),n},t}(e)}},1891:function(e,t,n){"use strict";var r=n(2125),o=n(2126),c=n(2127),l=n(2128),d=function(e,t,n){if("string"!=typeof e)throw new Error("RTCPeerConnection id must be a string");Object.defineProperties(this,{peerConnectionId:{value:e,enumerable:!0},localAudioTrackStats:{value:t.localAudioTrackStats.map((function(e){return new r(e.trackId,e,n)})),enumerable:!0},localVideoTrackStats:{value:t.localVideoTrackStats.map((function(e){return new o(e.trackId,e,n)})),enumerable:!0},remoteAudioTrackStats:{value:t.remoteAudioTrackStats.map((function(e){return new c(e.trackId,e)})),enumerable:!0},remoteVideoTrackStats:{value:t.remoteVideoTrackStats.map((function(e){return new l(e.trackId,e)})),enumerable:!0}})};e.exports=d},1892:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n,r){var o=e.call(this,t,n)||this;return Object.defineProperties(o,{bytesSent:{value:"number"==typeof n.bytesSent?n.bytesSent:r?0:null,enumerable:!0},packetsSent:{value:"number"==typeof n.packetsSent?n.packetsSent:r?0:null,enumerable:!0},roundTripTime:{value:"number"==typeof n.roundTripTime?n.roundTripTime:r?0:null,enumerable:!0}}),o}return o(t,e),t}(n(1893));e.exports=c},1893:function(e,t,n){"use strict";var r=function(e,t){if("string"!=typeof e)throw new Error("Track id must be a string");Object.defineProperties(this,{trackId:{value:e,enumerable:!0},trackSid:{value:t.trackSid,enumerable:!0},timestamp:{value:t.timestamp,enumerable:!0},ssrc:{value:t.ssrc,enumerable:!0},packetsLost:{value:"number"==typeof t.packetsLost?t.packetsLost:null,enumerable:!0},codec:{value:"string"==typeof t.codecName?t.codecName:null,enumerable:!0}})};e.exports=r},1894:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){var r=e.call(this,t,n)||this;return Object.defineProperties(r,{bytesReceived:{value:"number"==typeof n.bytesReceived?n.bytesReceived:null,enumerable:!0},packetsReceived:{value:"number"==typeof n.packetsReceived?n.packetsReceived:null,enumerable:!0}}),r}return o(t,e),t}(n(1893));e.exports=c},1895:function(e,t,n){var r=n(1896),o=n(2135),c=n(1898),l=n(2136);e.exports.Backoff=r,e.exports.FunctionCall=l,e.exports.FibonacciStrategy=c,e.exports.ExponentialStrategy=o,e.exports.fibonacci=function(e){return new r(new c(e))},e.exports.exponential=function(e){return new r(new o(e))},e.exports.call=function(e,t,n){var r=Array.prototype.slice.call(arguments);return e=r[0],t=r.slice(1,r.length-1),n=r[r.length-1],new l(e,t,n)}},1896:function(e,t,n){var r=n(373),o=n(1822);function c(e){r.EventEmitter.call(this),this.backoffStrategy_=e,this.maxNumberOfRetry_=-1,this.backoffNumber_=0,this.backoffDelay_=0,this.timeoutID_=-1,this.handlers={backoff:this.onBackoff_.bind(this)}}n(1521).inherits(c,r.EventEmitter),c.prototype.failAfter=function(e){o.checkArgument(e>0,"Expected a maximum number of retry greater than 0 but got %s.",e),this.maxNumberOfRetry_=e},c.prototype.backoff=function(e){o.checkState(-1===this.timeoutID_,"Backoff in progress."),this.backoffNumber_===this.maxNumberOfRetry_?(this.emit("fail",e),this.reset()):(this.backoffDelay_=this.backoffStrategy_.next(),this.timeoutID_=setTimeout(this.handlers.backoff,this.backoffDelay_),this.emit("backoff",this.backoffNumber_,this.backoffDelay_,e))},c.prototype.onBackoff_=function(){this.timeoutID_=-1,this.emit("ready",this.backoffNumber_,this.backoffDelay_),this.backoffNumber_++},c.prototype.reset=function(){this.backoffNumber_=0,this.backoffStrategy_.reset(),clearTimeout(this.timeoutID_),this.timeoutID_=-1},e.exports=c},1897:function(e,t,n){n(373),n(1521);function r(e){return null!=e}function o(e){if(r((e=e||{}).initialDelay)&&e.initialDelay<1)throw new Error("The initial timeout must be greater than 0.");if(r(e.maxDelay)&&e.maxDelay<1)throw new Error("The maximal timeout must be greater than 0.");if(this.initialDelay_=e.initialDelay||100,this.maxDelay_=e.maxDelay||1e4,this.maxDelay_<=this.initialDelay_)throw new Error("The maximal backoff delay must be greater than the initial backoff delay.");if(r(e.randomisationFactor)&&(e.randomisationFactor<0||e.randomisationFactor>1))throw new Error("The randomisation factor must be between 0 and 1.");this.randomisationFactor_=e.randomisationFactor||0}o.prototype.getMaxDelay=function(){return this.maxDelay_},o.prototype.getInitialDelay=function(){return this.initialDelay_},o.prototype.next=function(){var e=this.next_(),t=1+Math.random()*this.randomisationFactor_;return Math.round(e*t)},o.prototype.next_=function(){throw new Error("BackoffStrategy.next_() unimplemented.")},o.prototype.reset=function(){this.reset_()},o.prototype.reset_=function(){throw new Error("BackoffStrategy.reset_() unimplemented.")},e.exports=o},1898:function(e,t,n){var r=n(1521),o=n(1897);function c(e){o.call(this,e),this.backoffDelay_=0,this.nextBackoffDelay_=this.getInitialDelay()}r.inherits(c,o),c.prototype.next_=function(){var e=Math.min(this.nextBackoffDelay_,this.getMaxDelay());return this.nextBackoffDelay_+=this.backoffDelay_,this.backoffDelay_=e,e},c.prototype.reset_=function(){this.nextBackoffDelay_=this.getInitialDelay(),this.backoffDelay_=0},e.exports=c},1899:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1900),l=n(1901),d=n(1902),f=function(e){function t(t,n,r,o,c,l,d){var f=e.call(this,t,n,r)||this,h=c>0?o/c:0;return Object.defineProperties(f,{deltaPacketsLost:{enumerable:!0,value:o},deltaPacketsReceived:{enumerable:!0,value:c},fractionLost:{enumerable:!0,value:l},jitter:{enumerable:!0,value:d},phonyFractionLost:{enumerable:!0,value:h}}),f}return o(t,e),t.of=function(e,n,r){if(n.id!==r.id)throw new Error("RTCStats IDs must match");var o=(r.timestamp-n.timestamp)/1e3,c=r.bytesReceived-n.bytesReceived,l=o>0?c/o*8:0,d=Math.max(r.packetsLost-n.packetsLost,0),f=r.packetsReceived-n.packetsReceived,h=r.fractionLost,v=r.jitter;return new t(n.id,e,l,d,f,h,v)},t.summarize=function(e){var t=e.map((function(e){return e.summarize()}));return{bitrate:d(t.map((function(summary){return summary.bitrate}))),fractionLost:c(t.map((function(summary){return summary.fractionLost}))),jitter:c(t.map((function(summary){return summary.jitter})))}},t.prototype.summarize=function(){return{bitrate:this.bitrate,fractionLost:"number"==typeof this.fractionLost?this.fractionLost:this.phonyFractionLost,jitter:this.jitter}},t}(l);e.exports=f},1900:function(e,t,n){"use strict";e.exports=function(e){return(e=e.filter((function(e){return"number"==typeof e}))).length<1?void 0:e.reduce((function(e,t){return t+e}))/e.length}},1901:function(e,t,n){"use strict";var r=function(e,t,n){Object.defineProperties(this,{id:{enumerable:!0,value:e},trackId:{enumerable:!0,value:t},bitrate:{enumerable:!0,value:n}})};e.exports=r},1902:function(e,t,n){"use strict";e.exports=function(e){return e.reduce((function(e,t){return"number"==typeof t?t+e:e}),0)}},1903:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1900),l=n(1901),d=n(1902),f=function(e){function t(t,n,r,o){var c=e.call(this,t,n,r)||this;return Object.defineProperties(c,{rtt:{enumerable:!0,value:o}}),c}return o(t,e),t.of=function(e,n,r,o){if(n.id!==r.id)throw new Error("RTCStats IDs must match");var c=(r.timestamp-n.timestamp)/1e3,l=r.bytesSent-n.bytesSent,d=c>0?l/c*8:0,f=o&&"number"==typeof o.roundTripTime?o.roundTripTime/1e3:void 0;return new t(n.id,e,d,f)},t.summarize=function(e){return{bitrate:d(e.map((function(e){return e.bitrate}))),rtt:c(e.map((function(e){return e.rtt})))}},t}(l);e.exports=f},1904:function(e,t,n){"use strict";var r=function(e,t,n){Object.defineProperties(this,{id:{enumerable:!0,value:e,writable:!0},trackId:{enumerable:!0,value:t,writable:!0},lastStats:{enumerable:!0,value:n,writable:!0}})};e.exports=r},1905:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(){var t=e.call(this)||this;return Object.defineProperties(t,{_isEnabled:{value:null,writable:!0},isEnabled:{enumerable:!0,get:function(){return this._isEnabled}}}),t}return o(t,e),t.prototype.disable=function(){return this.enable(!1)},t.prototype.enable=function(e){return e="boolean"!=typeof e||e,this.isEnabled!==e&&(this._isEnabled=e,this.emit("updated")),this},t}(n(373).EventEmitter);e.exports=c},1906:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1905),l=n(1616),d=n(1609),f=n(1410).buildLogLevels,h=n(1425).DEFAULT_LOG_LEVEL,v=n(1570),_=n(1571),m=_.MediaConnectionError,y=_.MediaDTLSTransportFailedError,w=_.SignalingConnectionDisconnectedError,k=0,S={connected:["reconnecting","disconnected"],reconnecting:["connected","disconnected"],disconnected:[]},T=function(e){function t(t,n,r,o){var l=this;o=Object.assign({logLevel:h,RecordingSignaling:c,Timeout:d},o);var _=f(o.logLevel);l=e.call(this,"connected",S)||this;var m=o.RecordingSignaling,w=new o.Timeout((function(){l._disconnect(l._reconnectingError)}),o.sessionTimeout,!1);return Object.defineProperties(l,{_instanceId:{value:k++},_log:{value:o.log?o.log.createLog("default",l):new v("default",l,_,o.loggerName)},_mediaConnectionIsReconnecting:{writable:!0,value:!1},_options:{value:o},_reconnectingError:{value:null,writable:!0},_sessionTimeout:{value:w},dominantSpeakerSid:{enumerable:!0,value:null,writable:!0},localParticipant:{enumerable:!0,value:t},name:{enumerable:!0,value:r},participants:{enumerable:!0,value:new Map},recording:{enumerable:!0,value:new m},sid:{enumerable:!0,value:n}}),l.on("connectionStateChanged",(function(){"failed"!==l.connectionState||["disconnected","failed"].includes(l.iceConnectionState)||l._disconnect(new y)})),l.on("iceConnectionStateChanged",(function(){return O(l)})),l.on("signalingConnectionStateChanged",(function(){return O(l)})),setTimeout((function(){return O(l)})),l}return o(t,e),t.prototype._disconnect=function(e){return"disconnected"!==this.state&&(this.preempt("disconnected",null,[e]),!0)},t.prototype.toString=function(){return"[RoomSignaling #"+this._instanceId+": "+(this.localParticipant?this.localParticipant.sid:"null")+"]"},t.prototype.connectParticipant=function(e){var t=this;return"disconnected"!==e.state&&(!this.participants.has(e.sid)&&(this.participants.set(e.sid,e),e.on("stateChanged",(function n(r){"disconnected"===r&&(e.removeListener("stateChanged",n),t.participants.delete(e.sid),t.emit("participantDisconnected",e))})),this.emit("participantConnected",e),!0))},t.prototype.disconnect=function(){return this._disconnect()},t.prototype.setDominantSpeaker=function(e){this.dominantSpeakerSid=e,this.emit("dominantSpeakerChanged")},t}(l);function O(e){var t;"disconnected"!==e.state&&"disconnected"!==e.signalingConnectionState?("reconnecting"===e.signalingConnectionState?t=e.signalingConnectionState:"failed"===e.iceConnectionState?(e._mediaConnectionIsReconnecting=!0,t="reconnecting"):"new"===e.iceConnectionState||"checking"===e.iceConnectionState?t=e._mediaConnectionIsReconnecting?"reconnecting":"connected":(e._mediaConnectionIsReconnecting=!1,e._reconnectingError=null,e._sessionTimeout.clear(),t="connected"),t!==e.state&&("reconnecting"===t?(e._reconnectingError="reconnecting"===e.signalingConnectionState?new w:new m,e._sessionTimeout.start(),e.preempt(t,null,[e._reconnectingError])):e.preempt(t))):e._sessionTimeout.clear()}e.exports=T},1907:function(e,t,n){"use strict";var r=n(2163),o=n(2167),c=function(e){var t=e.send,n=e.recv,c=e.sendStats,l=void 0===c?null:c,d=e.recvStats,f=void 0===d?null:d;Object.defineProperties(this,{send:{value:t,enumerable:!0},recv:{value:n,enumerable:!0},sendStats:{value:l?new r(l):null,enumerable:!0},recvStats:{value:f?new o(f):null,enumerable:!0}})};e.exports=c},1908:function(e,t,n){"use strict";var r=n(2164),o=n(2165),c=n(2166),l=function(e){var t=e.bandwidth,n=void 0===t?null:t,l=e.fractionLost,d=void 0===l?null:l,f=e.latency,h=void 0===f?null:f;Object.defineProperties(this,{bandwidth:{value:n?new r(n):null,enumerable:!0},fractionLost:{value:d?new o(d):null,enumerable:!0},latency:{value:h?new c(h):null,enumerable:!0}})};e.exports=l},1909:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n,r,o){var c=e.call(this)||this,l=null;return Object.defineProperties(c,{_error:{value:null,writable:!0},_isEnabled:{value:r,writable:!0},_priority:{value:o,writable:!0},_trackTransceiver:{value:null,writable:!0},_sid:{get:function(){return l},set:function(e){null===l&&(l=e)}},kind:{enumerable:!0,value:n},name:{enumerable:!0,value:t}}),c}return o(t,e),Object.defineProperty(t.prototype,"error",{get:function(){return this._error},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isEnabled",{get:function(){return this._isEnabled},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"priority",{get:function(){return this._priority},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"sid",{get:function(){return this._sid},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"trackTransceiver",{get:function(){return this._trackTransceiver},enumerable:!1,configurable:!0}),t.prototype.disable=function(){return this.enable(!1)},t.prototype.enable=function(e){return e="boolean"!=typeof e||e,this.isEnabled!==e&&(this._isEnabled=e,this.emit("updated")),this},t.prototype.setTrackTransceiver=function(e){return e=e||null,this.trackTransceiver!==e&&(this._trackTransceiver=e,this.emit("updated")),this},t.prototype.setSid=function(e){return null===this.sid&&(this._sid=e,this.emit("updated")),this},t}(n(373).EventEmitter);e.exports=c},1910:function(e,t,n){"use strict";var r=n(1425),o=r.DEFAULT_LOG_LEVEL,c=r.DEFAULT_LOGGER_NAME;function l(e,t){t=Object.assign({loggerName:c,logLevel:o},t);var n={};n.loggerName=t.loggerName,n.logLevel=t.logLevel,delete t.loggerName,delete t.logLevel;var r=t.createLocalTracks;return delete t.createLocalTracks,n[e]=!(Object.keys(t).length>0)||t,r(n).then((function(e){return e[0]}))}e.exports={audio:function(e){return l("audio",e)},video:function(e){return l("video",e)}}},2074:function(e,t,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var s,i=1,t=arguments.length;i<t;i++)for(var p in s=arguments[i])Object.prototype.hasOwnProperty.call(s,p)&&(e[p]=s[p]);return e}).apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0});var o=n(2075),c=n(2097),l={connect:n(2107),createLocalAudioTrack:n(1910).audio,createLocalVideoTrack:n(1910).video,isSupported:n(2181)(),version:n(1863).version,Logger:n(1872),LocalAudioTrack:n(1615).LocalAudioTrack,LocalDataTrack:n(1615).LocalDataTrack,LocalVideoTrack:n(1615).LocalVideoTrack};var d=l.isSupported,f=l.version,h=l.Logger,v=l.LocalAudioTrack,_=l.LocalVideoTrack,m=l.LocalDataTrack;e.exports={connect:function(e,t){var n=r({createLocalTracks:o.createLocalTracks},t);return l.connect(e,n)},createLocalAudioTrack:function(e){var t=r({createLocalTracks:o.createLocalTracks},e);return l.createLocalAudioTrack(t)},createLocalVideoTrack:function(e){var t=r({createLocalTracks:o.createLocalTracks},e);return l.createLocalVideoTrack(t)},createLocalTracks:o.createLocalTracks,runPreflight:c.runPreflight,isSupported:d,version:f,Logger:h,LocalAudioTrack:v,LocalVideoTrack:_,LocalDataTrack:m}},2075:function(e,t,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var s,i=1,t=arguments.length;i<t;i++)for(var p in s=arguments[i])Object.prototype.hasOwnProperty.call(s,p)&&(e[p]=s[p]);return e}).apply(this,arguments)},o=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,c){function l(e){try{f(r.next(e))}catch(e){c(e)}}function d(e){try{f(r.throw(e))}catch(e){c(e)}}function f(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(l,d)}f((r=r.apply(e,t||[])).next())}))},c=this&&this.__generator||function(e,body){var t,n,r,g,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return g={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(g[Symbol.iterator]=function(){return this}),g;function c(c){return function(l){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;o;)try{if(t=1,n&&(r=2&c[0]?n.return:c[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,c[1])).done)return r;switch(n=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,n=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=body.call(e,o)}catch(e){c=[6,e],n=0}finally{t=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,l])}}},l=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},d=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.createLocalTracks=void 0;var f=n(1410),h=f.asLocalTrack,v=f.buildLogLevels,_=n(1608),m=_.getUserMedia,y=_.MediaStreamTrack,w=n(1615),k=w.LocalAudioTrack,S=w.LocalDataTrack,T=w.LocalVideoTrack,O=n(1570),P=n(1425),C=P.DEFAULT_LOG_LEVEL,E=P.DEFAULT_LOGGER_NAME,R=n(1876),j=0;t.createLocalTracks=function(e){return o(this,void 0,void 0,(function(){var t,n,o,f,_,w,P,x,L,A,D,I;return c(this,(function(c){switch(c.label){case 0:if(n=r({audio:t=!(e&&("audio"in e||"video"in e)),getUserMedia:m,loggerName:E,logLevel:C,LocalAudioTrack:k,LocalDataTrack:S,LocalVideoTrack:T,MediaStreamTrack:y,Log:O,video:t},e),o="[createLocalTracks #"+ ++j+"]",f=v(n.logLevel),_=new n.Log("default",o,f,n.loggerName),delete(w=Object.assign({log:_},n)).name,!1===n.audio&&!1===n.video)return _.info("Neither audio nor video requested, so returning empty LocalTracks"),[2,[]];if(n.tracks)return _.info("Adding user-provided LocalTracks"),_.debug("LocalTracks:",n.tracks),[2,n.tracks];(P={audio:"object"==typeof n.audio&&n.audio.name?{name:n.audio.name}:{},video:"object"==typeof n.video&&n.video.name?{name:n.video.name}:{}}).audio.isCreatedByCreateLocalTracks=!0,P.video.isCreatedByCreateLocalTracks=!0,"object"==typeof n.audio&&"boolean"==typeof n.audio.workaroundWebKitBug1208516&&(P.audio.workaroundWebKitBug1208516=n.audio.workaroundWebKitBug1208516),"object"==typeof n.video&&"boolean"==typeof n.video.workaroundWebKitBug1208516&&(P.video.workaroundWebKitBug1208516=n.video.workaroundWebKitBug1208516),"object"==typeof n.audio&&delete n.audio.name,"object"==typeof n.video&&delete n.video.name,x={audio:n.audio,video:n.video},L="object"==typeof n.audio&&n.audio.workaroundWebKitBug180748,c.label=1;case 1:return c.trys.push([1,3,,4]),[4,L?R(_,n.getUserMedia,x):n.getUserMedia(x)];case 2:return A=c.sent(),D=d(d([],l(A.getAudioTracks())),l(A.getVideoTracks())),_.info("Call to getUserMedia successful; got MediaStreamTracks:",D),[2,D.map((function(e){return h(e,r(r({},P[e.kind]),w))}))];case 3:throw I=c.sent(),_.warn("Call to getUserMedia failed:",I),I;case 4:return[2]}}))}))}},2076:function(e,t,n){"use strict";var r=n(1439).flatMap,o=n(1439).guessBrowser,c=n(1439).guessBrowserVersion,l=n(1694).getSdpFormat,d=o(),f=c(),h="chrome"===d,v="firefox"===d,_="safari"===d,m=h?f.major:null;function y(e){var t=Array.from(e.values()).find((function(e){return"candidate-pair"===e.type&&e.nominated}));if(!t)return null;var n=e.get(t.localCandidateId),r=e.get(t.remoteCandidateId),o=[{key:"candidateType",type:"string"},{key:"ip",type:"string"},{key:"port",type:"number"},{key:"priority",type:"number"},{key:"protocol",type:"string"},{key:"url",type:"string"}],c=o.concat([{key:"deleted",type:"boolean"},{key:"relayProtocol",type:"string"}]),l=n?c.reduce((function(e,t){return e[t.key]=typeof n[t.key]===t.type?n[t.key]:"deleted"!==t.key&&null,e}),{}):null,d=r?o.reduce((function(e,t){return e[t.key]=typeof r[t.key]===t.type?r[t.key]:null,e}),{}):null;return[{key:"availableIncomingBitrate",type:"number"},{key:"availableOutgoingBitrate",type:"number"},{key:"bytesReceived",type:"number"},{key:"bytesSent",type:"number"},{key:"consentRequestsSent",type:"number"},{key:"currentRoundTripTime",type:"number"},{key:"lastPacketReceivedTimestamp",type:"number"},{key:"lastPacketSentTimestamp",type:"number"},{key:"nominated",type:"boolean"},{key:"priority",type:"number"},{key:"readable",type:"boolean"},{key:"requestsReceived",type:"number"},{key:"requestsSent",type:"number"},{key:"responsesReceived",type:"number"},{key:"responsesSent",type:"number"},{key:"retransmissionsReceived",type:"number"},{key:"retransmissionsSent",type:"number"},{key:"state",type:"string",fixup:function(e){return"inprogress"===e?"in-progress":e}},{key:"totalRoundTripTime",type:"number"},{key:"transportId",type:"string"},{key:"writable",type:"boolean"}].reduce((function(e,n){return e[n.key]=typeof t[n.key]===n.type?n.fixup?n.fixup(t[n.key]):t[n.key]:null,e}),{localCandidate:l,remoteCandidate:d})}function w(e){var t=Array.from(e.values()).find((function(e){return"candidate-pair"===e.type&&e.nominated}));if(!t)return null;var n=e.get(t.localCandidateId),r=e.get(t.remoteCandidateId),o=[{key:"candidateType",type:"string"},{key:"ip",ffKeys:["address","ipAddress"],type:"string"},{key:"port",ffKeys:["portNumber"],type:"number"},{key:"priority",type:"number"},{key:"protocol",ffKeys:["transport"],type:"string"},{key:"url",type:"string"}],c=o.concat([{key:"deleted",type:"boolean"},{key:"relayProtocol",type:"string"}]),l={host:"host",peerreflexive:"prflx",relayed:"relay",serverreflexive:"srflx"},d=n?c.reduce((function(e,t){var r=t.ffKeys&&t.ffKeys.find((function(e){return e in n}))||t.key;return e[t.key]=typeof n[r]===t.type?"candidateType"===r&&l[n[r]]||n[r]:"deleted"!==r&&null,e}),{}):null,f=r?o.reduce((function(e,t){var n=t.ffKeys&&t.ffKeys.find((function(e){return e in r}))||t.key;return e[t.key]=typeof r[n]===t.type?"candidateType"===n&&l[r[n]]||r[n]:null,e}),{}):null;return[{key:"availableIncomingBitrate",type:"number"},{key:"availableOutgoingBitrate",type:"number"},{key:"bytesReceived",type:"number"},{key:"bytesSent",type:"number"},{key:"consentRequestsSent",type:"number"},{key:"currentRoundTripTime",type:"number"},{key:"lastPacketReceivedTimestamp",type:"number"},{key:"lastPacketSentTimestamp",type:"number"},{key:"nominated",type:"boolean"},{key:"priority",type:"number"},{key:"readable",type:"boolean"},{key:"requestsReceived",type:"number"},{key:"requestsSent",type:"number"},{key:"responsesReceived",type:"number"},{key:"responsesSent",type:"number"},{key:"retransmissionsReceived",type:"number"},{key:"retransmissionsSent",type:"number"},{key:"state",type:"string"},{key:"totalRoundTripTime",type:"number"},{key:"transportId",type:"string"},{key:"writable",type:"boolean"}].reduce((function(e,n){return e[n.key]=typeof t[n.key]===n.type?t[n.key]:null,e}),{localCandidate:d,remoteCandidate:f})}function k(e,t,n){var o="local"===n?"getSenders":"getReceivers";return e[o]?e[o]().map((function(e){return e.track})).filter((function(track){return track&&track.kind===t})):r(e["local"===n?"getLocalStreams":"getRemoteStreams"](),(function(e){return e["audio"===t?"getAudioTracks":"getVideoTracks"]()}))}function S(e,track){return new Promise((function(t,n){m&&m<67?e.getStats((function(e){t([T(e,track)])}),null,n):e.getStats(track).then((function(e){t(function(e){var t=null,n=[],r=null,o=null,track=null,c=null,l=null;e.forEach((function(e){switch(e.type){case"inbound-rtp":t=e;break;case"outbound-rtp":n.push(e);break;case"media-source":l=e;break;case"track":track=e;break;case"codec":c=e;break;case"remote-inbound-rtp":r=e;break;case"remote-outbound-rtp":o=e}}));var d=track&&track.remoteSource,f=[],h=d?o:r;return(d?[t]:n).forEach((function(source){var e={},t=[source,l,track,c,h&&h.ssrc===source.ssrc?h:null];function n(e){var n=t.find((function(t){return t&&void 0!==t[e]}))||null;return n?n[e]:null}var r=n("ssrc");"number"==typeof r&&(e.ssrc=String(r));var o=n("timestamp");e.timestamp=Math.round(o);var v=n("mimeType");"string"==typeof v&&(v=v.split("/"),e.codecName=v[v.length-1]);var _=n("roundTripTime");"number"==typeof _&&(e.roundTripTime=Math.round(1e3*_));var m=n("jitter");"number"==typeof m&&(e.jitter=Math.round(1e3*m));var y=n("frameWidth");"number"==typeof y&&(d?e.frameWidthReceived=y:(e.frameWidthSent=y,e.frameWidthInput=track.frameWidth));var w=n("frameHeight");"number"==typeof w&&(d?e.frameHeightReceived=w:(e.frameHeightSent=w,e.frameHeightInput=track.frameHeight));var k=n("framesPerSecond");"number"==typeof k&&(e[d?"frameRateReceived":"frameRateSent"]=k);var S=n("bytesReceived");"number"==typeof S&&(e.bytesReceived=S);var T=n("bytesSent");"number"==typeof T&&(e.bytesSent=T);var O=n("packetsLost");"number"==typeof O&&(e.packetsLost=O);var P=n("packetsReceived");"number"==typeof P&&(e.packetsReceived=P);var C=n("packetsSent");"number"==typeof C&&(e.packetsSent=C);var E=n("audioLevel");"number"==typeof E&&(E=Math.round(32767*E),d?e.audioOutputLevel=E:e.audioInputLevel=E);var R=n("totalPacketSendDelay");"number"==typeof R&&(e.totalPacketSendDelay=R);var j=n("totalEncodeTime");"number"==typeof j&&(e.totalEncodeTime=j);var x=n("framesEncoded");"number"==typeof x&&(e.framesEncoded=x);var L=n("estimatedPlayoutTimestamp");"number"==typeof L&&(e.estimatedPlayoutTimestamp=L);var A=n("totalDecodeTime");"number"==typeof A&&(e.totalDecodeTime=A);var D=n("framesDecoded");"number"==typeof D&&(e.framesDecoded=D);var I=n("jitterBufferDelay");"number"==typeof I&&(e.jitterBufferDelay=I);var M=n("jitterBufferEmittedCount");"number"==typeof M&&(e.jitterBufferEmittedCount=M),f.push(e)})),f}(e))}),n)}))}function T(e,track){var t=e.result().find((function(e){return"ssrc"===e.type&&e.stat("googTrackId")===track.id})),n={};return t&&(n.timestamp=Math.round(Number(t.timestamp)),n=t.names().reduce((function(e,n){switch(n){case"googCodecName":e.codecName=t.stat(n);break;case"googRtt":e.roundTripTime=Number(t.stat(n));break;case"googJitterReceived":e.jitter=Number(t.stat(n));break;case"googFrameWidthInput":e.frameWidthInput=Number(t.stat(n));break;case"googFrameHeightInput":e.frameHeightInput=Number(t.stat(n));break;case"googFrameWidthSent":e.frameWidthSent=Number(t.stat(n));break;case"googFrameHeightSent":e.frameHeightSent=Number(t.stat(n));break;case"googFrameWidthReceived":e.frameWidthReceived=Number(t.stat(n));break;case"googFrameHeightReceived":e.frameHeightReceived=Number(t.stat(n));break;case"googFrameRateInput":e.frameRateInput=Number(t.stat(n));break;case"googFrameRateSent":e.frameRateSent=Number(t.stat(n));break;case"googFrameRateReceived":e.frameRateReceived=Number(t.stat(n));break;case"ssrc":e[n]=t.stat(n);break;case"bytesReceived":case"bytesSent":case"packetsLost":case"packetsReceived":case"packetsSent":case"audioInputLevel":case"audioOutputLevel":e[n]=Number(t.stat(n))}return e}),n)),n}function O(e,t){e=e||new Map;var n=null,r=null;e.forEach((function(t){if(!t.isRemote)switch(t.type){case"inbound-rtp":n=t,r=e.get(t.remoteId);break;case"outbound-rtp":r=t,n=e.get(t.remoteId)}}));var o=t?n:r,c=t?r:n;function l(e){return o&&void 0!==o[e]?o[e]:c&&void 0!==c[e]?c[e]:null}var d={},f=l("timestamp");d.timestamp=Math.round(f);var h=l("ssrc");"number"==typeof h&&(d.ssrc=String(h));var v=l("bytesSent");"number"==typeof v&&(d.bytesSent=v);var _=l("packetsLost");"number"==typeof _&&(d.packetsLost=_);var m=l("packetsSent");"number"==typeof m&&(d.packetsSent=m);var y=l("roundTripTime");"number"==typeof y&&(d.roundTripTime=Math.round(1e3*y));var w=l("jitter");"number"==typeof w&&(d.jitter=Math.round(1e3*w));var k=l("framerateMean");"number"==typeof k&&(d.frameRateSent=Math.round(k));var S=l("bytesReceived");"number"==typeof S&&(d.bytesReceived=S);var T=l("packetsReceived");"number"==typeof T&&(d.packetsReceived=T);var O=l("framerateMean");"number"==typeof O&&(d.frameRateReceived=Math.round(O));var P=l("totalPacketSendDelay");"number"==typeof P&&(d.totalPacketSendDelay=P);var C=l("totalEncodeTime");"number"==typeof C&&(d.totalEncodeTime=C);var E=l("framesEncoded");"number"==typeof E&&(d.framesEncoded=E);var R=l("estimatedPlayoutTimestamp");"number"==typeof R&&(d.estimatedPlayoutTimestamp=R);var j=l("totalDecodeTime");"number"==typeof j&&(d.totalDecodeTime=j);var x=l("framesDecoded");"number"==typeof x&&(d.framesDecoded=x);var L=l("jitterBufferDelay");"number"==typeof L&&(d.jitterBufferDelay=L);var A=l("jitterBufferEmittedCount");return"number"==typeof A&&(d.jitterBufferEmittedCount=A),d}e.exports=function(e,t){return e&&"function"==typeof e.getStats?function(e,t){var n=k(e,"audio","local"),o=k(e,"video","local"),c=k(e,"audio"),d=k(e,"video"),f={activeIceCandidatePair:null,localAudioTrackStats:[],localVideoTrackStats:[],remoteAudioTrackStats:[],remoteVideoTrackStats:[]},m=r([[n,"localAudioTrackStats",!1],[o,"localVideoTrackStats",!1],[c,"remoteAudioTrackStats",!0],[d,"remoteVideoTrackStats",!0]],(function(n){var r=n[0],o=n[1],c=n[2];return r.map((function(track){return function(e,track,t){if(void 0!==(t=t||{}).testForChrome||h)return S(e,track);if(void 0!==t.testForFirefox||v)return function(e,track,t){return new Promise((function(n,r){e.getStats(track).then((function(e){n([O(e,t)])}),r)}))}(e,track,t.isRemote);if(void 0!==t.testForSafari||_)return void 0!==t.testForSafari||"unified"===l()?S(e,track):Promise.reject(new Error(["getStats() is not supported on this version of Safari","due to this bug: https://bugs.webkit.org/show_bug.cgi?id=192601"].join(" ")));return Promise.reject(new Error("RTCPeerConnection#getStats() not supported"))}(e,track,Object.assign({isRemote:c},t)).then((function(e){e.forEach((function(e){e.trackId=track.id,f[o].push(e)}))}))}))}));return Promise.all(m).then((function(){return function(e,t){if(void 0!==(t=t||{}).testForChrome||h||void 0!==t.testForSafari||_)return e.getStats().then(y);if(void 0!==t.testForFirefox||v)return e.getStats().then(w);return Promise.reject(new Error("RTCPeerConnection#getStats() not supported"))}(e,t)})).then((function(e){return f.activeIceCandidatePair=e,f}))}(e,t):Promise.reject(new Error("Given PeerConnection does not support getStats"))}},2077:function(e,t,n){"use strict";e.exports=function(e){return"object"==typeof navigator&&"object"==typeof navigator.mediaDevices&&"function"==typeof navigator.mediaDevices.getUserMedia?(e=e||{audio:!0,video:!0},navigator.mediaDevices.getUserMedia(e)):Promise.reject(new Error("getUserMedia is not supported"))}},2078:function(e,t,n){"use strict";"function"==typeof MediaStreamTrack?e.exports=MediaStreamTrack:e.exports=function(){throw new Error("MediaStreamTrack is not supported")}},2079:function(e,t,n){"use strict";"function"==typeof RTCIceCandidate?e.exports=RTCIceCandidate:e.exports=function(){throw new Error("RTCIceCandidate is not supported")}},2080:function(e,t,n){"use strict";if("function"==typeof RTCPeerConnection)switch((0,n(1439).guessBrowser)()){case"chrome":e.exports=n(2081);break;case"firefox":e.exports=n(2085);break;case"safari":e.exports=n(2086);break;default:e.exports=RTCPeerConnection}else e.exports=function(){throw new Error("RTCPeerConnection is not supported")}},2081:function(e,t,n){"use strict";var r=n(1866),o=n(1816),c=n(1521).inherits,l=n(1867),d=n(1865),f=n(2084),h=n(1694),v=n(1439),_=n(1439).isIOSChrome,m="unified"===h.getSdpFormat();function y(e,t){if(!(this instanceof y))return new y(e,t);o.call(this),e=e||{};var n=Object.assign(e.iceTransportPolicy?{iceTransports:e.iceTransportPolicy}:{},e);v.interceptEvent(this,"datachannel"),v.interceptEvent(this,"signalingstatechange");var r=h.getSdpFormat(n.sdpSemantics),c=new RTCPeerConnection(n,t);Object.defineProperties(this,{_appliedTracksToSSRCs:{value:new Map,writable:!0},_localStream:{value:new d},_peerConnection:{value:c},_pendingLocalOffer:{value:null,writable:!0},_pendingRemoteOffer:{value:null,writable:!0},_rolledBackTracksToSSRCs:{value:new Map,writable:!0},_sdpFormat:{value:r},_senders:{value:new Map},_signalingStateLatch:{value:new l},_tracksToSSRCs:{value:new Map,writable:!0},localDescription:{enumerable:!0,get:function(){return this._pendingLocalOffer?this._pendingLocalOffer:c.localDescription}},remoteDescription:{enumerable:!0,get:function(){return this._pendingRemoteOffer?this._pendingRemoteOffer:c.remoteDescription}},signalingState:{enumerable:!0,get:function(){return this._pendingLocalOffer?"have-local-offer":this._pendingRemoteOffer?"have-remote-offer":c.signalingState}}});var f=this;c.addEventListener("datachannel",(function(e){O(e.channel),f.dispatchEvent(e)})),c.addEventListener("signalingstatechange",(function(){"stable"===c.signalingState&&(f._appliedTracksToSSRCs=new Map(f._tracksToSSRCs)),f._pendingLocalOffer||f._pendingRemoteOffer||f.dispatchEvent.apply(f,arguments)})),c.ontrack=function(){},"function"!=typeof RTCPeerConnection.prototype.addTrack&&c.addStream(this._localStream),v.proxyProperties(RTCPeerConnection.prototype,this,c)}function w(e,t,n){var r,o,c=t?e._pendingLocalOffer:e._pendingRemoteOffer,l=t?e._pendingRemoteOffer:e._pendingLocalOffer,d=t?"have-local-offer":"have-remote-offer",f=t?"setLocalDescription":"setRemoteDescription";if(!t&&l&&"answer"===n.type)r=function(e,t){var n=e._pendingLocalOffer;return e._peerConnection.setLocalDescription(n).then((function(){return e._pendingLocalOffer=null,e.setRemoteDescription(t)})).then((function(){e._signalingStateLatch.lower()}))}(e,n);else if("offer"===n.type){if(e.signalingState!==d&&"stable"!==e.signalingState)return Promise.reject(new Error("Cannot set "+(t?"local":"remote")+" offer in state "+e.signalingState));c||"low"!==e._signalingStateLatch.state||e._signalingStateLatch.raise();var h=e.signalingState;o=S(n),t?e._pendingLocalOffer=o:e._pendingRemoteOffer=o,r=Promise.resolve(),e.signalingState!==h&&r.then((function(){e.dispatchEvent(new Event("signalingstatechange"))}))}else"rollback"===n.type&&(e.signalingState!==d?r=Promise.reject(new Error("Cannot rollback "+(t?"local":"remote")+" description in "+e.signalingState)):(t?e._pendingLocalOffer=null:e._pendingRemoteOffer=null,e._rolledBackTracksToSSRCs=new Map(e._tracksToSSRCs),e._tracksToSSRCs=new Map(e._appliedTracksToSSRCs),(r=Promise.resolve()).then((function(){e.dispatchEvent(new Event("signalingstatechange"))}))));return r||e._peerConnection[f](S(n))}function k(e,t){return!!e.getTransceivers().find((function(e){return e.receiver&&e.receiver.track&&e.receiver.track.kind===t}))}function S(e){return e instanceof r&&e._description?e._description:new RTCSessionDescription(e)}function T(){return"maxRetransmitTime"in RTCDataChannel.prototype&&!("maxPacketLifeTime"in RTCDataChannel.prototype)}function O(e){return Object.defineProperty(e,"maxRetransmits",{value:65535===e.maxRetransmits?null:e.maxRetransmits}),T()&&Object.defineProperty(e,"maxPacketLifeTime",{value:65535===e.maxRetransmitTime?null:e.maxRetransmitTime}),e}function P(e,t,n){return"unified"===e?h.updateUnifiedPlanTrackIdsToSSRCs(t,n):h.updatePlanBTrackIdsToSSRCs(t,n)}c(y,o),"function"!=typeof RTCPeerConnection.prototype.addTrack?(y.prototype.addTrack=function(){var e=[].slice.call(arguments),track=e[0];if("closed"===this._peerConnection.signalingState)throw new Error("Cannot add MediaStreamTrack ["+track.id+", "+track.kind+"]: RTCPeerConnection is closed");var t=this._senders.get(track);if(t&&t.track)throw new Error("Cannot add MediaStreamTrack ["+track.id+", "+track.kind+"]: RTCPeerConnection already has it");return this._peerConnection.removeStream(this._localStream),this._localStream.addTrack(track),this._peerConnection.addStream(this._localStream),t=new f(track),this._senders.set(track,t),t},y.prototype.removeTrack=function(e){if("closed"===this._peerConnection.signalingState)throw new Error("Cannot remove MediaStreamTrack: RTCPeerConnection is closed");var track=e.track;track&&(e=this._senders.get(track))&&e.track&&(e.track=null,this._peerConnection.removeStream(this._localStream),this._localStream.removeTrack(track),this._peerConnection.addStream(this._localStream))},y.prototype.getSenders=function(){return Array.from(this._senders.values())}):y.prototype.removeTrack=function(e){if("closed"===this._peerConnection.signalingState)throw new Error("Cannot remove MediaStreamTrack: RTCPeerConnection is closed");try{this._peerConnection.removeTrack(e)}catch(e){}},y.prototype.addIceCandidate=function(e){var t,n=[].slice.call(arguments),r=this;return t="have-remote-offer"===this.signalingState?this._signalingStateLatch.when("low").then((function(){return r._peerConnection.addIceCandidate(e)})):this._peerConnection.addIceCandidate(e),n.length>1?v.legacyPromise(t,n[1],n[2]):t},y.prototype.close=function(){"closed"!==this.signalingState&&(this._pendingLocalOffer=null,this._pendingRemoteOffer=null,this._peerConnection.close())},y.prototype.createAnswer=function(){var e,t=[].slice.call(arguments),n=this;return e=this._pendingRemoteOffer?this._peerConnection.setRemoteDescription(this._pendingRemoteOffer).then((function(){return n._signalingStateLatch.lower(),n._peerConnection.createAnswer()})).then((function(e){return n._pendingRemoteOffer=null,n._rolledBackTracksToSSRCs.clear(),new r({type:"answer",sdp:P(n._sdpFormat,n._tracksToSSRCs,e.sdp)})}),(function(e){throw n._pendingRemoteOffer=null,e})):this._peerConnection.createAnswer().then((function(e){return n._rolledBackTracksToSSRCs.clear(),new r({type:"answer",sdp:P(n._sdpFormat,n._tracksToSSRCs,e.sdp)})})),t.length>1?v.legacyPromise(e,t[0],t[1]):e},y.prototype.createOffer=function(){var e=[].slice.call(arguments),t=(e.length>1?e[2]:e[0])||{},n=this;if(_()){if(t.offerToReceiveVideo&&!this._audioTransceiver&&(!m||!k(this,"audio"))){delete t.offerToReceiveAudio;try{this._audioTransceiver=m?this.addTransceiver("audio",{direction:"recvonly"}):this.addTransceiver("audio")}catch(e){return Promise.reject(e)}}if(t.offerToReceiveVideo&&!this._videoTransceiver&&(!m||!k(this,"video"))){delete t.offerToReceiveVideo;try{this._videoTransceiver=m?this.addTransceiver("video",{direction:"recvonly"}):this.addTransceiver("video")}catch(e){return Promise.reject(e)}}}var o=this._peerConnection.createOffer(t).then((function(e){return n._rolledBackTracksToSSRCs.clear(),new r({type:e.type,sdp:P(n._sdpFormat,n._tracksToSSRCs,e.sdp)})}));return e.length>1?v.legacyPromise(o,e[0],e[1]):o},y.prototype.createDataChannel=function(label,e){e=function(e){e=Object.assign({},e),T()&&"maxPacketLifeTime"in e&&(e.maxRetransmitTime=e.maxPacketLifeTime);return e}(e);var t=this._peerConnection.createDataChannel(label,e);return O(t),t},y.prototype.setLocalDescription=function(){var e=[].slice.call(arguments),t=e[0];this._rolledBackTracksToSSRCs.size>0&&(this._tracksToSSRCs=new Map(this._rolledBackTracksToSSRCs),this._rolledBackTracksToSSRCs.clear());var n=w(this,!0,t);return e.length>1?v.legacyPromise(n,e[1],e[2]):n},y.prototype.setRemoteDescription=function(){var e=[].slice.call(arguments),t=e[0];this._rolledBackTracksToSSRCs.clear();var n=w(this,!1,t);return e.length>1?v.legacyPromise(n,e[1],e[2]):n},v.delegateMethods(RTCPeerConnection.prototype,y.prototype,"_peerConnection"),e.exports=y},2082:function(e,t){e.exports=function(e){return e&&"object"==typeof e&&"function"==typeof e.copy&&"function"==typeof e.fill&&"function"==typeof e.readUInt8}},2083:function(e,t){"function"==typeof Object.create?e.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(e,t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}},2084:function(e,t,n){"use strict";e.exports=function(track){Object.defineProperties(this,{track:{enumerable:!0,value:track,writable:!0}})}},2085:function(e,t,n){"use strict";var r=n(1816),o=n(1868),c=n(1521).inherits,l=n(1694).updateUnifiedPlanTrackIdsToSSRCs,d=n(1439),f="object"==typeof navigator&&navigator.userAgent&&(navigator.userAgent.match(/Firefox\/61/)||navigator.userAgent.match(/Firefox\/62/));function h(e){if(!(this instanceof h))return new h(e);r.call(this),d.interceptEvent(this,"signalingstatechange");var t=new RTCPeerConnection(e);Object.defineProperties(this,{_initiallyNegotiatedDtlsRole:{value:null,writable:!0},_isClosed:{value:!1,writable:!0},_peerConnection:{value:t},_rollingBack:{value:!1,writable:!0},_tracksToSSRCs:{value:new Map},iceGatheringState:{enumerable:!0,get:function(){return this._isClosed?"complete":this._peerConnection.iceGatheringState}},localDescription:{enumerable:!0,get:function(){return m(this._peerConnection.localDescription,this._initiallyNegotiatedDtlsRole)}},signalingState:{enumerable:!0,get:function(){return this._isClosed?"closed":this._peerConnection.signalingState}}});var n,o=this;t.addEventListener("signalingstatechange",(function(){if(!o._rollingBack&&o.signalingState!==n){n=o.signalingState;var e=o.dispatchEvent.apply.bind(o.dispatchEvent,o,arguments);o._isClosed?setTimeout(e):e()}})),d.proxyProperties(RTCPeerConnection.prototype,this,t)}function v(e,t,n){var r=t?"setLocalDescription":"setRemoteDescription";return e._rollingBack=!0,e._peerConnection[r](new o({type:"rollback"})).then(n).then((function(t){return e._rollingBack=!1,t}),(function(t){throw e._rollingBack=!1,t}))}function _(e,t,n){if(!e._initiallyNegotiatedDtlsRole&&"offer"!==t.type){var r=t.sdp.match(/a=setup:([a-z]+)/);if(r){var o=r[1];e._initiallyNegotiatedDtlsRole=n?{active:"passive",passive:"active"}[o]:o}}}function m(e,t){return e&&"answer"===e.type&&t?new o({type:e.type,sdp:e.sdp.replace(/a=setup:[a-z]+/g,"a=setup:"+t)}):e}c(h,r),Object.defineProperty(h.prototype,"peerIdentity",{enumerable:!0,value:Promise.resolve({idp:"",name:""})}),f&&(h.prototype.addTrack=function(){var track=arguments[0],e=this._peerConnection.addTrack.apply(this._peerConnection,arguments);return e.replaceTrack(track),e}),h.prototype.createAnswer=function(){var e,t=[].slice.call(arguments),n=this;return e=this._peerConnection.createAnswer().then((function(e){return _(n,e),m(e,n._initiallyNegotiatedDtlsRole)})),"function"==typeof t[0]?d.legacyPromise(e,t[0],t[1]):e},h.prototype.createOffer=function(){var e,t=[].slice.call(arguments),n=(t.length>1?t[2]:t[0])||{},r=this;if("have-local-offer"===this.signalingState||"have-remote-offer"===this.signalingState){var c="have-local-offer"===this.signalingState;e=v(this,c,(function(){return r.createOffer(n)}))}else e=r._peerConnection.createOffer(n);return e=e.then((function(e){return new o({type:e.type,sdp:l(r._tracksToSSRCs,e.sdp)})})),t.length>1?d.legacyPromise(e,t[0],t[1]):e},h.prototype.setLocalDescription=function(){var e,t=[].slice.call(arguments),n=t[0];return n&&"answer"===n.type&&"have-local-offer"===this.signalingState&&(e=Promise.reject(new Error("Cannot set local answer in state have-local-offer"))),e?t.length>1?d.legacyPromise(e,t[1],t[2]):e:this._peerConnection.setLocalDescription.apply(this._peerConnection,t)},h.prototype.setRemoteDescription=function(){var e,t=[].slice.call(arguments),n=t[0],r=this;return n&&"have-remote-offer"===this.signalingState&&("answer"===n.type?e=Promise.reject(new Error("Cannot set remote answer in state have-remote-offer")):"offer"===n.type&&(e=v(this,!1,(function(){return r._peerConnection.setRemoteDescription(n)})))),e||(e=this._peerConnection.setRemoteDescription(n)),e=e.then((function(){_(r,n,!0)})),t.length>1?d.legacyPromise(e,t[1],t[2]):e},h.prototype.close=function(){"closed"!==this.signalingState&&(this._isClosed=!0,this._peerConnection.close())},d.delegateMethods(RTCPeerConnection.prototype,h.prototype,"_peerConnection"),e.exports=h},2086:function(e,t,n){"use strict";var r=n(1816),o=n(1521).inherits,c=n(1867),l=n(1694),d=n(1439),f="unified"===l.getSdpFormat(),h=f?l.updateUnifiedPlanTrackIdsToSSRCs:l.updatePlanBTrackIdsToSSRCs;function v(e){if(!(this instanceof v))return new v(e);r.call(this),d.interceptEvent(this,"datachannel"),d.interceptEvent(this,"iceconnectionstatechange"),d.interceptEvent(this,"signalingstatechange"),d.interceptEvent(this,"track");var t=new RTCPeerConnection(e);Object.defineProperties(this,{_appliedTracksToSSRCs:{value:new Map,writable:!0},_audioTransceiver:{value:null,writable:!0},_isClosed:{value:!1,writable:!0},_peerConnection:{value:t},_pendingLocalOffer:{value:null,writable:!0},_pendingRemoteOffer:{value:null,writable:!0},_rolledBackTracksToSSRCs:{value:new Map,writable:!0},_signalingStateLatch:{value:new c},_tracksToSSRCs:{value:new Map,writable:!0},_videoTransceiver:{value:null,writable:!0},localDescription:{enumerable:!0,get:function(){return this._pendingLocalOffer||this._peerConnection.localDescription}},iceConnectionState:{enumerable:!0,get:function(){return this._isClosed?"closed":this._peerConnection.iceConnectionState}},iceGatheringState:{enumerable:!0,get:function(){return this._isClosed?"complete":this._peerConnection.iceGatheringState}},remoteDescription:{enumerable:!0,get:function(){return this._pendingRemoteOffer||this._peerConnection.remoteDescription}},signalingState:{enumerable:!0,get:function(){return this._isClosed?"closed":this._pendingLocalOffer?"have-local-offer":this._pendingRemoteOffer?"have-remote-offer":this._peerConnection.signalingState}}});var n=this;t.addEventListener("datachannel",(function(e){y(e.channel),n.dispatchEvent(e)})),t.addEventListener("iceconnectionstatechange",(function(){n._isClosed||n.dispatchEvent.apply(n,arguments)})),t.addEventListener("signalingstatechange",(function(){n._isClosed||("stable"===t.signalingState&&(n._appliedTracksToSSRCs=new Map(n._tracksToSSRCs)),n._pendingLocalOffer||n._pendingRemoteOffer||n.dispatchEvent.apply(n,arguments))})),t.addEventListener("track",(function(e){n._pendingRemoteOffer=null,n.dispatchEvent(e)})),d.proxyProperties(RTCPeerConnection.prototype,this,t)}function _(e,t,n){var r,o=t?e._pendingLocalOffer:e._pendingRemoteOffer,c=t?e._pendingRemoteOffer:e._pendingLocalOffer,l=t?"have-local-offer":"have-remote-offer",d=t?"setLocalDescription":"setRemoteDescription";if(!t&&c&&"answer"===n.type)return function(e,t){var n=e._pendingLocalOffer;return e._peerConnection.setLocalDescription(n).then((function(){return e._pendingLocalOffer=null,e.setRemoteDescription(t)})).then((function(){e._signalingStateLatch.lower()}))}(e,n);if("offer"===n.type){if(e.signalingState!==l&&"stable"!==e.signalingState)return Promise.reject(new Error("Cannot set "+(t?"local":"remote")+" offer in state "+e.signalingState));o||"low"!==e._signalingStateLatch.state||e._signalingStateLatch.raise();var f=e.signalingState;return r=n,t?e._pendingLocalOffer=r:e._pendingRemoteOffer=r,e.signalingState!==f?Promise.resolve().then((function(){e.dispatchEvent(new Event("signalingstatechange"))})):Promise.resolve()}return"rollback"===n.type?e.signalingState!==l?Promise.reject(new Error("Cannot rollback "+(t?"local":"remote")+" description in "+e.signalingState)):(t?e._pendingLocalOffer=null:e._pendingRemoteOffer=null,e._rolledBackTracksToSSRCs=new Map(e._tracksToSSRCs),e._tracksToSSRCs=new Map(e._appliedTracksToSSRCs),Promise.resolve().then((function(){e.dispatchEvent(new Event("signalingstatechange"))}))):e._peerConnection[d](n)}function m(e,t){return!!e.getTransceivers().find((function(e){return e.receiver&&e.receiver.track&&e.receiver.track.kind===t}))}function y(e){return Object.defineProperties(e,{maxPacketLifeTime:{value:65535===e.maxPacketLifeTime?null:e.maxPacketLifeTime},maxRetransmits:{value:65535===e.maxRetransmits?null:e.maxRetransmits}})}o(v,r),v.prototype.addIceCandidate=function(e){var t=this;return"have-remote-offer"===this.signalingState?this._signalingStateLatch.when("low").then((function(){return t._peerConnection.addIceCandidate(e)})):this._peerConnection.addIceCandidate(e)},v.prototype.createOffer=function(e){e=Object.assign({},e);var t=this;if(e.offerToReceiveVideo&&!this._audioTransceiver&&(!f||!m(this,"audio"))){delete e.offerToReceiveAudio;try{this._audioTransceiver=f?this.addTransceiver("audio",{direction:"recvonly"}):this.addTransceiver("audio")}catch(e){return Promise.reject(e)}}if(e.offerToReceiveVideo&&!this._videoTransceiver&&(!f||!m(this,"video"))){delete e.offerToReceiveVideo;try{this._videoTransceiver=f?this.addTransceiver("video",{direction:"recvonly"}):this.addTransceiver("video")}catch(e){return Promise.reject(e)}}return this._peerConnection.createOffer(e).then((function(e){return t._rolledBackTracksToSSRCs.clear(),new RTCSessionDescription({type:e.type,sdp:h(t._tracksToSSRCs,e.sdp)})}))},v.prototype.createAnswer=function(e){var t=this;return this._pendingRemoteOffer?this._peerConnection.setRemoteDescription(this._pendingRemoteOffer).then((function(){return t._signalingStateLatch.lower(),t._peerConnection.createAnswer()})).then((function(e){return t._pendingRemoteOffer=null,t._rolledBackTracksToSSRCs.clear(),f?new RTCSessionDescription({type:e.type,sdp:h(t._tracksToSSRCs,e.sdp)}):e}),(function(e){throw t._pendingRemoteOffer=null,e})):this._peerConnection.createAnswer(e).then((function(e){return t._rolledBackTracksToSSRCs.clear(),f?new RTCSessionDescription({type:e.type,sdp:h(t._tracksToSSRCs,e.sdp)}):e}))},v.prototype.createDataChannel=function(label,e){var t=this._peerConnection.createDataChannel(label,e);return y(t),t},v.prototype.removeTrack=function(e){e.replaceTrack(null),this._peerConnection.removeTrack(e)},v.prototype.setLocalDescription=function(e){return this._rolledBackTracksToSSRCs.size>0&&(this._tracksToSSRCs=new Map(this._rolledBackTracksToSSRCs),this._rolledBackTracksToSSRCs.clear()),_(this,!0,e)},v.prototype.setRemoteDescription=function(e){return this._rolledBackTracksToSSRCs.clear(),_(this,!1,e)},v.prototype.close=function(){if(!this._isClosed){this._isClosed=!0,this._peerConnection.close();var e=this;setTimeout((function(){e.dispatchEvent(new Event("iceconnectionstatechange")),e.dispatchEvent(new Event("signalingstatechange"))}))}},d.delegateMethods(RTCPeerConnection.prototype,v.prototype,"_peerConnection"),e.exports=v},2087:function(e,t,n){"use strict";if("function"==typeof RTCSessionDescription)switch((0,n(1439).guessBrowser)()){case"chrome":e.exports=n(1866);break;case"firefox":e.exports=n(1868);break;default:e.exports=RTCSessionDescription}else e.exports=function(){throw new Error("RTCSessionDescription is not supported")}},2088:function(e,t,n){"use strict";var r=n(1521).inherits,o=n(2089);function c(e,t){var track=new o(e,t);return Object.setPrototypeOf(track,c.prototype),track}r(c,o),e.exports=c},2089:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1869),l=function(e){function t(t,n){return e.call(this,t,n)||this}return o(t,e),t.prototype.toString=function(){return"[LocalAudioTrack #"+this._instanceId+": "+this.id+"]"},t.prototype.attach=function(t){return(t=e.prototype.attach.call(this,t)).muted=!0,t},t.prototype._end=function(){return e.prototype._end.apply(this,arguments)},t.prototype.disable=function(){return e.prototype.disable.apply(this,arguments)},t.prototype.enable=function(){return e.prototype.enable.apply(this,arguments)},t.prototype.restart=function(){return e.prototype.restart.apply(this,arguments)},t.prototype.stop=function(){return e.prototype.stop.apply(this,arguments)},t}(n(1873)(c));e.exports=l},2090:function(e,t,n){"use strict";var r=n(1874);e.exports=function(e){var t=n(1695),o={},c=t.getOrCreate(o),l=3;return function t(){return l--,r(c,e.srcObject,250).then((function(e){return!!e&&(!(l>0)||t())})).catch((function(){return!0}))}().finally((function(){t.release(o)}))}},2091:function(e,t,n){"use strict";var r=n(1521).inherits,o=n(2092);function c(e,t){var track=new o(e,t);return Object.setPrototypeOf(track,c.prototype),track}r(c,o),e.exports=c},2092:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1439),l=c.guessBrowser,d=c.isIOSChrome,f=n(1875),h=n(1873),v=n(1880),_=n(1410).isUserMediaTrack,m=function(e){function t(t,n){var r=this;return n=Object.assign({workaroundSilentLocalVideo:("safari"===l()||d())&&_(t)&&"undefined"!=typeof document&&"function"==typeof document.createElement},n),r=e.call(this,t,n)||this,Object.defineProperties(r,{_workaroundSilentLocalVideo:{value:n.workaroundSilentLocalVideo?y:null},_workaroundSilentLocalVideoCleanup:{value:null,writable:!0}}),r._workaroundSilentLocalVideo&&(r._workaroundSilentLocalVideoCleanup=r._workaroundSilentLocalVideo(r,document)),r}return o(t,e),t.prototype.toString=function(){return"[LocalVideoTrack #"+this._instanceId+": "+this.id+"]"},t.prototype._checkIfCanCaptureFrames=function(){return e.prototype._checkIfCanCaptureFrames.call(this,this._trackSender.isPublishing)},t.prototype._end=function(){return e.prototype._end.apply(this,arguments)},t.prototype._setSenderMediaStreamTrack=function(e){var t=this,n=this.mediaStreamTrack,r=e?this.processedTrack:n;return this._trackSender.setMediaStreamTrack(r).catch((function(e){return t._log.warn("setMediaStreamTrack failed on LocalVideoTrack RTCRtpSender",{error:e,mediaStreamTrack:r})})).then((function(){t._unprocessedTrack=e?n:null}))},t.prototype.addProcessor=function(){this._log.debug("Adding VideoProcessor to the LocalVideoTrack");var t=e.prototype.addProcessor.apply(this,arguments);return this.processedTrack?(this._log.debug("Updating LocalVideoTrack's MediaStreamTrack with the processed MediaStreamTrack",this.processedTrack),this._setSenderMediaStreamTrack(!0),t):this._log.warn("Unable to add a VideoProcessor to the LocalVideoTrack")},t.prototype.removeProcessor=function(){var t=this;this._log.debug("Removing VideoProcessor from the LocalVideoTrack");var n=e.prototype.removeProcessor.apply(this,arguments);return this._log.debug("Updating LocalVideoTrack's MediaStreamTrack with the original MediaStreamTrack"),this._setSenderMediaStreamTrack().then((function(){return t._updateElementsMediaStreamTrack()})),n},t.prototype.disable=function(){var t=e.prototype.disable.apply(this,arguments);return this.processedTrack&&(this.processedTrack.enabled=!1),t},t.prototype.enable=function(t){void 0===t&&(t=!0);var n=e.prototype.enable.apply(this,arguments);return this.processedTrack&&(this.processedTrack.enabled=t,t&&(this._captureFrames(),this._log.debug("Updating LocalVideoTrack's MediaStreamTrack with the processed MediaStreamTrack",this.processedTrack),this._setSenderMediaStreamTrack(!0))),n},t.prototype.restart=function(){var t=this;this._workaroundSilentLocalVideoCleanup&&(this._workaroundSilentLocalVideoCleanup(),this._workaroundSilentLocalVideoCleanup=null);var n=e.prototype.restart.apply(this,arguments);return this.processor&&n.then((function(){t._restartProcessor()})),this._workaroundSilentLocalVideo&&n.finally((function(){t._workaroundSilentLocalVideoCleanup=t._workaroundSilentLocalVideo(t,document)})),n},t.prototype.stop=function(){return this._workaroundSilentLocalVideoCleanup&&(this._workaroundSilentLocalVideoCleanup(),this._workaroundSilentLocalVideoCleanup=null),e.prototype.stop.apply(this,arguments)},t}(h(v));function y(e,t){var n=e._log,r=e._dummyEl,o=e.mediaStreamTrack;function c(){e.isEnabled&&(n.info("Unmuted, checking silence"),r.play().then((function(){return f(r,t)})).then((function(t){if(t)return n.warn("Silence detected, restarting"),e._stop(),e._restart();n.info("Non-silent frames detected, so no need to restart")})).catch((function(e){n.warn("Failed to detect silence and restart:",e)})).finally((function(){(r=e._dummyEl).paused||r.pause(),o.removeEventListener("unmute",c),(o=e.mediaStreamTrack).addEventListener("unmute",c)})))}return o.addEventListener("unmute",c),function(){o.removeEventListener("unmute",c)}}e.exports=m},2093:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(373).EventEmitter,l=n(1425).DEFAULT_VIDEO_PROCESSOR_STATS_INTERVAL_MS,d=function(e){function t(t){var n=e.call(this)||this;return Object.defineProperties(n,{_lastStatsSaveTime:{value:null,writable:!0},_lastStatsPublishTime:{value:null,writable:!0},_log:{value:t},_processorInfo:{value:null,writable:!0},_stats:{value:null,writable:!0}}),n.on("add",(function(e){n._lastStatsSaveTime=Date.now(),n._lastStatsPublishTime=Date.now(),n._processorInfo=e,n._stats=[],n._reemitEvent("add",n._getEventData())})),n.on("remove",(function(){var data=n._getEventData();n._lastStatsSaveTime=null,n._lastStatsPublishTime=null,n._processorInfo=null,n._stats=null,n._reemitEvent("remove",data)})),n.on("start",(function(){n._reemitEvent("start",n._getEventData())})),n.on("stop",(function(e){n._reemitEvent("stop",Object.assign({message:e},n._getEventData()))})),n.on("stats",(function(){return n._maybeEmitStats()})),n}return o(t,e),t.prototype._getEventData=function(){if(!this._processorInfo)return{};var e=this._processorInfo,t=e.processor,data={captureHeight:e.captureHeight,captureWidth:e.captureWidth,inputFrameRate:e.inputFrameRate,isRemoteVideoTrack:e.isRemoteVideoTrack};return data.name=t._name||"VideoProcessor",["assetsPath","blurFilterRadius","fitType","isSimdEnabled","maskBlurRadius","version"].forEach((function(e){var n=t["_"+e];void 0!==n&&(data[e]=n)})),Object.keys(data).forEach((function(e){var t=data[e];"boolean"==typeof t&&(data[e]=t?"true":"false")})),data},t.prototype._maybeEmitStats=function(){if(this._stats&&this._processorInfo){var e=this._processorInfo.processor._benchmark;if(e){var t=Date.now();if(!(t-this._lastStatsSaveTime<1e3)){var n={outputFrameRate:e.getRate("totalProcessingDelay")};if(["captureFrameDelay","imageCompositionDelay","inputImageResizeDelay","processFrameDelay","segmentationDelay"].forEach((function(t){n[t]=e.getAverageDelay(t)})),this._lastStatsSaveTime=t,this._stats.push(n),!(t-this._lastStatsPublishTime<l)){this._lastStatsPublishTime=t;var r=this._stats.splice(0).reduce((function(e,t,r){return Object.keys(n).forEach((function(n){e[n]||(e[n]=0),e[n]=(e[n]*r+t[n])/(r+1)})),e}),{});Object.keys(r).forEach((function(e){r[e]=parseFloat(r[e].toFixed(2))})),this._reemitEvent("stats",Object.assign({},r,this._getEventData()))}}}}},t.prototype._reemitEvent=function(e,data){this._log.debug("VideoProcessor:"+e,data),this.emit("event",{name:e,data:data})},t}(c);e.exports=d},2094:function(e,t,n){"use strict";var r=n(1521).inherits,o=n(2095);function c(e){var track=new o(e);return Object.setPrototypeOf(track,c.prototype),track}r(c,o),e.exports=c},2095:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1817),l=n(2096),d=function(e){function t(t){var n,r=new(0,(t=Object.assign({DataTrackSender:l,maxPacketLifeTime:null,maxRetransmits:null,ordered:!0},t)).DataTrackSender)(t.maxPacketLifeTime,t.maxRetransmits,t.ordered);return n=e.call(this,r.id,"data",t)||this,Object.defineProperties(n,{_trackSender:{value:r},id:{enumerable:!0,value:r.id},maxPacketLifeTime:{enumerable:!0,value:t.maxPacketLifeTime},maxRetransmits:{enumerable:!0,value:t.maxRetransmits},ordered:{enumerable:!0,value:t.ordered},reliable:{enumerable:!0,value:null===t.maxPacketLifeTime&&null===t.maxRetransmits}}),n}return o(t,e),t.prototype.send=function(data){this._trackSender.send(data)},t}(c);e.exports=d},2096:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1881),l=n(1410).makeUUID,d=function(e){function t(t,n,r){var o=e.call(this,l(),t,n,r)||this;return Object.defineProperties(o,{_clones:{value:new Set},_dataChannels:{value:new Set}}),o}return o(t,e),t.prototype._addClone=function(e){this._clones.add(e)},t.prototype.removeClone=function(e){this._clones.delete(e)},t.prototype.addDataChannel=function(e){return this._dataChannels.add(e),this},t.prototype.clone=function(){var e=this,n=new t(this.maxPacketLifeTime,this.maxRetransmits,this.ordered);return this._addClone(n),n.once("stopped",(function(){return e.removeClone(n)})),n},t.prototype.removeDataChannel=function(e){return this._dataChannels.delete(e),this},t.prototype.send=function(data){return this._dataChannels.forEach((function(e){try{e.send(data)}catch(e){}})),this._clones.forEach((function(e){try{e.send(data)}catch(e){}})),this},t.prototype.stop=function(){this._dataChannels.forEach((function(e){return e.close()})),this._clones.forEach((function(e){return e.stop()})),e.prototype.stop.call(this)},t}(c);e.exports=d},2097:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__assign||function(){return(c=Object.assign||function(e){for(var s,i=1,t=arguments.length;i<t;i++)for(var p in s=arguments[i])Object.prototype.hasOwnProperty.call(s,p)&&(e[p]=s[p]);return e}).apply(this,arguments)},l=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,c){function l(e){try{f(r.next(e))}catch(e){c(e)}}function d(e){try{f(r.throw(e))}catch(e){c(e)}}function f(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(l,d)}f((r=r.apply(e,t||[])).next())}))},d=this&&this.__generator||function(e,body){var t,n,r,g,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return g={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(g[Symbol.iterator]=function(){return this}),g;function c(c){return function(l){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;o;)try{if(t=1,n&&(r=2&c[0]?n.return:c[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,c[1])).done)return r;switch(n=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,n=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=body.call(e,o)}catch(e){c=[6,e],n=0}finally{t=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,l])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.runPreflight=t.PreflightTest=void 0;var f=n(1425),h=n(2098),v=n(2099),_=n(2100),m=n(2101),y=n(2104),w=n(2105),k=n(2106),S=n(1410),T=n(1425).WS_SERVER,O=n(1570),P=n(1684),C=n(1884),E=n(1885),R=n(1886),j=n(1864),x=j.createSID,L=j.sessionSID,A=n(1571),D=A.SignalingConnectionTimeoutError,I=A.MediaConnectionError,M="mediaAcquired",N="connected",V="mediaSubscribed",F="mediaStarted",B="dtlsConnected",U="peerConnectionConnected",H="iceConnected";function W(e){return null!=e}var Q=0,G=function(e){function t(t,n){var r=e.call(this)||this;r._testTiming=new h.Timer,r._dtlsTiming=new h.Timer,r._iceTiming=new h.Timer,r._peerConnectionTiming=new h.Timer,r._mediaTiming=new h.Timer,r._connectTiming=new h.Timer,r._sentBytesMovingAverage=new C,r._packetLossMovingAverage=new C,r._progressEvents=[],r._receivedBytesMovingAverage=new C;var o=n,c=o.environment,l=void 0===c?"prod":c,d=o.region,v=void 0===d?"gll":d,_=o.duration,m=void 0===_?1e4:_,y=o.wsServer||T(l,v);return r._log=new O("default",r,f.DEFAULT_LOG_LEVEL,f.DEFAULT_LOGGER_NAME),r._testDuration=m,r._instanceId=Q++,r._testTiming.start(),r._runPreflightTest(t,l,y),r}return o(t,e),t.prototype.toString=function(){return"[Preflight #"+this._instanceId+"]"},t.prototype.stop=function(){this._stopped=!0},t.prototype._generatePreflightReport=function(e){return this._testTiming.stop(),{testTiming:this._testTiming.getTimeMeasurement(),networkTiming:{dtls:this._dtlsTiming.getTimeMeasurement(),ice:this._iceTiming.getTimeMeasurement(),peerConnection:this._peerConnectionTiming.getTimeMeasurement(),connect:this._connectTiming.getTimeMeasurement(),media:this._mediaTiming.getTimeMeasurement()},stats:{jitter:y.makeStat(null==e?void 0:e.jitter),rtt:y.makeStat(null==e?void 0:e.rtt),packetLoss:y.makeStat(null==e?void 0:e.packetLoss)},selectedIceCandidatePairStats:e?e.selectedIceCandidatePairStats:null,iceCandidateStats:e?e.iceCandidateStats:[],progressEvents:this._progressEvents,mos:y.makeStat(null==e?void 0:e.mos)}},t.prototype._executePreflightStep=function(e,t,n){return l(this,void 0,void 0,(function(){var r,o,c,l;return d(this,(function(d){switch(d.label){case 0:if(this._log.debug("Executing step: ",e),r=this._testDuration+1e4,this._stopped)throw new Error("stopped");o=Promise.resolve().then(t),c=null,l=new Promise((function(t,o){c=setTimeout((function(){o(n||new Error(e+" timeout."))}),r)})),d.label=1;case 1:return d.trys.push([1,,3,4]),[4,Promise.race([l,o])];case 2:return[2,d.sent()];case 3:return null!==c&&clearTimeout(c),[7];case 4:return[2]}}))}))},t.prototype._collectNetworkTimings=function(e){var t=this;return new Promise((function(n){var r;e.addEventListener("iceconnectionstatechange",(function(){"checking"===e.iceConnectionState&&t._iceTiming.start(),"connected"===e.iceConnectionState&&(t._iceTiming.stop(),t._updateProgress(H),(!r||r&&"connected"===r.state)&&n())})),e.addEventListener("connectionstatechange",(function(){"connecting"===e.connectionState&&t._peerConnectionTiming.start(),"connected"===e.connectionState&&(t._peerConnectionTiming.stop(),t._updateProgress(U))}));var o=e.getSenders().map((function(e){return e.transport})).find(W);void 0!==o&&(r=o).addEventListener("statechange",(function(){"connecting"===r.state&&t._dtlsTiming.start(),"connected"===r.state&&(t._dtlsTiming.stop(),t._updateProgress(B),"connected"===e.iceConnectionState&&n())}))}))},t.prototype._setupInsights=function(e){var t=e.token,n=e.environment,r=void 0===n?f.DEFAULT_ENVIRONMENT:n,o=e.realm,c=void 0===o?f.DEFAULT_REALM:o,l=new R(t,f.SDK_NAME,f.SDK_VERSION,r,c,{});l.connect("PREFLIGHT_ROOM_SID","PREFLIGHT_PARTICIPANT");var d=new E(l,Date.now(),this._log),h=void 0;return{reportToInsights:function(e){var t,n,r=e.report,o=r.stats.jitter||h,c=r.stats.rtt||h,f=r.stats.packetLoss||h,v=r.mos||h,_=new Map;r.iceCandidateStats.forEach((function(e){if(e.candidateType&&e.protocol){var t=_.get(e.candidateType)||[];t.indexOf(e.protocol)<0&&t.push(e.protocol),_.set(e.candidateType,t)}}));var m=JSON.stringify(Object.fromEntries(_)),y={name:"report",group:"preflight",level:r.error?"error":"info",payload:{sessionSID:L,preflightSID:x("PF"),progressEvents:JSON.stringify(r.progressEvents),testTiming:r.testTiming,dtlsTiming:r.networkTiming.dtls,iceTiming:r.networkTiming.ice,peerConnectionTiming:r.networkTiming.peerConnection,connectTiming:r.networkTiming.connect,mediaTiming:r.networkTiming.media,selectedLocalCandidate:null===(t=r.selectedIceCandidatePairStats)||void 0===t?void 0:t.localCandidate,selectedRemoteCandidate:null===(n=r.selectedIceCandidatePairStats)||void 0===n?void 0:n.remoteCandidate,iceCandidateStats:m,jitterStats:o,rttStats:c,packetLossStats:f,mosStats:v,error:r.error}};d.emit("event",y),setTimeout((function(){return l.disconnect()}),2e3)}}},t.prototype._runPreflightTest=function(e,t,n){return l(this,void 0,void 0,(function(){var r,o,f,h,v,_,y,S,T,O,P,C,E=this;return d(this,(function(R){switch(R.label){case 0:r=[],o=[],f=this._setupInsights({token:e,environment:t}).reportToInsights,R.label=1;case 1:return R.trys.push([1,8,9,10]),h=[],[4,this._executePreflightStep("Acquire media",(function(){return[w.syntheticAudio(),k.syntheticVideo({width:640,height:480})]}))];case 2:return r=R.sent(),this._updateProgress(M),this.emit("debug",{localTracks:r}),this._connectTiming.start(),[4,this._executePreflightStep("Get turn credentials",(function(){return m.getTurnCredentials(e,n)}),new D)];case 3:return v=R.sent(),this._connectTiming.stop(),this._updateProgress(N),_=new RTCPeerConnection({iceServers:v,iceTransportPolicy:"relay",bundlePolicy:"max-bundle"}),y=new RTCPeerConnection({iceServers:v,bundlePolicy:"max-bundle"}),o.push(_),o.push(y),this._mediaTiming.start(),[4,this._executePreflightStep("Setup Peer Connections",(function(){return l(E,void 0,void 0,(function(){var e,t,n,o;return d(this,(function(c){switch(c.label){case 0:return _.addEventListener("icecandidate",(function(e){return e.candidate&&y.addIceCandidate(e.candidate)})),y.addEventListener("icecandidate",(function(e){return e.candidate&&_.addIceCandidate(e.candidate)})),r.forEach((function(track){return _.addTrack(track)})),e=new Promise((function(e){var t=[];y.addEventListener("track",(function(n){t.push(n.track),t.length===r.length&&e(t)}))})),[4,_.createOffer()];case 1:return t=c.sent(),n=t,[4,_.setLocalDescription(n)];case 2:return c.sent(),[4,y.setRemoteDescription(n)];case 3:return c.sent(),[4,y.createAnswer()];case 4:return o=c.sent(),[4,y.setLocalDescription(o)];case 5:return c.sent(),[4,_.setRemoteDescription(o)];case 6:return c.sent(),[4,this._collectNetworkTimings(_)];case 7:return c.sent(),[2,e]}}))}))}),new I)];case 4:return S=R.sent(),this.emit("debug",{remoteTracks:S}),S.forEach((function(track){track.addEventListener("ended",(function(){return E._log.warn(track.kind+":ended")})),track.addEventListener("mute",(function(){return E._log.warn(track.kind+":muted")})),track.addEventListener("unmute",(function(){return E._log.warn(track.kind+":unmuted")}))})),this._updateProgress(V),[4,this._executePreflightStep("Wait for tracks to start",(function(){return new Promise((function(e){var element=document.createElement("video");element.autoplay=!0,element.playsInline=!0,element.muted=!0,element.srcObject=new MediaStream(S),h.push(element),E.emit("debugElement",element),element.oncanplay=e}))}),new I)];case 5:return R.sent(),this._mediaTiming.stop(),this._updateProgress(F),[4,this._executePreflightStep("Collect stats for duration",(function(){return E._collectRTCStatsForDuration(E._testDuration,{mos:[],jitter:[],rtt:[],outgoingBitrate:[],incomingBitrate:[],packetLoss:[],selectedIceCandidatePairStats:null,iceCandidateStats:[]},_,y)}))];case 6:return T=R.sent(),[4,this._executePreflightStep("Generate report",(function(){return E._generatePreflightReport(T)}))];case 7:return O=R.sent(),f({report:O}),this.emit("completed",O),[3,10];case 8:return P=R.sent(),C=this._generatePreflightReport(),f({report:c(c({},C),{error:null==P?void 0:P.toString()})}),this.emit("failed",P,C),[3,10];case 9:return o.forEach((function(e){return e.close()})),r.forEach((function(track){return track.stop()})),[7];case 10:return[2]}}))}))},t.prototype._collectRTCStats=function(e,t,n){return l(this,void 0,void 0,(function(){var r,o,c,l,f,h,m,y,w,k,S,T,O,P;return d(this,(function(d){switch(d.label){case 0:return[4,_.getCombinedConnectionStats({publisher:t,subscriber:n})];case 1:return r=d.sent(),o=r.timestamp,c=r.bytesSent,l=r.bytesReceived,f=r.packets,h=r.packetsLost,m=r.roundTripTime,y=r.jitter,w=r.selectedIceCandidatePairStats,k=r.iceCandidateStats,S=e.jitter.length>0,e.jitter.push(y),e.rtt.push(m),this._sentBytesMovingAverage.putSample(c,o),this._receivedBytesMovingAverage.putSample(l,o),this._packetLossMovingAverage.putSample(h,f),S&&(e.outgoingBitrate.push(1e3*this._sentBytesMovingAverage.get()*8),e.incomingBitrate.push(1e3*this._receivedBytesMovingAverage.get()*8),T=this._packetLossMovingAverage.get(),O=Math.min(100,100*T),e.packetLoss.push(O),P=v.calculateMOS(m,y,T),e.mos.push(P)),e.selectedIceCandidatePairStats||(e.selectedIceCandidatePairStats=w),0===e.iceCandidateStats.length&&(e.iceCandidateStats=k),[2]}}))}))},t.prototype._collectRTCStatsForDuration=function(e,t,n,r){return l(this,void 0,void 0,(function(){var o,c,l;return d(this,(function(d){switch(d.label){case 0:return o=Date.now(),c=Math.min(1e3,e),[4,S.waitForSometime(c)];case 1:return d.sent(),[4,this._collectRTCStats(t,n,r)];case 2:return d.sent(),(l=e-(Date.now()-o))>0?[4,this._collectRTCStatsForDuration(l,t,n,r)]:[3,4];case 3:t=d.sent(),d.label=4;case 4:return[2,t]}}))}))},t.prototype._updateProgress=function(e){var t=Date.now()-this._testTiming.getTimeMeasurement().start;this._progressEvents.push({duration:t,name:e}),this.emit("progress",e)},t}(P);t.PreflightTest=G,t.runPreflight=function(e,t){return void 0===t&&(t={}),new G(e,t)}},2098:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Timer=void 0;var r=function(){function e(){this._end=void 0,this.start()}return e.prototype.start=function(){return this._start=Date.now(),this},e.prototype.stop=function(){return this._end=Date.now(),this},e.prototype.getTimeMeasurement=function(){return{start:this._start,end:this._end,duration:void 0===this._end?void 0:this._end-this._start}},e}();t.Timer=r},2099:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.mosToScore=t.calculateMOS=void 0;var r=94.768;t.calculateMOS=function(e,t,n){var o=e+2*t+10,c=0;switch(!0){case o<160:c=r-o/40;break;case o<1e3:c=r-(o-120)/10}switch(!0){case n<=c/2.5:c=Math.max(c-2.5*n,6.52);break;default:c=0}return 1+.035*c+7e-6*c*(c-60)*(100-c)},t.mosToScore=function(e){return e?e>4.2?5:e>4?4:e>3.6?3:e>3?2:1:0}},2100:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,c){function l(e){try{f(r.next(e))}catch(e){c(e)}}function d(e){try{f(r.throw(e))}catch(e){c(e)}}function f(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(l,d)}f((r=r.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,body){var t,n,r,g,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return g={next:c(0),throw:c(1),return:c(2)},"function"==typeof Symbol&&(g[Symbol.iterator]=function(){return this}),g;function c(c){return function(l){return function(c){if(t)throw new TypeError("Generator is already executing.");for(;o;)try{if(t=1,n&&(r=2&c[0]?n.return:c[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,c[1])).done)return r;switch(n=0,r&&(c=[2&c[0],r.value]),c[0]){case 0:case 1:r=c;break;case 4:return o.label++,{value:c[1],done:!1};case 5:o.label++,n=c[1],c=[0];continue;case 7:c=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==c[0]&&2!==c[0])){o=0;continue}if(3===c[0]&&(!r||c[1]>r[0]&&c[1]<r[3])){o.label=c[1];break}if(6===c[0]&&o.label<r[1]){o.label=r[1],r=c;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(c);break}r[2]&&o.ops.pop(),o.trys.pop();continue}c=body.call(e,o)}catch(e){c=[6,e],n=0}finally{t=r=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}([c,l])}}},c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c};function l(e,t,n,r){var o=[];return e.forEach((function(e){0!==r.length&&!r.includes(e.type)||0!==n.length&&!n.includes(e.kind)||"number"!=typeof e[t]||o.push(e[t])})),o}function d(input){return[{key:"transportId",type:"string"},{key:"candidateType",type:"string"},{key:"port",altKeys:["portNumber"],type:"number"},{key:"address",altKeys:["ip","ipAddress"],type:"string"},{key:"priority",type:"number"},{key:"protocol",altKeys:["transport"],type:"string"},{key:"url",type:"string"},{key:"relayProtocol",type:"string"}].reduce((function(e,t){var n=[t.key];t.altKeys&&(n=n.concat(t.altKeys));var r=n.find((function(e){return e in input}));return r&&typeof input[r]===t.type&&(e[t.key]=input[r]),e}),{})}Object.defineProperty(t,"__esModule",{value:!0}),t.getCombinedConnectionStats=void 0,t.getCombinedConnectionStats=function(e){var t=e.publisher,n=e.subscriber;return r(this,void 0,void 0,(function(){var e,r,f,h,v,_,m,y,w,k,S,T,O,P,C;return o(this,(function(o){switch(o.label){case 0:return[4,Promise.all([t,n].map((function(e){return e.getStats()})))];case 1:return e=c.apply(void 0,[o.sent(),2]),r=e[0],f=e[1],h=l(f,"timestamp",["audio"],["inbound-rtp"]),v=h.length>0?h[0]:0,_=l(f,"jitter",["audio"],["inbound-rtp"]).reduce((function(a,b){return Math.max(a,b)}),0),m=l(f,"packetsReceived",["audio","video"],["inbound-rtp"]).reduce((function(a,b){return a+b}),0),y=l(f,"packetsLost",["audio","video"],["inbound-rtp"]).reduce((function(a,b){return a+b}),0),w=l(r,"roundTripTime",["audio","video"],["remote-inbound-rtp"]).reduce((function(a,b){return Math.max(a,b)}),0),k=l(f,"currentRoundTripTime",[],["candidate-pair"]).reduce((function(a,b){return Math.max(a,b)}),0),S=1e3*(k||w),T=l(r,"bytesSent",[],["candidate-pair"]).reduce((function(a,b){return a+b}),0),O=l(f,"bytesReceived",[],["candidate-pair"]).reduce((function(a,b){return a+b}),0),P=function(e){var t=null,n=[];e.forEach((function(e){"transport"===e.type&&e.selectedCandidatePairId?t=e.selectedCandidatePairId:"candidate-pair"===e.type&&n.push(e)}));var r=n.find((function(e){return e.selected||t&&e.id===t}));if(!r)return null;var o=r,c=e.get(o.localCandidateId),l=e.get(o.remoteCandidateId);if(!c||!l)return null;return{localCandidate:d(c),remoteCandidate:d(l)}}(f),C=[],f.forEach((function(e){"local-candidate"!==e.type&&"remote-candidate"!==e.type||C.push(d(e))})),[2,{timestamp:v,jitter:_,packets:m,packetsLost:y,roundTripTime:S,bytesSent:T,bytesReceived:O,selectedIceCandidatePairStats:P,iceCandidateStats:C}]}}))}))}},2101:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getTurnCredentials=void 0;var r=n(1882),o=n(1425).ICE_VERSION,c=n(1571),l=c.createTwilioError,d=c.SignalingConnectionError,f=n(373);t.getTurnCredentials=function(e,t){return new Promise((function(n,c){var h=new f.EventEmitter,v=new r(t,{networkMonitor:null,eventObserver:h,helloBody:{edge:"roaming",preflight:!0,token:e,type:"ice",version:o}}),_=!1;v.once("close",(function(){_||(_=!0,c(new d))})),v.on("message",(function(e){var code=e.code,t=e.message,r=e.ice_servers,o=e.type;"iced"!==o&&"error"!==o||_||(_=!0,"iced"===o?n(r):c(l(code,t)),v.close())}))}))}},2102:function(e,t,n){"use strict";var r=function(){function e(e,t){var n=this,nav=(t=Object.assign({navigator:navigator,window:window},t)).navigator,r=nav.connection||{type:null},o=r.type,c=r.type?{_events:{value:["change","typechange"]},_listener:{value:function(){var t=o!==n.type&&n.isOnline;o=n.type,t&&e()}},_target:{value:r}}:{_events:{value:["online"]},_listener:{value:e},_target:{value:t.window}},l=c._events,d=c._listener,f=c._target;Object.defineProperties(this,{isOnline:{enumerable:!0,get:function(){return"boolean"!=typeof nav.onLine||nav.onLine}},type:{enumerable:!0,get:function(){return r.type||null}},_listener:d,_events:l,_target:f})}return e.prototype.start=function(){var e=this;this._events.forEach((function(t){e._target.addEventListener(t,e._listener)}))},e.prototype.stop=function(){var e=this;this._events.forEach((function(t){e._target.removeEventListener(t,e._listener)}))},e}();e.exports=r},2103:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},d=function(e){function t(code){var n=this,r=[].slice.call(arguments,1);n=e.apply(this,l([],c(r)))||this,Object.setPrototypeOf(n,t.prototype);var o=Error.apply(n,r);return o.name="TwilioError",Object.defineProperty(n,"code",{value:code,enumerable:!0}),Object.getOwnPropertyNames(o).forEach((function(e){Object.defineProperty(this,e,{value:o[e],enumerable:!0})}),n),n}return o(t,e),t.prototype.toString=function(){var e=this.message?": "+this.message:"";return this.name+" "+this.code+e},t}(Error);e.exports=d},2104:function(e,t,n){"use strict";var r=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},o=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.makeStat=void 0,t.makeStat=function(e){return e&&e.length?{min:Math.min.apply(Math,o([],r(e))),max:Math.max.apply(Math,o([],r(e))),average:e.reduce((function(e,t){return e+t}),0)/e.length}:null}},2105:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.syntheticAudio=void 0,t.syntheticAudio=function(){var e=n(1695),t={},r=e.getOrCreate(t),o=r.createOscillator(),c=o.connect(r.createMediaStreamDestination());o.start();var track=c.stream.getAudioTracks()[0],l=track.stop;return track.stop=function(){l.call(track),e.release(t)},track}},2106:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.syntheticVideo=void 0,t.syntheticVideo=function(e){var t=void 0===e?{}:e,n=t.width,r=void 0===n?640:n,o=t.height,c=void 0===o?480:o,canvas=Object.assign(document.createElement("canvas"),{width:r,height:c}),l=canvas.getContext("2d");l.fillStyle="green",l.fillRect(0,0,canvas.width,canvas.height);var d=!1;requestAnimationFrame((function animate(){if(!d){var e=Math.round(255*Math.random()),g=Math.round(255*Math.random()),b=Math.round(255*Math.random()),a=Math.round(255*Math.random());l.fillStyle="rgba("+e+", "+g+", "+b+", "+a+")",l.fillRect(Math.random()*r,Math.random()*c,50,50),requestAnimationFrame(animate)}}));var track=canvas.captureStream(30).getTracks()[0],f=track.stop;return track.stop=function(){d=!0,f.call(track)},track}},2107:function(e,t,n){"use strict";var r=n(1608).MediaStreamTrack,o=n(1439),c=o.guessBrowser,l=o.guessBrowserVersion,d=n(2108),f=n(2109),h=n(2110),v=n(1886),_=n(2121),m=n(1615),y=m.LocalAudioTrack,w=m.LocalDataTrack,k=m.LocalVideoTrack,S=n(2122),T=n(2123),O=n(2129),P=n(1410),C=P.asLocalTrack,E=P.buildLogLevels,R=P.filterObject,j=P.isNonArrayObject,x=n(1425),L=x.DEFAULT_ENVIRONMENT,A=x.DEFAULT_LOG_LEVEL,D=x.DEFAULT_LOGGER_NAME,I=x.DEFAULT_REALM,M=x.DEFAULT_REGION,N=x.WS_SERVER,V=x.SDK_NAME,F=x.SDK_VERSION,B=x.typeErrors,U=n(1819),H=n(1885),W=n(1570),Q=n(1887).validateBandwidthProfile,G="safari"===c()&&l(),z=0,K=!1,Y=!1;if(G){var J=G.major,$=G.minor;Y=J<12||12===J&&$<1}var X=new Set([{didWarn:!1,shouldDelete:!0,name:"abortOnIceServersTimeout"},{didWarn:!1,shouldDelete:!0,name:"dscpTagging",newName:"enableDscp"},{didWarn:!1,shouldDelete:!0,name:"iceServersTimeout"},{didWarn:!1,shouldDelete:!1,name:"eventListener",newName:"Video.Logger"},{didWarn:!1,shouldDelete:!1,name:"logLevel",newName:"Video.Logger"}]),Z=new Set([{didWarn:!1,shouldDelete:!1,name:"maxTracks",newName:"bandwidthProfile.video.clientTrackSwitchOffControl"},{didWarn:!1,shouldDelete:!1,name:"renderDimensions",newName:"bandwidthProfile.video.contentPreferencesMode"}]);function ee(e,t,n){n.forEach((function(n){var r=n.didWarn,o=n.name,c=n.newName,l=n.shouldDelete;o in e&&void 0!==e[o]&&(c&&l&&(e[c]=e[o]),l&&delete e[o],r||["error","off"].includes(t.level)||(t.warn('The ConnectOptions "'+o+'" is '+(c?'deprecated and scheduled for removal. Please use "'+c+'" instead.':"no longer applicable and will be ignored.")),n.didWarn=!0))}))}function te(e,t,n,r,o,c){var l=e.createLocalParticipantSignaling(n,r);return t.debug("Creating a new LocalParticipant:",l),new o.LocalParticipant(l,c,o)}function ne(e,t,n){var r=new T(t,n,e),o=e.log;return o.debug("Creating a new Room:",r),n.on("stateChanged",(function e(t){"disconnected"===t&&(o.info("Disconnected from Room:",r.toString()),n.removeListener("stateChanged",e))})),r}function re(e,t,n,r,o,c){return t.log.debug("Creating a new RoomSignaling"),n.connect(c._signaling,e,r,o,t)}function ie(e,t){var n=e.log;return e.shouldStopLocalTracks=!e.tracks,e.shouldStopLocalTracks?n.info("LocalTracks were not provided, so they will be acquired automatically before connecting to the Room. LocalTracks will be released if connecting to the Room fails or if the Room is disconnected"):(n.info("Getting LocalTracks"),n.debug("Options:",e)),e.createLocalTracks(e).then((function(r){var o=t(r);return o.catch((function(){e.shouldStopLocalTracks&&(n.info("The automatically acquired LocalTracks will now be stopped"),r.forEach((function(track){track.stop()})))})),o}))}function oe(e){var t="string"==typeof e?{codec:e}:e;switch(t.codec.toLowerCase()){case"opus":return Object.assign({dtx:!0},t);case"vp8":return Object.assign({simulcast:!1},t);default:return t}}e.exports=function(e,t){if(void 0===t&&(t={}),!j(t))return U.reject(B.INVALID_TYPE("options","object"));var n,o=t.Log||W,c=t.loggerName||D,l=t.logLevel||A,m=E(l),T="[connect #"+ ++z+"]";try{n=new o("default",T,m,c)}catch(se){return U.reject(se)}ee(t,n,X);var P="auto"===t.preferredVideoCodecs;if(P&&(t.preferredVideoCodecs=[{codec:"VP8",simulcast:!0,adaptiveSimulcast:!0}]),t.maxVideoBitrate&&P)return n.error('ConnectOptions "maxVideoBitrate" is not compatible with "preferredVideoCodecs=auto"'),U.reject(B.ILLEGAL_INVOKE("connect",'ConnectOptions "maxVideoBitrate" is not compatible with "preferredVideoCodecs=auto"'));var x={};"string"==typeof(t=Object.assign({automaticSubscription:!0,dominantSpeaker:!1,enableDscp:!1,environment:L,eventListener:null,insights:!0,LocalAudioTrack:y,LocalDataTrack:w,LocalParticipant:h,LocalVideoTrack:k,Log:o,MediaStreamTrack:r,loggerName:c,logLevel:l,maxAudioBitrate:null,maxVideoBitrate:null,name:null,networkMonitor:!0,networkQuality:!1,preferredAudioCodecs:[],preferredVideoCodecs:[],realm:I,region:M,signaling:O},R(t))).wsServerInsights&&(x.gateway=t.wsServerInsights);var G=new(t.insights?v:_)(e,V,F,t.environment,t.realm,x),J=N(t.environment,t.region),$=new H(G,Date.now(),n,t.eventListener);if((t=Object.assign({eventObserver:$,wsServer:J},t)).log=n,Y&&!K&&"error"!==n.logLevel&&"off"!==n.logLevel&&(K=!0,n.warn(["Support for Safari 12.0 and below is limited because it does not support VP8.","This means you may experience codec issues in Group Rooms. You may also","experience codec issues in Peer-to-Peer (P2P) Rooms containing Android- or","iOS-based Participants who do not support H.264. However, P2P Rooms","with browser-based Participants should work. For more information, please","refer to this guide: https://www.twilio.com/docs/video/javascript-v2-developing-safari-11"].join(" "))),"string"!=typeof e)return U.reject(B.INVALID_TYPE("token","string"));var ae=Object.assign({},t);if(delete ae.name,"tracks"in t){if(!Array.isArray(t.tracks))return U.reject(B.INVALID_TYPE("options.tracks","Array of LocalAudioTrack, LocalVideoTrack or MediaStreamTrack"));try{t.tracks=t.tracks.map((function(track){return C(track,ae)}))}catch(se){return U.reject(se)}}var se=Q(t.bandwidthProfile);if(se)return U.reject(se);t.clientTrackSwitchOffControl="disabled",t.contentPreferencesMode="disabled",t.bandwidthProfile&&(t.clientTrackSwitchOffControl="auto",t.contentPreferencesMode="auto",t.bandwidthProfile.video&&(ee(t.bandwidthProfile.video,n,Z),"maxTracks"in t.bandwidthProfile.video?t.clientTrackSwitchOffControl="disabled":"manual"===t.bandwidthProfile.video.clientTrackSwitchOffControl?t.clientTrackSwitchOffControl="manual":t.clientTrackSwitchOffControl="auto","renderDimensions"in t.bandwidthProfile.video?t.contentPreferencesMode="disabled":"manual"===t.bandwidthProfile.video.contentPreferencesMode?t.contentPreferencesMode="manual":t.contentPreferencesMode="auto"));var ce=new(0,t.signaling)(t.wsServer,t);n.info("Connecting to a Room"),n.debug("Options:",t);var ue=new f({maxAudioBitrate:t.maxAudioBitrate,maxVideoBitrate:t.maxVideoBitrate},P),le={audio:t.preferredAudioCodecs.map(oe),video:t.preferredVideoCodecs.map(oe)},de=new S(j(t.networkQuality)?t.networkQuality:{}),pe=d(ie.bind(null,t),te.bind(null,ce,n,ue,de,t),re.bind(null,e,t,ce,ue,le),ne.bind(null,t));return pe.then((function(e){return G.connect(e.sid,e.localParticipant.sid),n.info("Connected to Room:",e.toString()),n.info("Room name:",e.name),n.debug("Room:",e),e.once("disconnected",(function(){return G.disconnect()})),e}),(function(e){G.disconnect(),pe._isCanceled?n.info("Attempt to connect to a Room was canceled"):n.info("Error while connecting to a Room:",e)})),pe}},2108:function(e,t,n){"use strict";var r=n(1819);e.exports=function(e,t,n,o){var c,l=new Error("Canceled");return new r((function(d,f,h){var v;e((function(e){return h()?r.reject(l):(v=t(e),n(v).then((function(e){if(h())throw l;return c=e()})))})).then((function(e){if(h())throw e.disconnect(),l;d(o(v,e))})).catch((function(e){f(e)}))}),(function(){c&&c.cancel()}))}},2109:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){var r=e.call(this)||this;return t=Object.assign({maxAudioBitrate:null,maxVideoBitrate:null},t),Object.defineProperties(r,{maxAudioBitrate:{value:t.maxAudioBitrate,writable:!0},maxVideoBitrate:{value:t.maxVideoBitrate,writable:!0},adaptiveSimulcast:{value:n}}),r}return o(t,e),t.prototype.toJSON=function(){return{maxAudioBitrate:this.maxAudioBitrate,maxVideoBitrate:this.maxVideoBitrate}},t.prototype.update=function(e){var t=this;e=Object.assign({maxAudioBitrate:this.maxAudioBitrate,maxVideoBitrate:this.maxVideoBitrate},e),["maxAudioBitrate","maxVideoBitrate"].reduce((function(n,r){return t[r]!==e[r]&&(t[r]=e[r],n=!0),n}),!1)&&this.emit("changed")},t}(n(373).EventEmitter);e.exports=c},2110:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1608).MediaStreamTrack,l=n(1410),d=l.asLocalTrack,f=l.asLocalTrackPublication,h=l.trackClass,v=n(1425),_=v.typeErrors,m=v.trackPriority,y=n(1887).validateLocalTrack,w=n(1615),k=w.LocalAudioTrack,S=w.LocalDataTrack,T=w.LocalVideoTrack,O=n(2111),P=n(2112),C=n(2113),E=function(e){function t(t,n,r){var o=this,l=(r=Object.assign({LocalAudioTrack:k,LocalVideoTrack:T,LocalDataTrack:S,MediaStreamTrack:c,LocalAudioTrackPublication:O,LocalVideoTrackPublication:C,LocalDataTrackPublication:P,shouldStopLocalTracks:!1,tracks:n},r)).shouldStopLocalTracks?new Set(n.filter((function(e){return"data"!==e.kind}))):new Set;return o=e.call(this,t,r)||this,Object.defineProperties(o,{_eventObserver:{value:r.eventObserver},_LocalAudioTrack:{value:r.LocalAudioTrack},_LocalDataTrack:{value:r.LocalDataTrack},_LocalVideoTrack:{value:r.LocalVideoTrack},_MediaStreamTrack:{value:r.MediaStreamTrack},_LocalAudioTrackPublication:{value:r.LocalAudioTrackPublication},_LocalDataTrackPublication:{value:r.LocalDataTrackPublication},_LocalVideoTrackPublication:{value:r.LocalVideoTrackPublication},_tracksToStop:{value:l},signalingRegion:{enumerable:!0,get:function(){return t.signalingRegion}}}),o._handleTrackSignalingEvents(),o}return o(t,e),t.prototype._addTrack=function(track,t,n){var r=e.prototype._addTrack.call(this,track,t);return r&&"disconnected"!==this.state&&this._addLocalTrack(track,n),r},t.prototype._addLocalTrack=function(track,e){this._signaling.addTrack(track._trackSender,track.name,e),this._log.info("Added a new "+h(track,!0)+":",track.id),this._log.debug(h(track,!0)+":",track)},t.prototype._removeTrack=function(track,t){var n=e.prototype._removeTrack.call(this,track,t);return n&&"disconnected"!==this.state&&(this._signaling.removeTrack(track._trackSender),this._log.info("Removed a "+h(track,!0)+":",track.id),this._log.debug(h(track,!0)+":",track)),n},t.prototype._getTrackEvents=function(){return e.prototype._getTrackEvents.call(this).concat([["disabled","trackDisabled"],["enabled","trackEnabled"],["stopped","trackStopped"]])},t.prototype.toString=function(){return"[LocalParticipant #"+this._instanceId+(this.sid?": "+this.sid:"")+"]"},t.prototype._handleTrackSignalingEvents=function(){var e=this,t=this._log;if("disconnected"!==this.state){var n=this._signaling;this.on("trackDisabled",o),this.on("trackEnabled",c),this.on("trackStopped",l),this._tracks.forEach((function(track){e._addLocalTrack(track,m.PRIORITY_STANDARD),e._getOrCreateLocalTrackPublication(track).catch((function(e){t.warn("Failed to get or create LocalTrackPublication for "+track+":",e)}))}));var r=this;n.on("stateChanged",(function e(d){t.debug("Transitioned to state:",d),"disconnected"===d?(t.debug("Removing LocalTrack event listeners"),n.removeListener("stateChanged",e),r.removeListener("trackDisabled",o),r.removeListener("trackEnabled",c),r.removeListener("trackStopped",l),r._tracks.forEach((function(track){var e=l(track);e&&track._trackSender.removeClone(e._trackTransceiver)})),t.info("LocalParticipant disconnected. Stopping "+r._tracksToStop.size+" automatically-acquired LocalTracks"),r._tracksToStop.forEach((function(track){track.stop()}))):"connected"===d&&(t.info("reconnected"),setTimeout((function(){return r.emit("reconnected")}),0))}))}function o(e){var r=n.getPublication(e._trackSender);r&&(r.disable(),t.debug("Disabled the "+h(e,!0)+":",e.id))}function c(e){var r=n.getPublication(e._trackSender);r&&(r.enable(),t.debug("Enabled the "+h(e,!0)+":",e.id))}function l(e){var t=n.getPublication(e._trackSender);return t&&t.stop(),t}},t.prototype._getOrCreateLocalTrackPublication=function(e){var t=R(this.tracks,e);if(t)return Promise.resolve(t);var n=this._log,r=this,o=this._signaling.getPublication(e._trackSender);if(!o)return Promise.reject(new Error("Unexpected error: The "+e+" cannot be published"));function c(e){r.unpublishTrack(e.track)}return new Promise((function(l,d){o.on("updated",(function v(){var _=o.error;if(_)return o.removeListener("updated",v),n.warn("Failed to publish the "+h(e,!0)+": "+_.message),r._removeTrack(e,e.id),setTimeout((function(){r.emit("trackPublicationFailed",_,e)})),void d(_);if(!r._tracks.has(e.id))return o.removeListener("updated",v),void d(new Error("The "+e+" was unpublished"));if(o.sid){o.removeListener("updated",v);var m={log:n,LocalAudioTrackPublication:r._LocalAudioTrackPublication,LocalDataTrackPublication:r._LocalDataTrackPublication,LocalVideoTrackPublication:r._LocalVideoTrackPublication};(t=R(r.tracks,e))||(t=f(e,o,c,m),r._addTrackPublication(t));var y=r._signaling.state;"connected"!==y&&"connecting"!==y||(e._processorEventObserver&&e._processorEventObserver.on("event",(function(e){r._eventObserver.emit("event",{name:e.name,payload:e.data,group:"video-processor",level:"info"})})),e.processedTrack&&(e._captureFrames(),e._setSenderMediaStreamTrack(!0))),"connected"===y&&setTimeout((function(){r.emit("trackPublished",t)})),l(t)}}))}))},t.prototype.publishTrack=function(e,t){var n,r=R(this.tracks,e);if(r)return Promise.resolve(r);t=Object.assign({log:this._log,priority:m.PRIORITY_STANDARD,LocalAudioTrack:this._LocalAudioTrack,LocalDataTrack:this._LocalDataTrack,LocalVideoTrack:this._LocalVideoTrack,MediaStreamTrack:this._MediaStreamTrack},t);try{n=d(e,t)}catch(e){return Promise.reject(e)}var o=Object.values(m);if(!o.includes(t.priority))return Promise.reject(_.INVALID_VALUE("LocalTrackPublishOptions.priority",o));var c=this._addTrack(n,n.id,t.priority)||this._tracks.get(n.id);return this._getOrCreateLocalTrackPublication(c)},t.prototype.publishTracks=function(e){if(!Array.isArray(e))throw _.INVALID_TYPE("tracks","Array of LocalAudioTrack, LocalVideoTrack, LocalDataTrack, or MediaStreamTrack");return Promise.all(e.map(this.publishTrack,this))},t.prototype.setBandwidthProfile=function(){this._log.warn("setBandwidthProfile is not implemented yet and may be available in future versions of twilio-video.js")},t.prototype.setNetworkQualityConfiguration=function(e){if("object"!=typeof e||null===e)throw _.INVALID_TYPE("networkQualityConfiguration","NetworkQualityConfiguration");return["local","remote"].forEach((function(t){if(t in e&&("number"!=typeof e[t]||isNaN(e[t])))throw _.INVALID_TYPE("networkQualityConfiguration."+t,"number")})),this._signaling.setNetworkQualityConfiguration(e),this},t.prototype.setParameters=function(e){if(void 0!==e&&"object"!=typeof e)throw _.INVALID_TYPE("encodingParameters","EncodingParameters, null or undefined");if(e){if(this._signaling.getParameters().adaptiveSimulcast&&e.maxVideoBitrate)throw _.INVALID_TYPE("encodingParameters",'encodingParameters.maxVideoBitrate is not compatible with "preferredVideoCodecs=auto"');["maxAudioBitrate","maxVideoBitrate"].forEach((function(t){if(void 0!==e[t]&&"number"!=typeof e[t]&&null!==e[t])throw _.INVALID_TYPE("encodingParameters."+t,"number, null or undefined")}))}else null===e&&(e={maxAudioBitrate:null,maxVideoBitrate:null});return this._signaling.setParameters(e),this},t.prototype.unpublishTrack=function(track){y(track,{LocalAudioTrack:this._LocalAudioTrack,LocalDataTrack:this._LocalDataTrack,LocalVideoTrack:this._LocalVideoTrack,MediaStreamTrack:this._MediaStreamTrack});var e=this._tracks.get(track.id);if(!e)return null;if(this._signaling.getPublication(e._trackSender).publishFailed(new Error("The "+e+" was unpublished")),!(e=this._removeTrack(e,e.id)))return null;var t=R(this.tracks,e);return t&&this._removeTrackPublication(t),t},t.prototype.unpublishTracks=function(e){var t=this;if(!Array.isArray(e))throw _.INVALID_TYPE("tracks","Array of LocalAudioTrack, LocalVideoTrack, LocalDataTrack, or MediaStreamTrack");return e.reduce((function(e,track){var n=t.unpublishTrack(track);return n?e.concat(n):e}),[])},t}(n(1889));function R(e,track){return Array.from(e.values()).find((function(e){return e.track===track||e.track.mediaStreamTrack===track}))||null}e.exports=E},2111:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,track,n,r){return e.call(this,t,track,n,r)||this}return o(t,e),t.prototype.toString=function(){return"[LocalAudioTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(n(1820));e.exports=c},2112:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,track,n,r){return e.call(this,t,track,n,r)||this}return o(t,e),t.prototype.toString=function(){return"[LocalDataTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(n(1820));e.exports=c},2113:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,track,n,r){return e.call(this,t,track,n,r)||this}return o(t,e),t.prototype.toString=function(){return"[LocalVideoTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(n(1820));e.exports=c},2114:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1869),l=function(e){function t(t,n,r,o,c,l,d){return e.call(this,t,n,r,o,c,l,d)||this}return o(t,e),t.prototype.toString=function(){return"[RemoteAudioTrack #"+this._instanceId+": "+this.sid+"]"},t.prototype.setPriority=function(t){return e.prototype.setPriority.call(this,t)},t}(n(1890)(c));e.exports=l},2115:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){return e.call(this,t,n)||this}return o(t,e),t.prototype.toString=function(){return"[RemoteAudioTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(n(1821));e.exports=c},2116:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},d=n(1817),f=n(1425),h=f.typeErrors,v=f.trackPriority,_=function(e){function t(t,n,r){var o=e.call(this,n.id,"data",r)||this;return Object.defineProperties(o,{_isSwitchedOff:{value:!1,writable:!0},_priority:{value:null,writable:!0},isEnabled:{enumerable:!0,value:!0},isSwitchedOff:{enumerable:!0,get:function(){return this._isSwitchedOff}},maxPacketLifeTime:{enumerable:!0,value:n.maxPacketLifeTime},maxRetransmits:{enumerable:!0,value:n.maxRetransmits},ordered:{enumerable:!0,value:n.ordered},priority:{enumerable:!0,get:function(){return this._priority}},reliable:{enumerable:!0,value:null===n.maxPacketLifeTime&&null===n.maxRetransmits},sid:{enumerable:!0,value:t}}),n.on("message",(function(data){o.emit("message",data,o)})),o}return o(t,e),t.prototype.setPriority=function(e){var t=l([null],c(Object.values(v)));if(!t.includes(e))throw h.INVALID_VALUE("priority",t);return this._priority=e,this},t.prototype._setEnabled=function(){},t.prototype._setSwitchedOff=function(e){this._isSwitchedOff!==e&&(this._isSwitchedOff=e,this.emit(e?"switchedOff":"switchedOn",this))},t}(d);e.exports=_},2117:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){return e.call(this,t,n)||this}return o(t,e),t.prototype.toString=function(){return"[RemoteDataTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(n(1821));e.exports=c},2118:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=n(1890),d=n(1880),f=n(1818),h=n(2119).NullObserver,v=n(1609),_=l(d),m=function(e){function t(t,n,r,o,c,l,d){var f=this;return d=Object.assign({clientTrackSwitchOffControl:"auto",contentPreferencesMode:"auto",enableDocumentVisibilityTurnOff:!0},d),d=Object.assign({IntersectionObserver:"undefined"==typeof IntersectionObserver||"auto"!==d.clientTrackSwitchOffControl?h:IntersectionObserver,ResizeObserver:"undefined"==typeof ResizeObserver||"auto"!==d.contentPreferencesMode?h:ResizeObserver},d),f=e.call(this,t,n,r,o,c,l,d)||this,Object.defineProperties(f,{_enableDocumentVisibilityTurnOff:{value:!0===d.enableDocumentVisibilityTurnOff&&"auto"===d.clientTrackSwitchOffControl},_documentVisibilityTurnOffCleanup:{value:null,writable:!0},_clientTrackSwitchOffControl:{value:d.clientTrackSwitchOffControl},_contentPreferencesMode:{value:d.contentPreferencesMode},_invisibleElements:{value:new WeakSet},_elToPipCallbacks:{value:new WeakMap},_elToPipWindows:{value:new WeakMap},_turnOffTimer:{value:new v((function(){f._setRenderHint({enabled:!1})}),50,!1)},_resizeObserver:{value:new d.ResizeObserver((function(e){e.find((function(e){return!f._invisibleElements.has(e.target)}))&&w(f)}))},_intersectionObserver:{value:new d.IntersectionObserver((function(e){var t=!1;e.forEach((function(e){!f._invisibleElements.has(e.target)!==e.isIntersecting&&(e.isIntersecting?(f._log.debug("intersectionObserver detected: Off => On"),f._invisibleElements.delete(e.target)):(f._log.debug("intersectionObserver detected: On => Off"),f._invisibleElements.add(e.target)),t=!0)})),t&&(y(f),w(f))}),{threshold:.25})}}),f}return o(t,e),t.prototype._start=function(t){var n=e.prototype._start.call(this,t);return y(this),n},t.prototype.switchOn=function(){if("manual"!==this._clientTrackSwitchOffControl)throw new Error('Invalid state. You can call switchOn only when bandwidthProfile.video.clientTrackSwitchOffControl is set to "manual"');return this._setRenderHint({enabled:!0}),this},t.prototype.switchOff=function(){if("manual"!==this._clientTrackSwitchOffControl)throw new Error('Invalid state. You can call switchOff only when bandwidthProfile.video.clientTrackSwitchOffControl is set to "manual"');return this._setRenderHint({enabled:!1}),this},t.prototype.setContentPreferences=function(e){if("manual"!==this._contentPreferencesMode)throw new Error('Invalid state. You can call switchOn only when bandwidthProfile.video.contentPreferencesMode is set to "manual"');return e.renderDimensions&&this._setRenderHint({renderDimensions:e.renderDimensions}),this},t.prototype._unObservePip=function(e){var t=this._elToPipCallbacks.get(e);t&&(e.removeEventListener("enterpictureinpicture",t.onEnterPip),e.removeEventListener("leavepictureinpicture",t.onLeavePip),this._elToPipCallbacks.delete(e))},t.prototype._observePip=function(e){var t=this;if(!this._elToPipCallbacks.get(e)){var n=function(n){return t._onEnterPip(n,e)},r=function(n){return t._onLeavePip(n,e)};e.addEventListener("enterpictureinpicture",n),e.addEventListener("leavepictureinpicture",r),this._elToPipCallbacks.set(e,{onEnterPip:n,onLeavePip:r,onResizePip:function(n){return t._onResizePip(n,e)}})}},t.prototype._onEnterPip=function(e,t){this._log.debug("onEnterPip");var n=e.pictureInPictureWindow;this._elToPipWindows.set(t,n);var r=this._elToPipCallbacks.get(t).onResizePip;n.addEventListener("resize",r),y(this)},t.prototype._onLeavePip=function(e,t){this._log.debug("onLeavePip"),this._elToPipWindows.delete(t);var n=this._elToPipCallbacks.get(t).onResizePip;e.pictureInPictureWindow.removeEventListener("resize",n),y(this)},t.prototype._onResizePip=function(){w(this)},t.prototype.attach=function(t){var n=e.prototype.attach.call(this,t);return"auto"===this._clientTrackSwitchOffControl&&this._invisibleElements.add(n),this._intersectionObserver.observe(n),this._resizeObserver.observe(n),this._enableDocumentVisibilityTurnOff&&(this._documentVisibilityTurnOffCleanup=this._documentVisibilityTurnOffCleanup||function(e){function t(){y(e)}return f.onVisibilityChange(1,t),function(){f.offVisibilityChange(1,t)}}(this)),this._observePip(n),n},t.prototype.detach=function(t){var n=this,r=e.prototype.detach.call(this,t);return(Array.isArray(r)?r:[r]).forEach((function(element){n._intersectionObserver.unobserve(element),n._resizeObserver.unobserve(element),n._invisibleElements.delete(element),n._unObservePip(element)})),0===this._attachments.size&&this._documentVisibilityTurnOffCleanup&&(this._documentVisibilityTurnOffCleanup(),this._documentVisibilityTurnOffCleanup=null),y(this),w(this),r},t.prototype.addProcessor=function(){return e.prototype.addProcessor.apply(this,arguments)},t.prototype.removeProcessor=function(){return e.prototype.removeProcessor.apply(this,arguments)},t.prototype.toString=function(){return"[RemoteVideoTrack #"+this._instanceId+": "+this.sid+"]"},t.prototype.setPriority=function(t){return e.prototype.setPriority.call(this,t)},t}(_);function y(e){if("auto"===e._clientTrackSwitchOffControl){var t=e._getAllAttachedElements().filter((function(t){return!e._invisibleElements.has(t)}));!0===(e._getAllAttachedElements().filter((function(t){return e._elToPipWindows.has(t)})).length>0||"visible"===document.visibilityState&&t.length>0)?(e._turnOffTimer.clear(),e._setRenderHint({enabled:!0})):e._turnOffTimer.isSet||e._turnOffTimer.start()}}function w(e){if("auto"===e._contentPreferencesMode){var t=e._getAllAttachedElements().filter((function(t){return!e._invisibleElements.has(t)})),n=e._getAllAttachedElements().map((function(t){var n=e._elToPipWindows.get(t);return n?{clientHeight:n.height,clientWidth:n.width}:{clientHeight:0,clientWidth:0}})),r=t.concat(n);if(r.length>0){var o=c(r.sort((function(e,t){return t.clientHeight+t.clientWidth-e.clientHeight-e.clientWidth-1})),1)[0],l={height:o.clientHeight,width:o.clientWidth};e._setRenderHint({renderDimensions:l})}}}e.exports=m},2119:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(){function e(e){Object.defineProperties(this,{_callback:{value:e}})}return e.prototype.observe=function(){},e.prototype.unobserve=function(){},e.prototype.makeVisible=function(e){var t=this._makeFakeEntry(e,!0);this._callback([t])},e.prototype.makeInvisible=function(e){var t=this._makeFakeEntry(e,!1);this._callback([t])},e.prototype._makeFakeEntry=function(e,t){return{target:e,isIntersecting:t}},e}(),l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t}(c),d=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.resize=function(e){var t=this._makeFakeEntry(e,!0);this._callback([t])},t}(c);e.exports={NullIntersectionObserver:l,NullResizeObserver:d,NullObserver:c}},2120:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){return e.call(this,t,n)||this}return o(t,e),t.prototype.toString=function(){return"[RemoteVideoTrackPublication #"+this._instanceId+": "+this.trackSid+"]"},t}(n(1821));e.exports=c},2121:function(e,t,n){"use strict";var r=function(){function e(){Object.defineProperties(this,{_connected:{writable:!0,value:!0}})}return e.prototype.connect=function(){},e.prototype.disconnect=function(){return!!this._connected&&(this._connected=!1,!0)},e.prototype.publish=function(){return this._connected},e}();e.exports=r},2122:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=n(373).EventEmitter,d=n(1425),f=d.DEFAULT_NQ_LEVEL_LOCAL,h=d.DEFAULT_NQ_LEVEL_REMOTE,v=d.MAX_NQ_LEVEL,_=n(1410).inRange,m=function(e){function t(t){var n=e.call(this)||this;return t=Object.assign({local:f,remote:h},t),Object.defineProperties(n,{local:{value:_(t.local,f,v)?t.local:f,writable:!0},remote:{value:_(t.remote,h,v)?t.remote:h,writable:!0}}),n}return o(t,e),t.prototype.update=function(e){var t=this;e=Object.assign({local:this.local,remote:this.remote},e),[["local",f,3],["remote",h,3]].forEach((function(n){var r=c(n,3),o=r[0],l=r[1],d=r[2];t[o]="number"==typeof e[o]&&_(e[o],l,d)?e[o]:l}))},t}(l);e.exports=m},2123:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},d=n(1684),f=n(2124),h=n(1891),v=n(1410).valueToJSON,_=0,m=function(e){function t(t,n,r){var o,c,l=e.call(this)||this,d=r.log.createLog("default",l),f=new Map;return Object.defineProperties(l,{_log:{value:d},_clientTrackSwitchOffControl:{value:r.clientTrackSwitchOffControl||"disabled"},_contentPreferencesMode:{value:r.contentPreferencesMode||"disabled"},_instanceId:{value:++_},_options:{value:r},_participants:{value:f},_signaling:{value:n},dominantSpeaker:{enumerable:!0,get:function(){return this.participants.get(n.dominantSpeakerSid)||null}},isRecording:{enumerable:!0,get:function(){return n.recording.isEnabled||!1}},localParticipant:{enumerable:!0,value:t},name:{enumerable:!0,value:n.name},participants:{enumerable:!0,value:f},sid:{enumerable:!0,value:n.sid},state:{enumerable:!0,get:function(){return n.state}},mediaRegion:{enumerable:!0,value:n.mediaRegion}}),o=l,(c=n.recording).on("updated",(function(){var e=c.isEnabled;o._log.info("Recording "+(e?"started":"stopped")),o.emit("recording"+(e?"Started":"Stopped"))})),function(e,t){var n=e._log;n.debug("Creating a new RemoteParticipant for each ParticipantSignaling in the RoomSignaling"),t.participants.forEach(w.bind(null,e)),n.debug("Setting up RemoteParticipant creation for all subsequent ParticipantSignalings that connect to the RoomSignaling"),t.on("participantConnected",w.bind(null,e)),t.on("dominantSpeakerChanged",(function(){return e.emit("dominantSpeakerChanged",e.dominantSpeaker)})),t.on("stateChanged",(function r(o,c){switch(n.info("Transitioned to state:",o),o){case"disconnected":e.participants.forEach((function(e){e._unsubscribeTracks()})),e.emit(o,e,c),e.localParticipant.tracks.forEach((function(e){e.unpublish()})),t.removeListener("stateChanged",r);break;case"reconnecting":setTimeout((function(){return e.emit("reconnecting",c)}),0);break;default:setTimeout((function(){return e.emit("reconnected")}),0)}}))}(l,n),d.info("Created a new Room:",l.name),d.debug("Initial RemoteParticipants:",Array.from(l._participants.values())),l}return o(t,e),t.prototype.toString=function(){return"[Room #"+this._instanceId+": "+this.sid+"]"},t.prototype.disconnect=function(){return this._log.info("Disconnecting"),this._signaling.disconnect(),this},t.prototype.getStats=function(){var e=this;return this._signaling.getStats().then((function(t){return Array.from(t).map((function(t){var n=c(t,2),r=n[0],o=n[1];return new h(r,Object.assign({},o,{localAudioTrackStats:y(e,o.localAudioTrackStats),localVideoTrackStats:y(e,o.localVideoTrackStats)}))}))}))},t.prototype.toJSON=function(){return v(this)},t}(d);function y(e,t){var n=e.localParticipant._signaling;return t.reduce((function(e,t){var r=n.tracks.get(t.trackId),o=n.getSender(r);return o?[Object.assign({},t,{trackId:o.id})].concat(e):e}),[])}function w(e,t){var n=e._log,r=e._clientTrackSwitchOffControl,o=e._contentPreferencesMode,d=new f(t,{log:n,clientTrackSwitchOffControl:r,contentPreferencesMode:o});n.info("A new RemoteParticipant connected:",d),e._participants.set(d.sid,d),e.emit("participantConnected",d);var h=[["reconnected","participantReconnected"],["reconnecting","participantReconnecting"],"trackDimensionsChanged","trackDisabled","trackEnabled","trackMessage","trackPublished","trackPublishPriorityChanged","trackStarted","trackSubscribed","trackSubscriptionFailed","trackSwitchedOff","trackSwitchedOn","trackUnpublished","trackUnsubscribed"].map((function(t){var n=c(Array.isArray(t)?t:[t,t],2),r=n[0],o=n[1];function f(){var t=[].slice.call(arguments);t.unshift(o),t.push(d),e.emit.apply(e,l([],c(t)))}return d.on(r,f),[r,f]}));d.once("disconnected",(function(){var t=e.dominantSpeaker;n.info("RemoteParticipant disconnected:",d),e._participants.delete(d.sid),h.forEach((function(e){d.removeListener(e[0],e[1])})),e.emit("participantDisconnected",d),d===t&&e.emit("dominantSpeakerChanged",e.dominantSpeaker)}))}e.exports=m},2124:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},d=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r._handleTrackSignalingEvents(),r.once("disconnected",r._unsubscribeTracks.bind(r)),r}return o(t,e),t.prototype.toString=function(){return"[RemoteParticipant #"+this._instanceId+(this.sid?": "+this.sid:"")+"]"},t.prototype._addTrack=function(t,n,r){return e.prototype._addTrack.call(this,t,r)?(n._subscribed(t),this.emit("trackSubscribed",t,n),t):null},t.prototype._addTrackPublication=function(t){var n=e.prototype._addTrackPublication.call(this,t);return n?(this.emit("trackPublished",n),n):null},t.prototype._getTrackPublicationEvents=function(){return l(l([],c(e.prototype._getTrackPublicationEvents.call(this))),[["subscriptionFailed","trackSubscriptionFailed"],["trackDisabled","trackDisabled"],["trackEnabled","trackEnabled"],["publishPriorityChanged","trackPublishPriorityChanged"],["trackSwitchedOff","trackSwitchedOff"],["trackSwitchedOn","trackSwitchedOn"]])},t.prototype._unsubscribeTracks=function(){var e=this;this.tracks.forEach((function(t){if(t.isSubscribed){var track=t.track;t._unsubscribe(),e.emit("trackUnsubscribed",track,t)}}))},t.prototype._removeTrack=function(t,n,r){var o=this._tracks.get(r);return o?(e.prototype._removeTrack.call(this,o,r),n._unsubscribe(),this.emit("trackUnsubscribed",o,n),o):null},t.prototype._removeTrackPublication=function(t){this._signaling.clearTrackHint(t.trackSid);var n=e.prototype._removeTrackPublication.call(this,t);return n?(this.emit("trackUnpublished",n),n):null},t}(n(1889));e.exports=d},2125:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n,r){var o=e.call(this,t,n,r)||this;return Object.defineProperties(o,{audioLevel:{value:"number"==typeof n.audioInputLevel?n.audioInputLevel:null,enumerable:!0},jitter:{value:"number"==typeof n.jitter?n.jitter:null,enumerable:!0}}),o}return o(t,e),t}(n(1892));e.exports=c},2126:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n,r){var o=e.call(this,t,n,r)||this,c=null;"number"==typeof n.frameWidthInput&&"number"==typeof n.frameHeightInput&&(c={},Object.defineProperties(c,{width:{value:n.frameWidthInput,enumerable:!0},height:{value:n.frameHeightInput,enumerable:!0}}));var l=null;return"number"==typeof n.frameWidthSent&&"number"==typeof n.frameHeightSent&&(l={},Object.defineProperties(l,{width:{value:n.frameWidthSent,enumerable:!0},height:{value:n.frameHeightSent,enumerable:!0}})),Object.defineProperties(o,{captureDimensions:{value:c,enumerable:!0},dimensions:{value:l,enumerable:!0},captureFrameRate:{value:"number"==typeof n.frameRateInput?n.frameRateInput:null,enumerable:!0},frameRate:{value:"number"==typeof n.frameRateSent?n.frameRateSent:null,enumerable:!0}}),o}return o(t,e),t}(n(1892));e.exports=c},2127:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){var r=e.call(this,t,n)||this;return Object.defineProperties(r,{audioLevel:{value:"number"==typeof n.audioOutputLevel?n.audioOutputLevel:null,enumerable:!0},jitter:{value:"number"==typeof n.jitter?n.jitter:null,enumerable:!0}}),r}return o(t,e),t}(n(1894));e.exports=c},2128:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){var r=e.call(this,t,n)||this,o=null;return"number"==typeof n.frameWidthReceived&&"number"==typeof n.frameHeightReceived&&(o={},Object.defineProperties(o,{width:{value:n.frameWidthReceived,enumerable:!0},height:{value:n.frameHeightReceived,enumerable:!0}})),Object.defineProperties(r,{dimensions:{value:o,enumerable:!0},frameRate:{value:"number"==typeof n.frameRateReceived?n.frameRateReceived:null,enumerable:!0}}),r}return o(t,e),t}(n(1894));e.exports=c},2129:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(2130),l=n(2176),d=function(e){function t(t,n){var r;return n=Object.assign({createCancelableRoomSignalingPromise:c},n),r=e.call(this)||this,Object.defineProperties(r,{_createCancelableRoomSignalingPromise:{value:n.createCancelableRoomSignalingPromise},_options:{value:n},_wsServer:{value:t}}),r}return o(t,e),t.prototype._connect=function(e,t,n,r,o){return o=Object.assign({},this._options,o),this._createCancelableRoomSignalingPromise.bind(null,t,this._wsServer,e,n,r,o)},t.prototype.createLocalParticipantSignaling=function(e,t){return new l(e,t)},t}(n(2180));e.exports=d},2130:function(e,t,n){"use strict";var r=n(1819),o=n(2131),c=n(2147),l=n(2175),d=n(1571),f=d.SignalingConnectionDisconnectedError,h=d.SignalingIncomingMessageInvalidError,v=n(1410),_=v.flatMap,m=v.createRoomConnectEventPayload;e.exports=function(e,t,n,d,v,y){y=Object.assign({PeerConnectionManager:o,RoomV2:c,Transport:l},y);var w=v.video[0]&&!0===v.video[0].adaptiveSimulcast,k=y.PeerConnectionManager,S=y.RoomV2,T=y.Transport,O=y.iceServers,P=y.log,C=new k(d,v,y),E=_(n.tracks,(function(e){return[e.trackTransceiver]}));C.setTrackSenders(E);var R,j=new Error("Canceled"),x=new r((function(r,o,c){var l=y.automaticSubscription,d=y.bandwidthProfile,v=y.dominantSpeaker,_=y.environment,k=y.eventObserver,E=y.loggerName,x=y.logLevel,L=y.name,A=y.networkMonitor,D=y.networkQuality,I=y.realm,M=y.sdpSemantics,N=!!d,V=!!d,F=!!d&&("disabled"!==y.clientTrackSwitchOffControl||"disabled"!==y.contentPreferencesMode),B=Object.assign({adaptiveSimulcast:w,automaticSubscription:l,dominantSpeaker:v,environment:_,eventObserver:k,loggerName:E,logLevel:x,networkMonitor:A,networkQuality:D,iceServers:O,onIced:function(e){return c()?(o(j),Promise.reject(j)):(P.debug("Got ICE servers:",e),y.iceServers=e,C.setConfiguration(y),C.createAndOffer().then((function(){if(c())throw o(j),j;P.debug("createAndOffer() succeeded."),C.dequeue("description")})).catch((function(e){throw P.error("createAndOffer() failed:",e),o(e),e})))},realm:I,renderHints:F,sdpSemantics:M,trackPriority:N,trackSwitchOff:V},d?{bandwidthProfile:d}:{});R=new T(L,e,n,C,t,B);var U=m(y);k.emit("event",U),R.once("connected",(function(e){if(P.debug("Transport connected:",e),c())o(j);else if(e.participant){var t=e.options.signaling_region;n.setSignalingRegion(t),r(new S(n,e,R,C,y))}else o(new h)})),R.once("stateChanged",(function(e,t){"disconnected"===e?(R=null,o(t||new f)):P.debug("Transport state changed:",e)}))}),(function(){R&&(R.disconnect(),R=null)}));return x.catch((function(){R&&(R.disconnect(),R=null),C.close()})),x}},2131:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},d=n(1439).guessBrowser,f=n(2132),h=n(1877),v=n(2146),_=n(1410),m=n(1571).MediaConnectionError,y="firefox"===d(),w=function(e){function t(t,r,o){var c=e.call(this)||this,l=(o=Object.assign({audioContextFactory:y?n(1695):null,PeerConnectionV2:f},o)).audioContextFactory?o.audioContextFactory.getOrCreate(c):null,d=l?{offerToReceiveVideo:!0}:{offerToReceiveAudio:!0,offerToReceiveVideo:!0};return Object.defineProperties(c,{_audioContextFactory:{value:o.audioContextFactory},_closedPeerConnectionIds:{value:new Set},_configuration:{writable:!0,value:null},_configurationDeferred:{writable:!0,value:_.defer()},_connectionState:{value:"new",writable:!0},_dummyAudioTrackSender:{value:l?new h(k(l)):null},_encodingParameters:{value:t},_iceConnectionState:{writable:!0,value:"new"},_dataTrackSenders:{writable:!0,value:new Set},_lastConnectionState:{value:"new",writable:!0},_lastIceConnectionState:{writable:!0,value:"new"},_mediaTrackSenders:{writable:!0,value:new Set},_offerOptions:{value:d},_peerConnections:{value:new Map},_preferredCodecs:{value:r},_sessionTimeout:{value:null,writable:!0},_PeerConnectionV2:{value:o.PeerConnectionV2}}),c}return o(t,e),t.prototype.setEffectiveAdaptiveSimulcast=function(e){this._peerConnections.forEach((function(t){return t.setEffectiveAdaptiveSimulcast(e)})),this._preferredCodecs.video.forEach((function(t){"adaptiveSimulcast"in t&&(t.adaptiveSimulcast=e)}))},Object.defineProperty(t.prototype,"connectionState",{get:function(){return this._connectionState},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"iceConnectionState",{get:function(){return this._iceConnectionState},enumerable:!1,configurable:!0}),t.prototype._closeAbsentPeerConnections=function(e){var t=new Set(e.map((function(e){return e.id})));return this._peerConnections.forEach((function(e){t.has(e.id)||e._close()})),this},t.prototype._getConfiguration=function(){return this._configurationDeferred.promise},t.prototype._getOrCreate=function(e,t){var n=this,r=this,o=this._peerConnections.get(e);if(!o){var c=this._PeerConnectionV2,l=Object.assign({dummyAudioMediaStreamTrack:this._dummyAudioTrackSender?this._dummyAudioTrackSender.track:null,offerOptions:this._offerOptions},this._sessionTimeout?{sessionTimeout:this._sessionTimeout}:{},t);try{o=new c(e,this._encodingParameters,this._preferredCodecs,l)}catch(e){throw new m}this._peerConnections.set(o.id,o),o.on("candidates",this.queue.bind(this,"candidates")),o.on("description",this.queue.bind(this,"description")),o.on("trackAdded",this.queue.bind(this,"trackAdded")),o.on("stateChanged",(function e(t){"closed"===t&&(o.removeListener("stateChanged",e),r._dataTrackSenders.forEach((function(e){return o.removeDataTrackSender(e)})),r._mediaTrackSenders.forEach((function(e){return o.removeMediaTrackSender(e)})),r._peerConnections.delete(o.id),r._closedPeerConnectionIds.add(o.id),R(r),E(r))})),o.on("connectionStateChanged",(function(){return R(n)})),o.on("iceConnectionStateChanged",(function(){return E(n)})),this._dataTrackSenders.forEach(o.addDataTrackSender,o),this._mediaTrackSenders.forEach(o.addMediaTrackSender,o),E(this)}return o},t.prototype.close=function(){return this._peerConnections.forEach((function(e){e.close()})),this._dummyAudioTrackSender&&this._dummyAudioTrackSender.stop(),this._audioContextFactory&&this._audioContextFactory.release(this),E(this),this},t.prototype.createAndOffer=function(){var e=this;return this._getConfiguration().then((function(t){var n;do{n=_.makeUUID()}while(e._peerConnections.has(n));return e._getOrCreate(n,t)})).then((function(e){return e.offer()})).then((function(){return e}))},t.prototype.getTrackReceivers=function(){return _.flatMap(this._peerConnections,(function(e){return e.getTrackReceivers()}))},t.prototype.getStates=function(){var e=[];return this._peerConnections.forEach((function(t){var n=t.getState();n&&e.push(n)})),e},t.prototype.setConfiguration=function(e){return this._configuration&&(this._configurationDeferred=_.defer(),this._peerConnections.forEach((function(t){t.setConfiguration(e)}))),this._configuration=e,this._configurationDeferred.resolve(e),this},t.prototype.setIceReconnectTimeout=function(e){return null===this._sessionTimeout&&(this._peerConnections.forEach((function(t){t.setIceReconnectTimeout(e)})),this._sessionTimeout=e),this},t.prototype.setTrackSenders=function(e){var t=new Set(e.filter((function(e){return"data"===e.kind}))),n=new Set(e.filter((function(e){return e&&("audio"===e.kind||"video"===e.kind)}))),r=function(e,t,n){return{data:S(e,t),media:T(e,n)}}(this,t,n);return this._dataTrackSenders=t,this._mediaTrackSenders=n,function(e,t){(t.data.add.size||t.data.remove.size||t.media.add.size||t.media.remove.size)&&e._peerConnections.forEach((function(e){t.data.remove.forEach(e.removeDataTrackSender,e),t.media.remove.forEach(e.removeMediaTrackSender,e),t.data.add.forEach(e.addDataTrackSender,e),t.media.add.forEach(e.addMediaTrackSender,e),(t.media.add.size||t.media.remove.size||t.data.add.size&&!e.isApplicationSectionNegotiated)&&e.offer()}))}(this,r),this},t.prototype.update=function(e,t){var n=this;return void 0===t&&(t=!1),t&&this._closeAbsentPeerConnections(e),this._getConfiguration().then((function(t){return Promise.all(e.map((function(e){return n._closedPeerConnectionIds.has(e.id)?null:n._getOrCreate(e.id,t).update(e)})))})).then((function(){return n}))},t.prototype.getStats=function(){var e=Array.from(this._peerConnections.values());return Promise.all(e.map((function(e){return e.getStats().then((function(t){return[e.id,t]}))}))).then((function(e){return new Map(e)}))},t}(v);function k(e){return e.createMediaStreamDestination().stream.getAudioTracks()[0]}function S(e,t){return{add:_.difference(t,e._dataTrackSenders),remove:_.difference(e._dataTrackSenders,t)}}function T(e,t){return{add:_.difference(t,e._mediaTrackSenders),remove:_.difference(e._mediaTrackSenders,t)}}var O,P={new:0,checking:1,connecting:2,connected:3,completed:4,disconnected:-1,failed:-2,closed:-3};function C(e){return e.length?(O=O||Object.keys(P).reduce((function(e,t){var n;return Object.assign(e,((n={})[P[t]]=t,n))}),{}),e.reduce((function(e,t){return O[Math.max(P[e],P[t])]}))):"new"}function E(e){e._lastIceConnectionState=e.iceConnectionState,e._iceConnectionState=C(l([],c(e._peerConnections.values())).map((function(e){return e.iceConnectionState}))),e.iceConnectionState!==e._lastIceConnectionState&&e.emit("iceConnectionStateChanged")}function R(e){e._lastConnectionState=e.connectionState,e._connectionState=C(l([],c(e._peerConnections.values())).map((function(e){return e.connectionState}))),e.connectionState!==e._lastConnectionState&&e.emit("connectionStateChanged")}e.exports=w},2132:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},d=n(1895),f=n(1608),h=f.RTCIceCandidate,v=f.RTCPeerConnection,_=f.RTCSessionDescription,m=f.getStats,y=n(1439),w=n(1425),k=w.DEFAULT_ICE_GATHERING_TIMEOUT_MS,S=w.DEFAULT_LOG_LEVEL,T=w.DEFAULT_SESSION_TIMEOUT_SEC,O=w.iceRestartBackoffConfig,P=n(1823),C=P.addOrRewriteNewTrackIds,E=P.addOrRewriteTrackIds,R=P.createCodecMapForMediaSection,j=P.disableRtx,x=P.enableDtxForOpus,L=P.filterLocalCodecs,A=P.getMediaSections,D=P.removeSSRCAttributes,I=P.revertSimulcast,M=P.setCodecPreferences,N=P.setSimulcast,V=n(1609),F=n(1571),B=F.MediaClientLocalDescFailedError,U=F.MediaClientRemoteDescFailedError,H=n(1410),W=H.buildLogLevels,Q=H.getPlatform,G=H.isChromeScreenShareTrack,z=H.oncePerTick,K=H.defer,Y=n(2138),J=n(2140),$=n(2141),X=n(2143),Z=n(1616),ee=n(1570),te=n(2144),ne=n(2145),re=y.guessBrowser(),ie=Q(),oe=/android/.test(ie),ae="chrome"===re,se="firefox"===re,ce="safari"===re,ue=0,le={open:["closed","updating"],updating:["closed","open"],closed:[]},de=function(e){function t(t,n,r,o){var c=e.call(this,"open",le)||this,l=ve(o=Object.assign({enableDscp:!1,dummyAudioMediaStreamTrack:null,isChromeScreenShareTrack:G,iceServers:[],logLevel:S,offerOptions:{},revertSimulcast:I,sessionTimeout:1e3*T,setCodecPreferences:M,setSimulcast:N,Backoff:d,IceConnectionMonitor:J,RTCIceCandidate:h,RTCPeerConnection:v,RTCSessionDescription:_,Timeout:V},o)),f=W(o.logLevel),m=o.RTCPeerConnection;!0===o.enableDscp&&(o.chromeSpecificConstraints=o.chromeSpecificConstraints||{},o.chromeSpecificConstraints.optional=o.chromeSpecificConstraints.optional||[],o.chromeSpecificConstraints.optional.push({googDscp:!0}));var y=o.log?o.log.createLog("webrtc",c):new ee("webrtc",c,f,o.loggerName),w=new m(l,o.chromeSpecificConstraints);o.dummyAudioMediaStreamTrack&&w.addTrack(o.dummyAudioMediaStreamTrack),Object.defineProperties(c,{_appliedTrackIdsToAttributes:{value:new Map,writable:!0},_dataChannels:{value:new Map},_dataTrackReceivers:{value:new Set},_descriptionRevision:{writable:!0,value:0},_didGenerateLocalCandidates:{writable:!0,value:!1},_enableDscp:{value:o.enableDscp},_encodingParameters:{value:n},_isChromeScreenShareTrack:{value:o.isChromeScreenShareTrack},_iceGatheringFailed:{value:!1,writable:!0},_iceGatheringTimeout:{value:new o.Timeout((function(){return c._handleIceGatheringTimeout()}),k,!1)},_iceRestartBackoff:{value:o.Backoff.exponential(O)},_instanceId:{value:++ue},_isIceConnectionInactive:{writable:!0,value:!1},_isIceLite:{writable:!0,value:!1},_isIceRestartBackoffInProgress:{writable:!0,value:!1},_isRestartingIce:{writable:!0,value:!1},_lastIceConnectionState:{writable:!0,value:null},_lastStableDescriptionRevision:{writable:!0,value:0},_localCandidates:{writable:!0,value:[]},_localCodecs:{value:new Set},_localCandidatesRevision:{writable:!0,value:1},_localDescriptionWithoutSimulcast:{writable:!0,value:null},_localDescription:{writable:!0,value:null},_localUfrag:{writable:!0,value:null},_log:{value:y},_eventObserver:{value:o.eventObserver},_remoteCodecMaps:{value:new Map},_rtpSenders:{value:new Map},_rtpNewSenders:{value:new Set},_iceConnectionMonitor:{value:new o.IceConnectionMonitor(w)},_mediaTrackReceivers:{value:new Set},_needsAnswer:{writable:!0,value:!1},_negotiationRole:{writable:!0,value:null},_offerOptions:{writable:!0,value:o.offerOptions},_onEncodingParametersChanged:{value:z((function(){c._needsAnswer||ye(c)}))},_peerConnection:{value:w},_preferredAudioCodecs:{value:r.audio},_preferredVideoCodecs:{value:r.video},_shouldApplyDtx:{value:r.audio.every((function(e){return"opus"!==e.codec}))||r.audio.some((function(e){var t=e.codec,n=e.dtx;return"opus"===t&&n}))},_queuedDescription:{writable:!0,value:null},_iceReconnectTimeout:{value:new o.Timeout((function(){y.debug("ICE reconnect timed out"),c.close()}),o.sessionTimeout,!1)},_recycledTransceivers:{value:{audio:[],video:[]}},_replaceTrackPromises:{value:new Map},_remoteCandidates:{writable:!0,value:new Y},_setCodecPreferences:{value:se&&oe&&r.video[0]&&"h264"!==r.video[0].codec.toLowerCase()?function(e){return e}:o.setCodecPreferences},_setSimulcast:{value:o.setSimulcast},_revertSimulcast:{value:o.revertSimulcast},_RTCIceCandidate:{value:o.RTCIceCandidate},_RTCPeerConnection:{value:o.RTCPeerConnection},_RTCSessionDescription:{value:o.RTCSessionDescription},_shouldOffer:{writable:!0,value:!1},_shouldRestartIce:{writable:!0,value:!1},_trackIdsToAttributes:{value:new Map,writable:!0},_trackMatcher:{writable:!0,value:null},_mediaTrackSenderToPublisherHints:{value:new Map},id:{enumerable:!0,value:t}}),n.on("changed",c._onEncodingParametersChanged),w.addEventListener("connectionstatechange",c._handleConnectionStateChange.bind(c)),w.addEventListener("datachannel",c._handleDataChannelEvent.bind(c)),w.addEventListener("icecandidate",c._handleIceCandidateEvent.bind(c)),w.addEventListener("iceconnectionstatechange",c._handleIceConnectionStateChange.bind(c)),w.addEventListener("icegatheringstatechange",c._handleIceGatheringStateChange.bind(c)),w.addEventListener("signalingstatechange",c._handleSignalingStateChange.bind(c)),w.addEventListener("track",c._handleTrackEvent.bind(c)),c._iceRestartBackoff.on("ready",(function(){return c._initiateIceRestart()}));var P=c;return c.on("stateChanged",(function e(t){"closed"===t&&(P.removeListener("stateChanged",e),P._dataChannels.forEach((function(e,t){P.removeDataTrackSender(t)})))})),c}return o(t,e),t.prototype.toString=function(){return"[PeerConnectionV2 #"+this._instanceId+": "+this.id+"]"},t.prototype.setEffectiveAdaptiveSimulcast=function(e){this._log.debug("Setting setEffectiveAdaptiveSimulcast: ",e),this._preferredVideoCodecs.forEach((function(t){"adaptiveSimulcast"in t&&(t.adaptiveSimulcast=e)}))},Object.defineProperty(t.prototype,"_shouldApplySimulcast",{get:function(){return!(!ae&&!ce)&&this._preferredVideoCodecs.some((function(e){return"vp8"===e.codec.toLowerCase()&&e.simulcast&&!1!==e.adaptiveSimulcast}))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"connectionState",{get:function(){return"failed"===this.iceConnectionState?"failed":this._peerConnection.connectionState||this.iceConnectionState},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"iceConnectionState",{get:function(){return this._isIceConnectionInactive&&"disconnected"===this._peerConnection.iceConnectionState||this._iceGatheringFailed?"failed":this._peerConnection.iceConnectionState},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isApplicationSectionNegotiated",{get:function(){return"closed"===this._peerConnection.signalingState||!!this._peerConnection.localDescription&&A(this._peerConnection.localDescription.sdp,"application").length>0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"_isAdaptiveSimulcastEnabled",{get:function(){var e=this._preferredVideoCodecs.find((function(e){return"adaptiveSimulcast"in e}));return e&&!0===e.adaptiveSimulcast},enumerable:!1,configurable:!0}),t.prototype._maybeUpdateEncodings=function(track,e,t){if(void 0===t&&(t=!1),"video"!==track.kind)return!1;var n=y.guessBrowser();return!!("safari"===n||"chrome"===n&&this._isAdaptiveSimulcastEnabled)&&(this._updateEncodings(track,e,t),!0)},t.prototype._updateEncodings=function(track,e,t){if(this._isChromeScreenShareTrack(track)){var n=[{scaleResolutionDownBy:1},{scaleResolutionDownBy:1}];e.forEach((function(e,i){var r=n[i];r?(e.scaleResolutionDownBy=r.scaleResolutionDownBy,t&&delete e.active):(e.active=!1,delete e.scaleResolutionDownBy)}))}else{var r=track.getSettings(),o=r.width*r.height,c=[{pixels:518400,maxActiveLayers:3},{pixels:129600,maxActiveLayers:2},{pixels:0,maxActiveLayers:1}].find((function(e){return o>=e.pixels})),l=Math.min(e.length,c.maxActiveLayers);e.forEach((function(e,i){i<l?(e.scaleResolutionDownBy=1<<l-i-1,t&&(e.active=!0)):(e.active=!1,delete e.scaleResolutionDownBy)}))}this._log.debug("_updateEncodings:",e.map((function(e,i){return"["+i+": "+e.active+", "+(e.scaleResolutionDownBy||0)+"]"})).join(", "))},t.prototype._addIceCandidate=function(e){var t=this;return Promise.resolve().then((function(){return e=new t._RTCIceCandidate(e),t._peerConnection.addIceCandidate(e)})).catch((function(n){t._log.warn("Failed to add RTCIceCandidate "+(e?'"'+e.candidate+'"':"null")+": "+n.message)}))},t.prototype._addIceCandidates=function(e){return Promise.all(e.map(this._addIceCandidate,this)).then((function(){}))},t.prototype._addOrUpdateTransceiver=function(track){var e=this,t=function(e,t){var n={audio:e._preferredAudioCodecs.map((function(e){return e.codec.toLowerCase()})),video:e._preferredVideoCodecs.map((function(e){return e.codec.toLowerCase()}))}[t],r=e._recycledTransceivers[t],o=n.find((function(t){return e._localCodecs.has(t)}));if(!o)return r.shift();var c=r.find((function(t){var n=e._remoteCodecMaps.get(t.mid);return n&&n.has(o)}));c&&r.splice(r.indexOf(c),1);return c}(this,track.kind);if(t&&t.sender){var n=t.sender.track?t.sender.track.id:null;return n&&this._log.warn("Reusing transceiver: "+t.mid+"] "+n+" => "+track.id),this._replaceTrackPromises.set(t,t.sender.replaceTrack(track).then((function(){t.direction="sendrecv"}),(function(){})).finally((function(){e._replaceTrackPromises.delete(t)}))),t}return this._peerConnection.addTransceiver(track)},t.prototype._checkIceBox=function(e){var t=he(e);if(!t)return Promise.resolve();var n=this._remoteCandidates.setUfrag(t);return this._addIceCandidates(n)},t.prototype._answer=function(e){var t=this;return Promise.resolve().then((function(){return t._negotiationRole||(t._negotiationRole="answerer"),t._setRemoteDescription(e)})).catch((function(){throw new U})).then((function(){return t._peerConnection.createAnswer()})).then((function(n){n=se?new t._RTCSessionDescription({sdp:j(n.sdp),type:n.type}):ne(n);var r=D(n.sdp,["mslabel","label"]);if(t._shouldApplySimulcast){var o=r;r=t._setSimulcast(o,t._trackIdsToAttributes),r=t._revertSimulcast(r,o,e.sdp)}return r=r.replace(/42e015/g,"42e01f"),t._setLocalDescription({type:n.type,sdp:r})})).then((function(){return t._checkIceBox(e)})).then((function(){return t._queuedDescription&&t._updateDescription(t._queuedDescription)})).then((function(){return t._queuedDescription=null,t._maybeReoffer(t._peerConnection.localDescription)})).catch((function(e){var n=e instanceof U?e:new B;throw t._publishMediaWarning({message:"Failed to _answer",code:n.code,error:e}),n}))},t.prototype._close=function(){return this._iceConnectionMonitor.stop(),"closed"!==this._peerConnection.signalingState&&(this._peerConnection.close(),this.preempt("closed"),this._encodingParameters.removeListener("changed",this._onEncodingParametersChanged),!0)},t.prototype._handleConnectionStateChange=function(){this.emit("connectionStateChanged")},t.prototype._handleDataChannelEvent=function(e){var t=this,n=e.channel,r=new $(n);this._dataTrackReceivers.add(r),n.addEventListener("close",(function(){t._dataTrackReceivers.delete(r)})),this.emit("trackAdded",r)},t.prototype._handleGlare=function(e){var t=this;return this._log.debug("Glare detected; rolling back"),this._isRestartingIce&&(this._log.debug("An ICE restart was in progress; we'll need to restart ICE again after rolling back"),this._isRestartingIce=!1,this._shouldRestartIce=!0),Promise.resolve().then((function(){return t._trackIdsToAttributes=new Map(t._appliedTrackIdsToAttributes),t._setLocalDescription({type:"rollback"})})).then((function(){return t._needsAnswer=!1,t._answer(e)})).then((function(e){return e?Promise.resolve():t._offer()}))},t.prototype._publishMediaWarning=function(e){var t=e.message,code=e.code,n=e.error,r=e.sdp;this._eventObserver.emit("event",{level:"warning",name:"error",group:"media",payload:{message:t,code:code,context:JSON.stringify({error:n.message,sdp:r})}})},t.prototype._handleIceCandidateEvent=function(e){e.candidate&&(this._log.debug("Clearing ICE gathering timeout"),this._didGenerateLocalCandidates=!0,this._iceGatheringTimeout.clear(),this._localCandidates.push(e.candidate));var t={ice:{candidates:this._isIceLite?[]:this._localCandidates.slice(),ufrag:this._localUfrag},id:this.id};e.candidate||(t.ice.complete=!0),this._isIceLite&&e.candidate||(t.ice.revision=this._localCandidatesRevision++,this.emit("candidates",t))},t.prototype._handleIceConnectionStateChange=function(){var e=this,t=this._peerConnection.iceConnectionState,n=["connected","completed"].includes(t),r=this._log;r.debug('ICE connection state is "'+t+'"'),n&&(this._iceReconnectTimeout.clear(),this._iceRestartBackoff.reset()),"failed"===this._lastIceConnectionState||"failed"!==t||this._shouldRestartIce||this._isRestartingIce?["disconnected","failed"].includes(this._lastIceConnectionState)&&n&&r.debug("ICE reconnected"):(r.warn("ICE failed"),this._initiateIceRestartBackoff()),"connected"===t?(this._isIceConnectionInactive=!1,this._iceConnectionMonitor.start((function(){e._iceConnectionMonitor.stop(),e._shouldRestartIce||e._isRestartingIce||(r.warn("ICE Connection Monitor detected inactivity"),e._isIceConnectionInactive=!0,e._initiateIceRestartBackoff(),e.emit("iceConnectionStateChanged"),e.emit("connectionStateChanged"))}))):["disconnected","completed"].includes(t)||(this._iceConnectionMonitor.stop(),this._isIceConnectionInactive=!1),this._lastIceConnectionState=t,this.emit("iceConnectionStateChanged")},t.prototype._handleIceGatheringTimeout=function(){this._log.warn("ICE failed to gather any local candidates"),this._iceGatheringFailed=!0,this._initiateIceRestartBackoff(),this.emit("iceConnectionStateChanged"),this.emit("connectionStateChanged")},t.prototype._handleIceGatheringStateChange=function(){var e=this._peerConnection.iceGatheringState,t=this._log;t.debug('ICE gathering state is "'+e+'"');var n=this._iceGatheringTimeout,r=n.delay,o=n.isSet;"gathering"!==e||this._didGenerateLocalCandidates||o||(t.debug("Starting ICE gathering timeout: "+r),this._iceGatheringFailed=!1,this._iceGatheringTimeout.start())},t.prototype._handleSignalingStateChange=function(){"stable"===this._peerConnection.signalingState&&(this._appliedTrackIdsToAttributes=new Map(this._trackIdsToAttributes))},t.prototype._handleTrackEvent=function(e){var t=this,n=this._peerConnection.remoteDescription?this._peerConnection.remoteDescription.sdp:null;this._trackMatcher=this._trackMatcher||new te,this._trackMatcher.update(n);var r=e.track,o=this._trackMatcher.match(e)||r.id,c=new X(o,r);this._mediaTrackReceivers.forEach((function(e){e.track.id===c.track.id&&t._mediaTrackReceivers.delete(e)})),this._mediaTrackReceivers.add(c),r.addEventListener("ended",(function(){return t._mediaTrackReceivers.delete(c)})),this.emit("trackAdded",c)},t.prototype._initiateIceRestart=function(){if("closed"!==this._peerConnection.signalingState){var e=this._log;e.warn("Attempting to restart ICE"),this._didGenerateLocalCandidates=!1,this._isIceRestartBackoffInProgress=!1,this._shouldRestartIce=!0;var t=this._iceReconnectTimeout,n=t.delay;t.isSet||(e.debug("Starting ICE reconnect timeout: "+n),this._iceReconnectTimeout.start()),this.offer().catch((function(t){e.error("offer failed in _initiateIceRestart with: "+t.message)}))}},t.prototype._initiateIceRestartBackoff=function(){"closed"===this._peerConnection.signalingState||this._isIceRestartBackoffInProgress||(this._log.warn("An ICE restart has been scheduled"),this._isIceRestartBackoffInProgress=!0,this._iceRestartBackoff.backoff())},t.prototype._maybeReoffer=function(e){var t=this._shouldOffer;if(e&&e.sdp){var n=this._peerConnection.getSenders().filter((function(e){return e.track}));t=["audio","video"].reduce((function(t,r){var o=A(e.sdp,r,"(sendrecv|sendonly)"),c=n.filter(_e.bind(null,r));return t||o.length<c.length}),t);var r=this._dataChannels.size>0,o=A(e.sdp,"application").length>0;t=t||r&&!o}return(t?this._offer():Promise.resolve()).then((function(){return t}))},t.prototype._offer=function(){var e=this,t=Object.assign({},this._offerOptions);return this._needsAnswer=!0,this._shouldRestartIce&&(this._shouldRestartIce=!1,this._isRestartingIce=!0,t.iceRestart=!0),Promise.all(this._replaceTrackPromises.values()).then((function(){return e._peerConnection.createOffer(t)})).catch((function(t){var n=new B;throw e._publishMediaWarning({message:"Failed to create offer",code:n.code,error:t}),n})).then((function(t){t=se?new e._RTCSessionDescription({sdp:j(t.sdp),type:t.type}):ne(t);var n=D(t.sdp,["mslabel","label"]);n=e._peerConnection.remoteDescription?L(n,e._peerConnection.remoteDescription.sdp):n;var r=e._setCodecPreferences(n,e._preferredAudioCodecs,e._preferredVideoCodecs);return e._shouldOffer=!1,e._negotiationRole||(e._negotiationRole="offerer"),e._shouldApplySimulcast&&(e._localDescriptionWithoutSimulcast={type:"offer",sdp:r},r=e._setSimulcast(r,e._trackIdsToAttributes)),e._setLocalDescription({type:"offer",sdp:r})}))},t.prototype._getMediaTrackSenderId=function(e){var t=Array.from(this._rtpSenders.keys()).find((function(t){return t.track.id===e}));return t?t.id:e},t.prototype._addOrRewriteLocalTrackIds=function(e){var t=this,n=this._peerConnection.getTransceivers().filter((function(e){var t=e.sender;return!e.stopped&&t&&t.track})),r=n.filter((function(e){return e.mid})),o=new Map(r.map((function(e){var n=e.mid,r=e.sender;return[n,t._getMediaTrackSenderId(r.track.id)]}))),c=E(e.sdp,o),l=n.filter((function(e){return!e.mid})),d=new Map(["audio","video"].map((function(e){return[e,l.filter((function(t){return t.sender.track.kind===e})).map((function(e){var n=e.sender;return t._getMediaTrackSenderId(n.track.id)}))]}))),f=C(c,o,d);return new this._RTCSessionDescription({sdp:f,type:e.type})},t.prototype._rollbackAndApplyOffer=function(e){var t=this;return this._setLocalDescription({type:"rollback"}).then((function(){return t._setLocalDescription(e)}))},t.prototype._setLocalDescription=function(e){var t=this;return"rollback"!==e.type&&this._shouldApplyDtx&&(e=new this._RTCSessionDescription({sdp:x(e.sdp),type:e.type})),this._peerConnection.setLocalDescription(e).catch((function(n){t._log.warn('Calling setLocalDescription with an RTCSessionDescription of type "'+e.type+'" failed with the error "'+n.message+'".',n);var r=new B,o={message:'Calling setLocalDescription with an RTCSessionDescription of type "'+e.type+'" failed',code:r.code,error:n};throw e.sdp&&(t._log.warn("The SDP was "+e.sdp),o.sdp=e.sdp),t._publishMediaWarning(o),r})).then((function(){"rollback"!==e.type&&(t._localDescription=t._addOrRewriteLocalTrackIds(e),t._shouldApplyDtx&&(t._localDescription=new t._RTCSessionDescription({sdp:x(t._localDescription.sdp,[]),type:t._localDescription.type})),t._localCandidates=[],"offer"===e.type?t._descriptionRevision++:"answer"===e.type&&(t._lastStableDescriptionRevision=t._descriptionRevision,me(t)),t._localUfrag=he(e),t.emit("description",t.getState()))}))},t.prototype._setRemoteDescription=function(e){var t=this;return e.sdp&&(e.sdp=this._setCodecPreferences(e.sdp,this._preferredAudioCodecs,this._preferredVideoCodecs),this._shouldApplyDtx?e.sdp=x(e.sdp):e.sdp=x(e.sdp,[]),se&&(e.sdp=e.sdp.replace(/a=msid:[^ ]+ /g,"a=msid:- ")),this._peerConnection.remoteDescription||(this._isIceLite=/a=ice-lite/.test(e.sdp))),e=new this._RTCSessionDescription(e),Promise.resolve().then((function(){if("answer"===e.type&&t._localDescriptionWithoutSimulcast){var n=t._preferredVideoCodecs.find((function(e){return"adaptiveSimulcast"in e})),r=!!n&&!1===n.adaptiveSimulcast,o=t._revertSimulcast(t._localDescription.sdp,t._localDescriptionWithoutSimulcast.sdp,e.sdp,r);if(t._localDescriptionWithoutSimulcast=null,o!==t._localDescription.sdp)return t._rollbackAndApplyOffer({type:t._localDescription.type,sdp:o})}})).then((function(){return t._peerConnection.setRemoteDescription(e)})).then((function(){"answer"===e.type&&(t._isRestartingIce&&(t._log.debug("An ICE restart was in-progress and is now completed"),t._isRestartingIce=!1),me(t))}),(function(n){throw t._log.warn('Calling setRemoteDescription with an RTCSessionDescription of type "'+e.type+'" failed with the error "'+n.message+'".',n),e.sdp&&t._log.warn("The SDP was "+e.sdp),n}))},t.prototype._updateDescription=function(e){var t=this;switch(e.type){case"answer":case"pranswer":if(e.revision!==this._descriptionRevision||"have-local-offer"!==this._peerConnection.signalingState)return Promise.resolve();this._descriptionRevision=e.revision;break;case"close":return this._close();case"create-offer":return e.revision<=this._lastStableDescriptionRevision?Promise.resolve():this._needsAnswer?(this._queuedDescription=e,Promise.resolve()):(this._descriptionRevision=e.revision,this._offer());case"offer":return e.revision<=this._lastStableDescriptionRevision||"closed"===this._peerConnection.signalingState?Promise.resolve():"have-local-offer"===this._peerConnection.signalingState?this._needsAnswer&&0===this._lastStableDescriptionRevision?(this._queuedDescription=e,Promise.resolve()):(this._descriptionRevision=e.revision,this._handleGlare(e)):(this._descriptionRevision=e.revision,this._answer(e).then((function(){})))}var n=e.revision;return Promise.resolve().then((function(){return t._setRemoteDescription(e)})).catch((function(n){var r=new U;throw t._publishMediaWarning({message:'Calling setRemoteDescription with an RTCSessionDescription of type "'+e.type+'" failed',code:r.code,error:n,sdp:e.sdp}),r})).then((function(){return t._lastStableDescriptionRevision=n,t._needsAnswer=!1,t._checkIceBox(e)})).then((function(){return t._queuedDescription&&t._updateDescription(t._queuedDescription)})).then((function(){return t._queuedDescription=null,t._maybeReoffer(t._peerConnection.localDescription).then((function(){}))}))},t.prototype._updateIce=function(e){var t=this._remoteCandidates.update(e);return this._addIceCandidates(t)},t.prototype.addDataTrackSender=function(e){if(!this._dataChannels.has(e))try{var t={ordered:e.ordered};null!==e.maxPacketLifeTime&&(t.maxPacketLifeTime=e.maxPacketLifeTime),null!==e.maxRetransmits&&(t.maxRetransmits=e.maxRetransmits);var n=this._peerConnection.createDataChannel(e.id,t);e.addDataChannel(n),this._dataChannels.set(e,n)}catch(t){this._log.warn('Error creating an RTCDataChannel for DataTrack "'+e.id+'": '+t.message)}},t.prototype._handleQueuedPublisherHints=function(){var e=this;"stable"===this._peerConnection.signalingState&&this._mediaTrackSenderToPublisherHints.forEach((function(t,n){var r=t.deferred,o=t.encodings;e._mediaTrackSenderToPublisherHints.delete(n),e._setPublisherHint(n,o).then((function(e){return r.resolve(e)})).catch((function(e){return r.reject(e)}))}))},t.prototype._setPublisherHint=function(e,t){var n=this;if(se)return Promise.resolve("COULD_NOT_APPLY_HINT");this._mediaTrackSenderToPublisherHints.has(e)&&(this._mediaTrackSenderToPublisherHints.get(e).deferred.resolve("REQUEST_SKIPPED"),this._mediaTrackSenderToPublisherHints.delete(e));var r=this._rtpSenders.get(e);if(!r)return this._log.warn("Could not apply publisher hint because RTCRtpSender was not found"),Promise.resolve("UNKNOWN_TRACK");if("closed"===this._peerConnection.signalingState)return this._log.warn('Could not apply publisher hint because signalingState was "closed"'),Promise.resolve("COULD_NOT_APPLY_HINT");if("stable"!==this._peerConnection.signalingState){this._log.debug("Queuing up publisher hint because signalingState:",this._peerConnection.signalingState);var o=K();return this._mediaTrackSenderToPublisherHints.set(e,{deferred:o,encodings:t}),o.promise}var c=r.getParameters();return null!==t&&t.forEach((function(e){var t=e.enabled,r=e.layer_index;c.encodings.length>r?(n._log.debug("layer:"+r+", active:"+c.encodings[r].active+" => "+t),c.encodings[r].active=t):n._log.warn("invalid layer:"+r+", active:"+t)})),this._maybeUpdateEncodings(r.track,c.encodings,null===t),r.setParameters(c).then((function(){return"OK"})).catch((function(e){return n._log.error("Failed to apply publisher hints:",e),"COULD_NOT_APPLY_HINT"}))},t.prototype.addMediaTrackSender=function(e){var t=this;if("closed"!==this._peerConnection.signalingState&&!this._rtpSenders.has(e)){var n=this._addOrUpdateTransceiver(e.track).sender;e.addSender(n,(function(n){return t._setPublisherHint(e,n)})),this._rtpNewSenders.add(n),this._rtpSenders.set(e,n)}},t.prototype.close=function(){this._close()&&(this._descriptionRevision++,this._localDescription={type:"close"},this.emit("description",this.getState()))},t.prototype.getTrackReceivers=function(){return Array.from(this._dataTrackReceivers).concat(Array.from(this._mediaTrackReceivers))},t.prototype.getState=function(){if(!this._localDescription)return null;var e="answer"===this._localDescription.type?this._lastStableDescriptionRevision:this._descriptionRevision,t={type:this._localDescription.type,revision:e};return this._localDescription.sdp&&(t.sdp=this._localDescription.sdp),{description:t,id:this.id}},t.prototype.offer=function(){var e=this;return this._needsAnswer||this._isRestartingIce?(this._shouldOffer=!0,Promise.resolve()):this.bracket("offering",(function(t){return e.transition("updating",t),(e._needsAnswer||e._isRestartingIce?Promise.resolve():e._offer()).then((function(){e.tryTransition("open",t)}),(function(n){throw e.tryTransition("open",t),n}))}))},t.prototype.removeDataTrackSender=function(e){var t=this._dataChannels.get(e);t&&(e.removeDataChannel(t),this._dataChannels.delete(e),t.close())},t.prototype.removeMediaTrackSender=function(e){var t=this._rtpSenders.get(e);if(t){if("closed"!==this._peerConnection.signalingState&&this._peerConnection.removeTrack(t),e.removeSender(t),this._mediaTrackSenderToPublisherHints.has(e))this._mediaTrackSenderToPublisherHints.get(e).deferred.resolve("UNKNOWN_TRACK"),this._mediaTrackSenderToPublisherHints.delete(e);this._rtpNewSenders.delete(t),this._rtpSenders.delete(e)}},t.prototype.setConfiguration=function(e){"function"==typeof this._peerConnection.setConfiguration&&this._peerConnection.setConfiguration(ve(e))},t.prototype.setIceReconnectTimeout=function(e){return this._iceReconnectTimeout.setDelay(e),this._log.debug("Updated ICE reconnection timeout period:",this._iceReconnectTimeout.delay),this},t.prototype.update=function(e){var t=this;return this.bracket("updating",(function(n){if("closed"===t.state)return Promise.resolve();t.transition("updating",n);var r=[];return e.ice&&r.push(t._updateIce(e.ice)),e.description&&r.push(t._updateDescription(e.description)),Promise.all(r).then((function(){t.tryTransition("open",n)}),(function(e){throw t.tryTransition("open",n),e}))}))},t.prototype.getStats=function(){var e=this;return m(this._peerConnection).then((function(t){return function(e,t){return Object.assign(t,{remoteAudioTrackStats:t.remoteAudioTrackStats.map((function(t){return fe(e,t)})),remoteVideoTrackStats:t.remoteVideoTrackStats.map((function(t){return fe(e,t)})),localAudioTrackStats:t.localAudioTrackStats.map((function(t){return pe(e,t)})),localVideoTrackStats:t.localVideoTrackStats.map((function(t){return pe(e,t)}))})}(e,t)}))},t}(Z);function pe(e,t){var n=e._getMediaTrackSenderId(t.trackId);return Object.assign(t,{trackId:n})}function fe(e,t){var n=l([],c(e._mediaTrackReceivers)).find((function(e){return e.track.id===t.trackId})),r=n?n.id:null;return Object.assign(t,{trackId:r})}function he(e){if(e.sdp){var t=e.sdp.match(/^a=ice-ufrag:([a-zA-Z0-9+/]+)/m);if(t)return t[1]}return null}function ve(e){return Object.assign({bundlePolicy:"max-bundle",rtcpMuxPolicy:"require"},e)}function _e(e,t){var track=t.track;return track&&track.kind===e&&"ended"!==track.readyState}function me(e){!function(e){e._recycledTransceivers.audio=[],e._recycledTransceivers.video=[],e._peerConnection.getTransceivers().forEach((function(t){if(function(e,t){return!e.stopped&&!t._replaceTrackPromises.has(e)&&["inactive","recvonly"].includes(e.direction)}(t,e)){var track=t.receiver.track;e._recycledTransceivers[track.kind].push(t)}}))}(e),function(e){var t=e._peerConnection.localDescription;t&&t.sdp&&A(t.sdp).forEach((function(section){R(section).forEach((function(t,n){return e._localCodecs.add(n)}))}))}(e),function(e){var t=e._peerConnection.remoteDescription;t&&t.sdp&&A(t.sdp).forEach((function(section){var t=section.match(/^a=mid:(.+)$/m);if(t&&t[1]){var n=t[1],r=R(section);e._remoteCodecMaps.set(n,r)}}))}(e),ye(e).then((function(){e._handleQueuedPublisherHints()}))}function ye(e){var t=e._encodingParameters,n=t.maxAudioBitrate,r=t.maxVideoBitrate,o=new Map([["audio",n],["video",r]]),c=[];return e._peerConnection.getSenders().filter((function(e){return e.track})).forEach((function(t){var n=o.get(t.track.kind),r=t.getParameters();null===n||0===n?function(e){Array.isArray(e.encodings)&&e.encodings.forEach((function(e){return delete e.maxBitrate}))}(r):e._isChromeScreenShareTrack(t.track)?e._log.warn("Not setting maxBitrate for "+t.track.kind+" Track "+t.track.id+" because it appears to be screen share track: "+t.track.label):function(e,t){se?e.encodings=[{maxBitrate:t}]:e.encodings.forEach((function(e){e.maxBitrate=t}))}(r,n),!se&&e._enableDscp&&r.encodings.length>0&&(r.encodings[0].networkPriority="high");var l=e._rtpNewSenders.has(t);e._maybeUpdateEncodings(t.track,r.encodings,l),e._rtpNewSenders.delete(t);var d=t.setParameters(r).catch((function(n){e._log.warn("Error while setting encodings parameters for "+t.track.kind+" Track "+t.track.id+": "+(n.message||n.name))}));c.push(d)})),Promise.all(c)}e.exports=de},2133:function(e,t,n){var r=n(1521),o=e.exports=n(2134);function c(e,t,n,o){n=n||"";var c=new e(r.format.apply(this,[n].concat(o)));throw Error.captureStackTrace(c,t),c}function l(e,t,n){c(o.IllegalArgumentError,e,t,n)}function d(e,t,n){c(o.IllegalStateError,e,t,n)}function f(e){var s=typeof e;if("object"==s){if(!e)return"null";if(e instanceof Array)return"array"}return s}function h(e){return function(t,n){var r=f(t);if(r==e)return t;l(arguments.callee,n||'Expected "'+e+'" but got "'+r+'".',Array.prototype.slice.call(arguments,2))}}e.exports.checkArgument=function(e,t){e||l(arguments.callee,t,Array.prototype.slice.call(arguments,2))},e.exports.checkState=function(e,t){e||d(arguments.callee,t,Array.prototype.slice.call(arguments,2))},e.exports.checkIsDef=function(e,t){if(void 0!==e)return e;l(arguments.callee,t||"Expected value to be defined but was undefined.",Array.prototype.slice.call(arguments,2))},e.exports.checkIsDefAndNotNull=function(e,t){if(null!=e)return e;l(arguments.callee,t||'Expected value to be defined and not null but got "'+f(e)+'".',Array.prototype.slice.call(arguments,2))},e.exports.checkIsString=h("string"),e.exports.checkIsArray=h("array"),e.exports.checkIsNumber=h("number"),e.exports.checkIsBoolean=h("boolean"),e.exports.checkIsFunction=h("function"),e.exports.checkIsObject=h("object")},2134:function(e,t,n){var r=n(1521);function o(e){Error.call(this,e),this.message=e}function c(e){Error.call(this,e),this.message=e}r.inherits(o,Error),o.prototype.name="IllegalArgumentError",r.inherits(c,Error),c.prototype.name="IllegalStateError",e.exports.IllegalStateError=c,e.exports.IllegalArgumentError=o},2135:function(e,t,n){var r=n(1521),o=n(1822),c=n(1897);function l(e){c.call(this,e),this.backoffDelay_=0,this.nextBackoffDelay_=this.getInitialDelay(),this.factor_=l.DEFAULT_FACTOR,e&&void 0!==e.factor&&(o.checkArgument(e.factor>1,"Exponential factor should be greater than 1 but got %s.",e.factor),this.factor_=e.factor)}r.inherits(l,c),l.DEFAULT_FACTOR=2,l.prototype.next_=function(){return this.backoffDelay_=Math.min(this.nextBackoffDelay_,this.getMaxDelay()),this.nextBackoffDelay_=this.backoffDelay_*this.factor_,this.backoffDelay_},l.prototype.reset_=function(){this.backoffDelay_=0,this.nextBackoffDelay_=this.getInitialDelay()},e.exports=l},2136:function(e,t,n){var r=n(373),o=n(1822),c=n(1521),l=n(1896),d=n(1898);function f(e,t,n){r.EventEmitter.call(this),o.checkIsFunction(e,"Expected fn to be a function."),o.checkIsArray(t,"Expected args to be an array."),o.checkIsFunction(n,"Expected callback to be a function."),this.function_=e,this.arguments_=t,this.callback_=n,this.lastResult_=[],this.numRetries_=0,this.backoff_=null,this.strategy_=null,this.failAfter_=-1,this.retryPredicate_=f.DEFAULT_RETRY_PREDICATE_,this.state_=f.State_.PENDING}c.inherits(f,r.EventEmitter),f.State_={PENDING:0,RUNNING:1,COMPLETED:2,ABORTED:3},f.DEFAULT_RETRY_PREDICATE_=function(e){return!0},f.prototype.isPending=function(){return this.state_==f.State_.PENDING},f.prototype.isRunning=function(){return this.state_==f.State_.RUNNING},f.prototype.isCompleted=function(){return this.state_==f.State_.COMPLETED},f.prototype.isAborted=function(){return this.state_==f.State_.ABORTED},f.prototype.setStrategy=function(e){return o.checkState(this.isPending(),"FunctionCall in progress."),this.strategy_=e,this},f.prototype.retryIf=function(e){return o.checkState(this.isPending(),"FunctionCall in progress."),this.retryPredicate_=e,this},f.prototype.getLastResult=function(){return this.lastResult_.concat()},f.prototype.getNumRetries=function(){return this.numRetries_},f.prototype.failAfter=function(e){return o.checkState(this.isPending(),"FunctionCall in progress."),this.failAfter_=e,this},f.prototype.abort=function(){this.isCompleted()||this.isAborted()||(this.isRunning()&&this.backoff_.reset(),this.state_=f.State_.ABORTED,this.lastResult_=[new Error("Backoff aborted.")],this.emit("abort"),this.doCallback_())},f.prototype.start=function(e){o.checkState(!this.isAborted(),"FunctionCall is aborted."),o.checkState(this.isPending(),"FunctionCall already started.");var t=this.strategy_||new d;this.backoff_=e?e(t):new l(t),this.backoff_.on("ready",this.doCall_.bind(this,!0)),this.backoff_.on("fail",this.doCallback_.bind(this)),this.backoff_.on("backoff",this.handleBackoff_.bind(this)),this.failAfter_>0&&this.backoff_.failAfter(this.failAfter_),this.state_=f.State_.RUNNING,this.doCall_(!1)},f.prototype.doCall_=function(e){e&&this.numRetries_++;var t=["call"].concat(this.arguments_);r.EventEmitter.prototype.emit.apply(this,t);var n=this.handleFunctionCallback_.bind(this);this.function_.apply(null,this.arguments_.concat(n))},f.prototype.doCallback_=function(){this.callback_.apply(null,this.lastResult_)},f.prototype.handleFunctionCallback_=function(){if(!this.isAborted()){var e=Array.prototype.slice.call(arguments);this.lastResult_=e,r.EventEmitter.prototype.emit.apply(this,["callback"].concat(e));var t=e[0];t&&this.retryPredicate_(t)?this.backoff_.backoff(t):(this.state_=f.State_.COMPLETED,this.doCallback_())}},f.prototype.handleBackoff_=function(e,t,n){this.emit("backoff",e,t,n)},e.exports=f},2137:function(e,t,n){"use strict";var r=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},o=n(1410),c=o.difference,l=o.flatMap;function d(){return String(Math.floor(4294967295*Math.random()))}var f=function(){function e(e,t,n){Object.defineProperties(this,{cName:{enumerable:!0,value:n},isSimulcastEnabled:{enumerable:!0,value:!1,writable:!0},primarySSRCs:{enumerable:!0,value:new Set},rtxPairs:{enumerable:!0,value:new Map},streamId:{enumerable:!0,value:t},trackId:{enumerable:!0,value:e}})}return e.prototype.addSimulcastSSRCs=function(){if(!this.isSimulcastEnabled){var e=[d(),d()];e.forEach((function(e){this.primarySSRCs.add(e)}),this),this.rtxPairs.size&&e.forEach((function(e){this.rtxPairs.set(d(),e)}),this)}},e.prototype.addSSRC=function(e,t,n){t?this.rtxPairs.set(e,t):this.primarySSRCs.add(e),this.isSimulcastEnabled=this.isSimulcastEnabled||n},e.prototype.toSdpLines=function(e){var t=this,n=e?[]:Array.from(this.rtxPairs.entries()).map((function(e){return e.reverse()})),r=Array.from(this.primarySSRCs.values()),o=n.length?l(n):r,c=l(o,(function(e){return["a=ssrc:"+e+" cname:"+t.cName,"a=ssrc:"+e+" msid:"+t.streamId+" "+t.trackId]})),d=n.map((function(e){return"a=ssrc-group:FID "+e.join(" ")})),f=["a=ssrc-group:SIM "+r.join(" ")];return d.concat(c).concat(f)},e}();function h(section,pattern){return(section.match(new RegExp(pattern,"gm"))||[]).map((function(e){return(e.match(new RegExp(pattern))||[]).slice(1)}))}function v(section){var e=function(section){return new Set(l(h(section,"^a=ssrc-group:SIM ([0-9]+) ([0-9]+) ([0-9]+)$")))}(section),t=function(section){return new Map(h(section,"^a=ssrc-group:FID ([0-9]+) ([0-9]+)$").map((function(e){return e.reverse()})))}(section);return function(section){var e=r(l(h(section,"^a=msid:(.+) (.+)$")),2),t=e[0],n=e[1];return l(h(section,"^a=ssrc:(.+) cname:.+$")).map((function(e){return[e,t,n]}))}(section).reduce((function(n,r){var o=r[0],c=r[1],l=r[2],d=n.get(l)||new f(l,c,function(section,e,t){var pattern="a=ssrc:"+e+" "+t+":(.+)";return section.match(new RegExp(pattern))[1]}(section,o,"cname")),h=t.get(o)||null;return d.addSSRC(o,h,e.has(o)),n.set(l,d)}),new Map)}e.exports=function(section,e){var t=v(section),n=Array.from(t.keys()),r=Array.from(e.keys()),o=c(n,r),d=c(r,n);l(o,(function(e){return t.get(e)})).forEach((function(t){t.addSimulcastSSRCs(),e.set(t.trackId,t)})),r=Array.from(e.keys());var f=c(r,d),h=l(f,(function(t){return e.get(t)})),_=!section.match(/a=rtpmap:[0-9]+ rtx/),m=l(h,(function(e){return e.toSdpLines(_)})),y=l(new Set(section.split("\r\n").concat(m))),w="a=x-google-flag:conference";return section.match(w)||y.push(w),y.join("\r\n")}},2138:function(e,t,n){"use strict";var r=n(2139),o=function(){function e(){Object.defineProperties(this,{_filter:{value:new r({getKey:function(e){return e.ufrag},isLessThanOrEqualTo:function(a,b){return a.revision<=b.revision}})},_ufrag:{writable:!0,value:null},ufrag:{enumerable:!0,get:function(){return this._ufrag}}})}return e.prototype.setUfrag=function(e){this._ufrag=e;var t=this._filter.toMap().get(e);return t?t.candidates:[]},e.prototype.update=function(e){e.candidates=e.candidates||[];var t=this._filter.toMap().get(e.ufrag),n=t?t.candidates:[];return this._filter.update(e)&&this._ufrag===e.ufrag?e.candidates.slice(n.length):[]},e}();e.exports=o},2139:function(e,t,n){"use strict";var r=function(){function e(e){e=Object.assign({getKey:function(a){return a},getValue:function(a){return a},isLessThanOrEqualTo:function(a,b){return a<=b}},e),Object.defineProperties(this,{_getKey:{value:e.getKey},_getValue:{value:e.getValue},_isLessThanOrEqualTo:{value:e.isLessThanOrEqualTo},_map:{value:new Map}})}return e.prototype.toMap=function(){return new Map(this._map)},e.prototype.updateAndFilter=function(e){return e.filter(this.update,this)},e.prototype.update=function(e){var t=this._getKey(e),n=this._getValue(e);return(!this._map.has(t)||!this._isLessThanOrEqualTo(n,this._map.get(t)))&&(this._map.set(t,n),!0)},e}();e.exports=r},2140:function(e,t,n){"use strict";var r=n(1425),o=r.ICE_ACTIVITY_CHECK_PERIOD_MS,c=r.ICE_INACTIVITY_THRESHOLD_MS,l=function(){function e(e,t){t=Object.assign({activityCheckPeriodMs:o,inactivityThresholdMs:c},t),Object.defineProperties(this,{_activityCheckPeriodMs:{value:t.activityCheckPeriodMs},_inactivityThresholdMs:{value:t.inactivityThresholdMs},_lastActivity:{value:null,writable:!0},_peerConnection:{value:e},_timer:{value:null,writable:!0},_onIceConnectionStateChanged:{value:null,writable:!0}})}return e.prototype._getActivePairStat=function(e){return Array.from(e.values()).find((function(e){return"candidate-pair"===e.type&&e.nominated}))||{bytesReceived:0,timestamp:Math.round((new Date).getTime())}},e.prototype._getIceConnectionStats=function(){var e=this;return this._peerConnection.getStats().then((function(t){return e._getActivePairStat(t)})).catch((function(){return null}))},e.prototype._scheduleInactivityCallback=function(e){var t=this;e&&null===this._onIceConnectionStateChanged?(this._onIceConnectionStateChanged=function(){"disconnected"===t._peerConnection.iceConnectionState&&e()},this._peerConnection.addEventListener("iceconnectionstatechange",this._onIceConnectionStateChanged)):!e&&this._onIceConnectionStateChanged&&(this._peerConnection.removeEventListener("iceconnectionstatechange",this._onIceConnectionStateChanged),this._onIceConnectionStateChanged=null)},e.prototype.start=function(e){var t=this;this.stop(),this._timer=setInterval((function(){t._getIceConnectionStats().then((function(n){n&&(t._lastActivity&&t._lastActivity.bytesReceived===n.bytesReceived||(t._lastActivity=n,t._scheduleInactivityCallback(null)),n.timestamp-t._lastActivity.timestamp>=t._inactivityThresholdMs&&("disconnected"===t._peerConnection.iceConnectionState?e():null===t._onIceConnectionStateChanged&&t._scheduleInactivityCallback(e)))}))}),this._activityCheckPeriodMs)},e.prototype.stop=function(){this._scheduleInactivityCallback(null),null!==this._timer&&(clearInterval(this._timer),this._timer=null,this._lastActivity=null)},e}();e.exports=l},2141:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1881),l=n(2142),d=function(e){function t(t){var n=e.call(this,t.label,t.maxPacketLifeTime,t.maxRetransmits,t.ordered)||this;return Object.defineProperties(n,{_dataChannel:{value:t}}),t.binaryType="arraybuffer",t.addEventListener("message",(function(e){n.emit("message",e.data)})),t.addEventListener("close",(function(){n.emit("close")})),n}return o(t,e),t.prototype.stop=function(){this._dataChannel.close(),e.prototype.stop.call(this)},t.prototype.toDataTransport=function(){return new l(this._dataChannel)},t}(c);e.exports=d},2142:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t){var n=e.call(this)||this;return Object.defineProperties(n,{_dataChannel:{value:t},_messageQueue:{value:[]}}),t.addEventListener("open",(function(){n._messageQueue.splice(0).forEach((function(e){return n._publish(e)}))})),t.addEventListener("message",(function(e){var data=e.data;try{var t=JSON.parse(data);n.emit("message",t)}catch(e){}})),n.publish({type:"ready"}),n}return o(t,e),t.prototype._publish=function(e){var data=JSON.stringify(e);try{this._dataChannel.send(data)}catch(e){}},t.prototype.publish=function(e){var t=this._dataChannel;return"closing"!==t.readyState&&"closed"!==t.readyState&&("connecting"===t.readyState?(this._messageQueue.push(e),!0):(this._publish(e),!0))},t}(n(373).EventEmitter);e.exports=c},2143:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){return e.call(this,t,n)||this}return o(t,e),t}(n(1878));e.exports=c},2144:function(e,t,n){"use strict";var r=n(1823).getMediaSections,o=function(){function e(){Object.defineProperties(this,{_midsToTrackIds:{value:new Map,writable:!0}})}return e.prototype.match=function(e){return this._midsToTrackIds.get(e.transceiver.mid)||null},e.prototype.update=function(e){var t=r(e,"(audio|video)");this._midsToTrackIds=t.reduce((function(e,section){var t=section.match(/^a=mid:(.+)$/m)||[],n=section.match(/^a=msid:.+ (.+)$/m)||[],r=t[1],o=n[1];return r&&o?e.set(r,o):e}),this._midsToTrackIds)},e}();e.exports=o},2145:function(e,t,n){"use strict";var r=n(1608).RTCSessionDescription,o=n(1823),c=o.createPtToCodecName,l=o.getMediaSections;function d(e){var t=c(e);e=function(e,t){return Array.from(t.keys()).reduce((function(section,e){var n=new RegExp("^a=rtpmap:"+e+" rtx.+$","gm");return(section.match(n)||[]).slice("rtx"===t.get(e)?1:0).reduce((function(section,t){var n=new RegExp("\r\n"+t),r=new RegExp("\r\na=fmtp:"+e+" apt=[0-9]+");return section.replace(n,"").replace(r,"")}),section)}),e)}(e,t);var n=function(e){var t=new Map;return e.forEach((function(e,n){var r=t.get(e)||new Set;return t.set(e,r.add(n))})),t}(t),r=n.get("rtx")||new Set,o=new Set,l=function(e,t){var n=Array.from(e).reduce((function(e,t){var n=t[0],r=t[1],o=e.get(r)||new Set;return e.set(r,o.add(n))}),new Map);return Array.from(n).reduce((function(e,n){var r=n[0],o=Array.from(n[1]);return o.length>1?(o.forEach((function(e){t.add(e)})),e):e.set(r,o[0])}),new Map)}(function(e,t,n,r){return Array.from(n).reduce((function(n,o){var c=new RegExp("a=fmtp:"+o+" apt=(\\d+)"),l=e.match(c);if(!l)return r.add(o),n;var d=Number.parseInt(l[1]);return t.has(d)?"rtx"===t.get(d)?(r.add(o),n):n.set(o,d):(r.add(o),n)}),new Map)}(e,t,r,o),o),d=Array.from(o);return["h264","vp8","vp9"].reduce((function(e,t){var r=n.get(t)||new Set;return Array.from(r).reduce((function(e,t){return l.has(t)?e:e.add(t)}),e)}),new Set).forEach((function(t){if(d.length){var n=d.shift();e=function(e,t,n){return e.endsWith("\r\n")?e+"a=fmtp:"+t+" apt="+n+"\r\n":e+"\r\na=fmtp:"+t+" apt="+n}(e=f(e,n),n,t)}})),d.forEach((function(t){e=function(e,t){var pattern=new RegExp("a=rtpmap:"+t+".*\r\n","gm");return e.replace(pattern,"")}(e=f(e,t),t)})),e}function f(e,t){var pattern=new RegExp("a=fmtp:"+t+".*\r\n","gm");return e.replace(pattern,"")}e.exports=function(e){var t,n,o={type:e.type};return"rollback"!==e.type&&(o.sdp=(t=e.sdp,n=l(t),[t.split("\r\nm=")[0]].concat(n.map(d)).join("\r\n"))),new r(o)}},2146:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},d=function(e){function t(){var t=e.call(this)||this;return Object.defineProperties(t,{_queuedEvents:{value:new Map}}),t}return o(t,e),t.prototype.dequeue=function(e){var t=this,n=!0;if(!e)return this._queuedEvents.forEach((function(e,t){n=this.dequeue(t)&&n}),this),n;var r=this._queuedEvents.get(e)||[];return this._queuedEvents.delete(e),r.reduce((function(n,r){return t.emit.apply(t,l([],c([e].concat(r))))&&n}),n)},t.prototype.queue=function(){var e=[].slice.call(arguments);if(this.emit.apply(this,l([],c(e))))return!0;var t=e[0];return this._queuedEvents.has(t)||this._queuedEvents.set(t,[]),this._queuedEvents.get(t).push(e.slice(1)),!1},t}(n(373).EventEmitter);e.exports=d},2147:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=n(2148),d=n(2149),f=n(2156),h=n(2158),v=n(1906),_=n(2159),m=n(1891),y=n(2171),w=n(2172),k=n(2173),S=n(2174),T=n(1410),O=T.constants.DEFAULT_SESSION_TIMEOUT_SEC,P=T.createBandwidthProfilePayload,C=T.defer,E=T.difference,R=T.filterObject,j=T.flatMap,x=T.oncePerTick,L=n(1884),A=n(1571).createTwilioError,D=function(e){function t(t,n,r,o,c){var v=this;n.options=Object.assign({session_timeout:O},n.options),c=Object.assign({DominantSpeakerSignaling:l,NetworkQualityMonitor:d,NetworkQualitySignaling:f,RecordingSignaling:h,RemoteParticipantV2:_,TrackPrioritySignaling:y,TrackSwitchOffSignaling:w,bandwidthProfile:null,sessionTimeout:1e3*n.options.session_timeout,statsPublishIntervalMs:1e4},c),t.setBandwidthProfile(c.bandwidthProfile),o.setIceReconnectTimeout(c.sessionTimeout);var T=function(e){return v._getTrackReceiver(e)},P=(v=e.call(this,t,n.sid,n.name,c)||this)._log;return Object.defineProperties(v,{_disconnectedParticipantRevisions:{value:new Map},_NetworkQualityMonitor:{value:c.NetworkQualityMonitor},_lastBandwidthProfileRevision:{value:t.bandwidthProfileRevision,writable:!0},_networkQualityMonitor:{value:null,writable:!0},_networkQualityConfiguration:{value:t.networkQualityConfiguration},_peerConnectionManager:{value:o},_published:{value:new Map},_publishedRevision:{value:0,writable:!0},_RemoteParticipantV2:{value:c.RemoteParticipantV2},_subscribed:{value:new Map},_subscribedRevision:{value:0,writable:!0},_subscriptionFailures:{value:new Map},_dominantSpeakerSignaling:{value:new c.DominantSpeakerSignaling(T,{log:P})},_networkQualitySignaling:{value:new c.NetworkQualitySignaling(T,t.networkQualityConfiguration,{log:P})},_renderHintsSignaling:{value:new k(T,{log:P})},_publisherHintsSignaling:{value:new S(T,{log:P})},_trackPrioritySignaling:{value:new c.TrackPrioritySignaling(T,{log:P})},_trackSwitchOffSignaling:{value:new c.TrackSwitchOffSignaling(T,{log:P})},_pendingSwitchOffStates:{value:new Map},_transport:{value:r},_trackReceiverDeferreds:{value:new Map},mediaRegion:{enumerable:!0,value:n.options.media_region||null}}),v._initTrackSwitchOffSignaling(),v._initDominantSpeakerSignaling(),v._initNetworkQualityMonitorSignaling(),v._initPublisherHintSignaling(),function(e,t){var n=x((function(){e._publishNewLocalParticipantState()})),r=x((function(){var n=j(t.tracks,(function(e){return e.trackTransceiver}));e._peerConnectionManager.setTrackSenders(n)}));t.on("trackAdded",r),t.on("trackRemoved",r),t.on("updated",n),e.on("stateChanged",(function o(c){"disconnected"===c&&(t.removeListener("trackAdded",r),t.removeListener("trackRemoved",r),t.removeListener("updated",n),e.removeListener("stateChanged",o),t.disconnect())})),e.on("signalingConnectionStateChanged",(function(){var t=e.localParticipant,n=e.signalingConnectionState,r=t.identity,o=t.sid;switch(n){case"connected":t.connect(o,r);break;case"reconnecting":t.reconnecting()}}))}(v,t),function(e,t){t.on("description",(function(t){e._publishPeerConnectionState(t)})),t.dequeue("description"),t.on("candidates",(function(t){e._publishPeerConnectionState(t)})),t.dequeue("candidates"),t.on("trackAdded",e._addTrackReceiver.bind(e)),t.dequeue("trackAdded"),t.getTrackReceivers().forEach(e._addTrackReceiver,e),t.on("connectionStateChanged",(function(){e.emit("connectionStateChanged")})),t.on("iceConnectionStateChanged",(function(){e.emit("iceConnectionStateChanged"),"failed"===e.iceConnectionState&&(null!==e.localParticipant.networkQualityLevel&&e.localParticipant.setNetworkQualityLevel(0),e.participants.forEach((function(e){null!==e.networkQualityLevel&&e.setNetworkQualityLevel(0)})))}))}(v,o),function(e,t){t.on("message",e._update.bind(e)),t.on("stateChanged",(function n(r,o){"disconnected"===r&&("disconnected"!==e.state&&e._disconnect(o),t.removeListener("stateChanged",n)),e.emit("signalingConnectionStateChanged")}))}(v,r),function(e,t,n){var r=new Map,o=!1,c=setInterval((function(){e.getStats().then((function(e){o=!o,e.forEach((function(e,n){var c=new m(n,e,!0);t.publishEvent("quality","stats-report","info",{audioTrackStats:c.remoteAudioTrackStats.map((function(t,i){return F(t,e.remoteAudioTrackStats[i],r)})),localAudioTrackStats:c.localAudioTrackStats.map((function(t,i){return V(t,e.localAudioTrackStats[i],r)})),localVideoTrackStats:c.localVideoTrackStats.map((function(t,i){return V(t,e.localVideoTrackStats[i],r)})),peerConnectionId:c.peerConnectionId,videoTrackStats:c.remoteVideoTrackStats.map((function(t,i){return F(t,e.remoteVideoTrackStats[i],r)}))});var l=j(["localAudioTrackStats","localVideoTrackStats","remoteAudioTrackStats","remoteVideoTrackStats"],(function(e){return c[e].map((function(e){var t=e.ssrc;return e.trackSid+"+"+t}))}));if(E(Array.from(r.keys()),l).forEach((function(e){return r.delete(e)})),o){var d=function(e,t){return(e=Object.assign({availableIncomingBitrate:0,availableOutgoingBitrate:0,bytesReceived:0,bytesSent:0,consentRequestsSent:0,currentRoundTripTime:0,lastPacketReceivedTimestamp:0,lastPacketSentTimestamp:0,nominated:!1,peerConnectionId:t,priority:0,readable:!1,requestsReceived:0,requestsSent:0,responsesReceived:0,responsesSent:0,retransmissionsReceived:0,retransmissionsSent:0,state:"failed",totalRoundTripTime:0,transportId:"",writable:!1},R(e||{},null))).localCandidate=Object.assign({candidateType:"host",deleted:!1,ip:"",port:0,priority:0,protocol:"udp",url:""},R(e.localCandidate||{},null)),e.remoteCandidate=Object.assign({candidateType:"host",ip:"",port:0,priority:0,protocol:"udp",url:""},R(e.remoteCandidate||{},null)),e}(e.activeIceCandidatePair,c.peerConnectionId);t.publishEvent("quality","active-ice-candidate-pair","info",d)}}))}),(function(){}))}),n);e.on("stateChanged",(function t(n){"disconnected"===n&&(clearInterval(c),e.removeListener("stateChanged",t))}))}(v,r,c.statsPublishIntervalMs),v._update(n),v._peerConnectionManager.setEffectiveAdaptiveSimulcast(v._publisherHintsSignaling.isSetup),v}return o(t,e),Object.defineProperty(t.prototype,"connectionState",{get:function(){return this._peerConnectionManager.connectionState},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"signalingConnectionState",{get:function(){return"syncing"===this._transport.state?"reconnecting":this._transport.state},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"iceConnectionState",{get:function(){return this._peerConnectionManager.iceConnectionState},enumerable:!1,configurable:!0}),t.prototype._deleteTrackReceiverDeferred=function(e){return this._trackReceiverDeferreds.delete(e)},t.prototype._getOrCreateTrackReceiverDeferred=function(e){var t=this._trackReceiverDeferreds.get(e)||C(),n=this._peerConnectionManager.getTrackReceivers().find((function(t){return t.id===e&&"ended"!==t.readyState}));return n?t.resolve(n):this._trackReceiverDeferreds.set(e,t),t},t.prototype._addTrackReceiver=function(e){return this._getOrCreateTrackReceiverDeferred(e.id).resolve(e),this},t.prototype._disconnect=function(t){var n=e.prototype._disconnect.call(this,t);return n&&(this._teardownNetworkQualityMonitor(),this._transport.disconnect(),this._peerConnectionManager.close()),this.localParticipant.tracks.forEach((function(track){track.publishFailed(t||new Error("LocalParticipant disconnected"))})),n},t.prototype._getTrackReceiver=function(e){var t=this;return this._getOrCreateTrackReceiverDeferred(e).promise.then((function(n){return t._deleteTrackReceiverDeferred(e),n}))},t.prototype._getInitialTrackSwitchOffState=function(e){var t=this._pendingSwitchOffStates.get(e)||!1;return this._pendingSwitchOffStates.delete(e),t&&this._log.warn("["+e+"] was initially switched off! "),t},t.prototype._getTrackSidsToTrackSignalings=function(){var e=j(this.participants,(function(e){return Array.from(e.tracks)}));return new Map(e)},t.prototype._getOrCreateRemoteParticipant=function(e){var t=this,n=this._RemoteParticipantV2,r=this.participants.get(e.sid),o=this;return r||((r=new n(e,(function(e){return t._getInitialTrackSwitchOffState(e)}),(function(e,n){return t._trackPrioritySignaling.sendTrackPriorityUpdate(e,"subscribe",n)}),(function(e,n){return t._renderHintsSignaling.setTrackHint(e,n)}),(function(e){return t._renderHintsSignaling.clearTrackHint(e)}))).on("stateChanged",(function e(t){"disconnected"===t&&(r.removeListener("stateChanged",e),o.participants.delete(r.sid),o._disconnectedParticipantRevisions.set(r.sid,r.revision))})),this.connectParticipant(r)),r},t.prototype._getState=function(){return{participant:this.localParticipant.getState()}},t.prototype._maybeAddBandwidthProfile=function(e){var t=this.localParticipant,n=t.bandwidthProfile,r=t.bandwidthProfileRevision;return n&&this._lastBandwidthProfileRevision<r?(this._lastBandwidthProfileRevision=r,Object.assign({bandwidth_profile:P(n)},e)):e},t.prototype._publishNewLocalParticipantState=function(){this._transport.publish(this._maybeAddBandwidthProfile(this._getState()))},t.prototype._publishPeerConnectionState=function(e){this._transport.publish(Object.assign({peer_connections:[e]},this._getState()))},t.prototype._update=function(e){var t=this;if(e.subscribed&&e.subscribed.revision>this._subscribedRevision){this._subscribedRevision=e.subscribed.revision,e.subscribed.tracks.forEach((function(e){e.id?(t._subscriptionFailures.delete(e.sid),t._subscribed.set(e.sid,e.id)):e.error&&!t._subscriptionFailures.has(e.sid)&&t._subscriptionFailures.set(e.sid,e.error)}));var n=new Set(e.subscribed.tracks.filter((function(e){return!!e.id})).map((function(e){return e.sid})));this._subscribed.forEach((function(e,r){n.has(r)||t._subscribed.delete(r)}))}var r,o,c=new Set;return(e.participants||[]).forEach((function(e){if(e.sid!==t.localParticipant.sid){var n=t._disconnectedParticipantRevisions.get(e.sid);if(!(n&&e.revision<=n)){n&&t._disconnectedParticipantRevisions.delete(e.sid);var r=t._getOrCreateRemoteParticipant(e);r.update(e),c.add(r)}}})),"synced"===e.type&&this.participants.forEach((function(e){c.has(e)||e.disconnect()})),o=(r=this)._getTrackSidsToTrackSignalings(),r._subscriptionFailures.forEach((function(e,t){var n=o.get(t);n&&(r._subscriptionFailures.delete(t),n.subscribeFailed(A(e.code,e.message)))})),o.forEach((function(e){var t=r._subscribed.get(e.sid);(!t||e.isSubscribed&&e.trackTransceiver.id!==t)&&e.setTrackTransceiver(null),t&&r._getTrackReceiver(t).then((function(t){return e.setTrackTransceiver(t)}))})),e.peer_connections&&this._peerConnectionManager.update(e.peer_connections,"synced"===e.type),e.recording&&this.recording.update(e.recording),e.published&&e.published.revision>this._publishedRevision&&(this._publishedRevision=e.published.revision,e.published.tracks.forEach((function(track){track.sid&&t._published.set(track.id,track.sid)})),this.localParticipant.update(e.published)),e.participant&&this.localParticipant.connect(e.participant.sid,e.participant.identity),[this._dominantSpeakerSignaling,this._networkQualitySignaling,this._trackPrioritySignaling,this._trackSwitchOffSignaling,this._renderHintsSignaling,this._publisherHintsSignaling].forEach((function(t){var n=t.channel;!t.isSetup&&e.media_signaling&&e.media_signaling[n]&&e.media_signaling[n].transport&&"data-channel"===e.media_signaling[n].transport.type&&t.setup(e.media_signaling[n].transport.label)})),this},t.prototype._initPublisherHintSignaling=function(){var e=this;this._publisherHintsSignaling.on("updated",(function(t,n){Promise.all(t.map((function(t){return e.localParticipant.setPublisherHint(t.track,t.encodings).then((function(e){return{track:t.track,result:e}}))}))).then((function(t){e._publisherHintsSignaling.sendHintResponse({id:n,hints:t})}))}));var t=function(track){"video"===track.kind&&track.trackTransceiver.on("replaced",(function(){e._publisherHintsSignaling.sendTrackReplaced({trackSid:track.sid})}))};Array.from(this.localParticipant.tracks.values()).forEach((function(track){return t(track)})),this.localParticipant.on("trackAdded",(function(track){return t(track)}))},t.prototype._initTrackSwitchOffSignaling=function(){var e=this;this._trackSwitchOffSignaling.on("updated",(function(t,n){try{e._log.debug("received trackSwitch: ",{tracksOn:n,tracksOff:t});var r=new Map;n.forEach((function(e){return r.set(e,!0)})),t.forEach((function(t){r.get(t)&&e._log.warn(t+" is DUPLICATED in both tracksOff and tracksOn list"),r.set(t,!1)})),e.participants.forEach((function(e){e.tracks.forEach((function(track){var e=r.get(track.sid);void 0!==e&&(track.setSwitchedOff(!e),r.delete(track.sid))}))})),r.forEach((function(t,n){return e._pendingSwitchOffStates.set(n,!t)}))}catch(t){e._log.error("error processing track switch off:",t)}}))},t.prototype._initDominantSpeakerSignaling=function(){var e=this;this._dominantSpeakerSignaling.on("updated",(function(){return e.setDominantSpeaker(e._dominantSpeakerSignaling.loudestParticipantSid)}))},t.prototype._initNetworkQualityMonitorSignaling=function(){var e=this;this._networkQualitySignaling.on("ready",(function(){var t=new e._NetworkQualityMonitor(e._peerConnectionManager,e._networkQualitySignaling);e._networkQualityMonitor=t,t.on("updated",(function(){"failed"!==e.iceConnectionState&&(e.localParticipant.setNetworkQualityLevel(t.level,t.levels),e.participants.forEach((function(e){var n=t.remoteLevels.get(e.sid);n&&e.setNetworkQualityLevel(n.level,n)})))})),t.start()})),this._networkQualitySignaling.on("teardown",(function(){return e._teardownNetworkQualityMonitor()}))},t.prototype._teardownNetworkQualityMonitor=function(){this._networkQualityMonitor&&(this._networkQualityMonitor.stop(),this._networkQualityMonitor=null)},t.prototype.getStats=function(){var e=this;return this._peerConnectionManager.getStats().then((function(t){return new Map(Array.from(t).map((function(t){var n=c(t,2),r=n[0],o=n[1];return[r,Object.assign({},o,{localAudioTrackStats:M(e,o.localAudioTrackStats),localVideoTrackStats:M(e,o.localVideoTrackStats),remoteAudioTrackStats:N(e,o.remoteAudioTrackStats),remoteVideoTrackStats:N(e,o.remoteVideoTrackStats)})]})))}))},t}(v);function I(e,t){return t.reduce((function(t,n){var r=e.get(n.trackId);return r?[Object.assign({},n,{trackSid:r})].concat(t):t}),[])}function M(e,t){return I(e._published,t)}function N(e,t){return I(new Map(Array.from(e._subscribed.entries()).map((function(e){var t=c(e,2),n=t[0];return[t[1],n]}))),t)}function V(e,t,n){var r=t.framesEncoded,o=t.packetsSent,c=t.totalEncodeTime,l=t.totalPacketSendDelay,d=Object.assign({},e),f=e.trackSid+"+"+e.ssrc,h=n.get(f)||new Map;if("number"==typeof c&&"number"==typeof r){var v=h.get("avgEncodeDelay")||new L;v.putSample(1e3*c,r),d.avgEncodeDelay=Math.round(v.get()),h.set("avgEncodeDelay",v)}if("number"==typeof l&&"number"==typeof o){var _=h.get("avgPacketSendDelay")||new L;_.putSample(1e3*l,o),d.avgPacketSendDelay=Math.round(_.get()),h.set("avgPacketSendDelay",_)}return n.set(f,h),d}function F(e,t,n){var r=t.estimatedPlayoutTimestamp,o=t.framesDecoded,c=t.jitterBufferDelay,l=t.jitterBufferEmittedCount,d=t.totalDecodeTime,f=Object.assign({},e),h=e.trackSid+"+"+e.ssrc,v=n.get(h)||new Map;if("number"==typeof r&&(f.estimatedPlayoutTimestamp=r),"number"==typeof o&&"number"==typeof d){var _=v.get("avgDecodeDelay")||new L;_.putSample(1e3*d,o),f.avgDecodeDelay=Math.round(_.get()),v.set("avgDecodeDelay",_)}if("number"==typeof c&&"number"==typeof l){var m=v.get("avgJitterBufferDelay")||new L;m.putSample(1e3*c,l),f.avgJitterBufferDelay=Math.round(m.get()),v.set("avgJitterBufferDelay",m)}return n.set(h,v),f}e.exports=D},2148:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){var r=e.call(this,t,"active_speaker",n)||this;return Object.defineProperties(r,{_loudestParticipantSid:{value:null,writable:!0}}),r.on("ready",(function(e){e.on("message",(function(e){switch(e.type){case"active_speaker":r._setLoudestParticipantSid(e.participant)}}))})),r}return o(t,e),Object.defineProperty(t.prototype,"loudestParticipantSid",{get:function(){return this._loudestParticipantSid},enumerable:!1,configurable:!0}),t.prototype._setLoudestParticipantSid=function(e){this.loudestParticipantSid!==e&&(this._loudestParticipantSid=e,this.emit("updated"))},t}(n(1617));e.exports=c},2149:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},l=n(373),d=n(2150),f=function(e){function t(t,n){var r=e.call(this)||this;return Object.defineProperties(r,{_factories:{value:new WeakMap},_manager:{value:t},_signaling:{value:n}}),n.on("updated",(function(){return r.emit("updated")})),r}return o(t,e),Object.defineProperty(t.prototype,"level",{get:function(){return this._signaling.level},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"levels",{get:function(){return this._signaling.levels},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"remoteLevels",{get:function(){return this._signaling.remoteLevels},enumerable:!1,configurable:!0}),t.prototype.start=function(){var e=this;this.stop();var t=setTimeout((function(){var n,r;e._timeout===t&&(n=e,r=(n._manager._peerConnections?Array.from(n._manager._peerConnections.values()):[]).map((function(e){return e._peerConnection})).filter((function(e){return"closed"!==e.signalingState})).map((function(e){if(n._factories.has(e))return n._factories.get(e);var t=new d(e);return n._factories.set(e,t),t})).map((function(e){return e.next().catch((function(){return null}))})),Promise.all(r).then((function(e){return e.filter((function(e){return e})).map((function(e){return e.summarize()}))}))).then((function(n){if(e._timeout===t){if(n.length){var r=c(n,1)[0];e._signaling.put(r)}e.start()}}))}),200);this._timeout=t},t.prototype.stop=function(){clearTimeout(this._timeout),this._timeout=null},t}(l);e.exports=f},2150:function(e,t,n){"use strict";var r=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c},o=this&&this.__spreadArray||function(e,t){for(var i=0,n=t.length,r=e.length;i<n;i++,r++)e[r]=t[i];return e},c=this&&this.__values||function(e){var s="function"==typeof Symbol&&Symbol.iterator,t=s&&e[s],i=0;if(t)return t.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(s?"Object is not iterable.":"Symbol.iterator is not defined.")},l=n(1439).guessBrowser,d=n(2151),f=n(2153),h=n(2154),v=n(2155),_=function(){function e(e){Object.defineProperties(this,{pc:{enumerable:!0,value:e},ice:{enumerable:!0,value:new d},audio:{enumerable:!0,value:{send:new Map,recv:new Map}},video:{enumerable:!0,value:{send:new Map,recv:new Map}},lastReport:{enumerable:!0,value:null,writable:!0}})}return e.prototype.next=function(){var e,t,n,c=this;return("firefox"===l()?(t=(e=this).pc.getTransceivers().filter((function(e){return e.currentDirection&&e.currentDirection.match(/send/)&&e.sender.track})).map((function(e){return e.sender})),n=e.pc.getTransceivers().filter((function(e){return e.currentDirection&&e.currentDirection.match(/recv/)})).map((function(e){return e.receiver})),Promise.all([m(t),m(n),e.pc.getStats()]).then((function(t){var n=r(t,3),o=n[0],c=n[1],l=n[2],d=w(e),f=O(e);o.forEach((function(t,n){return C(e,t,f,n)})),R(d,f);var h=k(e),v=P(e);c.forEach((function(t,n){return E(e,t,v,n)})),R(h,v),j(e.ice,l)}))):function(e){return e.pc.getStats().then((function(t){var n=w(e),r=O(e);C(e,t,r),R(n,r);var o=k(e),c=P(e);E(e,t,c),R(o,c),j(e.ice,t)}))}(this)).then((function(){var e=o([],r(c.audio.send.values())),t=o([],r(c.video.send.values())),n=o([],r(c.audio.recv.values())),l=o([],r(c.video.recv.values())),d=new f(c.ice.lastReport,{send:e.map((function(e){return e.lastReport})).filter((function(e){return e})),recv:n.map((function(e){return e.lastReport})).filter((function(e){return e}))},{send:t.map((function(e){return e.lastReport})).filter((function(e){return e})),recv:l.map((function(e){return e.lastReport})).filter((function(e){return e}))});return c.lastReport=d,d}))},e}();function m(e){return Promise.all(e.map((function(e){var t=e.track.id;return e.getStats().then((function(e){var n,r;try{for(var o=c(e.values()),l=o.next();!l.done;l=o.next()){var d=l.value;"inbound-rtp"===d.type&&(d.id=t+"-"+d.id)}}catch(e){n={error:e}}finally{try{l&&!l.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return[t,e]}))}))).then((function(e){return new Map(e)}))}function y(e,t,n,r,o){var c=t[r.mediaType];if(!o){var l=n.get(r.trackId);l&&(o=l.trackIdentifier)}if(c&&o){if(c.has(r.id))return c.get(r.id);var d=new e(o,r);c.set(r.id,d)}return null}function w(e){return{audio:e.audio.send,video:e.video.send}}function k(e){return{audio:e.audio.recv,video:e.video.recv}}function S(e,t,n,r){return y(v,w(e),t,n,r)}function T(e,t,n,r){return y(h,k(e),t,n,r)}function O(e){return{audio:new Set(e.audio.send.keys()),video:new Set(e.video.send.keys())}}function P(e){return{audio:new Set(e.audio.recv.keys()),video:new Set(e.video.recv.keys())}}function C(e,t,n,r){var o,d;try{for(var f=c(t.values()),h=f.next();!h.done;h=f.next()){var v=h.value;if("outbound-rtp"===v.type&&!v.isRemote){if("firefox"!==l()&&!v.trackId)continue;var _=n[v.mediaType];_&&_.delete(v.id);var m=S(e,t,v,r);if(m){var y=t.get(v.remoteId);m.next(r||m.trackId,v,y)}}}}catch(e){o={error:e}}finally{try{h&&!h.done&&(d=f.return)&&d.call(f)}finally{if(o)throw o.error}}}function E(e,t,n,r){var o,l;try{for(var d=c(t.values()),f=d.next();!f.done;f=d.next()){var h=f.value;if("inbound-rtp"===h.type&&!h.isRemote){var v=n[h.mediaType];v&&v.delete(h.id);var _=T(e,t,h,r);_&&_.next(r||_.trackId,h)}}}catch(e){o={error:e}}finally{try{f&&!f.done&&(l=d.return)&&l.call(d)}finally{if(o)throw o.error}}}function R(e,t){var n=function(n){var r=e[n];t[n].forEach((function(e){return r.delete(e)}))};for(var r in t)n(r)}function j(e,t){var n,r,o,l,d;try{for(var f=c(t.values()),h=f.next();!h.done;h=f.next()){"transport"===(m=h.value).type&&(d=t.get(m.selectedCandidatePairId))}}catch(e){n={error:e}}finally{try{h&&!h.done&&(r=f.return)&&r.call(f)}finally{if(n)throw n.error}}if(d)e.next(d);else try{for(var v=c(t.values()),_=v.next();!_.done;_=v.next()){var m;"candidate-pair"!==(m=_.value).type||!m.nominated||"selected"in m&&!m.selected||e.next(m)}}catch(e){o={error:e}}finally{try{_&&!_.done&&(l=v.return)&&l.call(v)}finally{if(o)throw o.error}}}e.exports=_},2151:function(e,t,n){"use strict";var r=n(2152),o=function(){function e(){Object.defineProperties(this,{lastReport:{enumerable:!0,value:new r(0,0),writable:!0},lastStats:{enumerable:!0,value:null,writable:!0}})}return e.prototype.next=function(e){var t=this.lastStats;if(this.lastStats=e,t){var n=t.id===e.id?r.of(t,e):new r(0,0);this.lastReport=n}return this.lastReport},e}();e.exports=o},2152:function(e,t,n){"use strict";var r=function(){function e(e,t,n,r){Object.defineProperties(this,{availableSend:{enumerable:!0,value:n},recv:{enumerable:!0,value:t},rtt:{enumerable:!0,value:r},send:{enumerable:!0,value:e}})}return e.of=function(t,n){var r=(n.timestamp-t.timestamp)/1e3,o=n.bytesSent-t.bytesSent,c=n.bytesReceived-t.bytesReceived;return new e(r>0?o/r*8:0,r>0?c/r*8:0,n.availableOutgoingBitrate,n.currentRoundTripTime)},e}();e.exports=r},2153:function(e,t,n){"use strict";var r=n(1899),o=n(1903),c=function(){function e(e,audio,video){Object.defineProperties(this,{ice:{enumerable:!0,value:e},audio:{enumerable:!0,value:audio},video:{enumerable:!0,value:video}})}return e.prototype.summarize=function(){var e=this.audio.send.concat(this.video.send),t=o.summarize(e),n=this.audio.recv.concat(this.video.recv),c=r.summarize(n);return{ice:this.ice,send:t,recv:c,audio:{send:o.summarize(this.audio.send),recv:r.summarize(this.audio.recv)},video:{send:o.summarize(this.video.send),recv:r.summarize(this.video.recv)}}},e}();e.exports=c},2154:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1899),l=function(e){function t(t,n){var r=e.call(this,n.id,t,n)||this;return Object.defineProperties(r,{lastReport:{enumerable:!0,value:null,writable:!0}}),r}return o(t,e),t.prototype.next=function(e,t){var n=this.lastStats;this.lastStats=t,this.trackId=e;var r=c.of(e,n,t);return this.lastReport=r,r},t}(n(1904));e.exports=l},2155:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1904),l=n(1903),d=function(e){function t(t,n){var r=e.call(this,n.id,t,n)||this;return Object.defineProperties(r,{lastReport:{enumerable:!0,value:null,writable:!0}}),r}return o(t,e),t.prototype.next=function(e,t,n){var r=this.lastStats;this.lastStats=t,this.trackId=e;var o=l.of(e,r,t,n);return this.lastReport=o,o},t}(c);e.exports=d},2156:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1617),l=n(2157),d=n(1609),f=function(e){function t(t,n,r){var o=e.call(this,t,"network_quality",r)||this;return Object.defineProperties(o,{_level:{value:null,writable:!0},_levels:{value:null,writable:!0},_remoteLevels:{value:new Map,writable:!0},_networkQualityInputs:{value:new l},_resendTimer:{value:new d((function(){o._resendTimer.setDelay(1.5*o._resendTimer.delay),o._sendNetworkQualityInputs()}),5e3,!1)},_networkQualityReportLevels:{get:function(){return{reportLevel:n.local,remoteReportLevel:n.remote}}}}),o.on("ready",(function(e){e.on("message",(function(e){switch(o._log.debug("Incoming: ",e),e.type){case"network_quality":o._handleNetworkQualityMessage(e)}}))})),o._sendNetworkQualityInputs(),o}return o(t,e),Object.defineProperty(t.prototype,"level",{get:function(){return this._level},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"levels",{get:function(){return this._levels},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"remoteLevels",{get:function(){return this._remoteLevels},enumerable:!1,configurable:!0}),t.prototype._handleNetworkQualityMessage=function(e){var t=this,n=!1,r=null,o=e?e.local:null;"number"==typeof o?(r=o,this._levels=null):"object"==typeof o&&o&&(this._levels=o,r="number"==typeof o.level?o.level:Math.min(o.audio.send,o.audio.recv,o.video.send,o.video.recv)),null!==r&&this.level!==r&&(this._level=r,n=!0),this._remoteLevels=e&&e.remotes?e.remotes.reduce((function(e,r){return(t._remoteLevels.get(r.sid)||{}).level!==r.level&&(n=!0),e.set(r.sid,r)}),new Map):this._remoteLevels,n&&this.emit("updated"),this._resendTimer.setDelay(5e3),this._resendTimer.isSet&&setTimeout((function(){return t._sendNetworkQualityInputs()}),1e3)},t.prototype._sendNetworkQualityInputs=function(){var e=this;return this._resendTimer.clear(),this._networkQualityInputs.take().then((function(t){e._transport&&e._transport.publish(function(e,t){return Object.assign({type:"network_quality"},e,t)}(t,e._networkQualityReportLevels))})).finally((function(){e._resendTimer.start()}))},t.prototype.put=function(e){this._networkQualityInputs.put(e)},t}(c);e.exports=f},2157:function(e,t,n){"use strict";var r=n(1410).defer,o=function(){function e(){Object.defineProperties(this,{_deferreds:{value:[]},_hasValue:{value:!1,writable:!0},_value:{value:null,writable:!0}})}return e.prototype.put=function(e){this._hasValue=!0,this._value=e;var t=this._deferreds.shift();return t&&t.resolve(e),this},e.prototype.take=function(){var e=this;if(this._hasValue&&!this._deferreds.length)return this._hasValue=!1,Promise.resolve(this._value);var t=r();return this._deferreds.push(t),t.promise.then((function(t){return e._hasValue=!1,t}))},e}();e.exports=o},2158:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(){var t=e.call(this)||this;return Object.defineProperties(t,{_revision:{value:1,writable:!0}}),t}return o(t,e),t.prototype.update=function(e){return e.revision<this._revision?this:(this._revision=e.revision,this.enable(e.is_recording))},t}(n(1905));e.exports=c},2159:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(2160),l=n(2169),d=function(e){function t(t,n,r,o,c,d){var f=e.call(this,t.sid,t.identity)||this;return d=Object.assign({RemoteTrackPublicationV2:l},d),Object.defineProperties(f,{_revision:{writable:!0,value:null},_RemoteTrackPublicationV2:{value:d.RemoteTrackPublicationV2},_getInitialTrackSwitchOffState:{value:n},updateSubscriberTrackPriority:{value:function(e,t){return r(e,t)}},updateTrackRenderHint:{value:function(e,t){return o(e,t)}},clearTrackHint:{value:function(e){return c(e)}},revision:{enumerable:!0,get:function(){return this._revision}}}),f.update(t)}return o(t,e),t.prototype._getOrCreateTrack=function(e){var t=this._RemoteTrackPublicationV2,track=this.tracks.get(e.sid);track||(track=new t(e,this._getInitialTrackSwitchOffState(e.sid)),this.addTrack(track));return track},t.prototype.update=function(e){var t=this;if(null!==this.revision&&e.revision<=this.revision)return this;this._revision=e.revision;var n=new Set;switch(e.tracks.forEach((function(e){var track=t._getOrCreateTrack(e);track.update(e),n.add(track)})),this.tracks.forEach((function(track){n.has(track)||t.removeTrack(track)})),e.state){case"disconnected":this.disconnect();break;case"reconnecting":this.reconnecting();break;case"connected":this.connect(this.sid,this.identity)}return this},t}(c);e.exports=d},2160:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){var r=e.call(this)||this;return r.connect(t,n),r}return o(t,e),t}(n(1824));e.exports=c},2161:function(e,t,n){"use strict";var r=n(2162),o=n(2168),c=function(e){var t=e.level,audio=e.audio,video=e.video;Object.defineProperties(this,{level:{value:t,enumerable:!0},audio:{value:audio?new r(audio):null,enumerable:!0},video:{value:video?new o(video):null,enumerable:!0}})};e.exports=c},2162:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t){return e.call(this,t)||this}return o(t,e),t}(n(1907));e.exports=c},2163:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t){return e.call(this,t)||this}return o(t,e),t}(n(1908));e.exports=c},2164:function(e,t,n){"use strict";var r=function(e){var t=e.actual,n=void 0===t?null:t,r=e.available,o=void 0===r?null:r,c=e.level,l=void 0===c?null:c;Object.defineProperties(this,{actual:{value:n,enumerable:!0},available:{value:o,enumerable:!0},level:{value:l,enumerable:!0}})};e.exports=r},2165:function(e,t,n){"use strict";var r=function(e){var t=e.fractionLost,n=void 0===t?null:t,r=e.level,o=void 0===r?null:r;Object.defineProperties(this,{fractionLost:{value:n,enumerable:!0},level:{value:o,enumerable:!0}})};e.exports=r},2166:function(e,t,n){"use strict";var r=function(e){var t=e.jitter,n=void 0===t?null:t,r=e.rtt,o=void 0===r?null:r,c=e.level,l=void 0===c?null:c;Object.defineProperties(this,{jitter:{value:n,enumerable:!0},rtt:{value:o,enumerable:!0},level:{value:l,enumerable:!0}})};e.exports=r},2167:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t){return e.call(this,t)||this}return o(t,e),t}(n(1908));e.exports=c},2168:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t){return e.call(this,t)||this}return o(t,e),t}(n(1907));e.exports=c},2169:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(track,t){return e.call(this,track.sid,track.name,track.kind,track.enabled,track.priority,t)||this}return o(t,e),t.prototype.update=function(track){return this.enable(track.enabled),this.setPriority(track.priority),this},t}(n(2170));e.exports=c},2170:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n,r,o,c,l){var d=e.call(this,n,r,o,c)||this;return Object.defineProperties(d,{_isSwitchedOff:{value:l,writable:!0}}),d.setSid(t),d}return o(t,e),Object.defineProperty(t.prototype,"isSubscribed",{get:function(){return!!this.trackTransceiver},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isSwitchedOff",{get:function(){return this._isSwitchedOff},enumerable:!1,configurable:!0}),t.prototype.subscribeFailed=function(e){return this.error||(this._error=e,this.emit("updated")),this},t.prototype.setPriority=function(e){return this._priority!==e&&(this._priority=e,this.emit("updated")),this},t.prototype.setSwitchedOff=function(e){return this._isSwitchedOff!==e&&(this._isSwitchedOff=e,this.emit("updated")),this},t}(n(1909));e.exports=c},2171:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){var r=e.call(this,t,"track_priority",n)||this;return Object.defineProperties(r,{_enqueuedPriorityUpdates:{value:new Map}}),r.on("ready",(function(e){Array.from(r._enqueuedPriorityUpdates.keys()).forEach((function(t){e.publish({type:"track_priority",track:t,subscribe:r._enqueuedPriorityUpdates.get(t)})}))})),r}return o(t,e),t.prototype.sendTrackPriorityUpdate=function(e,t,n){if("subscribe"!==t)throw new Error("only subscribe priorities are supported, found: "+t);this._enqueuedPriorityUpdates.set(e,n),this._transport&&this._transport.publish({type:"track_priority",track:e,subscribe:n})},t}(n(1617));e.exports=c},2172:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n){var r=e.call(this,t,"track_switch_off",n)||this;return r.on("ready",(function(e){e.on("message",(function(e){switch(e.type){case"track_switch_off":r._setTrackSwitchOffUpdates(e.off||[],e.on||[])}}))})),r}return o(t,e),t.prototype._setTrackSwitchOffUpdates=function(e,t){this.emit("updated",e,t)},t}(n(1617));e.exports=c},2173:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1617),l=n(1609),d=n(1410).isDeepEqual,f=1,h=function(e){function t(t,n){var r=e.call(this,t,"render_hints",n)||this;return Object.defineProperties(r,{_trackSidsToRenderHints:{value:new Map},_responseTimer:{value:new l((function(){r._sendAllHints(),r._responseTimer.setDelay(2*r._responseTimer.delay)}),2e3,!1)}}),r.on("ready",(function(e){e.on("message",(function(e){switch(r._log.debug("Incoming: ",e),e.type){case"render_hints":r._processHintResults(e&&e.subscriber&&e.subscriber.hints||[]);break;default:r._log.warn("Unknown message type: ",e.type)}})),r._sendAllHints()})),r}return o(t,e),t.prototype._sendAllHints=function(){var e=this;Array.from(this._trackSidsToRenderHints.keys()).forEach((function(t){var n=e._trackSidsToRenderHints.get(t);n.renderDimensions&&(n.isDimensionDirty=!0),"enabled"in n&&(n.isEnabledDirty=!0)})),this._sendHints()},t.prototype._processHintResults=function(e){var t=this;this._responseTimer.clear(),this._responseTimer.setDelay(2e3),e.forEach((function(e){"OK"!==e.result&&t._log.debug("Server error processing hint:",e)})),this._sendHints()},t.prototype._sendHints=function(){var e=this;if(this._transport&&!this._responseTimer.isSet){var t=[];if(Array.from(this._trackSidsToRenderHints.keys()).forEach((function(n){var r=e._trackSidsToRenderHints.get(n);if(r.isEnabledDirty||r.isDimensionDirty){var o={track:n};r.isEnabledDirty&&(o.enabled=r.enabled,r.isEnabledDirty=!1),r.isDimensionDirty&&(o.render_dimensions=r.renderDimensions,r.isDimensionDirty=!1),t.push(o)}})),t.length>0){var n={type:"render_hints",subscriber:{id:f++,hints:t}};this._log.debug("Outgoing: ",n),this._transport.publish(n),this._responseTimer.start()}}},t.prototype.setTrackHint=function(e,t){var n=this._trackSidsToRenderHints.get(e)||{isEnabledDirty:!1,isDimensionDirty:!1};"enabled"in t&&n.enabled!==t.enabled&&(n.enabled=!!t.enabled,n.isEnabledDirty=!0),t.renderDimensions&&!d(t.renderDimensions,n.renderDimensions)&&(n.renderDimensions=t.renderDimensions,n.isDimensionDirty=!0),this._trackSidsToRenderHints.set(e,n),this._sendHints()},t.prototype.clearTrackHint=function(e){this._trackSidsToRenderHints.delete(e)},t}(c);e.exports=h},2174:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1617),l=1,d=function(e){function t(t,n){var r=e.call(this,t,"publisher_hints",n)||this;return r.on("ready",(function(e){r._log.debug("publisher_hints transport ready:",e),e.on("message",(function(e){switch(r._log.debug("Incoming: ",e),e.type){case"publisher_hints":e.publisher&&e.publisher.hints&&e.publisher.id&&r._processPublisherHints(e.publisher.hints,e.publisher.id);break;default:r._log.warn("Unknown message type: ",e.type)}}))})),r}return o(t,e),t.prototype.sendTrackReplaced=function(e){var t=e.trackSid;if(this._transport){var n={type:"client_reset",track:t,id:l++};this._log.debug("Outgoing: ",n),this._transport.publish(n)}},t.prototype.sendHintResponse=function(e){var t=e.id,n=e.hints;if(this._transport){var r={type:"publisher_hints",id:t,hints:n};this._log.debug("Outgoing: ",r),this._transport.publish(r)}},t.prototype._processPublisherHints=function(e,t){try{this.emit("updated",e,t)}catch(e){this._log.error("error processing hints:",e)}},t}(c);e.exports=d},2175:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1616),l=n(1882),d=n(1895),f=n(1425).reconnectBackoffConfig,h=n(1609),v=n(1425),_=v.SDK_NAME,m=v.SDK_VERSION,y=v.SDP_FORMAT,w=n(1410),k=w.createBandwidthProfilePayload,S=w.createMediaSignalingPayload,T=w.createSubscribePayload,O=w.getUserAgent,P=w.isNonArrayObject,C=n(1571),E=C.createTwilioError,R=C.RoomCompletedError,j=C.SignalingConnectionError,x=C.SignalingServerBusyError,L={connecting:["connected","disconnected"],connected:["disconnected","syncing"],syncing:["connected","disconnected"],disconnected:[]},A=function(e){function t(t,n,r,o,c,h){var v;return h=Object.assign({Backoff:d,TwilioConnection:l,iceServers:null,trackPriority:!0,trackSwitchOff:!0,renderHints:!0,userAgent:O()},h),v=e.call(this,"connecting",L)||this,Object.defineProperties(v,{_accessToken:{value:n},_automaticSubscription:{value:h.automaticSubscription},_bandwidthProfile:{value:h.bandwidthProfile},_dominantSpeaker:{value:h.dominantSpeaker},_adaptiveSimulcast:{value:h.adaptiveSimulcast},_eventObserver:{value:h.eventObserver,writable:!1},_renderHints:{value:h.renderHints},_iceServersStatus:{value:Array.isArray(h.iceServers)?"overrode":"acquire"},_localParticipant:{value:r},_name:{value:t},_networkQuality:{value:P(h.networkQuality)||h.networkQuality},_options:{value:h},_peerConnectionManager:{value:o},_sessionTimer:{value:null,writable:!0},_sessionTimeoutMS:{value:0,writable:!0},_reconnectBackoff:{value:h.Backoff.exponential(f)},_session:{value:null,writable:!0},_trackPriority:{value:h.trackPriority},_trackSwitchOff:{value:h.trackSwitchOff},_twilioConnection:{value:null,writable:!0},_updatesReceived:{value:[]},_updatesToSend:{value:[]},_userAgent:{value:h.userAgent},_wsServer:{value:c}}),function(e){function t(){if("disconnected"!==e.state){e._twilioConnection&&e._twilioConnection.removeListener("message",r);var t=e._iceServersStatus,o=e._options,c=e._wsServer,l=e.state,d=o.TwilioConnection,f=new d(c,Object.assign({helloBody:"connecting"===l&&"acquire"===t?e._createIceMessage():e._createConnectOrSyncOrDisconnectMessage()},o));f.once("close",(function(e){e===d.CloseReason.LOCAL?n():n(new Error(e))})),f.on("message",r),e._twilioConnection=f}}function n(n){if("disconnected"!==e.state)if(n){var r=e._getReconnectTimer();if(r)"connected"===e.state&&e.preempt("syncing"),r.then(t);else{var o=n.message===l.CloseReason.BUSY?new x:new j;e.disconnect(o)}}else e.disconnect()}function r(t){if("disconnected"!==e.state)if("error"!==t.type)switch(e.state){case"connected":switch(t.type){case"connected":case"synced":case"update":return void e.emit("message",t);case"disconnected":return void e.disconnect("completed"===t.status?new R:null);default:return}case"connecting":switch(t.type){case"iced":return void e._options.onIced(t.ice_servers).then((function(){e._sendConnectOrSyncOrDisconnectMessage()}));case"connected":return e._setSession(t.session,t.options.session_timeout),e.emit("connected",t),void e.preempt("connected");case"synced":case"update":return void e._updatesReceived.push(t);case"disconnected":return void e.disconnect("completed"===t.status?new R:null);default:return}case"syncing":switch(t.type){case"connected":case"update":return void e._updatesReceived.push(t);case"synced":return e._clearReconnectTimer(),e.emit("message",t),void e.preempt("connected");case"disconnected":return void e.disconnect("completed"===t.status?new R:null);default:return}default:return}else e.disconnect(E(t.code,t.message))}e.on("stateChanged",(function t(n){switch(n){case"connected":var o=e._updatesToSend.splice(0);return o.length&&e.publish(function(e){return e.reduce((function(e,t){return(!e.participant&&t.participant||e.participant&&t.participant&&t.participant.revision>e.participant.revision)&&(e.participant=t.participant),!e.peer_connections&&t.peer_connections?e.peer_connections=D(t.peer_connections):e.peer_connections&&t.peer_connections&&(e.peer_connections=D(e.peer_connections.concat(t.peer_connections))),e}),{})}(o)),void e._updatesReceived.splice(0).forEach((function(t){return e.emit("message",t)}));case"disconnected":return e._twilioConnection.removeListener("message",r),void e.removeListener("stateChanged",t);case"syncing":default:return}}));var o=e._options,c=e._iceServersStatus,d=o.iceServers,f=o.onIced;"overrode"===c?f(d).then(t):t()}(v),v}return o(t,e),t.prototype._createConnectOrSyncOrDisconnectMessage=function(){if("connected"===this.state)return null;if("disconnected"===this.state)return{session:this._session,type:"disconnect",version:2};var e={connecting:"connect",syncing:"sync"}[this.state],t={name:this._name,participant:this._localParticipant.getState(),peer_connections:this._peerConnectionManager.getStates(),type:e,version:2};return"connect"===t.type?(t.ice_servers=this._iceServersStatus,t.publisher={name:_,sdk_version:m,user_agent:this._userAgent},this._bandwidthProfile&&(t.bandwidth_profile=k(this._bandwidthProfile)),t.media_signaling=S(this._dominantSpeaker,this._networkQuality,this._trackPriority,this._trackSwitchOff,this._adaptiveSimulcast,this._renderHints),t.subscribe=T(this._automaticSubscription),t.format=y,t.token=this._accessToken):"sync"===t.type?(t.session=this._session,t.token=this._accessToken):"update"===t.type&&(t.session=this._session),t},t.prototype._createIceMessage=function(){return{edge:"roaming",token:this._accessToken,type:"ice",version:1}},t.prototype._sendConnectOrSyncOrDisconnectMessage=function(){var e=this._createConnectOrSyncOrDisconnectMessage();e&&this._twilioConnection.sendMessage(e)},t.prototype.disconnect=function(e){return"disconnected"!==this.state&&(this.preempt("disconnected",null,[e]),this._sendConnectOrSyncOrDisconnectMessage(),this._twilioConnection.close(),!0)},t.prototype.publish=function(e){switch(this.state){case"connected":return this._twilioConnection.sendMessage(Object.assign({session:this._session,type:"update",version:2},e)),!0;case"connecting":case"syncing":return this._updatesToSend.push(e),!0;case"disconnected":default:return!1}},t.prototype.publishEvent=function(e,t,n,r){this._eventObserver.emit("event",{group:e,name:t,level:n,payload:r})},t.prototype.sync=function(){return"connected"===this.state&&(this.preempt("syncing"),this._sendConnectOrSyncOrDisconnectMessage(),!0)},t.prototype._setSession=function(e,t){this._session=e,this._sessionTimeoutMS=1e3*t},t.prototype._getReconnectTimer=function(){var e=this;return 0===this._sessionTimeoutMS?null:(this._sessionTimer||(this._sessionTimer=new h((function(){e._sessionTimer&&(e._sessionTimeoutMS=0)}),this._sessionTimeoutMS)),new Promise((function(t){e._reconnectBackoff.once("ready",t),e._reconnectBackoff.backoff()})))},t.prototype._clearReconnectTimer=function(){this._reconnectBackoff.reset(),this._sessionTimer&&(this._sessionTimer.clear(),this._sessionTimer=null)},t}(c);function D(e){return Array.from(e.reduce((function(e,t){var n=e.get(t.id)||t;return(!n.description&&t.description||n.description&&t.description&&t.description.revision>n.description.revision)&&(n.description=t.description),(!n.ice&&t.ice||n.ice&&t.ice&&t.ice.revision>n.ice.revision)&&(n.ice=t.ice),e.set(n.id,n),e}),new Map).values())}e.exports=A},2176:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(2177),l=n(2178),d=n(1425).DEFAULT_LOG_LEVEL,f=n(1570),h=n(1410),v=h.buildLogLevels,_=h.isDeepEqual,m=function(e){function t(t,n,r){var o;r=Object.assign({logLevel:d,LocalTrackPublicationV2:l},r),o=e.call(this)||this;var c=v(r.logLevel);return Object.defineProperties(o,{_bandwidthProfile:{value:null,writable:!0},_bandwidthProfileRevision:{value:0,writable:!0},_encodingParameters:{value:t},_removeListeners:{value:new Map},_LocalTrackPublicationV2:{value:r.LocalTrackPublicationV2},_log:{value:r.log?r.log.createLog("default",o):new f("default",o,c,r.loggerName)},_publishedRevision:{writable:!0,value:0},_revision:{writable:!0,value:1},_signalingRegion:{value:null,writable:!0},bandwidthProfile:{enumerable:!0,get:function(){return this._bandwidthProfile}},bandwidthProfileRevision:{enumerable:!0,get:function(){return this._bandwidthProfileRevision}},networkQualityConfiguration:{enumerable:!0,value:n},revision:{enumerable:!0,get:function(){return this._revision}},signalingRegion:{enumerable:!0,get:function(){return this._signalingRegion}}}),o}return o(t,e),t.prototype.toString=function(){return"[LocalParticipantSignaling: "+this.sid+"]"},t.prototype.setSignalingRegion=function(e){this._signalingRegion||(this._signalingRegion=e)},t.prototype.setBandwidthProfile=function(e){_(this._bandwidthProfile,e)||(this._bandwidthProfile=JSON.parse(JSON.stringify(e)),this._bandwidthProfileRevision++,this.didUpdate())},t.prototype.getParameters=function(){return this._encodingParameters},t.prototype.setParameters=function(e){return this._encodingParameters.update(e),this},t.prototype.update=function(e){return this._publishedRevision>=e.revision||(this._publishedRevision=e.revision,e.tracks.forEach((function(e){var t=this.tracks.get(e.id);t&&t.update(e)}),this)),this},t.prototype._createLocalTrackPublicationSignaling=function(e,t,n){return new this._LocalTrackPublicationV2(e,t,n)},t.prototype.addTrack=function(t,n,r){var o=this;e.prototype.addTrack.call(this,t,n,r);var c=this.getPublication(t),l=c.isEnabled,d=c.updatedPriority,f=function(){l===c.isEnabled&&d===c.updatedPriority||(o.didUpdate(),l=c.isEnabled,d=c.updatedPriority)};return c.on("updated",f),this._removeListener(c),this._removeListeners.set(c,(function(){return c.removeListener("updated",f)})),this.didUpdate(),this},t.prototype._removeListener=function(e){var t=this._removeListeners.get(e);t&&t()},t.prototype.getState=function(){return{revision:this.revision,tracks:Array.from(this.tracks.values()).map((function(track){return track.getState()}))}},t.prototype.didUpdate=function(){this._revision++,this.emit("updated")},t.prototype.removeTrack=function(t){var n=e.prototype.removeTrack.call(this,t);return n&&(t.removeClone(n.trackTransceiver),this._removeListener(n),this.didUpdate()),n},t.prototype.setNetworkQualityConfiguration=function(e){this.networkQualityConfiguration.update(e)},t.prototype.setPublisherHint=function(e,t){var n=Array.from(this.tracks.values()).find((function(t){return t.sid===e}));return n?n.trackTransceiver.setPublisherHint(t):(this._log.warn("track:"+e+" not found"),Promise.resolve("UNKNOWN_TRACK"))},t}(c);e.exports=m},2177:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(){var t=e.call(this)||this;return Object.defineProperties(t,{_publicationsToTrackSenders:{value:new Map},_trackSendersToPublications:{value:new Map}}),t}return o(t,e),t.prototype.addTrack=function(t,n,r){var o=this._createLocalTrackPublicationSignaling(t,n,r);return this._trackSendersToPublications.set(t,o),this._publicationsToTrackSenders.set(o,t),e.prototype.addTrack.call(this,o),this},t.prototype.getPublication=function(e){return this._trackSendersToPublications.get(e)||null},t.prototype.getSender=function(e){return this._publicationsToTrackSenders.get(e)||null},t.prototype.removeTrack=function(t){var n=this._trackSendersToPublications.get(t);return n?(this._trackSendersToPublications.delete(t),this._publicationsToTrackSenders.delete(n),e.prototype.removeTrack.call(this,n)&&n.stop(),n):null},t}(n(1824));e.exports=c},2178:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(2179),l=n(1571).createTwilioError,d=function(e){function t(t,n,r){return e.call(this,t,n,r)||this}return o(t,e),t.prototype.getState=function(){return{enabled:this.isEnabled,id:this.id,kind:this.kind,name:this.name,priority:this.updatedPriority}},t.prototype.update=function(track){switch(track.state){case"ready":this.setSid(track.sid);break;case"failed":var e=track.error;this.publishFailed(l(e.code,e.message))}return this},t}(c);e.exports=d},2179:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=function(e){function t(t,n,r){var o=this,c="data"===(t=t.clone()).kind||t.track.enabled;return(o=e.call(this,n,t.kind,c,r)||this).setTrackTransceiver(t),Object.defineProperties(o,{_updatedPriority:{value:r,writable:!0},id:{enumerable:!0,value:t.id}}),o}return o(t,e),Object.defineProperty(t.prototype,"updatedPriority",{get:function(){return this._updatedPriority},enumerable:!1,configurable:!0}),t.prototype.enable=function(t){return t="boolean"!=typeof t||t,this.trackTransceiver.track.enabled=t,e.prototype.enable.call(this,t)},t.prototype.publishFailed=function(e){return function(e,t){if(null!==e._sid||e._error)return!1;return e._error=t,!0}(this,e)&&this.emit("updated"),this},t.prototype.setPriority=function(e){return this._updatedPriority!==e&&(this._updatedPriority=e,this.emit("updated")),this},t.prototype.setSid=function(t){return this._error?this:e.prototype.setSid.call(this,t)},t.prototype.stop=function(){this.trackTransceiver.stop()},t}(n(1909));e.exports=c},2180:function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,b){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,b){e.__proto__=b}||function(e,b){for(var p in b)Object.prototype.hasOwnProperty.call(b,p)&&(e[p]=b[p])})(e,b)},function(e,b){if("function"!=typeof b&&null!==b)throw new TypeError("Class extends value "+String(b)+" is not a constructor or null");function t(){this.constructor=e}r(e,b),e.prototype=null===b?Object.create(b):(t.prototype=b.prototype,new t)}),c=n(1824),l=n(1906),d=n(1616),f={closed:["opening"],opening:["closed","open"],open:["closed","closing"],closing:["closed","open"]},h=function(e){function t(){return e.call(this,"closed",f)||this}return o(t,e),t.prototype._close=function(e){return this.transition("closing",e),this.transition("closed",e),Promise.resolve(this)},t.prototype._connect=function(e,t,n,r,o){e.connect("PA00000000000000000000000000000000","test");var c=Promise.resolve(new l(e,"RM00000000000000000000000000000000",o));return c.cancel=function(){},c},t.prototype._open=function(e){return this.transition("opening",e),this.transition("open",e),Promise.resolve(this)},t.prototype.close=function(){var e=this;return this.bracket("close",(function(t){switch(e.state){case"closed":return e;case"open":return e._close(t);default:throw new Error('Unexpected Signaling state "'+e.state+'"')}}))},t.prototype.connect=function(e,t,n,r,o){var c=this;return this.bracket("connect",(function l(d){switch(c.state){case"closed":return c._open(d).then(l.bind(null,d));case"open":return c.releaseLockCompletely(d),c._connect(e,t,n,r,o);default:throw new Error('Unexpected Signaling state "'+c.state+'"')}}))},t.prototype.createLocalParticipantSignaling=function(){return new c},t.prototype.open=function(){var e=this;return this.bracket("open",(function(t){switch(e.state){case"closed":return e._open(t);case"open":return e;default:throw new Error('Unexpected Signaling state "'+e.state+'"')}}))},t}(d);e.exports=h},2181:function(e,t,n){"use strict";var r=n(1439),o=r.guessBrowser,c=r.support,l=n(2182),d=l.isAndroid,f=l.isMobile,h=l.isNonChromiumEdge,v=l.rebrandedChromeBrowser,_=l.mobileWebKitBrowser,m=["crios","edg","edge","electron","headlesschrome"],y=["chrome","firefox"],w=["chrome","safari"],k=[];e.exports=function(){var e=o();if(!e)return!1;var t=v(e),n=_(e),r=d()?y:w;return!!e&&c()&&(!t||m.includes(t))&&!h(e)&&(!n||k.includes(n))&&(!f()||r.includes(e))}},2182:function(e,t,n){"use strict";var r=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),c=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)c.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return c};e.exports={isAndroid:function(){return/Android/.test(navigator.userAgent)},isIOS:function(){return/iPad|iPhone|iPod/.test(navigator.userAgent)},isMobile:function(){return/Mobi/.test(navigator.userAgent)},isNonChromiumEdge:function(e){return"chrome"===e&&/Edge/.test(navigator.userAgent)&&("undefined"==typeof chrome||void 0===chrome.runtime)},mobileWebKitBrowser:function(e){return"safari"!==e?null:"brave"in navigator?"brave":["edge","edg"].find((function(e){return navigator.userAgent.toLowerCase().includes(e)}))||null},rebrandedChromeBrowser:function(e){if("chrome"!==e)return null;if("brave"in navigator)return"brave";var t=function(e){for(var t=[],n=[],i=0;i<e.length;i++)if("("===e[i])t.push(i);else if(")"===e[i]&&t.length>0){var r=t.pop();0===t.length&&n.push(e.substring(r,i+1))}return n}(navigator.userAgent).reduce((function(e,t){return e.replace(t,"")}),navigator.userAgent).match(/[^\s]+/g)||[];return r(t.map((function(e){return e.split("/")[0].toLowerCase()}))).slice(2).find((function(e){return!["chrome","mobile","safari"].includes(e)}))||null}}}}]);