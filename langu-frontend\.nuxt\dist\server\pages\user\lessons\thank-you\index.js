exports.ids = [146,11,12,34,35,63,64,77,90,91,92,93,94,95,96,97,124];
exports.modules = {

/***/ 1014:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CalendarDate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(962);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CalendarDate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CalendarDate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CalendarDate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CalendarDate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1015:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".calendar-date{position:relative}.calendar-date-marker{position:absolute;left:50%;bottom:3px;width:4px;height:4px;margin-left:-2px;border-radius:2px;background-color:transparent}.calendar-date-marker.calendar-date-marker--free{background-color:var(--v-green-base)}.calendar-date-marker.calendar-date-marker--some-free{background-color:var(--v-primary-base)}.calendar-date-marker.calendar-date-marker--occupied{background-color:var(--v-orange-base)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1016:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerDialog_vue_vue_type_style_index_0_id_3bb5b460_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(963);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerDialog_vue_vue_type_style_index_0_id_3bb5b460_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerDialog_vue_vue_type_style_index_0_id_3bb5b460_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerDialog_vue_vue_type_style_index_0_id_3bb5b460_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerDialog_vue_vue_type_style_index_0_id_3bb5b460_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1017:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".selects-language[data-v-3bb5b460]{width:auto!important;min-width:120px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1018:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Steps_vue_vue_type_style_index_0_id_307c13c8_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(964);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Steps_vue_vue_type_style_index_0_id_307c13c8_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Steps_vue_vue_type_style_index_0_id_307c13c8_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Steps_vue_vue_type_style_index_0_id_307c13c8_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Steps_vue_vue_type_style_index_0_id_307c13c8_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1019:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(68);
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(516);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
// Module
___CSS_LOADER_EXPORT___.push([module.i, "@media only screen and (min-width:992px){.steps[data-v-307c13c8]{margin-bottom:20px}}@media only screen and (max-width:991px){.steps-wrap[data-v-307c13c8]{width:calc(100% + 30px);height:50px;margin-left:-15px;margin-bottom:24px;overflow:hidden}}@media only screen and (max-width:991px){.steps-helper[data-v-307c13c8]{padding-bottom:17px;overflow-x:auto;overflow-y:hidden;box-sizing:content-box}}.steps-list[data-v-307c13c8]{display:flex;justify-content:space-between}@media only screen and (max-width:991px){.steps-list[data-v-307c13c8]{height:50px}}.steps .step-item[data-v-307c13c8]{position:relative;display:flex;align-items:center;width:100%;max-width:312px;margin-right:20px;padding:10px 22px;letter-spacing:.3px}@media only screen and (min-width:992px){.steps .step-item[data-v-307c13c8]{height:52px}.steps .step-item[data-v-307c13c8]:last-child{margin-right:0}}@media only screen and (min-width:992px)and (max-width:1439px){.steps .step-item[data-v-307c13c8]{margin-right:10px;padding:10px}}@media only screen and (max-width:991px){.steps .step-item[data-v-307c13c8]{flex:1 0 220px;width:220px;margin:0 0 0 15px}}@media only screen and (max-width:639px){.steps .step-item[data-v-307c13c8]{flex:1 0 280px;width:280px;padding:10px 5px 10px 12px}}.steps .step-item a[data-v-307c13c8]{position:absolute;top:0;left:0;width:100%;height:100%;z-index:2}.steps .step-item-helper[data-v-307c13c8]{position:relative;padding-left:48px}@media only screen and (max-width:639px){.steps .step-item-helper[data-v-307c13c8]{padding-left:45px}}.steps .step-item-number[data-v-307c13c8]{position:absolute;top:50%;left:0;display:flex;align-items:center;justify-content:center;width:33px;height:33px;padding:0 0 3px 2px;border-radius:50%;background:linear-gradient(126.15deg,rgba(128,182,34,.72),rgba(60,135,248,.72) 102.93%);transform:translateY(-50%)}.steps .step-item-number span[data-v-307c13c8]{position:relative;display:inline-block;font-size:16px;font-weight:700}.steps .step-item-title[data-v-307c13c8]{font-size:14px;font-weight:700;line-height:1.28}.steps .step-item-text[data-v-307c13c8]{font-size:12px;line-height:1.5}.steps .step-item:not(.step-item--active) .step-item-number span[data-v-307c13c8]{color:var(--v-success-base);background:linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));background:-webkit-linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));-webkit-background-clip:text;-webkit-text-fill-color:transparent}.steps .step-item:not(.step-item--active) .step-item-number[data-v-307c13c8]:before{content:\"\";position:absolute;top:1px;left:1px;width:calc(100% - 2px);height:calc(100% - 2px);background-color:var(--v-greyBg-base);border-radius:50%}.steps .step-item--active[data-v-307c13c8],.steps .step-item--link[data-v-307c13c8]:hover{background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");background-repeat:no-repeat;background-size:100% 100%;background-position:50%}.steps .step-item--active .step-item-number[data-v-307c13c8],.steps .step-item--link:hover .step-item-number[data-v-307c13c8]{color:#fff}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1027:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/TimePickerDialog.vue?vue&type=template&id=3bb5b460&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{"dialog":_vm.isShownTimePickerDialog,"max-width":"1030","custom-class":"time-picker","persistent":"","fullscreen":_vm.$vuetify.breakpoint.smAndDown}},_vm.$listeners),[_c('div',{staticClass:"time-picker-header text--gradient"},[_vm._v("\n    "+_vm._s(_vm.$t('schedule_your_lessons'))+":\n  ")]),_vm._v(" "),_c('div',{staticClass:"wrap"},[_c('div',{staticClass:"time-picker-body"},[_c('div',{staticClass:"time-picker-selects mb-md-2"},[_c('div',{staticClass:"selects"},[_c('div',{staticClass:"selects-language"},[_c('div',{staticClass:"selects-lesson-value d-flex align-center"},[_c('div',{staticClass:"icon icon-flag elevation-2"},[(_vm.language.isoCode)?_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (_vm.language.isoCode) + ".svg"),"width":"18","height":"18"}}):_vm._e()],1),_vm._v("\n              "+_vm._s(_vm.language.name)+"\n            ")])]),_vm._v(" "),(_vm.course)?_c('div',{staticClass:"selects-course"},[_vm._v("\n            "+_vm._s(_vm.course)+"\n          ")]):_vm._e(),_vm._v(" "),_c('div',{staticClass:"selects-lesson"},[_c('div',{staticClass:"selects-lesson-value d-flex align-center"},[_c('div',{staticClass:"icon"},[_c('v-img',{attrs:{"src":__webpack_require__(504),"width":"18","height":"18"}})],1),_vm._v("\n\n              "+_vm._s(_vm.$tc('lessons_count', _vm.countLessons))+"\n            ")])]),_vm._v(" "),_c('div',{staticClass:"selects-duration"},[_c('div',{staticClass:"selects-lesson-value d-flex align-center"},[_c('div',{staticClass:"icon"},[_c('v-img',{attrs:{"src":__webpack_require__(504),"width":"18","height":"18"}})],1),_vm._v("\n\n              "+_vm._s(_vm.$tc('minutes_count', _vm.lessonLength))+"\n            ")])])])]),_vm._v(" "),_c('time-picker',_vm._g({attrs:{"username":_vm.username,"lesson-length":_vm.lessonLength,"quantity-lessons":_vm.countLessons,"current-time":_vm.currentTime,"is-shown-time-picker-dialog":_vm.isShownTimePickerDialog}},_vm.$listeners))],1)]),_vm._v(" "),_c('div',{staticClass:"time-picker-footer d-flex justify-md-end"},[_c('v-btn',{attrs:{"small":"","color":"primary","disabled":_vm.isNotValid},on:{"click":_vm.scheduleLessons}},[_vm._v("\n      "+_vm._s(_vm.$t('schedule_now'))+"\n    ")])],1)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-lessons/TimePickerDialog.vue?vue&type=template&id=3bb5b460&scoped=true&

// EXTERNAL MODULE: ./components/LDialog.vue + 5 modules
var LDialog = __webpack_require__(28);

// EXTERNAL MODULE: ./components/TimePicker.vue + 4 modules
var TimePicker = __webpack_require__(955);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/TimePickerDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var TimePickerDialogvue_type_script_lang_js_ = ({
  name: 'TimePickerDialog',
  components: {
    LDialog: LDialog["default"],
    TimePicker: TimePicker["default"]
  },
  props: {
    isShownTimePickerDialog: {
      type: Boolean,
      required: true
    },
    username: {
      type: String,
      required: true
    },
    language: {
      type: Object,
      required: true
    },
    currentTime: {
      type: Object,
      required: true
    },
    lessonLength: {
      type: Number,
      required: true
    },
    countLessons: {
      type: Number,
      required: true
    },
    purchaseId: {
      type: Number,
      required: true
    },
    course: {
      type: String,
      required: true
    }
  },
  computed: {
    selectedSlots() {
      return this.$store.state.teacher_profile.selectedSlots || [];
    },

    isNotValid() {
      return this.selectedSlots.length === 0;
    }

  },
  methods: {
    scheduleLessons() {
      const slots = [];

      if (this.selectedSlots.length) {
        this.selectedSlots.forEach(item => {
          item.forEach(el => slots.push(el.id));
        });
      }

      if (slots.length) {
        this.$store.dispatch('lesson/scheduleLesson', {
          purchaseId: this.purchaseId,
          slots
        }).then(() => {
          this.$store.commit('user/DECREASE_UNSCHEDULED_LESSONS_NUMBER');
          this.$emit('close-dialog');
          this.$store.commit('teacher_profile/RESET_SELECTED_SLOTS');
          this.$router.push({
            path: '/user/lessons'
          });
        }).catch(e => {
          this.$store.dispatch('snackbar/error');
          console.info(e);
        });
      }
    }

  }
});
// CONCATENATED MODULE: ./components/user-lessons/TimePickerDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_lessons_TimePickerDialogvue_type_script_lang_js_ = (TimePickerDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-lessons/TimePickerDialog.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1016)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_lessons_TimePickerDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "3bb5b460",
  "6e78877e"
  
)

/* harmony default export */ var TimePickerDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {TimePicker: __webpack_require__(955).default,LDialog: __webpack_require__(28).default})


/* vuetify-loader */



installComponents_default()(component, {VBtn: VBtn["a" /* default */],VImg: VImg["a" /* default */]})


/***/ }),

/***/ 1032:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Calendar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(976);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Calendar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Calendar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Calendar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Calendar_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1033:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".calendar{padding:26px 22px 38px;color:var(--v-greyBg-base);background-color:var(--v-darkLight-base);box-shadow:0 30px 84px rgba(19,10,46,.08),0 8px 32px rgba(19,10,46,.07),0 3px 14px rgba(19,10,46,.03),0 1px 3px rgba(19,10,46,.13);border-radius:16px}@media only screen and (max-width:1439px){.calendar{padding:24px 12px 32px}}.calendar-title{font-size:18px}.calendar .theme--light.v-calendar-weekly{border:none!important;background-color:transparent!important}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday{font-weight:700;color:var(--v-greyBg-base)!important;text-overflow:unset;text-transform:capitalize;border:inherit!important}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday.v-outside{background-color:inherit!important}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day{border:inherit!important;background-color:inherit!important}@media only screen and (max-width:1215px){.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day-label{margin-top:8px}}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day .v-btn{min-width:38px!important;width:38px!important;height:38px!important;font-size:14px!important;color:#fff!important}@media only screen and (max-width:1215px){.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day .v-btn{min-width:32px!important;width:32px!important;height:32px!important;font-size:12px!important}}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day.v-outside{opacity:.7}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day.v-outside .v-btn{color:var(--v-greyBg-darken2)!important}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day.v-present .v-btn{font-weight:600!important;background:linear-gradient(126.15deg,rgba(128,182,34,.48),rgba(60,135,248,.48) 102.93%)!important}.calendar--read-only .v-calendar-weekly__day>*{cursor:auto!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1034:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1035);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("0769cd4a", content, true)

/***/ }),

/***/ 1035:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-calendar-events .v-event-timed{border:1px solid!important}.theme--light.v-calendar-events .v-event-more{background-color:#fff}.theme--light.v-calendar-events .v-event-more.v-outside{background-color:#f7f7f7}.theme--dark.v-calendar-events .v-event-timed{border:1px solid!important}.theme--dark.v-calendar-events .v-event-more{background-color:#303030}.theme--dark.v-calendar-events .v-event-more.v-outside{background-color:#202020}.v-calendar .v-event{line-height:20px;margin-right:-1px;border-radius:4px}.v-calendar .v-event,.v-calendar .v-event-more{position:relative;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:12px;cursor:pointer;z-index:1}.v-calendar .v-event-more{font-weight:700}.v-calendar .v-event-timed-container{position:absolute;top:0;bottom:0;left:0;right:0;margin-right:10px;pointer-events:none}.v-calendar .v-event-timed{position:absolute;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:12px;cursor:pointer;border-radius:4px;pointer-events:all}.v-calendar.v-calendar-events .v-calendar-weekly__head-weekday{margin-right:-1px}.v-calendar.v-calendar-events .v-calendar-weekly__day{overflow:visible;margin-right:-1px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1036:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-calendar-weekly{background-color:#fff;border-top:1px solid #e0e0e0;border-left:1px solid #e0e0e0}.theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday{border-right:1px solid #e0e0e0;color:#000}.theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday.v-past{color:rgba(0,0,0,.38)}.theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday.v-outside{background-color:#f7f7f7}.theme--light.v-calendar-weekly .v-calendar-weekly__head-weeknumber{background-color:#f1f3f4;border-right:1px solid #e0e0e0}.theme--light.v-calendar-weekly .v-calendar-weekly__day{border-right:1px solid #e0e0e0;border-bottom:1px solid #e0e0e0;color:#000}.theme--light.v-calendar-weekly .v-calendar-weekly__day.v-outside{background-color:#f7f7f7}.theme--light.v-calendar-weekly .v-calendar-weekly__weeknumber{background-color:#f1f3f4;border-right:1px solid #e0e0e0;border-bottom:1px solid #e0e0e0;color:#000}.theme--dark.v-calendar-weekly{background-color:#303030;border-top:1px solid #9e9e9e;border-left:1px solid #9e9e9e}.theme--dark.v-calendar-weekly .v-calendar-weekly__head-weekday{border-right:1px solid #9e9e9e;color:#fff}.theme--dark.v-calendar-weekly .v-calendar-weekly__head-weekday.v-past{color:hsla(0,0%,100%,.5)}.theme--dark.v-calendar-weekly .v-calendar-weekly__head-weekday.v-outside{background-color:#202020}.theme--dark.v-calendar-weekly .v-calendar-weekly__head-weeknumber{background-color:#202020;border-right:1px solid #9e9e9e}.theme--dark.v-calendar-weekly .v-calendar-weekly__day{border-right:1px solid #9e9e9e;border-bottom:1px solid #9e9e9e;color:#fff}.theme--dark.v-calendar-weekly .v-calendar-weekly__day.v-outside{background-color:#202020}.theme--dark.v-calendar-weekly .v-calendar-weekly__weeknumber{background-color:#202020;border-right:1px solid #9e9e9e;border-bottom:1px solid #9e9e9e;color:#fff}.v-calendar-weekly{width:100%;height:100%;display:flex;flex-direction:column;min-height:0}.v-calendar-weekly__head{display:flex}.v-calendar-weekly__head,.v-calendar-weekly__head-weekday{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-calendar-weekly__head-weekday{flex:1 0 20px;padding:0 2px 6px;font-size:13px;overflow:hidden;text-align:center;text-overflow:ellipsis;text-transform:uppercase;white-space:nowrap}.v-calendar-weekly__head-weeknumber{position:relative;flex:0 0 24px}.v-calendar-weekly__week{display:flex;flex:1;height:unset;min-height:0}.v-calendar-weekly__weeknumber{display:flex;flex:0 0 24px;height:unset;min-height:0;padding-top:14.5px;text-align:center}.v-calendar-weekly__weeknumber>small{width:100%!important}.v-calendar-weekly__day{flex:1;width:0;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;padding:0;min-width:0}.v-calendar-weekly__day.v-present .v-calendar-weekly__day-month{color:currentColor}.v-calendar-weekly__day-label{text-decoration:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;box-shadow:none;text-align:center;margin:14px 0 0}.v-calendar-weekly__day-label .v-btn{font-size:12px;text-transform:none}.v-calendar-weekly__day-month{position:absolute;text-decoration:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;box-shadow:none;top:0;left:36px;height:32px;line-height:32px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1037:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1038);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("1ffbeeca", content, true)

/***/ }),

/***/ 1038:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-calendar-daily{background-color:#fff;border-left:1px solid #e0e0e0;border-top:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__intervals-head{border-right:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__intervals-head:after{background:#e0e0e0;background:linear-gradient(90deg,transparent,#e0e0e0)}.theme--light.v-calendar-daily .v-calendar-daily_head-day{border-right:1px solid #e0e0e0;border-bottom:1px solid #e0e0e0;color:#000}.theme--light.v-calendar-daily .v-calendar-daily_head-day.v-past .v-calendar-daily_head-day-label,.theme--light.v-calendar-daily .v-calendar-daily_head-day.v-past .v-calendar-daily_head-weekday{color:rgba(0,0,0,.38)}.theme--light.v-calendar-daily .v-calendar-daily__intervals-body{border-right:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__intervals-body .v-calendar-daily__interval-text{color:#424242}.theme--light.v-calendar-daily .v-calendar-daily__day{border-right:1px solid #e0e0e0;border-bottom:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__day-interval{border-top:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__day-interval:first-child{border-top:none!important}.theme--light.v-calendar-daily .v-calendar-daily__interval:after{border-top:1px solid #e0e0e0}.theme--dark.v-calendar-daily{background-color:#303030;border-left:1px solid #9e9e9e;border-top:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__intervals-head{border-right:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__intervals-head:after{background:#9e9e9e;background:linear-gradient(90deg,transparent,#9e9e9e)}.theme--dark.v-calendar-daily .v-calendar-daily_head-day{border-right:1px solid #9e9e9e;border-bottom:1px solid #9e9e9e;color:#fff}.theme--dark.v-calendar-daily .v-calendar-daily_head-day.v-past .v-calendar-daily_head-day-label,.theme--dark.v-calendar-daily .v-calendar-daily_head-day.v-past .v-calendar-daily_head-weekday{color:hsla(0,0%,100%,.5)}.theme--dark.v-calendar-daily .v-calendar-daily__intervals-body{border-right:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__intervals-body .v-calendar-daily__interval-text{color:#eee}.theme--dark.v-calendar-daily .v-calendar-daily__day{border-right:1px solid #9e9e9e;border-bottom:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__day-interval{border-top:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__day-interval:first-child{border-top:none!important}.theme--dark.v-calendar-daily .v-calendar-daily__interval:after{border-top:1px solid #9e9e9e}.v-calendar-daily{display:flex;flex-direction:column;overflow:hidden;height:100%}.v-calendar-daily__head{flex:none;display:flex}.v-calendar-daily__intervals-head{flex:none;position:relative}.v-calendar-daily__intervals-head:after{position:absolute;bottom:0;height:1px;left:0;right:0;content:\"\"}.v-calendar-daily_head-day{flex:1 1 auto;width:0;position:relative}.v-calendar-daily_head-weekday{padding:3px 0 0;font-size:11px;text-transform:uppercase}.v-calendar-daily_head-day-label,.v-calendar-daily_head-weekday{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-align:center}.v-calendar-daily_head-day-label{padding:0 0 3px;cursor:pointer}.v-calendar-daily__body{flex:1 1 60%;overflow:hidden;display:flex;position:relative;flex-direction:column}.v-calendar-daily__scroll-area{overflow-y:scroll;flex:1 1 auto;display:flex;align-items:flex-start}.v-calendar-daily__pane{width:100%;overflow-y:hidden;flex:none;display:flex;align-items:flex-start}.v-calendar-daily__day-container{display:flex;flex:1;width:100%;height:100%}.v-calendar-daily__intervals-body{flex:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-calendar-daily__interval{text-align:right;padding-right:8px;border-bottom:none;position:relative}.v-calendar-daily__interval:after{width:8px;position:absolute;height:1px;display:block;content:\"\";right:0;bottom:-1px}.v-calendar-daily__interval-text{display:block;position:relative;top:-6px;font-size:10px;padding-right:4px}.v-calendar-daily__day{flex:1;width:0;position:relative}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1039:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1040);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("2b825c72", content, true)

/***/ }),

/***/ 1040:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-calendar-category .v-calendar-category__column,.theme--light.v-calendar-category .v-calendar-category__column-header{border-right:1px solid #e0e0e0}.theme--dark.v-calendar-category .v-calendar-category__column,.theme--dark.v-calendar-category .v-calendar-category__column-header{border-right:1px solid #9e9e9e}.v-calendar-category .v-calendar-category__category{text-align:center}.v-calendar-category .v-calendar-daily__day-container .v-calendar-category__columns{position:absolute;height:100%;width:100%;top:0}.v-calendar-category .v-calendar-category__columns{display:flex}.v-calendar-category .v-calendar-category__columns .v-calendar-category__column,.v-calendar-category .v-calendar-category__columns .v-calendar-category__column-header{flex:1 1 auto;width:0;position:relative}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1044:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/UpcomingLesson.vue?vue&type=template&id=bc3603d2&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('lesson-item',{attrs:{"item":_vm.item,"user-statuses":_vm.userStatuses},scopedSlots:_vm._u([{key:"lessonAdditionalActionsTop",fn:function(){return [_c('div',[_c('nuxt-link',{attrs:{"to":"/user/settings#calendar"}},[(_vm.item.isSyncedWithCalendar)?[_c('v-img',{attrs:{"src":__webpack_require__(509),"width":"12","height":"13"}}),_vm._v("\n          "+_vm._s(_vm.$t('synced_with_my_calendar'))+"\n        ")]:[_c('v-img',{attrs:{"src":__webpack_require__(519),"width":"13","height":"14"}}),_vm._v("\n          "+_vm._s(_vm.$t('configure_calendar_sync'))+"\n        ")]],2)],1),_vm._v(" "),_c('div',[_c('a',{attrs:{"href":("/lesson/" + (_vm.item.lessonId) + "/icalendar/get")}},[_c('v-img',{attrs:{"src":__webpack_require__(508),"width":"14","height":"14"}}),_vm._v("\n        "+_vm._s(_vm.$t('download_ics_calendar_file'))+"\n      ")],1)])]},proxy:true}])})}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-lessons/UpcomingLesson.vue?vue&type=template&id=bc3603d2&

// EXTERNAL MODULE: ./components/user-lessons/LessonItem.vue + 4 modules
var LessonItem = __webpack_require__(946);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/UpcomingLesson.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var UpcomingLessonvue_type_script_lang_js_ = ({
  name: 'UpcomingLesson',
  components: {
    LessonItem: LessonItem["default"]
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    userStatuses: {
      type: Object,
      default: () => ({})
    }
  }
});
// CONCATENATED MODULE: ./components/user-lessons/UpcomingLesson.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_lessons_UpcomingLessonvue_type_script_lang_js_ = (UpcomingLessonvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-lessons/UpcomingLesson.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_lessons_UpcomingLessonvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "32560e4f"
  
)

/* harmony default export */ var UpcomingLesson = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */


installComponents_default()(component, {VImg: VImg["a" /* default */]})


/***/ }),

/***/ 1045:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/PastLesson.vue?vue&type=template&id=5eda8af2&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('lesson-item',{attrs:{"item":_vm.item,"user-statuses":_vm.userStatuses},scopedSlots:_vm._u([(_vm.item.isInitialized || _vm.item.isFinished)?{key:"lessonAdditionalActionsTop",fn:function(){return [_c('div',[_c('span',{staticClass:"action",on:{"click":_vm.downloadPdfClickHandler}},[_c('v-img',{attrs:{"src":__webpack_require__(508),"width":"16","height":"16"}}),_vm._v("\n        "+_vm._s(_vm.$t('whiteboard_pdf'))+"\n      ")],1)])]},proxy:true}:null,{key:"lessonAdditionalActionsBottom",fn:function(scope){return [(_vm.allowedRate)?_c('div',[_c('span',{staticClass:"action",on:{"click":_vm.rateLesson}},[_c('v-img',{staticStyle:{"left":"1px"},attrs:{"src":__webpack_require__(520),"width":"14","height":"14"}}),_vm._v("\n        "+_vm._s(_vm.$t('rate_lesson'))+"\n      ")],1)]):_vm._e(),_vm._v(" "),(_vm.isTeacher)?[(_vm.item.isFinished)?[_c('div',[_c('div',[_c('v-img',{attrs:{"src":__webpack_require__(517),"width":"16","height":"16"}}),_vm._v("\n            "+_vm._s(_vm.$t('class_finished'))+"\n          ")],1)])]:[_c('div',[_c('span',{staticClass:"action",on:{"click":function($event){return _vm.showFinishDialog(scope)}}},[_c('v-img',{staticStyle:{"left":"3px"},attrs:{"src":__webpack_require__(509),"width":"12","height":"13"}}),_vm._v("\n            "+_vm._s(_vm.$t('mark_as_finished'))+"\n          ")],1)])]]:_vm._e()]}},{key:"dialog",fn:function(scope){return [(_vm.dialogType === 'finishDialog')?[_c('div',{staticClass:"lesson-dialog-title"},[_c('div',{staticClass:"lesson-dialog-title-icon"},[_c('v-img',{attrs:{"src":__webpack_require__(518),"width":"20","height":"20"}})],1),_vm._v(" "),_c('span',{staticClass:"font-weight-medium text--gradient"},[_vm._v("\n          "+_vm._s(_vm.$t('do_you_want_to_finish_this_class'))+"\n        ")])]),_vm._v(" "),_c('div',{staticClass:"lesson-dialog-content l-scroll l-scroll--grey"},[_vm._v("\n        "+_vm._s(_vm.$t('this_will_move_class_to_past_lessons_and_trigger_payment'))+"\n      ")]),_vm._v(" "),_c('div',{staticClass:"lesson-dialog-buttons"},[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"greyDark","outlined":""},on:{"click":function($event){return _vm.closeDialog(scope)}}},[_vm._v("\n          "+_vm._s(_vm.$t('do_not_finish_class'))+"\n        ")]),_vm._v(" "),_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"primary"},on:{"click":function($event){return _vm.finishClickHandler(scope)}}},[_vm._v("\n          "+_vm._s(_vm.$t('finish_class'))+"\n        ")])],1)]:_vm._e()]}}],null,true)},[_vm._v(" "),_vm._v(" "),_vm._v(" "),(_vm.allowedRate)?_c('lesson-evaluation-dialog',{attrs:{"item":_vm.feedbackLessonData,"dialog":_vm.isShownEvaluationDialog,"close-button":"","persistent":false},on:{"close":_vm.closeEvaluationDialog}}):_vm._e()],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-lessons/PastLesson.vue?vue&type=template&id=5eda8af2&

// EXTERNAL MODULE: ./components/user-lessons/LessonItem.vue + 4 modules
var LessonItem = __webpack_require__(946);

// EXTERNAL MODULE: ./components/user-lessons/LessonEvaluationDialog.vue + 4 modules
var LessonEvaluationDialog = __webpack_require__(428);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/PastLesson.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var PastLessonvue_type_script_lang_js_ = ({
  name: 'PastLesson',
  components: {
    LessonItem: LessonItem["default"],
    LessonEvaluationDialog: LessonEvaluationDialog["default"]
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    userStatuses: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      dialogType: null,
      feedbackLessonData: {},
      isShownEvaluationDialog: false
    };
  },

  computed: {
    isTeacher() {
      return this.$store.getters['user/isTeacher'];
    },

    isStudent() {
      return this.$store.getters['user/isStudent'];
    },

    allowedRate() {
      return this.isStudent && this.item.isSkippedFeedback && this.item.isFinished;
    }

  },
  methods: {
    downloadPdfClickHandler() {
      this.$store.dispatch('lesson/generatePdf', this.item.lessonId);
    },

    finishClickHandler(scope) {
      this.$store.dispatch('lesson/finishLesson', this.item.lessonId).then(() => {
        this.$store.commit('lesson/UPDATE_LESSON', {
          lessonId: this.item.lessonId,
          lessonStatus: 2
        });
        this.$store.dispatch('snackbar/success', {
          successMessage: 'class_successfully_finished'
        });
      }).catch(e => {
        this.$store.dispatch('loadingStop');
        this.$store.dispatch('snackbar/error');
        console.info(e);
      }).finally(() => this.closeDialog(scope));
    },

    showFinishDialog(scope) {
      this.dialogType = 'finishDialog';
      scope.showDialog();
    },

    closeDialog(scope) {
      scope.closeDialog();
      setTimeout(() => {
        this.dialogType = null;
      }, 500);
    },

    rateLesson() {
      this.$store.dispatch('lesson/getFeedbackItem', this.item.lessonId).then(data => {
        this.feedbackLessonData = data;
        this.isShownEvaluationDialog = true;
      });
    },

    closeEvaluationDialog() {
      this.isShownEvaluationDialog = false;
      this.feedbackLessonData = {};
    }

  }
});
// CONCATENATED MODULE: ./components/user-lessons/PastLesson.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_lessons_PastLessonvue_type_script_lang_js_ = (PastLessonvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-lessons/PastLesson.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_lessons_PastLessonvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "6e07c745"
  
)

/* harmony default export */ var PastLesson = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */



installComponents_default()(component, {VBtn: VBtn["a" /* default */],VImg: VImg["a" /* default */]})


/***/ }),

/***/ 1046:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/UnscheduledLesson.vue?vue&type=template&id=20ef4ce4&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('lesson-item',{attrs:{"item":_vm.item,"user-statuses":_vm.userStatuses},scopedSlots:_vm._u([{key:"date",fn:function(){return [_c('div',{staticClass:"date"},[_vm._v("\n      "+_vm._s(_vm.item.availableLessons)+" "+_vm._s(_vm.$t('of'))+" "+_vm._s(_vm.item.countLessons)+"\n    ")]),_vm._v(" "),_c('div',{staticClass:"remaining",domProps:{"innerHTML":_vm._s(_vm.$t('remaining_lessons'))}})]},proxy:true},{key:"lessonInfo",fn:function(){return [_c('div',[_c('span',[_vm._v(_vm._s(_vm.$t('purchase_date'))+":")]),_vm._v(" "),_c('span',{staticClass:"text--gradient font-weight-bold text-no-wrap"},[_vm._v("\n        "+_vm._s(_vm.$dayjs(_vm.item.createdDate).tz(_vm.timeZone).format('LL'))+"\n      ")])]),_vm._v(" "),(_vm.isTeacher)?_c('div',[_c('span',[_vm._v(_vm._s(_vm.$t('price_per_lesson'))+":")]),_vm._v(" "),_c('span',{staticClass:"text--gradient font-weight-bold text-no-wrap"},[_vm._v("\n        "+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.getPrice(_vm.item.priceForOneLesson))+"\n      ")])]):_vm._e()]},proxy:true},{key:"lessonActions",fn:function(){return [(_vm.isStudent && !_vm.userIsDeleted)?_c('v-btn',{staticClass:"font-weight-medium ml-1 mb-1",attrs:{"color":"primary"},on:{"click":_vm.schedule}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"20","height":"20","viewBox":"0 0 20 20"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#calendar")}})]),_vm._v("\n      "+_vm._s(_vm.$t('schedule'))+"\n    ")]):_vm._e()]},proxy:true}])},[_vm._v(" "),_vm._v(" "),_vm._v(" "),_c('time-picker-dialog',{attrs:{"is-shown-time-picker-dialog":_vm.isShownTimePickerDialog,"username":_vm.item.teacherUsername,"language":_vm.item.language,"course":_vm.item.course,"lesson-length":_vm.item.lessonLength,"count-lessons":_vm.item.availableLessons,"purchase-id":_vm.item.purchaseId,"current-time":_vm.currentTime},on:{"update-current-time":function($event){_vm.currentTime = $event},"close-dialog":_vm.closeTimePickerDialog}})],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-lessons/UnscheduledLesson.vue?vue&type=template&id=20ef4ce4&

// EXTERNAL MODULE: ./helpers/index.js
var helpers = __webpack_require__(501);

// EXTERNAL MODULE: ./components/user-lessons/LessonItem.vue + 4 modules
var LessonItem = __webpack_require__(946);

// EXTERNAL MODULE: ./components/user-lessons/TimePickerDialog.vue + 4 modules
var TimePickerDialog = __webpack_require__(1027);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/UnscheduledLesson.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



/* harmony default export */ var UnscheduledLessonvue_type_script_lang_js_ = ({
  name: 'UnscheduledLesson',
  components: {
    LessonItem: LessonItem["default"],
    TimePickerDialog: TimePickerDialog["default"]
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    userStatuses: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      getPrice: helpers["getPrice"],
      currentTime: this.$dayjs(),
      isShownTimePickerDialog: false
    };
  },

  computed: {
    isTeacher() {
      return this.$store.getters['user/isTeacher'];
    },

    isStudent() {
      return this.$store.getters['user/isStudent'];
    },

    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    },

    userIsDeleted() {
      return this.item.userIsDeleted;
    },

    timeZone() {
      return this.$store.getters['user/timeZone'];
    }

  },
  methods: {
    closeTimePickerDialog() {
      this.isShownTimePickerDialog = false;
      this.$store.commit('teacher_profile/RESET_SELECTED_SLOTS');
    },

    schedule() {
      this.$store.dispatch('teacher_profile/getSlots', {
        slug: this.item.teacherUsername,
        date: this.currentTime.day(1)
      }).then(() => {
        this.isShownTimePickerDialog = true;
      });
    }

  }
});
// CONCATENATED MODULE: ./components/user-lessons/UnscheduledLesson.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_lessons_UnscheduledLessonvue_type_script_lang_js_ = (UnscheduledLessonvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// CONCATENATED MODULE: ./components/user-lessons/UnscheduledLesson.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_lessons_UnscheduledLessonvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "ddda4bfe"
  
)

/* harmony default export */ var UnscheduledLesson = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */


installComponents_default()(component, {VBtn: VBtn["a" /* default */]})


/***/ }),

/***/ 1057:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonsPage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(994);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonsPage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonsPage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonsPage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonsPage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1058:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-lessons{--sidebar-width:345px;padding-bottom:140px}@media only screen and (max-width:1439px){.user-lessons{--sidebar-width:325px}}@media only screen and (max-width:1215px){.user-lessons{--sidebar-width:298px}}@media only screen and (max-width:991px){.user-lessons{padding-bottom:60px}}.user-lessons-wrap{max-width:1360px;padding-bottom:25px}@media only screen and (min-width:1216px){.user-lessons-header{display:flex;align-items:center;justify-content:space-between}}@media only screen and (max-width:1215px){.user-lessons-header{flex-wrap:wrap}.user-lessons-header>div{width:100%}}.user-lessons-title{position:relative}@media only screen and (min-width:992px){.user-lessons-title{margin-right:24px}}@media only screen and (max-width:1215px){.user-lessons-title{margin-right:0}}.user-lessons-title h1{white-space:nowrap;font-size:24px;line-height:1.333}@media only screen and (max-width:479px){.user-lessons-title h1{font-size:20px}}.user-lessons-credits{position:absolute;left:0;bottom:-20px;font-size:13px;color:var(--v-dark-lighten3)}.user-lessons-controls{justify-content:space-between;align-items:center;flex-grow:1}@media only screen and (max-width:1215px){.user-lessons-controls{margin-top:18px}}@media only screen and (min-width:1216px){.user-lessons-controls{max-width:970px}}@media only screen and (max-width:991px){.user-lessons-controls{max-width:100%;flex-wrap:wrap}}.user-lessons-search{width:100%}@media only screen and (min-width:992px){.user-lessons-search{min-width:240px;flex-basis:380px}}.user-lessons-nav{min-width:508px;margin-left:18px;padding:4px;background-color:#fff;box-shadow:0 4px 14px rgba(217,225,236,.47);border-radius:16px}@media only screen and (max-width:1439px){.user-lessons-nav{min-width:455px}}@media only screen and (max-width:991px){.user-lessons-nav{width:100%;min-width:auto;margin:12px 0 0}}.user-lessons-nav>a:not(:last-child){margin-right:4px}.user-lessons-nav .v-btn.nav-btn{flex-grow:1;border-radius:14px;background-color:transparent!important}@media only screen and (max-width:991px){.user-lessons-nav .v-btn.nav-btn{width:33.3333%;min-width:70px!important;text-align:center}}@media only screen and (max-width:639px){.user-lessons-nav .v-btn.nav-btn{font-size:13px!important;font-weight:400!important}}.user-lessons-nav .v-btn:before{background-color:transparent}.user-lessons-nav .v-btn:not(.v-btn--active){color:var(--v-greyDark-base)}.user-lessons-nav .v-btn--active:before,.user-lessons-nav .v-btn--active:hover:before{background:linear-gradient(126.15deg,rgba(128,182,34,.16),rgba(60,135,248,.16) 102.93%);opacity:1}@media only screen and (min-width:768px){.user-lessons-body{display:flex}}.user-lessons-content{display:flex;flex-direction:column;justify-content:space-between;width:calc(100% - var(--sidebar-width))}@media only screen and (max-width:991px){.user-lessons-content{width:100%}}.user-lessons-content h1{font-size:32px}.user-lessons-content .lessons-list>div:not(:last-child){margin-bottom:20px}@media only screen and (max-width:767px){.user-lessons-content .lessons-list>div:not(:last-child){margin-bottom:10px}}.user-lessons-sidebar{width:var(--sidebar-width)}.user-lessons-sidebar-helper{padding-left:20px}@media only screen and (max-width:1215px){.user-lessons-sidebar-helper{padding-left:16px}}.user-lessons-sidebar-helper>div:not(:last-child){margin-bottom:10px}@media only screen and (max-width:1215px){.user-lessons--student .user-lessons-title{margin-bottom:32px}}.user-lessons .lessons-list-empty .steps{border-bottom:none!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1066:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/LessonsPage.vue?vue&type=template&id=1a996957&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-col',{staticClass:"col-12 px-0"},[_c('div',{class:[
      'user-lessons',
      ("user-lessons--" + (_vm.isTeacher ? 'teacher' : 'student')) ]},[_c('v-container',{staticClass:"pa-0",attrs:{"fluid":""}},[_c('v-row',{attrs:{"no-gutters":""}},[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"user-lessons-wrap mx-auto"},[_c('div',{staticClass:"user-lessons-header mb-2 mb-md-4 mb-lg-5"},[_c('div',{staticClass:"user-lessons-title"},[_c('h1',{staticClass:"font-weight-medium"},[_vm._v(_vm._s(_vm.$t('my_lessons'))+" 📚")]),_vm._v(" "),(_vm.isStudent && _vm.userCredit)?_c('div',{staticClass:"user-lessons-credits text-no-wrap"},[_vm._v("\n                  "+_vm._s(_vm.$t('langu_credit'))+": "+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.getPrice(_vm.userCredit))+"\n                ")]):_vm._e()]),_vm._v(" "),_c('div',{staticClass:"user-lessons-controls d-flex"},[_c('div',{staticClass:"user-lessons-search"},[_c('search-input',{staticClass:"search-input--inner-border",attrs:{"placeholder":_vm.isTeacher ? 'search_for_student' : 'search_for_teacher'},on:{"submit":_vm.searchSubmitHandler},model:{value:(_vm.searchQuery),callback:function ($$v) {_vm.searchQuery=(typeof $$v === 'string'? $$v.trim(): $$v)},expression:"searchQuery"}})],1),_vm._v(" "),_c('div',{staticClass:"user-lessons-nav d-flex"},[_c('v-btn',{staticClass:"nav-btn font-weight-medium",attrs:{"to":_vm.localePath({ path: '/user/lessons' }),"height":"48"}},[_vm._v("\n                    "+_vm._s(_vm.$t('upcoming_lessons'))+"\n                  ")]),_vm._v(" "),_c('v-btn',{staticClass:"nav-btn font-weight-medium",attrs:{"to":_vm.localePath({ path: '/user/past-lessons' }),"height":"48"}},[_vm._v("\n                    "+_vm._s(_vm.$t('past_lessons'))+"\n                  ")]),_vm._v(" "),_c('v-btn',{staticClass:"nav-btn font-weight-medium",attrs:{"to":_vm.localePath({ path: '/user/unscheduled-lessons' }),"height":"48"}},[_vm._v("\n                    "+_vm._s(_vm.$t('unscheduled_lessons'))+" ("+_vm._s(_vm.totalNumberUnscheduledLessons)+")\n                  ")])],1)])]),_vm._v(" "),_c('div',{staticClass:"user-lessons-body"},[_c('div',{staticClass:"user-lessons-content"},[_c('div',[_c('div',{staticClass:"user-lessons-filters d-flex"},[(_vm.$route.query.search)?_c('l-chip',{staticClass:"mb-1 mr-1",attrs:{"clickable":true,"label":_vm.$route.query.search,"light":""},on:{"click:close":_vm.resetSearch}}):_vm._e(),_vm._v(" "),(_vm.selectedDate)?_c('l-chip',{staticClass:"mb-1 mr-1",attrs:{"label":_vm.$dayjs(_vm.selectedDate, 'YYYY-MM-DD').format('D MMMM'),"light":""},on:{"click:close":_vm.resetSelectedDate}}):_vm._e()],1),_vm._v(" "),(_vm.lessons.length)?[_c('div',{staticClass:"lessons-list"},[_vm._l((_vm.lessons),function(lesson){return [_c(_vm.lessonComponents[lesson.type],_vm._b({key:lesson.id,tag:"component"},'component',{ item: lesson, userStatuses: _vm.userStatuses },false))]})],2)]:[(_vm.isStudent)?_c('div',{staticClass:"lessons-list-empty"},[_c('steps',{attrs:{"active-item-id":0,"item-link":{ id: 1, path: '/teacher-listing' }}})],1):_vm._e()]],2),_vm._v(" "),(_vm.totalPages > 1)?_c('div',{staticClass:"mt-3 mt-md-5 text-center"},[_c('pagination',{attrs:{"current-page":_vm.page,"total-pages":_vm.totalPages,"route":_vm.route}})],1):_vm._e()]),_vm._v(" "),(_vm.$vuetify.breakpoint.mdAndUp)?_c('aside',{staticClass:"user-lessons-sidebar d-none d-md-block"},[_c('div',{staticClass:"user-lessons-sidebar-helper"},_vm._l((4),function(i){return _c('calendar',{key:i,attrs:{"type":_vm.type,"items":_vm.calendarItems,"current-date":_vm.$dayjs().add(i - 1, 'month')},on:{"click-date":_vm.clickDate}})}),1)]):_vm._e()])])])],1)],1)],1)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-lessons/LessonsPage.vue?vue&type=template&id=1a996957&

// EXTERNAL MODULE: ./utils/hash.js
var hash = __webpack_require__(655);

// EXTERNAL MODULE: ./helpers/index.js
var helpers = __webpack_require__(501);

// EXTERNAL MODULE: ./mixins/Avatars.vue + 2 modules
var Avatars = __webpack_require__(932);

// EXTERNAL MODULE: ./mixins/StatusOnline.vue + 2 modules
var StatusOnline = __webpack_require__(992);

// EXTERNAL MODULE: ./components/form/SearchInput.vue + 4 modules
var SearchInput = __webpack_require__(945);

// EXTERNAL MODULE: ./components/Calendar.vue + 24 modules
var Calendar = __webpack_require__(990);

// EXTERNAL MODULE: ./components/user-lessons/UpcomingLesson.vue + 4 modules
var UpcomingLesson = __webpack_require__(1044);

// EXTERNAL MODULE: ./components/user-lessons/PastLesson.vue + 4 modules
var PastLesson = __webpack_require__(1045);

// EXTERNAL MODULE: ./components/user-lessons/UnscheduledLesson.vue + 4 modules
var UnscheduledLesson = __webpack_require__(1046);

// EXTERNAL MODULE: ./components/Steps.vue + 4 modules
var Steps = __webpack_require__(984);

// EXTERNAL MODULE: ./components/Pagination.vue + 4 modules
var Pagination = __webpack_require__(930);

// EXTERNAL MODULE: ./components/LChip.vue + 4 modules
var LChip = __webpack_require__(70);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/LessonsPage.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//












/* harmony default export */ var LessonsPagevue_type_script_lang_js_ = ({
  name: 'LessonsPage',
  components: {
    SearchInput: SearchInput["default"],
    Calendar: Calendar["default"],
    Steps: Steps["default"],
    Pagination: Pagination["default"],
    LChip: LChip["default"]
  },
  mixins: [Avatars["a" /* default */], StatusOnline["a" /* default */]],
  props: {
    page: {
      type: Number,
      default: 1
    },
    route: {
      type: String,
      required: true
    },
    type: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      getPrice: helpers["getPrice"],
      lessonComponents: {
        upcoming: UpcomingLesson["default"],
        past: PastLesson["default"],
        unscheduled: UnscheduledLesson["default"]
      },
      searchQuery: '',
      now: this.$dayjs(),
      selectedDate: null
    };
  },

  computed: {
    isTeacher() {
      return this.$store.getters['user/isTeacher'];
    },

    isStudent() {
      return this.$store.getters['user/isStudent'];
    },

    lessons() {
      return this.$store.getters['lesson/lessons'];
    },

    calendarItems() {
      return this.$store.state.lesson.calendarItems;
    },

    totalPages() {
      return Math.ceil(this.$store.state.lesson.totalQuantity / "16");
    },

    totalNumberUnscheduledLessons() {
      return this.$store.getters['user/totalNumberUnscheduledLessons'];
    },

    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    },

    userCredit() {
      return this.$store.getters['user/userCredit'];
    }

  },
  watch: {
    selectedDate() {
      this.setArrStatusId();
    },

    '$route.params.search': {
      handler() {
        this.setArrStatusId();
      },

      deep: true
    }
  },

  beforeMount() {
    var _this$$route$query$se, _this$$route$query;

    this.searchQuery = (_this$$route$query$se = (_this$$route$query = this.$route.query) === null || _this$$route$query === void 0 ? void 0 : _this$$route$query.search) !== null && _this$$route$query$se !== void 0 ? _this$$route$query$se : ''; // Check for paid trial event data

    if (localStorage.getItem('event_data') !== null) {
      try {
        const eventData = JSON.parse(localStorage.getItem('event_data'));

        if (eventData.event === 'purchase_paid_trial') {
          // Get user data
          const tidioData = this.$store.state.user.tidioData || {};
          const userData = this.$store.state.user.item || {};
          const userEmail = tidioData.email || '';
          const userName = `${userData.firstName || ''} ${userData.lastName || ''}`.trim(); // Add hashed user data to the items array

          if (eventData.ecommerce && eventData.ecommerce.items && eventData.ecommerce.items.length > 0) {
            eventData.ecommerce.items.forEach(item => {
              if (!item.user_name) {
                item.user_name = Object(hash["hashUserData"])(userName);
              }

              if (!item.email_id) {
                item.email_id = Object(hash["hashUserData"])(userEmail);
              }
            });
          } // Ensure dataLayer is initialized


          window.dataLayer = window.dataLayer || []; // Push event to dataLayer

          window.dataLayer.push({
            ecommerce: null
          });
          window.dataLayer.push(eventData); // Remove event data from localStorage

          localStorage.removeItem('event_data');
        }
      } catch (error) {
        console.log(error);
      }
    }

    this.setArrStatusId();
    this.refreshStatusOnline();
  },

  methods: {
    setArrStatusId() {
      const prop = this.isTeacher ? 'studentId' : 'teacherId';
      this.arrStatusId = this.lessons.map(item => item[prop]);
    },

    async searchSubmitHandler() {
      if (this.selectedDate) {
        await this.resetSelectedDate();
      }

      await this.$router.push({
        name: this.$route.name,
        params: {
          page: '1'
        },
        query: this.searchQuery ? {
          search: this.searchQuery
        } : {}
      });
    },

    async clickDate(date) {
      if (this.searchQuery.length) {
        await this.resetSearch();
      } // if (this.type === 'past' || this.type === 'upcoming') {


      const data = {
        date
      }; // if (this.type === 'past') data.type = 'past'

      if (this.type === 'upcoming') data.type = 'upcoming';
      this.$store.dispatch('lesson/getLessonsByDate', data).then(() => this.selectedDate = date); // }
    },

    resetSelectedDate() {
      return new Promise(resolve => {
        const data = {
          page: this.page,
          perPage: "16",
          type: this.type
        };
        this.$store.dispatch('lesson/getUpcomingLessons', data).then(() => {
          this.selectedDate = null;
          resolve();
        });
      });
    },

    resetSearch() {
      return new Promise(resolve => {
        this.searchQuery = '';
        this.$router.push({
          name: this.$route.name,
          params: {
            page: '1'
          },
          query: {}
        });
        setTimeout(() => {
          resolve();
        });
      });
    }

  }
});
// CONCATENATED MODULE: ./components/user-lessons/LessonsPage.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_lessons_LessonsPagevue_type_script_lang_js_ = (LessonsPagevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/user-lessons/LessonsPage.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1057)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_lessons_LessonsPagevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "3bb66f9f"
  
)

/* harmony default export */ var LessonsPage = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LChip: __webpack_require__(70).default,Steps: __webpack_require__(984).default,Pagination: __webpack_require__(930).default,Calendar: __webpack_require__(990).default})


/* vuetify-loader */





installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1485:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/lessons/thank-you/index.vue?vue&type=template&id=3e1341ac&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('lessons-page',{attrs:{"route":"/user/lessons","type":_vm.type}})}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/user/lessons/thank-you/index.vue?vue&type=template&id=3e1341ac&

// EXTERNAL MODULE: ./utils/hash.js
var hash = __webpack_require__(655);

// EXTERNAL MODULE: ./components/user-lessons/LessonsPage.vue + 4 modules
var LessonsPage = __webpack_require__(1066);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/lessons/thank-you/index.vue?vue&type=script&lang=js&
//
//
//
//


/* harmony default export */ var thank_youvue_type_script_lang_js_ = ({
  name: 'UpcomingLessons',
  components: {
    LessonsPage: LessonsPage["default"]
  },
  middleware: ['authenticated', 'thankYouPageAllowed'],

  async asyncData({
    store,
    query
  }) {
    const type = 'upcoming';
    const searchQuery = query === null || query === void 0 ? void 0 : query.search;
    await Promise.all([store.dispatch('lesson/getUpcomingLessons', {
      page: 1,
      perPage: "16",
      type,
      searchQuery
    }), store.dispatch('lesson/getCalendarItems')]);
    return {
      type
    };
  },

  head() {
    return {
      title: this.$t('user_upcoming_lessons_page.seo_title'),
      meta: [{
        hid: 'description',
        name: 'description',
        content: this.$t('user_upcoming_lessons_page.seo_description')
      }, {
        hid: 'og:title',
        name: 'og:title',
        property: 'og:title',
        content: this.$t('user_upcoming_lessons_page.seo_title')
      }, {
        property: 'og:description',
        content: this.$t('user_upcoming_lessons_page.seo_description')
      }],
      bodyAttrs: {
        class: `${this.locale} user-lessons-page user-upcoming-lessons-page`
      }
    };
  },

  watchQuery: true,

  async beforeMount() {
    this.$cookiz.remove('thank_you_page_allowed', {
      domain: ".langu.loc",
      path: '/'
    }); // Ensure dataLayer is properly initialized

    window.dataLayer = window.dataLayer || []; // Get event data from localStorage

    if (localStorage.getItem('event_data') !== null) {
      try {
        const eventData = JSON.parse(localStorage.getItem('event_data')); // Try to get user data from the API for the most up-to-date information

        let userData = null;

        try {
          userData = await this.$store.dispatch('payments/fetchUserData');
        } catch (error) {
          console.error('Error fetching user data from API:', error);
        } // If API call fails, fall back to store state


        if (!userData || !userData.email) {
          userData = this.$store.state.user.item || {};
        }

        const tidioData = this.$store.state.user.tidioData || {};
        const userEmail = tidioData.email || '';
        const userName = `${userData.firstName || ''} ${userData.lastName || ''}`.trim();
        const hashedEmail = Object(hash["hashUserData"])(userEmail);
        const hashedName = Object(hash["hashUserData"])(userName); // Add hashed user data to the items array

        if (eventData.ecommerce && eventData.ecommerce.items && eventData.ecommerce.items.length > 0) {
          eventData.ecommerce.items.forEach(item => {
            if (!item.user_name) {
              item.user_name = hashedName;
            }

            if (!item.email_id) {
              item.email_id = hashedEmail;
            }
          });
        } // Push event to dataLayer with a slight delay to ensure GTM is ready


        setTimeout(() => {
          // First push a clear ecommerce object to prevent data leakage
          window.dataLayer.push({
            ecommerce: null
          }); // Then push the actual event

          window.dataLayer.push(eventData);
        }, 500); // Remove event data from localStorage

        localStorage.removeItem('event_data');
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Error pushing event to dataLayer:', error);
      }
    }
  }

});
// CONCATENATED MODULE: ./pages/user/lessons/thank-you/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var lessons_thank_youvue_type_script_lang_js_ = (thank_youvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./pages/user/lessons/thank-you/index.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  lessons_thank_youvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "aea5dfd4"
  
)

/* harmony default export */ var thank_you = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 27:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _VBtn__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(852);


/* harmony default export */ __webpack_exports__["a"] = (_VBtn__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]);

/***/ }),

/***/ 915:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(938);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("a98bb618", content, true, context)
};

/***/ }),

/***/ 916:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/UserStatus.vue?vue&type=template&id=652352c7&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[
    'user-status',
    ("user-status--" + _vm.status),
    { 'user-status--large': _vm.large } ]},[])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/UserStatus.vue?vue&type=template&id=652352c7&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/UserStatus.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var UserStatusvue_type_script_lang_js_ = ({
  name: 'UserStatus',
  props: {
    userId: {
      type: Number,
      default: 0
    },
    large: {
      type: Boolean,
      default: false
    },
    userStatuses: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    status() {
      var _this$userId;

      let status = 'offline';

      if (Object.prototype.hasOwnProperty.call(this.userStatuses, (_this$userId = this.userId) === null || _this$userId === void 0 ? void 0 : _this$userId.toString())) {
        status = this.userStatuses[this.userId];
      }

      return status;
    }

  }
});
// CONCATENATED MODULE: ./components/UserStatus.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_UserStatusvue_type_script_lang_js_ = (UserStatusvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/UserStatus.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(958)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_UserStatusvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "652352c7",
  "4c070a35"
  
)

/* harmony default export */ var UserStatus = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 928:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(949);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("ef3a6480", content, true, context)
};

/***/ }),

/***/ 930:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pagination.vue?vue&type=template&id=18a8bda5&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('nav',{staticClass:"pagination"},[_vm._ssrNode("<ul class=\"pagination-list d-flex justify-center align-center\" data-v-18a8bda5>","</ul>",[_vm._ssrNode("<li"+(_vm._ssrClass(null,['pagination-item pagination-item-prev']))+" data-v-18a8bda5><div class=\"icon next-prev-icon\" data-v-18a8bda5><svg width=\"17\" height=\"12\" viewBox=\"0 0 17 12\" data-v-18a8bda5><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#arrow-prev")))+" data-v-18a8bda5></use></svg></div> <span class=\"d-none d-sm-inline ml-2\" data-v-18a8bda5>"+_vm._ssrEscape(_vm._s(_vm.$t('previous')))+"</span></li> "),_vm._l((_vm.pages),function(page,index){return _vm._ssrNode("<li class=\"pagination-item\" data-v-18a8bda5>","</li>",[(page !== 0)?[_c('nuxt-link',{class:{ current: _vm.currentPage === page },attrs:{"to":_vm.getUrl(page)}},[_vm._v("\n          "+_vm._s(page)+"\n        ")])]:_vm._ssrNode("<span class=\"dots\" data-v-18a8bda5>...</span>")],2)}),_vm._ssrNode(" <li"+(_vm._ssrClass(null,['pagination-item pagination-item-next']))+" data-v-18a8bda5><span class=\"d-none d-sm-inline mr-2\" data-v-18a8bda5>"+_vm._ssrEscape(_vm._s(_vm.$t('next')))+"</span> <div class=\"icon\" data-v-18a8bda5><svg width=\"17\" height=\"12\" viewBox=\"0 0 17 12\" data-v-18a8bda5><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#arrow-next")))+" data-v-18a8bda5></use></svg></div></li>")],2)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/Pagination.vue?vue&type=template&id=18a8bda5&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pagination.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var Paginationvue_type_script_lang_js_ = ({
  name: 'Pagination',
  props: {
    currentPage: {
      type: Number,
      required: true
    },
    totalPages: {
      type: Number,
      required: true
    },
    route: {
      type: String,
      required: true
    },
    params: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      key: 1
    };
  },

  computed: {
    isFirstCurrentPage() {
      return this.currentPage <= 1;
    },

    isLastCurrentPage() {
      return this.currentPage >= this.totalPages;
    },

    pages() {
      const pages = [];

      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i);
      }

      let pagination = pages.slice();

      if (this.totalPages > 6) {
        let left = [];
        let right = [];
        let center = [];

        if (this.currentPage < 3 || this.currentPage > this.totalPages - 3) {
          left = pages.slice(0, 3);
          right = pages.slice(-3);
          pagination = [...left, 0, ...right];
        }

        if (this.currentPage === 3) {
          left = pages.slice(0, 5);
          right = pages.slice(-1);
          pagination = [...left, 0, ...right];
        }

        if (this.currentPage > 3 && this.currentPage < this.totalPages - 2) {
          left = pages.slice(0, 1);
          right = pages.slice(-1);
          center = pages.slice(this.currentPage - 2, this.currentPage + 1);
          pagination = [...left, 0, ...center, 0, ...right];
        }

        if (this.currentPage === this.totalPages - 2) {
          left = pages.slice(0, 1);
          right = pages.slice(-5);
          pagination = [...left, 0, ...right];
        }
      }

      return pagination;
    },

    queryStr() {
      const {
        query
      } = this.$route;
      const keys = Object.keys(query);
      let str = '';

      if (keys.length) {
        str += '?';

        for (let i = 0; i < keys.length; i++) {
          str += `${keys[i]}=${query[keys[i]]}`;

          if (i < keys.length - 1) {
            str += '&';
          }
        }
      }

      return str;
    }

  },
  watch: {
    currentPage() {
      this.key++;
    }

  },
  methods: {
    getUrl(page) {
      let url = this.route;

      if (page > 1 || this.params.length) {
        url += `/${page}${this.params.length ? '/' + this.params : ''}`;
      }

      if (this.queryStr.length) {
        url += this.queryStr;
      }

      return url;
    },

    prevPageClickHandler() {
      if (!this.isFirstCurrentPage) {
        this.$router.push({
          path: this.getUrl(this.currentPage - 1)
        });
      }
    },

    nextPageClickHandler() {
      if (!this.isLastCurrentPage) {
        this.$router.push({
          path: this.getUrl(this.currentPage + 1)
        });
      }
    }

  }
});
// CONCATENATED MODULE: ./components/Pagination.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_Paginationvue_type_script_lang_js_ = (Paginationvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/Pagination.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(948)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_Paginationvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "18a8bda5",
  "18cd97b2"
  
)

/* harmony default export */ var Pagination = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 931:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(954);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("637a1dfc", content, true, context)
};

/***/ }),

/***/ 932:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/Avatars.vue?vue&type=script&lang=js&
/* harmony default export */ var Avatarsvue_type_script_lang_js_ = ({
  methods: {
    getSrcAvatar(images, property, defaultImage = 'avatar.png') {
      return images !== null && images !== void 0 && images[property] ? images[property] : __webpack_require__(511)(`./${defaultImage}`);
    },

    getSrcSetAvatar(images, property1, property2) {
      return images !== null && images !== void 0 && images[property1] && images !== null && images !== void 0 && images[property2] ? `
            ${images[property1]} 1x,
            ${images[property2]} 2x,
          ` : '';
    }

  }
});
// CONCATENATED MODULE: ./mixins/Avatars.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_Avatarsvue_type_script_lang_js_ = (Avatarsvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/Avatars.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_Avatarsvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "0af9ff4e"
  
)

/* harmony default export */ var Avatars = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 933:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(959);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("006007e9", content, true, context)
};

/***/ }),

/***/ 937:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(915);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 938:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".text-editor{position:relative}.text-editor-buttons{position:absolute;right:18px;top:8px;z-index:2}.text-editor-buttons button{display:inline-flex;justify-content:center;align-items:center;width:24px;height:24px;margin-left:8px;border-radius:2px;border:1px solid transparent}.text-editor-buttons button.is-active{background-color:var(--v-greyBg-base);border-color:var(--v-greyLight-base)}.text-editor .ProseMirror{min-height:280px;margin-bottom:4px;padding:40px 12px 12px;border:1px solid #bebebe;font-size:13px;border-radius:16px;line-height:1.23}.text-editor .ProseMirror>*{position:relative}.text-editor .ProseMirror p{margin-bottom:0}.text-editor .ProseMirror ul{padding-left:28px}.text-editor .ProseMirror ul>li p{margin-bottom:0}.text-editor .ProseMirror strong{font-weight:700!important}.text-editor .ProseMirror.focus-visible,.text-editor .ProseMirror:focus,.text-editor .ProseMirror:focus-visible{outline:none!important}.text-editor .ProseMirror-focused:before{content:\"\";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:16px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}.text-editor .v-text-field__details{padding:0 14px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 939:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(979);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("0f94d031", content, true, context)
};

/***/ }),

/***/ 940:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(981);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("13082346", content, true, context)
};

/***/ }),

/***/ 942:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/form/Editor.vue?vue&type=template&id=23b137ee&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"text-editor"},[_vm._ssrNode(((_vm.editor)?("<div class=\"text-editor-buttons\"><button"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bold') }))+"><svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#editor-bold-icon")))+"></use></svg></button> <button"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bulletList') }))+"><svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#editor-list-icon")))+"></use></svg></button></div>"):"<!---->")+" "),_c('editor-content',{attrs:{"editor":_vm.editor}}),_vm._ssrNode(" "+((_vm.counter)?("<div class=\"v-text-field__details\"><div class=\"v-messages theme--light\"><div class=\"v-messages__wrapper\"></div></div> <div"+(_vm._ssrClass(null,[
        'v-counter theme--light',
        { 'error--text': !_vm.isValid && _vm.isDirty } ]))+">"+_vm._ssrEscape("\n      "+_vm._s(_vm.text.length)+" / "+_vm._s(_vm.limit)+"\n    ")+"</div></div>"):"<!---->"))],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/form/Editor.vue?vue&type=template&id=23b137ee&

// EXTERNAL MODULE: external "@tiptap/vue-2"
var vue_2_ = __webpack_require__(854);

// EXTERNAL MODULE: external "@tiptap/starter-kit"
var starter_kit_ = __webpack_require__(855);
var starter_kit_default = /*#__PURE__*/__webpack_require__.n(starter_kit_);

// EXTERNAL MODULE: external "@tiptap/extension-character-count"
var extension_character_count_ = __webpack_require__(856);
var extension_character_count_default = /*#__PURE__*/__webpack_require__.n(extension_character_count_);

// EXTERNAL MODULE: external "@tiptap/extension-link"
var extension_link_ = __webpack_require__(857);
var extension_link_default = /*#__PURE__*/__webpack_require__.n(extension_link_);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/form/Editor.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var Editorvue_type_script_lang_js_ = ({
  name: 'Editor',
  components: {
    EditorContent: vue_2_["EditorContent"]
  },
  props: {
    value: {
      type: String,
      required: true
    },
    counter: {
      type: Boolean,
      default: false
    },
    autoLink: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: null
    }
  },

  data() {
    return {
      editor: null,
      text: '',
      isValid: true,
      editorEl: null,
      keysPressed: {},
      isDirty: false
    };
  },

  watch: {
    value(value) {
      const isSame = this.editor.getHTML() === value;

      if (isSame) {
        return;
      }

      this.editor.commands.setContent(value, false);
    }

  },

  mounted() {
    this.editor = new vue_2_["Editor"]({
      content: this.value,
      extensions: [starter_kit_default.a, extension_character_count_default.a.configure({
        limit: this.limit
      }), extension_link_default.a.configure({
        autolink: true
      })]
    });
    this.editor.on('create', ({
      editor
    }) => {
      this.text = editor.getText();
      this.$nextTick(() => {
        this.editorEl = document.getElementsByClassName('ProseMirror')[0];

        if (this.editorEl) {
          this.editorEl.addEventListener('keydown', this.keydownHandler);
          this.editorEl.addEventListener('keyup', this.keyupHandler);
        }
      });
      this.validation();
    });
    this.editor.on('update', ({
      editor
    }) => {
      this.isDirty = true;
      this.text = editor.getText();
      this.validation();
      this.$emit('update', this.text ? editor.getHTML() : '');
    });
  },

  beforeDestroy() {
    if (this.editorEl) {
      this.editorEl.removeEventListener('keydown', this.keydownHandler);
      this.editorEl.removeEventListener('keyup', this.keyupHandler);
    }

    this.editor.destroy();
  },

  methods: {
    keydownHandler(e) {
      this.keysPressed[e.keyCode] = true;

      if ((e.ctrlKey || this.keysPressed[17] || this.keysPressed[91] || this.keysPressed[93] || this.keysPressed[224]) && this.keysPressed[13]) {
        e.preventDefault();
        this.$emit('submit');
        this.keysPressed = {};
      } else if (e.keyCode === 13 && !e.shiftKey) {
        e.preventDefault();
        this.editor.commands.enter();
      }
    },

    keyupHandler(e) {
      delete this.keysPressed[e.keyCode];
    },

    validation() {
      const strLength = this.text.trim().length;
      this.isValid = !!strLength;

      if (!!strLength && this.limit) {
        this.isValid = strLength <= this.limit;
      }

      this.$emit('validation', this.isValid);
    }

  }
});
// CONCATENATED MODULE: ./components/form/Editor.vue?vue&type=script&lang=js&
 /* harmony default export */ var form_Editorvue_type_script_lang_js_ = (Editorvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/form/Editor.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(937)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  form_Editorvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "0bb70d5d"
  
)

/* harmony default export */ var Editor = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 945:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/form/SearchInput.vue?vue&type=template&id=8bfec74e&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-form',{on:{"submit":function($event){$event.preventDefault();return _vm.submit.apply(null, arguments)}}},[_c('text-input',{class:['search-input', { 'search-input--small': _vm.small }],attrs:{"value":_vm.value,"type-class":"border-gradient","hide-details":"","disabled":_vm.disabled,"placeholder":_vm.$t(_vm.placeholder)},on:{"input":function($event){return _vm.$emit('input', $event)}},scopedSlots:_vm._u([{key:"append",fn:function(){return [_c('div',{staticStyle:{"margin-top":"6px","cursor":"pointer"},on:{"click":_vm.submit}},[_c('v-img',{attrs:{"src":__webpack_require__(506)}})],1)]},proxy:true}])})],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/form/SearchInput.vue?vue&type=template&id=8bfec74e&

// EXTERNAL MODULE: ./components/form/TextInput.vue + 4 modules
var TextInput = __webpack_require__(102);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/form/SearchInput.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var SearchInputvue_type_script_lang_js_ = ({
  name: 'SearchInput',
  components: {
    TextInput: TextInput["default"]
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      required: true
    },
    small: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    submit() {
      this.$emit('submit');
    }

  }
});
// CONCATENATED MODULE: ./components/form/SearchInput.vue?vue&type=script&lang=js&
 /* harmony default export */ var form_SearchInputvue_type_script_lang_js_ = (SearchInputvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(896);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/form/SearchInput.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(953)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  form_SearchInputvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "86c5c87c"
  
)

/* harmony default export */ var SearchInput = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */



installComponents_default()(component, {VForm: VForm["a" /* default */],VImg: VImg["a" /* default */]})


/***/ }),

/***/ 946:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/LessonItem.vue?vue&type=template&id=1f692612&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"lesson d-sm-flex"},[_vm._ssrNode("<div class=\"lesson-date d-flex text-center\">","</div>",[_vm._ssrNode("<div>","</div>",[(_vm.$slots.date)?[_vm._t("date")]:_vm._ssrNode(((_vm.$vuetify.breakpoint.smAndUp)?("<div class=\"weekday d-none d-sm-block\">"+_vm._ssrEscape("\n            "+_vm._s(_vm.$dayjs(_vm.startDate).format('dddd'))+"\n          ")+"</div> <div class=\"date d-none d-sm-block\">"+_vm._ssrEscape("\n            "+_vm._s(_vm.$dayjs(_vm.startDate).format('DD MMM'))+"\n          ")+"</div> <div class=\"time d-none d-sm-block\">"+_vm._ssrEscape(_vm._s(_vm.startTime))+"</div>"):("<div class=\"d-sm-none\">"+_vm._ssrEscape("\n            "+_vm._s(_vm.$dayjs(_vm.startDate).format('dddd'))+",\n            "+_vm._s(_vm.$dayjs(_vm.startDate).format('DD MMM'))+" - "+_vm._s(_vm.startTime)+"\n          ")+"</div>")))],2),_vm._ssrNode(" "+((_vm.$vuetify.breakpoint.smAndUp)?("<div class=\"duration d-none d-sm-block\"><div class=\"duration-icon\"><svg width=\"18\" height=\"18\" viewBox=\"0 0 18 18\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#clock-thin")))+"></use></svg></div>"+_vm._ssrEscape("\n      "+_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.item.lessonLength }))+"\n    ")+"</div>"):("<div class=\"duration d-sm-none\">"+_vm._ssrEscape("\n       ("+_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.item.lessonLength }))+")\n    ")+"</div>")))],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"lesson-content\">","</div>",[_vm._ssrNode("<div class=\"lesson-details\">","</div>",[_vm._ssrNode("<div class=\"avatar mr-2\">","</div>",[_c('v-avatar',{attrs:{"width":"110","height":"110"}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(_vm.item.userAvatars, 'user_thumb_110x110'),"srcset":_vm.getSrcSetAvatar(
                _vm.item.userAvatars,
                'user_thumb_110x110',
                'user_thumb_220x220'
              ),"options":{ rootMargin: '50%' }}}),_vm._v(" "),(_vm.teacherLink)?_c('nuxt-link',{attrs:{"to":_vm.teacherLink}}):_vm._e()],1),_vm._ssrNode(" "),_c('user-status',{attrs:{"user-id":_vm.userId,"user-statuses":_vm.userStatuses,"large":""}})],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"details\">","</div>",[_vm._ssrNode("<div class=\"user-info\"><div class=\"user-info-name\">"+_vm._ssrEscape("\n            "+_vm._s(_vm.userName)+"\n            ")+((_vm.isTeacher)?("<div><svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#info")))+"></use></svg></div>"):"<!---->")+"</div> <div"+(_vm._ssrClass(null,[
              'user-info-status',
              ("user-info-status--" + _vm.status),
              { 'text--red-gradient': _vm.status === 'idle' } ]))+">"+((_vm.status === 'online')?(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('online_now'))+"\n            ")):(_vm.status === 'idle')?(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('online_but_idle'))+"\n            ")):(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('offline_now'))+"\n            ")))+"</div></div> "),_vm._ssrNode("<div class=\"lesson-info\">","</div>",[_vm._ssrNode("<div class=\"avatar mr-2\">","</div>",[_c('v-avatar',{attrs:{"width":"85","height":"85"}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(_vm.item.userAvatars, 'user_thumb_110x110'),"srcset":_vm.getSrcSetAvatar(
                    _vm.item.userAvatars,
                    'user_thumb_110x110',
                    'user_thumb_220x220'
                  ),"options":{ rootMargin: '50%' }}}),_vm._v(" "),(_vm.teacherLink)?_c('nuxt-link',{attrs:{"to":_vm.teacherLink}}):_vm._e()],1),_vm._ssrNode(" "),_c('user-status',{attrs:{"user-id":_vm.userId,"user-statuses":_vm.userStatuses,"large":""}})],2),_vm._ssrNode(" "),_vm._ssrNode("<div>","</div>",[_vm._t("lessonInfo"),_vm._ssrNode(" "+((!_vm.isUnscheduled)?("<div><span class=\"text-capitalize\">"+_vm._ssrEscape(_vm._s(_vm.$t('lesson'))+":")+"</span> <span class=\"text--gradient font-weight-bold text-no-wrap\">"+_vm._ssrEscape("\n                "+_vm._s(_vm.item.lessonType)+"\n              ")+"</span></div>"):"<!---->")+" <div><span class=\"text-capitalize\">"+_vm._ssrEscape(_vm._s(_vm.$t('language'))+":")+"</span> <span class=\"text--gradient font-weight-bold text-no-wrap\">"+_vm._ssrEscape("\n                "+_vm._s(_vm.item.language.name)+"\n              ")+"</span></div> "+((_vm.item.courseName && _vm.item.courseName.length)?("<div><span class=\"text-capitalize\">"+_vm._ssrEscape(_vm._s(_vm.$t('course'))+":")+"</span> <span class=\"text--gradient font-weight-bold\">"+_vm._ssrEscape("\n                "+_vm._s(_vm.item.courseName)+"\n              ")+"</span></div>"):"<!---->")+" "),_vm._ssrNode("<div class=\"lesson-actions-additional\">","</div>",[_vm._t("lessonAdditionalActionsTop"),_vm._ssrNode(" "),(!_vm.isUnscheduleButtonHidden)?[_vm._ssrNode("<div>","</div>",[_vm._ssrNode("<span class=\"action\">","</span>",[_c('v-img',{staticStyle:{"left":"3px"},attrs:{"src":__webpack_require__(507),"width":"11","height":"11"}}),_vm._ssrNode(_vm._ssrEscape("\n                    "+_vm._s(_vm.$t('unschedule_lesson'))+"\n                  "))],2)])]:_vm._e(),_vm._ssrNode(" "),_vm._t("lessonAdditionalActionsBottom",null,{"showDialog":_vm.showDialog})],2)],2)],2)],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"lesson-actions\">","</div>",[_vm._ssrNode("<div class=\"lesson-actions-buttons\">","</div>",[(!_vm.item.userIsDeleted)?_c('v-btn',{staticClass:"btn-add gradient font-weight-medium ml-1 mb-1",attrs:{"width":"158"},on:{"click":_vm.showMessageDialog}},[_c('div',{staticClass:"mr-1"},[_c('v-img',{attrs:{"src":__webpack_require__(514),"width":"20","height":"20"}})],1),_vm._v(" "),_c('div',{staticClass:"text--gradient"},[_vm._v("\n            "+_vm._s(_vm.$t('message'))+"\n          ")])]):_vm._e(),_vm._ssrNode(" "),_vm._t("lessonActions"),_vm._ssrNode(" "),(!_vm.isUnscheduled)?[(_vm.isTeacher || (_vm.isStudent && !_vm.item.isCreated))?[_c('v-btn',{staticClass:"go-to-class-btn font-weight-medium ml-1 mb-1",attrs:{"href":("/lesson/" + (_vm.item.lessonId) + "/classroom"),"color":"primary","width":"158"}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"18","height":"18","viewBox":"0 0 18 18"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#save-icon")}})]),_vm._v("\n              "+_vm._s(_vm.$t('go_to_class'))+"\n            ")])]:[_c('v-btn',{staticClass:"go-to-class-btn go-to-class-btn--disabled primary--light font-weight-medium ml-1 mb-1",attrs:{"color":"primary","width":"158"},on:{"click":_vm.showInitializeDialog}},[_c('svg',{staticClass:"icon--rotated mr-1",attrs:{"width":"18","height":"18","viewBox":"0 0 18 18"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#save-icon")}})]),_vm._v("\n              "+_vm._s(_vm.$t('go_to_class'))+"\n              "),_c('svg',{staticClass:"icon--right",attrs:{"width":"18","height":"18","viewBox":"0 0 16 16"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#info")}})])])]]:_vm._e()],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"lesson-actions-additional\">","</div>",[_vm._t("lessonAdditionalActionsTop"),_vm._ssrNode(" "),(!_vm.isUnscheduleButtonHidden)?[_vm._ssrNode("<div>","</div>",[_vm._ssrNode("<span class=\"action\">","</span>",[_c('v-img',{staticStyle:{"left":"3px"},attrs:{"src":__webpack_require__(507),"width":"11","height":"11"}}),_vm._ssrNode(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('unschedule_lesson'))+"\n            "))],2)])]:_vm._e(),_vm._ssrNode(" "),_vm._t("lessonAdditionalActionsBottom",null,{"showDialog":_vm.showDialog})],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div"+(_vm._ssrClass(null,[
        'lesson-dialog d-flex flex-column justify-center',
        {
          'lesson-dialog--shown': _vm.isLessonDialogShown,
        },
        {
          'lesson-dialog--student-info': _vm.dialogType === 'studentInfoDialog',
        } ]))+">","</div>",[_vm._t("dialog",null,{"closeDialog":_vm.closeDialog}),_vm._ssrNode(" "),(_vm.dialogType === 'unscheduledDialog')?[_vm._ssrNode("<div class=\"lesson-dialog-title font-weight-medium text--red-gradient\">"+_vm._ssrEscape("\n          "+_vm._s(_vm.$t('are_you_sure_you_want_to_unschedule_this_lesson_with', {
              name: _vm.item.userFirstName,
            }))+"\n        ")+"</div> <div class=\"lesson-dialog-content l-scroll l-scroll--grey\">"+((_vm.isStudent)?(((_vm.item.isFreeTrial)?(_vm._ssrEscape("\n              "+_vm._s(_vm.$t(
                  'you_will_be_able_to_reschedule_this_lesson_from_your_teachers_profile_page'
                ))+"\n            ")):(_vm._ssrEscape("\n              "+_vm._s(_vm.$t(
                  'you_will_receive_credit_and_can_reschedule_lesson_for_anytime_your_teacher_is_available'
                ))+"\n            ")))):(_vm._ssrEscape("\n            "+_vm._s(_vm.$t('student_will_be_given_credit_for_lesson'))+"\n          ")))+"</div> "),_vm._ssrNode("<div class=\"lesson-dialog-buttons\">","</div>",[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"greyDark","outlined":""},on:{"click":_vm.closeDialog}},[_vm._v("\n            "+_vm._s(_vm.$t('do_not_cancel_lesson'))+"\n          ")]),_vm._ssrNode(" "),_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"error"},on:{"click":_vm.cancelClickHandler}},[_vm._v("\n            "+_vm._s(_vm.$t('cancel_lesson'))+"\n          ")])],2)]:_vm._e(),_vm._ssrNode(" "),(_vm.dialogType === 'initializeDialog')?[_vm._ssrNode("<div class=\"lesson-dialog-title font-weight-medium text--gradient\"><div class=\"lesson-dialog-title-icon\"><svg width=\"20\" height=\"20\" viewBox=\"0 0 12 12\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#attention")))+"></use></svg></div>"+_vm._ssrEscape("\n          "+_vm._s(_vm.$t('your_teacher_will_enter_classroom_first'))+"\n        ")+"</div> <div class=\"lesson-dialog-content l-scroll l-scroll--grey\">"+(_vm._s(
            _vm.$t(
              'after_your_teacher_enters_go_to_class_button_will_become_clickable_so_you_can_enter_as_well'
            )
          ))+"</div> "),_vm._ssrNode("<div class=\"lesson-dialog-buttons\">","</div>",[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"primary","max-width":"158"},on:{"click":_vm.closeDialog}},[_vm._v("\n            OK!\n          ")])],1)]:_vm._e(),_vm._ssrNode(" "),(_vm.dialogType === 'studentInfoDialog')?[_vm._ssrNode("<div class=\"lesson-dialog-title font-weight-medium text--gradient\"><div class=\"lesson-dialog-title-icon\"><svg width=\"20\" height=\"20\" viewBox=\"0 0 16 16\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#info")))+"></use></svg></div>"+_vm._ssrEscape("\n          "+_vm._s(_vm.$t('student_info'))+"\n        ")+"</div> <div class=\"lesson-dialog-content l-scroll l-scroll--grey\"><div><div><ul><li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('name'))+":")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.studentInfo.name)+"\n                ")+"</li> <li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('lifetime_free_trials_scheduled'))+":")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.studentInfo.freeTrialScheduled)+"\n                ")+"</li> <li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('lifetime_lessons_purchased'))+":")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.studentInfo.lessonsPurchased)+"\n                ")+"</li> <li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('lifetime_teachers_booked_with'))+":")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.studentInfo.teachersBookedWith)+"\n                ")+"</li></ul></div> <div class=\"pl-1\"><ul><li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('current_time'))+":")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.$dayjs().tz(_vm.studentInfo.timezone).format('LT'))+"\n                  ("+_vm._s(_vm.$dayjs().tz(_vm.studentInfo.timezone).format('z'))+",\n                  "+_vm._s(_vm.studentInfo.timezone)+")\n                ")+"</li> <li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('total_spent_with_you'))+" ("+_vm._s(_vm.currencyIsoCode)+"):")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.currencySymbol)+_vm._s(_vm.getPrice(_vm.studentInfo.totalSpendWithTeacher))+"\n                ")+"</li> <li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('date_registered_on_langu'))+":")+"</span>"+_vm._ssrEscape("\n                  "+_vm._s(_vm.$dayjs(_vm.studentInfo.dateRegistered)
                      .tz(_vm.timeZone)
                      .format('ll, LT'))+"\n                ")+"</li> <li><span class=\"font-weight-medium\">"+_vm._ssrEscape(_vm._s(_vm.$t('last_online'))+":")+"</span> "+((_vm.status === 'online')?(_vm._ssrEscape("\n                    "+_vm._s(_vm.$t('online_now'))+"\n                  ")):(_vm.status === 'idle')?(_vm._ssrEscape("\n                    "+_vm._s(_vm.$t('online_but_idle'))+"\n                  ")):(_vm._ssrEscape("\n                    "+_vm._s(_vm.$dayjs(_vm.studentInfo.lastLoginDate)
                        .tz(_vm.timeZone)
                        .format('ll, LT'))+"\n                  ")))+"</li></ul></div></div></div> "),_vm._ssrNode("<div class=\"lesson-dialog-buttons\">","</div>",[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"primary","max-width":"158"},on:{"click":_vm.closeDialog}},[_vm._v("\n            OK\n          ")])],1)]:_vm._e()],2)],2),_vm._ssrNode(" "),(_vm.isShownMessageDialog)?_c('message-dialog',{attrs:{"recipient-id":_vm.userId,"recipient-name":((_vm.item.userFirstName) + " " + (_vm.item.userLastName)),"is-shown-message-dialog":_vm.isShownMessageDialog},on:{"close-dialog":function($event){_vm.isShownMessageDialog = false}}}):_vm._e(),_vm._ssrNode(" "),_vm._t("default")],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-lessons/LessonItem.vue?vue&type=template&id=1f692612&

// EXTERNAL MODULE: ./helpers/index.js
var helpers = __webpack_require__(501);

// EXTERNAL MODULE: ./mixins/Avatars.vue + 2 modules
var Avatars = __webpack_require__(932);

// EXTERNAL MODULE: ./components/UserStatus.vue + 4 modules
var UserStatus = __webpack_require__(916);

// EXTERNAL MODULE: ./components/MessageDialog.vue + 4 modules
var MessageDialog = __webpack_require__(952);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/LessonItem.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var LessonItemvue_type_script_lang_js_ = ({
  name: 'LessonItem',
  components: {
    UserStatus: UserStatus["default"],
    MessageDialog: MessageDialog["default"]
  },
  mixins: [Avatars["a" /* default */]],
  props: {
    item: {
      type: Object,
      required: true
    },
    userStatuses: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      getPrice: helpers["getPrice"],
      isLessonDialogShown: false,
      dialogType: null,
      isShownMessageDialog: false,
      studentInfo: null
    };
  },

  computed: {
    isTeacher() {
      return this.$store.getters['user/isTeacher'];
    },

    isStudent() {
      return this.$store.getters['user/isStudent'];
    },

    userId() {
      return this.item[this.isTeacher ? 'studentId' : 'teacherId'];
    },

    userIsDeleted() {
      return this.item.userIsDeleted;
    },

    userName() {
      return this.userIsDeleted ? this.$t('deleted_user') : `${this.item.userFirstName} ${this.item.userLastName}`;
    },

    currencyIsoCode() {
      return this.$store.state.currency.item.isoCode;
    },

    currencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    },

    status() {
      var _this$userId;

      let status = 'offline';

      if (Object.prototype.hasOwnProperty.call(this.userStatuses, (_this$userId = this.userId) === null || _this$userId === void 0 ? void 0 : _this$userId.toString())) {
        status = this.userStatuses[this.userId];
      }

      return status;
    },

    isPast() {
      return this.item.type === 'past';
    },

    isUnscheduled() {
      return this.item.type === 'unscheduled';
    },

    timeZone() {
      return this.$store.getters['user/timeZone'];
    },

    startDate() {
      return this.$dayjs(this.item.startDate).tz(this.timeZone);
    },

    startTime() {
      return this.startDate.format('LT');
    },

    isUnscheduleButtonHidden() {
      return this.isUnscheduled || this.item.isFinished || this.isStudent && (this.isPast || this.$dayjs().add(1, 'day').isAfter(this.startDate, 'minute'));
    },

    teacherLink() {
      return this.isStudent && !this.item.userIsDeleted && this.item.teacherUsername ? `/teacher/${this.item.teacherUsername}` : null;
    }

  },
  methods: {
    showMessageDialog() {
      this.$store.dispatch('message/checkConversation', this.userId).then(res => {
        if (res.threadId) {
          this.$store.dispatch('loadingStart');
          this.$router.push({
            path: `/user/messages/${res.threadId}/view`
          });
        } else {
          this.isShownMessageDialog = true;
        }
      });
    },

    cancelClickHandler() {
      this.$store.dispatch('loadingStart');
      this.$store.dispatch('lesson/cancelLesson', this.item.lessonId).then(() => {
        location.reload();
      }).catch(e => {
        this.$store.dispatch('loadingStop');
        this.$store.dispatch('snackbar/error');
        console.info(e);
      });
    },

    studentInfoClickHandler() {
      this.$store.dispatch('lesson/getStudentInfo', this.item.studentId).then(res => {
        this.studentInfo = res;
        this.dialogType = 'studentInfoDialog';
        this.showDialog();
      });
    },

    showInitializeDialog() {
      this.dialogType = 'initializeDialog';
      this.showDialog();
    },

    showUnscheduledDialog() {
      this.dialogType = 'unscheduledDialog';
      this.showDialog();
    },

    showDialog() {
      this.isLessonDialogShown = true;
    },

    closeDialog() {
      this.isLessonDialogShown = false;
      setTimeout(() => {
        this.dialogType = null;
      }, 500);
    }

  }
});
// CONCATENATED MODULE: ./components/user-lessons/LessonItem.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_lessons_LessonItemvue_type_script_lang_js_ = (LessonItemvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/VAvatar.js
var VAvatar = __webpack_require__(830);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-lessons/LessonItem.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(997)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_lessons_LessonItemvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "7dfe9174"
  
)

/* harmony default export */ var LessonItem = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserStatus: __webpack_require__(916).default,MessageDialog: __webpack_require__(952).default})


/* vuetify-loader */




installComponents_default()(component, {VAvatar: VAvatar["a" /* default */],VBtn: VBtn["a" /* default */],VImg: VImg["a" /* default */]})


/***/ }),

/***/ 947:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/TimePickerItem.vue?vue&type=template&id=7467ec82&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[
    'time-picker-item',
    { active: _vm.isActive },
    { selected: _vm.item.isSelected },
    { free: _vm.item.isAvailable },
    { unavailable: _vm.item.isUnavailable } ],attrs:{"id":_vm.elId},on:{"mouseover":_vm.mouseoverHandler,"mouseleave":_vm.mouseleaveHandler,"click":function($event){$event.stopPropagation();return _vm.clickHandler.apply(null, arguments)}}},[])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/TimePickerItem.vue?vue&type=template&id=7467ec82&scoped=true&

// EXTERNAL MODULE: ./helpers/check_device.js
var check_device = __webpack_require__(145);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/TimePickerItem.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var TimePickerItemvue_type_script_lang_js_ = ({
  name: 'TimePickerItem',
  props: {
    idDefined: {
      type: Boolean,
      default: false
    },
    item: {
      type: Object,
      required: true
    },
    allowedToSelect: {
      type: Boolean,
      required: true
    },
    activeItems: {
      type: Array,
      required: true
    }
  },

  data() {
    return {
      isTouchDevice: Object(check_device["d" /* isTouchDevice */])()
    };
  },

  computed: {
    timezone() {
      return this.$store.getters['user/timeZone'];
    },

    elId() {
      return this.idDefined ? `h-${this.$dayjs(this.item.date).add(this.$dayjs(this.item.date).tz(this.timezone).utcOffset(), 'minute').format('HH-mm')}` : null;
    },

    isActive() {
      return this.item.isAvailable && this.activeItems.includes(this.item);
    }

  },
  methods: {
    clickHandler() {
      if (this.item.isAvailable) {
        this.$emit('click-item', this.item);
      }
    },

    mouseoverHandler() {
      if (!this.isTouchDevice && this.item.isAvailable && !this.item.isSelected && this.allowedToSelect) {
        this.$emit('mouseover-item', this.item);
      }
    },

    mouseleaveHandler() {
      if (this.item.isAvailable && !this.item.isSelected && this.allowedToSelect) {
        this.$emit('mouseleave-item');
      }
    }

  }
});
// CONCATENATED MODULE: ./components/TimePickerItem.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_TimePickerItemvue_type_script_lang_js_ = (TimePickerItemvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/TimePickerItem.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(980)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_TimePickerItemvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "7467ec82",
  "d1fa2cf4"
  
)

/* harmony default export */ var TimePickerItem = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 948:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Pagination_vue_vue_type_style_index_0_id_18a8bda5_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(928);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Pagination_vue_vue_type_style_index_0_id_18a8bda5_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Pagination_vue_vue_type_style_index_0_id_18a8bda5_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Pagination_vue_vue_type_style_index_0_id_18a8bda5_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Pagination_vue_vue_type_style_index_0_id_18a8bda5_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 949:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".pagination-list[data-v-18a8bda5]{padding-left:0;list-style-type:none}.pagination-item a[data-v-18a8bda5]{display:flex;justify-content:center;align-items:center;width:35px;height:35px;font-size:16px;font-weight:700;border-radius:4px;color:var(--v-darkLight-base);text-decoration:none;transition:color .3s;margin:0 10px}@media only screen and (max-width:639px){.pagination-item a[data-v-18a8bda5]{width:38px;height:38px}}@media only screen and (max-width:479px){.pagination-item a[data-v-18a8bda5]{width:36px;height:36px;font-size:14px;border-radius:2px}}.pagination-item a.current[data-v-18a8bda5]{background:var(--v-orange-base)}.pagination-item a[data-v-18a8bda5]:not(.current):hover{color:var(--v-orange-base)}.pagination-item-next[data-v-18a8bda5],.pagination-item-prev[data-v-18a8bda5]{display:flex;align-items:center;font-size:16px;font-weight:500;border-radius:50%;transition:color .3s}.pagination-item-next.disabled[data-v-18a8bda5],.pagination-item-prev.disabled[data-v-18a8bda5]{opacity:.6}.pagination-item-next[data-v-18a8bda5]:not(.disabled),.pagination-item-prev[data-v-18a8bda5]:not(.disabled){cursor:pointer}.pagination-item-next[data-v-18a8bda5]:not(.disabled):hover,.pagination-item-prev[data-v-18a8bda5]:not(.disabled):hover{color:var(--v-orange-base)}.pagination-item-prev[data-v-18a8bda5]{margin-right:15px}@media only screen and (max-width:639px){.pagination-item-prev[data-v-18a8bda5]{margin-right:10px}}@media only screen and (max-width:479px){.pagination-item-prev[data-v-18a8bda5]{margin-right:5px}}.pagination-item-prev .icon[data-v-18a8bda5]{margin-right:12px}.pagination-item-next[data-v-18a8bda5]{margin-left:15px}@media only screen and (max-width:639px){.pagination-item-next[data-v-18a8bda5]{margin-left:10px}}@media only screen and (max-width:479px){.pagination-item-next[data-v-18a8bda5]{margin-left:5px}}.pagination-item-next .icon[data-v-18a8bda5]{margin-left:12px}.pagination-item .dots[data-v-18a8bda5]{display:inline-block;width:64px;text-align:center}@media only screen and (max-width:639px){.pagination-item .dots[data-v-18a8bda5]{width:30px}}@media only screen and (max-width:479px){.pagination-item .dots[data-v-18a8bda5]{width:25px}}.pagination-item-prev[data-v-18a8bda5]{color:#000;fill:#000}.pagination-item-prev span[data-v-18a8bda5]{color:#000;fill:#000;font-weight:700}.pagination-item-next[data-v-18a8bda5]{color:#000;fill:#000}.pagination-item-next span[data-v-18a8bda5]{color:#000;fill:#000;font-weight:700}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 950:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(987);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("1fdd5634", content, true, context)
};

/***/ }),

/***/ 952:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/MessageDialog.vue?vue&type=template&id=01f70911&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{"dialog":_vm.isShownMessageDialog,"max-width":"725","custom-class":"message-dialog","persistent":"","fullscreen":_vm.$vuetify.breakpoint.xsOnly}},_vm.$listeners),[_c('v-form',{on:{"submit":function($event){$event.preventDefault();return _vm.submitHandler.apply(null, arguments)}}},[_c('div',{staticClass:"message-dialog-header text--gradient"},[_vm._v("\n      "+_vm._s(_vm.$t(
          _vm.$vuetify.breakpoint.xsOnly
            ? 'ask_question'
            : 'questions_ask_teacher_directly'
        ))+"\n    ")]),_vm._v(" "),_c('div',{class:[
        'message-dialog-body pt-0 pt-sm-3',
        {
          'l-scroll l-scroll--grey l-scroll--large':
            _vm.$vuetify.breakpoint.xsOnly,
        } ]},[_c('v-row',{attrs:{"no-gutters":""}},[_c('v-col',{staticClass:"col-12 col-sm-5 col-md-6"},[_c('div',{staticClass:"message-dialog-text pr-0 pr-sm-2"},[_c('div',{directives:[{name:"show",rawName:"v-show",value:(_vm.$vuetify.breakpoint.xsOnly),expression:"$vuetify.breakpoint.xsOnly"}]},[_c('span',{staticClass:"text-capitalize"},[_vm._v(_vm._s(_vm.$t('teacher')))]),_vm._v(": "+_vm._s(_vm.recipientName)),_c('br'),_vm._v(" "),_c('br')]),_vm._v(" "),_c('div',{domProps:{"innerHTML":_vm._s(_vm.$t('message_dialog_text', { recipientName: _vm.recipientName }))}})])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-7 col-md-6 mt-3 mt-sm-0"},[_c('editor',{attrs:{"value":_vm.newMessage,"limit":6000},on:{"update":function($event){_vm.newMessage = $event},"validation":function($event){_vm.isMessageValid = $event}}})],1)],1)],1),_vm._v(" "),_c('div',{staticClass:"message-dialog-footer d-flex justify-space-between align-center"},[_c('div',{staticClass:"prev-button body-1",on:{"click":function($event){return _vm.$emit('close-dialog')}}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"17","height":"12","viewBox":"0 0 17 12"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#arrow-prev")}})]),_vm._v(_vm._s(_vm.$t('go_back'))+"\n      ")]),_vm._v(" "),_c('div',[_c('v-btn',{attrs:{"small":"","color":"primary","type":"submit"}},[_vm._v("\n          "+_vm._s(_vm.$t('send_message'))+"\n        ")])],1)])])],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/MessageDialog.vue?vue&type=template&id=01f70911&

// EXTERNAL MODULE: ./components/LDialog.vue + 5 modules
var LDialog = __webpack_require__(28);

// EXTERNAL MODULE: ./components/form/Editor.vue + 4 modules
var Editor = __webpack_require__(942);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/MessageDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var MessageDialogvue_type_script_lang_js_ = ({
  name: 'MessageDialog',
  components: {
    LDialog: LDialog["default"],
    Editor: Editor["default"]
  },
  props: {
    recipientId: {
      type: Number,
      required: true
    },
    recipientName: {
      type: String,
      required: true
    },
    isShownMessageDialog: {
      type: Boolean,
      required: true
    }
  },

  data() {
    return {
      newMessage: '',
      isMessageValid: false
    };
  },

  beforeDestroy() {
    this.newMessage = '';
    this.isMessageValid = false;
  },

  methods: {
    submitHandler() {
      if (this.isMessageValid) {
        this.$store.dispatch('message/sendNewMessage', {
          recipientId: this.recipientId,
          message: this.newMessage
        }).then(res => {
          this.newMessage = '';
          this.$router.push({
            path: `/user/messages/${res.id}/view`
          });
        }).finally(() => this.$emit('close-dialog'));
      }
    }

  }
});
// CONCATENATED MODULE: ./components/MessageDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_MessageDialogvue_type_script_lang_js_ = (MessageDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(896);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/MessageDialog.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(978)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_MessageDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "06ad70c7"
  
)

/* harmony default export */ var MessageDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LDialog: __webpack_require__(28).default})


/* vuetify-loader */





installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VForm: VForm["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 953:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(931);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 954:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".search-input .v-input{background-color:#fff;border-radius:50px!important;box-shadow:0 4px 14px rgba(217,225,236,.47)}@media only screen and (max-width:767px){.search-input .v-input{border-radius:10px!important}}.search-input .v-input input::-moz-placeholder{color:var(--v-greyLight-darken2)}.search-input .v-input input:-ms-input-placeholder{color:var(--v-greyLight-darken2)}.search-input .v-input input::placeholder{color:var(--v-greyLight-darken2)}.search-input .v-input .v-input__control>.v-input__slot{height:56px!important}@media only screen and (max-width:767px){.search-input .v-input .v-input__control>.v-input__slot{height:40px!important}}.search-input .v-input .v-input__append-inner{margin-top:9px!important}@media only screen and (max-width:767px){.search-input .v-input .v-input__append-inner{margin-top:6px!important}}.search-input .v-input .v-input__append-inner .v-image{width:26px!important;height:26px!important}@media only screen and (max-width:767px){.search-input .v-input .v-input__append-inner .v-image{width:20px!important;height:20px!important}}.search-input .v-input.v-input--is-focused .v-input__control>.v-input__slot:before{border-radius:16px!important}.search-input .v-input.v-input.v-text-field--outlined fieldset{border-color:transparent!important;box-shadow:0 4px 14px rgba(217,225,236,.47)}.search-input--inner-border .v-input .v-input__control>.v-input__slot{padding-top:5px!important;padding-left:5px!important;padding-bottom:5px!important}.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot{position:relative;padding:0 16px;background-color:transparent!important}@media only screen and (max-width:1215px){.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot{padding:0 12px}}@media only screen and (max-width:767px){.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot{padding:0 10px}}.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot:before{display:none!important;content:\"\";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:15px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}@media only screen and (max-width:767px){.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot:before{border-radius:9px}}.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot input{position:relative;z-index:2}.search-input--inner-border .v-input .v-input__append-inner{margin-top:4px!important;padding-left:15px}@media only screen and (max-width:767px){.search-input--inner-border .v-input .v-input__append-inner{margin-top:0!important}}.search-input--inner-border .v-input.v-input--is-focused .v-input__control>.v-input__slot:before{display:none!important}.search-input--inner-border .v-input.v-input--is-focused .v-input__control>.v-input__slot>.v-text-field__slot:before{display:block!important}.search-input--small .v-input .v-input__control>.v-input__slot{height:44px!important}.search-input--small .v-input .v-input__append-inner{margin-top:6px!important}.search-input--small .v-input .v-input__append-inner .v-image{width:20px!important;height:20px!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 955:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/TimePicker.vue?vue&type=template&id=69022ce1&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"time-picker unselected"},[_vm._ssrNode("<div class=\"time-picker-toggle\" data-v-69022ce1>","</div>",[_vm._ssrNode("<div"+(_vm._ssrClass(null,['btn btn-prev', { 'btn--disabled': _vm.isPrevButtonDisabled }]))+" data-v-69022ce1>","</div>",[_c('v-icon',{attrs:{"color":"greyDark"}},[_vm._v(_vm._s(_vm.mdiChevronLeft))])],1),_vm._ssrNode(" <div class=\"period text-center\" data-v-69022ce1>"+_vm._ssrEscape("\n      "+_vm._s(_vm.firstDayOfWeek.format('D MMM'))+" -\n      "+_vm._s(_vm.lastDayOfWeek.format('D MMM'))+"\n    ")+"</div> "),_vm._ssrNode("<div class=\"btn btn-next\" data-v-69022ce1>","</div>",[_c('v-icon',{attrs:{"color":"greyDark"}},[_vm._v(_vm._s(_vm.mdiChevronRight))])],1)],2),_vm._ssrNode(" <div class=\"time-picker-top-bar\" data-v-69022ce1><div class=\"time-picker-top-bar-helper mx-auto\" data-v-69022ce1>"+(_vm._ssrList((7),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape("\n        "+_vm._s(_vm._f("dayFormat")(_vm.getDayOfWeek(i),'ddd,'))+"\n        ")+((_vm.$vuetify.breakpoint.xsOnly)?("<br class=\"d-sm-none\" data-v-69022ce1>"):"<!---->")+_vm._ssrEscape("\n        "+_vm._s(_vm._f("dayFormat")(_vm.getDayOfWeek(i),'MMM D'))+"\n      ")+"</div>")}))+"</div></div> "),_vm._ssrNode("<div class=\"time-picker-wrap l-scroll l-scroll--grey l-scroll--large\" data-v-69022ce1>","</div>",[_vm._ssrNode("<div class=\"time-picker-wrap-helper\" data-v-69022ce1>","</div>",[_vm._ssrNode("<div class=\"time-picker-left-bar\" data-v-69022ce1>"+((_vm.$i18n.locale === 'en')?("<div class=\"item\" data-v-69022ce1>12 AM</div> "+(_vm._ssrList((11),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape(_vm._s(i)+" AM")+"</div>")}))+" <div class=\"item\" data-v-69022ce1>12 PM</div> "+(_vm._ssrList((11),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape(_vm._s(i)+" PM")+"</div>")}))):((_vm._ssrList((24),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape(_vm._s(i - 1)+":00")+"</div>")}))))+"</div> "),_vm._ssrNode("<div class=\"time-picker-graph\" data-v-69022ce1>","</div>",_vm._l((_vm.calendar),function(day,idx){return _vm._ssrNode("<div class=\"day\" data-v-69022ce1>","</div>",[_c('client-only',_vm._l((day),function(item,index){return _c('time-picker-item',{key:(idx + "-" + index),class:index % 2 ? '' : 'first-half',attrs:{"id-defined":"","item":item,"allowed-to-select":_vm.allowedToSelect,"active-items":_vm.activeItems},on:{"mouseover-item":function($event){return _vm.mouseoverItem($event)},"mouseleave-item":_vm.mouseleaveItem,"click-item":function($event){return _vm.clickItem($event)}}})}),1)],1)}),0),_vm._ssrNode(" <div class=\"time-picker-right-bar\" data-v-69022ce1>"+((_vm.$i18n.locale === 'en')?("<div class=\"item\" data-v-69022ce1>12 AM</div> "+(_vm._ssrList((11),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape(_vm._s(i)+" AM")+"</div>")}))+" <div class=\"item\" data-v-69022ce1>12 PM</div> "+(_vm._ssrList((11),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape(_vm._s(i)+" PM")+"</div>")}))):((_vm._ssrList((24),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape(_vm._s(i - 1)+":00")+"</div>")}))))+"</div>")],2)]),_vm._ssrNode(" "),_c('loader',{attrs:{"is-loading":_vm.isLoading,"absolute":""}})],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/TimePicker.vue?vue&type=template&id=69022ce1&scoped=true&

// EXTERNAL MODULE: external "@mdi/js"
var js_ = __webpack_require__(48);

// EXTERNAL MODULE: ./components/TimePickerItem.vue + 4 modules
var TimePickerItem = __webpack_require__(947);

// EXTERNAL MODULE: ./components/Loader.vue + 4 modules
var Loader = __webpack_require__(84);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/TimePicker.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



const STATUS_FREE = 0;
const STATUS_RESERVED = 1;
const STATUS_OCCUPIED = 2; // const STATUS_SOME_AVAILABILITY = 4

/* harmony default export */ var TimePickervue_type_script_lang_js_ = ({
  name: 'TimePicker',
  components: {
    TimePickerItem: TimePickerItem["default"],
    Loader: Loader["default"]
  },
  filters: {
    dayFormat(time, format = 'HH:mm') {
      return time.format(format);
    }

  },
  props: {
    username: {
      type: String,
      required: true
    },
    lessonLength: {
      type: Number,
      required: true
    },
    quantityLessons: {
      type: Number,
      required: true
    },
    currentTime: {
      type: Object,
      required: true
    },
    isShownTimePickerDialog: {
      type: Boolean,
      required: true
    }
  },

  data() {
    return {
      mdiChevronLeft: js_["mdiChevronLeft"],
      mdiChevronRight: js_["mdiChevronRight"],
      key: 1,
      now: this.$dayjs(),
      items: [],
      activeItems: [],
      isLoading: false,
      mounted: false
    };
  },

  computed: {
    firstDayOfWeek() {
      return this.currentTime.day(1);
    },

    lastDayOfWeek() {
      return this.currentTime.day(7);
    },

    slots() {
      return this.$store.state.teacher_profile.slots;
    },

    selectedSlots() {
      return this.$store.state.teacher_profile.selectedSlots || [];
    },

    isPrevButtonDisabled() {
      return this.now.day(1).isSameOrAfter(this.firstDayOfWeek, 'day');
    },

    quantityItemsPerLesson() {
      return this.lessonLength / 30;
    },

    allowedToSelect() {
      return this.selectedSlots.length < this.quantityLessons || this.quantityLessons === 1;
    },

    calendar() {
      const result = [];

      for (let i = 0; i < 7; i++) {
        result.push(this.items.slice(i * 48, 48 * (i + 1)));
      }

      return result;
    },

    isUserLogged() {
      return this.$store.getters['user/isUserLogged'];
    },

    timeZone() {
      return this.$store.getters['user/timeZone'];
    }

  },
  watch: {
    lessonLength() {
      this.reset();
    },

    quantityLessons() {
      this.reset();
    },

    slots: {
      handler() {
        if (!this.isShownTimePickerDialog) {
          setTimeout(this.getItems);
        }
      },

      deep: true
    },

    isShownTimePickerDialog(newValue, oldValue) {
      if (this.mounted && newValue) {
        this.scroll();
      }
    }

  },

  async mounted() {
    const selectedSlots = window.localStorage.getItem('selected-slots') ? JSON.parse(window.localStorage.getItem('selected-slots')) : null;

    if (selectedSlots) {
      this.$store.commit('teacher_profile/SET_SELECTED_SLOTS', selectedSlots);
      window.localStorage.removeItem('selected-slots');
    }

    await this.getItems();
    this.scroll();
    this.mounted = true;
  },

  methods: {
    getItems() {
      const result = [];
      let selectedSlots = [];
      this.selectedSlots.forEach(item => selectedSlots = selectedSlots.concat(item.map(el => el.date)));

      for (let d = 1; d <= 7; d++) {
        for (let h = 0; h < 48; h++) {
          var _sameItem, _sameItem2, _sameItem3, _sameItem4, _sameItem5, _sameItem6;

          const date = this.getDayOfWeek(d).hour(Math.floor(h / 2)).minute(h % 2 ? 30 : 0).second(0);
          const dateOffset = date.tz(this.timeZone).utcOffset();
          let sameItem;

          for (let s = 0; s < this.slots.length; s++) {
            const dateObj = this.$dayjs(this.slots[s].date);

            if (date.isSame(dateObj.add(dateOffset, 'minute'), 'minute')) {
              sameItem = this.slots[s];
              break;
            }
          }

          const dateByUTC = date.add(this.$dayjs(date).tz(this.timeZone).utcOffset() * -1, 'minute').format();
          result.push({
            id: (_sameItem = sameItem) === null || _sameItem === void 0 ? void 0 : _sameItem.id,
            status: (_sameItem2 = sameItem) === null || _sameItem2 === void 0 ? void 0 : _sameItem2.status,
            date: dateByUTC,
            isSelected: selectedSlots.includes(dateByUTC),
            isAvailable: ((_sameItem3 = sameItem) === null || _sameItem3 === void 0 ? void 0 : _sameItem3.status) === STATUS_FREE && !this.now.add(1, 'day').isSameOrAfter(date, 'minute'),
            isUnavailable: ((_sameItem4 = sameItem) === null || _sameItem4 === void 0 ? void 0 : _sameItem4.status) === STATUS_OCCUPIED || ((_sameItem5 = sameItem) === null || _sameItem5 === void 0 ? void 0 : _sameItem5.status) === STATUS_RESERVED || ((_sameItem6 = sameItem) === null || _sameItem6 === void 0 ? void 0 : _sameItem6.status) === STATUS_FREE && this.now.add(1, 'day').isSameOrAfter(date, 'minute')
          });
        }
      }

      this.items = result;
    },

    async toggleWeek(day) {
      const date = this.firstDayOfWeek.add(day, 'day');
      await this.$store.dispatch('loadingAllow', false);
      this.isLoading = true;
      this.$store.dispatch('teacher_profile/getSlots', {
        slug: this.username,
        date
      }).then(() => {
        this.$emit('update-current-time', date);
        this.$nextTick(this.getItems);
      }).then(() => {
        this.scroll();
      }).finally(() => {
        this.isLoading = false;
        this.$store.dispatch('loadingAllow', true);
      });
    },

    getDayOfWeek(day) {
      return this.currentTime.day(day);
    },

    checkDirection(item, index) {
      let direction = 0;

      for (let i = 1; i < this.quantityItemsPerLesson; i++) {
        if (this.items[index + i].isAvailable && !this.items[index + i].isSelected) {
          direction = 1;
        } else {
          direction = 0;
          break;
        }
      }

      if (direction === 0) {
        for (let i = 1; i < this.quantityItemsPerLesson; i++) {
          if (this.items[index - i].isAvailable && !this.items[index - i].isSelected) {
            direction = -1;
          } else {
            direction = 0;
            break;
          }
        }
      }

      return direction;
    },

    clickItem(item) {
      if (!item.isSelected && this.allowedToSelect) {
        const arr = [item];

        if (this.quantityItemsPerLesson === 1 && this.quantityLessons === 1) {
          this.activeItems = [];
          this.resetSelectedSlots();
        }

        if (this.quantityItemsPerLesson > 1) {
          const index = this.items.indexOf(item);
          const direction = this.checkDirection(item, index);

          if (direction) {
            if (this.quantityLessons === 1) {
              this.resetSelectedSlots();
            }

            for (let i = 1; i < this.quantityItemsPerLesson; i++) {
              arr.push(this.items[index + i * direction]);
            }
          } else {
            return;
          }
        }

        arr.forEach(item => item.isSelected = true);
        this.$store.commit('teacher_profile/ADD_SELECTED_SLOT', arr);
      } else {
        for (let i = 0; i < this.selectedSlots.length; i++) {
          var _this$selectedSlots$i;

          const ids = (_this$selectedSlots$i = this.selectedSlots[i]) === null || _this$selectedSlots$i === void 0 ? void 0 : _this$selectedSlots$i.map(el => el.id);

          if (ids.includes(item.id)) {
            this.$store.commit('teacher_profile/REMOVE_SELECTED_SLOT', i);
            this.items.forEach(arr => {
              if (ids.includes(arr.id)) {
                arr.isSelected = false;
              }
            });
            break;
          }
        }
      }

      if (!this.isUserLogged) {
        window.setTimeout(() => {
          this.$emit('next-step');
        }, 300);
      }
    },

    mouseoverItem(item) {
      if (this.quantityItemsPerLesson === 1) {
        this.activeItems.push(item);
        return;
      }

      if (this.quantityItemsPerLesson > 1) {
        const index = this.items.indexOf(item);
        const direction = this.checkDirection(item, index);

        if (direction) {
          this.activeItems.push(item);

          for (let i = 1; i < this.quantityItemsPerLesson; i++) {
            this.activeItems.push(this.items[index + i * direction]);
          }
        }
      }
    },

    mouseleaveItem() {
      this.activeItems = [];
    },

    resetSelectedSlots() {
      this.$store.commit('teacher_profile/RESET_SELECTED_SLOTS');
      this.items.forEach(arr => {
        arr.isSelected = false;
      });
    },

    reset() {
      this.activeItems = [];
      this.resetSelectedSlots();
      this.key++;
    },

    scroll() {
      const options = {
        top: 560,
        behavior: 'instant'
      };

      if (this.selectedSlots.length) {
        const [earliestTime] = this.selectedSlots.flat().map(slot => {
          const dateObj = this.$dayjs(slot.date);
          return dateObj.add(dateObj.tz(this.timeZone).utcOffset(), 'minute').format();
        }).filter(date => this.$dayjs(date).isBetween(this.firstDayOfWeek, this.lastDayOfWeek)).map(date => this.$dayjs(date).format('HH-mm')).sort();
        const el = document.getElementById(`h-${earliestTime}`);

        if (el) {
          options.top = el.offsetTop - 84;
        }
      }

      setTimeout(() => {
        this.$refs.timePickerWrap.scroll(options);
      });
    }

  }
});
// CONCATENATED MODULE: ./components/TimePicker.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_TimePickervue_type_script_lang_js_ = (TimePickervue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(87);

// CONCATENATED MODULE: ./components/TimePicker.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(986)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_TimePickervue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "69022ce1",
  "637ca153"
  
)

/* harmony default export */ var TimePicker = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {TimePickerItem: __webpack_require__(947).default,Loader: __webpack_require__(84).default})


/* vuetify-loader */


installComponents_default()(component, {VIcon: VIcon["a" /* default */]})


/***/ }),

/***/ 956:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(998);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("a8b919c2", content, true, context)
};

/***/ }),

/***/ 958:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(933);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 959:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-status[data-v-652352c7]{width:16px;height:16px;border-radius:50%;border:2px solid #fff;background:#636363;z-index:2}.user-status--idle[data-v-652352c7]{background:linear-gradient(122.42deg,var(--v-redLight-base),var(--v-orangeLight2-base))}.user-status--online[data-v-652352c7]{background:var(--v-success-base)}.user-status--large[data-v-652352c7]{width:25px;height:25px}@media only screen and (max-width:991px){.user-status--large[data-v-652352c7]{width:23px;height:23px}}@media only screen and (max-width:639px){.user-status--large[data-v-652352c7]{width:21px;height:21px}}@media only screen and (max-width:479px){.user-status--large[data-v-652352c7]{width:19px;height:19px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 962:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1015);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("c8420d6e", content, true, context)
};

/***/ }),

/***/ 963:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1017);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("8eac9684", content, true, context)
};

/***/ }),

/***/ 964:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1019);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("9e60533c", content, true, context)
};

/***/ }),

/***/ 967:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/CalendarDate.vue?vue&type=template&id=76a41d38&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"calendar-date",style:(_vm.styles),on:{"click":_vm.clickHandler}},[_vm._ssrNode("<div class=\"v-btn\">"+_vm._ssrEscape(_vm._s(_vm.$dayjs(_vm.date).format('D')))+"</div> <div"+(_vm._ssrClass(null,[
      'calendar-date-marker',
      { 'calendar-date-marker--free': _vm.isFree },
      { 'calendar-date-marker--some-free': _vm.isSomeFree },
      { 'calendar-date-marker--occupied': _vm.isOccupied } ]))+"></div>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/CalendarDate.vue?vue&type=template&id=76a41d38&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/CalendarDate.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
const STATUS_PAST = -1;
const STATUS_FREE = 0;
const STATUS_OCCUPIED = 2;
const STATUS_EMPTY = 3;
const STATUS_SOME_FREE = 4;
/* harmony default export */ var CalendarDatevue_type_script_lang_js_ = ({
  name: 'CalendarDate',
  props: {
    date: {
      type: String,
      required: true
    },
    item: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: String,
      required: true
    }
  },
  computed: {
    isPast() {
      return this.item.status === STATUS_PAST;
    },

    isFree() {
      return this.item.status === STATUS_FREE;
    },

    isOccupied() {
      return this.item.status === STATUS_OCCUPIED;
    },

    isEmpty() {
      return this.item.status === STATUS_EMPTY;
    },

    isSomeFree() {
      return this.item.status === STATUS_SOME_FREE;
    },

    hasAction() {
      return (this.isOccupied || this.isSomeFree) && this.type === 'upcoming';
    },

    styles() {
      return {
        cursor: this.hasAction ? 'pointer' : 'auto'
      };
    }

  },
  methods: {
    clickHandler() {
      if (this.hasAction) {
        this.$emit('click-date', this.item.date);
      }
    }

  }
});
// CONCATENATED MODULE: ./components/CalendarDate.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_CalendarDatevue_type_script_lang_js_ = (CalendarDatevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/CalendarDate.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1014)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_CalendarDatevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "7e29c724"
  
)

/* harmony default export */ var CalendarDate = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 976:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1033);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("43d316a6", content, true, context)
};

/***/ }),

/***/ 977:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1036);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("37cc6ed2", content, true)

/***/ }),

/***/ 978:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessageDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(939);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessageDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessageDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessageDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessageDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 979:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-application .v-dialog.message-dialog>.v-card{padding:32px 40px!important}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog>.v-card{padding:50px 18px 74px!important}.v-application .v-dialog.message-dialog>.v-card .dialog-content,.v-application .v-dialog.message-dialog>.v-card .message-dialog-body,.v-application .v-dialog.message-dialog>.v-card .v-form{height:100%}.v-application .v-dialog.message-dialog>.v-card .message-dialog-body{overflow-y:auto}}.v-application .v-dialog.message-dialog .message-dialog-header{display:inline-block;padding-right:60px;font-size:20px;font-weight:700;line-height:1.1}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-header{position:absolute;top:0;left:0;width:100%;height:50px;display:flex;align-items:center;padding-left:18px;font-size:18px}}.v-application .v-dialog.message-dialog .message-dialog-body .row .col:first-child{padding-right:20px}.v-application .v-dialog.message-dialog .message-dialog-body .row .col:last-child{padding-left:20px}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-text div:first-child{font-size:16px;font-weight:600}}.v-application .v-dialog.message-dialog .message-dialog-text ul{padding-left:20px}@media only screen and (min-width:992px){.v-application .v-dialog.message-dialog .message-dialog-footer{margin-top:28px}}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-footer{position:absolute;bottom:0;left:0;width:100%;height:74px;padding:0 18px}}.v-application .v-dialog.message-dialog .message-dialog-footer .prev-button{color:var(--v-orange-base);cursor:pointer}.v-application .v-dialog.message-dialog .text-editor .ProseMirror{min-height:248px}.v-application .v-dialog.message-dialog .text-editor-buttons{left:8px;right:auto}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 980:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(940);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 981:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".time-picker-item[data-v-7467ec82]{position:relative;height:32px;box-shadow:inset -1px -1px 0 #e0e0e0}.time-picker-item.free[data-v-7467ec82]{background-color:var(--v-success-base);cursor:pointer}.time-picker-item.active[data-v-7467ec82]{background:#fff repeating-linear-gradient(45deg,rgba(251,176,59,.6),rgba(251,176,59,.6) 7px,var(--v-orange-base) 0,var(--v-orange-base) 20px)}.time-picker-item.selected[data-v-7467ec82]{background-color:var(--v-orange-base)}.time-picker-item.unavailable[data-v-7467ec82]{background-color:#636363}.time-picker-item.first-half[data-v-7467ec82]:after{content:\"\";position:absolute;left:0;bottom:0;width:100%;height:1px;box-shadow:inset 0 -1px 0 #f7f7f7}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 984:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Steps.vue?vue&type=template&id=307c13c8&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"steps"},[_vm._ssrNode("<div class=\"steps-wrap\" data-v-307c13c8>","</div>",[_vm._ssrNode("<div id=\"steps-helper\" class=\"steps-helper\" data-v-307c13c8>","</div>",[_vm._ssrNode("<div class=\"steps-list\" data-v-307c13c8>","</div>",_vm._l((_vm.steps),function(step){return _vm._ssrNode("<div"+(_vm._ssrAttr("id",("step-" + (step.id))))+(_vm._ssrClass(null,[
            'step-item',
            { 'step-item--active': step.id === _vm.activeItemId },
            { 'step-item--link': _vm.itemLink.id === step.id } ]))+" data-v-307c13c8>","</div>",[_vm._ssrNode("<div class=\"step-item-helper\" data-v-307c13c8><div class=\"step-item-number\" data-v-307c13c8><span data-v-307c13c8>"+_vm._ssrEscape(_vm._s(step.id)+".")+"</span></div> <div class=\"step-item-title\" data-v-307c13c8>"+_vm._ssrEscape(_vm._s(_vm.$t(step.title)))+"</div></div> "),(_vm.itemLink.id === step.id)?_c('nuxt-link',{attrs:{"to":_vm.itemLink.path}}):_vm._e()],2)}),0)])])])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/Steps.vue?vue&type=template&id=307c13c8&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Steps.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var Stepsvue_type_script_lang_js_ = ({
  name: 'Steps',
  props: {
    activeItemId: {
      type: Number,
      default: 1
    },
    itemLink: {
      type: Object,
      default: () => ({})
    }
  },

  mounted() {
    const el = document.getElementById('steps-helper');
    const activeEl = document.getElementById(`step-${this.activeItemId}`);

    if (this.$vuetify.breakpoint.smAndDown && el && activeEl) {
      const x = activeEl.getBoundingClientRect().left - 15;
      el.scrollTo({
        left: x,
        behavior: 'smooth'
      });
    }
  }

});
// CONCATENATED MODULE: ./components/Steps.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_Stepsvue_type_script_lang_js_ = (Stepsvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/Steps.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1018)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_Stepsvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "307c13c8",
  "7c31ffbf"
  
)

/* harmony default export */ var Steps = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 986:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePicker_vue_vue_type_style_index_0_id_69022ce1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(950);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePicker_vue_vue_type_style_index_0_id_69022ce1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePicker_vue_vue_type_style_index_0_id_69022ce1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePicker_vue_vue_type_style_index_0_id_69022ce1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePicker_vue_vue_type_style_index_0_id_69022ce1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 987:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".time-picker[data-v-69022ce1]{--timepicker-sidebar-width:48px;position:relative;display:flex;flex-direction:column;flex-grow:1}@media only screen and (max-width:767px){.time-picker[data-v-69022ce1]{--timepicker-sidebar-width:30px}}.time-picker-helper[data-v-69022ce1],.time-picker-toggle[data-v-69022ce1]{display:flex}.time-picker-toggle[data-v-69022ce1]{justify-content:center;align-items:center;margin-bottom:12px}.time-picker-toggle .btn[data-v-69022ce1]{margin:0 10px;cursor:pointer}.time-picker-toggle .btn--disabled[data-v-69022ce1]{cursor:auto;opacity:.4}.time-picker-toggle .period[data-v-69022ce1]{min-width:120px;font-size:16px;font-weight:700;line-height:22px}.time-picker-wrap[data-v-69022ce1]{flex-grow:1;height:132px;overflow-y:auto;overflow-x:hidden}.time-picker-wrap-helper[data-v-69022ce1]{display:flex}.time-picker-left-bar .item[data-v-69022ce1],.time-picker-right-bar .item[data-v-69022ce1],.time-picker-top-bar .item[data-v-69022ce1]{font-size:12px;color:#575757;text-align:center;line-height:1.333;white-space:nowrap}@media only screen and (max-width:991px){.time-picker-left-bar .item[data-v-69022ce1],.time-picker-right-bar .item[data-v-69022ce1],.time-picker-top-bar .item[data-v-69022ce1]{font-size:10px}}.time-picker-left-bar[data-v-69022ce1],.time-picker-right-bar[data-v-69022ce1]{width:var(--timepicker-sidebar-width)}.time-picker-left-bar .item[data-v-69022ce1],.time-picker-right-bar .item[data-v-69022ce1]{height:64px}.time-picker-top-bar[data-v-69022ce1]{padding-right:8px}.time-picker-top-bar-helper[data-v-69022ce1]{display:flex;justify-content:space-around;width:calc(100% - var(--timepicker-sidebar-width)*2 - 2px)}.time-picker-top-bar .item[data-v-69022ce1]{display:flex;flex-wrap:wrap;justify-content:center;align-items:center;flex-basis:14.2857%;height:32px}@media only screen and (max-width:479px){.time-picker-top-bar .item[data-v-69022ce1]{font-size:10px}}.time-picker-graph[data-v-69022ce1]{width:calc(100% - 48px);display:flex;border-color:#e0e0e0;border-style:solid;border-width:1px 0 0 1px}@media only screen and (max-width:767px){.time-picker-graph[data-v-69022ce1]{width:calc(100% - 24px)}}.time-picker-graph .day[data-v-69022ce1]{flex-grow:1}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 990:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Calendar.vue?vue&type=template&id=e37c7c60&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"calendar calendar--read-only"},[_vm._ssrNode("<div class=\"calendar-title text-center font-weight-bold mb-2 mb-lg-3\">"+_vm._ssrEscape("\n    "+_vm._s(_vm.$dayjs(_vm.currentDate).format('MMMM YYYY'))+"\n  ")+"</div> "),_c('v-calendar',{attrs:{"weekdays":_vm.weekday,"locale":_vm.locale,"start":_vm.$dayjs(_vm.currentDate).format('YYYY-MM-DD')},scopedSlots:_vm._u([{key:"day-label",fn:function(ref){
var date = ref.date;
return [_c('calendar-date',{attrs:{"date":date,"type":_vm.type,"item":_vm.getDate(date)},on:{"click-date":function($event){return _vm.$emit('click-date', $event)}}})]}}])})],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/Calendar.vue?vue&type=template&id=e37c7c60&

// EXTERNAL MODULE: ./components/CalendarDate.vue + 4 modules
var CalendarDate = __webpack_require__(967);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Calendar.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var Calendarvue_type_script_lang_js_ = ({
  name: 'Calendar',
  components: {
    CalendarDate: CalendarDate["default"]
  },
  props: {
    currentDate: {
      type: Object,
      required: true
    },
    items: {
      type: Array,
      required: true
    },
    type: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      weekday: [1, 2, 3, 4, 5, 6, 0]
    };
  },

  computed: {
    locale() {
      return this.$i18n.locale;
    }

  },
  methods: {
    getDate(date) {
      return this.items.find(item => item.date === date);
    }

  }
});
// CONCATENATED MODULE: ./components/Calendar.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_Calendarvue_type_script_lang_js_ = (Calendarvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VCalendar/mixins/calendar-with-events.sass
var calendar_with_events = __webpack_require__(1034);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/ripple/index.js
var ripple = __webpack_require__(22);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/mixins.js
var mixins = __webpack_require__(2);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/colorable/index.js
var colorable = __webpack_require__(9);

// EXTERNAL MODULE: external "vue"
var external_vue_ = __webpack_require__(1);
var external_vue_default = /*#__PURE__*/__webpack_require__.n(external_vue_);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/mixins/localable/index.js

/* harmony default export */ var localable = (external_vue_default.a.extend({
  name: 'localable',
  props: {
    locale: String
  },
  computed: {
    currentLocale() {
      return this.locale || this.$vuetify.lang.current;
    }

  }
}));
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/mixins/mouse.js

/* harmony default export */ var mouse = (external_vue_default.a.extend({
  name: 'mouse',
  methods: {
    getDefaultMouseEventHandlers(suffix, getEvent) {
      return this.getMouseEventHandlers({
        ['click' + suffix]: {
          event: 'click'
        },
        ['contextmenu' + suffix]: {
          event: 'contextmenu',
          prevent: true,
          result: false
        },
        ['mousedown' + suffix]: {
          event: 'mousedown'
        },
        ['mousemove' + suffix]: {
          event: 'mousemove'
        },
        ['mouseup' + suffix]: {
          event: 'mouseup'
        },
        ['mouseenter' + suffix]: {
          event: 'mouseenter'
        },
        ['mouseleave' + suffix]: {
          event: 'mouseleave'
        },
        ['touchstart' + suffix]: {
          event: 'touchstart'
        },
        ['touchmove' + suffix]: {
          event: 'touchmove'
        },
        ['touchend' + suffix]: {
          event: 'touchend'
        }
      }, getEvent);
    },

    getMouseEventHandlers(events, getEvent) {
      const on = {};

      for (const event in events) {
        const eventOptions = events[event];
        if (!this.$listeners[event]) continue; // TODO somehow pull in modifiers

        const prefix = eventOptions.passive ? '&' : (eventOptions.once ? '~' : '') + (eventOptions.capture ? '!' : '');
        const key = prefix + eventOptions.event;

        const handler = e => {
          const mouseEvent = e;

          if (eventOptions.button === undefined || mouseEvent.buttons > 0 && mouseEvent.button === eventOptions.button) {
            if (eventOptions.prevent) {
              e.preventDefault();
            }

            if (eventOptions.stop) {
              e.stopPropagation();
            }

            this.$emit(event, getEvent(e));
          }

          return eventOptions.result;
        };

        if (key in on) {
          /* istanbul ignore next */
          if (Array.isArray(on[key])) {
            on[key].push(handler);
          } else {
            on[key] = [on[key], handler];
          }
        } else {
          on[key] = handler;
        }
      }

      return on;
    }

  }
}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/themeable/index.js
var themeable = __webpack_require__(7);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/util/dateTimeUtils.js
function createUTCDate(year, month = 0, day = 1) {
  let date;

  if (year < 100 && year >= 0) {
    date = new Date(Date.UTC(year, month, day));

    if (isFinite(date.getUTCFullYear())) {
      date.setUTCFullYear(year);
    }
  } else {
    date = new Date(Date.UTC(year, month, day));
  }

  return date;
}

function firstWeekOffset(year, firstDayOfWeek, firstDayOfYear) {
  const firstWeekDayInFirstWeek = 7 + firstDayOfWeek - firstDayOfYear;
  const firstWeekDayOfYear = (7 + createUTCDate(year, 0, firstWeekDayInFirstWeek).getUTCDay() - firstDayOfWeek) % 7;
  return -firstWeekDayOfYear + firstWeekDayInFirstWeek - 1;
}

function dayOfYear(year, month, day, firstDayOfWeek) {
  let dayOfYear = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334][month];

  if (month > 1 && isLeapYear(year)) {
    dayOfYear++;
  }

  return dayOfYear + day;
}

function weeksInYear(year, firstDayOfWeek, firstDayOfYear) {
  const weekOffset = firstWeekOffset(year, firstDayOfWeek, firstDayOfYear);
  const weekOffsetNext = firstWeekOffset(year + 1, firstDayOfWeek, firstDayOfYear);
  const daysInYear = isLeapYear(year) ? 366 : 365;
  return (daysInYear - weekOffset + weekOffsetNext) / 7;
}

function weekNumber(year, month, day, firstDayOfWeek, localeFirstDayOfYear) {
  const weekOffset = firstWeekOffset(year, firstDayOfWeek, localeFirstDayOfYear);
  const week = Math.ceil((dayOfYear(year, month, day, firstDayOfWeek) - weekOffset) / 7);

  if (week < 1) {
    return week + weeksInYear(year - 1, firstDayOfWeek, localeFirstDayOfYear);
  } else if (week > weeksInYear(year, firstDayOfWeek, localeFirstDayOfYear)) {
    return week - weeksInYear(year, firstDayOfWeek, localeFirstDayOfYear);
  } else {
    return week;
  }
}
function isLeapYear(year) {
  return year % 4 === 0 && year % 100 !== 0 || year % 400 === 0;
}
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/util/timestamp.js

const PARSE_REGEX = /^(\d{4})-(\d{1,2})(-(\d{1,2}))?([^\d]+(\d{1,2}))?(:(\d{1,2}))?(:(\d{1,2}))?$/;
const PARSE_TIME = /(\d\d?)(:(\d\d?)|)(:(\d\d?)|)/;
const DAYS_IN_MONTH = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
const DAYS_IN_MONTH_LEAP = [0, 31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
const DAYS_IN_MONTH_MIN = 28;
const DAYS_IN_MONTH_MAX = 31;
const MONTH_MAX = 12;
const MONTH_MIN = 1;
const DAY_MIN = 1;
const DAYS_IN_WEEK = 7;
const MINUTES_IN_HOUR = 60;
const MINUTE_MAX = 59;
const MINUTES_IN_DAY = 24 * 60;
const HOURS_IN_DAY = 24;
const HOUR_MAX = 23;
const FIRST_HOUR = 0;
const OFFSET_YEAR = 10000;
const OFFSET_MONTH = 100;
const OFFSET_HOUR = 100;
const OFFSET_TIME = 10000;
function getStartOfWeek(timestamp, weekdays, today) {
  const start = copyTimestamp(timestamp);
  findWeekday(start, weekdays[0], prevDay);
  updateFormatted(start);

  if (today) {
    updateRelative(start, today, start.hasTime);
  }

  return start;
}
function getEndOfWeek(timestamp, weekdays, today) {
  const end = copyTimestamp(timestamp);
  findWeekday(end, weekdays[weekdays.length - 1]);
  updateFormatted(end);

  if (today) {
    updateRelative(end, today, end.hasTime);
  }

  return end;
}
function getStartOfMonth(timestamp) {
  const start = copyTimestamp(timestamp);
  start.day = DAY_MIN;
  updateWeekday(start);
  updateFormatted(start);
  return start;
}
function getEndOfMonth(timestamp) {
  const end = copyTimestamp(timestamp);
  end.day = daysInMonth(end.year, end.month);
  updateWeekday(end);
  updateFormatted(end);
  return end;
}
function validateTime(input) {
  return typeof input === 'number' && isFinite(input) || !!PARSE_TIME.exec(input) || typeof input === 'object' && isFinite(input.hour) && isFinite(input.minute);
}
function parseTime(input) {
  if (typeof input === 'number') {
    // when a number is given, it's minutes since 12:00am
    return input;
  } else if (typeof input === 'string') {
    // when a string is given, it's a hh:mm:ss format where seconds are optional
    const parts = PARSE_TIME.exec(input);

    if (!parts) {
      return false;
    }

    return parseInt(parts[1]) * 60 + parseInt(parts[3] || 0);
  } else if (typeof input === 'object') {
    // when an object is given, it must have hour and minute
    if (typeof input.hour !== 'number' || typeof input.minute !== 'number') {
      return false;
    }

    return input.hour * 60 + input.minute;
  } else {
    // unsupported type
    return false;
  }
}
function validateTimestamp(input) {
  return typeof input === 'number' && isFinite(input) || typeof input === 'string' && !!PARSE_REGEX.exec(input) || input instanceof Date;
}
function parseTimestamp(input, required = false, now) {
  if (typeof input === 'number' && isFinite(input)) {
    input = new Date(input);
  }

  if (input instanceof Date) {
    const date = parseDate(input);

    if (now) {
      updateRelative(date, now, date.hasTime);
    }

    return date;
  }

  if (typeof input !== 'string') {
    if (required) {
      throw new Error(`${input} is not a valid timestamp. It must be a Date, number of seconds since Epoch, or a string in the format of YYYY-MM-DD or YYYY-MM-DD hh:mm. Zero-padding is optional and seconds are ignored.`);
    }

    return null;
  } // YYYY-MM-DD hh:mm:ss


  const parts = PARSE_REGEX.exec(input);

  if (!parts) {
    if (required) {
      throw new Error(`${input} is not a valid timestamp. It must be a Date, number of seconds since Epoch, or a string in the format of YYYY-MM-DD or YYYY-MM-DD hh:mm. Zero-padding is optional and seconds are ignored.`);
    }

    return null;
  }

  const timestamp = {
    date: input,
    time: '',
    year: parseInt(parts[1]),
    month: parseInt(parts[2]),
    day: parseInt(parts[4]) || 1,
    hour: parseInt(parts[6]) || 0,
    minute: parseInt(parts[8]) || 0,
    weekday: 0,
    hasDay: !!parts[4],
    hasTime: !!(parts[6] && parts[8]),
    past: false,
    present: false,
    future: false
  };
  updateWeekday(timestamp);
  updateFormatted(timestamp);

  if (now) {
    updateRelative(timestamp, now, timestamp.hasTime);
  }

  return timestamp;
}
function parseDate(date) {
  return updateFormatted({
    date: '',
    time: '',
    year: date.getFullYear(),
    month: date.getMonth() + 1,
    day: date.getDate(),
    weekday: date.getDay(),
    hour: date.getHours(),
    minute: date.getMinutes(),
    hasDay: true,
    hasTime: true,
    past: false,
    present: true,
    future: false
  });
}
function getDayIdentifier(timestamp) {
  return timestamp.year * OFFSET_YEAR + timestamp.month * OFFSET_MONTH + timestamp.day;
}
function getTimeIdentifier(timestamp) {
  return timestamp.hour * OFFSET_HOUR + timestamp.minute;
}
function getTimestampIdentifier(timestamp) {
  return getDayIdentifier(timestamp) * OFFSET_TIME + getTimeIdentifier(timestamp);
}
function updateRelative(timestamp, now, time = false) {
  let a = getDayIdentifier(now);
  let b = getDayIdentifier(timestamp);
  let present = a === b;

  if (timestamp.hasTime && time && present) {
    a = getTimeIdentifier(now);
    b = getTimeIdentifier(timestamp);
    present = a === b;
  }

  timestamp.past = b < a;
  timestamp.present = present;
  timestamp.future = b > a;
  return timestamp;
}
function isTimedless(input) {
  return input instanceof Date || typeof input === 'number' && isFinite(input);
}
function updateHasTime(timestamp, hasTime, now) {
  if (timestamp.hasTime !== hasTime) {
    timestamp.hasTime = hasTime;

    if (!hasTime) {
      timestamp.hour = HOUR_MAX;
      timestamp.minute = MINUTE_MAX;
      timestamp.time = getTime(timestamp);
    }

    if (now) {
      updateRelative(timestamp, now, timestamp.hasTime);
    }
  }

  return timestamp;
}
function updateMinutes(timestamp, minutes, now) {
  timestamp.hasTime = true;
  timestamp.hour = Math.floor(minutes / MINUTES_IN_HOUR);
  timestamp.minute = minutes % MINUTES_IN_HOUR;
  timestamp.time = getTime(timestamp);

  if (now) {
    updateRelative(timestamp, now, true);
  }

  return timestamp;
}
function updateWeekday(timestamp) {
  timestamp.weekday = getWeekday(timestamp);
  return timestamp;
}
function updateFormatted(timestamp) {
  timestamp.time = getTime(timestamp);
  timestamp.date = getDate(timestamp);
  return timestamp;
}
function getWeekday(timestamp) {
  if (timestamp.hasDay) {
    const _ = Math.floor;
    const k = timestamp.day;
    const m = (timestamp.month + 9) % MONTH_MAX + 1;

    const C = _(timestamp.year / 100);

    const Y = timestamp.year % 100 - (timestamp.month <= 2 ? 1 : 0);
    return ((k + _(2.6 * m - 0.2) - 2 * C + Y + _(Y / 4) + _(C / 4)) % 7 + 7) % 7;
  }

  return timestamp.weekday;
}
function daysInMonth(year, month) {
  return isLeapYear(year) ? DAYS_IN_MONTH_LEAP[month] : DAYS_IN_MONTH[month];
}
function copyTimestamp(timestamp) {
  const {
    date,
    time,
    year,
    month,
    day,
    weekday,
    hour,
    minute,
    hasDay,
    hasTime,
    past,
    present,
    future
  } = timestamp;
  return {
    date,
    time,
    year,
    month,
    day,
    weekday,
    hour,
    minute,
    hasDay,
    hasTime,
    past,
    present,
    future
  };
}
function padNumber(x, length) {
  let padded = String(x);

  while (padded.length < length) {
    padded = '0' + padded;
  }

  return padded;
}
function getDate(timestamp) {
  let str = `${padNumber(timestamp.year, 4)}-${padNumber(timestamp.month, 2)}`;
  if (timestamp.hasDay) str += `-${padNumber(timestamp.day, 2)}`;
  return str;
}
function getTime(timestamp) {
  if (!timestamp.hasTime) {
    return '';
  }

  return `${padNumber(timestamp.hour, 2)}:${padNumber(timestamp.minute, 2)}`;
}
function nextMinutes(timestamp, minutes) {
  timestamp.minute += minutes;

  while (timestamp.minute > MINUTES_IN_HOUR) {
    timestamp.minute -= MINUTES_IN_HOUR;
    timestamp.hour++;

    if (timestamp.hour >= HOURS_IN_DAY) {
      nextDay(timestamp);
      timestamp.hour = FIRST_HOUR;
    }
  }

  return timestamp;
}
function nextDay(timestamp) {
  timestamp.day++;
  timestamp.weekday = (timestamp.weekday + 1) % DAYS_IN_WEEK;

  if (timestamp.day > DAYS_IN_MONTH_MIN && timestamp.day > daysInMonth(timestamp.year, timestamp.month)) {
    timestamp.day = DAY_MIN;
    timestamp.month++;

    if (timestamp.month > MONTH_MAX) {
      timestamp.month = MONTH_MIN;
      timestamp.year++;
    }
  }

  return timestamp;
}
function prevDay(timestamp) {
  timestamp.day--;
  timestamp.weekday = (timestamp.weekday + 6) % DAYS_IN_WEEK;

  if (timestamp.day < DAY_MIN) {
    timestamp.month--;

    if (timestamp.month < MONTH_MIN) {
      timestamp.year--;
      timestamp.month = MONTH_MAX;
    }

    timestamp.day = daysInMonth(timestamp.year, timestamp.month);
  }

  return timestamp;
}
function relativeDays(timestamp, mover = nextDay, days = 1) {
  while (--days >= 0) mover(timestamp);

  return timestamp;
}
function diffMinutes(min, max) {
  const Y = (max.year - min.year) * 525600;
  const M = (max.month - min.month) * 43800;
  const D = (max.day - min.day) * 1440;
  const h = (max.hour - min.hour) * 60;
  const m = max.minute - min.minute;
  return Y + M + D + h + m;
}
function findWeekday(timestamp, weekday, mover = nextDay, maxDays = 6) {
  while (timestamp.weekday !== weekday && --maxDays >= 0) mover(timestamp);

  return timestamp;
}
function getWeekdaySkips(weekdays) {
  const skips = [1, 1, 1, 1, 1, 1, 1];
  const filled = [0, 0, 0, 0, 0, 0, 0];

  for (let i = 0; i < weekdays.length; i++) {
    filled[weekdays[i]] = 1;
  }

  for (let k = 0; k < DAYS_IN_WEEK; k++) {
    let skip = 1;

    for (let j = 1; j < DAYS_IN_WEEK; j++) {
      const next = (k + j) % DAYS_IN_WEEK;

      if (filled[next]) {
        break;
      }

      skip++;
    }

    skips[k] = filled[k] * skip;
  }

  return skips;
}
function timestampToDate(timestamp) {
  const time = `${padNumber(timestamp.hour, 2)}:${padNumber(timestamp.minute, 2)}`;
  const date = timestamp.date;
  return new Date(`${date}T${time}:00+00:00`);
}
function createDayList(start, end, now, weekdaySkips, max = 42, min = 0) {
  const stop = getDayIdentifier(end);
  const days = [];
  let current = copyTimestamp(start);
  let currentIdentifier = 0;
  let stopped = currentIdentifier === stop;

  if (stop < getDayIdentifier(start)) {
    throw new Error('End date is earlier than start date.');
  }

  while ((!stopped || days.length < min) && days.length < max) {
    currentIdentifier = getDayIdentifier(current);
    stopped = stopped || currentIdentifier === stop;

    if (weekdaySkips[current.weekday] === 0) {
      current = nextDay(current);
      continue;
    }

    const day = copyTimestamp(current);
    updateFormatted(day);
    updateRelative(day, now);
    days.push(day);
    current = relativeDays(current, nextDay, weekdaySkips[current.weekday]);
  }

  if (!days.length) throw new Error('No dates found using specified start date, end date, and weekdays.');
  return days;
}
function createIntervalList(timestamp, first, minutes, count, now) {
  const intervals = [];

  for (let i = 0; i < count; i++) {
    const mins = first + i * minutes;
    const int = copyTimestamp(timestamp);
    intervals.push(updateMinutes(int, mins, now));
  }

  return intervals;
}
function createNativeLocaleFormatter(locale, getOptions) {
  const emptyFormatter = (_t, _s) => '';

  if (typeof Intl === 'undefined' || typeof Intl.DateTimeFormat === 'undefined') {
    return emptyFormatter;
  }

  return (timestamp, short) => {
    try {
      const intlFormatter = new Intl.DateTimeFormat(locale || undefined, getOptions(timestamp, short));
      return intlFormatter.format(timestampToDate(timestamp));
    } catch (e) {
      return '';
    }
  };
}
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/mixins/times.js


/* harmony default export */ var mixins_times = (external_vue_default.a.extend({
  name: 'times',
  props: {
    now: {
      type: String,
      validator: validateTimestamp
    }
  },
  data: () => ({
    times: {
      now: parseTimestamp('0000-00-00 00:00', true),
      today: parseTimestamp('0000-00-00', true)
    }
  }),
  computed: {
    parsedNow() {
      return this.now ? parseTimestamp(this.now, true) : null;
    }

  },
  watch: {
    parsedNow: 'updateTimes'
  },

  created() {
    this.updateTimes();
    this.setPresent();
  },

  methods: {
    setPresent() {
      this.times.now.present = this.times.today.present = true;
      this.times.now.past = this.times.today.past = false;
      this.times.now.future = this.times.today.future = false;
    },

    updateTimes() {
      const now = this.parsedNow || this.getNow();
      this.updateDay(now, this.times.now);
      this.updateTime(now, this.times.now);
      this.updateDay(now, this.times.today);
    },

    getNow() {
      return parseDate(new Date());
    },

    updateDay(now, target) {
      if (now.date !== target.date) {
        target.year = now.year;
        target.month = now.month;
        target.day = now.day;
        target.weekday = now.weekday;
        target.date = now.date;
      }
    },

    updateTime(now, target) {
      if (now.time !== target.time) {
        target.hour = now.hour;
        target.minute = now.minute;
        target.time = now.time;
      }
    }

  }
}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/resize/index.js
var resize = __webpack_require__(32);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/modes/common.js

const MILLIS_IN_DAY = 86400000;
function getVisuals(events, minStart = 0) {
  const visuals = events.map(event => ({
    event,
    columnCount: 0,
    column: 0,
    left: 0,
    width: 100
  }));
  visuals.sort((a, b) => {
    return Math.max(minStart, a.event.startTimestampIdentifier) - Math.max(minStart, b.event.startTimestampIdentifier) || b.event.endTimestampIdentifier - a.event.endTimestampIdentifier;
  });
  return visuals;
}
function hasOverlap(s0, e0, s1, e1, exclude = true) {
  return exclude ? !(s0 >= e1 || e0 <= s1) : !(s0 > e1 || e0 < s1);
}
function setColumnCount(groups) {
  groups.forEach(group => {
    group.visuals.forEach(groupVisual => {
      groupVisual.columnCount = groups.length;
    });
  });
}
function getRange(event) {
  return [event.startTimestampIdentifier, event.endTimestampIdentifier];
}
function getDayRange(event) {
  return [event.startIdentifier, event.endIdentifier];
}
function getNormalizedRange(event, dayStart) {
  return [Math.max(dayStart, event.startTimestampIdentifier), Math.min(dayStart + MILLIS_IN_DAY, event.endTimestampIdentifier)];
}
function getOpenGroup(groups, start, end, timed) {
  for (let i = 0; i < groups.length; i++) {
    const group = groups[i];
    let intersected = false;

    if (hasOverlap(start, end, group.start, group.end, timed)) {
      for (let k = 0; k < group.visuals.length; k++) {
        const groupVisual = group.visuals[k];
        const [groupStart, groupEnd] = timed ? getRange(groupVisual.event) : getDayRange(groupVisual.event);

        if (hasOverlap(start, end, groupStart, groupEnd, timed)) {
          intersected = true;
          break;
        }
      }
    }

    if (!intersected) {
      return i;
    }
  }

  return -1;
}
function getOverlapGroupHandler(firstWeekday) {
  const handler = {
    groups: [],
    min: -1,
    max: -1,
    reset: () => {
      handler.groups = [];
      handler.min = handler.max = -1;
    },
    getVisuals: (day, dayEvents, timed, reset = false) => {
      if (day.weekday === firstWeekday || reset) {
        handler.reset();
      }

      const dayStart = getTimestampIdentifier(day);
      const visuals = getVisuals(dayEvents, dayStart);
      visuals.forEach(visual => {
        const [start, end] = timed ? getRange(visual.event) : getDayRange(visual.event);

        if (handler.groups.length > 0 && !hasOverlap(start, end, handler.min, handler.max, timed)) {
          setColumnCount(handler.groups);
          handler.reset();
        }

        let targetGroup = getOpenGroup(handler.groups, start, end, timed);

        if (targetGroup === -1) {
          targetGroup = handler.groups.length;
          handler.groups.push({
            start,
            end,
            visuals: []
          });
        }

        const target = handler.groups[targetGroup];
        target.visuals.push(visual);
        target.start = Math.min(target.start, start);
        target.end = Math.max(target.end, end);
        visual.column = targetGroup;

        if (handler.min === -1) {
          handler.min = start;
          handler.max = end;
        } else {
          handler.min = Math.min(handler.min, start);
          handler.max = Math.max(handler.max, end);
        }
      });
      setColumnCount(handler.groups);

      if (timed) {
        handler.reset();
      }

      return visuals;
    }
  };
  return handler;
}
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/modes/stack.js


const FULL_WIDTH = 100;
const DEFAULT_OFFSET = 5;
const WIDTH_MULTIPLIER = 1.7;
/**
 * Variation of column mode where events can be stacked. The priority of this
 * mode is to stack events together taking up the least amount of space while
 * trying to ensure the content of the event is always visible as well as its
 * start and end. A sibling column has intersecting event content and must be
 * placed beside each other. Non-sibling columns are offset by 5% from the
 * previous column. The width is scaled by 1.7 so the events overlap and
 * whitespace is reduced. If there is a hole in columns the event width is
 * scaled up so it intersects with the next column. The columns have equal
 * width in the space they are given. If the event doesn't have any to the
 * right of it that intersect with it's content it's right side is extended
 * to the right side.
 */

const stack = (events, firstWeekday, overlapThreshold) => {
  const handler = getOverlapGroupHandler(firstWeekday); // eslint-disable-next-line max-statements

  return (day, dayEvents, timed, reset) => {
    if (!timed) {
      return handler.getVisuals(day, dayEvents, timed, reset);
    }

    const dayStart = getTimestampIdentifier(day);
    const visuals = getVisuals(dayEvents, dayStart);
    const groups = getGroups(visuals, dayStart);

    for (const group of groups) {
      const nodes = [];

      for (const visual of group.visuals) {
        const child = getNode(visual, dayStart);
        const index = getNextIndex(child, nodes);

        if (index === false) {
          const parent = getParent(child, nodes);

          if (parent) {
            child.parent = parent;
            child.sibling = hasOverlap(child.start, child.end, parent.start, addTime(parent.start, overlapThreshold));
            child.index = parent.index + 1;
            parent.children.push(child);
          }
        } else {
          const [parent] = getOverlappingRange(child, nodes, index - 1, index - 1);
          const children = getOverlappingRange(child, nodes, index + 1, index + nodes.length, true);
          child.children = children;
          child.index = index;

          if (parent) {
            child.parent = parent;
            child.sibling = hasOverlap(child.start, child.end, parent.start, addTime(parent.start, overlapThreshold));
            parent.children.push(child);
          }

          for (const grand of children) {
            if (grand.parent === parent) {
              grand.parent = child;
            }

            const grandNext = grand.index - child.index <= 1;

            if (grandNext && child.sibling && hasOverlap(child.start, addTime(child.start, overlapThreshold), grand.start, grand.end)) {
              grand.sibling = true;
            }
          }
        }

        nodes.push(child);
      }

      calculateBounds(nodes, overlapThreshold);
    }

    visuals.sort((a, b) => a.left - b.left || a.event.startTimestampIdentifier - b.event.startTimestampIdentifier);
    return visuals;
  };
};

function calculateBounds(nodes, overlapThreshold) {
  for (const node of nodes) {
    const {
      visual,
      parent
    } = node;
    const columns = getMaxChildIndex(node) + 1;
    const spaceLeft = parent ? parent.visual.left : 0;
    const spaceWidth = FULL_WIDTH - spaceLeft;
    const offset = Math.min(DEFAULT_OFFSET, FULL_WIDTH / columns);
    const columnWidthMultiplier = getColumnWidthMultiplier(node, nodes);
    const columnOffset = spaceWidth / (columns - node.index + 1);
    const columnWidth = spaceWidth / (columns - node.index + (node.sibling ? 1 : 0)) * columnWidthMultiplier;

    if (parent) {
      visual.left = node.sibling ? spaceLeft + columnOffset : spaceLeft + offset;
    }

    visual.width = hasFullWidth(node, nodes, overlapThreshold) ? FULL_WIDTH - visual.left : Math.min(FULL_WIDTH - visual.left, columnWidth * WIDTH_MULTIPLIER);
  }
}

function getColumnWidthMultiplier(node, nodes) {
  if (!node.children.length) {
    return 1;
  }

  const maxColumn = node.index + nodes.length;
  const minColumn = node.children.reduce((min, c) => Math.min(min, c.index), maxColumn);
  return minColumn - node.index;
}

function getOverlappingIndices(node, nodes) {
  const indices = [];

  for (const other of nodes) {
    if (hasOverlap(node.start, node.end, other.start, other.end)) {
      indices.push(other.index);
    }
  }

  return indices;
}

function getNextIndex(node, nodes) {
  const indices = getOverlappingIndices(node, nodes);
  indices.sort();

  for (let i = 0; i < indices.length; i++) {
    if (i < indices[i]) {
      return i;
    }
  }

  return false;
}

function getOverlappingRange(node, nodes, indexMin, indexMax, returnFirstColumn = false) {
  const overlapping = [];

  for (const other of nodes) {
    if (other.index >= indexMin && other.index <= indexMax && hasOverlap(node.start, node.end, other.start, other.end)) {
      overlapping.push(other);
    }
  }

  if (returnFirstColumn && overlapping.length > 0) {
    const first = overlapping.reduce((min, n) => Math.min(min, n.index), overlapping[0].index);
    return overlapping.filter(n => n.index === first);
  }

  return overlapping;
}

function getParent(node, nodes) {
  let parent = null;

  for (const other of nodes) {
    if (hasOverlap(node.start, node.end, other.start, other.end) && (parent === null || other.index > parent.index)) {
      parent = other;
    }
  }

  return parent;
}

function hasFullWidth(node, nodes, overlapThreshold) {
  for (const other of nodes) {
    if (other !== node && other.index > node.index && hasOverlap(node.start, addTime(node.start, overlapThreshold), other.start, other.end)) {
      return false;
    }
  }

  return true;
}

function getGroups(visuals, dayStart) {
  const groups = [];

  for (const visual of visuals) {
    const [start, end] = getNormalizedRange(visual.event, dayStart);
    let added = false;

    for (const group of groups) {
      if (hasOverlap(start, end, group.start, group.end)) {
        group.visuals.push(visual);
        group.end = Math.max(group.end, end);
        added = true;
        break;
      }
    }

    if (!added) {
      groups.push({
        start,
        end,
        visuals: [visual]
      });
    }
  }

  return groups;
}

function getNode(visual, dayStart) {
  const [start, end] = getNormalizedRange(visual.event, dayStart);
  return {
    parent: null,
    sibling: true,
    index: 0,
    visual,
    start,
    end,
    children: []
  };
}

function getMaxChildIndex(node) {
  let max = node.index;

  for (const child of node.children) {
    const childMax = getMaxChildIndex(child);

    if (childMax > max) {
      max = childMax;
    }
  }

  return max;
}

function addTime(identifier, minutes) {
  const removeMinutes = identifier % 100;
  const totalMinutes = removeMinutes + minutes;
  const addHours = Math.floor(totalMinutes / 60);
  const addMinutes = totalMinutes % 60;
  return identifier - removeMinutes + addHours * 100 + addMinutes;
}
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/modes/column.js

const column_FULL_WIDTH = 100;
const column = (events, firstWeekday, overlapThreshold) => {
  const handler = getOverlapGroupHandler(firstWeekday);
  return (day, dayEvents, timed, reset) => {
    const visuals = handler.getVisuals(day, dayEvents, timed, reset);

    if (timed) {
      visuals.forEach(visual => {
        visual.left = visual.column * column_FULL_WIDTH / visual.columnCount;
        visual.width = column_FULL_WIDTH / visual.columnCount;
      });
    }

    return visuals;
  };
};
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/modes/index.js


const CalendarEventOverlapModes = {
  stack: stack,
  column: column
};
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/util/props.js


/* harmony default export */ var props = ({
  base: {
    start: {
      type: [String, Number, Date],
      validate: validateTimestamp,
      default: () => parseDate(new Date()).date
    },
    end: {
      type: [String, Number, Date],
      validate: validateTimestamp
    },
    weekdays: {
      type: [Array, String],
      default: () => [0, 1, 2, 3, 4, 5, 6],
      validate: validateWeekdays
    },
    hideHeader: {
      type: Boolean
    },
    shortWeekdays: {
      type: Boolean,
      default: true
    },
    weekdayFormat: {
      type: Function,
      default: null
    },
    dayFormat: {
      type: Function,
      default: null
    }
  },
  intervals: {
    maxDays: {
      type: Number,
      default: 7
    },
    shortIntervals: {
      type: Boolean,
      default: true
    },
    intervalHeight: {
      type: [Number, String],
      default: 48,
      validate: validateNumber
    },
    intervalWidth: {
      type: [Number, String],
      default: 60,
      validate: validateNumber
    },
    intervalMinutes: {
      type: [Number, String],
      default: 60,
      validate: validateNumber
    },
    firstInterval: {
      type: [Number, String],
      default: 0,
      validate: validateNumber
    },
    firstTime: {
      type: [Number, String, Object],
      validate: validateTime
    },
    intervalCount: {
      type: [Number, String],
      default: 24,
      validate: validateNumber
    },
    intervalFormat: {
      type: Function,
      default: null
    },
    intervalStyle: {
      type: Function,
      default: null
    },
    showIntervalLabel: {
      type: Function,
      default: null
    }
  },
  weeks: {
    localeFirstDayOfYear: {
      type: [String, Number],
      default: 0
    },
    minWeeks: {
      validate: validateNumber,
      default: 1
    },
    shortMonths: {
      type: Boolean,
      default: true
    },
    showMonthOnFirst: {
      type: Boolean,
      default: true
    },
    showWeek: Boolean,
    monthFormat: {
      type: Function,
      default: null
    }
  },
  calendar: {
    type: {
      type: String,
      default: 'month'
    },
    value: {
      type: [String, Number, Date],
      validate: validateTimestamp
    }
  },
  category: {
    categories: {
      type: [Array, String],
      default: ''
    },
    categoryText: {
      type: [String, Function]
    },
    categoryHideDynamic: {
      type: Boolean
    },
    categoryShowAll: {
      type: Boolean
    },
    categoryForInvalid: {
      type: String,
      default: ''
    },
    categoryDays: {
      type: [Number, String],
      default: 1,
      validate: x => isFinite(parseInt(x)) && parseInt(x) > 0
    }
  },
  events: {
    events: {
      type: Array,
      default: () => []
    },
    eventStart: {
      type: String,
      default: 'start'
    },
    eventEnd: {
      type: String,
      default: 'end'
    },
    eventTimed: {
      type: [String, Function],
      default: 'timed'
    },
    eventCategory: {
      type: [String, Function],
      default: 'category'
    },
    eventHeight: {
      type: Number,
      default: 20
    },
    eventColor: {
      type: [String, Function],
      default: 'primary'
    },
    eventTextColor: {
      type: [String, Function],
      default: 'white'
    },
    eventName: {
      type: [String, Function],
      default: 'name'
    },
    eventOverlapThreshold: {
      type: [String, Number],
      default: 60
    },
    eventOverlapMode: {
      type: [String, Function],
      default: 'stack',
      validate: mode => mode in CalendarEventOverlapModes || typeof mode === 'function'
    },
    eventMore: {
      type: Boolean,
      default: true
    },
    eventMoreText: {
      type: String,
      default: '$vuetify.calendar.moreEvents'
    },
    eventRipple: {
      type: [Boolean, Object],
      default: null
    },
    eventMarginBottom: {
      type: Number,
      default: 1
    }
  }
});
function validateNumber(input) {
  return isFinite(parseInt(input));
}
function validateWeekdays(input) {
  if (typeof input === 'string') {
    input = input.split(',');
  }

  if (Array.isArray(input)) {
    const ints = input.map(x => parseInt(x));

    if (ints.length > DAYS_IN_WEEK || ints.length === 0) {
      return false;
    }

    const visited = {};
    let wrapped = false;

    for (let i = 0; i < ints.length; i++) {
      const x = ints[i];

      if (!isFinite(x) || x < 0 || x >= DAYS_IN_WEEK) {
        return false;
      }

      if (i > 0) {
        const d = x - ints[i - 1];

        if (d < 0) {
          if (wrapped) {
            return false;
          }

          wrapped = true;
        } else if (d === 0) {
          return false;
        }
      }

      if (visited[x]) {
        return false;
      }

      visited[x] = true;
    }

    return true;
  }

  return false;
}
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/mixins/calendar-base.js
// Mixins





 // Directives

 // Util



/* harmony default export */ var calendar_base = (Object(mixins["a" /* default */])(colorable["a" /* default */], localable, mouse, themeable["a" /* default */], mixins_times
/* @vue/component */
).extend({
  name: 'calendar-base',
  directives: {
    Resize: resize["a" /* default */]
  },
  props: props.base,
  computed: {
    parsedWeekdays() {
      return Array.isArray(this.weekdays) ? this.weekdays : (this.weekdays || '').split(',').map(x => parseInt(x, 10));
    },

    weekdaySkips() {
      return getWeekdaySkips(this.parsedWeekdays);
    },

    weekdaySkipsReverse() {
      const reversed = this.weekdaySkips.slice();
      reversed.reverse();
      return reversed;
    },

    parsedStart() {
      return parseTimestamp(this.start, true);
    },

    parsedEnd() {
      const start = this.parsedStart;
      const end = this.end ? parseTimestamp(this.end) || start : start;
      return getTimestampIdentifier(end) < getTimestampIdentifier(start) ? start : end;
    },

    days() {
      return createDayList(this.parsedStart, this.parsedEnd, this.times.today, this.weekdaySkips);
    },

    dayFormatter() {
      if (this.dayFormat) {
        return this.dayFormat;
      }

      const options = {
        timeZone: 'UTC',
        day: 'numeric'
      };
      return createNativeLocaleFormatter(this.currentLocale, (_tms, _short) => options);
    },

    weekdayFormatter() {
      if (this.weekdayFormat) {
        return this.weekdayFormat;
      }

      const longOptions = {
        timeZone: 'UTC',
        weekday: 'long'
      };
      const shortOptions = {
        timeZone: 'UTC',
        weekday: 'short'
      };
      return createNativeLocaleFormatter(this.currentLocale, (_tms, short) => short ? shortOptions : longOptions);
    }

  },
  methods: {
    getRelativeClasses(timestamp, outside = false) {
      return {
        'v-present': timestamp.present,
        'v-past': timestamp.past,
        'v-future': timestamp.future,
        'v-outside': outside
      };
    },

    getStartOfWeek(timestamp) {
      return getStartOfWeek(timestamp, this.parsedWeekdays, this.times.today);
    },

    getEndOfWeek(timestamp) {
      return getEndOfWeek(timestamp, this.parsedWeekdays, this.times.today);
    },

    getFormatter(options) {
      return createNativeLocaleFormatter(this.locale, (_tms, _short) => options);
    }

  }
}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/helpers.js
var helpers = __webpack_require__(0);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/util/events.js

function parseEvent(input, index, startProperty, endProperty, timed = false, category = false) {
  const startInput = input[startProperty];
  const endInput = input[endProperty];
  const startParsed = parseTimestamp(startInput, true);
  const endParsed = endInput ? parseTimestamp(endInput, true) : startParsed;
  const start = isTimedless(startInput) ? updateHasTime(startParsed, timed) : startParsed;
  const end = isTimedless(endInput) ? updateHasTime(endParsed, timed) : endParsed;
  const startIdentifier = getDayIdentifier(start);
  const startTimestampIdentifier = getTimestampIdentifier(start);
  const endIdentifier = getDayIdentifier(end);
  const endOffset = start.hasTime ? 0 : 2359;
  const endTimestampIdentifier = getTimestampIdentifier(end) + endOffset;
  const allDay = !start.hasTime;
  return {
    input,
    start,
    startIdentifier,
    startTimestampIdentifier,
    end,
    endIdentifier,
    endTimestampIdentifier,
    allDay,
    index,
    category
  };
}
function isEventOn(event, dayIdentifier) {
  return dayIdentifier >= event.startIdentifier && dayIdentifier <= event.endIdentifier && dayIdentifier * OFFSET_TIME !== event.endTimestampIdentifier;
}
function isEventStart(event, day, dayIdentifier, firstWeekday) {
  return dayIdentifier === event.startIdentifier || firstWeekday === day.weekday && isEventOn(event, dayIdentifier);
}
function isEventOverlapping(event, startIdentifier, endIdentifier) {
  return startIdentifier <= event.endIdentifier && endIdentifier >= event.startIdentifier;
}
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/mixins/calendar-with-events.js
// Styles
 // Directives

 // Mixins

 // Helpers

 // Util





const WIDTH_FULL = 100;
const WIDTH_START = 95;
const calendar_with_events_MINUTES_IN_DAY = 1440;
/* @vue/component */

/* harmony default export */ var mixins_calendar_with_events = (calendar_base.extend({
  name: 'calendar-with-events',
  directives: {
    ripple: ripple["a" /* default */]
  },
  props: { ...props.events,
    ...props.calendar,
    ...props.category
  },
  computed: {
    noEvents() {
      return this.events.length === 0;
    },

    parsedEvents() {
      return this.events.map(this.parseEvent);
    },

    parsedEventOverlapThreshold() {
      return parseInt(this.eventOverlapThreshold);
    },

    eventTimedFunction() {
      return typeof this.eventTimed === 'function' ? this.eventTimed : event => !!event[this.eventTimed];
    },

    eventCategoryFunction() {
      return typeof this.eventCategory === 'function' ? this.eventCategory : event => event[this.eventCategory];
    },

    eventTextColorFunction() {
      return typeof this.eventTextColor === 'function' ? this.eventTextColor : () => this.eventTextColor;
    },

    eventNameFunction() {
      return typeof this.eventName === 'function' ? this.eventName : (event, timedEvent) => Object(helpers["i" /* escapeHTML */])(event.input[this.eventName] || '');
    },

    eventModeFunction() {
      return typeof this.eventOverlapMode === 'function' ? this.eventOverlapMode : CalendarEventOverlapModes[this.eventOverlapMode];
    },

    eventWeekdays() {
      return this.parsedWeekdays;
    },

    categoryMode() {
      return this.type === 'category';
    }

  },
  methods: {
    eventColorFunction(e) {
      return typeof this.eventColor === 'function' ? this.eventColor(e) : e.color || this.eventColor;
    },

    parseEvent(input, index = 0) {
      return parseEvent(input, index, this.eventStart, this.eventEnd, this.eventTimedFunction(input), this.categoryMode ? this.eventCategoryFunction(input) : false);
    },

    formatTime(withTime, ampm) {
      const formatter = this.getFormatter({
        timeZone: 'UTC',
        hour: 'numeric',
        minute: withTime.minute > 0 ? 'numeric' : undefined
      });
      return formatter(withTime, true);
    },

    updateEventVisibility() {
      if (this.noEvents || !this.eventMore) {
        return;
      }

      const eventHeight = this.eventHeight;
      const eventsMap = this.getEventsMap();

      for (const date in eventsMap) {
        const {
          parent,
          events,
          more
        } = eventsMap[date];

        if (!more) {
          break;
        }

        const parentBounds = parent.getBoundingClientRect();
        const last = events.length - 1;
        let hide = false;
        let hidden = 0;

        for (let i = 0; i <= last; i++) {
          if (!hide) {
            const eventBounds = events[i].getBoundingClientRect();
            hide = i === last ? eventBounds.bottom > parentBounds.bottom : eventBounds.bottom + eventHeight > parentBounds.bottom;
          }

          if (hide) {
            events[i].style.display = 'none';
            hidden++;
          }
        }

        if (hide) {
          more.style.display = '';
          more.innerHTML = this.$vuetify.lang.t(this.eventMoreText, hidden);
        } else {
          more.style.display = 'none';
        }
      }
    },

    getEventsMap() {
      const eventsMap = {};
      const elements = this.$refs.events;

      if (!elements || !elements.forEach) {
        return eventsMap;
      }

      elements.forEach(el => {
        const date = el.getAttribute('data-date');

        if (el.parentElement && date) {
          if (!(date in eventsMap)) {
            eventsMap[date] = {
              parent: el.parentElement,
              more: null,
              events: []
            };
          }

          if (el.getAttribute('data-more')) {
            eventsMap[date].more = el;
          } else {
            eventsMap[date].events.push(el);
            el.style.display = '';
          }
        }
      });
      return eventsMap;
    },

    genDayEvent({
      event
    }, day) {
      const eventHeight = this.eventHeight;
      const eventMarginBottom = this.eventMarginBottom;
      const dayIdentifier = getDayIdentifier(day);
      const week = day.week;
      const start = dayIdentifier === event.startIdentifier;
      let end = dayIdentifier === event.endIdentifier;
      let width = WIDTH_START;

      if (!this.categoryMode) {
        for (let i = day.index + 1; i < week.length; i++) {
          const weekdayIdentifier = getDayIdentifier(week[i]);

          if (event.endIdentifier >= weekdayIdentifier) {
            width += WIDTH_FULL;
            end = end || weekdayIdentifier === event.endIdentifier;
          } else {
            end = true;
            break;
          }
        }
      }

      const scope = {
        eventParsed: event,
        day,
        start,
        end,
        timed: false
      };
      return this.genEvent(event, scope, false, {
        staticClass: 'v-event',
        class: {
          'v-event-start': start,
          'v-event-end': end
        },
        style: {
          height: `${eventHeight}px`,
          width: `${width}%`,
          'margin-bottom': `${eventMarginBottom}px`
        },
        attrs: {
          'data-date': day.date
        },
        key: event.index,
        ref: 'events',
        refInFor: true
      });
    },

    genTimedEvent({
      event,
      left,
      width
    }, day) {
      if (day.timeDelta(event.end) <= 0 || day.timeDelta(event.start) >= 1) {
        return false;
      }

      const dayIdentifier = getDayIdentifier(day);
      const start = event.startIdentifier >= dayIdentifier;
      const end = event.endIdentifier > dayIdentifier;
      const top = start ? day.timeToY(event.start) : 0;
      const bottom = end ? day.timeToY(calendar_with_events_MINUTES_IN_DAY) : day.timeToY(event.end);
      const height = Math.max(this.eventHeight, bottom - top);
      const scope = {
        eventParsed: event,
        day,
        start,
        end,
        timed: true
      };
      return this.genEvent(event, scope, true, {
        staticClass: 'v-event-timed',
        style: {
          top: `${top}px`,
          height: `${height}px`,
          left: `${left}%`,
          width: `${width}%`
        }
      });
    },

    genEvent(event, scopeInput, timedEvent, data) {
      var _this$eventRipple;

      const slot = this.$scopedSlots.event;
      const text = this.eventTextColorFunction(event.input);
      const background = this.eventColorFunction(event.input);
      const overlapsNoon = event.start.hour < 12 && event.end.hour >= 12;
      const singline = diffMinutes(event.start, event.end) <= this.parsedEventOverlapThreshold;
      const formatTime = this.formatTime;

      const timeSummary = () => formatTime(event.start, overlapsNoon) + ' - ' + formatTime(event.end, true);

      const eventSummary = () => {
        const name = this.eventNameFunction(event, timedEvent);

        if (event.start.hasTime) {
          if (timedEvent) {
            const time = timeSummary();
            const delimiter = singline ? ', ' : '<br>';
            return `<strong>${name}</strong>${delimiter}${time}`;
          } else {
            const time = formatTime(event.start, true);
            return `<strong>${time}</strong> ${name}`;
          }
        }

        return name;
      };

      const scope = { ...scopeInput,
        event: event.input,
        outside: scopeInput.day.outside,
        singline,
        overlapsNoon,
        formatTime,
        timeSummary,
        eventSummary
      };
      return this.$createElement('div', this.setTextColor(text, this.setBackgroundColor(background, {
        on: this.getDefaultMouseEventHandlers(':event', nativeEvent => ({ ...scope,
          nativeEvent
        })),
        directives: [{
          name: 'ripple',
          value: (_this$eventRipple = this.eventRipple) != null ? _this$eventRipple : true
        }],
        ...data
      })), slot ? slot(scope) : [this.genName(eventSummary)]);
    },

    genName(eventSummary) {
      return this.$createElement('div', {
        staticClass: 'pl-1',
        domProps: {
          innerHTML: eventSummary()
        }
      });
    },

    genPlaceholder(day) {
      const height = this.eventHeight + this.eventMarginBottom;
      return this.$createElement('div', {
        style: {
          height: `${height}px`
        },
        attrs: {
          'data-date': day.date
        },
        ref: 'events',
        refInFor: true
      });
    },

    genMore(day) {
      var _this$eventRipple2;

      const eventHeight = this.eventHeight;
      const eventMarginBottom = this.eventMarginBottom;
      return this.$createElement('div', {
        staticClass: 'v-event-more pl-1',
        class: {
          'v-outside': day.outside
        },
        attrs: {
          'data-date': day.date,
          'data-more': 1
        },
        directives: [{
          name: 'ripple',
          value: (_this$eventRipple2 = this.eventRipple) != null ? _this$eventRipple2 : true
        }],
        on: {
          click: () => this.$emit('click:more', day)
        },
        style: {
          display: 'none',
          height: `${eventHeight}px`,
          'margin-bottom': `${eventMarginBottom}px`
        },
        ref: 'events',
        refInFor: true
      });
    },

    getVisibleEvents() {
      const start = getDayIdentifier(this.days[0]);
      const end = getDayIdentifier(this.days[this.days.length - 1]);
      return this.parsedEvents.filter(event => isEventOverlapping(event, start, end));
    },

    isEventForCategory(event, category) {
      return !this.categoryMode || typeof category === 'object' && category.categoryName && category.categoryName === event.category || typeof event.category !== 'string' && category === null;
    },

    getEventsForDay(day) {
      const identifier = getDayIdentifier(day);
      const firstWeekday = this.eventWeekdays[0];
      return this.parsedEvents.filter(event => isEventStart(event, day, identifier, firstWeekday));
    },

    getEventsForDayAll(day) {
      const identifier = getDayIdentifier(day);
      const firstWeekday = this.eventWeekdays[0];
      return this.parsedEvents.filter(event => event.allDay && (this.categoryMode ? isEventOn(event, identifier) : isEventStart(event, day, identifier, firstWeekday)) && this.isEventForCategory(event, day.category));
    },

    getEventsForDayTimed(day) {
      const identifier = getDayIdentifier(day);
      return this.parsedEvents.filter(event => !event.allDay && isEventOn(event, identifier) && this.isEventForCategory(event, day.category));
    },

    getScopedSlots() {
      if (this.noEvents) {
        return { ...this.$scopedSlots
        };
      }

      const mode = this.eventModeFunction(this.parsedEvents, this.eventWeekdays[0], this.parsedEventOverlapThreshold);

      const isNode = input => !!input;

      const getSlotChildren = (day, getter, mapper, timed) => {
        const events = getter(day);
        const visuals = mode(day, events, timed, this.categoryMode);

        if (timed) {
          return visuals.map(visual => mapper(visual, day)).filter(isNode);
        }

        const children = [];
        visuals.forEach((visual, index) => {
          while (children.length < visual.column) {
            children.push(this.genPlaceholder(day));
          }

          const mapped = mapper(visual, day);

          if (mapped) {
            children.push(mapped);
          }
        });
        return children;
      };

      const slots = this.$scopedSlots;
      const slotDay = slots.day;
      const slotDayHeader = slots['day-header'];
      const slotDayBody = slots['day-body'];
      return { ...slots,
        day: day => {
          let children = getSlotChildren(day, this.getEventsForDay, this.genDayEvent, false);

          if (children && children.length > 0 && this.eventMore) {
            children.push(this.genMore(day));
          }

          if (slotDay) {
            const slot = slotDay(day);

            if (slot) {
              children = children ? children.concat(slot) : slot;
            }
          }

          return children;
        },
        'day-header': day => {
          let children = getSlotChildren(day, this.getEventsForDayAll, this.genDayEvent, false);

          if (slotDayHeader) {
            const slot = slotDayHeader(day);

            if (slot) {
              children = children ? children.concat(slot) : slot;
            }
          }

          return children;
        },
        'day-body': day => {
          const events = getSlotChildren(day, this.getEventsForDayTimed, this.genTimedEvent, true);
          let children = [this.$createElement('div', {
            staticClass: 'v-event-timed-container'
          }, events)];

          if (slotDayBody) {
            const slot = slotDayBody(day);

            if (slot) {
              children = children.concat(slot);
            }
          }

          return children;
        }
      };
    }

  }
}));
// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VCalendar/VCalendarWeekly.sass
var VCalendarWeekly = __webpack_require__(977);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/index.js
var VBtn = __webpack_require__(27);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/VCalendarWeekly.js
// Styles
 // Components

 // Mixins

 // Util





/* @vue/component */

/* harmony default export */ var VCalendar_VCalendarWeekly = (calendar_base.extend({
  name: 'v-calendar-weekly',
  props: props.weeks,
  computed: {
    staticClass() {
      return 'v-calendar-weekly';
    },

    classes() {
      return this.themeClasses;
    },

    parsedMinWeeks() {
      return parseInt(this.minWeeks);
    },

    days() {
      const minDays = this.parsedMinWeeks * this.parsedWeekdays.length;
      const start = this.getStartOfWeek(this.parsedStart);
      const end = this.getEndOfWeek(this.parsedEnd);
      return createDayList(start, end, this.times.today, this.weekdaySkips, Number.MAX_SAFE_INTEGER, minDays);
    },

    todayWeek() {
      const today = this.times.today;
      const start = this.getStartOfWeek(today);
      const end = this.getEndOfWeek(today);
      return createDayList(start, end, today, this.weekdaySkips, this.parsedWeekdays.length, this.parsedWeekdays.length);
    },

    monthFormatter() {
      if (this.monthFormat) {
        return this.monthFormat;
      }

      const longOptions = {
        timeZone: 'UTC',
        month: 'long'
      };
      const shortOptions = {
        timeZone: 'UTC',
        month: 'short'
      };
      return createNativeLocaleFormatter(this.currentLocale, (_tms, short) => short ? shortOptions : longOptions);
    }

  },
  methods: {
    isOutside(day) {
      const dayIdentifier = getDayIdentifier(day);
      return dayIdentifier < getDayIdentifier(this.parsedStart) || dayIdentifier > getDayIdentifier(this.parsedEnd);
    },

    genHead() {
      return this.$createElement('div', {
        staticClass: 'v-calendar-weekly__head'
      }, this.genHeadDays());
    },

    genHeadDays() {
      const header = this.todayWeek.map(this.genHeadDay);

      if (this.showWeek) {
        header.unshift(this.$createElement('div', {
          staticClass: 'v-calendar-weekly__head-weeknumber'
        }));
      }

      return header;
    },

    genHeadDay(day, index) {
      const outside = this.isOutside(this.days[index]);
      const color = day.present ? this.color : undefined;
      return this.$createElement('div', this.setTextColor(color, {
        key: day.date,
        staticClass: 'v-calendar-weekly__head-weekday',
        class: this.getRelativeClasses(day, outside)
      }), this.weekdayFormatter(day, this.shortWeekdays));
    },

    genWeeks() {
      const days = this.days;
      const weekDays = this.parsedWeekdays.length;
      const weeks = [];

      for (let i = 0; i < days.length; i += weekDays) {
        weeks.push(this.genWeek(days.slice(i, i + weekDays), this.getWeekNumber(days[i])));
      }

      return weeks;
    },

    genWeek(week, weekNumber) {
      const weekNodes = week.map((day, index) => this.genDay(day, index, week));

      if (this.showWeek) {
        weekNodes.unshift(this.genWeekNumber(weekNumber));
      }

      return this.$createElement('div', {
        key: week[0].date,
        staticClass: 'v-calendar-weekly__week'
      }, weekNodes);
    },

    getWeekNumber(determineDay) {
      return weekNumber(determineDay.year, determineDay.month - 1, determineDay.day, this.parsedWeekdays[0], parseInt(this.localeFirstDayOfYear));
    },

    genWeekNumber(weekNumber) {
      return this.$createElement('div', {
        staticClass: 'v-calendar-weekly__weeknumber'
      }, [this.$createElement('small', String(weekNumber))]);
    },

    genDay(day, index, week) {
      const outside = this.isOutside(day);
      return this.$createElement('div', {
        key: day.date,
        staticClass: 'v-calendar-weekly__day',
        class: this.getRelativeClasses(day, outside),
        on: this.getDefaultMouseEventHandlers(':day', _e => day)
      }, [this.genDayLabel(day), ...(Object(helpers["n" /* getSlot */])(this, 'day', () => ({
        outside,
        index,
        week,
        ...day
      })) || [])]);
    },

    genDayLabel(day) {
      return this.$createElement('div', {
        staticClass: 'v-calendar-weekly__day-label'
      }, Object(helpers["n" /* getSlot */])(this, 'day-label', day) || [this.genDayLabelButton(day)]);
    },

    genDayLabelButton(day) {
      const color = day.present ? this.color : 'transparent';
      const hasMonth = day.day === 1 && this.showMonthOnFirst;
      return this.$createElement(VBtn["a" /* default */], {
        props: {
          color,
          fab: true,
          depressed: true,
          small: true
        },
        on: this.getMouseEventHandlers({
          'click:date': {
            event: 'click',
            stop: true
          },
          'contextmenu:date': {
            event: 'contextmenu',
            stop: true,
            prevent: true,
            result: false
          }
        }, _e => day)
      }, hasMonth ? this.monthFormatter(day, this.shortMonths) + ' ' + this.dayFormatter(day, false) : this.dayFormatter(day, false));
    },

    genDayMonth(day) {
      const color = day.present ? this.color : undefined;
      return this.$createElement('div', this.setTextColor(color, {
        staticClass: 'v-calendar-weekly__day-month'
      }), Object(helpers["n" /* getSlot */])(this, 'day-month', day) || this.monthFormatter(day, this.shortMonths));
    }

  },

  render(h) {
    return h('div', {
      staticClass: this.staticClass,
      class: this.classes,
      on: {
        dragstart: e => {
          e.preventDefault();
        }
      }
    }, [!this.hideHeader ? this.genHead() : '', ...this.genWeeks()]);
  }

}));
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/VCalendarMonthly.js
// Styles
 // Mixins

 // Util


/* @vue/component */

/* harmony default export */ var VCalendarMonthly = (VCalendar_VCalendarWeekly.extend({
  name: 'v-calendar-monthly',
  computed: {
    staticClass() {
      return 'v-calendar-monthly v-calendar-weekly';
    },

    parsedStart() {
      return getStartOfMonth(parseTimestamp(this.start, true));
    },

    parsedEnd() {
      return getEndOfMonth(parseTimestamp(this.end, true));
    }

  }
}));
// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VCalendar/VCalendarDaily.sass
var VCalendarDaily = __webpack_require__(1037);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/mixins/calendar-with-intervals.js
// Mixins
 // Util



/* @vue/component */

/* harmony default export */ var calendar_with_intervals = (calendar_base.extend({
  name: 'calendar-with-intervals',
  props: props.intervals,
  computed: {
    parsedFirstInterval() {
      return parseInt(this.firstInterval);
    },

    parsedIntervalMinutes() {
      return parseInt(this.intervalMinutes);
    },

    parsedIntervalCount() {
      return parseInt(this.intervalCount);
    },

    parsedIntervalHeight() {
      return parseFloat(this.intervalHeight);
    },

    parsedFirstTime() {
      return parseTime(this.firstTime);
    },

    firstMinute() {
      const time = this.parsedFirstTime;
      return time !== false && time >= 0 && time <= MINUTES_IN_DAY ? time : this.parsedFirstInterval * this.parsedIntervalMinutes;
    },

    bodyHeight() {
      return this.parsedIntervalCount * this.parsedIntervalHeight;
    },

    days() {
      return createDayList(this.parsedStart, this.parsedEnd, this.times.today, this.weekdaySkips, this.maxDays);
    },

    intervals() {
      const days = this.days;
      const first = this.firstMinute;
      const minutes = this.parsedIntervalMinutes;
      const count = this.parsedIntervalCount;
      const now = this.times.now;
      return days.map(d => createIntervalList(d, first, minutes, count, now));
    },

    intervalFormatter() {
      if (this.intervalFormat) {
        return this.intervalFormat;
      }

      const longOptions = {
        timeZone: 'UTC',
        hour: '2-digit',
        minute: '2-digit'
      };
      const shortOptions = {
        timeZone: 'UTC',
        hour: 'numeric',
        minute: '2-digit'
      };
      const shortHourOptions = {
        timeZone: 'UTC',
        hour: 'numeric'
      };
      return createNativeLocaleFormatter(this.currentLocale, (tms, short) => short ? tms.minute === 0 ? shortHourOptions : shortOptions : longOptions);
    }

  },
  methods: {
    showIntervalLabelDefault(interval) {
      const first = this.intervals[0][0];
      const isFirst = first.hour === interval.hour && first.minute === interval.minute;
      return !isFirst;
    },

    intervalStyleDefault(_interval) {
      return undefined;
    },

    getTimestampAtEvent(e, day) {
      const timestamp = copyTimestamp(day);
      const bounds = e.currentTarget.getBoundingClientRect();
      const baseMinutes = this.firstMinute;
      const touchEvent = e;
      const mouseEvent = e;
      const touches = touchEvent.changedTouches || touchEvent.touches;
      const clientY = touches && touches[0] ? touches[0].clientY : mouseEvent.clientY;
      const addIntervals = (clientY - bounds.top) / this.parsedIntervalHeight;
      const addMinutes = Math.floor(addIntervals * this.parsedIntervalMinutes);
      const minutes = baseMinutes + addMinutes;
      return updateMinutes(timestamp, minutes, this.times.now);
    },

    getSlotScope(timestamp) {
      const scope = copyTimestamp(timestamp);
      scope.timeToY = this.timeToY;
      scope.timeDelta = this.timeDelta;
      scope.minutesToPixels = this.minutesToPixels;
      scope.week = this.days;
      return scope;
    },

    scrollToTime(time) {
      const y = this.timeToY(time);
      const pane = this.$refs.scrollArea;

      if (y === false || !pane) {
        return false;
      }

      pane.scrollTop = y;
      return true;
    },

    minutesToPixels(minutes) {
      return minutes / this.parsedIntervalMinutes * this.parsedIntervalHeight;
    },

    timeToY(time, clamp = true) {
      let y = this.timeDelta(time);

      if (y !== false) {
        y *= this.bodyHeight;

        if (clamp) {
          if (y < 0) {
            y = 0;
          }

          if (y > this.bodyHeight) {
            y = this.bodyHeight;
          }
        }
      }

      return y;
    },

    timeDelta(time) {
      const minutes = parseTime(time);

      if (minutes === false) {
        return false;
      }

      const min = this.firstMinute;
      const gap = this.parsedIntervalCount * this.parsedIntervalMinutes;
      return (minutes - min) / gap;
    }

  }
}));
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/VCalendarDaily.js
// Styles
 // Directives

 // Components

 // Mixins

 // Util


/* @vue/component */

/* harmony default export */ var VCalendar_VCalendarDaily = (calendar_with_intervals.extend({
  name: 'v-calendar-daily',
  directives: {
    Resize: resize["a" /* default */]
  },
  data: () => ({
    scrollPush: 0
  }),
  computed: {
    classes() {
      return {
        'v-calendar-daily': true,
        ...this.themeClasses
      };
    }

  },

  mounted() {
    this.init();
  },

  methods: {
    init() {
      this.$nextTick(this.onResize);
    },

    onResize() {
      this.scrollPush = this.getScrollPush();
    },

    getScrollPush() {
      const area = this.$refs.scrollArea;
      const pane = this.$refs.pane;
      return area && pane ? area.offsetWidth - pane.offsetWidth : 0;
    },

    genHead() {
      return this.$createElement('div', {
        staticClass: 'v-calendar-daily__head',
        style: {
          marginRight: this.scrollPush + 'px'
        }
      }, [this.genHeadIntervals(), ...this.genHeadDays()]);
    },

    genHeadIntervals() {
      const width = Object(helpers["f" /* convertToUnit */])(this.intervalWidth);
      return this.$createElement('div', {
        staticClass: 'v-calendar-daily__intervals-head',
        style: {
          width
        }
      }, Object(helpers["n" /* getSlot */])(this, 'interval-header'));
    },

    genHeadDays() {
      return this.days.map(this.genHeadDay);
    },

    genHeadDay(day, index) {
      return this.$createElement('div', {
        key: day.date,
        staticClass: 'v-calendar-daily_head-day',
        class: this.getRelativeClasses(day),
        on: this.getDefaultMouseEventHandlers(':day', _e => {
          return this.getSlotScope(day);
        })
      }, [this.genHeadWeekday(day), this.genHeadDayLabel(day), ...this.genDayHeader(day, index)]);
    },

    genDayHeader(day, index) {
      return Object(helpers["n" /* getSlot */])(this, 'day-header', () => ({
        week: this.days,
        ...day,
        index
      })) || [];
    },

    genHeadWeekday(day) {
      const color = day.present ? this.color : undefined;
      return this.$createElement('div', this.setTextColor(color, {
        staticClass: 'v-calendar-daily_head-weekday'
      }), this.weekdayFormatter(day, this.shortWeekdays));
    },

    genHeadDayLabel(day) {
      return this.$createElement('div', {
        staticClass: 'v-calendar-daily_head-day-label'
      }, Object(helpers["n" /* getSlot */])(this, 'day-label-header', day) || [this.genHeadDayButton(day)]);
    },

    genHeadDayButton(day) {
      const color = day.present ? this.color : 'transparent';
      return this.$createElement(VBtn["a" /* default */], {
        props: {
          color,
          fab: true,
          depressed: true
        },
        on: this.getMouseEventHandlers({
          'click:date': {
            event: 'click',
            stop: true
          },
          'contextmenu:date': {
            event: 'contextmenu',
            stop: true,
            prevent: true,
            result: false
          }
        }, _e => {
          return day;
        })
      }, this.dayFormatter(day, false));
    },

    genBody() {
      return this.$createElement('div', {
        staticClass: 'v-calendar-daily__body'
      }, [this.genScrollArea()]);
    },

    genScrollArea() {
      return this.$createElement('div', {
        ref: 'scrollArea',
        staticClass: 'v-calendar-daily__scroll-area'
      }, [this.genPane()]);
    },

    genPane() {
      return this.$createElement('div', {
        ref: 'pane',
        staticClass: 'v-calendar-daily__pane',
        style: {
          height: Object(helpers["f" /* convertToUnit */])(this.bodyHeight)
        }
      }, [this.genDayContainer()]);
    },

    genDayContainer() {
      return this.$createElement('div', {
        staticClass: 'v-calendar-daily__day-container'
      }, [this.genBodyIntervals(), ...this.genDays()]);
    },

    genDays() {
      return this.days.map(this.genDay);
    },

    genDay(day, index) {
      return this.$createElement('div', {
        key: day.date,
        staticClass: 'v-calendar-daily__day',
        class: this.getRelativeClasses(day),
        on: this.getDefaultMouseEventHandlers(':time', e => {
          return this.getSlotScope(this.getTimestampAtEvent(e, day));
        })
      }, [...this.genDayIntervals(index), ...this.genDayBody(day)]);
    },

    genDayBody(day) {
      return Object(helpers["n" /* getSlot */])(this, 'day-body', () => this.getSlotScope(day)) || [];
    },

    genDayIntervals(index) {
      return this.intervals[index].map(this.genDayInterval);
    },

    genDayInterval(interval) {
      const height = Object(helpers["f" /* convertToUnit */])(this.intervalHeight);
      const styler = this.intervalStyle || this.intervalStyleDefault;
      const data = {
        key: interval.time,
        staticClass: 'v-calendar-daily__day-interval',
        style: {
          height,
          ...styler(interval)
        }
      };
      const children = Object(helpers["n" /* getSlot */])(this, 'interval', () => this.getSlotScope(interval));
      return this.$createElement('div', data, children);
    },

    genBodyIntervals() {
      const width = Object(helpers["f" /* convertToUnit */])(this.intervalWidth);
      const data = {
        staticClass: 'v-calendar-daily__intervals-body',
        style: {
          width
        },
        on: this.getDefaultMouseEventHandlers(':interval', e => {
          return this.getTimestampAtEvent(e, this.parsedStart);
        })
      };
      return this.$createElement('div', data, this.genIntervalLabels());
    },

    genIntervalLabels() {
      if (!this.intervals.length) return null;
      return this.intervals[0].map(this.genIntervalLabel);
    },

    genIntervalLabel(interval) {
      const height = Object(helpers["f" /* convertToUnit */])(this.intervalHeight);
      const short = this.shortIntervals;
      const shower = this.showIntervalLabel || this.showIntervalLabelDefault;
      const show = shower(interval);
      const label = show ? this.intervalFormatter(interval, short) : undefined;
      return this.$createElement('div', {
        key: interval.time,
        staticClass: 'v-calendar-daily__interval',
        style: {
          height
        }
      }, [this.$createElement('div', {
        staticClass: 'v-calendar-daily__interval-text'
      }, label)]);
    }

  },

  render(h) {
    return h('div', {
      class: this.classes,
      on: {
        dragstart: e => {
          e.preventDefault();
        }
      },
      directives: [{
        modifiers: {
          quiet: true
        },
        name: 'resize',
        value: this.onResize
      }]
    }, [!this.hideHeader ? this.genHead() : '', this.genBody()]);
  }

}));
// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VCalendar/VCalendarCategory.sass
var VCalendarCategory = __webpack_require__(1039);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/util/parser.js
function parsedCategoryText(category, categoryText) {
  return typeof categoryText === 'string' && typeof category === 'object' && category ? category[categoryText] : typeof categoryText === 'function' ? categoryText(category) : category;
}
function getParsedCategories(categories, categoryText) {
  if (typeof categories === 'string') return categories.split(/\s*,\s/);

  if (Array.isArray(categories)) {
    return categories.map(category => {
      if (typeof category === 'string') return {
        categoryName: category
      };
      const categoryName = typeof category.categoryName === 'string' ? category.categoryName : parsedCategoryText(category, categoryText);
      return { ...category,
        categoryName
      };
    });
  }

  return [];
}
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/VCalendarCategory.js
// Styles
 // Mixins

 // Util




/* @vue/component */

/* harmony default export */ var VCalendar_VCalendarCategory = (VCalendar_VCalendarDaily.extend({
  name: 'v-calendar-category',
  props: props.category,
  computed: {
    classes() {
      return {
        'v-calendar-daily': true,
        'v-calendar-category': true,
        ...this.themeClasses
      };
    },

    parsedCategories() {
      return getParsedCategories(this.categories, this.categoryText);
    }

  },
  methods: {
    genDayHeader(day, index) {
      const data = {
        staticClass: 'v-calendar-category__columns'
      };
      const scope = {
        week: this.days,
        ...day,
        index
      };
      const children = this.parsedCategories.map(category => {
        return this.genDayHeaderCategory(day, this.getCategoryScope(scope, category));
      });
      return [this.$createElement('div', data, children)];
    },

    getCategoryScope(scope, category) {
      const cat = typeof category === 'object' && category && category.categoryName === this.categoryForInvalid ? null : category;
      return { ...scope,
        category: cat
      };
    },

    genDayHeaderCategory(day, scope) {
      return this.$createElement('div', {
        staticClass: 'v-calendar-category__column-header',
        on: this.getDefaultMouseEventHandlers(':day-category', e => {
          return this.getCategoryScope(this.getSlotScope(day), scope.category);
        })
      }, [Object(helpers["n" /* getSlot */])(this, 'category', scope) || this.genDayHeaderCategoryTitle(scope.category && scope.category.categoryName), Object(helpers["n" /* getSlot */])(this, 'day-header', scope)]);
    },

    genDayHeaderCategoryTitle(categoryName) {
      return this.$createElement('div', {
        staticClass: 'v-calendar-category__category'
      }, categoryName === null ? this.categoryForInvalid : categoryName);
    },

    genDays() {
      const days = [];
      this.days.forEach(d => {
        const day = new Array(this.parsedCategories.length || 1);
        day.fill(d);
        days.push(...day.map((v, i) => this.genDay(v, 0, i)));
      });
      return days;
    },

    genDay(day, index, categoryIndex) {
      const category = this.parsedCategories[categoryIndex];
      return this.$createElement('div', {
        key: day.date + '-' + categoryIndex,
        staticClass: 'v-calendar-daily__day',
        class: this.getRelativeClasses(day),
        on: this.getDefaultMouseEventHandlers(':time', e => {
          return this.getSlotScope(this.getTimestampAtEvent(e, day));
        })
      }, [...this.genDayIntervals(index, category), ...this.genDayBody(day, category)]);
    },

    genDayIntervals(index, category) {
      return this.intervals[index].map(v => this.genDayInterval(v, category));
    },

    genDayInterval(interval, category) {
      const height = Object(helpers["f" /* convertToUnit */])(this.intervalHeight);
      const styler = this.intervalStyle || this.intervalStyleDefault;
      const data = {
        key: interval.time,
        staticClass: 'v-calendar-daily__day-interval',
        style: {
          height,
          ...styler({ ...interval,
            category
          })
        }
      };
      const children = Object(helpers["n" /* getSlot */])(this, 'interval', () => this.getCategoryScope(this.getSlotScope(interval), category));
      return this.$createElement('div', data, children);
    },

    genDayBody(day, category) {
      const data = {
        staticClass: 'v-calendar-category__columns'
      };
      const children = [this.genDayBodyCategory(day, category)];
      return [this.$createElement('div', data, children)];
    },

    genDayBodyCategory(day, category) {
      const data = {
        staticClass: 'v-calendar-category__column',
        on: this.getDefaultMouseEventHandlers(':time-category', e => {
          return this.getCategoryScope(this.getSlotScope(this.getTimestampAtEvent(e, day)), category);
        })
      };
      const children = Object(helpers["n" /* getSlot */])(this, 'day-body', () => this.getCategoryScope(this.getSlotScope(day), category));
      return this.$createElement('div', data, children);
    }

  }
}));
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCalendar/VCalendar.js
// Styles
// import '../../stylus/components/_calendar-daily.styl'
// Mixins
 // Util


 // Calendars






/* @vue/component */

/* harmony default export */ var VCalendar = (mixins_calendar_with_events.extend({
  name: 'v-calendar',
  props: { ...props.calendar,
    ...props.weeks,
    ...props.intervals,
    ...props.category
  },
  data: () => ({
    lastStart: null,
    lastEnd: null
  }),
  computed: {
    parsedValue() {
      return validateTimestamp(this.value) ? parseTimestamp(this.value, true) : this.parsedStart || this.times.today;
    },

    parsedCategoryDays() {
      return parseInt(this.categoryDays) || 1;
    },

    renderProps() {
      const around = this.parsedValue;
      let component = null;
      let maxDays = this.maxDays;
      let weekdays = this.parsedWeekdays;
      let categories = this.parsedCategories;
      let start = around;
      let end = around;

      switch (this.type) {
        case 'month':
          component = VCalendarMonthly;
          start = getStartOfMonth(around);
          end = getEndOfMonth(around);
          break;

        case 'week':
          component = VCalendar_VCalendarDaily;
          start = this.getStartOfWeek(around);
          end = this.getEndOfWeek(around);
          maxDays = 7;
          break;

        case 'day':
          component = VCalendar_VCalendarDaily;
          maxDays = 1;
          weekdays = [start.weekday];
          break;

        case '4day':
          component = VCalendar_VCalendarDaily;
          end = relativeDays(copyTimestamp(end), nextDay, 3);
          updateFormatted(end);
          maxDays = 4;
          weekdays = [start.weekday, (start.weekday + 1) % 7, (start.weekday + 2) % 7, (start.weekday + 3) % 7];
          break;

        case 'custom-weekly':
          component = VCalendar_VCalendarWeekly;
          start = this.parsedStart || around;
          end = this.parsedEnd;
          break;

        case 'custom-daily':
          component = VCalendar_VCalendarDaily;
          start = this.parsedStart || around;
          end = this.parsedEnd;
          break;

        case 'category':
          const days = this.parsedCategoryDays;
          component = VCalendar_VCalendarCategory;
          end = relativeDays(copyTimestamp(end), nextDay, days);
          updateFormatted(end);
          maxDays = days;
          weekdays = [];

          for (let i = 0; i < days; i++) {
            weekdays.push((start.weekday + i) % 7);
          }

          categories = this.getCategoryList(categories);
          break;

        default:
          throw new Error(this.type + ' is not a valid Calendar type');
      }

      return {
        component,
        start,
        end,
        maxDays,
        weekdays,
        categories
      };
    },

    eventWeekdays() {
      return this.renderProps.weekdays;
    },

    categoryMode() {
      return this.type === 'category';
    },

    title() {
      const {
        start,
        end
      } = this.renderProps;
      const spanYears = start.year !== end.year;
      const spanMonths = spanYears || start.month !== end.month;

      if (spanYears) {
        return this.monthShortFormatter(start, true) + ' ' + start.year + ' - ' + this.monthShortFormatter(end, true) + ' ' + end.year;
      }

      if (spanMonths) {
        return this.monthShortFormatter(start, true) + ' - ' + this.monthShortFormatter(end, true) + ' ' + end.year;
      } else {
        return this.monthLongFormatter(start, false) + ' ' + start.year;
      }
    },

    monthLongFormatter() {
      return this.getFormatter({
        timeZone: 'UTC',
        month: 'long'
      });
    },

    monthShortFormatter() {
      return this.getFormatter({
        timeZone: 'UTC',
        month: 'short'
      });
    },

    parsedCategories() {
      return getParsedCategories(this.categories, this.categoryText);
    }

  },
  watch: {
    renderProps: 'checkChange'
  },

  mounted() {
    this.updateEventVisibility();
    this.checkChange();
  },

  updated() {
    window.requestAnimationFrame(this.updateEventVisibility);
  },

  methods: {
    checkChange() {
      const {
        lastStart,
        lastEnd
      } = this;
      const {
        start,
        end
      } = this.renderProps;

      if (!lastStart || !lastEnd || start.date !== lastStart.date || end.date !== lastEnd.date) {
        this.lastStart = start;
        this.lastEnd = end;
        this.$emit('change', {
          start,
          end
        });
      }
    },

    move(amount = 1) {
      const moved = copyTimestamp(this.parsedValue);
      const forward = amount > 0;
      const mover = forward ? nextDay : prevDay;
      const limit = forward ? DAYS_IN_MONTH_MAX : DAY_MIN;
      let times = forward ? amount : -amount;

      while (--times >= 0) {
        switch (this.type) {
          case 'month':
            moved.day = limit;
            mover(moved);
            break;

          case 'week':
            relativeDays(moved, mover, DAYS_IN_WEEK);
            break;

          case 'day':
            relativeDays(moved, mover, 1);
            break;

          case '4day':
            relativeDays(moved, mover, 4);
            break;

          case 'category':
            relativeDays(moved, mover, this.parsedCategoryDays);
            break;
        }
      }

      updateWeekday(moved);
      updateFormatted(moved);
      updateRelative(moved, this.times.now);

      if (this.value instanceof Date) {
        this.$emit('input', timestampToDate(moved));
      } else if (typeof this.value === 'number') {
        this.$emit('input', timestampToDate(moved).getTime());
      } else {
        this.$emit('input', moved.date);
      }

      this.$emit('moved', moved);
    },

    next(amount = 1) {
      this.move(amount);
    },

    prev(amount = 1) {
      this.move(-amount);
    },

    timeToY(time, clamp = true) {
      const c = this.$children[0];

      if (c && c.timeToY) {
        return c.timeToY(time, clamp);
      } else {
        return false;
      }
    },

    timeDelta(time) {
      const c = this.$children[0];

      if (c && c.timeDelta) {
        return c.timeDelta(time);
      } else {
        return false;
      }
    },

    minutesToPixels(minutes) {
      const c = this.$children[0];

      if (c && c.minutesToPixels) {
        return c.minutesToPixels(minutes);
      } else {
        return -1;
      }
    },

    scrollToTime(time) {
      const c = this.$children[0];

      if (c && c.scrollToTime) {
        return c.scrollToTime(time);
      } else {
        return false;
      }
    },

    parseTimestamp(input, required) {
      return parseTimestamp(input, required, this.times.now);
    },

    timestampToDate(timestamp) {
      return timestampToDate(timestamp);
    },

    getCategoryList(categories) {
      if (!this.noEvents) {
        const categoryMap = categories.reduce((map, category, index) => {
          if (typeof category === 'object' && category.categoryName) map[category.categoryName] = {
            index,
            count: 0
          };
          return map;
        }, {});

        if (!this.categoryHideDynamic || !this.categoryShowAll) {
          let categoryLength = categories.length;
          this.parsedEvents.forEach(ev => {
            let category = ev.category;

            if (typeof category !== 'string') {
              category = this.categoryForInvalid;
            }

            if (!category) {
              return;
            }

            if (category in categoryMap) {
              categoryMap[category].count++;
            } else if (!this.categoryHideDynamic) {
              categoryMap[category] = {
                index: categoryLength++,
                count: 1
              };
            }
          });
        }

        if (!this.categoryShowAll) {
          for (const category in categoryMap) {
            if (categoryMap[category].count === 0) {
              delete categoryMap[category];
            }
          }
        }

        categories = categories.filter(category => {
          if (typeof category === 'object' && category.categoryName) {
            return categoryMap.hasOwnProperty(category.categoryName);
          }

          return false;
        });
      }

      return categories;
    }

  },

  render(h) {
    const {
      start,
      end,
      maxDays,
      component,
      weekdays,
      categories
    } = this.renderProps;
    return h(component, {
      staticClass: 'v-calendar',
      class: {
        'v-calendar-events': !this.noEvents
      },
      props: { ...this.$props,
        start: start.date,
        end: end.date,
        maxDays,
        weekdays,
        categories
      },
      directives: [{
        modifiers: {
          quiet: true
        },
        name: 'resize',
        value: this.updateEventVisibility
      }],
      on: { ...this.$listeners,
        'click:date': day => {
          if (this.$listeners.input) {
            this.$emit('input', day.date);
          }

          if (this.$listeners['click:date']) {
            this.$emit('click:date', day);
          }
        }
      },
      scopedSlots: this.getScopedSlots()
    });
  }

}));
// CONCATENATED MODULE: ./components/Calendar.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1032)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var Calendar_component = Object(componentNormalizer["a" /* default */])(
  components_Calendarvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "fafced54"
  
)

/* harmony default export */ var Calendar = __webpack_exports__["default"] = (Calendar_component.exports);

/* nuxt-component-imports */
installComponents_default()(Calendar_component, {CalendarDate: __webpack_require__(967).default})


/* vuetify-loader */


installComponents_default()(Calendar_component, {VCalendar: VCalendar})


/***/ }),

/***/ 992:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: external "core-js/modules/esnext.set.add-all.js"
var esnext_set_add_all_js_ = __webpack_require__(836);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.delete-all.js"
var esnext_set_delete_all_js_ = __webpack_require__(837);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.difference.js"
var esnext_set_difference_js_ = __webpack_require__(838);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.every.js"
var esnext_set_every_js_ = __webpack_require__(839);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.filter.js"
var esnext_set_filter_js_ = __webpack_require__(840);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.find.js"
var esnext_set_find_js_ = __webpack_require__(841);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.intersection.js"
var esnext_set_intersection_js_ = __webpack_require__(842);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.is-disjoint-from.js"
var esnext_set_is_disjoint_from_js_ = __webpack_require__(843);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.is-subset-of.js"
var esnext_set_is_subset_of_js_ = __webpack_require__(844);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.is-superset-of.js"
var esnext_set_is_superset_of_js_ = __webpack_require__(845);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.join.js"
var esnext_set_join_js_ = __webpack_require__(846);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.map.js"
var esnext_set_map_js_ = __webpack_require__(847);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.reduce.js"
var esnext_set_reduce_js_ = __webpack_require__(848);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.some.js"
var esnext_set_some_js_ = __webpack_require__(849);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.symmetric-difference.js"
var esnext_set_symmetric_difference_js_ = __webpack_require__(850);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.union.js"
var esnext_set_union_js_ = __webpack_require__(851);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/StatusOnline.vue?vue&type=script&lang=js&
















/* harmony default export */ var StatusOnlinevue_type_script_lang_js_ = ({
  data() {
    return {
      timeoutId: null,
      userStatuses: {},
      arrStatusId: []
    };
  },

  computed: {
    preparedArr() {
      return [...new Set(this.arrStatusId)];
    }

  },

  mounted() {
    this.timeoutId = window.setInterval(() => {
      this.refreshStatusOnline();

      if (!this.arrStatusId.length) {
        this.clearInterval();
      }
    }, 10000);
  },

  beforeDestroy() {
    if (this.timeoutId) {
      this.clearInterval();
    }
  },

  methods: {
    refreshStatusOnline() {
      if (this.arrStatusId.length) {
        this.$store.dispatch('user/refreshStatusOnline', this.preparedArr).then(res => this.userStatuses = res);
      }
    },

    clearInterval() {
      window.clearInterval(this.timeoutId);
      this.timeoutId = null;
    }

  }
});
// CONCATENATED MODULE: ./mixins/StatusOnline.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_StatusOnlinevue_type_script_lang_js_ = (StatusOnlinevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/StatusOnline.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_StatusOnlinevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "2b0aab01"
  
)

/* harmony default export */ var StatusOnline = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 994:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1058);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("6d38e372", content, true, context)
};

/***/ }),

/***/ 997:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(956);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LessonItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 998:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".lesson{position:relative;min-height:128px;padding-left:142px}@media only screen and (max-width:1215px){.lesson{min-height:130px;padding-left:85px}}@media only screen and (min-width:768px){.lesson{background:#fff;border-radius:16px;box-shadow:0 6px 10px rgba(17,46,90,.08)}}@media only screen and (max-width:991px){.lesson{padding-left:112px}}@media only screen and (max-width:767px){.lesson{padding-left:0}}.lesson-date{justify-content:center;align-items:center;width:142px;padding:11px;font-size:16px;font-weight:600;background:linear-gradient(126.15deg,rgba(128,182,34,.74),rgba(60,135,248,.74) 102.93%);border-radius:16px;color:#fff}@media only screen and (min-width:768px){.lesson-date{position:absolute;left:0;top:0;height:100%;flex-direction:column;justify-content:space-between;box-shadow:4px 5px 8px hsla(0,0%,40%,.25);z-index:3}}@media only screen and (max-width:1215px){.lesson-date{width:85px;padding:14px 6px;font-size:15px}}@media only screen and (max-width:991px){.lesson-date{width:112px;padding:10px 4px;font-size:13px}}@media only screen and (max-width:767px){.lesson-date{width:100%;padding:7px 10px 32px;font-size:16px!important;font-weight:600!important;border-radius:10px 10px 0 0}.lesson-date>div{display:flex;align-items:center}}@media only screen and (max-width:480px){.lesson-date{font-size:14px!important;font-weight:400!important}}.lesson-date .time,.lesson-date .weekday{font-size:13px;font-weight:700;line-height:1}.lesson-date .date{font-weight:700;font-size:24px;line-height:1.2;white-space:nowrap}@media only screen and (max-width:1215px){.lesson-date .date{margin-bottom:4px;font-size:20px}}@media only screen and (max-width:767px){.lesson-date .date{margin-bottom:0;font-weight:inherit;font-size:inherit}}.lesson-date .remaining{line-height:1.23}@media only screen and (min-width:768px){.lesson-date .remaining{font-size:13px}}@media only screen and (max-width:767px){.lesson-date .remaining{padding:0 3px}}.lesson-date .duration{white-space:nowrap}@media only screen and (min-width:1216px){.lesson-date .duration{padding-left:22px}}@media only screen and (min-width:768px){.lesson-date .duration{position:relative;font-weight:400;color:#e8f1f7}}.lesson-date .duration-icon{position:absolute;left:0;top:50%;width:18px;height:18px;margin-top:-9px;color:var(--v-dark-lighten3)}@media only screen and (max-width:1215px){.lesson-date .duration-icon{display:none}}.lesson-content{position:relative;display:flex;justify-content:space-between;width:100%;border-radius:0 10px 10px 0;overflow:hidden}@media only screen and (max-width:767px){.lesson-content{min-height:128px;margin-top:-25px;border-radius:10px;background:#fff;box-shadow:0 1px 5px hsla(0,0%,51%,.3);z-index:2}}@media only screen and (max-width:639px){.lesson-content{flex-direction:column;padding:12px 12px 4px}}.lesson-details{display:flex;padding:8px 10px 8px 18px}@media only screen and (max-width:1215px){.lesson-details{padding:12px 4px 8px 12px}.lesson-details>.avatar{display:none}}@media only screen and (max-width:767px){.lesson-details{padding-top:10px}}@media only screen and (max-width:639px){.lesson-details{position:relative;padding:0 0 0 100px}}.lesson-details .avatar{position:relative}@media only screen and (max-width:639px){.lesson-details .avatar{position:absolute;left:0;top:0}}.lesson-details .avatar .user-status{position:absolute;top:0;right:0}@media only screen and (max-width:479px){.lesson-details .avatar .user-status{top:1px;right:1px}}.lesson-details .avatar a{position:absolute;top:0;left:0;width:100%;height:100%}.lesson-details .details{display:flex;flex-direction:column;justify-content:space-between}.lesson-details .details .user-info{line-height:1.1}@media only screen and (max-width:1439px){.lesson-details .details .user-info{margin-bottom:8px}}@media only screen and (min-width:1216px){.lesson-details .details .user-info{padding-top:2px}}.lesson-details .details .user-info-name{display:inline;font-size:24px}@media only screen and (min-width:1440px){.lesson-details .details .user-info-name{margin-right:6px}}@media only screen and (max-width:1439px){.lesson-details .details .user-info-name{font-size:22px}}@media only screen and (max-width:1215px){.lesson-details .details .user-info-name{font-size:20px}}@media only screen and (max-width:479px){.lesson-details .details .user-info-name{font-size:18px}}.lesson-details .details .user-info-name div{position:relative;top:-2px;display:inline-block;height:16px;color:var(--v-dark-lighten3);cursor:pointer}.lesson-details .details .user-info-status{display:inline;font-size:13px;color:var(--v-dark-lighten3);text-transform:lowercase}.lesson-details .details .user-info-status--online{color:var(--v-green-lighten1)}.lesson-details .details .lesson-info{font-size:16px;color:var(--v-dark-lighten3);line-height:1.1}@media only screen and (min-width:768px){.lesson-details .details .lesson-info{padding-bottom:4px}}@media only screen and (min-width:1216px){.lesson-details .details .lesson-info .avatar{display:none!important}}@media only screen and (max-width:1215px){.lesson-details .details .lesson-info{display:flex;align-items:flex-end;font-size:14px}}.lesson-details .details .lesson-info>div:not(.avatar)>div:not(:last-child){margin-bottom:3px}@media only screen and (max-width:639px){.lesson-details .details .lesson-info>div:not(.avatar)>div:not(:last-child){margin-bottom:5px}}@media only screen and (min-width:640px){.lesson-details .details .lesson-info .lesson-actions-additional{display:none!important}}.lesson-actions .v-btn:not(.v-btn--icon).v-size--default,.lesson-dialog .v-btn:not(.v-btn--icon).v-size--default{min-width:158px!important;padding:0 10px!important}.lesson-actions{display:flex;width:350px;padding:18px 18px 10px 0}@media only screen and (max-width:1215px){.lesson-actions{padding:12px 12px 8px 0}}@media only screen and (max-width:767px){.lesson-actions{padding-top:10px}}@media only screen and (min-width:640px){.lesson-actions{flex-direction:column;align-items:flex-end;justify-content:space-between;flex-grow:1}}@media only screen and (max-width:639px){.lesson-actions{width:100%;margin-top:8px;padding:8px 0 0;border-top:1px solid rgba(0,0,0,.08)}}.lesson-actions-buttons{display:flex;justify-content:flex-end}@media only screen and (max-width:639px){.lesson-actions-buttons{justify-content:space-between;width:100%}.lesson-actions-buttons .v-btn{width:158px!important;margin-left:0!important;margin-right:0!important}}.lesson-actions-buttons .go-to-class-btn .icon--right{margin-left:3px}.lesson-actions-buttons .go-to-class-btn .icon--rotated{transform:rotate(-90deg)}.lesson-actions-additional{min-width:158px;font-size:13px;line-height:1.333}@media only screen and (max-width:639px){.lesson-actions-additional{font-size:inherit;line-height:inherit}}@media only screen and (max-width:639px){.lesson-actions-additional>div{margin-top:5px}}.lesson-actions-additional>div>div,.lesson-actions-additional a,.lesson-actions-additional span.action{position:relative;display:inline-block;padding-left:20px;color:var(--v-darkLight-lighten3)!important;text-decoration:none;transition:color .3s}.lesson-actions-additional>div>div .v-image,.lesson-actions-additional a .v-image,.lesson-actions-additional span.action .v-image{position:absolute;left:0;top:50%;transform:translateY(-50%)}.lesson-actions-additional a,.lesson-actions-additional span.action{cursor:pointer}.lesson-actions-additional a:hover,.lesson-actions-additional span.action:hover{color:var(--v-success-base)!important}@media only screen and (max-width:639px){.lesson-actions .lesson-actions-additional{display:none!important}}.lesson-dialog{position:absolute;top:0;left:100%;width:100%;height:100%;padding:10px 18px 12px;font-size:13px;line-height:1.2;background-color:#fff;border-radius:0 16px 16px 0;z-index:2;transition:all .3s ease-in}@media only screen and (max-width:1215px){.lesson-dialog{padding:8px 12px}}@media only screen and (max-width:991px){.lesson-dialog{left:0;top:100%}}@media only screen and (max-width:639px){.lesson-dialog.justify-center{justify-content:space-between!important}}.lesson-dialog-title{display:flex;margin-bottom:5px;font-size:20px}@media only screen and (max-width:639px){.lesson-dialog-title{margin-bottom:12px;font-size:18px}}.lesson-dialog-title-icon{display:flex;align-items:center;padding-right:4px}.lesson-dialog-content{color:var(--v-dark-lighten3);overflow-y:auto}@media only screen and (max-width:639px){.lesson-dialog-buttons{display:flex;justify-content:space-between;margin-top:8px;border-top:1px solid rgba(0,0,0,.08)}}.lesson-dialog-buttons .v-btn{max-width:246px;margin-top:8px}@media only screen and (min-width:640px){.lesson-dialog-buttons .v-btn{width:100%}}.lesson-dialog-buttons>:first-child{margin-right:16px}@media only screen and (max-width:639px){.lesson-dialog-buttons>:first-child{margin-left:4px}}.lesson-dialog--student-info .lesson-dialog-content{display:flex}@media only screen and (min-width:640px){.lesson-dialog--student-info .lesson-dialog-content{margin-right:168px}}.lesson-dialog--student-info .lesson-dialog-content>div{display:flex;flex-grow:1}@media only screen and (max-width:639px){.lesson-dialog--student-info .lesson-dialog-content>div{width:100%;max-width:100%}}@media only screen and (max-width:479px){.lesson-dialog--student-info .lesson-dialog-content>div{flex-wrap:wrap}}@media only screen and (max-width:479px){.lesson-dialog--student-info .lesson-dialog-content>div>div{padding:0!important}}@media only screen and (min-width:480px){.lesson-dialog--student-info .lesson-dialog-content>div>div:first-child{padding-right:45px;border-right:1px solid var(--v-dark-lighten3)}}.lesson-dialog--student-info .lesson-dialog-content>div>div ul{padding:0;list-style-type:none;line-height:1.2}@media only screen and (min-width:1216px){.lesson-dialog--student-info .lesson-dialog-buttons{right:18px;bottom:12px}}@media only screen and (min-width:640px){.lesson-dialog--student-info .lesson-dialog-buttons{position:absolute;right:12px;bottom:8px}}@media only screen and (max-width:639px){.lesson-dialog--student-info .lesson-dialog-buttons{display:flex;justify-content:flex-end;align-items:flex-end}}.lesson-dialog--student-info .lesson-dialog-buttons .v-btn{margin-right:0!important}.lesson-dialog--shown{left:0;transition:all .3s ease-out}@media only screen and (max-width:991px){.lesson-dialog--shown{top:0}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

};;
//# sourceMappingURL=index.js.map