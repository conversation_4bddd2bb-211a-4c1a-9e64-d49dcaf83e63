(window.webpackJsonp=window.webpackJsonp||[]).push([[40,56],{1372:function(t,e,o){var n=o(43);t.exports=function(t){return n(Set.prototype.values,t)}},1384:function(t,e,o){"use strict";var n=o(43),r=o(79),h=o(32);t.exports=function(){for(var t=h(this),e=r(t.add),o=0,d=arguments.length;o<d;o++)n(e,t,arguments[o]);return t}},1390:function(t,e,o){"use strict";o(872)("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),o(873))},1391:function(t,e,o){"use strict";o(11)({target:"Set",proto:!0,real:!0,forced:!0},{addAll:o(1384)})},1392:function(t,e,o){"use strict";o(11)({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:o(874)})},1393:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(43),d=o(79),l=o(32),c=o(125),v=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{difference:function(t){var e=l(this),o=new(c(e,r("Set")))(e),n=d(o.delete);return v(t,(function(t){h(n,o,t)})),o}})},1394:function(t,e,o){"use strict";var n=o(11),r=o(32),h=o(69),d=o(1372),l=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{every:function(t){var e=r(this),o=d(e),n=h(t,arguments.length>1?arguments[1]:void 0);return!l(o,(function(t,o){if(!n(t,t,e))return o()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1395:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(43),d=o(79),l=o(32),c=o(69),v=o(125),f=o(1372),m=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{filter:function(t){var e=l(this),o=f(e),n=c(t,arguments.length>1?arguments[1]:void 0),x=new(v(e,r("Set"))),y=d(x.add);return m(o,(function(t){n(t,t,e)&&h(y,x,t)}),{IS_ITERATOR:!0}),x}})},1396:function(t,e,o){"use strict";var n=o(11),r=o(32),h=o(69),d=o(1372),l=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{find:function(t){var e=r(this),o=d(e),n=h(t,arguments.length>1?arguments[1]:void 0);return l(o,(function(t,o){if(n(t,t,e))return o(t)}),{IS_ITERATOR:!0,INTERRUPTED:!0}).result}})},1397:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(43),d=o(79),l=o(32),c=o(125),v=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function(t){var e=l(this),o=new(c(e,r("Set"))),n=d(e.has),f=d(o.add);return v(t,(function(t){h(n,e,t)&&h(f,o,t)})),o}})},1398:function(t,e,o){"use strict";var n=o(11),r=o(43),h=o(79),d=o(32),l=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function(t){var e=d(this),o=h(e.has);return!l(t,(function(t,n){if(!0===r(o,e,t))return n()}),{INTERRUPTED:!0}).stopped}})},1399:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(43),d=o(79),l=o(45),c=o(32),v=o(209),f=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function(t){var e=v(this),o=c(t),n=o.has;return l(n)||(o=new(r("Set"))(t),n=d(o.has)),!f(e,(function(t,e){if(!1===h(n,o,t))return e()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1400:function(t,e,o){"use strict";var n=o(11),r=o(43),h=o(79),d=o(32),l=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function(t){var e=d(this),o=h(e.has);return!l(t,(function(t,n){if(!1===r(o,e,t))return n()}),{INTERRUPTED:!0}).stopped}})},1401:function(t,e,o){"use strict";var n=o(11),r=o(17),h=o(32),d=o(61),l=o(1372),c=o(86),v=r([].join),f=[].push;n({target:"Set",proto:!0,real:!0,forced:!0},{join:function(t){var e=h(this),o=l(e),n=void 0===t?",":d(t),r=[];return c(o,f,{that:r,IS_ITERATOR:!0}),v(r,n)}})},1402:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(69),d=o(43),l=o(79),c=o(32),v=o(125),f=o(1372),m=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{map:function(t){var e=c(this),o=f(e),n=h(t,arguments.length>1?arguments[1]:void 0),x=new(v(e,r("Set"))),y=l(x.add);return m(o,(function(t){d(y,x,n(t,t,e))}),{IS_ITERATOR:!0}),x}})},1403:function(t,e,o){"use strict";var n=o(11),r=o(5),h=o(79),d=o(32),l=o(1372),c=o(86),v=r.TypeError;n({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function(t){var e=d(this),o=l(e),n=arguments.length<2,r=n?void 0:arguments[1];if(h(t),c(o,(function(o){n?(n=!1,r=o):r=t(r,o,o,e)}),{IS_ITERATOR:!0}),n)throw v("Reduce of empty set with no initial value");return r}})},1404:function(t,e,o){"use strict";var n=o(11),r=o(32),h=o(69),d=o(1372),l=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{some:function(t){var e=r(this),o=d(e),n=h(t,arguments.length>1?arguments[1]:void 0);return l(o,(function(t,o){if(n(t,t,e))return o()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1405:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(43),d=o(79),l=o(32),c=o(125),v=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function(t){var e=l(this),o=new(c(e,r("Set")))(e),n=d(o.delete),f=d(o.add);return v(t,(function(t){h(n,o,t)||h(f,o,t)})),o}})},1406:function(t,e,o){"use strict";var n=o(11),r=o(87),h=o(79),d=o(32),l=o(125),c=o(86);n({target:"Set",proto:!0,real:!0,forced:!0},{union:function(t){var e=d(this),o=new(l(e,r("Set")))(e);return c(t,h(o.add),{that:o}),o}})},1428:function(t,e,o){"use strict";o(35),o(81),o(23),o(6),o(24),o(38);var n={computed:{role:function(){return this.$store.getters["classroom/role"]}},methods:{setTool:function(t,e){this.$store.commit("classroom/enableContainerComponent","pointer"===t),this.$socket.emit("cursor-moved",{tool:t,cursor:e.replace(/(-cursor|cursor-)/i,""),lessonId:this.$store.state.classroom.lessonId}),this.$store.commit("classroom/setUserTool",t),this.$store.commit("classroom/setUserCursor",e);var o=document.body,n=o.classList;this.removeCursors(n),o.classList.add("".concat(this.role,"-").concat(e)),this.classList=o.classList},removeCursors:function(t){t.forEach((function(t){t.includes("cursor")&&document.body.classList.remove(t)}))}}},r=o(22),component=Object(r.a)(n,undefined,undefined,!1,null,null,null);e.a=component.exports},1449:function(t,e,o){var content=o(1477);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("07a1f444",content,!0,{sourceMap:!1})},1459:function(t,e,o){"use strict";o.r(e);o(31),o(35),o(60);var n=o(1528),r=o(266),h=o(1428),d=(o(1449),{components:{VueDraggableResizable:n.default},mixins:[h.a],props:{asset:{type:Object,required:!0},childHeaderHeight:{type:Number,default:0},lockAspectRatio:{type:Boolean,default:!0},scalable:{type:Boolean,default:!0},hoverEnabled:{type:Boolean,default:!0},isDraggableProp:{type:Boolean,default:!0},hideResizeIcon:{type:Boolean,default:!1},handles:{type:Array,default:function(){return["tl","tm","tr","mr","br","bm","bl","ml"]}}},data:function(){return{width:r.j,height:r.i,top:50,left:500,isHovered:!1,isHoveredByAsset:!1,index:5,eventBodyClass:null,allowIndexChange:!0,resizable:!0,draggable:!0,synchronizeable:!0,viewportWidth:window.innerWidth,viewportHeight:window.innerHeight,offset:5}},computed:{isCanvasOversizeX:function(){return r.n>this.viewportWidth},isCanvasOversizeY:function(){return r.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return r.k*this.zoomIndex>this.viewportHeight},getRoleHoverColor:function(){return"teacher"===this.role?"var(--v-teacherColor-base)":"var(--v-studentColor-base)"},enabledResizeable:function(){return this.resizable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},enabledDraggable:function(){return this.isDraggableProp&&this.draggable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},maxIndex:function(){return this.$store.state.classroom.maxIndex},zoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},zoomIndex:function(){var t,e;return null!==(t=null===(e=this.zoom)||void 0===e?void 0:e.zoomIndex)&&void 0!==t?t:1},type:function(){var t,e;return null===(t=this.asset)||void 0===t||null===(e=t.asset)||void 0===e?void 0:e.type},topScale:function(){return"toolbar"===this.type?this.top<0||this.top+this.height>this.viewportHeight?this.viewportHeight-this.height-70:this.top:this.synchronizeable?this.top-this.zoom.y:this.top},leftScale:function(){return"toolbar"===this.type?this.left+this.width>this.viewportWidth||this.left<0?this.viewportWidth-this.width-15:this.left:this.synchronizeable?this.left-this.zoom.x:this.left},isLockedForStudent:function(){return this.$store.getters["classroom/isLocked"]&&"student"===this.role},isSocketConnected:function(){return this.$store.state.socket.isConnected}},watch:{"asset.asset":function(t){this.move(t)}},mounted:function(){this.move(this.asset.asset)},methods:{mouseenter:function(){var t,e;(this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!0,this.socketAssetMoved({isHovered:!0})),"twilio"===this.type||"tokbox"===this.type||"editor"===this.type)&&(this.$store.commit("classroom/setCursorNameBeforeChange",(null===(t=this.$store.getters["classroom/userParams"])||void 0===t?void 0:t.cursor)||"cursor-pointer"),this.$store.commit("classroom/setToolNameBeforeChange",(null===(e=this.$store.getters["classroom/userParams"])||void 0===e?void 0:e.tool)||"pointer"),this.setTool("pointer","cursor-pointer"))},mouseleave:function(){this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!1,this.socketAssetMoved({isHovered:!1})),"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||this.setTool(this.$store.state.classroom.toolNameBeforeChange,this.$store.state.classroom.cursorNameBeforeChange)},onIndex:function(){this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{index:this.index}}),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{index:this.index}})},updateAsset:function(t,e){this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,index:this.index}})},onDrag:function(t,e){if(this.synchronizeable){if(this.left=t+this.zoom.x,this.top=e+this.zoom.y,this.allowIndexChange){var o=document.body;o.classList.contains(this.eventBodyClass)||(this.eventBodyClass="dragging",o.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var n={left:this.left,top:this.top,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:n}),this.socketAssetMoved(n)}},onDragStop:function(t,e){var o=document.body;o.classList.contains(this.eventBodyClass)&&(o.classList.remove(this.eventBodyClass),this.eventBodyClass=null),this.allowIndexChange=!0,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,index:this.index}})},onResize:function(t,e,o,n,r){if(this.synchronizeable){if(this.left=t+this.zoom.x,this.top=e+this.zoom.y,this.width=o,this.height=n-this.childHeaderHeight,this.allowIndexChange){var h=document.body;h.classList.contains(this.eventBodyClass)||(this.eventBodyClass=r.split(" ")[1],h.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var d={left:this.left,top:this.top,width:this.width,height:this.height,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:d}),this.socketAssetMoved(d)}},onResizeStop:function(t,e,o,n){this.eventBodyClass&&document.body.classList.remove(this.eventBodyClass),this.allowIndexChange=!0,this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,width:o,height:n-this.childHeaderHeight,index:this.index}})},socketAssetMoved:function(t){this.isSocketConnected&&this.$socket.emit("asset-moved",{id:this.asset.id,lessonId:this.asset.lessonId,asset:t})},move:function(t){if(void 0!==t.width?this.width=t.width:"editor"===this.type&&(this.width=.66*(this.isCanvasOversizeX?this.viewportWidth:r.n)),void 0!==t.height)this.height=t.height;else{if("editor"===this.type){var e=.8*(this.isCanvasOversizeY?this.viewportHeight:r.k);e>1200&&(e=1200),e<400&&(e=400),this.height=e-2*this.offset}"audio"===this.type&&(this.height=0)}void 0!==t.top?this.top=t.top:"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||(this.top=this.offset),void 0!==t.left?this.left=t.left:("twilio"!==this.type&&"tokbox"!==this.type||(this.left=(this.isCanvasOversizeX?this.viewportWidth:r.n)-this.width-this.offset),"editor"===this.type&&(this.left=this.offset)),void 0!==t.index&&(this.index=t.index),void 0!==t.resizable&&(this.resizable=t.resizable),void 0!==t.draggable&&(this.draggable=t.draggable),void 0!==t.synchronizeable&&(this.synchronizeable=t.synchronizeable),void 0!==t.isHovered&&(this.isHoveredByAsset=t.isHovered)}}}),l=o(22),component=Object(l.a)(d,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{on:{mouseenter:t.mouseenter,mouseleave:t.mouseleave}},[o("vue-draggable-resizable",{ref:"vueDraggableResizable",class:{student:"student"===t.role,teacher:"teacher"===t.role,"hide-resize-icon":t.hideResizeIcon},style:{outline:t.isHoveredByAsset||t.isHovered?"3px solid "+t.getRoleHoverColor:"none"},attrs:{draggable:t.enabledDraggable,resizable:t.enabledResizeable,w:t.width,h:t.height+t.childHeaderHeight,x:t.leftScale,y:t.topScale,z:t.index,"zoom-index":t.scalable?t.zoom.zoomIndex:1,"zoom-x":t.zoom.x,"zoom-y":t.zoom.y,"child-header-height":t.childHeaderHeight,"lock-aspect-ratio":t.lockAspectRatio,handles:t.handles},on:{dragging:t.onDrag,resizing:t.onResize,dragstop:t.onDragStop,resizestop:t.onResizeStop,"update-asset":t.updateAsset},nativeOn:{click:function(e){return t.onIndex.apply(null,arguments)}}},[t._t("default",null,{onIndex:t.onIndex})],2)],1)}),[],!1,null,null,null);e.default=component.exports},1477:function(t,e,o){var n=o(18),r=o(265),h=o(870),d=n(!1),l=r(h);d.push([t.i,'.vdr{touch-action:none;position:absolute;box-sizing:border-box;border:none}.handle{display:block!important}.resizable.vdr:not(.hide-resize-icon):after{content:"";position:absolute;bottom:4px;right:2px;width:8px;height:8px;background-image:url('+l+');background-size:contain;background-repeat:no-repeat;background-position:50%;opacity:.9;z-index:99}.vdr .handle{box-sizing:border-box;position:absolute;width:12px;height:12px;border:none;background-color:transparent;z-index:10}.vdr .handle-tl{top:-7px;left:-7px}.vdr .handle-tm{top:-7px;margin-left:-5px}.vdr .handle-tr{top:-7px;right:-7px}.vdr .handle-ml{margin-top:-5px;left:-7px}.vdr .handle-mr{margin-top:-5px;right:-7px}.vdr .handle-bl{bottom:-7px;left:-7px}.vdr .handle-bm{bottom:-7px;margin-left:-5px}.vdr .handle-br{bottom:-7px;right:-7px}@media only screen and (max-width:768px){[class*=handle-]:before{content:"";left:-10px;right:-10px;bottom:-10px;top:-10px;position:absolute}}.vdr .handle-bm,.vdr .handle-tm{width:100%;left:5px}.vdr .handle-ml,.vdr .handle-mr{height:100%;top:5px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:14px;height:14px;z-index:11}@media only screen and (max-width:1199px){.vdr .handle-bm,.vdr .handle-tm{height:15px}.vdr .handle-ml,.vdr .handle-mr{width:15px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:15px;height:15px}.vdr .handle-tl{top:-10px;left:-10px}.vdr .handle-tm{top:-10px;margin-left:-5px}.vdr .handle-tr{top:-10px;right:-10px}.vdr .handle-ml{margin-top:-5px;left:-10px}.vdr .handle-mr{margin-top:-5px;right:-10px}.vdr .handle-bl{bottom:-10px;left:-10px}.vdr .handle-bm{bottom:-10px;margin-left:-5px}.vdr .handle-br{bottom:-10px;right:-10px}}',""]),t.exports=d}}]);