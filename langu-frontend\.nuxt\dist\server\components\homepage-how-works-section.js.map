{"version": 3, "file": "components/homepage-how-works-section.js", "sources": ["webpack:///./components/homepage/HowWorksSection.vue?c422", "webpack:///./components/homepage/HowWorksSection.vue?90cc", "webpack:///./components/homepage/HowWorksSection.vue?c5c2", "webpack:///./components/homepage/HowWorksSection.vue?4289", "webpack:///./components/homepage/HowWorksSection.vue", "webpack:///./components/homepage/HowWorksSection.vue?ff8b", "webpack:///./components/homepage/HowWorksSection.vue?cdad"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HowWorksSection.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"4eb1170e\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HowWorksSection.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_GET_URL_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/getUrl.js\");\nvar ___CSS_LOADER_URL_IMPORT_0___ = require(\"../../assets/images/homepage/details-circle-bg.svg\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".how-works{position:relative;padding-top:105px}@media only screen and (max-width:767px){.how-works{padding-top:80px}}.how-works .section-head{margin-bottom:90px}@media only screen and (max-width:767px){.how-works .section-head{margin-bottom:60px}}.how-works-content{position:relative;color:#fff}@media only screen and (min-width:992px){.how-works-content{height:1px;min-height:730px}}@media only screen and (max-width:991px){.how-works-content{padding:146px 0 92px}}@media only screen and (max-width:639px){.how-works-content{padding:180px 0 65px}}.how-works-content:after{content:\\\"\\\";position:absolute;top:-230px;right:0;width:295px;height:648px;background-image:url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \");background-size:contain;background-position:100%}@media only screen and (max-width:1439px){.how-works-content:after{top:-115px;width:200px;height:448px}}@media only screen and (max-width:991px){.how-works-content:after{top:0;width:110px;height:221px}}@media only screen and (max-width:639px){.how-works-content:after{top:-50px}}.how-works-item{position:relative;margin-bottom:75px}@media only screen and (max-width:991px){.how-works-item{max-width:80%;margin-bottom:0;padding-left:30px;padding-bottom:110px}}@media only screen and (max-width:639px){.how-works-item{max-width:100%;padding-left:25px}}@media only screen and (max-width:479px){.how-works-item{padding-bottom:150px}}.how-works-item-bg{position:absolute}.how-works-item-title{position:relative}.how-works-item-title:before{position:absolute;top:-8px;left:-28px;font-size:82px;font-weight:700;line-height:.8;color:var(--v-orange-base);opacity:.2}.how-works-item-i1 .how-works-item-bg{left:-265px;top:20px;width:210px;height:170px}@media only screen and (max-width:1439px){.how-works-item-i1 .how-works-item-bg{left:-225px;top:52px;width:180px;height:155px}}@media only screen and (max-width:991px){.how-works-item-i1 .how-works-item-bg{left:205px;top:60px;width:169px;height:142px}}@media only screen and (max-width:639px){.how-works-item-i1 .how-works-item-bg{left:180px;top:65px}}@media only screen and (max-width:479px){.how-works-item-i1 .how-works-item-bg{left:125px;height:150px;top:auto;bottom:0}}.how-works-item-i1 .details-item-title:before{content:\\\"1\\\"}.how-works-item-i2 .how-works-item-bg{right:-350px;top:8px;width:280px;height:80px}@media only screen and (max-width:1439px){.how-works-item-i2 .how-works-item-bg{right:-292px;top:22px;width:238px;height:66px}}@media only screen and (max-width:991px){.how-works-item-i2 .how-works-item-bg{left:70px;top:65px;width:167px;height:190px}}@media only screen and (max-width:639px){.how-works-item-i2 .how-works-item-bg{left:85px;top:70px}}@media only screen and (max-width:479px){.how-works-item-i2 .how-works-item-bg{height:150px;top:auto;bottom:0}}.how-works-item-i2 .details-item-title:before{content:\\\"2\\\"}.how-works-item-i3 .how-works-item-bg{left:-20px;top:calc(100% + 22px);width:178px;height:135px}@media only screen and (max-width:1439px){.how-works-item-i3 .how-works-item-bg{left:0;width:148px;height:115px}}@media only screen and (max-width:991px){.how-works-item-i3 .how-works-item-bg{left:auto;right:5px;top:75px;width:174px;height:140px}}@media only screen and (max-width:479px){.how-works-item-i3 .how-works-item-bg{height:150px;top:auto;bottom:0}}.how-works-item-i3 .details-item-title:before{content:\\\"3\\\"}.how-works-item-i4 .details-item-title:before{content:\\\"4\\\"}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"how-works\"},[_c('v-container',{staticClass:\"py-0\"},[_c('v-row',[_c('v-col',{staticClass:\"col-12 py-0\"},[_c('div',{staticClass:\"section-head section-head--decorated\"},[_c('h3',{staticClass:\"section-head-title\",staticStyle:{\"color\":\"#262626\",\"-webkit-text-fill-color\":\"#262626\"}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('home_page.details_section_title'))+\"\\n          \")])])])],1)],1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"how-works-content\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"section-bg\\\">\",\"</div>\",[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/details-bg.png'),\"options\":{ rootMargin: '50%' }}})],1),_vm._ssrNode(\" \"),_c('v-container',{staticClass:\"py-0\",attrs:{\"fill-height\":\"\"}},[_c('v-row',[_c('v-col',{staticClass:\"col-xl-10 offset-xl-1 py-0\"},[_c('v-row',[_c('v-col',{staticClass:\"col-12 col-md-4 offset-md-4 py-0\"},[_c('div',{staticClass:\"how-works-item how-works-item-i1\"},[_c('div',{staticClass:\"how-works-item-bg d-none d-md-block\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/arrow-1.svg'),\"options\":{ rootMargin: '50%' },\"contain\":\"\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"how-works-item-bg d-md-none\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/arrow-1-1.svg'),\"options\":{ rootMargin: '50%' },\"contain\":\"\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"how-works-item-title\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.details_1_title'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"how-works-item-text\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.details_1_text'))+\"\\n                \")])])])],1),_vm._v(\" \"),_c('v-row',[_c('v-col',{staticClass:\"col-12 col-md-4 py-0\"},[_c('div',{staticClass:\"how-works-item how-works-item-i2\"},[_c('div',{staticClass:\"how-works-item-bg d-none d-md-block\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/arrow-2.svg'),\"options\":{ rootMargin: '50%' },\"contain\":\"\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"how-works-item-bg d-md-none\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/arrow-2-1.svg'),\"options\":{ rootMargin: '50%' },\"contain\":\"\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"how-works-item-title\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.details_2_title'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"how-works-item-text\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.details_2_text'))+\"\\n                \")])])]),_vm._v(\" \"),_c('v-col',{staticClass:\"col-12 col-md-4 offset-md-4 py-0\"},[_c('div',{staticClass:\"how-works-item how-works-item-i3\"},[_c('div',{staticClass:\"how-works-item-bg d-none d-md-block\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/arrow-3.svg'),\"options\":{ rootMargin: '50%' },\"contain\":\"\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"how-works-item-bg d-md-none\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/arrow-3-1.svg'),\"options\":{ rootMargin: '50%' },\"contain\":\"\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"how-works-item-title\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.details_3_title'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"how-works-item-text\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.details_3_text'))+\"\\n                \")])])])],1),_vm._v(\" \"),_c('v-row',[_c('v-col',{staticClass:\"col-12 col-md-4 offset-md-4 py-0\"},[_c('div',{staticClass:\"how-works-item how-works-item-i4\"},[_c('div',{staticClass:\"how-works-item-title\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.details_4_title'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"how-works-item-text\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.details_4_text'))+\"\\n                \")])])])],1)],1)],1)],1)],2)],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'HowWorksSection',\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HowWorksSection.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./HowWorksSection.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./HowWorksSection.vue?vue&type=template&id=7dd35c7a&\"\nimport script from \"./HowWorksSection.vue?vue&type=script&lang=js&\"\nexport * from \"./HowWorksSection.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./HowWorksSection.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"eb63e846\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VContainer } from 'vuetify/lib/components/VGrid';\nimport { VImg } from 'vuetify/lib/components/VImg';\nimport { VRow } from 'vuetify/lib/components/VGrid';\ninstallComponents(component, {VCol,VContainer,VImg,VRow})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACTA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AADA;;AC1HA;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}