{"version": 3, "file": "components/confirm-dialog.js", "sources": ["webpack:///./components/ConfirmDialog.vue?8e2d", "webpack:///./components/ConfirmDialog.vue?56aa", "webpack:///./components/ConfirmDialog.vue?48f5", "webpack:///./components/ConfirmDialog.vue?5333", "webpack:///./components/ConfirmDialog.vue", "webpack:///./components/ConfirmDialog.vue?51a0", "webpack:///./components/ConfirmDialog.vue?56f3"], "sourcesContent": ["export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ConfirmDialog.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".remove-illustration-title{font-size:20px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ConfirmDialog.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"f203485e\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.isShownConfirmDialog)?_c('l-dialog',_vm._g({attrs:{\"dialog\":_vm.isShownConfirmDialog,\"hide-close-button\":\"\",\"max-width\":\"418\",\"custom-class\":\"remove-illustration text-center\"}},_vm.$listeners),[_c('div',[_c('div',{staticClass:\"remove-illustration-title font-weight-medium\"},[_vm._v(\"\\n      \"+_vm._s(_vm.$t('are_you_sure'))+\"\\n    \")]),_vm._v(\" \"),_c('div',{staticClass:\"mt-2\"},[_vm._t(\"default\")],2),_vm._v(\" \"),_c('div',{staticClass:\"d-flex justify-space-around justify-sm-space-between flex-wrap mt-2\"},[_c('v-btn',{staticClass:\"gradient font-weight-medium my-1\",on:{\"click\":function($event){return _vm.$emit('close-dialog')}}},[_c('div',{staticClass:\"text--gradient\"},[_vm._v(\"\\n          \"+_vm._s(_vm.$t(_vm.cancelTextButton))+\"\\n        \")])]),_vm._v(\" \"),_c('v-btn',{staticClass:\"font-weight-medium my-1\",attrs:{\"color\":\"primary\"},on:{\"click\":function($event){return _vm.$emit('confirm')}}},[_vm._v(\"\\n        \"+_vm._s(_vm.$t(_vm.confirmTextButton))+\"\\n      \")])],1)])]):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'ConfirmDialog',\n  props: {\n    isShownConfirmDialog: {\n      type: Boolean,\n      required: true,\n    },\n    cancelTextButton: {\n      type: String,\n      default: 'close',\n    },\n    confirmTextButton: {\n      type: String,\n      default: 'confirm',\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ConfirmDialog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./ConfirmDialog.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./ConfirmDialog.vue?vue&type=template&id=2a649283&\"\nimport script from \"./ConfirmDialog.vue?vue&type=script&lang=js&\"\nexport * from \"./ConfirmDialog.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./ConfirmDialog.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"33ddf780\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {LDialog: require('D:/languworks/langu-frontend/components/LDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\ninstallComponents(component, {VBtn})\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AATA;AAFA;;ACxCA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}