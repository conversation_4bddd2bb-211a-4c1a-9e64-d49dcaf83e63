exports.ids = [17];
exports.modules = {

/***/ 1205:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./helpers/index.js
var helpers = __webpack_require__(501);

// EXTERNAL MODULE: ./helpers/constants.js
var constants = __webpack_require__(69);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/UploadFiles.vue?vue&type=script&lang=js&


/* harmony default export */ var UploadFilesvue_type_script_lang_js_ = ({
  computed: {
    lessonId() {
      return this.$store.state.classroom.lessonId;
    },

    role() {
      return this.$store.getters['classroom/role'];
    },

    acceptedFiles() {
      return this.$store.state.classroom.acceptedFiles;
    }

  },
  methods: {
    async uploadFiles(files) {
      files = [...files];
      const formData = new FormData();

      for (let i = 0; i <= files.length - 1; i++) {
        const file = files[i];
        const fileExtension = Object(helpers["getFileExtension"])(file.name);

        if (file.size > constants["a" /* MAX_FILE_SIZE */]) {
          await this.$store.dispatch('snackbar/error', {
            errorMessage: this.$t('filename_size_should_be_less_than', {
              fileName: file.name,
              value: `${(constants["a" /* MAX_FILE_SIZE */] / 8 / 1000).toFixed(0)} Mb`
            }),
            timeout: 5000
          });
          continue;
        }

        if (this.acceptedFiles.officeTypes.includes(fileExtension)) {
          const {
            data,
            fileName
          } = await this.$store.dispatch('classroom/convertOfficeToPdf', file);
          formData.append(i.toString(), new Blob([data]), fileName);
        } else {
          formData.append(i.toString(), file);
        }
      }

      this.$store.dispatch('classroom/uploadFiles', formData).then(assets => {
        let offsetX = 0;
        let offsetY = 0;
        this.$store.commit('classroom/addAssets', assets);
        assets.forEach(asset => {
          var _this$acceptedFiles, _this$acceptedFiles2, _this$acceptedFiles3;

          const item = {
            id: asset.id,
            lessonId: this.lessonId,
            asset: { ...asset.asset,
              index: this.$store.state.classroom.maxIndex + 1,
              owner: this.role,
              top: this.$store.getters['classroom/zoomAsset'].asset.y + offsetY + 100,
              left: this.viewportWidth / 2 + this.$store.getters['classroom/zoomAsset'].asset.x + offsetX - 250
            }
          };
          const ext = Object(helpers["getFileExtension"])(item.asset.path);
          let type;

          if ((_this$acceptedFiles = this.acceptedFiles) !== null && _this$acceptedFiles !== void 0 && _this$acceptedFiles.pdfTypes.includes(ext)) {
            type = 'pdf';
          } else if ((_this$acceptedFiles2 = this.acceptedFiles) !== null && _this$acceptedFiles2 !== void 0 && _this$acceptedFiles2.imageTypes.includes(ext)) {
            type = 'image';
          } else if ((_this$acceptedFiles3 = this.acceptedFiles) !== null && _this$acceptedFiles3 !== void 0 && _this$acceptedFiles3.audioTypes.includes(ext)) {
            type = 'audio';
          } else {
            return;
          }

          item.asset.type = type;
          this.$store.commit('classroom/moveAsset', item);
          this.$store.dispatch('classroom/moveAsset', item);
          this.$socket.emit('asset-added', item);
          offsetX += 50;
          offsetY += 50;
        });
      }).catch(e => {
        // @TODO classroom
        // Bugsnag.notify(e)
        throw e;
      });
    }

  }
});
// CONCATENATED MODULE: ./mixins/UploadFiles.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_UploadFilesvue_type_script_lang_js_ = (UploadFilesvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/UploadFiles.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_UploadFilesvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "20b20e0a"
  
)

/* harmony default export */ var UploadFiles = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1239:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1316);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("88facf08", content, true, context)
};

/***/ }),

/***/ 1315:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DropFileArea_vue_vue_type_style_index_0_id_190879a5_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1239);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DropFileArea_vue_vue_type_style_index_0_id_190879a5_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DropFileArea_vue_vue_type_style_index_0_id_190879a5_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DropFileArea_vue_vue_type_style_index_0_id_190879a5_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_DropFileArea_vue_vue_type_style_index_0_id_190879a5_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1316:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".popup-load-files-drop-wrap[data-v-190879a5]{position:relative!important;display:flex;justify-content:center;align-items:center;height:100vh!important;background-color:hsla(0,0%,100%,.9);z-index:999999;pointer-events:none}.popup-load-files-drop-wrap .drop-area--wrapper[data-v-190879a5]{padding:30px}.popup-load-files-drop-wrap .drop-area--wrapper__dropbox-img[data-v-190879a5]{width:100%;height:auto}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1399:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/DropFileArea.vue?vue&type=template&id=190879a5&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{on:{"drop":function($event){$event.preventDefault();return _vm.drop.apply(null, arguments)},"dragleave":function($event){$event.preventDefault();return _vm.dragleave.apply(null, arguments)}}},[_vm._ssrNode("<div class=\"popup-load-files-drop-wrap\""+(_vm._ssrStyle(null,null, { display: (_vm.isDragging) ? '' : 'none' }))+" data-v-190879a5><div class=\"drop-area--wrapper\" data-v-190879a5><img"+(_vm._ssrAttr("src",__webpack_require__(577)))+" alt class=\"drop-area--wrapper__dropbox-img\" data-v-190879a5></div></div>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/classroom/DropFileArea.vue?vue&type=template&id=190879a5&scoped=true&

// EXTERNAL MODULE: ./mixins/UploadFiles.vue + 2 modules
var UploadFiles = __webpack_require__(1205);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/DropFileArea.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var DropFileAreavue_type_script_lang_js_ = ({
  name: 'DropFileArea',
  mixins: [UploadFiles["a" /* default */]],
  props: {
    viewportWidth: {
      type: Number,
      required: true
    }
  },
  computed: {
    isDragging() {
      return this.$store.state.classroom.isDragging;
    }

  },

  mounted() {
    document.addEventListener('dragover', this.dragover);
  },

  beforeDestroy() {
    document.removeEventListener('dragover', this.dragover);
  },

  methods: {
    dragover(e) {
      e.preventDefault();
      this.$store.commit('classroom/isDraggingTrigger', true);
    },

    dragleave() {
      this.$store.commit('classroom/isDraggingTrigger', false);
    },

    drop(e) {
      this.$store.commit('classroom/isDraggingTrigger', false);
      this.uploadFiles(e.dataTransfer.files);
    }

  }
});
// CONCATENATED MODULE: ./components/classroom/DropFileArea.vue?vue&type=script&lang=js&
 /* harmony default export */ var classroom_DropFileAreavue_type_script_lang_js_ = (DropFileAreavue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/classroom/DropFileArea.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1315)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  classroom_DropFileAreavue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "190879a5",
  "143d43b9"
  
)

/* harmony default export */ var DropFileArea = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=classroom-drop-file-area.js.map