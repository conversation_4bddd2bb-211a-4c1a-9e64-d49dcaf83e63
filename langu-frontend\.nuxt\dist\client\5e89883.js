(window.webpackJsonp=window.webpackJsonp||[]).push([[7],{148:function(e,t,n){"use strict";var r=n(1327);t.a=r.a},1839:function(e,t,n){var content=n(2009);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("37cc6ed2",content,!0,{sourceMap:!1})},2007:function(e,t,n){var content=n(2008);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("0769cd4a",content,!0,{sourceMap:!1})},2008:function(e,t,n){var r=n(18)(!1);r.push([e.i,".theme--light.v-calendar-events .v-event-timed{border:1px solid!important}.theme--light.v-calendar-events .v-event-more{background-color:#fff}.theme--light.v-calendar-events .v-event-more.v-outside{background-color:#f7f7f7}.theme--dark.v-calendar-events .v-event-timed{border:1px solid!important}.theme--dark.v-calendar-events .v-event-more{background-color:#303030}.theme--dark.v-calendar-events .v-event-more.v-outside{background-color:#202020}.v-calendar .v-event{line-height:20px;margin-right:-1px;border-radius:4px}.v-calendar .v-event,.v-calendar .v-event-more{position:relative;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:12px;cursor:pointer;z-index:1}.v-calendar .v-event-more{font-weight:700}.v-calendar .v-event-timed-container{position:absolute;top:0;bottom:0;left:0;right:0;margin-right:10px;pointer-events:none}.v-calendar .v-event-timed{position:absolute;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:12px;cursor:pointer;border-radius:4px;pointer-events:all}.v-calendar.v-calendar-events .v-calendar-weekly__head-weekday{margin-right:-1px}.v-calendar.v-calendar-events .v-calendar-weekly__day{overflow:visible;margin-right:-1px}",""]),e.exports=r},2009:function(e,t,n){var r=n(18)(!1);r.push([e.i,".theme--light.v-calendar-weekly{background-color:#fff;border-top:1px solid #e0e0e0;border-left:1px solid #e0e0e0}.theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday{border-right:1px solid #e0e0e0;color:#000}.theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday.v-past{color:rgba(0,0,0,.38)}.theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday.v-outside{background-color:#f7f7f7}.theme--light.v-calendar-weekly .v-calendar-weekly__head-weeknumber{background-color:#f1f3f4;border-right:1px solid #e0e0e0}.theme--light.v-calendar-weekly .v-calendar-weekly__day{border-right:1px solid #e0e0e0;border-bottom:1px solid #e0e0e0;color:#000}.theme--light.v-calendar-weekly .v-calendar-weekly__day.v-outside{background-color:#f7f7f7}.theme--light.v-calendar-weekly .v-calendar-weekly__weeknumber{background-color:#f1f3f4;border-right:1px solid #e0e0e0;border-bottom:1px solid #e0e0e0;color:#000}.theme--dark.v-calendar-weekly{background-color:#303030;border-top:1px solid #9e9e9e;border-left:1px solid #9e9e9e}.theme--dark.v-calendar-weekly .v-calendar-weekly__head-weekday{border-right:1px solid #9e9e9e;color:#fff}.theme--dark.v-calendar-weekly .v-calendar-weekly__head-weekday.v-past{color:hsla(0,0%,100%,.5)}.theme--dark.v-calendar-weekly .v-calendar-weekly__head-weekday.v-outside{background-color:#202020}.theme--dark.v-calendar-weekly .v-calendar-weekly__head-weeknumber{background-color:#202020;border-right:1px solid #9e9e9e}.theme--dark.v-calendar-weekly .v-calendar-weekly__day{border-right:1px solid #9e9e9e;border-bottom:1px solid #9e9e9e;color:#fff}.theme--dark.v-calendar-weekly .v-calendar-weekly__day.v-outside{background-color:#202020}.theme--dark.v-calendar-weekly .v-calendar-weekly__weeknumber{background-color:#202020;border-right:1px solid #9e9e9e;border-bottom:1px solid #9e9e9e;color:#fff}.v-calendar-weekly{width:100%;height:100%;display:flex;flex-direction:column;min-height:0}.v-calendar-weekly__head{display:flex}.v-calendar-weekly__head,.v-calendar-weekly__head-weekday{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-calendar-weekly__head-weekday{flex:1 0 20px;padding:0 2px 6px;font-size:13px;overflow:hidden;text-align:center;text-overflow:ellipsis;text-transform:uppercase;white-space:nowrap}.v-calendar-weekly__head-weeknumber{position:relative;flex:0 0 24px}.v-calendar-weekly__week{display:flex;flex:1;height:unset;min-height:0}.v-calendar-weekly__weeknumber{display:flex;flex:0 0 24px;height:unset;min-height:0;padding-top:14.5px;text-align:center}.v-calendar-weekly__weeknumber>small{width:100%!important}.v-calendar-weekly__day{flex:1;width:0;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;padding:0;min-width:0}.v-calendar-weekly__day.v-present .v-calendar-weekly__day-month{color:currentColor}.v-calendar-weekly__day-label{text-decoration:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;box-shadow:none;text-align:center;margin:14px 0 0}.v-calendar-weekly__day-label .v-btn{font-size:12px;text-transform:none}.v-calendar-weekly__day-month{position:absolute;text-decoration:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;box-shadow:none;top:0;left:36px;height:32px;line-height:32px}",""]),e.exports=r},2010:function(e,t,n){n(11)({target:"Number",stat:!0},{MAX_SAFE_INTEGER:9007199254740991})},2011:function(e,t,n){var content=n(2012);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("1ffbeeca",content,!0,{sourceMap:!1})},2012:function(e,t,n){var r=n(18)(!1);r.push([e.i,'.theme--light.v-calendar-daily{background-color:#fff;border-left:1px solid #e0e0e0;border-top:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__intervals-head{border-right:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__intervals-head:after{background:#e0e0e0;background:linear-gradient(90deg,transparent,#e0e0e0)}.theme--light.v-calendar-daily .v-calendar-daily_head-day{border-right:1px solid #e0e0e0;border-bottom:1px solid #e0e0e0;color:#000}.theme--light.v-calendar-daily .v-calendar-daily_head-day.v-past .v-calendar-daily_head-day-label,.theme--light.v-calendar-daily .v-calendar-daily_head-day.v-past .v-calendar-daily_head-weekday{color:rgba(0,0,0,.38)}.theme--light.v-calendar-daily .v-calendar-daily__intervals-body{border-right:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__intervals-body .v-calendar-daily__interval-text{color:#424242}.theme--light.v-calendar-daily .v-calendar-daily__day{border-right:1px solid #e0e0e0;border-bottom:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__day-interval{border-top:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__day-interval:first-child{border-top:none!important}.theme--light.v-calendar-daily .v-calendar-daily__interval:after{border-top:1px solid #e0e0e0}.theme--dark.v-calendar-daily{background-color:#303030;border-left:1px solid #9e9e9e;border-top:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__intervals-head{border-right:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__intervals-head:after{background:#9e9e9e;background:linear-gradient(90deg,transparent,#9e9e9e)}.theme--dark.v-calendar-daily .v-calendar-daily_head-day{border-right:1px solid #9e9e9e;border-bottom:1px solid #9e9e9e;color:#fff}.theme--dark.v-calendar-daily .v-calendar-daily_head-day.v-past .v-calendar-daily_head-day-label,.theme--dark.v-calendar-daily .v-calendar-daily_head-day.v-past .v-calendar-daily_head-weekday{color:hsla(0,0%,100%,.5)}.theme--dark.v-calendar-daily .v-calendar-daily__intervals-body{border-right:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__intervals-body .v-calendar-daily__interval-text{color:#eee}.theme--dark.v-calendar-daily .v-calendar-daily__day{border-right:1px solid #9e9e9e;border-bottom:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__day-interval{border-top:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__day-interval:first-child{border-top:none!important}.theme--dark.v-calendar-daily .v-calendar-daily__interval:after{border-top:1px solid #9e9e9e}.v-calendar-daily{display:flex;flex-direction:column;overflow:hidden;height:100%}.v-calendar-daily__head{flex:none;display:flex}.v-calendar-daily__intervals-head{flex:none;position:relative}.v-calendar-daily__intervals-head:after{position:absolute;bottom:0;height:1px;left:0;right:0;content:""}.v-calendar-daily_head-day{flex:1 1 auto;width:0;position:relative}.v-calendar-daily_head-weekday{padding:3px 0 0;font-size:11px;text-transform:uppercase}.v-calendar-daily_head-day-label,.v-calendar-daily_head-weekday{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-align:center}.v-calendar-daily_head-day-label{padding:0 0 3px;cursor:pointer}.v-calendar-daily__body{flex:1 1 60%;overflow:hidden;display:flex;position:relative;flex-direction:column}.v-calendar-daily__scroll-area{overflow-y:scroll;flex:1 1 auto;display:flex;align-items:flex-start}.v-calendar-daily__pane{width:100%;overflow-y:hidden;flex:none;display:flex;align-items:flex-start}.v-calendar-daily__day-container{display:flex;flex:1;width:100%;height:100%}.v-calendar-daily__intervals-body{flex:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-calendar-daily__interval{text-align:right;padding-right:8px;border-bottom:none;position:relative}.v-calendar-daily__interval:after{width:8px;position:absolute;height:1px;display:block;content:"";right:0;bottom:-1px}.v-calendar-daily__interval-text{display:block;position:relative;top:-6px;font-size:10px;padding-right:4px}.v-calendar-daily__day{flex:1;width:0;position:relative}',""]),e.exports=r},2013:function(e,t,n){var content=n(2014);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("2b825c72",content,!0,{sourceMap:!1})},2014:function(e,t,n){var r=n(18)(!1);r.push([e.i,".theme--light.v-calendar-category .v-calendar-category__column,.theme--light.v-calendar-category .v-calendar-category__column-header{border-right:1px solid #e0e0e0}.theme--dark.v-calendar-category .v-calendar-category__column,.theme--dark.v-calendar-category .v-calendar-category__column-header{border-right:1px solid #9e9e9e}.v-calendar-category .v-calendar-category__category{text-align:center}.v-calendar-category .v-calendar-daily__day-container .v-calendar-category__columns{position:absolute;height:100%;width:100%;top:0}.v-calendar-category .v-calendar-category__columns{display:flex}.v-calendar-category .v-calendar-category__columns .v-calendar-category__column,.v-calendar-category .v-calendar-category__columns .v-calendar-category__column-header{flex:1 1 auto;width:0;position:relative}",""]),e.exports=r},2195:function(e,t,n){"use strict";n(7),n(8),n(14),n(15);var r=n(25),o=n(2),l=(n(6),n(9),n(39),n(23),n(2007),n(127)),d=(n(35),n(60),n(55),n(12)),c=n(51),h=n(3),v=h.default.extend({name:"localable",props:{locale:String},computed:{currentLocale:function(){return this.locale||this.$vuetify.lang.current}}}),y=h.default.extend({name:"mouse",methods:{getDefaultMouseEventHandlers:function(e,t){var n;return this.getMouseEventHandlers((n={},Object(o.a)(n,"click"+e,{event:"click"}),Object(o.a)(n,"contextmenu"+e,{event:"contextmenu",prevent:!0,result:!1}),Object(o.a)(n,"mousedown"+e,{event:"mousedown"}),Object(o.a)(n,"mousemove"+e,{event:"mousemove"}),Object(o.a)(n,"mouseup"+e,{event:"mouseup"}),Object(o.a)(n,"mouseenter"+e,{event:"mouseenter"}),Object(o.a)(n,"mouseleave"+e,{event:"mouseleave"}),Object(o.a)(n,"touchstart"+e,{event:"touchstart"}),Object(o.a)(n,"touchmove"+e,{event:"touchmove"}),Object(o.a)(n,"touchend"+e,{event:"touchend"}),n),t)},getMouseEventHandlers:function(e,t){var n=this,r={},o=function(o){var l=e[o];if(!n.$listeners[o])return"continue";var d=(l.passive?"&":(l.once?"~":"")+(l.capture?"!":""))+l.event,c=function(e){var r=e;return(void 0===l.button||r.buttons>0&&r.button===l.button)&&(l.prevent&&e.preventDefault(),l.stop&&e.stopPropagation(),n.$emit(o,t(e))),l.result};d in r?Array.isArray(r[d])?r[d].push(c):r[d]=[r[d],c]:r[d]=c};for(var l in e)o(l);return r}}}),f=n(36);function m(e,t,n){var r=7+t-n;return-((7+function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return e<100&&e>=0?(t=new Date(Date.UTC(e,n,r)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC(e,n,r)),t}(e,0,r).getUTCDay()-t)%7)+r-1}function k(e,t,n){var r=m(e,t,n),o=m(e+1,t,n);return((x(e)?366:365)-r+o)/7}function w(e,t,n,r,o){var l=m(e,r,o),d=Math.ceil((function(e,t,n,r){var o=[0,31,59,90,120,151,181,212,243,273,304,334][t];return t>1&&x(e)&&o++,o+n}(e,t,n)-l)/7);return d<1?d+k(e-1,r,o):d>k(e,r,o)?d-k(e,r,o):d}function x(e){return e%4==0&&e%100!=0||e%400==0}var _=/^(\d{4})-(\d{1,2})(-(\d{1,2}))?([^\d]+(\d{1,2}))?(:(\d{1,2}))?(:(\d{1,2}))?$/,O=/(\d\d?)(:(\d\d?)|)(:(\d\d?)|)/,D=[0,31,28,31,30,31,30,31,31,30,31,30,31],E=[0,31,29,31,30,31,30,31,31,30,31,30,31];function j(e){var t=R(e);return t.day=1,Y(t),B(t),t}function C(e){var t=R(e);return t.day=L(t.year,t.month),Y(t),B(t),t}function T(input){if("number"==typeof input)return input;if("string"==typeof input){var e=O.exec(input);return!!e&&60*parseInt(e[1])+parseInt(e[3]||0)}return"object"===Object(r.a)(input)&&("number"==typeof input.hour&&"number"==typeof input.minute&&60*input.hour+input.minute)}function S(input){return"number"==typeof input&&isFinite(input)||"string"==typeof input&&!!_.exec(input)||input instanceof Date}function I(input){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=arguments.length>2?arguments[2]:void 0;if("number"==typeof input&&isFinite(input)&&(input=new Date(input)),input instanceof Date){var n=F(input);return t&&$(n,t,n.hasTime),n}if("string"!=typeof input){if(e)throw new Error("".concat(input," is not a valid timestamp. It must be a Date, number of seconds since Epoch, or a string in the format of YYYY-MM-DD or YYYY-MM-DD hh:mm. Zero-padding is optional and seconds are ignored."));return null}var r=_.exec(input);if(!r){if(e)throw new Error("".concat(input," is not a valid timestamp. It must be a Date, number of seconds since Epoch, or a string in the format of YYYY-MM-DD or YYYY-MM-DD hh:mm. Zero-padding is optional and seconds are ignored."));return null}var o={date:input,time:"",year:parseInt(r[1]),month:parseInt(r[2]),day:parseInt(r[4])||1,hour:parseInt(r[6])||0,minute:parseInt(r[8])||0,weekday:0,hasDay:!!r[4],hasTime:!(!r[6]||!r[8]),past:!1,present:!1,future:!1};return Y(o),B(o),t&&$(o,t,o.hasTime),o}function F(e){return B({date:"",time:"",year:e.getFullYear(),month:e.getMonth()+1,day:e.getDate(),weekday:e.getDay(),hour:e.getHours(),minute:e.getMinutes(),hasDay:!0,hasTime:!0,past:!1,present:!0,future:!1})}function M(e){return 1e4*e.year+100*e.month+e.day}function P(e){return 100*e.hour+e.minute}function H(e){return 1e4*M(e)+P(e)}function $(e,t){var time=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=M(t),b=M(e),n=a===b;return e.hasTime&&time&&n&&(n=(a=P(t))===(b=P(e))),e.past=b<a,e.present=n,e.future=b>a,e}function W(input){return input instanceof Date||"number"==typeof input&&isFinite(input)}function N(e,t,n){return e.hasTime!==t&&(e.hasTime=t,t||(e.hour=23,e.minute=59,e.time=U(e)),n&&$(e,n,e.hasTime)),e}function A(e,t,n){return e.hasTime=!0,e.hour=Math.floor(t/60),e.minute=t%60,e.time=U(e),n&&$(e,n,!0),e}function Y(e){return e.weekday=function(e){if(e.hasDay){var t=Math.floor,n=e.day,r=(e.month+9)%12+1,o=t(e.year/100),l=e.year%100-(e.month<=2?1:0);return((n+t(2.6*r-.2)-2*o+l+t(l/4)+t(o/4))%7+7)%7}return e.weekday}(e),e}function B(e){return e.time=U(e),e.date=function(e){var t="".concat(z(e.year,4),"-").concat(z(e.month,2));e.hasDay&&(t+="-".concat(z(e.day,2)));return t}(e),e}function L(e,t){return x(e)?E[t]:D[t]}function R(e){return{date:e.date,time:e.time,year:e.year,month:e.month,day:e.day,weekday:e.weekday,hour:e.hour,minute:e.minute,hasDay:e.hasDay,hasTime:e.hasTime,past:e.past,present:e.present,future:e.future}}function z(e,t){for(var n=String(e);n.length<t;)n="0"+n;return n}function U(e){return e.hasTime?"".concat(z(e.hour,2),":").concat(z(e.minute,2)):""}function Z(e){return e.day++,e.weekday=(e.weekday+1)%7,e.day>28&&e.day>L(e.year,e.month)&&(e.day=1,e.month++,e.month>12&&(e.month=1,e.year++)),e}function V(e){return e.day--,e.weekday=(e.weekday+6)%7,e.day<1&&(e.month--,e.month<1&&(e.year--,e.month=12),e.day=L(e.year,e.month)),e}function G(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Z,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;--n>=0;)t(e);return e}function J(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Z,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:6;e.weekday!==t&&--r>=0;)n(e);return e}function X(e){var time="".concat(z(e.hour,2),":").concat(z(e.minute,2)),t=e.date;return new Date("".concat(t,"T").concat(time,":00+00:00"))}function K(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:42,l=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0,d=M(t),c=[],h=R(e),v=0,y=v===d;if(d<M(e))throw new Error("End date is earlier than start date.");for(;(!y||c.length<l)&&c.length<o;)if(v=M(h),y=y||v===d,0!==r[h.weekday]){var f=R(h);B(f),$(f,n),c.push(f),h=G(h,Z,r[h.weekday])}else h=Z(h);if(!c.length)throw new Error("No dates found using specified start date, end date, and weekdays.");return c}function Q(e,t){return"undefined"==typeof Intl||void 0===Intl.DateTimeFormat?function(e,t){return""}:function(n,r){try{return new Intl.DateTimeFormat(e||void 0,t(n,r)).format(X(n))}catch(e){return""}}}var ee=h.default.extend({name:"times",props:{now:{type:String,validator:S}},data:function(){return{times:{now:I("0000-00-00 00:00",!0),today:I("0000-00-00",!0)}}},computed:{parsedNow:function(){return this.now?I(this.now,!0):null}},watch:{parsedNow:"updateTimes"},created:function(){this.updateTimes(),this.setPresent()},methods:{setPresent:function(){this.times.now.present=this.times.today.present=!0,this.times.now.past=this.times.today.past=!1,this.times.now.future=this.times.today.future=!1},updateTimes:function(){var e=this.parsedNow||this.getNow();this.updateDay(e,this.times.now),this.updateTime(e,this.times.now),this.updateDay(e,this.times.today)},getNow:function(){return F(new Date)},updateDay:function(e,t){e.date!==t.date&&(t.year=e.year,t.month=e.month,t.day=e.day,t.weekday=e.weekday,t.date=e.date)},updateTime:function(e,t){e.time!==t.time&&(t.hour=e.hour,t.minute=e.minute,t.time=e.time)}}}),te=n(153),ne=(n(31),n(20),n(40),n(64),n(37),n(66),n(82),n(44),n(13));function re(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=e.map((function(e){return{event:e,columnCount:0,column:0,left:0,width:100}}));return n.sort((function(a,b){return Math.max(t,a.event.startTimestampIdentifier)-Math.max(t,b.event.startTimestampIdentifier)||b.event.endTimestampIdentifier-a.event.endTimestampIdentifier})),n}function ae(e,t,n,r){var o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];return o?!(e>=r||t<=n):!(e>r||t<n)}function ie(e){e.forEach((function(t){t.visuals.forEach((function(t){t.columnCount=e.length}))}))}function oe(e){return[e.startTimestampIdentifier,e.endTimestampIdentifier]}function se(e){return[e.startIdentifier,e.endIdentifier]}function le(e,t){return[Math.max(t,e.startTimestampIdentifier),Math.min(t+864e5,e.endTimestampIdentifier)]}function de(e,t,n,r){for(var i=0;i<e.length;i++){var o=e[i],l=!1;if(ae(t,n,o.start,o.end,r))for(var d=0;d<o.visuals.length;d++){var c=o.visuals[d],h=r?oe(c.event):se(c.event),v=Object(ne.a)(h,2);if(ae(t,n,v[0],v[1],r)){l=!0;break}}if(!l)return i}return-1}function ce(e){var t={groups:[],min:-1,max:-1,reset:function(){t.groups=[],t.min=t.max=-1},getVisuals:function(n,r,o){var l=arguments.length>3&&void 0!==arguments[3]&&arguments[3];(n.weekday===e||l)&&t.reset();var d=H(n),c=re(r,d);return c.forEach((function(e){var n=o?oe(e.event):se(e.event),r=Object(ne.a)(n,2),l=r[0],d=r[1];t.groups.length>0&&!ae(l,d,t.min,t.max,o)&&(ie(t.groups),t.reset());var c=de(t.groups,l,d,o);-1===c&&(c=t.groups.length,t.groups.push({start:l,end:d,visuals:[]}));var h=t.groups[c];h.visuals.push(e),h.start=Math.min(h.start,l),h.end=Math.max(h.end,d),e.column=c,-1===t.min?(t.min=l,t.max=d):(t.min=Math.min(t.min,l),t.max=Math.max(t.max,d))})),ie(t.groups),o&&t.reset(),c}};return t}function ue(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return he(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return he(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,l=!0,d=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return l=e.done,e},e:function(e){d=!0,o=e},f:function(){try{l||null==n.return||n.return()}finally{if(d)throw o}}}}function he(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}var ve=100;function ye(e,t){var n,r=ue(e);try{for(r.s();!(n=r.n()).done;){var o=n.value,l=o.visual,d=o.parent,c=we(o)+1,h=d?d.visual.left:0,v=ve-h,y=Math.min(5,ve/c),f=fe(o,e),m=v/(c-o.index+1),k=v/(c-o.index+(o.sibling?1:0))*f;d&&(l.left=o.sibling?h+m:h+y),l.width=be(o,e,t)?ve-l.left:Math.min(ve-l.left,1.7*k)}}catch(e){r.e(e)}finally{r.f()}}function fe(e,t){if(!e.children.length)return 1;var n=e.index+t.length;return e.children.reduce((function(e,t){return Math.min(e,t.index)}),n)-e.index}function pe(e,t){var n=function(e,t){var n,r=[],o=ue(t);try{for(o.s();!(n=o.n()).done;){var l=n.value;ae(e.start,e.end,l.start,l.end)&&r.push(l.index)}}catch(e){o.e(e)}finally{o.f()}return r}(e,t);n.sort();for(var i=0;i<n.length;i++)if(i<n[i])return i;return!1}function me(e,t,n,r){var o,l=arguments.length>4&&void 0!==arguments[4]&&arguments[4],d=[],c=ue(t);try{for(c.s();!(o=c.n()).done;){var h=o.value;h.index>=n&&h.index<=r&&ae(e.start,e.end,h.start,h.end)&&d.push(h)}}catch(e){c.e(e)}finally{c.f()}if(l&&d.length>0){var v=d.reduce((function(e,t){return Math.min(e,t.index)}),d[0].index);return d.filter((function(e){return e.index===v}))}return d}function ge(e,t){var n,r=null,o=ue(t);try{for(o.s();!(n=o.n()).done;){var l=n.value;ae(e.start,e.end,l.start,l.end)&&(null===r||l.index>r.index)&&(r=l)}}catch(e){o.e(e)}finally{o.f()}return r}function be(e,t,n){var r,o=ue(t);try{for(o.s();!(r=o.n()).done;){var l=r.value;if(l!==e&&l.index>e.index&&ae(e.start,xe(e.start,n),l.start,l.end))return!1}}catch(e){o.e(e)}finally{o.f()}return!0}function ke(e,t){var n=le(e.event,t),r=Object(ne.a)(n,2);return{parent:null,sibling:!0,index:0,visual:e,start:r[0],end:r[1],children:[]}}function we(e){var t,n=e.index,r=ue(e.children);try{for(r.s();!(t=r.n()).done;){var o=we(t.value);o>n&&(n=o)}}catch(e){r.e(e)}finally{r.f()}return n}function xe(e,t){var n=e%100,r=n+t;return e-n+100*Math.floor(r/60)+r%60}var _e={stack:function(e,t,n){var r=ce(t);return function(e,t,o,l){if(!o)return r.getVisuals(e,t,o,l);var d,c=H(e),h=re(t,c),v=function(e,t){var n,r=[],o=ue(e);try{for(o.s();!(n=o.n()).done;){var l,d=n.value,c=le(d.event,t),h=Object(ne.a)(c,2),v=h[0],y=h[1],f=!1,m=ue(r);try{for(m.s();!(l=m.n()).done;){var k=l.value;if(ae(v,y,k.start,k.end)){k.visuals.push(d),k.end=Math.max(k.end,y),f=!0;break}}}catch(e){m.e(e)}finally{m.f()}f||r.push({start:v,end:y,visuals:[d]})}}catch(e){o.e(e)}finally{o.f()}return r}(h,c),y=ue(v);try{for(y.s();!(d=y.n()).done;){var f,m=d.value,k=[],w=ue(m.visuals);try{for(w.s();!(f=w.n()).done;){var x=ke(f.value,c),_=pe(x,k);if(!1===_){var O=ge(x,k);O&&(x.parent=O,x.sibling=ae(x.start,x.end,O.start,xe(O.start,n)),x.index=O.index+1,O.children.push(x))}else{var D=me(x,k,_-1,_-1),E=Object(ne.a)(D,1)[0],j=me(x,k,_+1,_+k.length,!0);x.children=j,x.index=_,E&&(x.parent=E,x.sibling=ae(x.start,x.end,E.start,xe(E.start,n)),E.children.push(x));var C,T=ue(j);try{for(T.s();!(C=T.n()).done;){var S=C.value;S.parent===E&&(S.parent=x),S.index-x.index<=1&&x.sibling&&ae(x.start,xe(x.start,n),S.start,S.end)&&(S.sibling=!0)}}catch(e){T.e(e)}finally{T.f()}}k.push(x)}}catch(e){w.e(e)}finally{w.f()}ye(k,n)}}catch(e){y.e(e)}finally{y.f()}return h.sort((function(a,b){return a.left-b.left||a.event.startTimestampIdentifier-b.event.startTimestampIdentifier})),h}},column:function(e,t,n){var r=ce(t);return function(e,t,n,o){var l=r.getVisuals(e,t,n,o);return n&&l.forEach((function(e){e.left=100*e.column/e.columnCount,e.width=100/e.columnCount})),l}}},Oe={base:{start:{type:[String,Number,Date],validate:S,default:function(){return F(new Date).date}},end:{type:[String,Number,Date],validate:S},weekdays:{type:[Array,String],default:function(){return[0,1,2,3,4,5,6]},validate:function(input){"string"==typeof input&&(input=input.split(","));if(Array.isArray(input)){var e=input.map((function(e){return parseInt(e)}));if(e.length>7||0===e.length)return!1;for(var t={},n=!1,i=0;i<e.length;i++){var r=e[i];if(!isFinite(r)||r<0||r>=7)return!1;if(i>0){var o=r-e[i-1];if(o<0){if(n)return!1;n=!0}else if(0===o)return!1}if(t[r])return!1;t[r]=!0}return!0}return!1}},hideHeader:{type:Boolean},shortWeekdays:{type:Boolean,default:!0},weekdayFormat:{type:Function,default:null},dayFormat:{type:Function,default:null}},intervals:{maxDays:{type:Number,default:7},shortIntervals:{type:Boolean,default:!0},intervalHeight:{type:[Number,String],default:48,validate:De},intervalWidth:{type:[Number,String],default:60,validate:De},intervalMinutes:{type:[Number,String],default:60,validate:De},firstInterval:{type:[Number,String],default:0,validate:De},firstTime:{type:[Number,String,Object],validate:function(input){return"number"==typeof input&&isFinite(input)||!!O.exec(input)||"object"===Object(r.a)(input)&&isFinite(input.hour)&&isFinite(input.minute)}},intervalCount:{type:[Number,String],default:24,validate:De},intervalFormat:{type:Function,default:null},intervalStyle:{type:Function,default:null},showIntervalLabel:{type:Function,default:null}},weeks:{localeFirstDayOfYear:{type:[String,Number],default:0},minWeeks:{validate:De,default:1},shortMonths:{type:Boolean,default:!0},showMonthOnFirst:{type:Boolean,default:!0},showWeek:Boolean,monthFormat:{type:Function,default:null}},calendar:{type:{type:String,default:"month"},value:{type:[String,Number,Date],validate:S}},category:{categories:{type:[Array,String],default:""},categoryText:{type:[String,Function]},categoryHideDynamic:{type:Boolean},categoryShowAll:{type:Boolean},categoryForInvalid:{type:String,default:""},categoryDays:{type:[Number,String],default:1,validate:function(e){return isFinite(parseInt(e))&&parseInt(e)>0}}},events:{events:{type:Array,default:function(){return[]}},eventStart:{type:String,default:"start"},eventEnd:{type:String,default:"end"},eventTimed:{type:[String,Function],default:"timed"},eventCategory:{type:[String,Function],default:"category"},eventHeight:{type:Number,default:20},eventColor:{type:[String,Function],default:"primary"},eventTextColor:{type:[String,Function],default:"white"},eventName:{type:[String,Function],default:"name"},eventOverlapThreshold:{type:[String,Number],default:60},eventOverlapMode:{type:[String,Function],default:"stack",validate:function(e){return e in _e||"function"==typeof e}},eventMore:{type:Boolean,default:!0},eventMoreText:{type:String,default:"$vuetify.calendar.moreEvents"},eventRipple:{type:[Boolean,Object],default:null},eventMarginBottom:{type:Number,default:1}}};function De(input){return isFinite(parseInt(input))}var Ee=Object(d.a)(c.a,v,y,f.a,ee).extend({name:"calendar-base",directives:{Resize:te.a},props:Oe.base,computed:{parsedWeekdays:function(){return Array.isArray(this.weekdays)?this.weekdays:(this.weekdays||"").split(",").map((function(e){return parseInt(e,10)}))},weekdaySkips:function(){return function(e){for(var t=[1,1,1,1,1,1,1],n=[0,0,0,0,0,0,0],i=0;i<e.length;i++)n[e[i]]=1;for(var r=0;r<7;r++){for(var o=1,l=1;l<7&&!n[(r+l)%7];l++)o++;t[r]=n[r]*o}return t}(this.parsedWeekdays)},weekdaySkipsReverse:function(){var e=this.weekdaySkips.slice();return e.reverse(),e},parsedStart:function(){return I(this.start,!0)},parsedEnd:function(){var e=this.parsedStart,t=this.end&&I(this.end)||e;return H(t)<H(e)?e:t},days:function(){return K(this.parsedStart,this.parsedEnd,this.times.today,this.weekdaySkips)},dayFormatter:function(){if(this.dayFormat)return this.dayFormat;var e={timeZone:"UTC",day:"numeric"};return Q(this.currentLocale,(function(t,n){return e}))},weekdayFormatter:function(){if(this.weekdayFormat)return this.weekdayFormat;var e={timeZone:"UTC",weekday:"long"},t={timeZone:"UTC",weekday:"short"};return Q(this.currentLocale,(function(n,r){return r?t:e}))}},methods:{getRelativeClasses:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return{"v-present":e.present,"v-past":e.past,"v-future":e.future,"v-outside":t}},getStartOfWeek:function(e){return function(e,t,n){var r=R(e);return J(r,t[0],V),B(r),n&&$(r,n,r.hasTime),r}(e,this.parsedWeekdays,this.times.today)},getEndOfWeek:function(e){return function(e,t,n){var r=R(e);return J(r,t[t.length-1]),B(r),n&&$(r,n,r.hasTime),r}(e,this.parsedWeekdays,this.times.today)},getFormatter:function(e){return Q(this.locale,(function(t,n){return e}))}}}),je=n(1);function Ce(input,e,t,n){var r=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=arguments.length>5&&void 0!==arguments[5]&&arguments[5],l=input[t],d=input[n],c=I(l,!0),h=d?I(d,!0):c,v=W(l)?N(c,r):c,y=W(d)?N(h,r):h,f=M(v),m=H(v),k=M(y),w=v.hasTime?0:2359,x=H(y)+w,_=!v.hasTime;return{input:input,start:v,startIdentifier:f,startTimestampIdentifier:m,end:y,endIdentifier:k,endTimestampIdentifier:x,allDay:_,index:e,category:o}}function Te(e,t){return t>=e.startIdentifier&&t<=e.endIdentifier&&1e4*t!==e.endTimestampIdentifier}function Se(e,t,n,r){return n===e.startIdentifier||r===t.weekday&&Te(e,n)}function Ie(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,n)}return t}function Fe(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?Ie(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):Ie(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var Me=Ee.extend({name:"calendar-with-events",directives:{ripple:l.a},props:Fe(Fe(Fe({},Oe.events),Oe.calendar),Oe.category),computed:{noEvents:function(){return 0===this.events.length},parsedEvents:function(){return this.events.map(this.parseEvent)},parsedEventOverlapThreshold:function(){return parseInt(this.eventOverlapThreshold)},eventTimedFunction:function(){var e=this;return"function"==typeof this.eventTimed?this.eventTimed:function(t){return!!t[e.eventTimed]}},eventCategoryFunction:function(){var e=this;return"function"==typeof this.eventCategory?this.eventCategory:function(t){return t[e.eventCategory]}},eventTextColorFunction:function(){var e=this;return"function"==typeof this.eventTextColor?this.eventTextColor:function(){return e.eventTextColor}},eventNameFunction:function(){var e=this;return"function"==typeof this.eventName?this.eventName:function(t,n){return Object(je.i)(t.input[e.eventName]||"")}},eventModeFunction:function(){return"function"==typeof this.eventOverlapMode?this.eventOverlapMode:_e[this.eventOverlapMode]},eventWeekdays:function(){return this.parsedWeekdays},categoryMode:function(){return"category"===this.type}},methods:{eventColorFunction:function(e){return"function"==typeof this.eventColor?this.eventColor(e):e.color||this.eventColor},parseEvent:function(input){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Ce(input,e,this.eventStart,this.eventEnd,this.eventTimedFunction(input),!!this.categoryMode&&this.eventCategoryFunction(input))},formatTime:function(e,t){return this.getFormatter({timeZone:"UTC",hour:"numeric",minute:e.minute>0?"numeric":void 0})(e,!0)},updateEventVisibility:function(){if(!this.noEvents&&this.eventMore){var e=this.eventHeight,t=this.getEventsMap();for(var n in t){var r=t[n],o=r.parent,l=r.events,d=r.more;if(!d)break;for(var c=o.getBoundingClientRect(),h=l.length-1,v=!1,y=0,i=0;i<=h;i++){if(!v){var f=l[i].getBoundingClientRect();v=i===h?f.bottom>c.bottom:f.bottom+e>c.bottom}v&&(l[i].style.display="none",y++)}v?(d.style.display="",d.innerHTML=this.$vuetify.lang.t(this.eventMoreText,y)):d.style.display="none"}}},getEventsMap:function(){var e={},t=this.$refs.events;return t&&t.forEach?(t.forEach((function(t){var n=t.getAttribute("data-date");t.parentElement&&n&&(n in e||(e[n]={parent:t.parentElement,more:null,events:[]}),t.getAttribute("data-more")?e[n].more=t:(e[n].events.push(t),t.style.display=""))})),e):e},genDayEvent:function(e,t){var n=e.event,r=this.eventHeight,o=this.eventMarginBottom,l=M(t),d=t.week,c=l===n.startIdentifier,h=l===n.endIdentifier,v=95;if(!this.categoryMode)for(var i=t.index+1;i<d.length;i++){var y=M(d[i]);if(!(n.endIdentifier>=y)){h=!0;break}v+=100,h=h||y===n.endIdentifier}var f={eventParsed:n,day:t,start:c,end:h,timed:!1};return this.genEvent(n,f,!1,{staticClass:"v-event",class:{"v-event-start":c,"v-event-end":h},style:{height:"".concat(r,"px"),width:"".concat(v,"%"),"margin-bottom":"".concat(o,"px")},attrs:{"data-date":t.date},key:n.index,ref:"events",refInFor:!0})},genTimedEvent:function(e,t){var n=e.event,r=e.left,o=e.width;if(t.timeDelta(n.end)<=0||t.timeDelta(n.start)>=1)return!1;var l=M(t),d=n.startIdentifier>=l,c=n.endIdentifier>l,h=d?t.timeToY(n.start):0,v=c?t.timeToY(1440):t.timeToY(n.end),y=Math.max(this.eventHeight,v-h),f={eventParsed:n,day:t,start:d,end:c,timed:!0};return this.genEvent(n,f,!0,{staticClass:"v-event-timed",style:{top:"".concat(h,"px"),height:"".concat(y,"px"),left:"".concat(r,"%"),width:"".concat(o,"%")}})},genEvent:function(e,t,n,data){var r,o,l,d=this,slot=this.$scopedSlots.event,text=this.eventTextColorFunction(e.input),c=this.eventColorFunction(e.input),h=e.start.hour<12&&e.end.hour>=12,v=(o=e.start,525600*((l=e.end).year-o.year)+43800*(l.month-o.month)+1440*(l.day-o.day)+60*(l.hour-o.hour)+(l.minute-o.minute)<=this.parsedEventOverlapThreshold),y=this.formatTime,f=function(){return y(e.start,h)+" - "+y(e.end,!0)},m=function(){var t=d.eventNameFunction(e,n);if(e.start.hasTime){if(n){var time=f(),r=v?", ":"<br>";return"<strong>".concat(t,"</strong>").concat(r).concat(time)}var o=y(e.start,!0);return"<strong>".concat(o,"</strong> ").concat(t)}return t},k=Fe(Fe({},t),{},{event:e.input,outside:t.day.outside,singline:v,overlapsNoon:h,formatTime:y,timeSummary:f,eventSummary:m});return this.$createElement("div",this.setTextColor(text,this.setBackgroundColor(c,Fe({on:this.getDefaultMouseEventHandlers(":event",(function(e){return Fe(Fe({},k),{},{nativeEvent:e})})),directives:[{name:"ripple",value:null==(r=this.eventRipple)||r}]},data))),slot?slot(k):[this.genName(m)])},genName:function(e){return this.$createElement("div",{staticClass:"pl-1",domProps:{innerHTML:e()}})},genPlaceholder:function(e){var t=this.eventHeight+this.eventMarginBottom;return this.$createElement("div",{style:{height:"".concat(t,"px")},attrs:{"data-date":e.date},ref:"events",refInFor:!0})},genMore:function(e){var t,n=this,r=this.eventHeight,o=this.eventMarginBottom;return this.$createElement("div",{staticClass:"v-event-more pl-1",class:{"v-outside":e.outside},attrs:{"data-date":e.date,"data-more":1},directives:[{name:"ripple",value:null==(t=this.eventRipple)||t}],on:{click:function(){return n.$emit("click:more",e)}},style:{display:"none",height:"".concat(r,"px"),"margin-bottom":"".concat(o,"px")},ref:"events",refInFor:!0})},getVisibleEvents:function(){var e=M(this.days[0]),t=M(this.days[this.days.length-1]);return this.parsedEvents.filter((function(n){return function(e,t,n){return t<=e.endIdentifier&&n>=e.startIdentifier}(n,e,t)}))},isEventForCategory:function(e,t){return!this.categoryMode||"object"===Object(r.a)(t)&&t.categoryName&&t.categoryName===e.category||"string"!=typeof e.category&&null===t},getEventsForDay:function(e){var t=M(e),n=this.eventWeekdays[0];return this.parsedEvents.filter((function(r){return Se(r,e,t,n)}))},getEventsForDayAll:function(e){var t=this,n=M(e),r=this.eventWeekdays[0];return this.parsedEvents.filter((function(o){return o.allDay&&(t.categoryMode?Te(o,n):Se(o,e,n,r))&&t.isEventForCategory(o,e.category)}))},getEventsForDayTimed:function(e){var t=this,n=M(e);return this.parsedEvents.filter((function(r){return!r.allDay&&Te(r,n)&&t.isEventForCategory(r,e.category)}))},getScopedSlots:function(){var e=this;if(this.noEvents)return Fe({},this.$scopedSlots);var t=this.eventModeFunction(this.parsedEvents,this.eventWeekdays[0],this.parsedEventOverlapThreshold),n=function(input){return!!input},r=function(r,o,l,d){var c=o(r),h=t(r,c,d,e.categoryMode);if(d)return h.map((function(e){return l(e,r)})).filter(n);var v=[];return h.forEach((function(t,n){for(;v.length<t.column;)v.push(e.genPlaceholder(r));var o=l(t,r);o&&v.push(o)})),v},o=this.$scopedSlots,l=o.day,d=o["day-header"],c=o["day-body"];return Fe(Fe({},o),{},{day:function(t){var n=r(t,e.getEventsForDay,e.genDayEvent,!1);if(n&&n.length>0&&e.eventMore&&n.push(e.genMore(t)),l){var slot=l(t);slot&&(n=n?n.concat(slot):slot)}return n},"day-header":function(t){var n=r(t,e.getEventsForDayAll,e.genDayEvent,!1);if(d){var slot=d(t);slot&&(n=n?n.concat(slot):slot)}return n},"day-body":function(t){var n=r(t,e.getEventsForDayTimed,e.genTimedEvent,!0),o=[e.$createElement("div",{staticClass:"v-event-timed-container"},n)];if(c){var slot=c(t);slot&&(o=o.concat(slot))}return o}})}}}),Pe=(n(1839),n(28)),He=(n(2010),n(148));function $e(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,n)}return t}var We=Ee.extend({name:"v-calendar-weekly",props:Oe.weeks,computed:{staticClass:function(){return"v-calendar-weekly"},classes:function(){return this.themeClasses},parsedMinWeeks:function(){return parseInt(this.minWeeks)},days:function(){var e=this.parsedMinWeeks*this.parsedWeekdays.length;return K(this.getStartOfWeek(this.parsedStart),this.getEndOfWeek(this.parsedEnd),this.times.today,this.weekdaySkips,Number.MAX_SAFE_INTEGER,e)},todayWeek:function(){var e=this.times.today;return K(this.getStartOfWeek(e),this.getEndOfWeek(e),e,this.weekdaySkips,this.parsedWeekdays.length,this.parsedWeekdays.length)},monthFormatter:function(){if(this.monthFormat)return this.monthFormat;var e={timeZone:"UTC",month:"long"},t={timeZone:"UTC",month:"short"};return Q(this.currentLocale,(function(n,r){return r?t:e}))}},methods:{isOutside:function(e){var t=M(e);return t<M(this.parsedStart)||t>M(this.parsedEnd)},genHead:function(){return this.$createElement("div",{staticClass:"v-calendar-weekly__head"},this.genHeadDays())},genHeadDays:function(){var header=this.todayWeek.map(this.genHeadDay);return this.showWeek&&header.unshift(this.$createElement("div",{staticClass:"v-calendar-weekly__head-weeknumber"})),header},genHeadDay:function(e,t){var n=this.isOutside(this.days[t]),r=e.present?this.color:void 0;return this.$createElement("div",this.setTextColor(r,{key:e.date,staticClass:"v-calendar-weekly__head-weekday",class:this.getRelativeClasses(e,n)}),this.weekdayFormatter(e,this.shortWeekdays))},genWeeks:function(){for(var e=this.days,t=this.parsedWeekdays.length,n=[],i=0;i<e.length;i+=t)n.push(this.genWeek(e.slice(i,i+t),this.getWeekNumber(e[i])));return n},genWeek:function(e,t){var n=this,r=e.map((function(t,r){return n.genDay(t,r,e)}));return this.showWeek&&r.unshift(this.genWeekNumber(t)),this.$createElement("div",{key:e[0].date,staticClass:"v-calendar-weekly__week"},r)},getWeekNumber:function(e){return w(e.year,e.month-1,e.day,this.parsedWeekdays[0],parseInt(this.localeFirstDayOfYear))},genWeekNumber:function(e){return this.$createElement("div",{staticClass:"v-calendar-weekly__weeknumber"},[this.$createElement("small",String(e))])},genDay:function(e,t,n){var r=this.isOutside(e);return this.$createElement("div",{key:e.date,staticClass:"v-calendar-weekly__day",class:this.getRelativeClasses(e,r),on:this.getDefaultMouseEventHandlers(":day",(function(t){return e}))},[this.genDayLabel(e)].concat(Object(Pe.a)(Object(je.n)(this,"day",(function(){return function(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?$e(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):$e(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}({outside:r,index:t,week:n},e)}))||[])))},genDayLabel:function(e){return this.$createElement("div",{staticClass:"v-calendar-weekly__day-label"},Object(je.n)(this,"day-label",e)||[this.genDayLabelButton(e)])},genDayLabelButton:function(e){var t=e.present?this.color:"transparent",n=1===e.day&&this.showMonthOnFirst;return this.$createElement(He.a,{props:{color:t,fab:!0,depressed:!0,small:!0},on:this.getMouseEventHandlers({"click:date":{event:"click",stop:!0},"contextmenu:date":{event:"contextmenu",stop:!0,prevent:!0,result:!1}},(function(t){return e}))},n?this.monthFormatter(e,this.shortMonths)+" "+this.dayFormatter(e,!1):this.dayFormatter(e,!1))},genDayMonth:function(e){var t=e.present?this.color:void 0;return this.$createElement("div",this.setTextColor(t,{staticClass:"v-calendar-weekly__day-month"}),Object(je.n)(this,"day-month",e)||this.monthFormatter(e,this.shortMonths))}},render:function(e){return e("div",{staticClass:this.staticClass,class:this.classes,on:{dragstart:function(e){e.preventDefault()}}},[this.hideHeader?"":this.genHead()].concat(Object(Pe.a)(this.genWeeks())))}}),Ne=We.extend({name:"v-calendar-monthly",computed:{staticClass:function(){return"v-calendar-monthly v-calendar-weekly"},parsedStart:function(){return j(I(this.start,!0))},parsedEnd:function(){return C(I(this.end,!0))}}});n(2011);function Ae(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,n)}return t}function Ye(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?Ae(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):Ae(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var Be=Ee.extend({name:"calendar-with-intervals",props:Oe.intervals,computed:{parsedFirstInterval:function(){return parseInt(this.firstInterval)},parsedIntervalMinutes:function(){return parseInt(this.intervalMinutes)},parsedIntervalCount:function(){return parseInt(this.intervalCount)},parsedIntervalHeight:function(){return parseFloat(this.intervalHeight)},parsedFirstTime:function(){return T(this.firstTime)},firstMinute:function(){var time=this.parsedFirstTime;return!1!==time&&time>=0&&time<=1440?time:this.parsedFirstInterval*this.parsedIntervalMinutes},bodyHeight:function(){return this.parsedIntervalCount*this.parsedIntervalHeight},days:function(){return K(this.parsedStart,this.parsedEnd,this.times.today,this.weekdaySkips,this.maxDays)},intervals:function(){var e=this.days,t=this.firstMinute,n=this.parsedIntervalMinutes,r=this.parsedIntervalCount,o=this.times.now;return e.map((function(e){return function(e,t,n,r,o){for(var l=[],i=0;i<r;i++){var d=t+i*n,c=R(e);l.push(A(c,d,o))}return l}(e,t,n,r,o)}))},intervalFormatter:function(){if(this.intervalFormat)return this.intervalFormat;var e={timeZone:"UTC",hour:"2-digit",minute:"2-digit"},t={timeZone:"UTC",hour:"numeric",minute:"2-digit"},n={timeZone:"UTC",hour:"numeric"};return Q(this.currentLocale,(function(r,o){return o?0===r.minute?n:t:e}))}},methods:{showIntervalLabelDefault:function(e){var t=this.intervals[0][0];return!(t.hour===e.hour&&t.minute===e.minute)},intervalStyleDefault:function(e){},getTimestampAtEvent:function(e,t){var n=R(t),r=e.currentTarget.getBoundingClientRect(),o=this.firstMinute,l=e,d=e,c=l.changedTouches||l.touches,h=((c&&c[0]?c[0].clientY:d.clientY)-r.top)/this.parsedIntervalHeight;return A(n,o+Math.floor(h*this.parsedIntervalMinutes),this.times.now)},getSlotScope:function(e){var t=R(e);return t.timeToY=this.timeToY,t.timeDelta=this.timeDelta,t.minutesToPixels=this.minutesToPixels,t.week=this.days,t},scrollToTime:function(time){var e=this.timeToY(time),t=this.$refs.scrollArea;return!(!1===e||!t)&&(t.scrollTop=e,!0)},minutesToPixels:function(e){return e/this.parsedIntervalMinutes*this.parsedIntervalHeight},timeToY:function(time){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],t=this.timeDelta(time);return!1!==t&&(t*=this.bodyHeight,e&&(t<0&&(t=0),t>this.bodyHeight&&(t=this.bodyHeight))),t},timeDelta:function(time){var e=T(time);return!1!==e&&(e-this.firstMinute)/(this.parsedIntervalCount*this.parsedIntervalMinutes)}}}).extend({name:"v-calendar-daily",directives:{Resize:te.a},data:function(){return{scrollPush:0}},computed:{classes:function(){return Ye({"v-calendar-daily":!0},this.themeClasses)}},mounted:function(){this.init()},methods:{init:function(){this.$nextTick(this.onResize)},onResize:function(){this.scrollPush=this.getScrollPush()},getScrollPush:function(){var area=this.$refs.scrollArea,e=this.$refs.pane;return area&&e?area.offsetWidth-e.offsetWidth:0},genHead:function(){return this.$createElement("div",{staticClass:"v-calendar-daily__head",style:{marginRight:this.scrollPush+"px"}},[this.genHeadIntervals()].concat(Object(Pe.a)(this.genHeadDays())))},genHeadIntervals:function(){var e=Object(je.f)(this.intervalWidth);return this.$createElement("div",{staticClass:"v-calendar-daily__intervals-head",style:{width:e}},Object(je.n)(this,"interval-header"))},genHeadDays:function(){return this.days.map(this.genHeadDay)},genHeadDay:function(e,t){var n=this;return this.$createElement("div",{key:e.date,staticClass:"v-calendar-daily_head-day",class:this.getRelativeClasses(e),on:this.getDefaultMouseEventHandlers(":day",(function(t){return n.getSlotScope(e)}))},[this.genHeadWeekday(e),this.genHeadDayLabel(e)].concat(Object(Pe.a)(this.genDayHeader(e,t))))},genDayHeader:function(e,t){var n=this;return Object(je.n)(this,"day-header",(function(){return Ye(Ye({week:n.days},e),{},{index:t})}))||[]},genHeadWeekday:function(e){var t=e.present?this.color:void 0;return this.$createElement("div",this.setTextColor(t,{staticClass:"v-calendar-daily_head-weekday"}),this.weekdayFormatter(e,this.shortWeekdays))},genHeadDayLabel:function(e){return this.$createElement("div",{staticClass:"v-calendar-daily_head-day-label"},Object(je.n)(this,"day-label-header",e)||[this.genHeadDayButton(e)])},genHeadDayButton:function(e){var t=e.present?this.color:"transparent";return this.$createElement(He.a,{props:{color:t,fab:!0,depressed:!0},on:this.getMouseEventHandlers({"click:date":{event:"click",stop:!0},"contextmenu:date":{event:"contextmenu",stop:!0,prevent:!0,result:!1}},(function(t){return e}))},this.dayFormatter(e,!1))},genBody:function(){return this.$createElement("div",{staticClass:"v-calendar-daily__body"},[this.genScrollArea()])},genScrollArea:function(){return this.$createElement("div",{ref:"scrollArea",staticClass:"v-calendar-daily__scroll-area"},[this.genPane()])},genPane:function(){return this.$createElement("div",{ref:"pane",staticClass:"v-calendar-daily__pane",style:{height:Object(je.f)(this.bodyHeight)}},[this.genDayContainer()])},genDayContainer:function(){return this.$createElement("div",{staticClass:"v-calendar-daily__day-container"},[this.genBodyIntervals()].concat(Object(Pe.a)(this.genDays())))},genDays:function(){return this.days.map(this.genDay)},genDay:function(e,t){var n=this;return this.$createElement("div",{key:e.date,staticClass:"v-calendar-daily__day",class:this.getRelativeClasses(e),on:this.getDefaultMouseEventHandlers(":time",(function(t){return n.getSlotScope(n.getTimestampAtEvent(t,e))}))},[].concat(Object(Pe.a)(this.genDayIntervals(t)),Object(Pe.a)(this.genDayBody(e))))},genDayBody:function(e){var t=this;return Object(je.n)(this,"day-body",(function(){return t.getSlotScope(e)}))||[]},genDayIntervals:function(e){return this.intervals[e].map(this.genDayInterval)},genDayInterval:function(e){var t=this,n=Object(je.f)(this.intervalHeight),r=this.intervalStyle||this.intervalStyleDefault,data={key:e.time,staticClass:"v-calendar-daily__day-interval",style:Ye({height:n},r(e))},o=Object(je.n)(this,"interval",(function(){return t.getSlotScope(e)}));return this.$createElement("div",data,o)},genBodyIntervals:function(){var e=this,data={staticClass:"v-calendar-daily__intervals-body",style:{width:Object(je.f)(this.intervalWidth)},on:this.getDefaultMouseEventHandlers(":interval",(function(t){return e.getTimestampAtEvent(t,e.parsedStart)}))};return this.$createElement("div",data,this.genIntervalLabels())},genIntervalLabels:function(){return this.intervals.length?this.intervals[0].map(this.genIntervalLabel):null},genIntervalLabel:function(e){var t=Object(je.f)(this.intervalHeight),n=this.shortIntervals,label=(this.showIntervalLabel||this.showIntervalLabelDefault)(e)?this.intervalFormatter(e,n):void 0;return this.$createElement("div",{key:e.time,staticClass:"v-calendar-daily__interval",style:{height:t}},[this.$createElement("div",{staticClass:"v-calendar-daily__interval-text"},label)])}},render:function(e){return e("div",{class:this.classes,on:{dragstart:function(e){e.preventDefault()}},directives:[{modifiers:{quiet:!0},name:"resize",value:this.onResize}]},[this.hideHeader?"":this.genHead(),this.genBody()])}});n(1024),n(2013);function Le(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,n)}return t}function Re(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?Le(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):Le(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}function ze(e,t){return"string"==typeof e?e.split(/\s*,\s/):Array.isArray(e)?e.map((function(e){if("string"==typeof e)return{categoryName:e};var n="string"==typeof e.categoryName?e.categoryName:function(e,t){return"string"==typeof t&&"object"===Object(r.a)(e)&&e?e[t]:"function"==typeof t?t(e):e}(e,t);return Re(Re({},e),{},{categoryName:n})})):[]}function Ue(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,n)}return t}function Ze(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?Ue(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):Ue(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var Ve=Be.extend({name:"v-calendar-category",props:Oe.category,computed:{classes:function(){return Ze({"v-calendar-daily":!0,"v-calendar-category":!0},this.themeClasses)},parsedCategories:function(){return ze(this.categories,this.categoryText)}},methods:{genDayHeader:function(e,t){var n=this,r=Ze(Ze({week:this.days},e),{},{index:t}),o=this.parsedCategories.map((function(t){return n.genDayHeaderCategory(e,n.getCategoryScope(r,t))}));return[this.$createElement("div",{staticClass:"v-calendar-category__columns"},o)]},getCategoryScope:function(e,t){var n="object"===Object(r.a)(t)&&t&&t.categoryName===this.categoryForInvalid?null:t;return Ze(Ze({},e),{},{category:n})},genDayHeaderCategory:function(e,t){var n=this;return this.$createElement("div",{staticClass:"v-calendar-category__column-header",on:this.getDefaultMouseEventHandlers(":day-category",(function(r){return n.getCategoryScope(n.getSlotScope(e),t.category)}))},[Object(je.n)(this,"category",t)||this.genDayHeaderCategoryTitle(t.category&&t.category.categoryName),Object(je.n)(this,"day-header",t)])},genDayHeaderCategoryTitle:function(e){return this.$createElement("div",{staticClass:"v-calendar-category__category"},null===e?this.categoryForInvalid:e)},genDays:function(){var e=this,t=[];return this.days.forEach((function(n){var r=new Array(e.parsedCategories.length||1);r.fill(n),t.push.apply(t,Object(Pe.a)(r.map((function(t,i){return e.genDay(t,0,i)}))))})),t},genDay:function(e,t,n){var r=this,o=this.parsedCategories[n];return this.$createElement("div",{key:e.date+"-"+n,staticClass:"v-calendar-daily__day",class:this.getRelativeClasses(e),on:this.getDefaultMouseEventHandlers(":time",(function(t){return r.getSlotScope(r.getTimestampAtEvent(t,e))}))},[].concat(Object(Pe.a)(this.genDayIntervals(t,o)),Object(Pe.a)(this.genDayBody(e,o))))},genDayIntervals:function(e,t){var n=this;return this.intervals[e].map((function(e){return n.genDayInterval(e,t)}))},genDayInterval:function(e,t){var n=this,r=Object(je.f)(this.intervalHeight),o=this.intervalStyle||this.intervalStyleDefault,data={key:e.time,staticClass:"v-calendar-daily__day-interval",style:Ze({height:r},o(Ze(Ze({},e),{},{category:t})))},l=Object(je.n)(this,"interval",(function(){return n.getCategoryScope(n.getSlotScope(e),t)}));return this.$createElement("div",data,l)},genDayBody:function(e,t){var n=[this.genDayBodyCategory(e,t)];return[this.$createElement("div",{staticClass:"v-calendar-category__columns"},n)]},genDayBodyCategory:function(e,t){var n=this,data={staticClass:"v-calendar-category__column",on:this.getDefaultMouseEventHandlers(":time-category",(function(r){return n.getCategoryScope(n.getSlotScope(n.getTimestampAtEvent(r,e)),t)}))},r=Object(je.n)(this,"day-body",(function(){return n.getCategoryScope(n.getSlotScope(e),t)}));return this.$createElement("div",data,r)}}});function qe(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,n)}return t}function Ge(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?qe(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):qe(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}t.a=Me.extend({name:"v-calendar",props:Ge(Ge(Ge(Ge({},Oe.calendar),Oe.weeks),Oe.intervals),Oe.category),data:function(){return{lastStart:null,lastEnd:null}},computed:{parsedValue:function(){return S(this.value)?I(this.value,!0):this.parsedStart||this.times.today},parsedCategoryDays:function(){return parseInt(this.categoryDays)||1},renderProps:function(){var e=this.parsedValue,component=null,t=this.maxDays,n=this.parsedWeekdays,r=this.parsedCategories,o=e,l=e;switch(this.type){case"month":component=Ne,o=j(e),l=C(e);break;case"week":component=Be,o=this.getStartOfWeek(e),l=this.getEndOfWeek(e),t=7;break;case"day":component=Be,t=1,n=[o.weekday];break;case"4day":component=Be,B(l=G(R(l),Z,3)),t=4,n=[o.weekday,(o.weekday+1)%7,(o.weekday+2)%7,(o.weekday+3)%7];break;case"custom-weekly":component=We,o=this.parsedStart||e,l=this.parsedEnd;break;case"custom-daily":component=Be,o=this.parsedStart||e,l=this.parsedEnd;break;case"category":var d=this.parsedCategoryDays;component=Ve,B(l=G(R(l),Z,d)),t=d,n=[];for(var i=0;i<d;i++)n.push((o.weekday+i)%7);r=this.getCategoryList(r);break;default:throw new Error(this.type+" is not a valid Calendar type")}return{component:component,start:o,end:l,maxDays:t,weekdays:n,categories:r}},eventWeekdays:function(){return this.renderProps.weekdays},categoryMode:function(){return"category"===this.type},title:function(){var e=this.renderProps,t=e.start,n=e.end,r=t.year!==n.year,o=r||t.month!==n.month;return r?this.monthShortFormatter(t,!0)+" "+t.year+" - "+this.monthShortFormatter(n,!0)+" "+n.year:o?this.monthShortFormatter(t,!0)+" - "+this.monthShortFormatter(n,!0)+" "+n.year:this.monthLongFormatter(t,!1)+" "+t.year},monthLongFormatter:function(){return this.getFormatter({timeZone:"UTC",month:"long"})},monthShortFormatter:function(){return this.getFormatter({timeZone:"UTC",month:"short"})},parsedCategories:function(){return ze(this.categories,this.categoryText)}},watch:{renderProps:"checkChange"},mounted:function(){this.updateEventVisibility(),this.checkChange()},updated:function(){window.requestAnimationFrame(this.updateEventVisibility)},methods:{checkChange:function(){var e=this.lastStart,t=this.lastEnd,n=this.renderProps,r=n.start,o=n.end;e&&t&&r.date===e.date&&o.date===t.date||(this.lastStart=r,this.lastEnd=o,this.$emit("change",{start:r,end:o}))},move:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=R(this.parsedValue),n=e>0,r=n?Z:V,o=n?31:1,l=n?e:-e;--l>=0;)switch(this.type){case"month":t.day=o,r(t);break;case"week":G(t,r,7);break;case"day":G(t,r,1);break;case"4day":G(t,r,4);break;case"category":G(t,r,this.parsedCategoryDays)}Y(t),B(t),$(t,this.times.now),this.value instanceof Date?this.$emit("input",X(t)):"number"==typeof this.value?this.$emit("input",X(t).getTime()):this.$emit("input",t.date),this.$emit("moved",t)},next:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.move(e)},prev:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.move(-e)},timeToY:function(time){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],t=this.$children[0];return!(!t||!t.timeToY)&&t.timeToY(time,e)},timeDelta:function(time){var e=this.$children[0];return!(!e||!e.timeDelta)&&e.timeDelta(time)},minutesToPixels:function(e){var t=this.$children[0];return t&&t.minutesToPixels?t.minutesToPixels(e):-1},scrollToTime:function(time){var e=this.$children[0];return!(!e||!e.scrollToTime)&&e.scrollToTime(time)},parseTimestamp:function(input,e){return I(input,e,this.times.now)},timestampToDate:function(e){return X(e)},getCategoryList:function(e){var t=this;if(!this.noEvents){var n=e.reduce((function(map,e,t){return"object"===Object(r.a)(e)&&e.categoryName&&(map[e.categoryName]={index:t,count:0}),map}),{});if(!this.categoryHideDynamic||!this.categoryShowAll){var o=e.length;this.parsedEvents.forEach((function(e){var r=e.category;"string"!=typeof r&&(r=t.categoryForInvalid),r&&(r in n?n[r].count++:t.categoryHideDynamic||(n[r]={index:o++,count:1}))}))}if(!this.categoryShowAll)for(var l in n)0===n[l].count&&delete n[l];e=e.filter((function(e){return!("object"!==Object(r.a)(e)||!e.categoryName)&&n.hasOwnProperty(e.categoryName)}))}return e}},render:function(e){var t=this,n=this.renderProps,r=n.start,o=n.end,l=n.maxDays,component=n.component,d=n.weekdays,c=n.categories;return e(component,{staticClass:"v-calendar",class:{"v-calendar-events":!this.noEvents},props:Ge(Ge({},this.$props),{},{start:r.date,end:o.date,maxDays:l,weekdays:d,categories:c}),directives:[{modifiers:{quiet:!0},name:"resize",value:this.updateEventVisibility}],on:Ge(Ge({},this.$listeners),{},{"click:date":function(e){t.$listeners.input&&t.$emit("input",e.date),t.$listeners["click:date"]&&t.$emit("click:date",e)}}),scopedSlots:this.getScopedSlots()})}})}}]);