{"version": 3, "file": "components/pagination.js", "sources": ["webpack:///./components/Pagination.vue?0246", "webpack:///./components/Pagination.vue?f157", "webpack:///./components/Pagination.vue", "webpack:///./components/Pagination.vue?0e64", "webpack:///./components/Pagination.vue?ea7a", "webpack:///./components/Pagination.vue?89ad", "webpack:///./components/Pagination.vue?8991"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Pagination.vue?vue&type=style&index=0&id=18a8bda5&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"ef3a6480\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('nav',{staticClass:\"pagination\"},[_vm._ssrNode(\"<ul class=\\\"pagination-list d-flex justify-center align-center\\\" data-v-18a8bda5>\",\"</ul>\",[_vm._ssrNode(\"<li\"+(_vm._ssrClass(null,['pagination-item pagination-item-prev']))+\" data-v-18a8bda5><div class=\\\"icon next-prev-icon\\\" data-v-18a8bda5><svg width=\\\"17\\\" height=\\\"12\\\" viewBox=\\\"0 0 17 12\\\" data-v-18a8bda5><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#arrow-prev\")))+\" data-v-18a8bda5></use></svg></div> <span class=\\\"d-none d-sm-inline ml-2\\\" data-v-18a8bda5>\"+_vm._ssrEscape(_vm._s(_vm.$t('previous')))+\"</span></li> \"),_vm._l((_vm.pages),function(page,index){return _vm._ssrNode(\"<li class=\\\"pagination-item\\\" data-v-18a8bda5>\",\"</li>\",[(page !== 0)?[_c('nuxt-link',{class:{ current: _vm.currentPage === page },attrs:{\"to\":_vm.getUrl(page)}},[_vm._v(\"\\n          \"+_vm._s(page)+\"\\n        \")])]:_vm._ssrNode(\"<span class=\\\"dots\\\" data-v-18a8bda5>...</span>\")],2)}),_vm._ssrNode(\" <li\"+(_vm._ssrClass(null,['pagination-item pagination-item-next']))+\" data-v-18a8bda5><span class=\\\"d-none d-sm-inline mr-2\\\" data-v-18a8bda5>\"+_vm._ssrEscape(_vm._s(_vm.$t('next')))+\"</span> <div class=\\\"icon\\\" data-v-18a8bda5><svg width=\\\"17\\\" height=\\\"12\\\" viewBox=\\\"0 0 17 12\\\" data-v-18a8bda5><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#arrow-next\")))+\" data-v-18a8bda5></use></svg></div></li>\")],2)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'Pagination',\n  props: {\n    currentPage: {\n      type: Number,\n      required: true,\n    },\n    totalPages: {\n      type: Number,\n      required: true,\n    },\n    route: {\n      type: String,\n      required: true,\n    },\n    params: {\n      type: String,\n      default: '',\n    },\n  },\n  data() {\n    return {\n      key: 1,\n    }\n  },\n  computed: {\n    isFirstCurrentPage() {\n      return this.currentPage <= 1\n    },\n    isLastCurrentPage() {\n      return this.currentPage >= this.totalPages\n    },\n    pages() {\n      const pages = []\n\n      for (let i = 1; i <= this.totalPages; i++) {\n        pages.push(i)\n      }\n\n      let pagination = pages.slice()\n\n      if (this.totalPages > 6) {\n        let left = []\n        let right = []\n        let center = []\n\n        if (this.currentPage < 3 || this.currentPage > this.totalPages - 3) {\n          left = pages.slice(0, 3)\n          right = pages.slice(-3)\n\n          pagination = [...left, 0, ...right]\n        }\n\n        if (this.currentPage === 3) {\n          left = pages.slice(0, 5)\n          right = pages.slice(-1)\n\n          pagination = [...left, 0, ...right]\n        }\n\n        if (this.currentPage > 3 && this.currentPage < this.totalPages - 2) {\n          left = pages.slice(0, 1)\n          right = pages.slice(-1)\n          center = pages.slice(this.currentPage - 2, this.currentPage + 1)\n\n          pagination = [...left, 0, ...center, 0, ...right]\n        }\n\n        if (this.currentPage === this.totalPages - 2) {\n          left = pages.slice(0, 1)\n          right = pages.slice(-5)\n\n          pagination = [...left, 0, ...right]\n        }\n      }\n\n      return pagination\n    },\n    queryStr() {\n      const { query } = this.$route\n      const keys = Object.keys(query)\n\n      let str = ''\n\n      if (keys.length) {\n        str += '?'\n\n        for (let i = 0; i < keys.length; i++) {\n          str += `${keys[i]}=${query[keys[i]]}`\n\n          if (i < keys.length - 1) {\n            str += '&'\n          }\n        }\n      }\n\n      return str\n    },\n  },\n  watch: {\n    currentPage() {\n      this.key++\n    },\n  },\n  methods: {\n    getUrl(page) {\n      let url = this.route\n\n      if (page > 1 || this.params.length) {\n        url += `/${page}${this.params.length ? '/' + this.params : ''}`\n      }\n\n      if (this.queryStr.length) {\n        url += this.queryStr\n      }\n\n      return url\n    },\n    prevPageClickHandler() {\n      if (!this.isFirstCurrentPage) {\n        this.$router.push({ path: this.getUrl(this.currentPage - 1) })\n      }\n    },\n    nextPageClickHandler() {\n      if (!this.isLastCurrentPage) {\n        this.$router.push({ path: this.getUrl(this.currentPage + 1) })\n      }\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Pagination.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Pagination.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Pagination.vue?vue&type=template&id=18a8bda5&scoped=true&\"\nimport script from \"./Pagination.vue?vue&type=script&lang=js&\"\nexport * from \"./Pagination.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./Pagination.vue?vue&type=style&index=0&id=18a8bda5&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"18a8bda5\",\n  \"18cd97b2\"\n  \n)\n\nexport default component.exports", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Pagination.vue?vue&type=style&index=0&id=18a8bda5&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".pagination-list[data-v-18a8bda5]{padding-left:0;list-style-type:none}.pagination-item a[data-v-18a8bda5]{display:flex;justify-content:center;align-items:center;width:35px;height:35px;font-size:16px;font-weight:700;border-radius:4px;color:var(--v-darkLight-base);text-decoration:none;transition:color .3s;margin:0 10px}@media only screen and (max-width:639px){.pagination-item a[data-v-18a8bda5]{width:38px;height:38px}}@media only screen and (max-width:479px){.pagination-item a[data-v-18a8bda5]{width:36px;height:36px;font-size:14px;border-radius:2px}}.pagination-item a.current[data-v-18a8bda5]{background:var(--v-orange-base)}.pagination-item a[data-v-18a8bda5]:not(.current):hover{color:var(--v-orange-base)}.pagination-item-next[data-v-18a8bda5],.pagination-item-prev[data-v-18a8bda5]{display:flex;align-items:center;font-size:16px;font-weight:500;border-radius:50%;transition:color .3s}.pagination-item-next.disabled[data-v-18a8bda5],.pagination-item-prev.disabled[data-v-18a8bda5]{opacity:.6}.pagination-item-next[data-v-18a8bda5]:not(.disabled),.pagination-item-prev[data-v-18a8bda5]:not(.disabled){cursor:pointer}.pagination-item-next[data-v-18a8bda5]:not(.disabled):hover,.pagination-item-prev[data-v-18a8bda5]:not(.disabled):hover{color:var(--v-orange-base)}.pagination-item-prev[data-v-18a8bda5]{margin-right:15px}@media only screen and (max-width:639px){.pagination-item-prev[data-v-18a8bda5]{margin-right:10px}}@media only screen and (max-width:479px){.pagination-item-prev[data-v-18a8bda5]{margin-right:5px}}.pagination-item-prev .icon[data-v-18a8bda5]{margin-right:12px}.pagination-item-next[data-v-18a8bda5]{margin-left:15px}@media only screen and (max-width:639px){.pagination-item-next[data-v-18a8bda5]{margin-left:10px}}@media only screen and (max-width:479px){.pagination-item-next[data-v-18a8bda5]{margin-left:5px}}.pagination-item-next .icon[data-v-18a8bda5]{margin-left:12px}.pagination-item .dots[data-v-18a8bda5]{display:inline-block;width:64px;text-align:center}@media only screen and (max-width:639px){.pagination-item .dots[data-v-18a8bda5]{width:30px}}@media only screen and (max-width:479px){.pagination-item .dots[data-v-18a8bda5]{width:25px}}.pagination-item-prev[data-v-18a8bda5]{color:#000;fill:#000}.pagination-item-prev span[data-v-18a8bda5]{color:#000;fill:#000;font-weight:700}.pagination-item-next[data-v-18a8bda5]{color:#000;fill:#000}.pagination-item-next span[data-v-18a8bda5]{color:#000;fill:#000;font-weight:700}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AACA;AAiBA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzEA;AA0EA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAxBA;AAxGA;;AC/CA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}