{"version": 3, "file": "components/classroom-konva.js", "sources": ["webpack:///./components/classroom/Konva.vue?014a", "webpack:///./components/classroom/Konva.vue", "webpack:///./components/classroom/Konva.vue?ec97", "webpack:///./components/classroom/Konva.vue?dee3"], "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-stage',{ref:\"stage\",attrs:{\"config\":_vm.config},on:{\"mousedown\":_vm.mousedown,\"touchstart\":_vm.mousedown,\"touchmove\":_vm.move,\"touchend\":_vm.mouseup,\"mouseup\":_vm.mouseup,\"mousemove\":_vm.move}},[_c('v-layer',{ref:\"globalLayer\"},[_vm._l((_vm.shapes),function(shape,index){return [(\n          (!shape.hasOwnProperty('time') && !shape.hasOwnProperty('page')) ||\n          (shape.hasOwnProperty('time') &&\n            _vm.currentTime + 1 >= shape.time &&\n            shape.time >= _vm.currentTime - 1) ||\n          (shape.hasOwnProperty('page') && _vm.currentPage === shape.page)\n        )?_c(shape.type,{key:index,tag:\"component\",attrs:{\"config\":shape}}):_vm._e()]})],2)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Konva from 'konva'\nimport {\n  TOOL_CIRCLE,\n  TOOL_ERASER,\n  TOOL_LINE,\n  TOOL_PEN,\n  TOOL_POINTER,\n  TOOL_SQUARE,\n  TOOL_TRIANGLE,\n  mainCanvasWidth,\n  mainCanvasHeight,\n  // mainCanvasOffsetX,\n  // mainCanvasOffsetY,\n} from '~/helpers/constants'\n\nexport default {\n  name: 'Konva',\n  props: {\n    file: {\n      type: Object,\n      required: true,\n    },\n    width: {\n      type: Number,\n      required: true,\n    },\n    height: {\n      type: Number,\n      required: true,\n    },\n    scale: {\n      type: Number,\n      default: 1,\n    },\n    currentTime: {\n      type: Number,\n      default: null,\n    },\n    currentPage: {\n      type: Number,\n      default: null,\n    },\n    isMainKonva: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  data() {\n    return {\n      shapeData: null,\n      beginPoint: null,\n      konvaEl: null,\n      konvaOverlayREl: null,\n      konvaOverlayBEl: null,\n    }\n  },\n  computed: {\n    isCanvasOversizeX() {\n      return mainCanvasWidth > this.width\n    },\n    isScaledCanvasOversizeX() {\n      return mainCanvasWidth * this.scale > this.width\n    },\n    isCanvasOversizeY() {\n      return mainCanvasHeight > this.viewportHeight\n    },\n    isScaledCanvasOversizeY() {\n      return mainCanvasHeight * this.scale > this.height\n    },\n    assetType() {\n      return this.file.asset.type\n    },\n    userParams() {\n      return this.$store.getters['classroom/userParams']\n    },\n    assetShapes() {\n      return this.file.asset?.shapes ?? []\n    },\n    shapes() {\n      const assetShapes = [...this.assetShapes]\n      const _shapes = []\n\n      if (this.shapeData) {\n        assetShapes.push(this.shapeData)\n      }\n\n      assetShapes.forEach((shape) => {\n        const _shape = { ...shape }\n\n        _shape.x = shape.x - this.zoomX\n        _shape.y = shape.y - this.zoomY\n\n        if (this.file.asset?.originalWidth) {\n          _shape.strokeWidth =\n            (shape.strokeWidth * this.file.asset.originalWidth) /\n            this.file.asset.width\n        }\n\n        _shapes.push(_shape)\n      })\n\n      // if (this.isMainKonva) {\n      //   _shapes.push({\n      //     type: 'v-line',\n      //     stroke: '#f8ae3c',\n      //     strokeWidth: 5,\n      //     x: 0,\n      //     y: 0,\n      //     points: [\n      //       mainCanvasOffsetX, mainCanvasOffsetY,\n      //       mainCanvasOffsetX, mainCanvasHeight + mainCanvasOffsetY,\n      //       mainCanvasWidth + mainCanvasOffsetX, mainCanvasHeight + mainCanvasOffsetY,\n      //       mainCanvasWidth + mainCanvasOffsetX, mainCanvasOffsetY,\n      //       mainCanvasOffsetX, mainCanvasOffsetY,\n      //     ],\n      //   })\n      // }\n\n      return _shapes\n    },\n    zoomX() {\n      return this.isMainKonva ? this.zoom.x : 0\n    },\n    zoomY() {\n      return this.isMainKonva ? this.zoom.y : 0\n    },\n    zoom() {\n      return this.$store.getters['classroom/zoomAsset'].asset\n    },\n    config() {\n      return {\n        scale: {\n          x: this.scale,\n          y: this.scale,\n        },\n        width: this.width,\n        height: this.height,\n        draggable: false,\n      }\n    },\n  },\n  watch: {\n    width(width) {\n      this.stage.setWidth(width)\n\n      if (this.isMainKonva) {\n        if (this.konvaOverlayREl) {\n          this.setStyleForHorizontalMainKonvaOverlays()\n        }\n\n        if (this.konvaOverlayBEl) {\n          this.setStyleForVerticalMainKonvaOverlays()\n        }\n      }\n    },\n    height(height) {\n      this.stage.setHeight(height)\n\n      if (this.isMainKonva) {\n        if (this.konvaOverlayREl) {\n          this.setStyleForHorizontalMainKonvaOverlays()\n        }\n\n        if (this.konvaOverlayBEl) {\n          this.setStyleForVerticalMainKonvaOverlays()\n        }\n      }\n    },\n    scale(scale) {\n      this.stage.setScale({\n        x: scale,\n        y: scale,\n      })\n\n      if (this.isMainKonva) {\n        if (this.konvaOverlayREl) {\n          this.setStyleForHorizontalMainKonvaOverlays()\n        }\n\n        if (this.konvaOverlayBEl) {\n          this.setStyleForVerticalMainKonvaOverlays()\n        }\n      }\n    },\n    isScaledCanvasOversizeX(newValue) {\n      if (this.isMainKonva) {\n        if (newValue) {\n          this.konvaEl.removeChild(this.konvaOverlayREl)\n\n          this.konvaOverlayREl = null\n        } else {\n          this.addHorizontalMainKonvaOverlays()\n        }\n      }\n    },\n    isScaledCanvasOversizeY(newValue) {\n      if (this.isMainKonva) {\n        if (newValue) {\n          this.konvaEl.removeChild(this.konvaOverlayBEl)\n\n          this.konvaOverlayBEl = null\n        } else {\n          this.addVerticalMainKonvaOverlays()\n        }\n      }\n    },\n  },\n  mounted() {\n    this.stage = this.$refs.stage.getStage()\n\n    this.$nextTick(() => {\n      this.move()\n    })\n\n    this.konvaEl = document.getElementById('konva')\n\n    if (this.isMainKonva) {\n      if (!this.isScaledCanvasOversizeX) {\n        this.addHorizontalMainKonvaOverlays()\n      }\n\n      if (!this.isScaledCanvasOversizeY) {\n        this.addVerticalMainKonvaOverlays()\n      }\n    }\n  },\n  methods: {\n    addHorizontalMainKonvaOverlays() {\n      if (!this.konvaOverlayREl) {\n        this.konvaOverlayREl = document.createElement('div')\n\n        this.konvaOverlayREl.classList.add('konva-overlay-r')\n\n        this.setStyleForHorizontalMainKonvaOverlays()\n\n        this.konvaOverlayREl.addEventListener('mouseenter', this.mouseup)\n\n        this.konvaEl.appendChild(this.konvaOverlayREl)\n      }\n    },\n    addVerticalMainKonvaOverlays() {\n      if (!this.konvaOverlayBEl) {\n        this.konvaOverlayBEl = document.createElement('div')\n\n        this.konvaOverlayBEl.classList.add('konva-overlay-b')\n\n        this.setStyleForVerticalMainKonvaOverlays()\n\n        this.konvaOverlayBEl.addEventListener('mouseenter', this.mouseup)\n\n        this.konvaEl.appendChild(this.konvaOverlayBEl)\n      }\n    },\n    setStyleForHorizontalMainKonvaOverlays() {\n      this.konvaOverlayREl.style.position = 'absolute'\n      this.konvaOverlayREl.style.top = '0'\n      this.konvaOverlayREl.style.right = '0'\n      this.konvaOverlayREl.style.width = `${\n        this.width - mainCanvasWidth * this.scale\n      }px`\n      this.konvaOverlayREl.style.height = `${this.height}px`\n      this.konvaOverlayREl.style.backgroundColor = '#DCDCDD'\n    },\n    setStyleForVerticalMainKonvaOverlays() {\n      this.konvaOverlayBEl.style.position = 'absolute'\n      this.konvaOverlayBEl.style.bottom = '0'\n      this.konvaOverlayBEl.style.left = '0'\n      this.konvaOverlayBEl.style.width = `${this.width}px`\n      this.konvaOverlayBEl.style.height = `${\n        this.height - mainCanvasHeight * this.scale\n      }px`\n      this.konvaOverlayBEl.style.backgroundColor = '#DCDCDD'\n    },\n    mousedown(event) {\n      const layer = this.$refs.globalLayer.getNode()\n      const position = event.target.getStage().getPointerPosition()\n\n      this.beginPoint = {\n        x: position.x / this.scale + this.zoomX,\n        y: position.y / this.scale + this.zoomY,\n      }\n\n      switch (this.userParams.tool) {\n        case TOOL_POINTER: {\n          const ripple = new Konva.Circle({\n            x: position.x / this.scale,\n            y: position.y / this.scale,\n            radius: 2,\n            stroke: this.userParams.color,\n            strokeWidth: 1,\n          })\n\n          layer.add(ripple)\n          new Konva.Tween({\n            node: ripple,\n            duration: 1,\n            radius: 24,\n            onFinish: () => ripple.destroy(),\n          }).play()\n\n          return\n        }\n\n        case TOOL_PEN:\n          this.shapeData = {\n            type: 'v-line',\n            stroke: this.userParams.color,\n            strokeWidth: 5,\n            x: 0,\n            y: 0,\n            points: [\n              position.x / this.scale + this.zoomX,\n              position.y / this.scale + this.zoomY,\n            ],\n            lineCap: 'round',\n            lineJoin: 'round',\n            tension: 0,\n            bezier: true,\n            perfectDrawEnabled: false,\n          }\n          break\n\n        case TOOL_SQUARE:\n          this.shapeData = {\n            type: 'v-rect',\n            x: position.x / this.scale + this.zoomX,\n            y: position.y / this.scale + this.zoomY,\n            width: 1,\n            height: 1,\n            stroke: this.userParams.color,\n            strokeWidth: 5,\n          }\n          break\n\n        case TOOL_CIRCLE:\n          this.shapeData = {\n            type: 'v-circle',\n            x: position.x / this.scale + this.zoomX,\n            y: position.y / this.scale + this.zoomY,\n            radius: 1,\n            stroke: this.userParams.color,\n            strokeWidth: 5,\n          }\n          break\n\n        case TOOL_TRIANGLE:\n          this.shapeData = {\n            type: 'v-line',\n            stroke: this.userParams.color,\n            strokeWidth: 5,\n            x: position.x / this.scale + this.zoomX,\n            y: position.y / this.scale + this.zoomY,\n            points: [0, 0, 0, 0, 0, 0],\n            tension: 0,\n            closed: true,\n          }\n          break\n\n        case TOOL_LINE:\n          this.shapeData = {\n            type: 'v-line',\n            stroke: this.userParams.color,\n            strokeWidth: 5,\n            x: 0,\n            y: 0,\n            points: [\n              position.x / this.scale + this.zoomX,\n              position.y / this.scale + this.zoomY,\n            ],\n          }\n          break\n\n        case TOOL_ERASER:\n          this.shapeData = {\n            type: 'v-line',\n            stroke: '#f2f2f2',\n            strokeWidth: 30,\n            x: 0,\n            y: 0,\n            points: [\n              position.x / this.scale + this.zoomX,\n              position.y / this.scale + this.zoomY,\n            ],\n            globalCompositeOperation: 'destination-out',\n          }\n          break\n\n        default:\n          console.warn(\n            'Requested action doesnt found for selected cursor - ' +\n              this.userParams.tool\n          )\n      }\n\n      if (this.userParams.tool !== TOOL_POINTER) {\n        if (this.assetType === 'video') {\n          this.shapeData.time = this.currentTime\n        }\n\n        if (this.assetType === 'pdf') {\n          this.shapeData.page = this.currentPage\n        }\n      }\n    },\n    mouseup() {\n      if (!this.shapeData || !this.shapeData.type) return\n\n      const asset = {\n        shapes: [...this.assetShapes, this.shapeData],\n      }\n\n      this.$store.commit('classroom/moveAsset', {\n        id: this.file.id,\n        asset,\n      })\n      this.$store.dispatch('classroom/moveAsset', {\n        id: this.file.id,\n        lessonId: this.file.lessonId,\n        asset,\n      })\n\n      this.beginPoint = null\n      this.shapeData = null\n    },\n    move(event) {\n      if (this.shapeData) {\n        const position = event.target.getStage().getPointerPosition()\n\n        this.drawing(position)\n      }\n    },\n    drawing(position) {\n      if (this.shapeData) {\n        let offsetWidth\n        let offsetHeight\n\n        switch (this.userParams.tool) {\n          case TOOL_PEN:\n          case TOOL_ERASER:\n            this.shapeData.points = [\n              ...this.shapeData.points,\n              position.x / this.scale + this.zoomX,\n              position.y / this.scale + this.zoomY,\n            ]\n            break\n\n          case TOOL_SQUARE:\n            this.shapeData.width =\n              position.x / this.scale + this.zoomX - this.beginPoint.x\n            this.shapeData.height =\n              position.y / this.scale + this.zoomY - this.beginPoint.y\n            break\n\n          case TOOL_CIRCLE:\n            offsetWidth = Math.abs(\n              position.x / this.scale + this.zoomX - this.beginPoint.x\n            )\n            offsetHeight = Math.abs(\n              position.y / this.scale + this.zoomY - this.beginPoint.y\n            )\n            this.shapeData.radius = Math.max(offsetWidth, offsetHeight)\n            break\n\n          case TOOL_TRIANGLE:\n            this.shapeData.points = [\n              -(position.x / this.scale + this.zoomX - this.beginPoint.x),\n              position.y / this.scale + this.zoomY - this.beginPoint.y,\n              0,\n              0,\n              position.x / this.scale + this.zoomX - this.beginPoint.x,\n              position.y / this.scale + this.zoomY - this.beginPoint.y,\n            ]\n            break\n\n          case TOOL_LINE:\n            this.shapeData.points = [\n              this.beginPoint.x,\n              this.beginPoint.y,\n              position.x / this.scale + this.zoomX,\n              position.y / this.scale + this.zoomY,\n            ]\n            break\n\n          default:\n            console.warn('Requested action doesnt found for selected cursor')\n        }\n      }\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Konva.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Konva.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Konva.vue?vue&type=template&id=4fc3dbd7&lang=html&\"\nimport script from \"./Konva.vue?vue&type=script&lang=js&\"\nexport * from \"./Konva.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"70e06b04\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAcA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAzBA;AACA;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAPA;AASA;AACA;AApFA;AAqFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAFA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAjEA;AACA;AAiEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAFA;AACA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAQA;AACA;AACA;AACA;AACA;AACA;AAJA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AAdA;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAVA;AAYA;AACA;AACA;AACA;AA1GA;AACA;AA+GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AADA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAHA;AAMA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAGA;AAGA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AAhDA;AAkDA;AACA;AACA;AAtQA;AAnNA;;AC9CA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}