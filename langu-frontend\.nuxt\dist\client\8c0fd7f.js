(window.webpackJsonp=window.webpackJsonp||[]).push([[138,120,136],{1375:function(t,e,n){"use strict";n.r(e);var o={name:"UserSettingTemplate",props:{title:{type:String,required:!0},hideFooter:{type:Boolean,default:!1},customValid:{type:Boolean,default:!0},submitFunc:{type:Function,default:function(){}}},data:function(){return{formValid:!0}},computed:{valid:function(){return this.formValid&&this.customValid}},mounted:function(){this.validate()},methods:{validate:function(){this.$refs.form.validate()},submit:function(){this.valid&&this.submitFunc()}}},l=(n(1419),n(22)),r=n(42),c=n.n(r),d=n(1327),h=n(1363),component=Object(l.a)(o,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-form",{ref:"form",attrs:{value:t.formValid},on:{validate:t.validate,submit:function(e){return e.preventDefault(),t.submit.apply(null,arguments)},input:function(e){t.formValid=e}}},[o("div",{staticClass:"user-settings-panel"},[o("div",{staticClass:"panel"},[t.$vuetify.breakpoint.smAndUp?o("div",{staticClass:"panel-head d-none d-sm-block"},[o("div",{staticClass:"panel-head-title font-weight-medium mb-2 mb-sm-3 mb-md-5"},[t._v("\n          "+t._s(t.title)+"\n        ")])]):t._e(),t._v(" "),o("div",{staticClass:"panel-body"},[t._t("default")],2),t._v(" "),t.hideFooter?t._e():o("div",{staticClass:"panel-footer d-flex justify-center justify-sm-end"},[o("v-btn",{staticClass:"font-weight-medium",attrs:{color:"primary",type:"submit",disabled:!t.valid}},[o("svg",{staticClass:"mr-1",attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[o("use",{attrs:{"xlink:href":n(91)+"#save-icon"}})]),t._v("\n          "+t._s(t.$t("save_changes"))+"\n        ")])],1)])])])}),[],!1,null,null,null);e.default=component.exports;c()(component,{VBtn:d.a,VForm:h.a})},1385:function(t,e,n){var content=n(1420);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("419d3f06",content,!0,{sourceMap:!1})},1419:function(t,e,n){"use strict";n(1385)},1420:function(t,e,n){var o=n(18)(!1);o.push([t.i,".user-settings-panel{padding:44px;border-radius:20px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1)}@media only screen and (max-width:1439px){.user-settings-panel{padding:24px}}@media only screen and (max-width:767px){.user-settings-panel{padding:0;box-shadow:none}}.user-settings-panel .row{margin:0 -14px!important}.user-settings-panel .col{padding:0 14px!important}.user-settings-panel .panel{color:var(--v-greyDark-base)}.user-settings-panel .panel-head-title{font-size:24px;line-height:1.333;color:var(--v-darkLight-base)}@media only screen and (max-width:1439px){.user-settings-panel .panel-head-title{font-size:20px}}.user-settings-panel .panel-body .chips>div{margin-top:6px}.user-settings-panel .panel-body .price-input .v-text-field--outlined.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot{min-height:32px!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr>th{color:var(--v-dark-base)}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr:last-child>th{border:none!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:hover:not(.v-data-table__expanded__content):not(.v-data-table__empty-wrapper){background:none}.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tbody>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tfoot>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>thead>tr>td{height:38px;color:var(--v-greyDark-base)}.user-settings-panel .panel-footer{margin-top:115px}@media only screen and (max-width:1439px){.user-settings-panel .panel-footer{margin-top:56px}}.user-settings-panel .panel-footer .v-btn{letter-spacing:.1px}@media only screen and (max-width:479px){.user-settings-panel .panel-footer .v-btn{width:100%!important}}.user-settings-panel .l-checkbox .v-input--selection-controls__input{width:16px;height:16px;margin-top:2px}",""]),t.exports=o},1429:function(t,e,n){"use strict";var o=n(3),l=n(1);e.a=o.default.extend({name:"comparable",props:{valueComparator:{type:Function,default:l.h}}})},1479:function(t,e,n){var content=n(1564);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("2e2bc7da",content,!0,{sourceMap:!1})},1480:function(t,e,n){"use strict";n.d(e,"b",(function(){return d}));n(20),n(80),n(9);var o=n(117),l=n(1556),r=n(1429),c=n(12);function d(t){t.preventDefault()}e.a=Object(c.a)(o.a,l.a,r.a).extend({name:"selectable",model:{prop:"inputValue",event:"change"},props:{id:String,inputValue:null,falseValue:null,trueValue:null,multiple:{type:Boolean,default:null},label:String},data:function(){return{hasColor:this.inputValue,lazyValue:this.inputValue}},computed:{computedColor:function(){if(this.isActive)return this.color?this.color:this.isDark&&!this.appIsDark?"white":"primary"},isMultiple:function(){return!0===this.multiple||null===this.multiple&&Array.isArray(this.internalValue)},isActive:function(){var t=this,e=this.value,input=this.internalValue;return this.isMultiple?!!Array.isArray(input)&&input.some((function(n){return t.valueComparator(n,e)})):void 0===this.trueValue||void 0===this.falseValue?e?this.valueComparator(e,input):Boolean(input):this.valueComparator(input,this.trueValue)},isDirty:function(){return this.isActive},rippleState:function(){return this.isDisabled||this.validationState?this.validationState:void 0}},watch:{inputValue:function(t){this.lazyValue=t,this.hasColor=t}},methods:{genLabel:function(){var label=o.a.options.methods.genLabel.call(this);return label?(label.data.on={click:d},label):label},genInput:function(t,e){return this.$createElement("input",{attrs:Object.assign({"aria-checked":this.isActive.toString(),disabled:this.isDisabled,id:this.computedId,role:t,type:t},e),domProps:{value:this.value,checked:this.isActive},on:{blur:this.onBlur,change:this.onChange,focus:this.onFocus,keydown:this.onKeydown,click:d},ref:"input"})},onBlur:function(){this.isFocused=!1},onClick:function(t){this.onChange(),this.$emit("click",t)},onChange:function(){var t=this;if(this.isInteractive){var e=this.value,input=this.internalValue;if(this.isMultiple){Array.isArray(input)||(input=[]);var n=input.length;(input=input.filter((function(n){return!t.valueComparator(n,e)}))).length===n&&input.push(e)}else input=void 0!==this.trueValue&&void 0!==this.falseValue?this.valueComparator(input,this.trueValue)?this.falseValue:this.trueValue:e?this.valueComparator(input,e)?null:e:!input;this.validate(!0,input),this.internalValue=input,this.hasColor=input}},onFocus:function(){this.isFocused=!0},onKeydown:function(t){}}})},1507:function(t,e,n){var content=n(1508);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("12a190a6",content,!0,{sourceMap:!1})},1508:function(t,e,n){var o=n(18)(!1);o.push([t.i,".v-input--checkbox.v-input--indeterminate.v-input--is-disabled{opacity:.6}",""]),t.exports=o},1556:function(t,e,n){"use strict";var o=n(127),l=n(3);e.a=l.default.extend({name:"rippleable",directives:{ripple:o.a},props:{ripple:{type:[Boolean,Object],default:!0}},methods:{genRipple:function(){var data=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.ripple?(data.staticClass="v-input--selection-controls__ripple",data.directives=data.directives||[],data.directives.push({name:"ripple",value:{center:!0}}),this.$createElement("div",data)):null}}})},1564:function(t,e,n){var o=n(18)(!1);o.push([t.i,'.theme--light.v-input--selection-controls.v-input--is-disabled:not(.v-input--indeterminate) .v-icon{color:rgba(0,0,0,.26)!important}.theme--dark.v-input--selection-controls.v-input--is-disabled:not(.v-input--indeterminate) .v-icon{color:hsla(0,0%,100%,.3)!important}.v-input--selection-controls{margin-top:16px;padding-top:4px}.v-input--selection-controls>.v-input__append-outer,.v-input--selection-controls>.v-input__prepend-outer{margin-top:0;margin-bottom:0}.v-input--selection-controls:not(.v-input--hide-details)>.v-input__slot{margin-bottom:12px}.v-input--selection-controls .v-input__slot,.v-input--selection-controls .v-radio{cursor:pointer}.v-input--selection-controls .v-input__slot>.v-label,.v-input--selection-controls .v-radio>.v-label{align-items:center;display:inline-flex;flex:1 1 auto;height:auto}.v-input--selection-controls__input{color:inherit;display:inline-flex;flex:0 0 auto;height:24px;position:relative;transition:.3s cubic-bezier(.25,.8,.5,1);transition-property:transform;width:24px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__input .v-icon{width:100%}.v-application--is-ltr .v-input--selection-controls__input{margin-right:8px}.v-application--is-rtl .v-input--selection-controls__input{margin-left:8px}.v-input--selection-controls__input input[role=checkbox],.v-input--selection-controls__input input[role=radio],.v-input--selection-controls__input input[role=switch]{position:absolute;opacity:0;width:100%;height:100%;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__input+.v-label{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__ripple{border-radius:50%;cursor:pointer;height:34px;position:absolute;transition:inherit;width:34px;left:-12px;top:calc(50% - 24px);margin:7px}.v-input--selection-controls__ripple:before{border-radius:inherit;bottom:0;content:"";position:absolute;opacity:.2;left:0;right:0;top:0;transform-origin:center center;transform:scale(.2);transition:inherit}.v-input--selection-controls__ripple>.v-ripple__container{transform:scale(1.2)}.v-input--selection-controls.v-input--dense .v-input--selection-controls__ripple{width:28px;height:28px;left:-9px}.v-input--selection-controls.v-input--dense:not(.v-input--switch) .v-input--selection-controls__ripple{top:calc(50% - 21px)}.v-input--selection-controls.v-input{flex:0 1 auto}.v-input--selection-controls.v-input--is-focused .v-input--selection-controls__ripple:before,.v-input--selection-controls .v-radio--is-focused .v-input--selection-controls__ripple:before{background:currentColor;transform:scale(1.2)}.v-input--selection-controls__input:hover .v-input--selection-controls__ripple:before{background:currentColor;transform:scale(1.2);transition:none}',""]),t.exports=o},1581:function(t,e,n){var content=n(1647);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("16b2336a",content,!0,{sourceMap:!1})},1611:function(t,e,n){"use strict";n(7),n(8),n(9),n(14),n(6),n(15);var o=n(2),l=(n(20),n(80),n(1507),n(1479),n(263)),r=n(117),c=n(1480);function d(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function h(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(e){Object(o.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}e.a=c.a.extend({name:"v-checkbox",props:{indeterminate:Boolean,indeterminateIcon:{type:String,default:"$checkboxIndeterminate"},offIcon:{type:String,default:"$checkboxOff"},onIcon:{type:String,default:"$checkboxOn"}},data:function(){return{inputIndeterminate:this.indeterminate}},computed:{classes:function(){return h(h({},r.a.options.computed.classes.call(this)),{},{"v-input--selection-controls":!0,"v-input--checkbox":!0,"v-input--indeterminate":this.inputIndeterminate})},computedIcon:function(){return this.inputIndeterminate?this.indeterminateIcon:this.isActive?this.onIcon:this.offIcon},validationState:function(){if(!this.isDisabled||this.inputIndeterminate)return this.hasError&&this.shouldValidate?"error":this.hasSuccess?"success":null!==this.hasColor?this.computedColor:void 0}},watch:{indeterminate:function(t){var e=this;this.$nextTick((function(){return e.inputIndeterminate=t}))},inputIndeterminate:function(t){this.$emit("update:indeterminate",t)},isActive:function(){this.indeterminate&&(this.inputIndeterminate=!1)}},methods:{genCheckbox:function(){return this.$createElement("div",{staticClass:"v-input--selection-controls__input"},[this.$createElement(l.a,this.setTextColor(this.validationState,{props:{dense:this.dense,dark:this.dark,light:this.light}}),this.computedIcon),this.genInput("checkbox",h(h({},this.attrs$),{},{"aria-checked":this.inputIndeterminate?"mixed":this.isActive.toString()})),this.genRipple(this.setTextColor(this.rippleState))])},genDefaultSlot:function(){return[this.genCheckbox(),this.genLabel()]}}})},1646:function(t,e,n){"use strict";n(1581)},1647:function(t,e,n){var o=n(18)(!1);o.push([t.i,".speciality-picker{height:calc(100vh - 10%)}.speciality-picker .dialog-content,.speciality-picker .v-card{height:100%}@media only screen and (max-width:1439px){.speciality-picker .v-card{padding:32px 28px}}.speciality-picker .dialog-content{position:relative}.speciality-picker-content{padding-bottom:88px}.speciality-picker-content>.row{height:100%}.speciality-picker-content>.row>.col,.speciality-picker-content>.row>.col>.column{height:inherit}.speciality-picker-title{font-size:20px}.speciality-picker-text{color:var(--v-grey-base);letter-spacing:.3px}.speciality-picker-text ul{padding-left:0;list-style-type:none}.speciality-picker-text ul>li:not(:last-child){margin-bottom:4px}.speciality-picker-bottom{position:absolute;left:0;bottom:0;width:100%}.speciality-picker .column{padding:20px 15px;border:1px solid #00a500;border-radius:24px}.speciality-picker .column-helper{height:100%;overflow-y:auto;overflow-x:hidden}.speciality-picker .column-helper>div,.speciality-picker .column-helper>div>div{height:100%}.speciality-picker .column .list-group-item{cursor:move}.speciality-picker .column .list-group-item:not(:last-child){margin-bottom:8px}.speciality-picker .column .list-group-item.highest-priority{color:var(--v-success-base)}",""]),t.exports=o},1648:function(t,e,n){var content=n(1752);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("a3fe467e",content,!0,{sourceMap:!1})},1698:function(t,e,n){"use strict";n.r(e);var o=n(28),l=(n(63),n(126),n(1689)),r={name:"SpecialityDialog",components:{draggable:n.n(l).a},props:{isShownSpecialitiesDialog:{type:Boolean,required:!0}},data:function(){return{contentElHeight:"auto"}},computed:{availableSpecializations:{get:function(){return this.$store.getters["settings/availableSpecializations"]},set:function(t){this.$store.commit("settings/UPDATE_AVAILABLE_SPECIALIZATIONS",t)}},teacherSpecialities:{get:function(){return this.$store.getters["settings/teacherSpecialities"]},set:function(t){this.$store.commit("settings/UPDATE_TEACHER_SPECIALITIES",t)}}},watch:{isShownSpecialitiesDialog:function(t,e){var n=this;t&&this.$nextTick((function(){window.setTimeout((function(){return n.setContentElHeight()}))}))}},methods:{onEnd:function(t){if(this.teacherSpecialities.length>8){var e=Object(o.a)(this.availableSpecializations),n=Object(o.a)(this.teacherSpecialities);e.splice(t.oldIndex,0,n[t.newIndex]),n.splice(t.newIndex,1),this.$store.commit("settings/UPDATE_AVAILABLE_SPECIALIZATIONS",e),this.$store.commit("settings/UPDATE_TEACHER_SPECIALITIES",n)}},setContentElHeight:function(){var t,e;this.contentElHeight="calc(100% - ".concat(null!==(t=null===(e=this.$refs.header)||void 0===e?void 0:e.clientHeight)&&void 0!==t?t:0,"px)")},onResize:function(){this.setContentElHeight()},submitData:function(){var t=this;this.$store.dispatch("settings/updateSpecialities").then((function(){return t.$emit("close-dialog")}))}}},c=(n(1646),n(22)),d=n(42),h=n.n(d),v=n(1327),m=n(1360),f=n(1361),_=n(699),x=n.n(_),y=n(153),component=Object(c.a)(r,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("l-dialog",t._g({directives:[{name:"resize",rawName:"v-resize",value:t.onResize,expression:"onResize"}],attrs:{dialog:t.isShownSpecialitiesDialog,"max-width":"820","custom-class":"speciality-picker"}},t.$listeners),[o("div",{ref:"header",staticClass:"header"},[o("div",{staticClass:"speciality-picker-title font-weight-medium"},[t._v("\n      "+t._s(t.$t("manage_specialties"))+":\n    ")]),t._v(" "),o("div",{staticClass:"speciality-picker-text body-2 mt-2"},[o("ul",[o("li",[t._v("1. "+t._s(t.$t("drag_your_teaching_specialities")))]),t._v(" "),o("li",[t._v("\n          2.\n          "+t._s(t.$t("top_specialities_will_be_featured_on_teacher_search_results_page"))+"\n        ")])])]),t._v(" "),o("v-row",{staticClass:"my-0"},[o("v-col",{staticClass:"col-6 py-0"},[o("div",{staticClass:"text--gradient text-center subtitle-2 font-weight-medium mt-3 mb-1"},[t._v("\n          "+t._s(t.$t("available_specialties"))+":\n        ")])]),t._v(" "),o("v-col",{staticClass:"col-6 py-0"},[o("div",{staticClass:"text--gradient text-center subtitle-2 font-weight-medium mt-3 mb-1"},[t._v("\n          "+t._s(t.$t("selected_specialties"))+":\n        ")])])],1)],1),t._v(" "),o("div",{staticClass:"speciality-picker-content",style:{height:t.contentElHeight}},[o("v-row",{staticClass:"my-0"},[o("v-col",{staticClass:"col-6 py-0"},[o("div",{staticClass:"column"},[o("div",{staticClass:"column-helper l-scroll l-scroll--dark-grey l-scroll--large"},[o("div",{staticClass:"column-content"},[o("draggable",{staticClass:"list-group",attrs:{group:"specialities"},on:{end:t.onEnd},model:{value:t.availableSpecializations,callback:function(e){t.availableSpecializations=e},expression:"availableSpecializations"}},t._l(t.availableSpecializations,(function(element){return o("div",{key:element.name,staticClass:"list-group-item body-1"},[t._v("\n                  "+t._s(element.name)+"\n                ")])})),0)],1)])])]),t._v(" "),o("v-col",{staticClass:"col-6 py-0"},[o("div",{staticClass:"column l-scroll"},[o("div",{staticClass:"column-helper l-scroll--dark-grey l-scroll--large"},[o("div",{staticClass:"column-content"},[o("draggable",{staticClass:"list-group",attrs:{group:"specialities"},model:{value:t.teacherSpecialities,callback:function(e){t.teacherSpecialities=e},expression:"teacherSpecialities"}},t._l(t.teacherSpecialities,(function(element,e){return o("div",{key:element.name,class:["list-group-item body-1",{"highest-priority":e<3}]},[t._v("\n                  "+t._s(element.name)+"\n                ")])})),0)],1)])])])],1),t._v(" "),o("div",{staticClass:"speciality-picker-bottom d-flex justify-end"},[o("v-btn",{staticClass:"font-weight-medium",attrs:{color:"primary"},on:{click:t.submitData}},[o("svg",{staticClass:"mr-1",attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[o("use",{attrs:{"xlink:href":n(91)+"#save-icon"}})]),t._v("\n        "+t._s(t.$t("save_changes"))+"\n      ")])],1)],1)])}),[],!1,null,null,null);e.default=component.exports;h()(component,{LDialog:n(149).default}),h()(component,{VBtn:v.a,VCol:m.a,VRow:f.a}),x()(component,{Resize:y.a})},1751:function(t,e,n){"use strict";n(1648)},1752:function(t,e,n){var o=n(18)(!1);o.push([t.i,".checkbox[data-v-22c932db]:not(:last-child){margin-bottom:20px}",""]),t.exports=o},1930:function(t,e,n){"use strict";n.r(e);var o=n(10),l=(n(62),n(9),n(20),n(37),n(44),n(1375)),r=n(1698),c={name:"TeachingPreferencesInfo",components:{UserSettingTemplate:l.default,SpecialityDialog:r.default},data:function(){return{isShownSpecialitiesDialog:!1}},computed:{preferences:function(){return this.$store.state.settings.preferenceItems},selectedPreferences:{get:function(){return this.preferences.filter((function(t){return t.isSelected}))},set:function(t){return this.$store.commit("settings/UPDATE_TEACHING_PREFERENCE_ITEMS",t)}}},beforeCreate:function(){var t=this;return Object(o.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Promise.all([t.$store.dispatch("settings/getTeachingPreferences"),t.$store.dispatch("settings/getSpecialities")]);case 2:case"end":return e.stop()}}),e)})))()},methods:{submitData:function(){this.$store.dispatch("settings/updateTeachingPreferences")}}},d=(n(1751),n(22)),h=n(42),v=n.n(h),m=n(1327),f=n(1611),_=n(1360),x=n(261),y=n(1361),component=Object(d.a)(c,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return t.preferences.length?o("user-setting-template",{attrs:{title:t.$t("teaching_preferences"),"submit-func":t.submitData}},[o("div",{staticClass:"mb-4 mb-md-7"},[o("v-row",[o("v-col",{staticClass:"col-12 col-md-10"},[o("div",{staticClass:"input-wrap"},[o("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[t._v("\n            "+t._s(t.$t("specialities"))+"\n          ")]),t._v(" "),o("div",[o("v-btn",{staticClass:"gradient font-weight-medium mt-2",on:{click:function(e){t.isShownSpecialitiesDialog=!0}}},[o("div",{staticClass:"mr-1"},[o("v-img",{attrs:{src:n(972),width:"24",height:"24"}})],1),t._v(" "),o("div",{staticClass:"text--gradient"},[t._v("\n                "+t._s(t.$t("manage_specialties"))+"\n              ")])])],1)])])],1)],1),t._v(" "),o("div",[o("v-row",[o("v-col",{staticClass:"col-12 col-md-10"},[o("div",{staticClass:"input-wrap"},[o("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-2"},[t._v("\n            "+t._s(t.$t("which_levels_would_you_like_to_teach"))+"\n          ")]),t._v(" "),o("div",t._l(t.preferences,(function(e,n){return o("div",{key:n,staticClass:"checkbox"},[o("v-checkbox",{staticClass:"l-checkbox",attrs:{value:e,label:e.name,"hide-details":"",ripple:!1},model:{value:t.selectedPreferences,callback:function(e){t.selectedPreferences=e},expression:"selectedPreferences"}})],1)})),0)])])],1)],1),t._v(" "),o("speciality-dialog",{attrs:{"is-shown-specialities-dialog":t.isShownSpecialitiesDialog},on:{"close-dialog":function(e){t.isShownSpecialitiesDialog=!1}}})],1):t._e()}),[],!1,null,"22c932db",null);e.default=component.exports;v()(component,{UserSettingTemplate:n(1375).default}),v()(component,{VBtn:m.a,VCheckbox:f.a,VCol:_.a,VImg:x.a,VRow:y.a})}}]);