exports.ids = [130];
exports.modules = {

/***/ 1373:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1446);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("352751b0", content, true, context)
};

/***/ }),

/***/ 1445:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1373);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1446:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".p24-payin{max-width:620px;min-height:410px;padding:56px 0 80px}@media only screen and (max-width:991px){.p24-payin{padding:38px 0 64px}}@media only screen and (max-width:479px){.p24-payin{padding:28px 0 45px}}.p24-payin--error{max-width:800px}.p24-payin a{color:var(--v-success-base)!important;text-decoration:none}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1490:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/checkout/p24-payin/_id/index.vue?vue&type=template&id=cbef3c2a&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-col',{staticClass:"col-12 pa-0"},[_c('div',{class:['p24-payin mx-auto', { 'p24-payin--error': _vm.isFailure }]},[_c('v-container',{staticClass:"py-0",attrs:{"fluid":""}},[_c('v-row',[_c('v-col',{staticClass:"col-12"},[(_vm.isFailure)?[_c('div',{staticClass:"text-center"},[_c('div',{domProps:{"innerHTML":_vm._s(
                  _vm.$t(
                    'przelewy24_payment_failure_try_again_or_contact_langu_customer_service'
                  )
                )}}),_vm._v(" "),(_vm.username)?_c('div',{staticClass:"mt-6"},[_c('v-btn',{staticClass:"gradient font-weight-medium",attrs:{"to":("/teacher/" + _vm.username)}},[_c('div',{staticClass:"text--gradient"},[_vm._v("\n                    "+_vm._s(_vm.buttonText)+"\n                  ")])])],1):_vm._e()])]:[_c('div',{staticClass:"text-center"},[_c('div',{domProps:{"innerHTML":_vm._s(_vm.$t('confirming_payment_p24'))}}),_vm._v(" "),_c('p',[_c('a',{staticClass:"text--gradient",attrs:{"href":"https://instagram.com/heylangu","target":"_blank"}},[_vm._v("\n                  https://instagram.com/heylangu\n                ")])]),_vm._v(" "),_c('div',{staticClass:"spinner mt-6"},[_c('v-img',{staticClass:"mx-auto",attrs:{"src":__webpack_require__(665),"width":"48","height":"48"}})],1)])]],2)],1)],1)],1)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/checkout/p24-payin/_id/index.vue?vue&type=template&id=cbef3c2a&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/checkout/p24-payin/_id/index.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
const CALL_LIMIT = 100;
/* harmony default export */ var _idvue_type_script_lang_js_ = ({
  name: 'P24Payin',

  data() {
    return {
      called: 0,
      intervalId: null,
      username: null,
      isFailure: false
    };
  },

  computed: {
    buttonText() {
      return `${"'http://localhost:3000'"}/teacher/${this.username}`;
    }

  },

  beforeMount() {
    this.username = window.localStorage.getItem('teacher-username');
    this.intervalId = window.setInterval(() => {
      this.called++;

      if (this.called === CALL_LIMIT) {
        this.clearInterval();
        this.isFailure = true;
      }

      this.$store.dispatch('loadingAllow', false);
      this.$store.dispatch('purchase/p24Paying', this.$route.params.id).then(async data => {
        if (+(data === null || data === void 0 ? void 0 : data.confirmed) === 1) {
          this.clearInterval();
          this.clearLocalStorage();

          if (this.$store.getters['teacher_profile/isSelectedTrial']) {
            await this.$cookiz.set('confirmation_page_allowed', 1, {
              domain: ".langu.loc",
              path: '/'
            });
            return this.$router.push('/user/lessons/confirmation');
          } else {
            await this.$cookiz.set('thank_you_page_allowed', 1, {
              domain: ".langu.loc",
              path: '/'
            });
            return this.$router.push('/user/lessons/thank-you');
          }
        }
      }).catch(e => {
        this.isFailure = true;
      }).finally(() => {
        this.$store.dispatch('loadingAllow', true);
      });
    }, 5000);
  },

  beforeDestroy() {
    this.clearInterval();
    this.clearLocalStorage();
  },

  methods: {
    clearInterval() {
      clearInterval(this.intervalId);
    },

    clearLocalStorage() {
      window.localStorage.removeItem('teacher-username');
    }

  }
});
// CONCATENATED MODULE: ./pages/checkout/p24-payin/_id/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var p24_payin_idvue_type_script_lang_js_ = (_idvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./pages/checkout/p24-payin/_id/index.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1445)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  p24_payin_idvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "77fb9346"
  
)

/* harmony default export */ var _id = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */






installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ })

};;
//# sourceMappingURL=index.js.map