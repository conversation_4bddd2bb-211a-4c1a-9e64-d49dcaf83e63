(window.webpackJsonp=window.webpackJsonp||[]).push([[48,56],{1372:function(t,o,e){var r=e(43);t.exports=function(t){return r(Set.prototype.values,t)}},1384:function(t,o,e){"use strict";var r=e(43),n=e(79),l=e(32);t.exports=function(){for(var t=l(this),o=n(t.add),e=0,c=arguments.length;e<c;e++)r(o,t,arguments[e]);return t}},1390:function(t,o,e){"use strict";e(872)("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),e(873))},1391:function(t,o,e){"use strict";e(11)({target:"Set",proto:!0,real:!0,forced:!0},{addAll:e(1384)})},1392:function(t,o,e){"use strict";e(11)({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:e(874)})},1393:function(t,o,e){"use strict";var r=e(11),n=e(87),l=e(43),c=e(79),d=e(32),h=e(125),v=e(86);r({target:"Set",proto:!0,real:!0,forced:!0},{difference:function(t){var o=d(this),e=new(h(o,n("Set")))(o),r=c(e.delete);return v(t,(function(t){l(r,e,t)})),e}})},1394:function(t,o,e){"use strict";var r=e(11),n=e(32),l=e(69),c=e(1372),d=e(86);r({target:"Set",proto:!0,real:!0,forced:!0},{every:function(t){var o=n(this),e=c(o),r=l(t,arguments.length>1?arguments[1]:void 0);return!d(e,(function(t,e){if(!r(t,t,o))return e()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1395:function(t,o,e){"use strict";var r=e(11),n=e(87),l=e(43),c=e(79),d=e(32),h=e(69),v=e(125),f=e(1372),m=e(86);r({target:"Set",proto:!0,real:!0,forced:!0},{filter:function(t){var o=d(this),e=f(o),r=h(t,arguments.length>1?arguments[1]:void 0),w=new(v(o,n("Set"))),_=c(w.add);return m(e,(function(t){r(t,t,o)&&l(_,w,t)}),{IS_ITERATOR:!0}),w}})},1396:function(t,o,e){"use strict";var r=e(11),n=e(32),l=e(69),c=e(1372),d=e(86);r({target:"Set",proto:!0,real:!0,forced:!0},{find:function(t){var o=n(this),e=c(o),r=l(t,arguments.length>1?arguments[1]:void 0);return d(e,(function(t,e){if(r(t,t,o))return e(t)}),{IS_ITERATOR:!0,INTERRUPTED:!0}).result}})},1397:function(t,o,e){"use strict";var r=e(11),n=e(87),l=e(43),c=e(79),d=e(32),h=e(125),v=e(86);r({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function(t){var o=d(this),e=new(h(o,n("Set"))),r=c(o.has),f=c(e.add);return v(t,(function(t){l(r,o,t)&&l(f,e,t)})),e}})},1398:function(t,o,e){"use strict";var r=e(11),n=e(43),l=e(79),c=e(32),d=e(86);r({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function(t){var o=c(this),e=l(o.has);return!d(t,(function(t,r){if(!0===n(e,o,t))return r()}),{INTERRUPTED:!0}).stopped}})},1399:function(t,o,e){"use strict";var r=e(11),n=e(87),l=e(43),c=e(79),d=e(45),h=e(32),v=e(209),f=e(86);r({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function(t){var o=v(this),e=h(t),r=e.has;return d(r)||(e=new(n("Set"))(t),r=c(e.has)),!f(o,(function(t,o){if(!1===l(r,e,t))return o()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1400:function(t,o,e){"use strict";var r=e(11),n=e(43),l=e(79),c=e(32),d=e(86);r({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function(t){var o=c(this),e=l(o.has);return!d(t,(function(t,r){if(!1===n(e,o,t))return r()}),{INTERRUPTED:!0}).stopped}})},1401:function(t,o,e){"use strict";var r=e(11),n=e(17),l=e(32),c=e(61),d=e(1372),h=e(86),v=n([].join),f=[].push;r({target:"Set",proto:!0,real:!0,forced:!0},{join:function(t){var o=l(this),e=d(o),r=void 0===t?",":c(t),n=[];return h(e,f,{that:n,IS_ITERATOR:!0}),v(n,r)}})},1402:function(t,o,e){"use strict";var r=e(11),n=e(87),l=e(69),c=e(43),d=e(79),h=e(32),v=e(125),f=e(1372),m=e(86);r({target:"Set",proto:!0,real:!0,forced:!0},{map:function(t){var o=h(this),e=f(o),r=l(t,arguments.length>1?arguments[1]:void 0),w=new(v(o,n("Set"))),_=d(w.add);return m(e,(function(t){c(_,w,r(t,t,o))}),{IS_ITERATOR:!0}),w}})},1403:function(t,o,e){"use strict";var r=e(11),n=e(5),l=e(79),c=e(32),d=e(1372),h=e(86),v=n.TypeError;r({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function(t){var o=c(this),e=d(o),r=arguments.length<2,n=r?void 0:arguments[1];if(l(t),h(e,(function(e){r?(r=!1,n=e):n=t(n,e,e,o)}),{IS_ITERATOR:!0}),r)throw v("Reduce of empty set with no initial value");return n}})},1404:function(t,o,e){"use strict";var r=e(11),n=e(32),l=e(69),c=e(1372),d=e(86);r({target:"Set",proto:!0,real:!0,forced:!0},{some:function(t){var o=n(this),e=c(o),r=l(t,arguments.length>1?arguments[1]:void 0);return d(e,(function(t,e){if(r(t,t,o))return e()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}})},1405:function(t,o,e){"use strict";var r=e(11),n=e(87),l=e(43),c=e(79),d=e(32),h=e(125),v=e(86);r({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function(t){var o=d(this),e=new(h(o,n("Set")))(o),r=c(e.delete),f=c(e.add);return v(t,(function(t){l(r,e,t)||l(f,e,t)})),e}})},1406:function(t,o,e){"use strict";var r=e(11),n=e(87),l=e(79),c=e(32),d=e(125),h=e(86);r({target:"Set",proto:!0,real:!0,forced:!0},{union:function(t){var o=c(this),e=new(d(o,n("Set")))(o);return h(t,l(e.add),{that:e}),e}})},1428:function(t,o,e){"use strict";e(35),e(81),e(23),e(6),e(24),e(38);var r={computed:{role:function(){return this.$store.getters["classroom/role"]}},methods:{setTool:function(t,o){this.$store.commit("classroom/enableContainerComponent","pointer"===t),this.$socket.emit("cursor-moved",{tool:t,cursor:o.replace(/(-cursor|cursor-)/i,""),lessonId:this.$store.state.classroom.lessonId}),this.$store.commit("classroom/setUserTool",t),this.$store.commit("classroom/setUserCursor",o);var e=document.body,r=e.classList;this.removeCursors(r),e.classList.add("".concat(this.role,"-").concat(o)),this.classList=e.classList},removeCursors:function(t){t.forEach((function(t){t.includes("cursor")&&document.body.classList.remove(t)}))}}},n=e(22),component=Object(n.a)(r,undefined,undefined,!1,null,null,null);o.a=component.exports},1438:function(t,o,e){"use strict";var r=e(28),n=(e(20),e(1390),e(37),e(1391),e(1392),e(1393),e(1394),e(1395),e(1396),e(1397),e(1398),e(1399),e(1400),e(1401),e(1402),e(1403),e(1404),e(1405),e(1406),e(44),e(63),{data:function(){return{timeoutId:null,userStatuses:{},arrStatusId:[]}},computed:{preparedArr:function(){return Object(r.a)(new Set(this.arrStatusId))}},mounted:function(){var t=this;this.timeoutId=window.setInterval((function(){t.refreshStatusOnline(),t.arrStatusId.length||t.clearInterval()}),1e4)},beforeDestroy:function(){this.timeoutId&&this.clearInterval()},methods:{refreshStatusOnline:function(){var t=this;this.arrStatusId.length&&this.$store.dispatch("user/refreshStatusOnline",this.preparedArr).then((function(o){return t.userStatuses=o}))},clearInterval:function(){window.clearInterval(this.timeoutId),this.timeoutId=null}}}),l=e(22),component=Object(l.a)(n,undefined,undefined,!1,null,null,null);o.a=component.exports},1621:function(t,o,e){"use strict";var r=e(2),n=e(28),l=e(10),c=(e(62),e(40),e(174),e(24),e(38),e(20),e(80),e(6),e(7),e(8),e(9),e(14),e(15),e(859)),d=e(266);function h(object,t){var o=Object.keys(object);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(object);t&&(e=e.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),o.push.apply(o,e)}return o}function v(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?h(Object(source),!0).forEach((function(o){Object(r.a)(t,o,source[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):h(Object(source)).forEach((function(o){Object.defineProperty(t,o,Object.getOwnPropertyDescriptor(source,o))}))}return t}var f={computed:{lessonId:function(){return this.$store.state.classroom.lessonId},role:function(){return this.$store.getters["classroom/role"]},acceptedFiles:function(){return this.$store.state.classroom.acceptedFiles}},methods:{uploadFiles:function(t){var o=this;return Object(l.a)(regeneratorRuntime.mark((function e(){var r,i,l,h,f,data,m;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=Object(n.a)(t),r=new FormData,i=0;case 3:if(!(i<=t.length-1)){e.next=23;break}if(l=t[i],h=Object(c.getFileExtension)(l.name),!(l.size>d.a)){e.next=10;break}return e.next=9,o.$store.dispatch("snackbar/error",{errorMessage:o.$t("filename_size_should_be_less_than",{fileName:l.name,value:"".concat((d.a/8/1e3).toFixed(0)," Mb")}),timeout:5e3});case 9:return e.abrupt("continue",20);case 10:if(!o.acceptedFiles.officeTypes.includes(h)){e.next=19;break}return e.next=13,o.$store.dispatch("classroom/convertOfficeToPdf",l);case 13:f=e.sent,data=f.data,m=f.fileName,r.append(i.toString(),new Blob([data]),m),e.next=20;break;case 19:r.append(i.toString(),l);case 20:i++,e.next=3;break;case 23:o.$store.dispatch("classroom/uploadFiles",r).then((function(t){var e=0,r=0;o.$store.commit("classroom/addAssets",t),t.forEach((function(t){var n,l,d,h,f={id:t.id,lessonId:o.lessonId,asset:v(v({},t.asset),{},{index:o.$store.state.classroom.maxIndex+1,owner:o.role,top:o.$store.getters["classroom/zoomAsset"].asset.y+r+100,left:o.viewportWidth/2+o.$store.getters["classroom/zoomAsset"].asset.x+e-250})},m=Object(c.getFileExtension)(f.asset.path);if(null!==(n=o.acceptedFiles)&&void 0!==n&&n.pdfTypes.includes(m))h="pdf";else if(null!==(l=o.acceptedFiles)&&void 0!==l&&l.imageTypes.includes(m))h="image";else{if(null===(d=o.acceptedFiles)||void 0===d||!d.audioTypes.includes(m))return;h="audio"}f.asset.type=h,o.$store.commit("classroom/moveAsset",f),o.$store.dispatch("classroom/moveAsset",f),o.$socket.emit("asset-added",f),e+=50,r+=50}))})).catch((function(t){throw t}));case 24:case"end":return e.stop()}}),e)})))()}}},m=e(22),component=Object(m.a)(f,undefined,undefined,!1,null,null,null);o.a=component.exports},1669:function(t,o,e){var content=e(1794);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,e(19).default)("3a5e080a",content,!0,{sourceMap:!1})},1793:function(t,o,e){"use strict";e(1669)},1794:function(t,o,e){var r=e(18)(!1);r.push([t.i,".toolbar[data-v-53b5e223]{position:fixed;z-index:99999!important}label.popup-load-files-label-upload[data-v-53b5e223]{margin-right:0}.toolbar-buttons-wrapper[data-v-53b5e223]{position:absolute;top:50%;right:2%;transform:translateY(-50%)}.cursor-pointer[data-v-53b5e223],.cursor-pointer *[data-v-53b5e223]{cursor:pointer!important}.toolbar-buttons[data-v-53b5e223]{margin-bottom:0;padding:8px 0}.toolbar-buttons-horizontal-file.toolbar-show[data-v-53b5e223],.toolbar-buttons-horizontal.toolbar-show[data-v-53b5e223]{display:flex!important}.toolbar-buttons li[data-v-53b5e223]{list-style:none}.toolbar-button-wrapper form[data-v-53b5e223]{display:inline-block;width:100%}.toolbar-button-wrapper-horizontal[data-v-53b5e223]{width:40px;height:40px;display:flex;justify-content:center;position:relative}.toolbar-button-wrapper-pencil>button[data-v-53b5e223]{padding:9px}.toolbar-button-wrapper-exit button[data-v-53b5e223]{padding-left:7px;padding-right:10px}.toolbar-button-wrapper-reset button[data-v-53b5e223]{padding-left:10px;padding-right:10px}.toolbar-button-wrapper-finish button[data-v-53b5e223]{padding-right:7px}.toolbar-button-wrapper-horizontal-books button[data-v-53b5e223]{padding:9px}.toolbar-button-wrapper-horizontal-laptop button[data-v-53b5e223]{padding-top:10px}.toolbar-buttons-horizontal>ul[data-v-53b5e223]{display:flex;padding:0 10px;background-color:#fff;border-radius:6px;box-shadow:0 2px 10px rgba(0,0,0,.25)}.toolbar-button-wrapper-horizontal-draw-line button[data-v-53b5e223]{padding:10px 5px 6px 10px}.toolbar-button-wrapper-horizontal-draw-pencil button[data-v-53b5e223]{padding:9px}.toolbar-button-item-hand[data-v-53b5e223]{padding-left:0}.toolbar-buttons-horizontal>ul li[data-v-53b5e223]:first-child,.toolbar-buttons-horizontal>ul li:first-child button[data-v-53b5e223]{border-bottom-left-radius:6px!important;border-top-left-radius:6px!important}.toolbar-buttons-horizontal>ul li[data-v-53b5e223]:last-child,.toolbar-buttons-horizontal>ul li:last-child button[data-v-53b5e223]{border-bottom-right-radius:6px!important;border-top-right-radius:6px!important}#toolbar-switch[data-v-53b5e223]{border-top-left-radius:4px;border-top-right-radius:4px}.toolbar--student .toolbar-button-item.selected:not(:disabled) svg[data-v-53b5e223],.toolbar--student .toolbar-button-wrapper-horizontal:hover svg[data-v-53b5e223],.toolbar--student .toolbar-button-wrapper:hover>button:enabled>svg[data-v-53b5e223],.toolbar--student .toolbar-button-wrapper:hover>form>button:enabled>svg[data-v-53b5e223]{color:var(--v-studentColor-base)!important}.toolbar--teacher .toolbar-button-item.selected:not(:disabled) svg[data-v-53b5e223],.toolbar--teacher .toolbar-button-wrapper-horizontal:hover svg[data-v-53b5e223],.toolbar--teacher .toolbar-button-wrapper:hover>button:enabled>svg[data-v-53b5e223],.toolbar--teacher .toolbar-button-wrapper:hover>form>button:enabled>svg[data-v-53b5e223]{color:var(--v-teacherColor-base)!important}.toolbar-button-wrapper .toolbar-button-item:disabled svg[data-v-53b5e223]{color:#c6c6c6!important;fill:#c6c6c6!important;stroke:#c6c6c6!important}.toolbar-button-item svg[data-v-53b5e223]{color:var(--v-darkLight-base)}.hover-btn-info-horizontal[data-v-53b5e223]{top:-20px;right:-60%}.toolbar-button-replace+.hover-btn-info[data-v-53b5e223]{top:40%}.selected[data-v-53b5e223]{border-bottom:none}",""]),t.exports=r},1946:function(t,o,e){"use strict";e.r(o);var r=e(2),n=(e(31),e(20),e(80),e(63),e(6),e(55),e(7),e(8),e(9),e(14),e(15),e(266)),l=e(1428),c=e(1621),d=e(1438);function h(object,t){var o=Object.keys(object);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(object);t&&(e=e.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),o.push.apply(o,e)}return o}var v={name:"Toolbar",mixins:[l.a,c.a,d.a],props:{studentId:{type:String,required:!0},file:{type:Object,required:!0},viewportWidth:{type:Number,required:!0},viewportHeight:{type:Number,required:!0},scale:{type:Number,default:1},minZoom:{type:Number,required:!0},isFinishedAllowed:{type:Boolean,required:!0}},data:function(){return{buzzed:!1,currentTool:"pointer",currentHorizontalMenu:null,offset:5}},computed:{isCanvasOversizeX:function(){return n.n>this.viewportWidth},isScaledCanvasOversizeX:function(){return n.n*this.scale>this.viewportWidth},isCanvasOversizeY:function(){return n.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return n.k*this.scale>this.viewportHeight},style:function(){return{bottom:this.isScaledCanvasOversizeY?"10px":"".concat(this.viewportHeight-n.k*this.scale+2*this.offset,"px"),right:this.isScaledCanvasOversizeX?"10px":"".concat(this.viewportWidth-n.n*this.scale+2*this.offset,"px")}},studentStatus:function(){var t,o="offline";return Object.prototype.hasOwnProperty.call(this.userStatuses,null===(t=this.studentId)||void 0===t?void 0:t.toString())&&(o=this.userStatuses[this.studentId]),o},alertDisabled:function(){return this.buzzed||"online"!==this.studentStatus},maxIndex:function(){return this.$store.state.classroom.maxIndex+100},isLocked:function(){var t,o;return null===(t=this.file)||void 0===t||null===(o=t.asset)||void 0===o?void 0:o.isLocked},isTeacher:function(){return this.$store.getters["user/isTeacher"]},isStudent:function(){return this.$store.getters["user/isStudent"]},isLockedForStudent:function(){return this.isLocked&&this.isStudent},lessonId:function(){return this.$store.state.classroom.lessonId},isLessonFinished:function(){return this.$store.getters["classroom/isLessonFinished"]},defaultZoomIndex:function(){return this.minZoom>1?this.minZoom:1},acceptedFilesStr:function(){return this.$store.getters["classroom/acceptedFilesStr"]}},watch:{isLockedForStudent:function(t,o){t&&(this.resetCurrentValues(),this.$store.commit("classroom/closeVideoInput"))}},beforeMount:function(){this.arrStatusId=[this.studentId],this.refreshStatusOnline()},beforeDestroy:function(){this.resetCurrentValues()},methods:{selectToolClickHandler:function(t,o){this.currentTool=t,this.currentHorizontalMenu=null,this.setTool(t,o)},uploadFromComputer:function(t){this.currentHorizontalMenu=null,this.uploadFiles(t.target.files)},buzz:function(){var t=this;this.buzzed=!0,setTimeout((function(){t.buzzed=!1}),3e4),this.$store.dispatch("classroom/buzz",this.lessonId)},reset:function(){var t,o,e=this,i=1,l=0,c=0;this.$store.state.classroom.assets.slice(0).forEach((function(d){var v=function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?h(Object(source),!0).forEach((function(o){Object(r.a)(t,o,source[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):h(Object(source)).forEach((function(o){Object.defineProperty(t,o,Object.getOwnPropertyDescriptor(source,o))}))}return t}({},d.asset);switch(i++,v.type){case"shape":case"lock":break;case"editor":v.width=.66*(e.isCanvasOversizeX?e.viewportWidth:n.n),(t=.8*(e.isCanvasOversizeY?e.viewportHeight:n.k))>1200&&(t=1200),t<400&&(t=400),v.height=t-2*e.offset,v.top=e.offset,v.left=e.offset,v.index=1;break;case"whereby":v.width=400,v.height=300,v.top=e.offset,v.left=(e.isCanvasOversizeX?e.viewportWidth:n.n)-v.width-e.offset,v.index=i;break;case"pdf":case"image":case"video":case"audio":o=n.j/d.asset.width,v.width=n.j,v.height=v.height*o,v.top=e.$store.getters["classroom/zoomAsset"].asset.y+c+100,v.left=e.viewportWidth/2+e.$store.getters["classroom/zoomAsset"].asset.x+l-250,v.index=i,l+=50,c+=50;break;case"zoom":v.zoomIndex=e.defaultZoomIndex,v.x=0,v.y=0}e.$store.commit("classroom/moveAsset",{id:d.id,asset:v}),e.$store.dispatch("classroom/moveAsset",{id:d.id,lessonId:d.lessonId,asset:v})}))},toggleVideoInput:function(){this.$store.commit("classroom/toggleVideoInput")},openLibrary:function(){this.currentHorizontalMenu=null,this.$store.commit("classroom/toggleLibrary")},toggleStudentRoomStatus:function(){var t={isLocked:!this.isLocked};this.$store.commit("classroom/moveAsset",{id:this.file.id,asset:t}),this.$store.dispatch("classroom/moveAsset",{id:this.file.id,lessonId:this.lessonId,asset:t})},resetCurrentValues:function(){this.currentTool="pointer",this.currentHorizontalMenu=null},finishLesson:function(){var t=this;this.$store.dispatch("lesson/finishLesson",this.lessonId).then((function(){t.exitLesson()})).catch((function(o){t.$store.dispatch("snackbar/error"),console.info(o)}))},exitLesson:function(){window.location="/user/lessons"}}},f=(e(1793),e(22)),component=Object(f.a)(v,(function(){var t=this,o=t.$createElement,r=t._self._c||o;return r("div",{class:["toolbar","toolbar--"+t.role],style:t.style},[r("ul",{ref:"toolbar_buttons",staticClass:"toolbar-buttons",attrs:{id:"toolbar-buttons"}},[r("li",{staticClass:"toolbar-button-wrapper"},[r("button",{class:["toolbar-button-item toolbar-button-pointer cursor-pointer",{selected:"pointer"===t.currentTool}],attrs:{id:"toolbar-switch","data-toolbar-default-cursor":"",disabled:t.isLockedForStudent},on:{click:function(o){return t.selectToolClickHandler("pointer","cursor-pointer")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"32",height:"34",viewBox:"0 0 32 34"}},[r("use",{attrs:{"xlink:href":e(862)+"#pointer"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v(t._s(t.$t("default_cursor")))])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper toolbar-button-wrapper-pencil",on:{mouseleave:function(o){o.stopPropagation(),t.currentHorizontalMenu=null}}},[r("button",{class:["toolbar-button-item toolbar-button-hand cursor-pointer",{selected:"line"===t.currentTool||"circle"===t.currentTool||"triangle"===t.currentTool||"square"===t.currentTool||"pen"===t.currentTool}],attrs:{disabled:t.isLockedForStudent},on:{click:function(o){t.currentHorizontalMenu="toolbar-horizontal"}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"33",height:"33",viewBox:"0 0 33 33"}},[r("use",{attrs:{"xlink:href":e(862)+"#pencil"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v(t._s(t.$t("drawing")))]),t._v(" "),r("div",{class:["toolbar-buttons-horizontal",{"toolbar-show":"toolbar-horizontal"===t.currentHorizontalMenu}]},[r("ul",[r("li",{staticClass:"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-draw-line"},[r("button",{class:["toolbar-button-item toolbar-button-item-horizontal cursor-pointer",{selected:"line"===t.currentTool}],attrs:{"data-toolbar-tool-line":""},on:{click:function(o){return t.selectToolClickHandler("line","pencil-cursor")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"39",height:"37",viewBox:"0 0 39 37"}},[r("use",{attrs:{"xlink:href":e(862)+"#draw-line"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("draw_line"))+"\n            ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper-horizontal"},[r("button",{class:["toolbar-button-item toolbar-button-item-horizontal cursor-pointer",{selected:"circle"===t.currentTool}],attrs:{"data-toolbar-tool-circle":""},on:{click:function(o){return t.selectToolClickHandler("circle","pencil-cursor")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"36",height:"37",viewBox:"0 0 39 40"}},[r("use",{attrs:{"xlink:href":e(862)+"#draw-circle"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("draw_circle"))+"\n            ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper-horizontal"},[r("button",{class:["toolbar-button-item toolbar-button-item-horizontal cursor-pointer",{selected:"triangle"===t.currentTool}],attrs:{"data-toolbar-tool-triangle":""},on:{click:function(o){return t.selectToolClickHandler("triangle","pencil-cursor")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"41",height:"34",viewBox:"0 0 41 34"}},[r("use",{attrs:{"xlink:href":e(862)+"#draw-triangle"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("draw_triangle"))+"\n            ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper-horizontal"},[r("button",{class:["toolbar-button-item toolbar-button-item-horizontal cursor-pointer",{selected:"square"===t.currentTool}],attrs:{"data-toolbar-tool-square":""},on:{click:function(o){return t.selectToolClickHandler("square","pencil-cursor")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"36",height:"38",viewBox:"0 0 36 38"}},[r("use",{attrs:{"xlink:href":e(862)+"#draw-square"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("draw_square"))+"\n            ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-draw-pencil"},[r("button",{class:["toolbar-button-item toolbar-button-item-horizontal cursor-pointer",{selected:"pen"===t.currentTool}],attrs:{"data-toolbar-tool-pen":""},on:{click:function(o){return t.selectToolClickHandler("pen","pencil-cursor")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"33",height:"33",viewBox:"0 0 33 33"}},[r("use",{attrs:{"xlink:href":e(862)+"#pencil"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("enable_drawing_tool"))+"\n            ")])])])])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper"},[r("button",{class:["toolbar-button-item cursor-pointer",{selected:"eraser"===t.currentTool}],attrs:{"data-toolbar-eraser":"",disabled:t.isLockedForStudent},on:{click:function(o){return t.selectToolClickHandler("eraser","eraser-cursor")}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"35",height:"31",viewBox:"0 0 35 31"}},[r("use",{attrs:{"xlink:href":e(862)+"#lastic"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v("\n        "+t._s(t.$t("enable_erasing_tool"))+"\n      ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper"},[r("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{id:"toolbar-button-video","data-toolbar-add-video":"",disabled:t.isLockedForStudent},on:{click:t.toggleVideoInput}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"39",height:"31",viewBox:"0 0 39 31"}},[r("use",{attrs:{"xlink:href":e(862)+"#play"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v(t._s(t.$t("add_video")))])]),t._v(" "),t.isTeacher?r("li",{staticClass:"toolbar-button-wrapper"},[r("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-toolbar-buzz-student":"",disabled:t.alertDisabled},on:{click:function(o){return o.preventDefault(),t.buzz.apply(null,arguments)}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"35",height:"38",viewBox:"0 0 35 38"}},[r("use",{attrs:{"xlink:href":e(862)+"#ring"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v(t._s(t.$t("buzz_student")))])]):t._e(),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper",on:{mouseleave:function(o){o.stopPropagation(),t.currentHorizontalMenu=null}}},[r("button",{staticClass:"toolbar-button-item toolbar-button-hand cursor-pointer",on:{click:function(o){t.currentHorizontalMenu="toolbar-horizontal-file"}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"29",height:"38",viewBox:"0 0 29 38"}},[r("use",{attrs:{"xlink:href":e(862)+"#library"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v("\n        "+t._s(t.$t("library"))+"\n      ")]),t._v(" "),r("div",{class:["toolbar-buttons-horizontal toolbar-buttons-horizontal-file",{"toolbar-show":"toolbar-horizontal-file"===t.currentHorizontalMenu}]},[r("ul",[r("li",{staticClass:"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-books"},[r("button",{staticClass:"toolbar-button-item toolbar-button-item-horizontal cursor-pointer",attrs:{id:"load-files-library","data-toolbar-library":""},on:{click:t.openLibrary}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"38",height:"38",viewBox:"0 0 38 38"}},[r("use",{attrs:{"xlink:href":e(862)+"#books"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("select_from_library"))+"\n            ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-laptop"},[r("button",{staticClass:"toolbar-button-item toolbar-button-item-horizontal cursor-pointer",attrs:{"data-toolbar-computer":""}},[r("label",{staticClass:"popup-load-files-label-upload popup-load-files-label-upload-laptop"},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"41",height:"34",viewBox:"0 0 41 34"}},[r("use",{attrs:{"xlink:href":e(862)+"#laptop"}})]),t._v(" "),r("input",{staticClass:"popup-load-files-btn-upload",attrs:{id:"upload-library-files-laptop",type:"file",multiple:"",accept:t.acceptedFilesStr},on:{change:t.uploadFromComputer}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("upload_from_computer"))+"\n            ")])])])])]),t._v(" "),t.isTeacher?r("li",{staticClass:"toolbar-button-wrapper"},[r("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-toolbar-lock":""},on:{click:function(o){return o.preventDefault(),t.toggleStudentRoomStatus.apply(null,arguments)}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"38",height:"50",viewBox:"0 0 38 50"}},[r("use",{attrs:{"xlink:href":e(862)+"#"+(t.isLocked?"lock":"unlock")}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t.isLocked?[t._v("\n          "+t._s(t.$t("enable_moving_resizing_drawing_for_student"))+"\n        ")]:[t._v("\n          "+t._s(t.$t("disable_moving_resizing_drawing_for_student"))+"\n        ")]],2)]):t._e(),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper toolbar-button-wrapper-reset"},[r("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-toolbar-reset":"",disabled:t.isLockedForStudent},on:{click:t.reset}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"36",height:"36",viewBox:"0 0 37 37"}},[r("use",{attrs:{"xlink:href":e(862)+"#restore"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v("\n        "+t._s(t.$t("restore_whiteboard_video_to_original_positions"))+"\n      ")])]),t._v(" "),r("li",{staticClass:"toolbar-button-wrapper toolbar-button-wrapper-exit"},[r("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-toolbar-exit":""},on:{click:t.exitLesson}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"36",height:"36",viewBox:"0 0 37 37"}},[r("use",{attrs:{"xlink:href":e(862)+"#exit"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v(t._s(t.$t("exit_class")))])]),t._v(" "),t.isTeacher?r("li",{staticClass:"toolbar-button-wrapper",on:{mouseleave:function(o){o.stopPropagation(),t.currentHorizontalMenu=null}}},[r("button",{staticClass:"toolbar-button-item toolbar-button-hand cursor-pointer",attrs:{disabled:t.isLessonFinished||!t.isFinishedAllowed},on:{click:function(o){t.currentHorizontalMenu="toolbar-horizontal-finish"}}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"43",height:"38",viewBox:"0 0 43 38"}},[r("use",{attrs:{"xlink:href":e(862)+"#tick"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t._v("\n        "+t._s(t.$t(t.isLessonFinished?"class_finished":"finish_class"))+"\n      ")]),t._v(" "),r("div",{class:["toolbar-buttons-horizontal toolbar-buttons-horizontal-file",{"toolbar-show":"toolbar-horizontal-finish"===t.currentHorizontalMenu}]},[r("ul",[r("li",{staticClass:"toolbar-button-wrapper-horizontal toolbar-button-wrapper-finish"},[r("button",{staticClass:"toolbar-button-item toolbar-button-item-horizontal cursor-pointer",attrs:{"data-toolbar-finish":"",type:"submit"},on:{click:t.finishLesson}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"43",height:"38",viewBox:"0 0 43 38"}},[r("use",{attrs:{"xlink:href":e(862)+"#tick"}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info hover-horizontal-button"},[t._v("\n              "+t._s(t.$t("finish_class"))+"\n            ")])])])])]):t._e(),t._v(" "),t.isStudent?r("li",{staticClass:"toolbar-button-wrapper"},[r("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{disabled:"disabled"}},[r("svg",{staticClass:"toolbar-button-icon",attrs:{width:"38",height:"50",viewBox:"0 0 38 50"}},[r("use",{attrs:{"xlink:href":e(862)+"#"+(t.isLocked?"lock":"unlock")}})])]),t._v(" "),r("div",{staticClass:"hover-btn-info"},[t.isLocked?[t._v("\n          "+t._s(t.$t("moving_resizing_drawing_are_disabled"))+"\n        ")]:[t._v("\n          "+t._s(t.$t("classroom_controls_are_unlocked"))+"\n        ")]],2)]):t._e()])])}),[],!1,null,"53b5e223",null);o.default=component.exports}}]);