(window.webpackJsonp=window.webpackJsonp||[]).push([[58],{1434:function(t,e,r){var content=r(1491);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(19).default)("a98bb618",content,!0,{sourceMap:!1})},1490:function(t,e,r){"use strict";r(1434)},1491:function(t,e,r){var o=r(18)(!1);o.push([t.i,'.text-editor{position:relative}.text-editor-buttons{position:absolute;right:18px;top:8px;z-index:2}.text-editor-buttons button{display:inline-flex;justify-content:center;align-items:center;width:24px;height:24px;margin-left:8px;border-radius:2px;border:1px solid transparent}.text-editor-buttons button.is-active{background-color:var(--v-greyBg-base);border-color:var(--v-greyLight-base)}.text-editor .ProseMirror{min-height:280px;margin-bottom:4px;padding:40px 12px 12px;border:1px solid #bebebe;font-size:13px;border-radius:16px;line-height:1.23}.text-editor .ProseMirror>*{position:relative}.text-editor .ProseMirror p{margin-bottom:0}.text-editor .ProseMirror ul{padding-left:28px}.text-editor .ProseMirror ul>li p{margin-bottom:0}.text-editor .ProseMirror strong{font-weight:700!important}.text-editor .ProseMirror.focus-visible,.text-editor .ProseMirror:focus,.text-editor .ProseMirror:focus-visible{outline:none!important}.text-editor .ProseMirror-focused:before{content:"";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:16px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}.text-editor .v-text-field__details{padding:0 14px}',""]),t.exports=o},1503:function(t,e,r){"use strict";r.r(e);r(31),r(96);var o=r(1586),n=r(1587),d=r(1577),l=r(1589),c={name:"Editor",components:{EditorContent:o.b},props:{value:{type:String,required:!0},counter:{type:Boolean,default:!1},autoLink:{type:Boolean,default:!1},limit:{type:Number,default:null}},data:function(){return{editor:null,text:"",isValid:!0,editorEl:null,keysPressed:{},isDirty:!1}},watch:{value:function(t){this.editor.getHTML()===t||this.editor.commands.setContent(t,!1)}},mounted:function(){var t=this;this.editor=new o.a({content:this.value,extensions:[n.a,d.a.configure({limit:this.limit}),l.a.configure({autolink:!0})]}),this.editor.on("create",(function(e){var r=e.editor;t.text=r.getText(),t.$nextTick((function(){t.editorEl=document.getElementsByClassName("ProseMirror")[0],t.editorEl&&(t.editorEl.addEventListener("keydown",t.keydownHandler),t.editorEl.addEventListener("keyup",t.keyupHandler))})),t.validation()})),this.editor.on("update",(function(e){var r=e.editor;t.isDirty=!0,t.text=r.getText(),t.validation(),t.$emit("update",t.text?r.getHTML():"")}))},beforeDestroy:function(){this.editorEl&&(this.editorEl.removeEventListener("keydown",this.keydownHandler),this.editorEl.removeEventListener("keyup",this.keyupHandler)),this.editor.destroy()},methods:{keydownHandler:function(t){this.keysPressed[t.keyCode]=!0,(t.ctrlKey||this.keysPressed[17]||this.keysPressed[91]||this.keysPressed[93]||this.keysPressed[224])&&this.keysPressed[13]?(t.preventDefault(),this.$emit("submit"),this.keysPressed={}):13!==t.keyCode||t.shiftKey||(t.preventDefault(),this.editor.commands.enter())},keyupHandler:function(t){delete this.keysPressed[t.keyCode]},validation:function(){var t=this.text.trim().length;this.isValid=!!t,t&&this.limit&&(this.isValid=t<=this.limit),this.$emit("validation",this.isValid)}}},f=(r(1490),r(22)),component=Object(f.a)(c,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"text-editor"},[t.editor?o("div",{staticClass:"text-editor-buttons"},[o("button",{class:{"is-active":t.editor.isActive("bold")},on:{click:function(e){e.stopPropagation(),e.preventDefault(),t.editor.chain().focus().toggleBold().run()}}},[o("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16"}},[o("use",{attrs:{"xlink:href":r(91)+"#editor-bold-icon"}})])]),t._v(" "),o("button",{class:{"is-active":t.editor.isActive("bulletList")},on:{click:function(e){e.stopPropagation(),e.preventDefault(),t.editor.chain().focus().toggleBulletList().run()}}},[o("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16"}},[o("use",{attrs:{"xlink:href":r(91)+"#editor-list-icon"}})])])]):t._e(),t._v(" "),o("editor-content",{attrs:{editor:t.editor}}),t._v(" "),t.counter?o("div",{staticClass:"v-text-field__details"},[t._m(0),t._v(" "),o("div",{class:["v-counter theme--light",{"error--text":!t.isValid&&t.isDirty}]},[t._v("\n      "+t._s(t.text.length)+" / "+t._s(t.limit)+"\n    ")])]):t._e()],1)}),[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"v-messages theme--light"},[e("div",{staticClass:"v-messages__wrapper"})])}],!1,null,null,null);e.default=component.exports}}]);