exports.ids = [91];
exports.modules = {

/***/ 940:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(981);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("13082346", content, true, context)
};

/***/ }),

/***/ 947:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/TimePickerItem.vue?vue&type=template&id=7467ec82&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[
    'time-picker-item',
    { active: _vm.isActive },
    { selected: _vm.item.isSelected },
    { free: _vm.item.isAvailable },
    { unavailable: _vm.item.isUnavailable } ],attrs:{"id":_vm.elId},on:{"mouseover":_vm.mouseoverHandler,"mouseleave":_vm.mouseleaveHandler,"click":function($event){$event.stopPropagation();return _vm.clickHandler.apply(null, arguments)}}},[])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/TimePickerItem.vue?vue&type=template&id=7467ec82&scoped=true&

// EXTERNAL MODULE: ./helpers/check_device.js
var check_device = __webpack_require__(145);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/TimePickerItem.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var TimePickerItemvue_type_script_lang_js_ = ({
  name: 'TimePickerItem',
  props: {
    idDefined: {
      type: Boolean,
      default: false
    },
    item: {
      type: Object,
      required: true
    },
    allowedToSelect: {
      type: Boolean,
      required: true
    },
    activeItems: {
      type: Array,
      required: true
    }
  },

  data() {
    return {
      isTouchDevice: Object(check_device["d" /* isTouchDevice */])()
    };
  },

  computed: {
    timezone() {
      return this.$store.getters['user/timeZone'];
    },

    elId() {
      return this.idDefined ? `h-${this.$dayjs(this.item.date).add(this.$dayjs(this.item.date).tz(this.timezone).utcOffset(), 'minute').format('HH-mm')}` : null;
    },

    isActive() {
      return this.item.isAvailable && this.activeItems.includes(this.item);
    }

  },
  methods: {
    clickHandler() {
      if (this.item.isAvailable) {
        this.$emit('click-item', this.item);
      }
    },

    mouseoverHandler() {
      if (!this.isTouchDevice && this.item.isAvailable && !this.item.isSelected && this.allowedToSelect) {
        this.$emit('mouseover-item', this.item);
      }
    },

    mouseleaveHandler() {
      if (this.item.isAvailable && !this.item.isSelected && this.allowedToSelect) {
        this.$emit('mouseleave-item');
      }
    }

  }
});
// CONCATENATED MODULE: ./components/TimePickerItem.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_TimePickerItemvue_type_script_lang_js_ = (TimePickerItemvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/TimePickerItem.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(980)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_TimePickerItemvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "7467ec82",
  "d1fa2cf4"
  
)

/* harmony default export */ var TimePickerItem = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 980:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(940);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 981:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".time-picker-item[data-v-7467ec82]{position:relative;height:32px;box-shadow:inset -1px -1px 0 #e0e0e0}.time-picker-item.free[data-v-7467ec82]{background-color:var(--v-success-base);cursor:pointer}.time-picker-item.active[data-v-7467ec82]{background:#fff repeating-linear-gradient(45deg,rgba(251,176,59,.6),rgba(251,176,59,.6) 7px,var(--v-orange-base) 0,var(--v-orange-base) 20px)}.time-picker-item.selected[data-v-7467ec82]{background-color:var(--v-orange-base)}.time-picker-item.unavailable[data-v-7467ec82]{background-color:#636363}.time-picker-item.first-half[data-v-7467ec82]:after{content:\"\";position:absolute;left:0;bottom:0;width:100%;height:1px;box-shadow:inset 0 -1px 0 #f7f7f7}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

};;
//# sourceMappingURL=time-picker-item.js.map