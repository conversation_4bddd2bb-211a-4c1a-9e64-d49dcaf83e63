# Edit this file to introduce tasks to be run by cron.
#
# Each task to run has to be defined through a single line
# indicating with different fields when the task will be run
# and what command to run for the task
#
# To define the time you can provide concrete values for
# minute (m), hour (h), day of month (dom), month (mon),
# and day of week (dow) or use '*' in these fields (for 'any').#
# Notice that tasks will be started based on the cron's system
# daemon's notion of time and timezones.
#
# Output of the crontab jobs (including errors) is sent through
# email to the user the crontab file belongs to (unless redirected).
#
# For example, you can run a backup of all your user accounts
# at 5 a.m every week with:
# 0 5 * * 1 tar -zcf /var/backups/home.tgz /home/
#
# For more information see the manual pages of crontab(5) and cron(8)
#
# m h  dom mon dow   command


#*/1 * * * * echo "kamiloooooooooooooooooooooooooooooooooooooooooooooooooooooooooooos"
#* * * * * php /var/www/l2branches/l2-langu-prod/current/app/console availability:slots:release
#*/5 * * * * php /var/www/l2branches/l2-langu-prod/current/app/console teachers:order-cache --env=prod
#*/5 * * * * php /var/www/l2branches/l2-langu-prod/current/app/console message:notifications:send --env=prod
#*/5 * * * * php /var/www/l2branches/l2-langu-prod/current/app/console lesson:upcoming:reminder --env=prod
#0 * * * * php /var/www/l2branches/l2-langu-prod/current/app/console fixer:fetch --env=prod
#*/10 * * * * php /var/www/l2branches/l2-langu-prod/current/app/console payments:process --env=prod

#* * * * * php /var/www/l2branches/horse.langu.io/current/app/console availability:slots:release
#*/1 * * * * php /var/www/l2branches/horse.langu.io/current/app/console teachers:order-cache --env=dev
#0 * * * * php /var/www/l2branches/horse.langu.io/current/app/console fixer:fetch --env=dev
* * * * * root echo "Hello world" >> /var/log/cron.log 2>&1
# Don't remove the empty line at the end of this file. It is required to run the cron job
