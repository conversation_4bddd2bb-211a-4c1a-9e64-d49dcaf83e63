{"version": 3, "file": "components/lesson-time-notice.js", "sources": ["webpack:///./components/LessonTimeNotice.vue?284e", "webpack:///./components/LessonTimeNotice.vue?743a", "webpack:///./components/LessonTimeNotice.vue?a1f5", "webpack:///./components/LessonTimeNotice.vue?5e30", "webpack:///./components/LessonTimeNotice.vue", "webpack:///./components/LessonTimeNotice.vue?6109", "webpack:///./components/LessonTimeNotice.vue?0a5b"], "sourcesContent": ["export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonTimeNotice.vue?vue&type=style&index=0&id=372f019a&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".time-notice[data-v-372f019a]{padding-bottom:1px}.time-notice span[data-v-372f019a]{display:inline-block;cursor:pointer;transition:color .3s}.time-notice span.text--gradient[data-v-372f019a]{position:relative}.time-notice span.text--gradient[data-v-372f019a]:after{content:\\\"\\\";position:absolute;width:100%;height:1px;left:0;bottom:-1px;background:linear-gradient(75deg,var(--v-success-base),var(--v-primary-base))}.time-notice--dark span[data-v-372f019a]{color:#fff}.time-notice--dark span[data-v-372f019a]:hover{color:var(--v-success-base)}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonTimeNotice.vue?vue&type=style&index=0&id=372f019a&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"12bcaf99\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.currentTime)?_c('div',{class:['time-notice', { 'time-notice--dark': _vm.dark }]},[_vm._ssrNode(_vm._ssrEscape(\"\\n  \"+_vm._s(_vm.$t('lesson_times_displayed_based_on_your_current_local_time'))+\":\\n  \"+_vm._s(_vm.currentTime.format('LT'))+\" (\"+_vm._s(_vm.currentTime.format('z'))+\").\\n  \")+((!_vm.isUserLogged)?(((!_vm.oneLine)?(\"<br data-v-372f019a>\"):\"<!---->\")+\" <span\"+(_vm._ssrClass(null,{ 'text--gradient': !_vm.dark }))+\" data-v-372f019a>\"+_vm._ssrEscape(\"\\n      \"+_vm._s(_vm.$t('log_in'))+\"\\n    \")+\"</span>\"+_vm._ssrEscape(\"\\n    \"+_vm._s(_vm.$t('to_change_your_time_zone'))+\".\\n  \")):\"<!---->\"))]):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'LessonTimeNotice',\n  props: {\n    dark: {\n      type: Boolean,\n      default: false,\n    },\n    oneLine: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  data() {\n    return {\n      currentTime: null,\n      intervalId: null,\n    }\n  },\n  computed: {\n    isUserLogged() {\n      return this.$store.getters['user/isUserLogged']\n    },\n    timezone() {\n      return this.$store.getters['user/timeZone']\n    },\n  },\n  created() {\n    this.setCurrentTime()\n\n    this.intervalId = setInterval(() => {\n      this.setCurrentTime()\n    }, 10000)\n  },\n  beforeDestroy() {\n    window.clearInterval(this.intervalId)\n  },\n  methods: {\n    setCurrentTime() {\n      this.currentTime = this.$dayjs().tz(this.timezone)\n    },\n    showLoginSidebarClickHandler() {\n      this.$emit('show-login-sidebar')\n      this.$store.commit('SET_IS_LOGIN_SIDEBAR', true)\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonTimeNotice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonTimeNotice.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./LessonTimeNotice.vue?vue&type=template&id=372f019a&scoped=true&\"\nimport script from \"./LessonTimeNotice.vue?vue&type=script&lang=js&\"\nexport * from \"./LessonTimeNotice.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./LessonTimeNotice.vue?vue&type=style&index=0&id=372f019a&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"372f019a\",\n  \"d445dc16\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AACA;AASA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;AACA;AAOA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AARA;AApCA;;ACrBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}