(window.webpackJsonp=window.webpackJsonp||[]).push([[93],{1635:function(e,t,r){var content=r(1736);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(19).default)("9f254164",content,!0,{sourceMap:!1})},1735:function(e,t,r){"use strict";r(1635)},1736:function(e,t,r){var n=r(18)(!1);n.push([e.i,".wise-transfer-modal[data-v-0964c1f5]  .v-card{box-shadow:none!important}.wise-transfer-modal .v-text-field .v-input .v-input__control .v-text-field--outlined[data-v-0964c1f5]{border-radius:16px!important}.wise-transfer-modal .input-label[data-v-0964c1f5]{font-size:14px;font-weight:400;color:rgba(0,0,0,.87)}.wise-transfer-modal .currency-info[data-v-0964c1f5],.wise-transfer-modal .wise-transfer-info[data-v-0964c1f5]{color:rgba(0,0,0,.6);font-size:14px;line-height:1.4}@media(max-width:768px){.wise-transfer-modal .v-card[data-v-0964c1f5]{padding:16px!important}.wise-transfer-modal .v-row[data-v-0964c1f5]{margin:0}.wise-transfer-modal .v-col[data-v-0964c1f5]{padding:0;margin-bottom:2px}.wise-transfer-modal .v-col[data-v-0964c1f5]:last-child{margin-bottom:0}.wise-transfer-modal .text-right[data-v-0964c1f5]{display:flex;justify-content:flex-end}.wise-transfer-modal .text-right .v-btn[data-v-0964c1f5]{width:-webkit-max-content!important;width:-moz-max-content!important;width:max-content!important}.wise-transfer-modal .currency-info[data-v-0964c1f5],.wise-transfer-modal .wise-transfer-info[data-v-0964c1f5]{margin-bottom:2px}.wise-transfer-modal .wise-modal[data-v-0964c1f5]{flex-direction:column;margin-bottom:2px;width:100%}}",""]),e.exports=n},1921:function(e,t,r){"use strict";r.r(t);var n=r(10),o=(r(62),r(149)),l=r(370),c={name:"WiseTransferModal",components:{LDialog:o.default,TextInput:l.default},props:{show:{type:Boolean,default:!1}},data:function(){return{loading:!1,form:{email:"",fullName:"",currency:""},rules:{required:function(e){return!!e||"This field is required"},email:function(e){return/.+@.+\..+/.test(e)||"Please enter a valid email"},currencyCode:function(e){return!e||/^[A-Z]{3}$/.test(e)||"Please enter a valid 3-letter currency code"}}}},watch:{show:function(e){e&&this.resetForm()}},methods:{updateValue:function(e,t){this.form[t]=e},resetForm:function(){this.form={email:"",fullName:"",currency:""},this.$refs.form&&this.$refs.form.resetValidation()},handleSubmit:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.loading||!e.$refs.form.validate()){t.next=14;break}return e.loading=!0,t.prev=2,t.next=5,e.$store.dispatch("payments/requestWiseTransfer",e.form);case 5:t.sent.success?(e.$store.dispatch("snackbar/success",{successMessage:"Form submitted successfully"},{root:!0}),e.$emit("submit",e.form),e.$emit("close")):e.$store.dispatch("snackbar/error",{errorMessage:"Something went wrong"},{root:!0}),t.next=11;break;case 9:t.prev=9,t.t0=t.catch(2);case 11:return t.prev=11,e.loading=!1,t.finish(11);case 14:case"end":return t.stop()}}),t,null,[[2,9,11,14]])})))()}}},d=(r(1735),r(22)),f=r(42),m=r.n(f),v=r(1327),w=r(1329),h=r(1360),x=r(1363),y=r(1361),component=Object(d.a)(c,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("l-dialog",{attrs:{dialog:e.show,"max-width":"800","custom-class":"wise-transfer-modal"},on:{"close-dialog":function(t){return e.$emit("close")}}},[r("v-card",{staticClass:"pa-2",attrs:{flat:""}},[r("div",{staticClass:"d-flex justify-space-between align-center mb-2"},[r("h2",{staticClass:"text-h6 font-weight-medium"},[e._v("Wise Transfer")])]),e._v(" "),r("div",{staticClass:"wise-transfer-info mb-2"},[e._v("\n      Wise payouts are processed each Monday. You will receive a link by email\n      from Wise, and you will enter your banking details directly with them.\n      Please enter your email address and your full name below.\n    ")]),e._v(" "),r("v-form",{ref:"form",on:{submit:function(t){return t.preventDefault(),e.handleSubmit.apply(null,arguments)}}},[r("v-row",{staticClass:"wise-modal",attrs:{"no-gutters":""}},[r("v-col",{staticClass:"pr-2",attrs:{cols:"6"}},[r("div",{staticClass:"input-label mb-1"},[e._v("Email address:")]),e._v(" "),r("text-input",{attrs:{value:e.form.email,"type-class":"border-gradient",height:"44",rules:[e.rules.required,e.rules.email]},on:{input:function(t){return e.updateValue(t,"email")}}})],1),e._v(" "),r("v-col",{staticClass:"pl-2",attrs:{cols:"6"}},[r("div",{staticClass:"input-label mb-1"},[e._v("Full name:")]),e._v(" "),r("text-input",{attrs:{value:e.form.fullName,"type-class":"border-gradient",height:"44",rules:[e.rules.required]},on:{input:function(t){return e.updateValue(t,"fullName")}}})],1)],1),e._v(" "),r("div",{staticClass:"currency-info mb-2"},[e._v("\n        In what currency would you like to receive the transfer? Please enter\n        the 3-letter currency code (i.e. AUD, ZAR, PEN, etc.).\n      ")]),e._v(" "),r("v-row",{attrs:{"no-gutters":""}},[r("v-col",{staticClass:"wise-modal",attrs:{cols:"6"}},[r("div",{staticClass:"input-label mb-1"},[e._v("Currency:")]),e._v(" "),r("text-input",{attrs:{value:e.form.currency,"type-class":"border-gradient",height:"44",rules:[e.rules.required,e.rules.currencyCode]},on:{input:function(t){return e.updateValue(t,"currency")}}})],1)],1),e._v(" "),r("div",{staticClass:"text-right"},[r("v-btn",{staticClass:"px-12",attrs:{color:"primary",large:"",type:"submit",loading:e.loading}},[e._v("\n          Confirm payout\n        ")])],1)],1)],1)],1)}),[],!1,null,"0964c1f5",null);t.default=component.exports;m()(component,{LDialog:r(149).default}),m()(component,{VBtn:v.a,VCard:w.a,VCol:h.a,VForm:x.a,VRow:y.a})}}]);