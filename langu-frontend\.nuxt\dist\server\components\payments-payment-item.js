exports.ids = [68];
exports.modules = {

/***/ 1011:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1084);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("fd0dd7ee", content, true, context)
};

/***/ }),

/***/ 1083:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentItem_vue_vue_type_style_index_0_id_995c1e74_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1011);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentItem_vue_vue_type_style_index_0_id_995c1e74_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentItem_vue_vue_type_style_index_0_id_995c1e74_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentItem_vue_vue_type_style_index_0_id_995c1e74_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PaymentItem_vue_vue_type_style_index_0_id_995c1e74_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1084:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".payment-item[data-v-995c1e74]{display:flex;background:#fff;border-radius:14px;margin-bottom:12px;overflow:hidden;box-shadow:0 4px 12px rgba(0,0,0,.1)}.payment-item[data-v-995c1e74]:hover{box-shadow:0 4px 14px rgba(217,225,236,.47)}.payment-item-date[data-v-995c1e74]{min-width:100px;padding:11px;display:flex;flex-direction:column;align-items:center;width:142px;border-radius:16px;justify-content:center;background:linear-gradient(126.15deg,rgba(128,182,34,.74),rgba(60,135,248,.74) 102.93%);color:#fff;box-shadow:4px 0 8px rgba(0,0,0,.1);position:relative;z-index:1}.payment-item-date .weekday[data-v-995c1e74]{font-size:13px;font-weight:700;line-height:1;text-transform:capitalize;text-align:center}.payment-item-date .date[data-v-995c1e74]{font-size:24px;font-weight:700;line-height:1.2;margin-bottom:2px}.payment-item-date .time[data-v-995c1e74]{font-size:13px;line-height:1;font-weight:700;margin-bottom:18px;text-align:center}.payment-item-date .duration-icon[data-v-995c1e74]{color:var(--v-dark-lighten3)}.payment-item-date .duration[data-v-995c1e74]{display:flex;align-items:center;font-size:16px}.payment-item-date .duration span[data-v-995c1e74]{color:#e8f1f7}.payment-item-date .duration-icon[data-v-995c1e74]{margin-right:4px;display:flex;align-items:center}.payment-item-content[data-v-995c1e74]{flex:1;padding:16px 24px}.payment-item-content .payment-info .student-name[data-v-995c1e74]{font-size:24px;font-weight:500;color:#333;margin-bottom:12px}.payment-item-content .payment-info .details[data-v-995c1e74]{display:flex;align-items:center;grid-gap:24px;gap:24px;font-size:14px}.payment-item-content .payment-info .details .detail-group[data-v-995c1e74]{align-items:center;grid-gap:6px;gap:6px}.payment-item-content .payment-info .details .detail-group p[data-v-995c1e74]{margin:0}.payment-item-content .payment-info .details .detail-group .label[data-v-995c1e74]{color:#666;font-size:14px}.payment-item-content .payment-info .details .detail-group .value[data-v-995c1e74]{color:#333}.payment-item-content .payment-info .details .detail-group .value.gradient-text[data-v-995c1e74]{background:linear-gradient(126.15deg,#80b622,#3c87f8 102.93%);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;font-weight:500;font-size:16px;line-height:18px}.payment-item-content .payment-info .details .detail-group .pdf-download-link[data-v-995c1e74]{cursor:pointer}.payment-item-content .payment-info .details .detail-group .pdf-download-link[data-v-995c1e74]:hover{text-decoration:underline}.d-none[data-v-995c1e74]{display:none}@media screen and (min-width:768px){.d-sm-none[data-v-995c1e74]{display:none}}@media screen and (min-width:768px){.d-sm-block[data-v-995c1e74]{display:block}}@media screen and (max-width:768px){.payment-item[data-v-995c1e74]{flex-direction:column;margin-bottom:16px;box-shadow:none;background:transparent}.payment-item[data-v-995c1e74],.payment-item-date[data-v-995c1e74]{box-shadow:4px 0 8px rgba(0,0,0,.1)}.payment-item-date[data-v-995c1e74]{width:auto;min-height:auto;padding:8px 16px;flex-direction:row;justify-content:flex-start;border-radius:24px;margin-bottom:8px}.payment-item-date .date[data-v-995c1e74]{margin-right:8px;margin-bottom:0}.payment-item-date .time[data-v-995c1e74]{margin-left:0;opacity:1;margin-bottom:0}.payment-item-content[data-v-995c1e74]{background:#fff;border-radius:12px;padding:16px;box-shadow:0 4px 12px rgba(0,0,0,.1)}.payment-item-content .payment-info .student-name[data-v-995c1e74]{font-size:20px;margin-bottom:4px;padding-bottom:12px;border-bottom:1px solid rgba(0,0,0,.1)}.payment-item-content .payment-info .details[data-v-995c1e74]{flex-direction:column;grid-gap:8px;gap:8px}.payment-item-content .payment-info .details .detail-group[data-v-995c1e74]{display:flex;justify-content:space-between;width:100%}.payment-item-content .payment-info .details .detail-group .value[data-v-995c1e74]{font-size:16px;font-weight:500}.payment-item-content .payment-info .details .detail-group[data-v-995c1e74]:first-child{margin-bottom:4px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1102:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/payments/PaymentItem.vue?vue&type=template&id=995c1e74&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"payment-item"},[_vm._ssrNode("<div class=\"payment-item-date\" data-v-995c1e74><div data-v-995c1e74><div class=\"weekday d-none d-sm-block\" data-v-995c1e74>"+_vm._ssrEscape("\n        "+_vm._s(_vm.formatWeekday(_vm.item.date))+"\n      ")+"</div> <div class=\"date d-none d-sm-block\" data-v-995c1e74>"+_vm._ssrEscape("\n        "+_vm._s(_vm.formatDate(_vm.item.date))+"\n      ")+"</div> <div class=\"time d-none d-sm-block\" data-v-995c1e74>"+_vm._ssrEscape(_vm._s(_vm.formatTime(_vm.item.time)))+"</div> <div class=\"d-sm-none\" data-v-995c1e74>"+_vm._ssrEscape("\n        "+_vm._s(_vm.formatWeekday(_vm.item.date))+", "+_vm._s(_vm.formatDate(_vm.item.date))+" -\n        "+_vm._s(_vm.formatTime(_vm.item.time))+"\n      ")+"</div></div> <div class=\"duration d-none d-sm-block\" data-v-995c1e74><div class=\"duration-icon\" data-v-995c1e74><svg width=\"18\" height=\"18\" viewBox=\"0 0 18 18\" data-v-995c1e74><use"+(_vm._ssrAttr("xlink:href",__webpack_require__(14) + "#clock-thin"))+" data-v-995c1e74></use></svg> <span class=\"ml-1\" data-v-995c1e74>"+_vm._ssrEscape(_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.lessonLength })))+"</span></div></div> <div class=\"duration d-sm-none\" data-v-995c1e74>"+_vm._ssrEscape("\n       ("+_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.lessonLength }))+")\n    ")+"</div></div> <div class=\"payment-item-content\" data-v-995c1e74><div class=\"payment-info\" data-v-995c1e74><div class=\"student-name\" data-v-995c1e74>"+_vm._ssrEscape(_vm._s(_vm.item.student))+"</div> <div class=\"details\" data-v-995c1e74><div class=\"detail-group\" data-v-995c1e74><p class=\"label\" data-v-995c1e74>Lesson:</p> <p class=\"value gradient-text\" data-v-995c1e74>"+_vm._ssrEscape(_vm._s(_vm.item.lessonType))+"</p></div> <div class=\"detail-group\" data-v-995c1e74><p class=\"label\" data-v-995c1e74>Finished:</p> <p class=\"value gradient-text\" data-v-995c1e74>"+_vm._ssrEscape("\n            "+_vm._s(_vm.formatFinishedAt(_vm.item.finishedAt))+"\n          ")+"</p></div> <div class=\"detail-group\" data-v-995c1e74><p class=\"label\" data-v-995c1e74>Invoice no.</p> <p class=\"value gradient-text\" data-v-995c1e74>"+_vm._ssrEscape(_vm._s(_vm.item.invoiceNo))+"</p></div> <div class=\"detail-group\" data-v-995c1e74><p class=\"label\" data-v-995c1e74>Lesson no.</p> <p class=\"value gradient-text\" data-v-995c1e74>"+_vm._ssrEscape(_vm._s(_vm.item.lessonNo))+"</p></div> <div class=\"detail-group\" data-v-995c1e74><p class=\"label\" data-v-995c1e74>Value</p> <p class=\"value gradient-text\" data-v-995c1e74>"+_vm._ssrEscape("\n            "+_vm._s(_vm.formatCurrencyValue(_vm.item.value))+"\n          ")+"</p></div> "+((_vm.item.transactionId && _vm.item.invoiceNumber)?("<div class=\"detail-group\" data-v-995c1e74><p class=\"label\" data-v-995c1e74>PDF</p> <p class=\"value gradient-text\" data-v-995c1e74><a href=\"#\" class=\"pdf-download-link\" data-v-995c1e74>\n              Download\n            </a></p></div>"):"<!---->")+"</div></div></div>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/payments/PaymentItem.vue?vue&type=template&id=995c1e74&scoped=true&

// EXTERNAL MODULE: ./helpers/index.js
var helpers = __webpack_require__(501);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/payments/PaymentItem.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

const DEFAULT_PAYMENT_ITEM = {
  date: '2023-11-18',
  time: '9:00 AM',
  student: 'Kathrin Donaldson',
  lessonType: 'Trial',
  status: 'Finished',
  completedAt: '18 Nov, 10:02 AM',
  invoiceNo: '8395',
  lessonNo: '295032',
  value: '12.50',
  lessonLength: 30 // Default lesson length in minutes

};
/* harmony default export */ var PaymentItemvue_type_script_lang_js_ = ({
  name: 'PaymentItem',
  props: {
    item: {
      type: Object,
      required: true,
      default: () => ({ ...DEFAULT_PAYMENT_ITEM
      }),

      validator(value) {
        return ['date', 'time', 'student', 'lessonType', 'status', 'completedAt', 'invoiceNo', 'lessonNo', 'value'].every(key => key in value);
      }

    }
  },
  computed: {
    lessonLength() {
      // If lessonLength is available in the item, use it, otherwise default to 30 minutes
      return this.item.lessonLength || 30;
    },

    userLocale() {
      var _this$$store$state$us;

      // Get user's UI language/locale, fallback to browser locale or 'en'
      return this.$store.getters['user/isUserLogged'] ? ((_this$$store$state$us = this.$store.state.user.item) === null || _this$$store$state$us === void 0 ? void 0 : _this$$store$state$us.uiLanguage) || this.$i18n.locale : this.$i18n.locale || 'en';
    },

    timeZone() {
      // Get user's timezone, fallback to browser timezone
      return this.$store.getters['user/timeZone'];
    },

    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    },

    currentCurrency() {
      // Get current currency info from store
      return this.$store.state.currency.item;
    }

  },
  methods: {
    formatDate(date) {
      try {
        return this.$dayjs(date).tz(this.timeZone).format('DD MMM');
      } catch (e) {
        // Fallback to default formatting if there's an error
        return date;
      }
    },

    formatWeekday(date) {
      // Format weekday using user's locale and timezone
      try {
        // Use dayjs with timezone support and locale formatting
        return this.$dayjs(date).tz(this.timeZone).format('dddd');
      } catch (e) {
        // Fallback using Intl.DateTimeFormat with user's locale
        return new Intl.DateTimeFormat(this.userLocale, {
          weekday: 'long'
        }).format(new Date(date));
      }
    },

    formatTime(time) {
      // Format time using user's locale and timezone
      try {
        // If time is already in a good format, we can try to parse it with the date
        // and format it according to user's locale
        if (time && this.item.date) {
          // Combine date and time for proper timezone conversion
          const dateTimeString = `${this.item.date} ${time}`;
          const dateTime = this.$dayjs(dateTimeString).tz(this.timeZone); // Format time using locale-aware format (LT = localized time)

          return dateTime.format('LT');
        } // Fallback: return the original time if we can't parse it


        return time;
      } catch (e) {
        // Fallback to original time if there's an error
        return time;
      }
    },

    formatFinishedAt(finishedAt) {
      // Format finished date/time using user's locale and timezone
      try {
        if (!finishedAt) return '-'; // Use dayjs with timezone support and locale formatting
        // Format as "DD MMM, LT" (e.g., "18 Nov, 10:02 AM")

        return this.$dayjs(finishedAt).tz(this.timeZone).format('DD MMM, LT');
      } catch (e) {
        // Fallback to original value if there's an error
        return finishedAt || '-';
      }
    },

    formatValue(value) {
      // Format the value with exactly 2 decimal places
      return Number(value).toFixed(2);
    },

    formatCurrencyValue(value) {
      var _this$currentCurrency;

      // Format currency value according to user's locale
      const currencyCode = ((_this$currentCurrency = this.currentCurrency) === null || _this$currentCurrency === void 0 ? void 0 : _this$currentCurrency.isoCode) || 'EUR';
      return Object(helpers["formatCurrencyLocale"])(value, currencyCode, this.userLocale, true);
    },

    openPdf() {
      try {
        this.$store.dispatch('payments/openInvoicePdf', {
          transactionId: this.item.transactionId,
          invoiceNumber: this.item.invoiceNumber
        });
      } catch (error) {
        // Handle error - show user-friendly message
        if (this.$store.dispatch) {
          this.$store.dispatch('snackbar/error', {
            errorMessage: 'Failed to open invoice PDF. Please try again.'
          });
        }
      }
    }

  }
});
// CONCATENATED MODULE: ./components/payments/PaymentItem.vue?vue&type=script&lang=js&
 /* harmony default export */ var payments_PaymentItemvue_type_script_lang_js_ = (PaymentItemvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/payments/PaymentItem.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1083)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  payments_PaymentItemvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "995c1e74",
  "067c283c"
  
)

/* harmony default export */ var PaymentItem = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=payments-payment-item.js.map