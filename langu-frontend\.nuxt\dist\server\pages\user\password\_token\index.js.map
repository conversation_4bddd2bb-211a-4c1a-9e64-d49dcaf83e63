{"version": 3, "file": "pages/user/password/_token/index.js", "sources": ["webpack:///./pages/user/password/_token/index.vue?2627", "webpack:///./pages/user/password/_token/index.vue", "webpack:///./pages/user/password/_token/index.vue?3224", "webpack:///./pages/user/password/_token/index.vue?c1fc"], "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticStyle:{\"min-height\":\"calc(100vh - 300px)\"}},[])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n\nexport default {\n  name: 'UserPasswordToken',\n  beforeMount() {\n    const token = this.$route.params.token\n    const redirectUrl = this.$route.query.redirectUrl\n\n    // If redirectUrl is present in the query parameters, save it to the store\n    if (redirectUrl) {\n      this.$store.dispatch('user/setRedirectUrl', redirectUrl)\n    }\n\n    this.$store.dispatch('auth/checkPasswordToken', token).finally(() => {\n      this.$router.push('/teacher-listing')\n    })\n  },\n}\n", "import mod from \"-!../../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=6ec54c0a&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"73690256\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA;;ACLA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}