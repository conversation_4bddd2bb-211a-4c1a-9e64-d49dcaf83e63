(window.webpackJsonp=window.webpackJsonp||[]).push([[165],{2206:function(e,t,n){"use strict";n.r(t);var r=n(10),o=(n(62),n(35),n(173),{name:"UserMessages",components:{MessagesPage:n(1918).default},middleware:"authenticated",asyncData:function(e){return Object(r.a)(regeneratorRuntime.mark((function t(){var n,o,c,l;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.store,o=e.query,c=null==o?void 0:o.search,t.next=4,n.dispatch("message/getItems",{page:1,searchQuery:c}).then(function(){var e=Object(r.a)(regeneratorRuntime.mark((function e(data){var t,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(l=null!==(t=null==data?void 0:data.count)&&void 0!==t?t:0,null==data||null===(r=data.threads)||void 0===r||!r.length){e.next=5;break}return n.commit("message/SET_ITEM",data.threads[0]),e.next=5,n.dispatch("message/getConversation",{threadId:data.threads[0].id});case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 4:return t.abrupt("return",{count:l});case 5:case"end":return t.stop()}}),t)})))()},head:function(){return{title:this.$t("user_messages_page.seo_title"),meta:[{hid:"description",name:"description",content:this.$t("user_messages_page.seo_description")},{hid:"og:title",name:"og:title",property:"og:title",content:this.$t("user_messages_page.seo_title")},{property:"og:description",content:this.$t("user_messages_page.seo_description")}],bodyAttrs:{class:"".concat(this.locale," user-messages-page")}}},computed:{locale:function(){return this.$i18n.locale}},watchQuery:!0}),c=n(22),component=Object(c.a)(o,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("messages-page",{attrs:{"total-quantity":e.count}})}),[],!1,null,null,null);t.default=component.exports}}]);