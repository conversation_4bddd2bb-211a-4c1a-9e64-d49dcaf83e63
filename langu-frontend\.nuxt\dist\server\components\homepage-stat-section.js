exports.ids = [46];
exports.modules = {

/***/ 1255:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1343);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("44e291ec", content, true, context)
};

/***/ }),

/***/ 1342:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StatSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1255);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StatSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StatSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StatSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StatSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1343:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(68);
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(141);
var ___CSS_LOADER_URL_IMPORT_1___ = __webpack_require__(142);
var ___CSS_LOADER_URL_IMPORT_2___ = __webpack_require__(143);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
var ___CSS_LOADER_URL_REPLACEMENT_1___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_1___);
var ___CSS_LOADER_URL_REPLACEMENT_2___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_2___);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".stat{padding-top:85px}@media only screen and (max-width:991px){.stat{padding-top:40px}}@media only screen and (max-width:479px){.stat{padding-top:10px}}.stat-item{min-height:138px}@media only screen and (min-width:768px){.stat-item{display:flex;justify-content:center}}@media only screen and (max-width:991px){.stat-item{min-height:172px}}.stat-item-wrap{position:relative;min-height:inherit}@media only screen and (max-width:991px){.stat-item-wrap{max-width:258px;margin-left:auto;margin-right:auto}}.stat-item-wrap:before{content:\"\";position:absolute;top:10px;left:-25px;width:100%;height:100%;background-size:contain;background-repeat:no-repeat}@media only screen and (max-width:991px){.stat-item-wrap:before{left:0;top:-10px}}.stat-item-i1 .stat-item-wrap:before{background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");background-position:0}@media only screen and (max-width:991px){.stat-item-i1 .stat-item-text{max-width:150px}}.stat-item-i2 .stat-item-wrap:before{background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_1___ + ");background-position:0}@media only screen and (max-width:991px){.stat-item-i2 .stat-item-text{max-width:166px}}.stat-item-i3 .stat-item-wrap:before{background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_2___ + ");background-position:0}@media only screen and (max-width:991px){.stat-item-i3 .stat-item-text{max-width:136px}}.stat-item-value{position:relative;margin-bottom:12px;font-size:64px;font-weight:800;line-height:1}@media only screen and (max-width:1439px){.stat-item-value{margin-bottom:0;font-size:56px}}@media only screen and (max-width:991px){.stat-item-value{font-size:54px;text-align:center}}.stat-item-text{position:relative;font-size:18px;color:var(--v-grey-base);line-height:1.5}.stat-item-text span{font-size:16px}@media only screen and (max-width:991px){.stat-item-text{margin:0 auto;font-size:15px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1411:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/StatSection.vue?vue&type=template&id=92e11f48&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"stat"},[_c('v-container',[_c('v-row',[_c('v-col',{staticClass:"col-xl-10 offset-xl-1"},[_c('v-row',_vm._l((_vm.items),function(item,idx){return _c('v-col',{key:idx,class:("col-12 col-sm-4 stat-item stat-item-i" + (idx + 1))},[_c('div',{staticClass:"stat-item-wrap"},[_c('div',{staticClass:"stat-item-value text--gradient"},[_vm._v("\n                "+_vm._s(item.value)+"\n              ")]),_vm._v(" "),_c('div',{staticClass:"stat-item-text",domProps:{"innerHTML":_vm._s(_vm.$t(item.text))}})])])}),1)],1)],1)],1)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/StatSection.vue?vue&type=template&id=92e11f48&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/StatSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var StatSectionvue_type_script_lang_js_ = ({
  name: 'StatSection',

  data() {
    return {
      items: [{
        value: '98%',
        text: 'home_page.stat_1'
      }, {
        value: this.$t('n_15000') + '+',
        text: 'home_page.stat_2'
      }, {
        value: '250+',
        text: 'home_page.stat_3'
      }]
    };
  }

});
// CONCATENATED MODULE: ./components/homepage/StatSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_StatSectionvue_type_script_lang_js_ = (StatSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/StatSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1342)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_StatSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "d1266b4a"
  
)

/* harmony default export */ var StatSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */




installComponents_default()(component, {VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VRow: VRow["a" /* default */]})


/***/ })

};;
//# sourceMappingURL=homepage-stat-section.js.map