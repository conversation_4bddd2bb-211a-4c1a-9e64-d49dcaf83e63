(window.webpackJsonp=window.webpackJsonp||[]).push([[94],{1627:function(t,e,r){var content=r(1727);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,r(19).default)("36fed771",content,!0,{sourceMap:!1})},1726:function(t,e,r){"use strict";r(1627)},1727:function(t,e,r){var o=r(18)(!1);o.push([t.i,".set-password .set-password-title{color:var(--v-dark-base)!important;font-size:20px;font-weight:600}@media only screen and (max-width:639px){.set-password .set-password-title{text-align:center;font-size:18px}}.set-password .set-password-text{color:var(--v-dark-base)!important;font-size:16px}@media only screen and (max-width:639px){.set-password .set-password-text{font-size:15px}}@media only screen and (max-width:479px){.set-password .set-password-text{font-size:14px}}.set-password .set-password-button{text-align:right}@media only screen and (max-width:639px){.set-password .set-password-button{text-align:center}}.set-password .set-password-button .v-btn{border-radius:16px!important}.set-password .set-password-button .v-btn .v-btn__content{font-weight:600!important}",""]),t.exports=o},1826:function(t,e,r){"use strict";r.r(e);var o=r(149),n=r(370),d=r(702),l={name:"SetPasswordDialog",components:{LDialog:o.default,TextInput:n.default},props:{showSetPasswordDialog:{type:Boolean,required:!0}},data:function(){return{password:"",passwordRepeat:"",passwordTextError:""}},computed:{passwordLengthError:function(){return!!this.passwordTextError.length},token:function(){var t;return null===(t=this.$store.state.auth.passwordTokenItem)||void 0===t?void 0:t.token},isValid:function(){var t,e;return!!this.token&&(null!==(t=!(null!==(e=this.$store.state.auth.passwordTokenItem)&&void 0!==e&&e.isExpired))&&void 0!==t&&t)}},watch:{password:function(){this.passwordTextError=""},passwordRepeat:function(){this.passwordTextError=""}},beforeDestroy:function(){this.resetData()},methods:{setPasswordSubmitHandler:function(){var t=this;this.passwordTextError="";this.password===this.passwordRepeat?/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[^\w\s]).{8,}$/.test(this.password)?this.$store.dispatch("auth/setPassword",{token:this.token,password:this.password,confirmPassword:this.passwordRepeat}).then((function(){t.$store.dispatch("user/getUserStatus").then((function(){if(t.resetData(),!Object(d.a)(t.$router,!0)){var e=t.$store.getters["user/redirectUrl"];e?(t.$router.push(e),t.$store.dispatch("user/clearRedirectUrl")):t.$store.getters["user/isStudent"]&&!t.$store.getters["user/registrationConfirmed"]&&t.$router.push({path:"/teacher-listing/welcome"})}}))})).catch((function(e){e.response&&(400===e.response.status&&(t.passwordTextError=t.$t("password_error")),404===e.response.status&&(t.$store.commit("SET_IS_PASSWORD_LINK_EXPIRED",!0),t.$store.commit("auth/SET_PASSWORD_TOKEN_ITEM",null),t.$store.commit("SET_IS_LOGIN_SIDEBAR",!0)))})):this.passwordTextError=this.$t("password_error"):this.passwordTextError=this.$t("passwords_are_different")},resetData:function(){this.password="",this.passwordRepeat="",this.$store.commit("auth/SET_PASSWORD_TOKEN_ITEM",null)}}},w=(r(1726),r(22)),c=r(42),h=r.n(c),m=r(1327),_=r(261),component=Object(w.a)(l,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("l-dialog",{attrs:{dialog:t.showSetPasswordDialog,"hide-close-button":t.isValid,"max-width":"388","custom-class":"set-password"},on:{"close-dialog":t.resetData}},[o("div",[t.isValid?[o("div",{staticClass:"set-password-title"},[t._v("\n        "+t._s(t.$t("thank_you_for_confirming_your_email_address"))+"\n      ")]),t._v(" "),o("div",{staticClass:"set-password-text mt-3 mt-md-5"},[t._v("\n        "+t._s(t.$t("please_create_your_langu_password"))+"\n      ")]),t._v(" "),o("form",{staticClass:"mt-2",on:{submit:function(e){return e.preventDefault(),t.setPasswordSubmitHandler.apply(null,arguments)}}},[o("div",{staticClass:"mb-3"},[o("text-input",{attrs:{type:"password",placeholder:t.$t("password"),name:"password","hide-details":"",autocomplete:"new-password"},scopedSlots:t._u([{key:"append",fn:function(){return[o("div",{staticStyle:{"margin-top":"5px"}},[o("v-img",{attrs:{src:r(390),width:"14",height:"21"}})],1)]},proxy:!0}],null,!1,3250624211),model:{value:t.password,callback:function(e){t.password=e},expression:"password"}})],1),t._v(" "),o("div",[o("text-input",{attrs:{type:"password",placeholder:t.$t("repeat_password"),name:"confirmPassword","hide-details":"",autocomplete:"new-password"},scopedSlots:t._u([{key:"append",fn:function(){return[o("div",{staticStyle:{"margin-top":"5px"}},[o("v-img",{attrs:{src:r(390),width:"14",height:"21"}})],1)]},proxy:!0}],null,!1,3250624211),model:{value:t.passwordRepeat,callback:function(e){t.passwordRepeat=e},expression:"passwordRepeat"}})],1),t._v(" "),o("div",{staticClass:"form-message"},[t.passwordLengthError?[o("div",{staticClass:"form-message-wrap form-message-wrap--error"},[o("div",{staticClass:"form-message-icon"},[o("svg",{attrs:{width:"12",height:"12",viewBox:"0 0 12 12"}},[o("use",{attrs:{"xlink:href":r(91)+"#attention"}})])]),t._v("\n              "+t._s(t.passwordTextError)+"\n            ")])]:t._e()],2),t._v(" "),o("div",{staticClass:"set-password-button mt-1"},[o("v-btn",{staticClass:"font-weight-medium",attrs:{color:"orange","x-large":"",type:"submit"}},[t._v("\n            "+t._s(t.$t("save"))+"\n          ")])],1)])]:[t.token?[o("div",{staticClass:"set-password-title"},[t._v("\n          "+t._s(t.$t("sorry_this_link_has_expired"))+"\n        ")]),t._v(" "),o("div",{staticClass:"set-password-text mt-3 mt-md-5"},[t._v("\n          "+t._s(t.$t("please_request_new_link_by_clicking_forgot_password_button"))+"\n        ")])]:[o("div",{staticClass:"set-password-title"},[t._v("Something went wrong!")])]]],2)])}),[],!1,null,null,null);e.default=component.exports;h()(component,{LDialog:r(149).default}),h()(component,{VBtn:m.a,VImg:_.a})}}]);