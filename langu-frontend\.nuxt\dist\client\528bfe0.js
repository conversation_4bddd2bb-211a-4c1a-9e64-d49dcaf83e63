(window.webpackJsonp=window.webpackJsonp||[]).push([[188],{1960:function(e,n,r){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}r.r(n),r.d(n,"loadStripe",(function(){return E}));var o,l="https://js.stripe.com/v3",c=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,d="loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used",f=function(e){var n=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",script=document.createElement("script");script.src="".concat(l).concat(n);var r=document.head||document.body;if(!r)throw new Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return r.appendChild(script),script},v=null,w=null,m=null,y=function(e){return null!==v?v:(v=new Promise((function(n,r){if("undefined"!=typeof window&&"undefined"!=typeof document)if(window.Stripe&&e&&console.warn(d),window.Stripe)n(window.Stripe);else try{var script=function(){for(var e=document.querySelectorAll('script[src^="'.concat(l,'"]')),i=0;i<e.length;i++){var script=e[i];if(c.test(script.src))return script}return null}();if(script&&e)console.warn(d);else if(script){if(script&&null!==m&&null!==w){var t;script.removeEventListener("load",m),script.removeEventListener("error",w),null===(t=script.parentNode)||void 0===t||t.removeChild(script),script=f(e)}}else script=f(e);m=function(e,n){return function(){window.Stripe?e(window.Stripe):n(new Error("Stripe.js not available"))}}(n,r),w=function(e){return function(){e(new Error("Failed to load Stripe.js"))}}(r),script.addEventListener("load",m),script.addEventListener("error",w)}catch(e){return void r(e)}else n(null)}))).catch((function(e){return v=null,Promise.reject(e)}))},S=function(e,n,r){if(null===e)return null;var t=e.apply(void 0,n);return function(e,n){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"3.4.0",startTime:n})}(t,r),t},h=function(e){var n="invalid load parameters; expected object of shape\n\n    {advancedFraudSignals: boolean}\n\nbut received\n\n    ".concat(JSON.stringify(e),"\n");if(null===e||"object"!==t(e))throw new Error(n);if(1===Object.keys(e).length&&"boolean"==typeof e.advancedFraudSignals)return e;throw new Error(n)},j=!1,E=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];j=!0;var t=Date.now();return y(o).then((function(e){return S(e,n,t)}))};E.setLoadParameters=function(e){if(j&&o){var n=h(e);if(Object.keys(n).reduce((function(n,r){var t;return n&&e[r]===(null===(t=o)||void 0===t?void 0:t[r])}),!0))return}if(j)throw new Error("You cannot change load parameters after calling loadStripe");o=h(e)}}}]);