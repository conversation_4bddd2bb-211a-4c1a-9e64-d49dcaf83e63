exports.ids = [83];
exports.modules = {

/***/ 1053:
/***/ (function(module, exports, __webpack_require__) {

var map = {
	"./business.svg": 523,
	"./career.svg": 524,
	"./conversation.svg": 525,
	"./default.svg": 510,
	"./diplomacy.svg": 526,
	"./education.svg": 527,
	"./engineering.svg": 528,
	"./exam-preparation.svg": 529,
	"./finance-banking.svg": 530,
	"./grammar.svg": 531,
	"./interview-prep.svg": 532,
	"./it.svg": 533,
	"./law.svg": 534,
	"./life.svg": 535,
	"./marketing.svg": 536,
	"./medicine.svg": 537,
	"./science.svg": 538,
	"./tourism.svg": 539,
	"./travel.svg": 540,
	"./university-preparation.svg": 541,
	"./vocabulary.svg": 542,
	"./writing.svg": 543,
	"./young-learner.svg": 544
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = 1053;

/***/ }),

/***/ 1054:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1132);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("f30475ea", content, true, context)
};

/***/ }),

/***/ 1089:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/teacher-listing/TeacherListingBanner.vue?vue&type=template&id=5a0a35ec&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"banner"},[_vm._ssrNode("<div class=\"banner-content\" data-v-5a0a35ec>"+((_vm.banner.name)?("<div class=\"banner-title\" data-v-5a0a35ec>"+((_vm.banner.id)?(((_vm.userTag)?(_vm._ssrEscape("\n          "+_vm._s(_vm.banner.name)+"\n        ")):(((_vm.locale === 'es')?(_vm._ssrEscape("\n            "+_vm._s(_vm.selectedLanguage)+" Para\n            "+_vm._s(_vm.banner.name)+"\n          ")):(_vm.locale === 'pl')?(_vm._ssrEscape("\n            "+_vm._s(_vm.selectedLanguage)+": "+_vm._s(_vm.banner.name.toLowerCase())+"\n          ")):(_vm._ssrEscape("\n            "+_vm._s(_vm.selectedLanguage)+" for\n            "+_vm._s(_vm.banner.name)+"\n          ")))))):(_vm._ssrEscape("\n        "+_vm._s(_vm.banner.name)+"\n      ")))+"</div>"):"<!---->")+" <div class=\"banner-text\" data-v-5a0a35ec>"+_vm._ssrEscape("\n      "+_vm._s(_vm.banner.description)+"\n    ")+"</div></div> "),(_vm.banner.image)?_vm._ssrNode("<div"+(_vm._ssrClass(null,['banner-image d-flex', _vm.userTag ? 'align-center' : 'align-end']))+" data-v-5a0a35ec>","</div>",[_vm._ssrNode("<div class=\"banner-image-helper\" data-v-5a0a35ec>","</div>",[_c('v-img',{attrs:{"src":_vm.banner.image,"contain":"","max-height":"120","eager":""}})],1)]):_vm._e()],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/teacher-listing/TeacherListingBanner.vue?vue&type=template&id=5a0a35ec&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/teacher-listing/TeacherListingBanner.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var TeacherListingBannervue_type_script_lang_js_ = ({
  name: 'TeacherListingBanner',

  data() {
    return {
      banner: {}
    };
  },

  computed: {
    locale() {
      return this.$i18n.locale;
    },

    activeFilters() {
      return this.$store.getters['teacher_filter/activeFilters'];
    },

    selectedLanguage() {
      const language = this.$store.getters['teacher_filter/selectedLanguage'];
      return language ? language.name.charAt(0).toUpperCase() + language.name.slice(1) : this.$t('learning');
    },

    selectedMotivation() {
      return this.activeFilters.find(item => item.type === 'motivation');
    },

    selectedSpecialities() {
      return this.activeFilters.filter(item => item.type === 'speciality');
    },

    motivationBanners() {
      return this.$store.state.teacher_filter.motivationBanners;
    },

    specialityBanners() {
      return this.$store.state.teacher_filter.specialityBanners;
    },

    isUserLogged() {
      return this.$store.getters['user/isUserLogged'];
    },

    userTag() {
      return this.$store.getters['user/userTag'];
    }

  },
  watch: {
    isUserLogged(newValue, oldValue) {
      if (newValue) {
        this.setBanner();
      }
    }

  },

  created() {
    this.setBanner();
  },

  methods: {
    setBanner() {
      let image = __webpack_require__(510);

      if (this.userTag) {
        var _this$userTag, _this$userTag2;

        this.banner = { ...this.banner,
          name: this.userTag.headLine,
          description: this.userTag.description
        };

        if ((_this$userTag = this.userTag) !== null && _this$userTag !== void 0 && _this$userTag.logo && ((_this$userTag2 = this.userTag) === null || _this$userTag2 === void 0 ? void 0 : _this$userTag2.logo.length) > 0) {
          this.banner.image = this.userTag.logo;
        }

        return;
      }

      if (this.selectedMotivation) {
        const motivationBanner = this.motivationBanners.find(item => item.id === this.selectedMotivation.id);

        if (motivationBanner) {
          image = motivationBanner !== null && motivationBanner !== void 0 && motivationBanner.image ? __webpack_require__(1053)(`./${motivationBanner.image}`) : image;
          this.banner = { ...this.selectedMotivation,
            image,
            name: this.selectedMotivation.motivationName,
            description: this.$t(motivationBanner.description)
          };
        }

        if (this.selectedSpecialities.length === 1) {
          const speciality = this.selectedMotivation.specialities.find(item => item.id === this.selectedSpecialities[0].id);
          const specialityBanner = this.specialityBanners.find(item => item.id === speciality.id);

          if (speciality) {
            this.banner = { ...speciality,
              image: specialityBanner !== null && specialityBanner !== void 0 && specialityBanner.image ? __webpack_require__(1053)(`./${specialityBanner.image}`) : image,
              props: specialityBanner === null || specialityBanner === void 0 ? void 0 : specialityBanner.props
            };
          }
        }

        return;
      }

      return this.banner = {
        image
      };
    }

  }
});
// CONCATENATED MODULE: ./components/teacher-listing/TeacherListingBanner.vue?vue&type=script&lang=js&
 /* harmony default export */ var teacher_listing_TeacherListingBannervue_type_script_lang_js_ = (TeacherListingBannervue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/teacher-listing/TeacherListingBanner.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1131)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  teacher_listing_TeacherListingBannervue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "5a0a35ec",
  "d13121ee"
  
)

/* harmony default export */ var TeacherListingBanner = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */


installComponents_default()(component, {VImg: VImg["a" /* default */]})


/***/ }),

/***/ 1131:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeacherListingBanner_vue_vue_type_style_index_0_id_5a0a35ec_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1054);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeacherListingBanner_vue_vue_type_style_index_0_id_5a0a35ec_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeacherListingBanner_vue_vue_type_style_index_0_id_5a0a35ec_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeacherListingBanner_vue_vue_type_style_index_0_id_5a0a35ec_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeacherListingBanner_vue_vue_type_style_index_0_id_5a0a35ec_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1132:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".banner[data-v-5a0a35ec]{position:relative;display:flex;justify-content:space-between;min-height:125px;padding:8px 8px 0 32px;line-height:1.333}@media only screen and (min-width:992px)and (max-width:1439px){.banner[data-v-5a0a35ec]{padding:5px 15px 0 20px}}@media only screen and (max-width:767px){.banner[data-v-5a0a35ec]{flex-direction:column}}@media only screen and (max-width:639px){.banner[data-v-5a0a35ec]{padding:16px 16px 0}}.banner[data-v-5a0a35ec]:before{content:\"\";position:absolute;top:0;left:0;width:100%;height:100%;opacity:.1;border-radius:16px}.banner-content[data-v-5a0a35ec]{position:relative;display:flex;flex-direction:column;justify-content:center;padding:15px 10px 20px 0}@media only screen and (min-width:768px){.banner-content[data-v-5a0a35ec]{max-width:600px;min-width:296px}}@media only screen and (max-width:639px){.banner-content[data-v-5a0a35ec]{padding:0 0 15px}}.banner-title[data-v-5a0a35ec]{margin-bottom:8px;font-size:24px;font-weight:700;color:var(--v-success-base);background:linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));background:-webkit-linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));-webkit-background-clip:text;-webkit-text-fill-color:transparent}@media only screen and (min-width:992px)and (max-width:1439px){.banner-title[data-v-5a0a35ec]{font-size:22px}}@media only screen and (max-width:639px){.banner-title[data-v-5a0a35ec]{font-size:20px}}.banner-text[data-v-5a0a35ec]{font-weight:300;font-size:14px;letter-spacing:-.002em}@media only screen and (max-width:767px){.banner-image[data-v-5a0a35ec]{justify-content:center}.banner-image .v-image[data-v-5a0a35ec]{max-height:90px!important}}.banner-image-helper[data-v-5a0a35ec]{width:362px}@media only screen and (max-width:1439px){.banner-image-helper[data-v-5a0a35ec]{width:250px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

};;
//# sourceMappingURL=teacher-listing-banner.js.map