{"version": 3, "file": "components/classroom-video-input.js", "sources": ["webpack:///./components/classroom/VideoInput.vue?ca37", "webpack:///./components/classroom/VideoInput.vue?c656", "webpack:///./components/classroom/VideoInput.vue?5635", "webpack:///./components/classroom/VideoInput.vue?abe7", "webpack:///./components/classroom/VideoInput.vue", "webpack:///./components/classroom/VideoInput.vue?b75a", "webpack:///./components/classroom/VideoInput.vue?8c95"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./VideoInput.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"67f245fe\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./VideoInput.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".video-component,.video-component *{cursor:auto!important}.video-component .v-card{padding:70px 15px 20px}.video-component .v-input .v-input__slot{background-color:#fff}.video-component .input-wrap{position:relative}.video-component .input-wrap-error{position:absolute;bottom:8px;left:0;padding-left:16px}.video-component .video-buttons-wrap{display:flex;justify-content:center;align-items:center;width:100%}.video-component button,.video-component button *{cursor:pointer!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-form',{on:{\"submit\":function($event){$event.preventDefault();return _vm.submit.apply(null, arguments)}}},[_c('div',{staticClass:\"input-wrap\"},[_c('text-input',{ref:\"input\",attrs:{\"value\":_vm.inputUrl,\"type-class\":\"border-gradient\",\"height\":\"44\",\"rules\":[_vm.rules.required],\"placeholder\":_vm.$t('enter_youtube_url')},on:{\"input\":function($event){_vm.inputUrl = $event}}}),_vm._v(\" \"),(!_vm.isValid)?_c('div',{staticClass:\"input-wrap-error v-text-field__details\"},[_c('div',{staticClass:\"v-messages theme--light error--text\",attrs:{\"role\":\"alert\"}},[_c('div',{staticClass:\"v-messages__wrapper\"},[_c('div',{staticClass:\"v-messages__message\"},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('invalid_url_or_video_id'))+\"\\n          \")])])])]):_vm._e()],1),_vm._v(\" \"),_c('div',{staticClass:\"video-buttons-wrap\"},[_c('v-btn',{attrs:{\"color\":\"primary\",\"small\":\"\",\"type\":\"submit\"}},[_vm._v(\"\\n      \"+_vm._s(_vm.$t('load_video'))+\"\\n    \")])],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport TextInput from '~/components/form/TextInput'\n\nexport default {\n  name: 'VideoInput',\n  components: { TextInput },\n  data() {\n    return {\n      inputUrl: null,\n      isValid: true,\n      videoId: null,\n      rules: {\n        required: (v) => !!v || this.$t('field_required'),\n      },\n    }\n  },\n  computed: {\n    role() {\n      return this.$store.getters['classroom/role']\n    },\n    isVideoInputOpened() {\n      return this.$store.state.classroom.isVideoInputOpened\n    },\n  },\n  watch: {\n    inputUrl(newValue, oldValue) {\n      this.isValid = true\n\n      let videoId\n\n      if (newValue?.includes('v=')) {\n        videoId = newValue.split('v=')[1]\n\n        const ampersandPosition = videoId.indexOf('&')\n\n        if (ampersandPosition !== -1) {\n          videoId = videoId.substring(0, ampersandPosition)\n        }\n      }\n\n      if (newValue.length && (!videoId || !videoId.match(/[^\"&=\\s?]{11}/))) {\n        this.isValid = false\n      }\n\n      this.videoId = videoId\n    },\n  },\n  mounted() {\n    this.$refs.input.focus()\n  },\n  methods: {\n    async submit() {\n      if (!this.isValid || !this.videoId) return\n\n      await this.$store.dispatch('classroom/createAsset', {\n        type: 'video',\n        videoId: this.videoId,\n        index: this.$store.state.classroom.maxIndex + 1,\n        shapes: [],\n        owner: this.role,\n      })\n\n      this.$store.commit('classroom/closeVideoInput')\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./VideoInput.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./VideoInput.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./VideoInput.vue?vue&type=template&id=2169d7bb&\"\nimport script from \"./VideoInput.vue?vue&type=script&lang=js&\"\nexport * from \"./VideoInput.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./VideoInput.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"7f4d3e50\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VForm } from 'vuetify/lib/components/VForm';\ninstallComponents(component, {VBtn,VForm})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AAJA;AAQA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;AAQA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA;AACA;AAsBA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AALA;AAQA;AACA;AACA;AAdA;AA/CA;;ACjCA;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}