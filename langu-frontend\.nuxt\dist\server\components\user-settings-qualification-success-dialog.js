exports.ids = [118];
exports.modules = {

/***/ 1275:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/QualificationSuccessDialog.vue?vue&type=template&id=1fa2a67e&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{"dialog":_vm.isShownDialog,"max-width":"418","custom-class":"qualification-added text-center"}},_vm.$listeners),[_c('div',[_c('v-img',{staticClass:"mx-auto mb-3",attrs:{"src":__webpack_require__(621),"width":"56","height":"56"}}),_vm._v(" "),_c('div',{staticClass:"qualification-added-text"},[_vm._v("\n      "+_vm._s(_vm.$t(
          'thank_you_for_saving_your_qualification_it_will_shortly_be_verified_by_langu_admin'
        ))+"\n    ")])],1)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/QualificationSuccessDialog.vue?vue&type=template&id=1fa2a67e&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/QualificationSuccessDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var QualificationSuccessDialogvue_type_script_lang_js_ = ({
  name: 'QualificationSuccessDialog',
  props: {
    isShownDialog: {
      type: Boolean,
      required: true
    }
  }
});
// CONCATENATED MODULE: ./components/user-settings/QualificationSuccessDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_QualificationSuccessDialogvue_type_script_lang_js_ = (QualificationSuccessDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-settings/QualificationSuccessDialog.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_QualificationSuccessDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "40ac936b"
  
)

/* harmony default export */ var QualificationSuccessDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LDialog: __webpack_require__(28).default})


/* vuetify-loader */


installComponents_default()(component, {VImg: VImg["a" /* default */]})


/***/ })

};;
//# sourceMappingURL=user-settings-qualification-success-dialog.js.map