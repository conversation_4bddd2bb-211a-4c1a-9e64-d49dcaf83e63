(window.webpackJsonp=window.webpackJsonp||[]).push([[53,49],{1389:function(t,e,o){"use strict";o.r(e);o(31),o(35),o(60);var n=o(1528),r=o(266),l=o(1428),c=(o(1449),{components:{VueDraggableResizable:n.default},mixins:[l.a],props:{asset:{type:Object,required:!0},childHeaderHeight:{type:Number,default:0},lockAspectRatio:{type:Boolean,default:!0},scalable:{type:Boolean,default:!0},hoverEnabled:{type:Boolean,default:!0},isDraggableProp:{type:Boolean,default:!0},hideResizeIcon:{type:Boolean,default:!1},handles:{type:Array,default:function(){return["tl","tm","tr","mr","br","bm","bl","ml"]}}},data:function(){return{width:r.j,height:r.i,top:50,left:500,isHovered:!1,isHoveredByAsset:!1,index:5,eventBodyClass:null,allowIndexChange:!0,resizable:!0,draggable:!0,synchronizeable:!0,viewportWidth:window.innerWidth,viewportHeight:window.innerHeight,offset:5}},computed:{isCanvasOversizeX:function(){return r.n>this.viewportWidth},isCanvasOversizeY:function(){return r.k>this.viewportHeight},isScaledCanvasOversizeY:function(){return r.k*this.zoomIndex>this.viewportHeight},getRoleHoverColor:function(){return"teacher"===this.role?"var(--v-teacherColor-base)":"var(--v-studentColor-base)"},enabledResizeable:function(){return this.resizable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},enabledDraggable:function(){return this.isDraggableProp&&this.draggable&&this.$store.state.classroom.containerComponentEnabled&&!this.isLockedForStudent},maxIndex:function(){return this.$store.state.classroom.maxIndex},zoom:function(){return this.$store.getters["classroom/zoomAsset"].asset},zoomIndex:function(){var t,e;return null!==(t=null===(e=this.zoom)||void 0===e?void 0:e.zoomIndex)&&void 0!==t?t:1},type:function(){var t,e;return null===(t=this.asset)||void 0===t||null===(e=t.asset)||void 0===e?void 0:e.type},topScale:function(){return"toolbar"===this.type?this.top<0||this.top+this.height>this.viewportHeight?this.viewportHeight-this.height-70:this.top:this.synchronizeable?this.top-this.zoom.y:this.top},leftScale:function(){return"toolbar"===this.type?this.left+this.width>this.viewportWidth||this.left<0?this.viewportWidth-this.width-15:this.left:this.synchronizeable?this.left-this.zoom.x:this.left},isLockedForStudent:function(){return this.$store.getters["classroom/isLocked"]&&"student"===this.role},isSocketConnected:function(){return this.$store.state.socket.isConnected}},watch:{"asset.asset":function(t){this.move(t)}},mounted:function(){this.move(this.asset.asset)},methods:{mouseenter:function(){var t,e;(this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!0,this.socketAssetMoved({isHovered:!0})),"twilio"===this.type||"tokbox"===this.type||"editor"===this.type)&&(this.$store.commit("classroom/setCursorNameBeforeChange",(null===(t=this.$store.getters["classroom/userParams"])||void 0===t?void 0:t.cursor)||"cursor-pointer"),this.$store.commit("classroom/setToolNameBeforeChange",(null===(e=this.$store.getters["classroom/userParams"])||void 0===e?void 0:e.tool)||"pointer"),this.setTool("pointer","cursor-pointer"))},mouseleave:function(){this.hoverEnabled&&this.synchronizeable&&(this.isHovered=!1,this.socketAssetMoved({isHovered:!1})),"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||this.setTool(this.$store.state.classroom.toolNameBeforeChange,this.$store.state.classroom.cursorNameBeforeChange)},onIndex:function(){this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{index:this.index}}),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{index:this.index}})},updateAsset:function(t,e){this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,index:this.index}})},onDrag:function(t,e){if(this.synchronizeable){if(this.left=t+this.zoom.x,this.top=e+this.zoom.y,this.allowIndexChange){var o=document.body;o.classList.contains(this.eventBodyClass)||(this.eventBodyClass="dragging",o.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var n={left:this.left,top:this.top,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:n}),this.socketAssetMoved(n)}},onDragStop:function(t,e){var o=document.body;o.classList.contains(this.eventBodyClass)&&(o.classList.remove(this.eventBodyClass),this.eventBodyClass=null),this.allowIndexChange=!0,this.$store.commit("classroom/setMaxIndex",this.index),this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,index:this.index}})},onResize:function(t,e,o,n,r){if(this.synchronizeable){if(this.left=t+this.zoom.x,this.top=e+this.zoom.y,this.width=o,this.height=n-this.childHeaderHeight,this.allowIndexChange){var l=document.body;l.classList.contains(this.eventBodyClass)||(this.eventBodyClass=r.split(" ")[1],l.classList.add(this.eventBodyClass)),this.allowIndexChange=!1,this.index=this.maxIndex+1,this.$store.commit("classroom/setMaxIndex",this.index)}var c={left:this.left,top:this.top,width:this.width,height:this.height,index:this.index};this.$store.commit("classroom/moveAsset",{id:this.asset.id,asset:c}),this.socketAssetMoved(c)}},onResizeStop:function(t,e,o,n){this.eventBodyClass&&document.body.classList.remove(this.eventBodyClass),this.allowIndexChange=!0,this.$store.dispatch("classroom/moveAsset",{id:this.asset.id,lessonId:this.asset.lessonId,asset:{left:this.synchronizeable?t+this.zoom.x:t,top:this.synchronizeable?e+this.zoom.y:e,width:o,height:n-this.childHeaderHeight,index:this.index}})},socketAssetMoved:function(t){this.isSocketConnected&&this.$socket.emit("asset-moved",{id:this.asset.id,lessonId:this.asset.lessonId,asset:t})},move:function(t){if(void 0!==t.width?this.width=t.width:"editor"===this.type&&(this.width=.66*(this.isCanvasOversizeX?this.viewportWidth:r.n)),void 0!==t.height)this.height=t.height;else{if("editor"===this.type){var e=.8*(this.isCanvasOversizeY?this.viewportHeight:r.k);e>1200&&(e=1200),e<400&&(e=400),this.height=e-2*this.offset}"audio"===this.type&&(this.height=0)}void 0!==t.top?this.top=t.top:"twilio"!==this.type&&"tokbox"!==this.type&&"editor"!==this.type||(this.top=this.offset),void 0!==t.left?this.left=t.left:("twilio"!==this.type&&"tokbox"!==this.type||(this.left=(this.isCanvasOversizeX?this.viewportWidth:r.n)-this.width-this.offset),"editor"===this.type&&(this.left=this.offset)),void 0!==t.index&&(this.index=t.index),void 0!==t.resizable&&(this.resizable=t.resizable),void 0!==t.draggable&&(this.draggable=t.draggable),void 0!==t.synchronizeable&&(this.synchronizeable=t.synchronizeable),void 0!==t.isHovered&&(this.isHoveredByAsset=t.isHovered)}}}),d=o(22),component=Object(d.a)(c,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{on:{mouseenter:t.mouseenter,mouseleave:t.mouseleave}},[o("vue-draggable-resizable",{ref:"vueDraggableResizable",class:{student:"student"===t.role,teacher:"teacher"===t.role,"hide-resize-icon":t.hideResizeIcon},style:{outline:t.isHoveredByAsset||t.isHovered?"3px solid "+t.getRoleHoverColor:"none"},attrs:{draggable:t.enabledDraggable,resizable:t.enabledResizeable,w:t.width,h:t.height+t.childHeaderHeight,x:t.leftScale,y:t.topScale,z:t.index,"zoom-index":t.scalable?t.zoom.zoomIndex:1,"zoom-x":t.zoom.x,"zoom-y":t.zoom.y,"child-header-height":t.childHeaderHeight,"lock-aspect-ratio":t.lockAspectRatio,handles:t.handles},on:{dragging:t.onDrag,resizing:t.onResize,dragstop:t.onDragStop,resizestop:t.onResizeStop,"update-asset":t.updateAsset},nativeOn:{click:function(e){return t.onIndex.apply(null,arguments)}}},[t._t("default",null,{onIndex:t.onIndex})],2)],1)}),[],!1,null,null,null);e.default=component.exports},1428:function(t,e,o){"use strict";o(35),o(81),o(23),o(6),o(24),o(38);var n={computed:{role:function(){return this.$store.getters["classroom/role"]}},methods:{setTool:function(t,e){this.$store.commit("classroom/enableContainerComponent","pointer"===t),this.$socket.emit("cursor-moved",{tool:t,cursor:e.replace(/(-cursor|cursor-)/i,""),lessonId:this.$store.state.classroom.lessonId}),this.$store.commit("classroom/setUserTool",t),this.$store.commit("classroom/setUserCursor",e);var o=document.body,n=o.classList;this.removeCursors(n),o.classList.add("".concat(this.role,"-").concat(e)),this.classList=o.classList},removeCursors:function(t){t.forEach((function(t){t.includes("cursor")&&document.body.classList.remove(t)}))}}},r=o(22),component=Object(r.a)(n,undefined,undefined,!1,null,null,null);e.a=component.exports},1449:function(t,e,o){var content=o(1477);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("07a1f444",content,!0,{sourceMap:!1})},1477:function(t,e,o){var n=o(18),r=o(265),l=o(870),c=n(!1),d=r(l);c.push([t.i,'.vdr{touch-action:none;position:absolute;box-sizing:border-box;border:none}.handle{display:block!important}.resizable.vdr:not(.hide-resize-icon):after{content:"";position:absolute;bottom:4px;right:2px;width:8px;height:8px;background-image:url('+d+');background-size:contain;background-repeat:no-repeat;background-position:50%;opacity:.9;z-index:99}.vdr .handle{box-sizing:border-box;position:absolute;width:12px;height:12px;border:none;background-color:transparent;z-index:10}.vdr .handle-tl{top:-7px;left:-7px}.vdr .handle-tm{top:-7px;margin-left:-5px}.vdr .handle-tr{top:-7px;right:-7px}.vdr .handle-ml{margin-top:-5px;left:-7px}.vdr .handle-mr{margin-top:-5px;right:-7px}.vdr .handle-bl{bottom:-7px;left:-7px}.vdr .handle-bm{bottom:-7px;margin-left:-5px}.vdr .handle-br{bottom:-7px;right:-7px}@media only screen and (max-width:768px){[class*=handle-]:before{content:"";left:-10px;right:-10px;bottom:-10px;top:-10px;position:absolute}}.vdr .handle-bm,.vdr .handle-tm{width:100%;left:5px}.vdr .handle-ml,.vdr .handle-mr{height:100%;top:5px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:14px;height:14px;z-index:11}@media only screen and (max-width:1199px){.vdr .handle-bm,.vdr .handle-tm{height:15px}.vdr .handle-ml,.vdr .handle-mr{width:15px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:15px;height:15px}.vdr .handle-tl{top:-10px;left:-10px}.vdr .handle-tm{top:-10px;margin-left:-5px}.vdr .handle-tr{top:-10px;right:-10px}.vdr .handle-ml{margin-top:-5px;left:-10px}.vdr .handle-mr{margin-top:-5px;right:-10px}.vdr .handle-bl{bottom:-10px;left:-10px}.vdr .handle-bm{bottom:-10px;margin-left:-5px}.vdr .handle-br{bottom:-10px;right:-10px}}',""]),t.exports=c},1585:function(t,e,o){var content=o(1683);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("77e2f3f6",content,!0,{sourceMap:!1})},1682:function(t,e,o){"use strict";o(1585)},1683:function(t,e,o){var n=o(18)(!1);n.push([t.i,".stream-controls{position:absolute;left:50%;bottom:-38px;z-index:10;transform:translateX(-50%)}.stream-controls .toolbar-button-wrapper{width:40px;height:40px}.stream-controls .toolbar-button-wrapper button svg{margin:0 auto}.stream-controls .toolbar-button-wrapper button:focus{outline:none}.stream-controls .toolbar-button-wrapper-full-screen button{padding:9px}.stream-controls .hover-btn-info{left:50%;right:auto;top:auto;bottom:-36px;transform:translateX(-50%)}.stream-controls .hover-btn-info:after{left:50%;top:-9px;right:auto;border:5px solid transparent;border-bottom-color:#444;transform:translateX(-50%)}.stream-controls .stream-controls-switch{display:flex;justify-content:center;align-items:center;margin-top:-6px;padding:10px 6px 3px;background:#fff;border:none;border-radius:0 0 6px 6px;font-size:13px;line-height:1;outline:none;box-shadow:0 2px 10px rgba(0,0,0,.25)}.stream-controls .stream-controls-switch .toolbar-button-wrapper{width:18px;height:18px}.stream-controls .stream-controls-switch .toolbar-button-wrapper:not(:last-child){margin-right:4px}.stream-controls .stream-controls-switch .toolbar-button-item{min-width:18px!important;font-size:13px!important;border-radius:50%!important;overflow:hidden}.stream-controls .stream-controls-switch .toolbar-button-item--selected{color:#fff;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%)!important}.toolbar-button-icon.hand-raised{fill:#ffc107!important}.toolbar-button-icon.chat-enabled,.toolbar-button-icon.participants-enabled{fill:#2196f3!important}",""]),t.exports=n},1704:function(t,e,o){"use strict";o.r(e);var n={name:"VideoActions",props:{settings:{type:Object,required:!0},isJoined:{type:Boolean,required:!0},isScreenShareDisabled:{type:Boolean,default:!1},type:{type:String,required:!0}},computed:{role:function(){return this.$store.getters["classroom/role"]}}},r=(o(1682),o(22)),component=Object(r.a)(n,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"stream-controls"},[n("div",{staticClass:"video-window-buttons-wrap",attrs:{id:"video-window-buttons"}},[n("div",{staticClass:"stream-controls-wrapper cursor-auto"},[n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-video":"",type:"button",disabled:!t.isJoined},on:{click:function(e){return t.$emit("toggle-video")}}},[t.settings.isVideoEnabled?n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"51",height:"33",viewBox:"0 0 51 33"}},[n("use",{attrs:{"xlink:href":o(884)+"#videocam"}})]):t.settings.isVideoEnabled?t._e():n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"39",height:"33",viewBox:"0 0 39 33"}},[n("use",{attrs:{"xlink:href":o(884)+"#videocam_off"}})])]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.settings.isVideoEnabled?[t._v("\n            "+t._s(t.$t("turn_off_camera"))+"\n          ")]:[t._v("\n            "+t._s(t.$t("turn_on_camera"))+"\n          ")]],2)]),t._v(" "),n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-audio":"",type:"button",disabled:!t.isJoined},on:{click:function(e){return t.$emit("toggle-audio")}}},[t.settings.isMuted?t._e():n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"23",height:"33",viewBox:"0 0 23 33"}},[n("use",{attrs:{"xlink:href":o(883)+"#microphone"}})]),t._v(" "),t.settings.isMuted?n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"31",height:"34",viewBox:"0 0 31 34"}},[n("use",{attrs:{"xlink:href":o(883)+"#microphone-off"}})]):t._e()]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.settings.isMuted?[t._v("\n            "+t._s(t.$t("unmute_microphone"))+"\n          ")]:[t._v("\n            "+t._s(t.$t("mute_microphone"))+"\n          ")]],2)]),t._v(" "),n("div",{staticClass:"toolbar-button-wrapper toolbar-button-wrapper-full-screen"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-full-screen":"",type:"button",disabled:!t.isJoined},on:{click:function(e){return t.$emit("toggle-full-screen")}}},[t.settings.isFullscreenEnabled?t._e():n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"31",height:"31",viewBox:"0 0 31 31"}},[n("use",{attrs:{"xlink:href":o(882)+"#full_screen"}})]),t._v(" "),t.settings.isFullscreenEnabled?n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"31",height:"31",viewBox:"0 0 31 31"}},[n("use",{attrs:{"xlink:href":o(882)+"#window_screen"}})]):t._e()]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.settings.isFullscreenEnabled?[t._v("\n            "+t._s(t.$t("leave_full_screen_video"))+"\n          ")]:[t._v("\n            "+t._s(t.$t("full_screen_video"))+"\n          ")]],2)]),t._v(" "),n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-screen-share":"",type:"button",disabled:t.isScreenShareDisabled||!t.isJoined&&"twilio"===t.type},on:{click:function(e){return t.$emit("toggle-screen-share")}}},[t.settings.isScreenShareEnabled?n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"38",height:"35",viewBox:"0 0 38 35"}},[n("use",{attrs:{"xlink:href":o(875)+"#not_share"}})]):t._e(),t._v(" "),t.settings.isScreenShareEnabled?t._e():n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"37",height:"28",viewBox:"0 0 37 28"}},[n("use",{attrs:{"xlink:href":o(875)+"#share"}})])]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.isScreenShareDisabled?[t._v("\n            "+t._s(t.$t("share_is_disabled"))+"\n          ")]:[t.settings.isScreenShareEnabled?[t._v("\n              "+t._s(t.$t("stop_screenshare"))+"\n            ")]:[t._v("\n              "+t._s(t.$t("share_my_screen"))+"\n            ")]]],2)]),t._v(" "),"whereby"===t.type?n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-hand-raise":"",type:"button",disabled:!t.isJoined},on:{click:function(e){return t.$emit("toggle-hand-raise")}}},[n("svg",{class:["toolbar-button-icon",{"hand-raised":t.settings.isHandRaised}],attrs:{width:"24",height:"33",viewBox:"0 0 24 33"}},[n("use",{attrs:{"xlink:href":o(933)+"#hand"}})])]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.settings.isHandRaised?[t._v("\n            "+t._s(t.$t("lower_hand"))+"\n          ")]:[t._v("\n            "+t._s(t.$t("raise_hand"))+"\n          ")]],2)]):t._e(),t._v(" "),"whereby"===t.type?n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-chat":"",type:"button",disabled:!t.isJoined},on:{click:function(e){return t.$emit("toggle-chat")}}},[n("svg",{class:["toolbar-button-icon",{"chat-enabled":t.settings.isChatEnabled}],attrs:{width:"28",height:"28",viewBox:"0 0 28 28"}},[n("use",{attrs:{"xlink:href":o(932)+"#chat"}})])]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.settings.isChatEnabled?[t._v("\n            "+t._s(t.$t("hide_chat"))+"\n          ")]:[t._v("\n            "+t._s(t.$t("show_chat"))+"\n          ")]],2)]):t._e(),t._v(" "),"whereby"===t.type?n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-toggle-participants":"",type:"button",disabled:!t.isJoined},on:{click:function(e){return t.$emit("toggle-participants")}}},[n("svg",{class:["toolbar-button-icon",{"participants-enabled":t.settings.isParticipantsEnabled}],attrs:{width:"32",height:"28",viewBox:"0 0 32 28"}},[n("use",{attrs:{"xlink:href":o(934)+"#participants"}})])]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t.settings.isParticipantsEnabled?[t._v("\n            "+t._s(t.$t("hide_participants"))+"\n          ")]:[t._v("\n            "+t._s(t.$t("show_participants"))+"\n          ")]],2)]):t._e()])])])}),[],!1,null,null,null);e.default=component.exports},1911:function(t,e,o){var content=o(2184);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,o(19).default)("75ee3699",content,!0,{sourceMap:!1})},2183:function(t,e,o){"use strict";o(1911)},2184:function(t,e,o){var n=o(18)(!1);n.push([t.i,".twilio-stream .local-stream video,.twilio-stream .remote-stream video{position:absolute;top:50%;left:0;width:100%;height:100%;transform:translateY(-50%)}.twilio-stream .remote-stream{position:relative}",""]),t.exports=n},2235:function(t,e,o){"use strict";o.r(e);o(7),o(8),o(9),o(14),o(15);var n=o(2),r=(o(6),o(40),o(23),o(2074)),l=o(859),c=o(695),d=o(1389),h=o(1704);function m(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(object);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,o)}return e}function v(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?m(Object(source),!0).forEach((function(e){Object(n.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):m(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var f={name:"Twilio",components:{ClassroomContainer:d.default,VideoActions:h.default},props:{file:{type:Object,required:!0},screenShareAsset:{type:Object,required:!0},zoomOtherAsset:{type:Object,required:!0}},data:function(){return{room:null,localStreamContainer:null,remoteStreamContainer:null,screenShareStreamContainer:null,screenShareTrack:null,isLocalScreenShareEnabled:!1,isRemoteScreenShareEnabled:!1,screenSharingNotSupported:!Object(c.c)(),isJoined:!1,settings:{isScreenShareEnabled:!1,isFullscreenEnabled:!1}}},computed:{twilioAccessToken:function(){return this.$store.getters["classroom/twilioAccessToken"]},twilioRoomName:function(){return this.$store.getters["classroom/twilioRoomName"]},role:function(){return this.$store.getters["classroom/role"]},isVideoEnabled:function(){var t,e,o,n;return null===(t=null===(e=this.file.asset)||void 0===e||null===(o=e.settings)||void 0===o||null===(n=o[this.role])||void 0===n?void 0:n.isVideoEnabled)||void 0===t||t},isMuted:function(){var t,e,o,n;return null!==(t=null===(e=this.file.asset)||void 0===e||null===(o=e.settings)||void 0===o||null===(n=o[this.role])||void 0===n?void 0:n.isMuted)&&void 0!==t&&t},maxIndex:function(){return this.$store.state.classroom.maxIndex}},mounted:function(){var t=this;this.$nextTick((function(){t.localStreamContainer=document.getElementById("twilio-local-stream-placeholder"),t.remoteStreamContainer=document.getElementById("twilio-remote-stream-placeholder"),t.screenShareStreamContainer=document.getElementById("twilio-screenshare-placeholder"),r.isSupported?Object(r.connect)(t.twilioAccessToken,{name:t.twilioRoomName,audio:!0}).then((function(e){t.room=e,t.isJoined=!0,e.participants.forEach((function(e){t.attachRemoteParticipant(e)})),e.on("participantConnected",(function(e){t.attachRemoteParticipant(e)})),e.on("disconnected",(function(e,o){t.stopLocalVideo(),e.localParticipant.tracks.forEach((function(t){var e=t.track.detach();t.track.stop(),t.unpublish(),e.forEach((function(element){return element.remove()}))})),t.isLocalScreenShareEnabled=!1,t.isRemoteScreenShareEnabled=!1,t.localStreamContainer.innerHTML="",t.screenShareStreamContainer.innerHTML="",t.screenShareTrack=null,o&&console.log("You were disconnected from the Room:",o.code,o.message)})),t.isVideoEnabled&&t.startLocalVideo(),t.isMuted&&t.stopLocalAudio()}),(function(e){return t.handleMediaError(e)})).catch(t.handleMediaError):t.switchVideoPlayer("tokbox")})),window.addEventListener("beforeunload",this.closeStream),window.addEventListener("pagehide",this.closeStream),document.addEventListener("fullscreenchange",this.fullscreenChangeHandler)},beforeDestroy:function(){document.removeEventListener("fullscreenchange",this.fullscreenChangeHandler),this.closeStream()},methods:{startLocalAudio:function(){this.room.localParticipant.audioTracks.forEach((function(t){t.track.enable()}))},stopLocalAudio:function(){this.room.localParticipant.audioTracks.forEach((function(t){t.track.disable()}))},startLocalVideo:function(){var t=this;Object(r.createLocalVideoTrack)({name:"camera"}).then((function(e){t.room.localParticipant.publishTrack(e),t.localStreamContainer.appendChild(e.attach())})).catch(this.handleMediaError)},stopLocalVideo:function(){var t=this;this.room.localParticipant.videoTracks.forEach((function(e){"camera"===e.track.name&&(e.track.stop(),e.unpublish(),t.localStreamContainer.innerHTML="")}))},attachRemoteParticipant:function(t){var e=this;t.tracks.forEach((function(t){if(t.isSubscribed){var track=t.track;e.remoteStreamContainer.appendChild(track.attach())}})),t.on("trackSubscribed",(function(track){"screenshare"===track.name?(e.screenShareStreamContainer.appendChild(track.attach()),e.isRemoteScreenShareEnabled=!0):e.remoteStreamContainer.appendChild(track.attach())})),t.on("trackUnsubscribed",(function(track){"screenshare"===track.name&&(e.isRemoteScreenShareEnabled=!1,e.screenShareStreamContainer.innerHTML=""),"camera"!==track.name||e.hasActiveVideoStream()||(e.remoteStreamContainer.innerHTML="")}))},toggleVideo:function(){this.updateData(this.file.id,{settings:v(v({},this.file.asset.settings),{},Object(n.a)({},this.role,{isVideoEnabled:!this.isVideoEnabled,isMuted:this.isMuted}))}),this.isVideoEnabled?this.startLocalVideo():this.stopLocalVideo()},toggleAudio:function(){this.updateData(this.file.id,{settings:v(v({},this.file.asset.settings),{},Object(n.a)({},this.role,{isVideoEnabled:this.isVideoEnabled,isMuted:!this.isMuted}))}),this.isMuted?this.stopLocalAudio():this.startLocalAudio()},toggleFullScreen:function(){this.settings.isFullscreenEnabled=!this.settings.isFullscreenEnabled,Object(l.switchFullScreen)(this.settings.isFullscreenEnabled)},toggleScreenShare:function(){var t=this;this.settings.isScreenShareEnabled=!this.settings.isScreenShareEnabled,this.screenShareTrack&&!this.settings.isScreenShareEnabled?(this.isLocalScreenShareEnabled=!1,this.room.localParticipant.unpublishTrack(this.screenShareTrack),this.screenShareTrack.stop(),this.screenShareTrack=null,this.screenShareStreamContainer.innerHTML=""):navigator.mediaDevices.getDisplayMedia().then((function(e){t.screenShareTrack=Object(r.LocalVideoTrack)(e.getTracks()[0],{name:"screenshare"}),t.room.localParticipant.publishTrack(t.screenShareTrack),t.screenShareStreamContainer.appendChild(t.screenShareTrack.attach()),t.updateData(t.screenShareAsset.id,{index:t.maxIndex+1}),t.isLocalScreenShareEnabled=!0})).catch((function(){console.error("Could not share the screen"),alert("Could not share the screen")}))},switchVideoPlayer:function(t){this.$store.dispatch("classroom/deleteAsset",this.file),this.$store.dispatch("classroom/createAsset",v(v({},this.file.asset),{},{type:t}))},fullscreenChangeHandler:function(){document.fullscreenElement||(this.settings.isFullscreenEnabled=!1)},updateData:function(t,e){this.$store.commit("classroom/moveAsset",{id:t,asset:e}),this.$store.dispatch("classroom/moveAsset",{id:t,lessonId:this.file.lessonId,asset:e})},hasActiveVideoStream:function(){var t=!1;return this.room.participants.forEach((function(e){return e.videoTracks.forEach((function(e){e.isSubscribed&&(t=!0)}))})),t},closeStream:function(){this.room&&this.room.disconnect()},handleMediaError:function(t){switch(this.isJoined=!1,t.name){case"NotAllowedError":case"NotReadableError":alert("We are unable to access your video camera. Either the browser has not been given permissions or the camera is in use by another program.");break;case"NotFoundError":alert("1. Permission denied by system \n2. The object cannot be found here \n3. Requested device not found");break;default:alert("Sorry, we encountered an error trying to connect you to the remote stream. Please report to Langu Team. Error code: ".concat(t.code,", details: ").concat(t.message))}}}},x=(o(2183),o(22)),component=Object(x.a)(f,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"twilio-stream"},[n("classroom-container",{attrs:{asset:t.file,"hover-enabled":!1}},[n("div",{class:["twilio-component cursor-before-grab",{"video-window--is-fullscreen":t.settings.isFullscreenEnabled}],attrs:{id:"video-window"}},[n("div",{staticClass:"local-stream hr-flip",attrs:{id:"twilio-local-stream-placeholder"}}),t._v(" "),n("div",{staticClass:"remote-stream",attrs:{id:"twilio-remote-stream-placeholder"}}),t._v(" "),n("video-actions",{attrs:{"is-joined":t.isJoined,settings:Object.assign({},t.settings,{isMuted:t.isMuted,isVideoEnabled:t.isVideoEnabled}),"is-screen-share-disabled":t.isRemoteScreenShareEnabled||t.screenSharingNotSupported,type:t.file.asset.type},on:{"switch-video-player":t.switchVideoPlayer,"toggle-video":t.toggleVideo,"toggle-audio":t.toggleAudio,"toggle-full-screen":t.toggleFullScreen,"toggle-screen-share":t.toggleScreenShare}})],1)]),t._v(" "),n("classroom-container",{directives:[{name:"show",rawName:"v-show",value:t.isLocalScreenShareEnabled||t.isRemoteScreenShareEnabled,expression:"isLocalScreenShareEnabled || isRemoteScreenShareEnabled"}],attrs:{asset:t.screenShareAsset,"hover-enabled":!1}},[n("div",{staticClass:"tokbox-component screenshare-component cursor-before-grab"},[n("div",{staticClass:"user-name"},[t.isLocalScreenShareEnabled?[t._v("\n          "+t._s(t.$t("my_screen"))+"\n        ")]:t._e(),t._v(" "),t.isRemoteScreenShareEnabled?[t._v("\n          "+t._s(t.$t("classroom_user_screen",{username:t.zoomOtherAsset.asset.username}))+"\n        ")]:t._e()],2),t._v(" "),n("div",{staticClass:"remote-stream",attrs:{id:"twilio-screenshare-placeholder"}}),t._v(" "),t.isLocalScreenShareEnabled?n("div",{staticClass:"stream-controls stream-controls--screenshare"},[n("div",{staticClass:"video-window-buttons-wrap"},[n("div",{staticClass:"stream-controls-wrapper cursor-auto"},[n("div",{staticClass:"toolbar-button-wrapper"},[n("button",{staticClass:"toolbar-button-item cursor-pointer",attrs:{"data-stream-stop-screen-share":"",type:"button"},on:{click:t.toggleScreenShare}},[n("svg",{staticClass:"toolbar-button-icon",attrs:{width:"38",height:"35",viewBox:"0 0 38 35"}},[n("use",{attrs:{"xlink:href":o(875)+"#not_share"}})])]),t._v(" "),n("div",{staticClass:"hover-btn-info"},[t._v("\n                "+t._s(t.$t("stop_screenshare"))+"\n              ")])])])])]):t._e()])])],1)}),[],!1,null,null,null);e.default=component.exports;installComponents(component,{ClassroomContainer:o(1389).default})}}]);