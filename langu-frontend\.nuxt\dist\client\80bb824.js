(window.webpackJsonp=window.webpackJsonp||[]).push([[124,118,119,120],{1324:function(t,e,n){"use strict";var l=n(175);e.a=l.a},1375:function(t,e,n){"use strict";n.r(e);var l={name:"UserSettingTemplate",props:{title:{type:String,required:!0},hideFooter:{type:Boolean,default:!1},customValid:{type:Boolean,default:!0},submitFunc:{type:Function,default:function(){}}},data:function(){return{formValid:!0}},computed:{valid:function(){return this.formValid&&this.customValid}},mounted:function(){this.validate()},methods:{validate:function(){this.$refs.form.validate()},submit:function(){this.valid&&this.submitFunc()}}},r=(n(1419),n(22)),o=n(42),c=n.n(o),d=n(1327),h=n(1363),component=Object(r.a)(l,(function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("v-form",{ref:"form",attrs:{value:t.formValid},on:{validate:t.validate,submit:function(e){return e.preventDefault(),t.submit.apply(null,arguments)},input:function(e){t.formValid=e}}},[l("div",{staticClass:"user-settings-panel"},[l("div",{staticClass:"panel"},[t.$vuetify.breakpoint.smAndUp?l("div",{staticClass:"panel-head d-none d-sm-block"},[l("div",{staticClass:"panel-head-title font-weight-medium mb-2 mb-sm-3 mb-md-5"},[t._v("\n          "+t._s(t.title)+"\n        ")])]):t._e(),t._v(" "),l("div",{staticClass:"panel-body"},[t._t("default")],2),t._v(" "),t.hideFooter?t._e():l("div",{staticClass:"panel-footer d-flex justify-center justify-sm-end"},[l("v-btn",{staticClass:"font-weight-medium",attrs:{color:"primary",type:"submit",disabled:!t.valid}},[l("svg",{staticClass:"mr-1",attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[l("use",{attrs:{"xlink:href":n(91)+"#save-icon"}})]),t._v("\n          "+t._s(t.$t("save_changes"))+"\n        ")])],1)])])])}),[],!1,null,null,null);e.default=component.exports;c()(component,{VBtn:d.a,VForm:h.a})},1380:function(t,e,n){var content=n(1381);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("73707fd0",content,!0,{sourceMap:!1})},1381:function(t,e,n){var l=n(18)(!1);l.push([t.i,".v-item-group{flex:0 1 auto;position:relative;max-width:100%;transition:.3s cubic-bezier(.25,.8,.5,1)}",""]),t.exports=l},1385:function(t,e,n){var content=n(1420);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("419d3f06",content,!0,{sourceMap:!1})},1411:function(t,e,n){"use strict";n.d(e,"a",(function(){return f}));n(7),n(8),n(14),n(15);var l=n(2),r=(n(31),n(9),n(24),n(38),n(126),n(6),n(55),n(71),n(371),n(1380),n(372)),o=n(36),c=n(12),d=n(16);function h(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var f=Object(c.a)(r.a,o.a).extend({name:"base-item-group",props:{activeClass:{type:String,default:"v-item--active"},mandatory:Boolean,max:{type:[Number,String],default:null},multiple:Boolean,tag:{type:String,default:"div"}},data:function(){return{internalLazyValue:void 0!==this.value?this.value:this.multiple?[]:void 0,items:[]}},computed:{classes:function(){return function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?h(Object(source),!0).forEach((function(e){Object(l.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):h(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({"v-item-group":!0},this.themeClasses)},selectedIndex:function(){return this.selectedItem&&this.items.indexOf(this.selectedItem)||-1},selectedItem:function(){if(!this.multiple)return this.selectedItems[0]},selectedItems:function(){var t=this;return this.items.filter((function(e,n){return t.toggleMethod(t.getValue(e,n))}))},selectedValues:function(){return null==this.internalValue?[]:Array.isArray(this.internalValue)?this.internalValue:[this.internalValue]},toggleMethod:function(){var t=this;if(!this.multiple)return function(e){return t.internalValue===e};var e=this.internalValue;return Array.isArray(e)?function(t){return e.includes(t)}:function(){return!1}}},watch:{internalValue:"updateItemsState",items:"updateItemsState"},created:function(){this.multiple&&!Array.isArray(this.internalValue)&&Object(d.c)("Model must be bound to an array if the multiple property is true.",this)},methods:{genData:function(){return{class:this.classes}},getValue:function(t,i){return null==t.value||""===t.value?i:t.value},onClick:function(t){this.updateInternalValue(this.getValue(t,this.items.indexOf(t)))},register:function(t){var e=this,n=this.items.push(t)-1;t.$on("change",(function(){return e.onClick(t)})),this.mandatory&&!this.selectedValues.length&&this.updateMandatory(),this.updateItem(t,n)},unregister:function(t){if(!this._isDestroyed){var e=this.items.indexOf(t),n=this.getValue(t,e);if(this.items.splice(e,1),!(this.selectedValues.indexOf(n)<0)){if(!this.mandatory)return this.updateInternalValue(n);this.multiple&&Array.isArray(this.internalValue)?this.internalValue=this.internalValue.filter((function(t){return t!==n})):this.internalValue=void 0,this.selectedItems.length||this.updateMandatory(!0)}}},updateItem:function(t,e){var n=this.getValue(t,e);t.isActive=this.toggleMethod(n)},updateItemsState:function(){var t=this;this.$nextTick((function(){if(t.mandatory&&!t.selectedItems.length)return t.updateMandatory();t.items.forEach(t.updateItem)}))},updateInternalValue:function(t){this.multiple?this.updateMultiple(t):this.updateSingle(t)},updateMandatory:function(t){if(this.items.length){var e=this.items.slice();t&&e.reverse();var n=e.find((function(t){return!t.disabled}));if(n){var l=this.items.indexOf(n);this.updateInternalValue(this.getValue(n,l))}}},updateMultiple:function(t){var e=(Array.isArray(this.internalValue)?this.internalValue:[]).slice(),n=e.findIndex((function(e){return e===t}));this.mandatory&&n>-1&&e.length-1<1||null!=this.max&&n<0&&e.length+1>this.max||(n>-1?e.splice(n,1):e.push(t),this.internalValue=e)},updateSingle:function(t){var e=t===this.internalValue;this.mandatory&&e||(this.internalValue=e?void 0:t)}},render:function(t){return t(this.tag,this.genData(),this.$slots.default)}});f.extend({name:"v-item-group",provide:function(){return{itemGroup:this}}})},1419:function(t,e,n){"use strict";n(1385)},1420:function(t,e,n){var l=n(18)(!1);l.push([t.i,".user-settings-panel{padding:44px;border-radius:20px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1)}@media only screen and (max-width:1439px){.user-settings-panel{padding:24px}}@media only screen and (max-width:767px){.user-settings-panel{padding:0;box-shadow:none}}.user-settings-panel .row{margin:0 -14px!important}.user-settings-panel .col{padding:0 14px!important}.user-settings-panel .panel{color:var(--v-greyDark-base)}.user-settings-panel .panel-head-title{font-size:24px;line-height:1.333;color:var(--v-darkLight-base)}@media only screen and (max-width:1439px){.user-settings-panel .panel-head-title{font-size:20px}}.user-settings-panel .panel-body .chips>div{margin-top:6px}.user-settings-panel .panel-body .price-input .v-text-field--outlined.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot{min-height:32px!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr>th{color:var(--v-dark-base)}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr:last-child>th{border:none!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:hover:not(.v-data-table__expanded__content):not(.v-data-table__empty-wrapper){background:none}.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tbody>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tfoot>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>thead>tr>td{height:38px;color:var(--v-greyDark-base)}.user-settings-panel .panel-footer{margin-top:115px}@media only screen and (max-width:1439px){.user-settings-panel .panel-footer{margin-top:56px}}.user-settings-panel .panel-footer .v-btn{letter-spacing:.1px}@media only screen and (max-width:479px){.user-settings-panel .panel-footer .v-btn{width:100%!important}}.user-settings-panel .l-checkbox .v-input--selection-controls__input{width:16px;height:16px;margin-top:2px}",""]),t.exports=l},1429:function(t,e,n){"use strict";var l=n(3),r=n(1);e.a=l.default.extend({name:"comparable",props:{valueComparator:{type:Function,default:r.h}}})},1440:function(t,e,n){"use strict";n.r(e);n(31);var l={name:"UserSettingSelect",props:{value:{type:Object,required:!0},items:{type:Array,required:!0},attachId:{type:String,required:!0},itemValue:{type:String,default:"id"},itemText:{type:String,default:"name"},hideDetails:{type:Boolean,default:!0},hideSelected:{type:Boolean,default:!0},rules:{type:Array,default:function(){return[]}},validateOnBlur:{type:Boolean,default:!1},maxHeight:{type:Number,default:162},placeholder:[Boolean,String]},data:function(){return{chevronIcon:"".concat(n(91),"#chevron-down")}},computed:{_placeholder:function(){return this.placeholder?this.$t(this.placeholder):""}}},r=(n(1519),n(22)),o=n(42),c=n.n(o),d=n(261),h=n(1610),component=Object(r.a)(l,(function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("div",{staticClass:"user-setting-select",attrs:{id:t.attachId}},[l("v-select",t._g({attrs:{value:t.value,items:t.items,height:"44","full-width":"",outlined:"",dense:"","hide-selected":t.hideSelected,"hide-details":t.hideDetails,"return-object":"",rules:t.rules,placeholder:t._placeholder,"item-value":t.itemValue,"item-text":t.itemText,attach:"#"+t.attachId,"validate-on-blur":t.validateOnBlur,"menu-props":{bottom:!0,offsetY:!0,minWidth:200,maxHeight:t.maxHeight,nudgeBottom:8,contentClass:"select-list l-scroll"}},scopedSlots:t._u([{key:"append",fn:function(){return[l("svg",{attrs:{width:"12",height:"12",viewBox:"0 0 12 12"}},[l("use",{attrs:{"xlink:href":t.chevronIcon}})])]},proxy:!0},{key:"item",fn:function(e){var r=e.item;return[r.isoCode?l("div",{staticClass:"icon"},[l("v-img",{attrs:{src:n(369)("./"+r.isoCode+".svg"),height:"24",width:"24",eager:""}})],1):t._e(),t._v(" "),l("div",{staticClass:"text",style:{color:"all"===r.id?"#888":"inherit"}},[t._v("\n        "+t._s(r[t.itemText])+"\n      ")])]}}])},t.$listeners))],1)}),[],!1,null,"322ff0d6",null);e.default=component.exports;c()(component,{VImg:d.a,VSelect:h.a})},1448:function(t,e,n){var content=n(1520);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("5bc00c5d",content,!0,{sourceMap:!1})},1458:function(t,e,n){"use strict";n.r(e);n(9),n(24),n(38),n(39);var l={name:"UserSettingAutocomplete",props:{value:{type:Object,default:function(){return{}}},items:{type:Array,required:!0},selectedItems:{type:Array,default:function(){return[]}},attachId:{type:String,required:!0},itemText:{type:String,default:"name"},rules:{type:Array,default:function(){return[]}},hideDetails:{type:Boolean,default:!0},placeholder:[Boolean,String]},data:function(){return{key:1,chevronIcon:"".concat(n(91),"#chevron-down")}},computed:{_placeholder:function(){return this.placeholder?this.$t(this.placeholder||"choose_language"):""},_items:function(){var t=this;return this.selectedItems.length?this.items.filter((function(e){var n,l;return!(null!==(n=null===(l=t.selectedItems)||void 0===l?void 0:l.map((function(t){return t.id})))&&void 0!==n?n:[]).includes(e.id)})):this.items}},methods:{clearSelection:function(){var t=this;this.$nextTick((function(){var e,n,input=null===(e=t.$refs.autocomplete)||void 0===e||null===(n=e.$el)||void 0===n?void 0:n.querySelector("input");input&&(input.setSelectionRange(0,0),input.blur())}))}}},r=(n(1549),n(22)),o=n(42),c=n.n(o),d=n(1612),h=n(261),component=Object(r.a)(l,(function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("div",{key:t.key,staticClass:"user-setting-autocomplete",attrs:{id:t.attachId}},[l("v-autocomplete",t._g({ref:"autocomplete",attrs:{value:t.value,items:t._items,dense:"",filled:"",outlined:"","hide-selected":"","hide-no-data":"","return-object":"","hide-details":t.hideDetails,rules:t.rules,"item-text":t.itemText,placeholder:t._placeholder,attach:"#"+t.attachId,"menu-props":{bottom:!0,offsetY:!0,nudgeBottom:8,contentClass:"select-list l-scroll",maxHeight:205}},on:{focus:t.clearSelection,change:function(e){t.key++}},scopedSlots:t._u([{key:"append",fn:function(){return[l("svg",{attrs:{width:"12",height:"12",viewBox:"0 0 12 12"}},[l("use",{attrs:{"xlink:href":t.chevronIcon}})])]},proxy:!0},{key:"item",fn:function(e){var r=e.item;return[r.isoCode?l("div",{staticClass:"icon"},[l("v-img",{attrs:{src:n(369)("./"+r.isoCode+".svg"),height:"24",width:"24",eager:""}})],1):t._e(),t._v(" "),l("div",{staticClass:"text"},[t._v(t._s(r[t.itemText]))])]}}])},t.$listeners))],1)}),[],!1,null,"53fdd87c",null);e.default=component.exports;c()(component,{VAutocomplete:d.a,VImg:h.a})},1476:function(t,e,n){var content=n(1550);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("61200eaa",content,!0,{sourceMap:!1})},1509:function(t,e,n){var content=n(1510);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("50788f08",content,!0,{sourceMap:!1})},1510:function(t,e,n){var l=n(18)(!1);l.push([t.i,".v-autocomplete.v-input>.v-input__control>.v-input__slot{cursor:text}.v-autocomplete input{align-self:center}.v-autocomplete.v-select.v-input--is-focused input{min-width:64px}.v-autocomplete:not(.v-input--is-focused).v-select--chips input{max-height:0;padding:0}.v-autocomplete--is-selecting-index input{opacity:0}.v-autocomplete.v-text-field--enclosed:not(.v-text-field--solo):not(.v-text-field--single-line):not(.v-text-field--outlined) .v-select__slot>input{margin-top:24px}.v-autocomplete.v-text-field--enclosed:not(.v-text-field--solo):not(.v-text-field--single-line):not(.v-text-field--outlined).v-input--dense .v-select__slot>input{margin-top:20px}.v-autocomplete:not(.v-input--is-disabled).v-select.v-text-field input{pointer-events:inherit}.v-autocomplete__content.v-menu__content,.v-autocomplete__content.v-menu__content .v-card{border-radius:0}",""]),t.exports=l},1513:function(t,e,n){var content=n(1514);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("83ff91dc",content,!0,{sourceMap:!1})},1514:function(t,e,n){var l=n(18)(!1);l.push([t.i,".theme--light.v-file-input .v-file-input__text{color:rgba(0,0,0,.87)}.theme--light.v-file-input .v-file-input__text--placeholder{color:rgba(0,0,0,.6)}.theme--light.v-file-input.v-input--is-disabled .v-file-input__text,.theme--light.v-file-input.v-input--is-disabled .v-file-input__text .v-file-input__text--placeholder{color:rgba(0,0,0,.38)}.theme--dark.v-file-input .v-file-input__text{color:#fff}.theme--dark.v-file-input .v-file-input__text--placeholder{color:hsla(0,0%,100%,.7)}.theme--dark.v-file-input.v-input--is-disabled .v-file-input__text,.theme--dark.v-file-input.v-input--is-disabled .v-file-input__text .v-file-input__text--placeholder{color:hsla(0,0%,100%,.5)}.v-file-input input[type=file]{left:0;opacity:0;pointer-events:none;position:absolute;max-width:0;width:0}.v-file-input .v-file-input__text{align-items:center;align-self:stretch;display:flex;flex-wrap:wrap;width:100%}.v-file-input .v-file-input__text.v-file-input__text--chips{flex-wrap:wrap}.v-file-input .v-file-input__text .v-chip{margin:4px}.v-file-input .v-text-field__slot{min-height:32px}.v-file-input.v-input--dense .v-text-field__slot{min-height:26px}.v-file-input.v-text-field--filled:not(.v-text-field--single-line) .v-file-input__text{padding-top:22px}.v-file-input.v-text-field--outlined .v-text-field__slot{padding:6px 0}.v-file-input.v-text-field--outlined.v-input--dense .v-text-field__slot{padding:3px 0}",""]),t.exports=l},1519:function(t,e,n){"use strict";n(1448)},1520:function(t,e,n){var l=n(18)(!1);l.push([t.i,".user-setting-select[data-v-322ff0d6]{position:relative}",""]),t.exports=l},1549:function(t,e,n){"use strict";n(1476)},1550:function(t,e,n){var l=n(18)(!1);l.push([t.i,".user-setting-autocomplete[data-v-53fdd87c]{position:relative}",""]),t.exports=l},1612:function(t,e,n){"use strict";n(7),n(8),n(14),n(6),n(15);var l=n(2),r=(n(39),n(9),n(96),n(71),n(24),n(38),n(1509),n(1610)),o=n(175),c=n(92),d=n(1);function h(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function f(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?h(Object(source),!0).forEach((function(e){Object(l.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):h(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var m=f(f({},r.b),{},{offsetY:!0,offsetOverflow:!0,transition:!1});e.a=r.a.extend({name:"v-autocomplete",props:{allowOverflow:{type:Boolean,default:!0},autoSelectFirst:{type:Boolean,default:!1},filter:{type:Function,default:function(t,e,n){return n.toLocaleLowerCase().indexOf(e.toLocaleLowerCase())>-1}},hideNoData:Boolean,menuProps:{type:r.a.options.props.menuProps.type,default:function(){return m}},noFilter:Boolean,searchInput:{type:String}},data:function(){return{lazySearch:this.searchInput}},computed:{classes:function(){return f(f({},r.a.options.computed.classes.call(this)),{},{"v-autocomplete":!0,"v-autocomplete--is-selecting-index":this.selectedIndex>-1})},computedItems:function(){return this.filteredItems},selectedValues:function(){var t=this;return this.selectedItems.map((function(e){return t.getValue(e)}))},hasDisplayedItems:function(){var t=this;return this.hideSelected?this.filteredItems.some((function(e){return!t.hasItem(e)})):this.filteredItems.length>0},currentRange:function(){return null==this.selectedItem?0:String(this.getText(this.selectedItem)).length},filteredItems:function(){var t=this;return!this.isSearching||this.noFilter||null==this.internalSearch?this.allItems:this.allItems.filter((function(e){var n=Object(d.m)(e,t.itemText),text=null!=n?String(n):"";return t.filter(e,String(t.internalSearch),text)}))},internalSearch:{get:function(){return this.lazySearch},set:function(t){this.lazySearch=t,this.$emit("update:search-input",t)}},isAnyValueAllowed:function(){return!1},isDirty:function(){return this.searchIsDirty||this.selectedItems.length>0},isSearching:function(){return this.multiple&&this.searchIsDirty||this.searchIsDirty&&this.internalSearch!==this.getText(this.selectedItem)},menuCanShow:function(){return!!this.isFocused&&(this.hasDisplayedItems||!this.hideNoData)},$_menuProps:function(){var t=r.a.options.computed.$_menuProps.call(this);return t.contentClass="v-autocomplete__content ".concat(t.contentClass||"").trim(),f(f({},m),t)},searchIsDirty:function(){return null!=this.internalSearch&&""!==this.internalSearch},selectedItem:function(){var t=this;return this.multiple?null:this.selectedItems.find((function(i){return t.valueComparator(t.getValue(i),t.getValue(t.internalValue))}))},listData:function(){var data=r.a.options.computed.listData.call(this);return data.props=f(f({},data.props),{},{items:this.virtualizedItems,noFilter:this.noFilter||!this.isSearching||!this.filteredItems.length,searchInput:this.internalSearch}),data}},watch:{filteredItems:"onFilteredItemsChanged",internalValue:"setSearch",isFocused:function(t){t?(document.addEventListener("copy",this.onCopy),this.$refs.input&&this.$refs.input.select()):(document.removeEventListener("copy",this.onCopy),this.$refs.input&&this.$refs.input.blur(),this.updateSelf())},isMenuActive:function(t){!t&&this.hasSlot&&(this.lazySearch=null)},items:function(t,e){e&&e.length||!this.hideNoData||!this.isFocused||this.isMenuActive||!t.length||this.activateMenu()},searchInput:function(t){this.lazySearch=t},internalSearch:"onInternalSearchChanged",itemText:"updateSelf"},created:function(){this.setSearch()},destroyed:function(){document.removeEventListener("copy",this.onCopy)},methods:{onFilteredItemsChanged:function(t,e){var n=this;t!==e&&(this.setMenuIndex(-1),this.$nextTick((function(){n.internalSearch&&(1===t.length||n.autoSelectFirst)&&(n.$refs.menu.getTiles(),n.setMenuIndex(0))})))},onInternalSearchChanged:function(){this.updateMenuDimensions()},updateMenuDimensions:function(){this.isMenuActive&&this.$refs.menu&&this.$refs.menu.updateDimensions()},changeSelectedIndex:function(t){this.searchIsDirty||(this.multiple&&t===d.s.left?-1===this.selectedIndex?this.selectedIndex=this.selectedItems.length-1:this.selectedIndex--:this.multiple&&t===d.s.right?this.selectedIndex>=this.selectedItems.length-1?this.selectedIndex=-1:this.selectedIndex++:t!==d.s.backspace&&t!==d.s.delete||this.deleteCurrentItem())},deleteCurrentItem:function(){var t=this.selectedIndex,e=this.selectedItems[t];if(this.isInteractive&&!this.getDisabled(e)){var n=this.selectedItems.length-1;if(-1!==this.selectedIndex||0===n){var l=t!==this.selectedItems.length-1?t:t-1;this.selectedItems[l]?this.selectItem(e):this.setValue(this.multiple?[]:null),this.selectedIndex=l}else this.selectedIndex=n}},clearableCallback:function(){this.internalSearch=null,r.a.options.methods.clearableCallback.call(this)},genInput:function(){var input=o.a.options.methods.genInput.call(this);return input.data=Object(c.a)(input.data,{attrs:{"aria-activedescendant":Object(d.l)(this.$refs.menu,"activeTile.id"),autocomplete:Object(d.l)(input.data,"attrs.autocomplete","off")},domProps:{value:this.internalSearch}}),input},genInputSlot:function(){var slot=r.a.options.methods.genInputSlot.call(this);return slot.data.attrs.role="combobox",slot},genSelections:function(){return this.hasSlot||this.multiple?r.a.options.methods.genSelections.call(this):[]},onClick:function(t){this.isInteractive&&(this.selectedIndex>-1?this.selectedIndex=-1:this.onFocus(),this.isAppendInner(t.target)||this.activateMenu())},onInput:function(t){if(!(this.selectedIndex>-1)&&t.target){var e=t.target,n=e.value;e.value&&this.activateMenu(),this.internalSearch=n,this.badInput=e.validity&&e.validity.badInput}},onKeyDown:function(t){var e=t.keyCode;!t.ctrlKey&&[d.s.home,d.s.end].includes(e)||r.a.options.methods.onKeyDown.call(this,t),this.changeSelectedIndex(e)},onSpaceDown:function(t){},onTabDown:function(t){r.a.options.methods.onTabDown.call(this,t),this.updateSelf()},onUpDown:function(t){t.preventDefault(),this.activateMenu()},selectItem:function(t){r.a.options.methods.selectItem.call(this,t),this.setSearch()},setSelectedItems:function(){r.a.options.methods.setSelectedItems.call(this),this.isFocused||this.setSearch()},setSearch:function(){var t=this;this.$nextTick((function(){t.multiple&&t.internalSearch&&t.isMenuActive||(t.internalSearch=!t.selectedItems.length||t.multiple||t.hasSlot?null:t.getText(t.selectedItem))}))},updateSelf:function(){(this.searchIsDirty||this.internalValue)&&(this.valueComparator(this.internalSearch,this.getValue(this.internalValue))||this.setSearch())},hasItem:function(t){return this.selectedValues.indexOf(this.getValue(t))>-1},onCopy:function(t){var e,n;if(-1!==this.selectedIndex){var l=this.selectedItems[this.selectedIndex],r=this.getText(l);null==(e=t.clipboardData)||e.setData("text/plain",r),null==(n=t.clipboardData)||n.setData("text/vnd.vuetify.autocomplete.item+plain",r),t.preventDefault()}}}})},1614:function(t,e,n){"use strict";n(7),n(8),n(9),n(14),n(15);var l=n(28),r=n(2),o=n(25),c=(n(31),n(24),n(39),n(40),n(23),n(126),n(6),n(55),n(1513),n(1324)),d=n(1563),h=n(1),f=n(16),m=n(92);function v(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function y(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?v(Object(source),!0).forEach((function(e){Object(r.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):v(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}e.a=c.a.extend({name:"v-file-input",model:{prop:"value",event:"change"},props:{chips:Boolean,clearable:{type:Boolean,default:!0},counterSizeString:{type:String,default:"$vuetify.fileInput.counterSize"},counterString:{type:String,default:"$vuetify.fileInput.counter"},hideInput:Boolean,placeholder:String,prependIcon:{type:String,default:"$file"},readonly:{type:Boolean,default:!1},showSize:{type:[Boolean,Number],default:!1,validator:function(t){return"boolean"==typeof t||[1e3,1024].includes(t)}},smallChips:Boolean,truncateLength:{type:[Number,String],default:22},type:{type:String,default:"file"},value:{default:void 0,validator:function(t){return Object(h.y)(t).every((function(t){return null!=t&&"object"===Object(o.a)(t)}))}}},computed:{classes:function(){return y(y({},c.a.options.computed.classes.call(this)),{},{"v-file-input":!0})},computedCounterValue:function(){var t=this.isMultiple&&this.lazyValue?this.lazyValue.length:this.lazyValue instanceof File?1:0;if(!this.showSize)return this.$vuetify.lang.t(this.counterString,t);var e=this.internalArrayValue.reduce((function(t,e){var n=e.size;return t+(void 0===n?0:n)}),0);return this.$vuetify.lang.t(this.counterSizeString,t,Object(h.q)(e,1024===this.base))},internalArrayValue:function(){return Object(h.y)(this.internalValue)},internalValue:{get:function(){return this.lazyValue},set:function(t){this.lazyValue=t,this.$emit("change",this.lazyValue)}},isDirty:function(){return this.internalArrayValue.length>0},isLabelActive:function(){return this.isDirty},isMultiple:function(){return this.$attrs.hasOwnProperty("multiple")},text:function(){var t=this;return this.isDirty||!this.isFocused&&this.hasLabel?this.internalArrayValue.map((function(e){var n=e.name,l=void 0===n?"":n,r=e.size,o=void 0===r?0:r,c=t.truncateText(l);return t.showSize?"".concat(c," (").concat(Object(h.q)(o,1024===t.base),")"):c})):[this.placeholder]},base:function(){return"boolean"!=typeof this.showSize?this.showSize:void 0},hasChips:function(){return this.chips||this.smallChips}},watch:{readonly:{handler:function(t){!0===t&&Object(f.b)("readonly is not supported on <v-file-input>",this)},immediate:!0},value:function(t){var e=this.isMultiple?t:t?[t]:[];Object(h.h)(e,this.$refs.input.files)||(this.$refs.input.value="")}},methods:{clearableCallback:function(){this.internalValue=this.isMultiple?[]:null,this.$refs.input.value=""},genChips:function(){var t=this;return this.isDirty?this.text.map((function(text,e){return t.$createElement(d.a,{props:{small:t.smallChips},on:{"click:close":function(){var n=t.internalValue;n.splice(e,1),t.internalValue=n}}},[text])})):[]},genControl:function(){var t=c.a.options.methods.genControl.call(this);return this.hideInput&&(t.data.style=Object(m.c)(t.data.style,{display:"none"})),t},genInput:function(){var input=c.a.options.methods.genInput.call(this);return delete input.data.domProps.value,delete input.data.on.input,input.data.on.change=this.onInput,[this.genSelections(),input]},genPrependSlot:function(){var t=this;if(!this.prependIcon)return null;var e=this.genIcon("prepend",(function(){t.$refs.input.click()}));return this.genSlot("prepend","outer",[e])},genSelectionText:function(){var t=this.text.length;return t<2?this.text:this.showSize&&!this.counter?[this.computedCounterValue]:[this.$vuetify.lang.t(this.counterString,t)]},genSelections:function(){var t=this,e=[];return this.isDirty&&this.$scopedSlots.selection?this.internalArrayValue.forEach((function(n,l){t.$scopedSlots.selection&&e.push(t.$scopedSlots.selection({text:t.text[l],file:n,index:l}))})):e.push(this.hasChips&&this.isDirty?this.genChips():this.genSelectionText()),this.$createElement("div",{staticClass:"v-file-input__text",class:{"v-file-input__text--placeholder":this.placeholder&&!this.isDirty,"v-file-input__text--chips":this.hasChips&&!this.$scopedSlots.selection}},e)},genTextFieldSlot:function(){var t=this,e=c.a.options.methods.genTextFieldSlot.call(this);return e.data.on=y(y({},e.data.on||{}),{},{click:function(){return t.$refs.input.click()}}),e},onInput:function(t){var e=Object(l.a)(t.target.files||[]);this.internalValue=this.isMultiple?e:e[0],this.initialValue=this.internalValue},onKeyDown:function(t){this.$emit("keydown",t)},truncateText:function(t){if(t.length<Number(this.truncateLength))return t;var e=Math.floor((Number(this.truncateLength)-1)/2);return"".concat(t.slice(0,e),"…").concat(t.slice(t.length-e))}}})},1637:function(t,e,n){var content=n(1740);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("a97915c6",content,!0,{sourceMap:!1})},1739:function(t,e,n){"use strict";n(1637)},1740:function(t,e,n){var l=n(18)(!1);l.push([t.i,".user-settings-avatar .v-avatar{cursor:pointer}.user-settings-email,.user-settings-nickname{position:relative;padding-left:32px}.user-settings-email svg,.user-settings-nickname svg{position:absolute;left:0;top:2px}.user-settings-email a,.user-settings-nickname a{color:inherit!important;text-decoration:none;transition:color .3s}.user-settings-email a:hover,.user-settings-nickname a:hover{color:var(--v-orange-base)!important}@media only screen and (min-width:768px){.user-settings-email{margin-bottom:12px}}.user-settings-nickname .input-wrap-notice{font-size:12px!important}",""]),t.exports=l},1923:function(t,e,n){"use strict";n.r(e);n(7),n(8),n(9),n(14),n(6),n(15);var l=n(2),r=(n(39),n(40),n(71),n(23),n(1375)),o=n(1440),c=n(1458),d=n(370);function h(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function f(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?h(Object(source),!0).forEach((function(e){Object(l.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):h(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}var m={name:"UserSettingBasic",components:{UserSettingTemplate:r.default,UserSettingSelect:o.default,UserSettingAutocomplete:c.default,TextInput:d.default},props:{basicInfoItem:{type:Object,required:!0}},data:function(){return{domain:"'http://localhost:3000'",file:null,fileValid:!0,fileSizeLimit:6e6,rules:{required:function(t){return!!t&&t.length>1}}}},computed:{taxCountries:function(){return this.basicInfoItem.taxCountries.map((function(t){return t.taxCountry}))},locales:function(){var t=this;return this.$store.state.locales.map((function(e){return f(f({},e),{},{name:t.$t(e.name)})}))},timeZones:function(){return this.basicInfoItem.timezones},selectedUiLocal:function(){var t=this;return this.locales.find((function(e){return e.code===t.basicInfoItem.uiLanguage}))},selectedTimeZone:function(){var t=this;return this.timeZones.find((function(e){return e.name===t.basicInfoItem.timezone}))},isTeacher:function(){return this.$store.getters["user/isTeacher"]},userLink:function(){return"".concat(this.domain,"/teacher/").concat(this.basicInfoItem.username)}},watch:{$route:function(){this.file=null,this.fileValid=!0}},methods:{getSrcAvatar:function(t,e){var l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"avatar.png";return null!=t&&t[e]?t[e]:n(881)("./".concat(l))},getSrcSetAvatar:function(t,e,n){return null!=t&&t[e]&&null!=t&&t[n]?"\n            ".concat(t[e]," 1x,\n            ").concat(t[n]," 2x,\n          "):""},updateTimezone:function(t){null!=t&&t.name&&this.updateValue(t.name,"timezone")},updateValue:function(t,e){this.$store.commit("settings/UPDATE_BASIC_INFO_ITEM",Object(l.a)({},e,t))},updateAvatar:function(t){this.fileValid=!0,t.size>this.fileSizeLimit?this.fileValid=!1:this.$store.dispatch("settings/updateAvatar",t)},copyLink:function(){try{var t=this.$refs.profileLink;t.setAttribute("value",this.userLink),t.select(),t.setSelectionRange(0,99999),navigator.clipboard.writeText(t.value),this.$store.dispatch("snackbar/success",{successMessage:"link_copied",timeout:1500})}catch(t){console.log(t)}},submitData:function(){var t=this;this.$store.dispatch("settings/updateBasicInfo").then((function(){t.$store.dispatch("loadingStart"),t.$emit("reload-page")}))}}},v=(n(1739),n(22)),y=n(42),_=n.n(y),x=n(1343),I=n(1360),S=n(1614),w=n(261),O=n(1361),component=Object(v.a)(m,(function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("user-setting-template",{attrs:{title:t.$t("basic_info"),"submit-func":t.submitData}},[l("div",{staticClass:"mb-3"},[l("v-row",[l("v-col",{staticClass:"col-12 col-md-7 mb-3 mb-sm-2"},[l("div",{staticClass:"user-settings-avatar"},[l("div",{staticClass:"d-flex flex-column flex-sm-row align-center justify-center justify-sm-start"},[l("v-avatar",{staticClass:"mb-1 mb-sm-0 mr-sm-2",attrs:{size:"124px"},on:{click:function(e){t.$refs.fileAvatar.$el.querySelector("input").click()}}},[l("v-img",{attrs:{src:t.getSrcAvatar(t.basicInfoItem.avatars,"user_thumb_124x124"),srcset:t.getSrcSetAvatar(t.basicInfoItem.avatars,"user_thumb_124x124","user_thumb_248x248"),options:{rootMargin:"50%"}}})],1),t._v(" "),l("div",{staticClass:"text-center text-sm-left body-2"},[t._v("\n              "+t._s(t.$t("click_photo_to_upload_new_one"))+"\n            ")])],1),t._v(" "),l("div",{staticClass:"input-wrap"},[l("v-file-input",{ref:"fileAvatar",staticClass:"l-file-input pt-0",attrs:{accept:"image/png, image/jpeg, image/bmp","prepend-icon":"","hide-input":""},on:{change:t.updateAvatar},model:{value:t.file,callback:function(e){t.file=e},expression:"file"}}),t._v(" "),t.fileValid?t._e():l("div",{staticClass:"v-text-field__details"},[l("div",{staticClass:"input-wrap-error"},[l("div",{staticClass:"v-messages theme--light error--text",attrs:{role:"alert"}},[l("div",{staticClass:"v-messages__wrapper"},[l("div",{staticClass:"v-messages__message"},[t._v("\n                      "+t._s(t.$t("file_size_should_be_less_than",{value:"6 MB"}))+"\n                    ")])])])])])],1)])]),t._v(" "),l("v-col",{staticClass:"col-12 d-flex flex-column-reverse flex-sm-column justify-center align-start"},[l("div",{staticClass:"user-settings-email body-1"},[l("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 20 20"}},[l("use",{attrs:{"xlink:href":n(91)+"#email-icon"}})]),t._v("\n          "+t._s(t.basicInfoItem.email)+"\n        ")]),t._v(" "),t.isTeacher?l("div",{staticClass:"user-settings-nickname body-1 mb-1 mb-sm-0"},[l("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 20 20"}},[l("use",{attrs:{"xlink:href":n(91)+"#user-icon"}})]),t._v(" "),t.$vuetify.breakpoint.smAndUp?[l("nuxt-link",{attrs:{to:{path:"/teacher/"+t.basicInfoItem.username},target:"_blank"}},[t._v("\n              "+t._s(t.userLink)+"\n            ")]),t._v(" "),l("div",{staticClass:"input-wrap-notice text--gradient"},[t._v("\n              "+t._s(t.$t("here_is_link_to_your_profile_feel_free_to_share"))+"\n            ")])]:[l("div",{staticClass:"d-flex align-center"},[l("div",[t._v(t._s(t.basicInfoItem.username))]),t._v(" "),l("div",{staticClass:"d-flex align-center text--gradient ml-2"},[l("v-img",{staticClass:"mr-1",attrs:{src:n(938),width:"16",height:"16"}}),t._v(" "),l("input",{ref:"profileLink",staticClass:"d-none",attrs:{type:"text"}}),t._v(" "),l("div",{on:{click:t.copyLink}},[t._v("\n                  "+t._s(t.$t("copy_link"))+"\n                ")])],1)])]],2):t._e()])],1)],1),t._v(" "),l("v-row",[l("v-col",{staticClass:"col-12 col-sm-6 mb-2 mb-md-4"},[l("text-input",{attrs:{value:t.basicInfoItem.firstName,"type-class":"border-gradient",height:"44","hide-details":"",rules:[t.rules.required],label:t.$t("first_name"),autocomplete:"given-name"},on:{input:function(e){return t.updateValue(e,"firstName")}}})],1),t._v(" "),l("v-col",{staticClass:"col-12 col-sm-6 mb-2 mb-md-4"},[l("text-input",{attrs:{value:t.basicInfoItem.lastName,"type-class":"border-gradient",height:"44","hide-details":"",rules:[t.rules.required],label:t.$t("last_name"),autocomplete:"family-name"},on:{input:function(e){return t.updateValue(e,"lastName")}}})],1)],1),t._v(" "),l("v-row",[l("v-col",{staticClass:"col-12 col-sm-6 d-flex align-end mb-2 mb-md-4"},[l("div",{staticClass:"input-wrap"},[l("div",{staticClass:"input-wrap-label"},[t._v("\n          "+t._s(t.$t("please_choose_your_website_language"))+"\n        ")]),t._v(" "),l("user-setting-select",{attrs:{value:t.selectedUiLocal,items:t.locales,"attach-id":"website-language"},on:{change:function(e){return t.updateValue(e.code,"uiLanguage")}}})],1)])],1),t._v(" "),l("v-row",[l("v-col",{staticClass:"col-12 col-sm-6 mb-2 mb-sm-0 d-flex align-end"},[l("div",{staticClass:"input-wrap"},[l("div",{staticClass:"input-wrap-label"},[t._v("\n          "+t._s(t.$t("select_desired_local_time_zone"))+"\n        ")]),t._v(" "),l("user-setting-autocomplete",{attrs:{value:t.selectedTimeZone,items:t.timeZones,"attach-id":"time-zone","item-text":"gmt",placeholder:!1},on:{change:t.updateTimezone}})],1)]),t._v(" "),t.isTeacher?l("v-col",{staticClass:"col-12 col-sm-6 d-flex align-end"},[l("div",{staticClass:"input-wrap"},[l("div",{staticClass:"input-wrap-label"},[t._v("\n          "+t._s(t.$t("in_which_country_do_you_normally_pay_taxes"))+"\n        ")]),t._v(" "),l("user-setting-autocomplete",{attrs:{value:t.basicInfoItem.taxCountry,items:t.taxCountries,"attach-id":"country-pay-taxes"},on:{change:function(e){return t.updateValue(e,"taxCountry")}}})],1)]):t._e()],1)],1)}),[],!1,null,null,null);e.default=component.exports;_()(component,{UserSettingSelect:n(1440).default,UserSettingAutocomplete:n(1458).default,UserSettingTemplate:n(1375).default}),_()(component,{VAvatar:x.a,VCol:I.a,VFileInput:S.a,VImg:w.a,VRow:O.a})}}]);