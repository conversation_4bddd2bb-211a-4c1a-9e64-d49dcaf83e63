exports.ids = [43];
exports.modules = {

/***/ 1258:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1349);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("368f37aa", content, true, context)
};

/***/ }),

/***/ 1348:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LanguagesSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1258);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LanguagesSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LanguagesSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LanguagesSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LanguagesSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1349:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".home-page .languages{padding:90px 0 210px}@media only screen and (max-width:1439px){.home-page .languages{padding-bottom:155px}}@media only screen and (max-width:991px){.home-page .languages{padding-bottom:120px}}@media only screen and (max-width:767px){.home-page .languages{padding:30px 0}}@media only screen and (max-width:479px){.home-page .languages{padding:0 0 30px}}.home-page .languages-content{position:relative;margin-top:136px}@media only screen and (max-width:1439px){.home-page .languages-content{margin-top:96px}}@media only screen and (max-width:991px){.home-page .languages-content{margin-top:75px}}@media only screen and (max-width:767px){.home-page .languages-content{padding-left:28%}}@media only screen and (max-width:479px){.home-page .languages-content{margin-top:50px}}.home-page .languages-content-image{position:absolute;left:50%;max-width:932px;transform:translateX(-50%)}@media only screen and (max-width:1439px){.home-page .languages-content-image{max-width:760px}}@media only screen and (max-width:991px){.home-page .languages-content-image{max-width:550px}}@media only screen and (max-width:767px){.home-page .languages-content-image{position:relative;width:100%;height:0;left:auto;right:0;padding-bottom:168%;transform:none}.home-page .languages-content-image>.v-image{position:absolute;top:0;right:0;width:100%;height:100%}}.home-page .languages-lines-mobile{position:absolute;top:0;left:0;width:100%;height:100%}.home-page .languages-line{display:flex;justify-content:center;width:100%}@media only screen and (min-width:768px){.home-page .languages-line{position:relative}}.home-page .languages-line-r1>div{padding:0 45px}@media only screen and (max-width:1439px){.home-page .languages-line-r1>div{padding:0 28px}}@media only screen and (max-width:991px){.home-page .languages-line-r1>div{padding:0 14px}}@media only screen and (max-width:767px){.home-page .languages-line-r1>div{padding:0}}.home-page .languages-country{position:relative;display:flex;align-items:center;line-height:1.3}@media only screen and (max-width:767px){.home-page .languages-country{position:absolute;margin:0!important}}.home-page .languages-country-flag{display:flex;align-items:center;width:32px;margin-right:16px}@media only screen and (max-width:991px){.home-page .languages-country-flag{width:24px;margin-right:12px}}@media only screen and (max-width:479px){.home-page .languages-country-flag{margin-right:6px}}.home-page .languages-country-flag--double{width:49px}@media only screen and (max-width:991px){.home-page .languages-country-flag--double{width:39px}}.home-page .languages-country a{font-size:24px;font-weight:600;color:var(--v-darkLight-base)!important;text-decoration:none;transition:color .2s}@media only screen and (max-width:1439px){.home-page .languages-country a{font-size:18px}}@media only screen and (max-width:991px){.home-page .languages-country a{font-size:16px}}@media only screen and (max-width:767px){.home-page .languages-country a{white-space:nowrap}}.home-page .languages-country a:hover{color:var(--v-orange-base)!important}.home-page .languages-country-uk{top:-28px}@media only screen and (max-width:1439px){.home-page .languages-country-uk{top:-22px}}@media only screen and (max-width:767px){.home-page .languages-country-uk{top:auto;bottom:84%;right:60%;left:auto}}@media only screen and (max-width:479px){.home-page .languages-country-uk{right:65%}}@media only screen and (max-width:767px){.home-page .languages-country-fr{bottom:73%;right:79%;left:auto}}@media only screen and (max-width:767px){.home-page .languages-country-pl{bottom:100%;right:88%;left:auto}}.home-page .languages-country-it{margin-top:80px;left:-322px}@media only screen and (max-width:1439px){.home-page .languages-country-it{margin-top:55px;left:-266px}}@media only screen and (max-width:991px){.home-page .languages-country-it{margin-top:46px;left:-192px}}@media only screen and (max-width:767px){.home-page .languages-country-it{bottom:43%;right:69%;left:auto}}.home-page .languages-country-ar{margin-top:50px;left:408px}@media only screen and (max-width:1439px){.home-page .languages-country-ar{margin-top:16px;left:318px}}@media only screen and (max-width:991px){.home-page .languages-country-ar{margin-top:12px;left:238px}}@media only screen and (max-width:767px){.home-page .languages-country-ar{bottom:62%;right:68%;left:auto}}.home-page .languages-country-ge{margin-top:90px;left:-382px}@media only screen and (max-width:1439px){.home-page .languages-country-ge{margin-top:68px;left:-260px}}@media only screen and (max-width:991px){.home-page .languages-country-ge{margin-top:42px;left:-190px}}@media only screen and (max-width:767px){.home-page .languages-country-ge{bottom:12%;left:auto;right:38%}}@media only screen and (max-width:479px){.home-page .languages-country-ge{bottom:10%}}.home-page .languages-country-pr-br{margin-top:70px;left:435px}@media only screen and (max-width:1439px){.home-page .languages-country-pr-br{margin-top:50px;left:308px}}@media only screen and (max-width:991px){.home-page .languages-country-pr-br{margin-top:28px;left:245px}}@media only screen and (max-width:767px){.home-page .languages-country-pr-br{bottom:17%;right:68%;left:auto}}.home-page .languages-country-ru{margin-top:106px;left:-444px}@media only screen and (max-width:1439px){.home-page .languages-country-ru{margin-top:55px;left:-320px}}@media only screen and (max-width:991px){.home-page .languages-country-ru{margin-top:55px;left:-260px}}@media only screen and (max-width:767px){.home-page .languages-country-ru{bottom:18%;left:auto;right:6%}}.home-page .languages-country-jp{margin-top:95px;left:466px}@media only screen and (max-width:1439px){.home-page .languages-country-jp{margin-top:66px;left:352px}}@media only screen and (max-width:991px){.home-page .languages-country-jp{margin-top:40px;left:268px}}@media only screen and (max-width:767px){.home-page .languages-country-jp{bottom:31%;right:82%;left:auto}}.home-page .languages-country-sp{margin-top:140px;left:-415px}@media only screen and (max-width:1439px){.home-page .languages-country-sp{margin-top:135px;left:-330px}}@media only screen and (max-width:991px){.home-page .languages-country-sp{margin-top:80px;left:-245px}}@media only screen and (max-width:767px){.home-page .languages-country-sp{bottom:52%;right:85%;left:auto}}.home-page .languages-country-ch{margin-top:15px;left:415px}@media only screen and (max-width:1439px){.home-page .languages-country-ch{margin-top:12px;left:324px}}@media only screen and (max-width:991px){.home-page .languages-country-ch{margin-top:7px;left:248px}}@media only screen and (max-width:767px){.home-page .languages-country-ch{bottom:95%;right:46%;left:auto}}.home-page .languages-country-du{margin-top:60px;left:-260px}@media only screen and (max-width:1439px){.home-page .languages-country-du{margin-top:28px;left:-206px}}@media only screen and (max-width:991px){.home-page .languages-country-du{margin-top:22px;left:-145px}}@media only screen and (max-width:767px){.home-page .languages-country-du{bottom:28%;right:40%;left:auto}}@media only screen and (max-width:479px){.home-page .languages-country-du{bottom:26%;right:28%}}.home-page .languages-country-sw{margin-top:54px;left:290px}@media only screen and (max-width:1439px){.home-page .languages-country-sw{margin-top:42px;left:235px}}@media only screen and (max-width:991px){.home-page .languages-country-sw{margin-top:32px;left:170px}}@media only screen and (max-width:767px){.home-page .languages-country-sw{top:-3%;right:12%;left:auto}}@media only screen and (max-width:479px){.home-page .languages-country-sw{top:-6%;right:2%}}@media only screen and (min-width:1440px){.es.home-page .languages-country-uk{top:-50px}.es.home-page .languages-country-jp{margin-top:20px}.es.home-page .languages-country-pr-br{margin-top:35px}}@media only screen and (min-width:992px)and (max-width:1439px){.es.home-page .languages-country-uk{top:-40px}.es.home-page .languages-country-ge{margin-top:35px}.es.home-page .languages-country-jp{margin-top:50px}}@media only screen and (min-width:768px)and (max-width:991px){.es.home-page .languages-country-uk{top:-40px}.es.home-page .languages-country-ge{margin-top:26px}.es.home-page .languages-country-jp{margin-top:35px}.es.home-page .languages-country-pr-br{margin-top:12px}}@media only screen and (max-width:479px){.es.home-page .languages-country-uk{right:46%}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1414:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/LanguagesSection.vue?vue&type=template&id=19955d52&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"languages"},[_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{staticClass:"section-head-title",staticStyle:{"color":"#262626","-webkit-text-fill-color":"#262626"}},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.languages_section_title'))+"\n          ")])])])],1)],1),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-content\">","</div>",[_vm._ssrNode("<div class=\"languages-content-image\">","</div>",[_c('v-img',{staticClass:"d-none d-sm-block",attrs:{"src":__webpack_require__(140),"contain":"","options":{ rootMargin: '50%' }}}),_vm._ssrNode(" "),_c('v-img',{staticClass:"d-sm-none",attrs:{"src":__webpack_require__(139),"contain":"","options":{ rootMargin: '50%' }}}),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-lines-mobile d-sm-none\">","</div>",[_vm._ssrNode("<div class=\"languages-line languages-line-r1\">","</div>",[_vm._ssrNode("<div class=\"languages-country languages-country-pl\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(113),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,32"}},[_vm._v("\n              "+_vm._s(_vm.$t('polish'))+"\n            ")])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-uk\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag languages-country-flag--double\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(118),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,12"}},[_c('span',{domProps:{"innerHTML":_vm._s(_vm.$t('uk_usa_english'))}})])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-fr\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(110),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,16"}},[_vm._v("\n              "+_vm._s(_vm.$t('french'))+"\n            ")])],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-line languages-line-r2\">","</div>",[_vm._ssrNode("<div class=\"languages-country languages-country-it\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(111),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,23"}},[_vm._v("\n              "+_vm._s(_vm.$t('italian'))+"\n            ")])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-ar\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(106),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,2"}},[_vm._v("\n              "+_vm._s(_vm.$t('arabic'))+"\n            ")])],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-line languages-line-r3\">","</div>",[_vm._ssrNode("<div class=\"languages-country languages-country-ge\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(108),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,17"}},[_vm._v("\n              "+_vm._s(_vm.$t('germany'))+"\n            ")])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-pr-br\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag languages-country-flag--double\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(114),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,34"}},[_c('span',{domProps:{"innerHTML":_vm._s(_vm.$t('portuguese_brazilian'))}})])],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-line languages-line-r4\">","</div>",[_vm._ssrNode("<div class=\"languages-country languages-country-ru\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(115),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,38"}},[_vm._v("\n              "+_vm._s(_vm.$t('russian'))+"\n            ")])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-jp\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(112),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,24"}},[_vm._v("\n              "+_vm._s(_vm.$t('japanese'))+"\n            ")])],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-line languages-line-r5\">","</div>",[_vm._ssrNode("<div class=\"languages-country languages-country-sp\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(116),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,44"}},[_vm._v("\n              "+_vm._s(_vm.$t('spanish'))+"\n            ")])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-ch\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(107),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,7"}},[_vm._v("\n              "+_vm._s(_vm.$t('chinese'))+"\n            ")])],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-line languages-line-r6\">","</div>",[_vm._ssrNode("<div class=\"languages-country languages-country-du\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(109),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,11"}},[_vm._v("\n              "+_vm._s(_vm.$t('dutch'))+"\n            ")])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-sw\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(117),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,45"}},[_vm._v("\n              "+_vm._s(_vm.$t('swedish'))+"\n            ")])],2)],2)],2)],2),_vm._ssrNode(" "),_c('v-container',{staticClass:"py-0 d-none d-sm-block"},[_c('v-row',[_c('v-col',{staticClass:"col-xl-10 offset-xl-1 py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"languages-line languages-line-r1"},[_c('div',{staticClass:"languages-country languages-country-pl"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(113),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,32"}},[_vm._v("\n                    "+_vm._s(_vm.$t('polish'))+"\n                  ")])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-uk"},[_c('div',{staticClass:"languages-country-flag languages-country-flag--double"},[_c('v-img',{attrs:{"src":__webpack_require__(118),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,12"}},[_c('span',{domProps:{"innerHTML":_vm._s(_vm.$t('uk_usa_english'))}})])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-fr"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(110),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,16"}},[_vm._v("\n                    "+_vm._s(_vm.$t('french'))+"\n                  ")])],1)]),_vm._v(" "),_c('div',{staticClass:"languages-line languages-line-r2"},[_c('div',{staticClass:"languages-country languages-country-it"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(111),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,23"}},[_vm._v("\n                    "+_vm._s(_vm.$t('italian'))+"\n                  ")])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-ar"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(106),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,2"}},[_vm._v("\n                    "+_vm._s(_vm.$t('arabic'))+"\n                  ")])],1)]),_vm._v(" "),_c('div',{staticClass:"languages-line languages-line-r3"},[_c('div',{staticClass:"languages-country languages-country-ge"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(108),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,17"}},[_vm._v("\n                    "+_vm._s(_vm.$t('germany'))+"\n                  ")])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-pr-br"},[_c('div',{staticClass:"languages-country-flag languages-country-flag--double"},[_c('v-img',{attrs:{"src":__webpack_require__(114),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,34"}},[_c('span',{domProps:{"innerHTML":_vm._s(_vm.$t('portuguese_brazilian'))}})])],1)]),_vm._v(" "),_c('div',{staticClass:"languages-line languages-line-r4"},[_c('div',{staticClass:"languages-country languages-country-ru"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(115),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,38"}},[_vm._v("\n                    "+_vm._s(_vm.$t('russian'))+"\n                  ")])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-jp"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(112),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,24"}},[_vm._v("\n                    "+_vm._s(_vm.$t('japanese'))+"\n                  ")])],1)]),_vm._v(" "),_c('div',{staticClass:"languages-line languages-line-r5"},[_c('div',{staticClass:"languages-country languages-country-sp"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(116),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,44"}},[_vm._v("\n                    "+_vm._s(_vm.$t('spanish'))+"\n                  ")])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-ch"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(107),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,7"}},[_vm._v("\n                    "+_vm._s(_vm.$t('chinese'))+"\n                  ")])],1)]),_vm._v(" "),_c('div',{staticClass:"languages-line languages-line-r6"},[_c('div',{staticClass:"languages-country languages-country-du"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(109),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,11"}},[_vm._v("\n                    "+_vm._s(_vm.$t('dutch'))+"\n                  ")])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-sw"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(117),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,45"}},[_vm._v("\n                    "+_vm._s(_vm.$t('swedish'))+"\n                  ")])],1)])])],1)],1)],1)],1)],2)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/LanguagesSection.vue?vue&type=template&id=19955d52&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/LanguagesSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var LanguagesSectionvue_type_script_lang_js_ = ({
  name: 'LanguagesSection'
});
// CONCATENATED MODULE: ./components/homepage/LanguagesSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_LanguagesSectionvue_type_script_lang_js_ = (LanguagesSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/LanguagesSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1348)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_LanguagesSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "6831a660"
  
)

/* harmony default export */ var LanguagesSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */





installComponents_default()(component, {VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ })

};;
//# sourceMappingURL=homepage-languages-section.js.map