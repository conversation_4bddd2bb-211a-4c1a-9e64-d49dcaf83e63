(window.webpackJsonp=window.webpackJsonp||[]).push([[125,120],{1375:function(t,e,n){"use strict";n.r(e);var o={name:"UserSettingTemplate",props:{title:{type:String,required:!0},hideFooter:{type:Boolean,default:!1},customValid:{type:Boolean,default:!0},submitFunc:{type:Function,default:function(){}}},data:function(){return{formValid:!0}},computed:{valid:function(){return this.formValid&&this.customValid}},mounted:function(){this.validate()},methods:{validate:function(){this.$refs.form.validate()},submit:function(){this.valid&&this.submitFunc()}}},l=(n(1419),n(22)),r=n(42),c=n.n(r),d=n(1327),h=n(1363),component=Object(l.a)(o,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("v-form",{ref:"form",attrs:{value:t.formValid},on:{validate:t.validate,submit:function(e){return e.preventDefault(),t.submit.apply(null,arguments)},input:function(e){t.formValid=e}}},[o("div",{staticClass:"user-settings-panel"},[o("div",{staticClass:"panel"},[t.$vuetify.breakpoint.smAndUp?o("div",{staticClass:"panel-head d-none d-sm-block"},[o("div",{staticClass:"panel-head-title font-weight-medium mb-2 mb-sm-3 mb-md-5"},[t._v("\n          "+t._s(t.title)+"\n        ")])]):t._e(),t._v(" "),o("div",{staticClass:"panel-body"},[t._t("default")],2),t._v(" "),t.hideFooter?t._e():o("div",{staticClass:"panel-footer d-flex justify-center justify-sm-end"},[o("v-btn",{staticClass:"font-weight-medium",attrs:{color:"primary",type:"submit",disabled:!t.valid}},[o("svg",{staticClass:"mr-1",attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[o("use",{attrs:{"xlink:href":n(91)+"#save-icon"}})]),t._v("\n          "+t._s(t.$t("save_changes"))+"\n        ")])],1)])])])}),[],!1,null,null,null);e.default=component.exports;c()(component,{VBtn:d.a,VForm:h.a})},1385:function(t,e,n){var content=n(1420);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("419d3f06",content,!0,{sourceMap:!1})},1419:function(t,e,n){"use strict";n(1385)},1420:function(t,e,n){var o=n(18)(!1);o.push([t.i,".user-settings-panel{padding:44px;border-radius:20px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1)}@media only screen and (max-width:1439px){.user-settings-panel{padding:24px}}@media only screen and (max-width:767px){.user-settings-panel{padding:0;box-shadow:none}}.user-settings-panel .row{margin:0 -14px!important}.user-settings-panel .col{padding:0 14px!important}.user-settings-panel .panel{color:var(--v-greyDark-base)}.user-settings-panel .panel-head-title{font-size:24px;line-height:1.333;color:var(--v-darkLight-base)}@media only screen and (max-width:1439px){.user-settings-panel .panel-head-title{font-size:20px}}.user-settings-panel .panel-body .chips>div{margin-top:6px}.user-settings-panel .panel-body .price-input .v-text-field--outlined.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot{min-height:32px!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr>th{color:var(--v-dark-base)}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr:last-child>th{border:none!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:hover:not(.v-data-table__expanded__content):not(.v-data-table__empty-wrapper){background:none}.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tbody>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tfoot>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>thead>tr>td{height:38px;color:var(--v-greyDark-base)}.user-settings-panel .panel-footer{margin-top:115px}@media only screen and (max-width:1439px){.user-settings-panel .panel-footer{margin-top:56px}}.user-settings-panel .panel-footer .v-btn{letter-spacing:.1px}@media only screen and (max-width:479px){.user-settings-panel .panel-footer .v-btn{width:100%!important}}.user-settings-panel .l-checkbox .v-input--selection-controls__input{width:16px;height:16px;margin-top:2px}",""]),t.exports=o},1429:function(t,e,n){"use strict";var o=n(3),l=n(1);e.a=o.default.extend({name:"comparable",props:{valueComparator:{type:Function,default:l.h}}})},1479:function(t,e,n){var content=n(1564);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("2e2bc7da",content,!0,{sourceMap:!1})},1480:function(t,e,n){"use strict";n.d(e,"b",(function(){return d}));n(20),n(80),n(9);var o=n(117),l=n(1556),r=n(1429),c=n(12);function d(t){t.preventDefault()}e.a=Object(c.a)(o.a,l.a,r.a).extend({name:"selectable",model:{prop:"inputValue",event:"change"},props:{id:String,inputValue:null,falseValue:null,trueValue:null,multiple:{type:Boolean,default:null},label:String},data:function(){return{hasColor:this.inputValue,lazyValue:this.inputValue}},computed:{computedColor:function(){if(this.isActive)return this.color?this.color:this.isDark&&!this.appIsDark?"white":"primary"},isMultiple:function(){return!0===this.multiple||null===this.multiple&&Array.isArray(this.internalValue)},isActive:function(){var t=this,e=this.value,input=this.internalValue;return this.isMultiple?!!Array.isArray(input)&&input.some((function(n){return t.valueComparator(n,e)})):void 0===this.trueValue||void 0===this.falseValue?e?this.valueComparator(e,input):Boolean(input):this.valueComparator(input,this.trueValue)},isDirty:function(){return this.isActive},rippleState:function(){return this.isDisabled||this.validationState?this.validationState:void 0}},watch:{inputValue:function(t){this.lazyValue=t,this.hasColor=t}},methods:{genLabel:function(){var label=o.a.options.methods.genLabel.call(this);return label?(label.data.on={click:d},label):label},genInput:function(t,e){return this.$createElement("input",{attrs:Object.assign({"aria-checked":this.isActive.toString(),disabled:this.isDisabled,id:this.computedId,role:t,type:t},e),domProps:{value:this.value,checked:this.isActive},on:{blur:this.onBlur,change:this.onChange,focus:this.onFocus,keydown:this.onKeydown,click:d},ref:"input"})},onBlur:function(){this.isFocused=!1},onClick:function(t){this.onChange(),this.$emit("click",t)},onChange:function(){var t=this;if(this.isInteractive){var e=this.value,input=this.internalValue;if(this.isMultiple){Array.isArray(input)||(input=[]);var n=input.length;(input=input.filter((function(n){return!t.valueComparator(n,e)}))).length===n&&input.push(e)}else input=void 0!==this.trueValue&&void 0!==this.falseValue?this.valueComparator(input,this.trueValue)?this.falseValue:this.trueValue:e?this.valueComparator(input,e)?null:e:!input;this.validate(!0,input),this.internalValue=input,this.hasColor=input}},onFocus:function(){this.isFocused=!0},onKeydown:function(t){}}})},1556:function(t,e,n){"use strict";var o=n(127),l=n(3);e.a=l.default.extend({name:"rippleable",directives:{ripple:o.a},props:{ripple:{type:[Boolean,Object],default:!0}},methods:{genRipple:function(){var data=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.ripple?(data.staticClass="v-input--selection-controls__ripple",data.directives=data.directives||[],data.directives.push({name:"ripple",value:{center:!0}}),this.$createElement("div",data)):null}}})},1564:function(t,e,n){var o=n(18)(!1);o.push([t.i,'.theme--light.v-input--selection-controls.v-input--is-disabled:not(.v-input--indeterminate) .v-icon{color:rgba(0,0,0,.26)!important}.theme--dark.v-input--selection-controls.v-input--is-disabled:not(.v-input--indeterminate) .v-icon{color:hsla(0,0%,100%,.3)!important}.v-input--selection-controls{margin-top:16px;padding-top:4px}.v-input--selection-controls>.v-input__append-outer,.v-input--selection-controls>.v-input__prepend-outer{margin-top:0;margin-bottom:0}.v-input--selection-controls:not(.v-input--hide-details)>.v-input__slot{margin-bottom:12px}.v-input--selection-controls .v-input__slot,.v-input--selection-controls .v-radio{cursor:pointer}.v-input--selection-controls .v-input__slot>.v-label,.v-input--selection-controls .v-radio>.v-label{align-items:center;display:inline-flex;flex:1 1 auto;height:auto}.v-input--selection-controls__input{color:inherit;display:inline-flex;flex:0 0 auto;height:24px;position:relative;transition:.3s cubic-bezier(.25,.8,.5,1);transition-property:transform;width:24px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__input .v-icon{width:100%}.v-application--is-ltr .v-input--selection-controls__input{margin-right:8px}.v-application--is-rtl .v-input--selection-controls__input{margin-left:8px}.v-input--selection-controls__input input[role=checkbox],.v-input--selection-controls__input input[role=radio],.v-input--selection-controls__input input[role=switch]{position:absolute;opacity:0;width:100%;height:100%;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__input+.v-label{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__ripple{border-radius:50%;cursor:pointer;height:34px;position:absolute;transition:inherit;width:34px;left:-12px;top:calc(50% - 24px);margin:7px}.v-input--selection-controls__ripple:before{border-radius:inherit;bottom:0;content:"";position:absolute;opacity:.2;left:0;right:0;top:0;transform-origin:center center;transform:scale(.2);transition:inherit}.v-input--selection-controls__ripple>.v-ripple__container{transform:scale(1.2)}.v-input--selection-controls.v-input--dense .v-input--selection-controls__ripple{width:28px;height:28px;left:-9px}.v-input--selection-controls.v-input--dense:not(.v-input--switch) .v-input--selection-controls__ripple{top:calc(50% - 21px)}.v-input--selection-controls.v-input{flex:0 1 auto}.v-input--selection-controls.v-input--is-focused .v-input--selection-controls__ripple:before,.v-input--selection-controls .v-radio--is-focused .v-input--selection-controls__ripple:before{background:currentColor;transform:scale(1.2)}.v-input--selection-controls__input:hover .v-input--selection-controls__ripple:before{background:currentColor;transform:scale(1.2);transition:none}',""]),t.exports=o},1604:function(t,e,n){var content=n(1605);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("beda1088",content,!0,{sourceMap:!1})},1605:function(t,e,n){var o=n(18)(!1);o.push([t.i,".theme--light.v-input--switch .v-input--switch__thumb{color:#fff}.theme--light.v-input--switch .v-input--switch__track{color:rgba(0,0,0,.38)}.theme--light.v-input--switch.v-input--is-disabled:not(.v-input--is-dirty) .v-input--switch__thumb{color:#fafafa!important}.theme--light.v-input--switch.v-input--is-disabled:not(.v-input--is-dirty) .v-input--switch__track{color:rgba(0,0,0,.12)!important}.theme--dark.v-input--switch .v-input--switch__thumb{color:#bdbdbd}.theme--dark.v-input--switch .v-input--switch__track{color:hsla(0,0%,100%,.3)}.theme--dark.v-input--switch.v-input--is-disabled:not(.v-input--is-dirty) .v-input--switch__thumb{color:#424242!important}.theme--dark.v-input--switch.v-input--is-disabled:not(.v-input--is-dirty) .v-input--switch__track{color:hsla(0,0%,100%,.1)!important}.v-input--switch__thumb,.v-input--switch__track{background-color:currentColor;pointer-events:none;transition:inherit}.v-input--switch__track{border-radius:8px;width:36px;height:14px;left:2px;position:absolute;opacity:.6;right:2px;top:calc(50% - 7px)}.v-input--switch__thumb{border-radius:50%;top:calc(50% - 10px);height:20px;position:relative;width:20px;display:flex;justify-content:center;align-items:center;transition:.3s cubic-bezier(.25,.8,.5,1)}.v-input--switch .v-input--selection-controls__input{width:38px}.v-input--switch .v-input--selection-controls__ripple{top:calc(50% - 24px)}.v-input--switch.v-input--dense .v-input--switch__thumb{width:18px;height:18px}.v-input--switch.v-input--dense .v-input--switch__track{height:12px;width:32px}.v-input--switch.v-input--dense.v-input--switch--inset .v-input--switch__track{height:22px;width:44px;top:calc(50% - 12px);left:-3px}.v-input--switch.v-input--dense .v-input--selection-controls__ripple{top:calc(50% - 22px)}.v-input--switch.v-input--is-dirty.v-input--is-disabled{opacity:.6}.v-application--is-ltr .v-input--switch .v-input--selection-controls__ripple{left:-14px}.v-application--is-ltr .v-input--switch.v-input--dense .v-input--selection-controls__ripple{left:-12px}.v-application--is-ltr .v-input--switch.v-input--is-dirty .v-input--selection-controls__ripple,.v-application--is-ltr .v-input--switch.v-input--is-dirty .v-input--switch__thumb{transform:translate(20px)}.v-application--is-rtl .v-input--switch .v-input--selection-controls__ripple{right:-14px}.v-application--is-rtl .v-input--switch.v-input--dense .v-input--selection-controls__ripple{right:-12px}.v-application--is-rtl .v-input--switch.v-input--is-dirty .v-input--selection-controls__ripple,.v-application--is-rtl .v-input--switch.v-input--is-dirty .v-input--switch__thumb{transform:translate(-20px)}.v-input--switch:not(.v-input--switch--flat):not(.v-input--switch--inset) .v-input--switch__thumb{box-shadow:0 2px 4px -1px rgba(0,0,0,.2),0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12)}.v-input--switch--inset .v-input--selection-controls__input,.v-input--switch--inset .v-input--switch__track{width:48px}.v-input--switch--inset .v-input--switch__track{border-radius:14px;height:28px;left:-4px;opacity:.32;top:calc(50% - 14px)}.v-application--is-ltr .v-input--switch--inset .v-input--selection-controls__ripple,.v-application--is-ltr .v-input--switch--inset .v-input--switch__thumb{transform:translate(0)!important}.v-application--is-rtl .v-input--switch--inset .v-input--selection-controls__ripple,.v-application--is-rtl .v-input--switch--inset .v-input--switch__thumb{transform:translate(-6px)!important}.v-application--is-ltr .v-input--switch--inset.v-input--is-dirty .v-input--selection-controls__ripple,.v-application--is-ltr .v-input--switch--inset.v-input--is-dirty .v-input--switch__thumb{transform:translate(20px)!important}.v-application--is-rtl .v-input--switch--inset.v-input--is-dirty .v-input--selection-controls__ripple,.v-application--is-rtl .v-input--switch--inset.v-input--is-dirty .v-input--switch__thumb{transform:translate(-26px)!important}",""]),t.exports=o},1747:function(t,e,n){"use strict";n(7),n(8),n(9),n(14),n(6),n(15);var o=n(2),l=(n(211),n(1479),n(1604),n(1480)),r=n(117),c=n(703),d=n(267),h=n(269),v=n(1);function m(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}function f(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?m(Object(source),!0).forEach((function(e){Object(o.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):m(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}e.a=l.a.extend({name:"v-switch",directives:{Touch:c.a},props:{inset:Boolean,loading:{type:[Boolean,String],default:!1},flat:{type:Boolean,default:!1}},computed:{classes:function(){return f(f({},r.a.options.computed.classes.call(this)),{},{"v-input--selection-controls v-input--switch":!0,"v-input--switch--flat":this.flat,"v-input--switch--inset":this.inset})},attrs:function(){return{"aria-checked":String(this.isActive),"aria-disabled":String(this.isDisabled),role:"switch"}},validationState:function(){return this.hasError&&this.shouldValidate?"error":this.hasSuccess?"success":null!==this.hasColor?this.computedColor:void 0},switchData:function(){return this.setTextColor(this.loading?void 0:this.validationState,{class:this.themeClasses})}},methods:{genDefaultSlot:function(){return[this.genSwitch(),this.genLabel()]},genSwitch:function(){return this.$createElement("div",{staticClass:"v-input--selection-controls__input"},[this.genInput("checkbox",f(f({},this.attrs),this.attrs$)),this.genRipple(this.setTextColor(this.validationState,{directives:[{name:"touch",value:{left:this.onSwipeLeft,right:this.onSwipeRight}}]})),this.$createElement("div",f({staticClass:"v-input--switch__track"},this.switchData)),this.$createElement("div",f({staticClass:"v-input--switch__thumb"},this.switchData),[this.genProgress()])])},genProgress:function(){return this.$createElement(d.c,{},[!1===this.loading?null:this.$slots.progress||this.$createElement(h.a,{props:{color:!0===this.loading||""===this.loading?this.color||"primary":this.loading,size:16,width:2,indeterminate:!0}})])},onSwipeLeft:function(){this.isActive&&this.onChange()},onSwipeRight:function(){this.isActive||this.onChange()},onKeydown:function(t){(t.keyCode===v.s.left&&this.isActive||t.keyCode===v.s.right&&!this.isActive)&&this.onChange()}}})},1932:function(t,e,n){"use strict";n.r(e);var o=n(1375),l=n(370),r={name:"CalendarNotificationInfo",components:{UserSettingTemplate:o.default,TextInput:l.default},data:function(){return{syncCalendarWithSlots:void 0}},computed:{isTeacher:function(){return this.$store.getters["user/isTeacher"]},item:function(){return this.$store.state.settings.notificationCalendarItem},emailRules:function(){return this.enabled?[function(t){return!!t},function(t){return/.+@.+\..+/.test(t)}]:[]},email:{get:function(){return this.item.email},set:function(t){this.$store.commit("settings/UPDATE_NOTIFICATION_CALENDAR_ITEM",{email:t})}},enabled:{get:function(){return this.item.enabled},set:function(t){this.$store.commit("settings/UPDATE_NOTIFICATION_CALENDAR_ITEM",{enabled:t})}},styles:function(){var t={};return this.enabled||(t={opacity:0,visibility:"hidden",zIndex:-1}),t}},beforeCreate:function(){this.$store.dispatch("settings/getNotificationCalendar")},beforeMount:function(){this.syncCalendarWithSlots=this.$store.state.user.item.syncCalendarWithSlots},methods:{submitData:function(){this.$store.dispatch("settings/updateNotificationCalendar")},toggleAdvancedIntegration:function(t){window.location=t?"/user/login/google-calendar":"/google/calendar-notification-webhook-deactivate"}}},c=n(22),d=n(42),h=n.n(d),v=n(1360),m=n(1361),f=n(1747),component=Object(c.a)(r,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.item?n("user-setting-template",{attrs:{title:t.$t("calendar"),"submit-func":t.submitData}},[n("v-row",[n("v-col",{staticClass:"col-12 col-md-8"},[n("div",{staticClass:"input-wrap mb-3 mb-md-0"},[n("div",{staticClass:"input-wrap-title body-1 font-weight-medium"},[t._v("\n          "+t._s(t.$t("do_you_want_to_automatically_add_lesson_bookings_to_your_calendar"))+"\n        ")]),t._v(" "),t.$vuetify.breakpoint.mdAndUp?n("div",{staticClass:"mt-4 d-none d-md-block",style:t.styles},[n("text-input",{attrs:{"type-class":"border-gradient",height:"44",rules:t.emailRules,"hide-details":"",placeholder:t.$t("calendar_email_address")},model:{value:t.email,callback:function(e){t.email=e},expression:"email"}})],1):t._e()])]),t._v(" "),n("v-col",{staticClass:"col-12 col-md-auto",attrs:{"offset-md":"1"}},[n("v-row",{attrs:{align:"center",justify:"space-between"}},[t.$vuetify.breakpoint.smAndDown?n("v-col",{staticClass:"col-9 col-md-12 d-md-none"},[n("div",{style:t.styles},[n("text-input",{attrs:{"type-class":"border-gradient",height:"44",rules:t.emailRules,"hide-details":"",placeholder:t.$t("calendar_email_address")},model:{value:t.email,callback:function(e){t.email=e},expression:"email"}})],1)]):t._e(),t._v(" "),n("v-col",{staticClass:"col-auto"},[n("v-switch",{staticClass:"pt-0 mt-0 mt-md-3",attrs:{inset:"",ripple:!1,color:"success",dense:"","hide-details":""},model:{value:t.enabled,callback:function(e){t.enabled=e},expression:"enabled"}})],1)],1)],1)],1),t._v(" "),t.isTeacher?n("v-row",{staticClass:"pt-3 pt-md-4"},[n("v-col",{staticClass:"col-12 col-md-8"},[n("div",{staticClass:"input-wrap mb-3 mb-md-0"},[n("div",{staticClass:"input-wrap-title body-1 font-weight-medium d-flex align-center"},[n("img",{staticStyle:{height:"24px",width:"24px","margin-right":"8px"},attrs:{src:"https://www.gstatic.com/marketing-cms/assets/images/cf/3c/0d56042f479fac9ad22d06855578/calender.webp=s96-fcrop64=1,00000000ffffffff-rw",alt:"Google Calendar"}}),t._v("\n          Connect to Google Calendar\n        ")]),t._v(" "),n("span",{staticStyle:{"font-size":"14px"},domProps:{innerHTML:t._s(t.$t("advanced_integration_google_calendar_only"))}})])]),t._v(" "),n("v-col",{staticClass:"col-12 col-md-auto d-flex justify-end",attrs:{"offset-md":"1"}},[n("v-switch",{staticClass:"pt-0 mt-0 mt-md-3",attrs:{inset:"",ripple:!1,color:"success",dense:"","hide-details":""},on:{change:t.toggleAdvancedIntegration},model:{value:t.syncCalendarWithSlots,callback:function(e){t.syncCalendarWithSlots=e},expression:"syncCalendarWithSlots"}})],1)],1):t._e(),t._v(" "),t.isTeacher?n("v-row",{staticClass:"pt-0 pb-2"},[n("v-col",{staticClass:"col-12"},[n("div",{staticClass:"mt-2",staticStyle:{"font-size":"13px",color:"#757575"}},[t._v("\n        This app's use and transfer to any other app of information received\n        from Google APIs will adhere to the Google API Services User Data\n        Policy, including the Limited Use requirements.\n        "),n("a",{staticStyle:{color:"#1a73e8","text-decoration":"underline"},attrs:{href:"https://developers.google.com/terms/api-services-user-data-policy",target:"_blank",rel:"noopener"}},[t._v("\n          Read more\n        ")])])])],1):t._e()],1):t._e()}),[],!1,null,"0780ed20",null);e.default=component.exports;h()(component,{UserSettingTemplate:n(1375).default}),h()(component,{VCol:v.a,VRow:m.a,VSwitch:f.a})}}]);