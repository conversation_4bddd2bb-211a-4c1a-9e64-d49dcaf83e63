exports.ids = [95,90,91];
exports.modules = {

/***/ 1016:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerDialog_vue_vue_type_style_index_0_id_3bb5b460_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(963);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerDialog_vue_vue_type_style_index_0_id_3bb5b460_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerDialog_vue_vue_type_style_index_0_id_3bb5b460_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerDialog_vue_vue_type_style_index_0_id_3bb5b460_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerDialog_vue_vue_type_style_index_0_id_3bb5b460_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1017:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".selects-language[data-v-3bb5b460]{width:auto!important;min-width:120px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1027:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/TimePickerDialog.vue?vue&type=template&id=3bb5b460&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{"dialog":_vm.isShownTimePickerDialog,"max-width":"1030","custom-class":"time-picker","persistent":"","fullscreen":_vm.$vuetify.breakpoint.smAndDown}},_vm.$listeners),[_c('div',{staticClass:"time-picker-header text--gradient"},[_vm._v("\n    "+_vm._s(_vm.$t('schedule_your_lessons'))+":\n  ")]),_vm._v(" "),_c('div',{staticClass:"wrap"},[_c('div',{staticClass:"time-picker-body"},[_c('div',{staticClass:"time-picker-selects mb-md-2"},[_c('div',{staticClass:"selects"},[_c('div',{staticClass:"selects-language"},[_c('div',{staticClass:"selects-lesson-value d-flex align-center"},[_c('div',{staticClass:"icon icon-flag elevation-2"},[(_vm.language.isoCode)?_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (_vm.language.isoCode) + ".svg"),"width":"18","height":"18"}}):_vm._e()],1),_vm._v("\n              "+_vm._s(_vm.language.name)+"\n            ")])]),_vm._v(" "),(_vm.course)?_c('div',{staticClass:"selects-course"},[_vm._v("\n            "+_vm._s(_vm.course)+"\n          ")]):_vm._e(),_vm._v(" "),_c('div',{staticClass:"selects-lesson"},[_c('div',{staticClass:"selects-lesson-value d-flex align-center"},[_c('div',{staticClass:"icon"},[_c('v-img',{attrs:{"src":__webpack_require__(504),"width":"18","height":"18"}})],1),_vm._v("\n\n              "+_vm._s(_vm.$tc('lessons_count', _vm.countLessons))+"\n            ")])]),_vm._v(" "),_c('div',{staticClass:"selects-duration"},[_c('div',{staticClass:"selects-lesson-value d-flex align-center"},[_c('div',{staticClass:"icon"},[_c('v-img',{attrs:{"src":__webpack_require__(504),"width":"18","height":"18"}})],1),_vm._v("\n\n              "+_vm._s(_vm.$tc('minutes_count', _vm.lessonLength))+"\n            ")])])])]),_vm._v(" "),_c('time-picker',_vm._g({attrs:{"username":_vm.username,"lesson-length":_vm.lessonLength,"quantity-lessons":_vm.countLessons,"current-time":_vm.currentTime,"is-shown-time-picker-dialog":_vm.isShownTimePickerDialog}},_vm.$listeners))],1)]),_vm._v(" "),_c('div',{staticClass:"time-picker-footer d-flex justify-md-end"},[_c('v-btn',{attrs:{"small":"","color":"primary","disabled":_vm.isNotValid},on:{"click":_vm.scheduleLessons}},[_vm._v("\n      "+_vm._s(_vm.$t('schedule_now'))+"\n    ")])],1)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-lessons/TimePickerDialog.vue?vue&type=template&id=3bb5b460&scoped=true&

// EXTERNAL MODULE: ./components/LDialog.vue + 5 modules
var LDialog = __webpack_require__(28);

// EXTERNAL MODULE: ./components/TimePicker.vue + 4 modules
var TimePicker = __webpack_require__(955);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-lessons/TimePickerDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var TimePickerDialogvue_type_script_lang_js_ = ({
  name: 'TimePickerDialog',
  components: {
    LDialog: LDialog["default"],
    TimePicker: TimePicker["default"]
  },
  props: {
    isShownTimePickerDialog: {
      type: Boolean,
      required: true
    },
    username: {
      type: String,
      required: true
    },
    language: {
      type: Object,
      required: true
    },
    currentTime: {
      type: Object,
      required: true
    },
    lessonLength: {
      type: Number,
      required: true
    },
    countLessons: {
      type: Number,
      required: true
    },
    purchaseId: {
      type: Number,
      required: true
    },
    course: {
      type: String,
      required: true
    }
  },
  computed: {
    selectedSlots() {
      return this.$store.state.teacher_profile.selectedSlots || [];
    },

    isNotValid() {
      return this.selectedSlots.length === 0;
    }

  },
  methods: {
    scheduleLessons() {
      const slots = [];

      if (this.selectedSlots.length) {
        this.selectedSlots.forEach(item => {
          item.forEach(el => slots.push(el.id));
        });
      }

      if (slots.length) {
        this.$store.dispatch('lesson/scheduleLesson', {
          purchaseId: this.purchaseId,
          slots
        }).then(() => {
          this.$store.commit('user/DECREASE_UNSCHEDULED_LESSONS_NUMBER');
          this.$emit('close-dialog');
          this.$store.commit('teacher_profile/RESET_SELECTED_SLOTS');
          this.$router.push({
            path: '/user/lessons'
          });
        }).catch(e => {
          this.$store.dispatch('snackbar/error');
          console.info(e);
        });
      }
    }

  }
});
// CONCATENATED MODULE: ./components/user-lessons/TimePickerDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_lessons_TimePickerDialogvue_type_script_lang_js_ = (TimePickerDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-lessons/TimePickerDialog.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1016)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_lessons_TimePickerDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "3bb5b460",
  "6e78877e"
  
)

/* harmony default export */ var TimePickerDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {TimePicker: __webpack_require__(955).default,LDialog: __webpack_require__(28).default})


/* vuetify-loader */



installComponents_default()(component, {VBtn: VBtn["a" /* default */],VImg: VImg["a" /* default */]})


/***/ }),

/***/ 940:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(981);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("13082346", content, true, context)
};

/***/ }),

/***/ 947:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/TimePickerItem.vue?vue&type=template&id=7467ec82&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[
    'time-picker-item',
    { active: _vm.isActive },
    { selected: _vm.item.isSelected },
    { free: _vm.item.isAvailable },
    { unavailable: _vm.item.isUnavailable } ],attrs:{"id":_vm.elId},on:{"mouseover":_vm.mouseoverHandler,"mouseleave":_vm.mouseleaveHandler,"click":function($event){$event.stopPropagation();return _vm.clickHandler.apply(null, arguments)}}},[])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/TimePickerItem.vue?vue&type=template&id=7467ec82&scoped=true&

// EXTERNAL MODULE: ./helpers/check_device.js
var check_device = __webpack_require__(145);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/TimePickerItem.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var TimePickerItemvue_type_script_lang_js_ = ({
  name: 'TimePickerItem',
  props: {
    idDefined: {
      type: Boolean,
      default: false
    },
    item: {
      type: Object,
      required: true
    },
    allowedToSelect: {
      type: Boolean,
      required: true
    },
    activeItems: {
      type: Array,
      required: true
    }
  },

  data() {
    return {
      isTouchDevice: Object(check_device["d" /* isTouchDevice */])()
    };
  },

  computed: {
    timezone() {
      return this.$store.getters['user/timeZone'];
    },

    elId() {
      return this.idDefined ? `h-${this.$dayjs(this.item.date).add(this.$dayjs(this.item.date).tz(this.timezone).utcOffset(), 'minute').format('HH-mm')}` : null;
    },

    isActive() {
      return this.item.isAvailable && this.activeItems.includes(this.item);
    }

  },
  methods: {
    clickHandler() {
      if (this.item.isAvailable) {
        this.$emit('click-item', this.item);
      }
    },

    mouseoverHandler() {
      if (!this.isTouchDevice && this.item.isAvailable && !this.item.isSelected && this.allowedToSelect) {
        this.$emit('mouseover-item', this.item);
      }
    },

    mouseleaveHandler() {
      if (this.item.isAvailable && !this.item.isSelected && this.allowedToSelect) {
        this.$emit('mouseleave-item');
      }
    }

  }
});
// CONCATENATED MODULE: ./components/TimePickerItem.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_TimePickerItemvue_type_script_lang_js_ = (TimePickerItemvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/TimePickerItem.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(980)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_TimePickerItemvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "7467ec82",
  "d1fa2cf4"
  
)

/* harmony default export */ var TimePickerItem = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 950:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(987);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("1fdd5634", content, true, context)
};

/***/ }),

/***/ 955:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/TimePicker.vue?vue&type=template&id=69022ce1&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"time-picker unselected"},[_vm._ssrNode("<div class=\"time-picker-toggle\" data-v-69022ce1>","</div>",[_vm._ssrNode("<div"+(_vm._ssrClass(null,['btn btn-prev', { 'btn--disabled': _vm.isPrevButtonDisabled }]))+" data-v-69022ce1>","</div>",[_c('v-icon',{attrs:{"color":"greyDark"}},[_vm._v(_vm._s(_vm.mdiChevronLeft))])],1),_vm._ssrNode(" <div class=\"period text-center\" data-v-69022ce1>"+_vm._ssrEscape("\n      "+_vm._s(_vm.firstDayOfWeek.format('D MMM'))+" -\n      "+_vm._s(_vm.lastDayOfWeek.format('D MMM'))+"\n    ")+"</div> "),_vm._ssrNode("<div class=\"btn btn-next\" data-v-69022ce1>","</div>",[_c('v-icon',{attrs:{"color":"greyDark"}},[_vm._v(_vm._s(_vm.mdiChevronRight))])],1)],2),_vm._ssrNode(" <div class=\"time-picker-top-bar\" data-v-69022ce1><div class=\"time-picker-top-bar-helper mx-auto\" data-v-69022ce1>"+(_vm._ssrList((7),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape("\n        "+_vm._s(_vm._f("dayFormat")(_vm.getDayOfWeek(i),'ddd,'))+"\n        ")+((_vm.$vuetify.breakpoint.xsOnly)?("<br class=\"d-sm-none\" data-v-69022ce1>"):"<!---->")+_vm._ssrEscape("\n        "+_vm._s(_vm._f("dayFormat")(_vm.getDayOfWeek(i),'MMM D'))+"\n      ")+"</div>")}))+"</div></div> "),_vm._ssrNode("<div class=\"time-picker-wrap l-scroll l-scroll--grey l-scroll--large\" data-v-69022ce1>","</div>",[_vm._ssrNode("<div class=\"time-picker-wrap-helper\" data-v-69022ce1>","</div>",[_vm._ssrNode("<div class=\"time-picker-left-bar\" data-v-69022ce1>"+((_vm.$i18n.locale === 'en')?("<div class=\"item\" data-v-69022ce1>12 AM</div> "+(_vm._ssrList((11),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape(_vm._s(i)+" AM")+"</div>")}))+" <div class=\"item\" data-v-69022ce1>12 PM</div> "+(_vm._ssrList((11),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape(_vm._s(i)+" PM")+"</div>")}))):((_vm._ssrList((24),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape(_vm._s(i - 1)+":00")+"</div>")}))))+"</div> "),_vm._ssrNode("<div class=\"time-picker-graph\" data-v-69022ce1>","</div>",_vm._l((_vm.calendar),function(day,idx){return _vm._ssrNode("<div class=\"day\" data-v-69022ce1>","</div>",[_c('client-only',_vm._l((day),function(item,index){return _c('time-picker-item',{key:(idx + "-" + index),class:index % 2 ? '' : 'first-half',attrs:{"id-defined":"","item":item,"allowed-to-select":_vm.allowedToSelect,"active-items":_vm.activeItems},on:{"mouseover-item":function($event){return _vm.mouseoverItem($event)},"mouseleave-item":_vm.mouseleaveItem,"click-item":function($event){return _vm.clickItem($event)}}})}),1)],1)}),0),_vm._ssrNode(" <div class=\"time-picker-right-bar\" data-v-69022ce1>"+((_vm.$i18n.locale === 'en')?("<div class=\"item\" data-v-69022ce1>12 AM</div> "+(_vm._ssrList((11),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape(_vm._s(i)+" AM")+"</div>")}))+" <div class=\"item\" data-v-69022ce1>12 PM</div> "+(_vm._ssrList((11),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape(_vm._s(i)+" PM")+"</div>")}))):((_vm._ssrList((24),function(i){return ("<div class=\"item\" data-v-69022ce1>"+_vm._ssrEscape(_vm._s(i - 1)+":00")+"</div>")}))))+"</div>")],2)]),_vm._ssrNode(" "),_c('loader',{attrs:{"is-loading":_vm.isLoading,"absolute":""}})],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/TimePicker.vue?vue&type=template&id=69022ce1&scoped=true&

// EXTERNAL MODULE: external "@mdi/js"
var js_ = __webpack_require__(48);

// EXTERNAL MODULE: ./components/TimePickerItem.vue + 4 modules
var TimePickerItem = __webpack_require__(947);

// EXTERNAL MODULE: ./components/Loader.vue + 4 modules
var Loader = __webpack_require__(84);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/TimePicker.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



const STATUS_FREE = 0;
const STATUS_RESERVED = 1;
const STATUS_OCCUPIED = 2; // const STATUS_SOME_AVAILABILITY = 4

/* harmony default export */ var TimePickervue_type_script_lang_js_ = ({
  name: 'TimePicker',
  components: {
    TimePickerItem: TimePickerItem["default"],
    Loader: Loader["default"]
  },
  filters: {
    dayFormat(time, format = 'HH:mm') {
      return time.format(format);
    }

  },
  props: {
    username: {
      type: String,
      required: true
    },
    lessonLength: {
      type: Number,
      required: true
    },
    quantityLessons: {
      type: Number,
      required: true
    },
    currentTime: {
      type: Object,
      required: true
    },
    isShownTimePickerDialog: {
      type: Boolean,
      required: true
    }
  },

  data() {
    return {
      mdiChevronLeft: js_["mdiChevronLeft"],
      mdiChevronRight: js_["mdiChevronRight"],
      key: 1,
      now: this.$dayjs(),
      items: [],
      activeItems: [],
      isLoading: false,
      mounted: false
    };
  },

  computed: {
    firstDayOfWeek() {
      return this.currentTime.day(1);
    },

    lastDayOfWeek() {
      return this.currentTime.day(7);
    },

    slots() {
      return this.$store.state.teacher_profile.slots;
    },

    selectedSlots() {
      return this.$store.state.teacher_profile.selectedSlots || [];
    },

    isPrevButtonDisabled() {
      return this.now.day(1).isSameOrAfter(this.firstDayOfWeek, 'day');
    },

    quantityItemsPerLesson() {
      return this.lessonLength / 30;
    },

    allowedToSelect() {
      return this.selectedSlots.length < this.quantityLessons || this.quantityLessons === 1;
    },

    calendar() {
      const result = [];

      for (let i = 0; i < 7; i++) {
        result.push(this.items.slice(i * 48, 48 * (i + 1)));
      }

      return result;
    },

    isUserLogged() {
      return this.$store.getters['user/isUserLogged'];
    },

    timeZone() {
      return this.$store.getters['user/timeZone'];
    }

  },
  watch: {
    lessonLength() {
      this.reset();
    },

    quantityLessons() {
      this.reset();
    },

    slots: {
      handler() {
        if (!this.isShownTimePickerDialog) {
          setTimeout(this.getItems);
        }
      },

      deep: true
    },

    isShownTimePickerDialog(newValue, oldValue) {
      if (this.mounted && newValue) {
        this.scroll();
      }
    }

  },

  async mounted() {
    const selectedSlots = window.localStorage.getItem('selected-slots') ? JSON.parse(window.localStorage.getItem('selected-slots')) : null;

    if (selectedSlots) {
      this.$store.commit('teacher_profile/SET_SELECTED_SLOTS', selectedSlots);
      window.localStorage.removeItem('selected-slots');
    }

    await this.getItems();
    this.scroll();
    this.mounted = true;
  },

  methods: {
    getItems() {
      const result = [];
      let selectedSlots = [];
      this.selectedSlots.forEach(item => selectedSlots = selectedSlots.concat(item.map(el => el.date)));

      for (let d = 1; d <= 7; d++) {
        for (let h = 0; h < 48; h++) {
          var _sameItem, _sameItem2, _sameItem3, _sameItem4, _sameItem5, _sameItem6;

          const date = this.getDayOfWeek(d).hour(Math.floor(h / 2)).minute(h % 2 ? 30 : 0).second(0);
          const dateOffset = date.tz(this.timeZone).utcOffset();
          let sameItem;

          for (let s = 0; s < this.slots.length; s++) {
            const dateObj = this.$dayjs(this.slots[s].date);

            if (date.isSame(dateObj.add(dateOffset, 'minute'), 'minute')) {
              sameItem = this.slots[s];
              break;
            }
          }

          const dateByUTC = date.add(this.$dayjs(date).tz(this.timeZone).utcOffset() * -1, 'minute').format();
          result.push({
            id: (_sameItem = sameItem) === null || _sameItem === void 0 ? void 0 : _sameItem.id,
            status: (_sameItem2 = sameItem) === null || _sameItem2 === void 0 ? void 0 : _sameItem2.status,
            date: dateByUTC,
            isSelected: selectedSlots.includes(dateByUTC),
            isAvailable: ((_sameItem3 = sameItem) === null || _sameItem3 === void 0 ? void 0 : _sameItem3.status) === STATUS_FREE && !this.now.add(1, 'day').isSameOrAfter(date, 'minute'),
            isUnavailable: ((_sameItem4 = sameItem) === null || _sameItem4 === void 0 ? void 0 : _sameItem4.status) === STATUS_OCCUPIED || ((_sameItem5 = sameItem) === null || _sameItem5 === void 0 ? void 0 : _sameItem5.status) === STATUS_RESERVED || ((_sameItem6 = sameItem) === null || _sameItem6 === void 0 ? void 0 : _sameItem6.status) === STATUS_FREE && this.now.add(1, 'day').isSameOrAfter(date, 'minute')
          });
        }
      }

      this.items = result;
    },

    async toggleWeek(day) {
      const date = this.firstDayOfWeek.add(day, 'day');
      await this.$store.dispatch('loadingAllow', false);
      this.isLoading = true;
      this.$store.dispatch('teacher_profile/getSlots', {
        slug: this.username,
        date
      }).then(() => {
        this.$emit('update-current-time', date);
        this.$nextTick(this.getItems);
      }).then(() => {
        this.scroll();
      }).finally(() => {
        this.isLoading = false;
        this.$store.dispatch('loadingAllow', true);
      });
    },

    getDayOfWeek(day) {
      return this.currentTime.day(day);
    },

    checkDirection(item, index) {
      let direction = 0;

      for (let i = 1; i < this.quantityItemsPerLesson; i++) {
        if (this.items[index + i].isAvailable && !this.items[index + i].isSelected) {
          direction = 1;
        } else {
          direction = 0;
          break;
        }
      }

      if (direction === 0) {
        for (let i = 1; i < this.quantityItemsPerLesson; i++) {
          if (this.items[index - i].isAvailable && !this.items[index - i].isSelected) {
            direction = -1;
          } else {
            direction = 0;
            break;
          }
        }
      }

      return direction;
    },

    clickItem(item) {
      if (!item.isSelected && this.allowedToSelect) {
        const arr = [item];

        if (this.quantityItemsPerLesson === 1 && this.quantityLessons === 1) {
          this.activeItems = [];
          this.resetSelectedSlots();
        }

        if (this.quantityItemsPerLesson > 1) {
          const index = this.items.indexOf(item);
          const direction = this.checkDirection(item, index);

          if (direction) {
            if (this.quantityLessons === 1) {
              this.resetSelectedSlots();
            }

            for (let i = 1; i < this.quantityItemsPerLesson; i++) {
              arr.push(this.items[index + i * direction]);
            }
          } else {
            return;
          }
        }

        arr.forEach(item => item.isSelected = true);
        this.$store.commit('teacher_profile/ADD_SELECTED_SLOT', arr);
      } else {
        for (let i = 0; i < this.selectedSlots.length; i++) {
          var _this$selectedSlots$i;

          const ids = (_this$selectedSlots$i = this.selectedSlots[i]) === null || _this$selectedSlots$i === void 0 ? void 0 : _this$selectedSlots$i.map(el => el.id);

          if (ids.includes(item.id)) {
            this.$store.commit('teacher_profile/REMOVE_SELECTED_SLOT', i);
            this.items.forEach(arr => {
              if (ids.includes(arr.id)) {
                arr.isSelected = false;
              }
            });
            break;
          }
        }
      }

      if (!this.isUserLogged) {
        window.setTimeout(() => {
          this.$emit('next-step');
        }, 300);
      }
    },

    mouseoverItem(item) {
      if (this.quantityItemsPerLesson === 1) {
        this.activeItems.push(item);
        return;
      }

      if (this.quantityItemsPerLesson > 1) {
        const index = this.items.indexOf(item);
        const direction = this.checkDirection(item, index);

        if (direction) {
          this.activeItems.push(item);

          for (let i = 1; i < this.quantityItemsPerLesson; i++) {
            this.activeItems.push(this.items[index + i * direction]);
          }
        }
      }
    },

    mouseleaveItem() {
      this.activeItems = [];
    },

    resetSelectedSlots() {
      this.$store.commit('teacher_profile/RESET_SELECTED_SLOTS');
      this.items.forEach(arr => {
        arr.isSelected = false;
      });
    },

    reset() {
      this.activeItems = [];
      this.resetSelectedSlots();
      this.key++;
    },

    scroll() {
      const options = {
        top: 560,
        behavior: 'instant'
      };

      if (this.selectedSlots.length) {
        const [earliestTime] = this.selectedSlots.flat().map(slot => {
          const dateObj = this.$dayjs(slot.date);
          return dateObj.add(dateObj.tz(this.timeZone).utcOffset(), 'minute').format();
        }).filter(date => this.$dayjs(date).isBetween(this.firstDayOfWeek, this.lastDayOfWeek)).map(date => this.$dayjs(date).format('HH-mm')).sort();
        const el = document.getElementById(`h-${earliestTime}`);

        if (el) {
          options.top = el.offsetTop - 84;
        }
      }

      setTimeout(() => {
        this.$refs.timePickerWrap.scroll(options);
      });
    }

  }
});
// CONCATENATED MODULE: ./components/TimePicker.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_TimePickervue_type_script_lang_js_ = (TimePickervue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(87);

// CONCATENATED MODULE: ./components/TimePicker.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(986)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_TimePickervue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "69022ce1",
  "637ca153"
  
)

/* harmony default export */ var TimePicker = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {TimePickerItem: __webpack_require__(947).default,Loader: __webpack_require__(84).default})


/* vuetify-loader */


installComponents_default()(component, {VIcon: VIcon["a" /* default */]})


/***/ }),

/***/ 963:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1017);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("8eac9684", content, true, context)
};

/***/ }),

/***/ 980:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(940);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePickerItem_vue_vue_type_style_index_0_id_7467ec82_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 981:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".time-picker-item[data-v-7467ec82]{position:relative;height:32px;box-shadow:inset -1px -1px 0 #e0e0e0}.time-picker-item.free[data-v-7467ec82]{background-color:var(--v-success-base);cursor:pointer}.time-picker-item.active[data-v-7467ec82]{background:#fff repeating-linear-gradient(45deg,rgba(251,176,59,.6),rgba(251,176,59,.6) 7px,var(--v-orange-base) 0,var(--v-orange-base) 20px)}.time-picker-item.selected[data-v-7467ec82]{background-color:var(--v-orange-base)}.time-picker-item.unavailable[data-v-7467ec82]{background-color:#636363}.time-picker-item.first-half[data-v-7467ec82]:after{content:\"\";position:absolute;left:0;bottom:0;width:100%;height:1px;box-shadow:inset 0 -1px 0 #f7f7f7}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 986:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePicker_vue_vue_type_style_index_0_id_69022ce1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(950);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePicker_vue_vue_type_style_index_0_id_69022ce1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePicker_vue_vue_type_style_index_0_id_69022ce1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePicker_vue_vue_type_style_index_0_id_69022ce1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TimePicker_vue_vue_type_style_index_0_id_69022ce1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 987:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".time-picker[data-v-69022ce1]{--timepicker-sidebar-width:48px;position:relative;display:flex;flex-direction:column;flex-grow:1}@media only screen and (max-width:767px){.time-picker[data-v-69022ce1]{--timepicker-sidebar-width:30px}}.time-picker-helper[data-v-69022ce1],.time-picker-toggle[data-v-69022ce1]{display:flex}.time-picker-toggle[data-v-69022ce1]{justify-content:center;align-items:center;margin-bottom:12px}.time-picker-toggle .btn[data-v-69022ce1]{margin:0 10px;cursor:pointer}.time-picker-toggle .btn--disabled[data-v-69022ce1]{cursor:auto;opacity:.4}.time-picker-toggle .period[data-v-69022ce1]{min-width:120px;font-size:16px;font-weight:700;line-height:22px}.time-picker-wrap[data-v-69022ce1]{flex-grow:1;height:132px;overflow-y:auto;overflow-x:hidden}.time-picker-wrap-helper[data-v-69022ce1]{display:flex}.time-picker-left-bar .item[data-v-69022ce1],.time-picker-right-bar .item[data-v-69022ce1],.time-picker-top-bar .item[data-v-69022ce1]{font-size:12px;color:#575757;text-align:center;line-height:1.333;white-space:nowrap}@media only screen and (max-width:991px){.time-picker-left-bar .item[data-v-69022ce1],.time-picker-right-bar .item[data-v-69022ce1],.time-picker-top-bar .item[data-v-69022ce1]{font-size:10px}}.time-picker-left-bar[data-v-69022ce1],.time-picker-right-bar[data-v-69022ce1]{width:var(--timepicker-sidebar-width)}.time-picker-left-bar .item[data-v-69022ce1],.time-picker-right-bar .item[data-v-69022ce1]{height:64px}.time-picker-top-bar[data-v-69022ce1]{padding-right:8px}.time-picker-top-bar-helper[data-v-69022ce1]{display:flex;justify-content:space-around;width:calc(100% - var(--timepicker-sidebar-width)*2 - 2px)}.time-picker-top-bar .item[data-v-69022ce1]{display:flex;flex-wrap:wrap;justify-content:center;align-items:center;flex-basis:14.2857%;height:32px}@media only screen and (max-width:479px){.time-picker-top-bar .item[data-v-69022ce1]{font-size:10px}}.time-picker-graph[data-v-69022ce1]{width:calc(100% - 48px);display:flex;border-color:#e0e0e0;border-style:solid;border-width:1px 0 0 1px}@media only screen and (max-width:767px){.time-picker-graph[data-v-69022ce1]{width:calc(100% - 24px)}}.time-picker-graph .day[data-v-69022ce1]{flex-grow:1}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

};;
//# sourceMappingURL=user-lessons-time-picker-dialog.js.map