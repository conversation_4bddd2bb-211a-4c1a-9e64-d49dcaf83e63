{"version": 3, "file": "components/landing-page-testimonials-slider.js", "sources": ["webpack:///./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css?06df", "webpack:///./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css", "webpack:///./components/landing-page/TestimonialsSlider.vue?16d9", "webpack:///./components/landing-page/TestimonialsSlider.vue?f93c", "webpack:///./components/landing-page/TestimonialsSlider.vue?f501", "webpack:///./components/landing-page/TestimonialsSlider.vue?fbb1", "webpack:///./components/landing-page/TestimonialsSlider.vue", "webpack:///./components/landing-page/TestimonialsSlider.vue?1c21", "webpack:///./components/landing-page/TestimonialsSlider.vue?8f30"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../css-loader/dist/cjs.js??ref--3-oneOf-1-1!../../postcss-loader/src/index.js??ref--3-oneOf-1-2!./vue-slick-carousel.css\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../vue-style-loader/lib/addStylesServer.js\").default(\"20c2c1c7\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".slick-track[data-v-e4caeaf8]{position:relative;top:0;left:0;display:block;transform:translateZ(0)}.slick-track.slick-center[data-v-e4caeaf8]{margin-left:auto;margin-right:auto}.slick-track[data-v-e4caeaf8]:after,.slick-track[data-v-e4caeaf8]:before{display:table;content:\\\"\\\"}.slick-track[data-v-e4caeaf8]:after{clear:both}.slick-loading .slick-track[data-v-e4caeaf8]{visibility:hidden}.slick-slide[data-v-e4caeaf8]{display:none;float:left;height:100%;min-height:1px}[dir=rtl] .slick-slide[data-v-e4caeaf8]{float:right}.slick-slide img[data-v-e4caeaf8]{display:block}.slick-slide.slick-loading img[data-v-e4caeaf8]{display:none}.slick-slide.dragging img[data-v-e4caeaf8]{pointer-events:none}.slick-initialized .slick-slide[data-v-e4caeaf8]{display:block}.slick-loading .slick-slide[data-v-e4caeaf8]{visibility:hidden}.slick-vertical .slick-slide[data-v-e4caeaf8]{display:block;height:auto;border:1px solid transparent}.slick-arrow.slick-hidden[data-v-21137603]{display:none}.slick-slider[data-v-3d1a4f76]{position:relative;display:block;box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;touch-action:pan-y;-webkit-tap-highlight-color:transparent}.slick-list[data-v-3d1a4f76]{position:relative;display:block;overflow:hidden;margin:0;padding:0;transform:translateZ(0)}.slick-list[data-v-3d1a4f76]:focus{outline:none}.slick-list.dragging[data-v-3d1a4f76]{cursor:pointer;cursor:hand}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestimonialsSlider.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"335dfb13\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestimonialsSlider.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".trusted-slider{max-width:1640px;margin:50px auto;background:none!important;position:relative!important;height:-webkit-max-content;height:-moz-max-content;height:max-content}@media(max-width:1170px){.trusted-slider{margin:30px auto 50px}}@media only screen and (max-width:991px){.trusted-slider{margin:30px auto 0}}.trusted-slider:before{content:none!important}.trusted-slider .slick-track{display:flex!important;align-items:center}.trusted-slider .slick-slide{padding:0 15px;transition:all .5s ease-in}@media only screen and (max-width:639px){.trusted-slider .slick-slide{padding:0 10px}}.trusted-slider .slick-slide:not(.slick-center){transform:translateZ(0) scale(.8)!important;opacity:.7}@media only screen and (max-width:991px){.trusted-slider .slick-slide:not(.slick-center){transform:translateZ(0) scale(.9)!important;opacity:.6}}.trusted-slider .slick-slide .slider-elem{display:flex;width:100%;max-width:1000px;height:-webkit-max-content;height:-moz-max-content;height:max-content;margin:0 auto;padding:40px;background:#fcc062;border-radius:15px}@media only screen and (max-width:991px){.trusted-slider .slick-slide .slider-elem{padding:35px}}@media only screen and (max-width:767px){.trusted-slider .slick-slide .slider-elem{padding:25px}}@media only screen and (max-width:639px){.trusted-slider .slick-slide .slider-elem{flex-direction:column;align-items:center}}.trusted-slider .slick-slide .slider-elem__title{margin-bottom:20px;color:#fff;font-weight:700;font-size:25px}@media only screen and (max-width:767px){.trusted-slider .slick-slide .slider-elem__title{margin-bottom:12px;font-size:22px}}@media only screen and (max-width:639px){.trusted-slider .slick-slide .slider-elem__title{margin-bottom:8px;text-align:center;font-size:18px}}@media only screen and (max-width:479px){.trusted-slider .slick-slide .slider-elem__title{font-size:16px}}.trusted-slider .slick-slide .slider-elem__text{color:#fff;font-weight:500;font-size:20px;line-height:1.4}@media only screen and (max-width:767px){.trusted-slider .slick-slide .slider-elem__text{font-size:16px}}@media only screen and (max-width:639px){.trusted-slider .slick-slide .slider-elem__text{font-size:15px}}@media only screen and (max-width:479px){.trusted-slider .slick-slide .slider-elem__text{font-size:14px}}.trusted-slider .slick-slide .slider-elem__content{display:flex;flex-direction:column;justify-content:center}.trusted-slider .slick-slide .slider-elem__img{height:178px;width:178px;-o-object-fit:cover;object-fit:cover;border-radius:50%;margin-right:52px}@media only screen and (max-width:991px){.trusted-slider .slick-slide .slider-elem__img{margin-right:35px}}@media only screen and (max-width:767px){.trusted-slider .slick-slide .slider-elem__img{height:100px;width:100px;margin-right:20px}}@media only screen and (max-width:639px){.trusted-slider .slick-slide .slider-elem__img{height:76px;width:76px;margin-right:0;margin-bottom:14px}}.trusted-slider .slick-arrow{position:absolute;top:50%;background-color:rgba(252,192,98,.95);transform:translateY(-50%)}.trusted-slider .slick-arrow.slick-next{right:8%}.trusted-slider .slick-arrow.slick-prev{left:8%}.trusted-slider--dark .slick-slide .slider-elem{background:var(--v-darkLight-base)}.trusted-slider--dark .slick-slide .slider-elem__title{color:#f9af48}.trusted-slider--dark .slick-arrow{background-color:#000}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:['trusted-slider', { 'trusted-slider--dark': _vm.dark }]},[_c('client-only',[_c('VueSlickCarousel',_vm._b({},'VueSlickCarousel',_vm.sliderSettings,false),_vm._l((_vm.data),function(item,index){return _c('div',{key:index},[_c('div',{staticClass:\"slider-elem\"},[_c('div',{staticClass:\"slider-elem__img-wrap\"},[_c('img',{staticClass:\"slider-elem__img\",attrs:{\"src\":_vm.getSrcAvatar(item.profileImage),\"alt\":\"\"}})]),_vm._v(\" \"),_c('div',{staticClass:\"slider-elem__content\"},[_c('div',{staticClass:\"slider-elem__title\"},[_vm._v(_vm._s(item.information))]),_vm._v(\" \"),_c('div',{staticClass:\"slider-elem__text\"},[_vm._v(_vm._s(item.description))])])])])}),0)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport VueSlickCarousel from 'vue-slick-carousel'\nimport 'vue-slick-carousel/dist/vue-slick-carousel.css'\n\nexport default {\n  name: 'TestimonialsSlider',\n  components: { VueSlickCarousel },\n  props: {\n    data: {\n      type: Array,\n      default: () => [],\n    },\n    dark: {\n      type: Boolean,\n      default: true,\n    },\n  },\n  data() {\n    return {\n      slider: null,\n      sliderSettings: {\n        centerMode: true,\n        centerPadding: '210px',\n        dots: false,\n        focusOnSelect: true,\n        infinite: true,\n        speed: 800,\n        slidesToShow: 1,\n        slidesToScroll: 1,\n        responsive: [\n          {\n            breakpoint: 1099,\n            settings: {\n              centerPadding: '170px',\n            },\n          },\n          {\n            breakpoint: 991,\n            settings: {\n              arrows: false,\n              centerPadding: '80px',\n            },\n          },\n          {\n            breakpoint: 639,\n            settings: {\n              arrows: false,\n              centerPadding: '60px',\n            },\n          },\n          {\n            breakpoint: 480,\n            settings: {\n              arrows: false,\n              centerPadding: '45px',\n            },\n          },\n        ],\n      },\n    }\n  },\n  methods: {\n    getSrcAvatar(image, defaultImage = 'avatar.png') {\n      return image || require(`~/assets/images/homepage/${defaultImage}`)\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestimonialsSlider.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TestimonialsSlider.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TestimonialsSlider.vue?vue&type=template&id=0dd1e3c5&\"\nimport script from \"./TestimonialsSlider.vue?vue&type=script&lang=js&\"\nexport * from \"./TestimonialsSlider.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./TestimonialsSlider.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"014cf678\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AADA;AAFA;AAOA;AACA;AACA;AACA;AAFA;AAFA;AAQA;AACA;AACA;AACA;AAFA;AAFA;AAQA;AACA;AACA;AACA;AAFA;AAFA;AA9BA;AAFA;AA0CA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAzDA;;AC5BA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}