{"version": 3, "file": "components/classroom-toolbar.js", "sources": ["webpack:///./mixins/UploadFiles.vue", "webpack:///./mixins/UploadFiles.vue?903b", "webpack:///./mixins/UploadFiles.vue?17e1", "webpack:///./components/classroom/Toolbar.vue?fc8a", "webpack:///./components/classroom/Toolbar.vue?3a91", "webpack:///./components/classroom/Toolbar.vue?603e", "webpack:///./components/classroom/Toolbar.vue?8c21", "webpack:///./components/classroom/Toolbar.vue", "webpack:///./components/classroom/Toolbar.vue?474b", "webpack:///./components/classroom/Toolbar.vue?edac", "webpack:///./mixins/SetTool.vue", "webpack:///./mixins/SetTool.vue?42f1", "webpack:///./mixins/SetTool.vue?6f86", "webpack:///./mixins/StatusOnline.vue", "webpack:///./mixins/StatusOnline.vue?c046", "webpack:///./mixins/StatusOnline.vue?62a8"], "sourcesContent": ["\nimport { getFileExtension } from '~/helpers'\nimport { MAX_FILE_SIZE } from '~/helpers/constants'\n\nexport default {\n  computed: {\n    lessonId() {\n      return this.$store.state.classroom.lessonId\n    },\n    role() {\n      return this.$store.getters['classroom/role']\n    },\n    acceptedFiles() {\n      return this.$store.state.classroom.acceptedFiles\n    },\n  },\n  methods: {\n    async uploadFiles(files) {\n      files = [...files]\n\n      const formData = new FormData()\n\n      for (let i = 0; i <= files.length - 1; i++) {\n        const file = files[i]\n        const fileExtension = getFileExtension(file.name)\n\n        if (file.size > MAX_FILE_SIZE) {\n          await this.$store.dispatch('snackbar/error', {\n            errorMessage: this.$t('filename_size_should_be_less_than', {\n              fileName: file.name,\n              value: `${(MAX_FILE_SIZE / 8 / 1000).toFixed(0)} Mb`,\n            }),\n            timeout: 5000,\n          })\n\n          continue\n        }\n\n        if (this.acceptedFiles.officeTypes.includes(fileExtension)) {\n          const { data, fileName } = await this.$store.dispatch(\n            'classroom/convertOfficeToPdf',\n            file\n          )\n\n          formData.append(i.toString(), new Blob([data]), fileName)\n        } else {\n          formData.append(i.toString(), file)\n        }\n      }\n\n      this.$store\n        .dispatch('classroom/uploadFiles', formData)\n        .then((assets) => {\n          let offsetX = 0\n          let offsetY = 0\n\n          this.$store.commit('classroom/addAssets', assets)\n\n          assets.forEach((asset) => {\n            const item = {\n              id: asset.id,\n              lessonId: this.lessonId,\n              asset: {\n                ...asset.asset,\n                index: this.$store.state.classroom.maxIndex + 1,\n                owner: this.role,\n                top:\n                  this.$store.getters['classroom/zoomAsset'].asset.y +\n                  offsetY +\n                  100,\n                left:\n                  this.viewportWidth / 2 +\n                  this.$store.getters['classroom/zoomAsset'].asset.x +\n                  offsetX -\n                  250,\n              },\n            }\n            const ext = getFileExtension(item.asset.path)\n\n            let type\n\n            if (this.acceptedFiles?.pdfTypes.includes(ext)) {\n              type = 'pdf'\n            } else if (this.acceptedFiles?.imageTypes.includes(ext)) {\n              type = 'image'\n            } else if (this.acceptedFiles?.audioTypes.includes(ext)) {\n              type = 'audio'\n            } else {\n              return\n            }\n\n            item.asset.type = type\n\n            this.$store.commit('classroom/moveAsset', item)\n            this.$store.dispatch('classroom/moveAsset', item)\n\n            this.$socket.emit('asset-added', item)\n\n            offsetX += 50\n            offsetY += 50\n          })\n        })\n        .catch((e) => {\n          // @TODO classroom\n          // Bugsnag.notify(e)\n          throw e\n        })\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UploadFiles.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UploadFiles.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./UploadFiles.vue?vue&type=script&lang=js&\"\nexport * from \"./UploadFiles.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"20b20e0a\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Toolbar.vue?vue&type=style&index=0&id=53b5e223&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"3a5e080a\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Toolbar.vue?vue&type=style&index=0&id=53b5e223&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".toolbar[data-v-53b5e223]{position:fixed;z-index:99999!important}label.popup-load-files-label-upload[data-v-53b5e223]{margin-right:0}.toolbar-buttons-wrapper[data-v-53b5e223]{position:absolute;top:50%;right:2%;transform:translateY(-50%)}.cursor-pointer[data-v-53b5e223],.cursor-pointer *[data-v-53b5e223]{cursor:pointer!important}.toolbar-buttons[data-v-53b5e223]{margin-bottom:0;padding:8px 0}.toolbar-buttons-horizontal-file.toolbar-show[data-v-53b5e223],.toolbar-buttons-horizontal.toolbar-show[data-v-53b5e223]{display:flex!important}.toolbar-buttons li[data-v-53b5e223]{list-style:none}.toolbar-button-wrapper form[data-v-53b5e223]{display:inline-block;width:100%}.toolbar-button-wrapper-horizontal[data-v-53b5e223]{width:40px;height:40px;display:flex;justify-content:center;position:relative}.toolbar-button-wrapper-pencil>button[data-v-53b5e223]{padding:9px}.toolbar-button-wrapper-exit button[data-v-53b5e223]{padding-left:7px;padding-right:10px}.toolbar-button-wrapper-reset button[data-v-53b5e223]{padding-left:10px;padding-right:10px}.toolbar-button-wrapper-finish button[data-v-53b5e223]{padding-right:7px}.toolbar-button-wrapper-horizontal-books button[data-v-53b5e223]{padding:9px}.toolbar-button-wrapper-horizontal-laptop button[data-v-53b5e223]{padding-top:10px}.toolbar-buttons-horizontal>ul[data-v-53b5e223]{display:flex;padding:0 10px;background-color:#fff;border-radius:6px;box-shadow:0 2px 10px rgba(0,0,0,.25)}.toolbar-button-wrapper-horizontal-draw-line button[data-v-53b5e223]{padding:10px 5px 6px 10px}.toolbar-button-wrapper-horizontal-draw-pencil button[data-v-53b5e223]{padding:9px}.toolbar-button-item-hand[data-v-53b5e223]{padding-left:0}.toolbar-buttons-horizontal>ul li[data-v-53b5e223]:first-child,.toolbar-buttons-horizontal>ul li:first-child button[data-v-53b5e223]{border-bottom-left-radius:6px!important;border-top-left-radius:6px!important}.toolbar-buttons-horizontal>ul li[data-v-53b5e223]:last-child,.toolbar-buttons-horizontal>ul li:last-child button[data-v-53b5e223]{border-bottom-right-radius:6px!important;border-top-right-radius:6px!important}#toolbar-switch[data-v-53b5e223]{border-top-left-radius:4px;border-top-right-radius:4px}.toolbar--student .toolbar-button-item.selected:not(:disabled) svg[data-v-53b5e223],.toolbar--student .toolbar-button-wrapper-horizontal:hover svg[data-v-53b5e223],.toolbar--student .toolbar-button-wrapper:hover>button:enabled>svg[data-v-53b5e223],.toolbar--student .toolbar-button-wrapper:hover>form>button:enabled>svg[data-v-53b5e223]{color:var(--v-studentColor-base)!important}.toolbar--teacher .toolbar-button-item.selected:not(:disabled) svg[data-v-53b5e223],.toolbar--teacher .toolbar-button-wrapper-horizontal:hover svg[data-v-53b5e223],.toolbar--teacher .toolbar-button-wrapper:hover>button:enabled>svg[data-v-53b5e223],.toolbar--teacher .toolbar-button-wrapper:hover>form>button:enabled>svg[data-v-53b5e223]{color:var(--v-teacherColor-base)!important}.toolbar-button-wrapper .toolbar-button-item:disabled svg[data-v-53b5e223]{color:#c6c6c6!important;fill:#c6c6c6!important;stroke:#c6c6c6!important}.toolbar-button-item svg[data-v-53b5e223]{color:var(--v-darkLight-base)}.hover-btn-info-horizontal[data-v-53b5e223]{top:-20px;right:-60%}.toolbar-button-replace+.hover-btn-info[data-v-53b5e223]{top:40%}.selected[data-v-53b5e223]{border-bottom:none}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:['toolbar', (\"toolbar--\" + _vm.role)],style:(_vm.style)},[_vm._ssrNode(\"<ul id=\\\"toolbar-buttons\\\" class=\\\"toolbar-buttons\\\" data-v-53b5e223><li class=\\\"toolbar-button-wrapper\\\" data-v-53b5e223><button id=\\\"toolbar-switch\\\" data-toolbar-default-cursor\"+(_vm._ssrAttr(\"disabled\",_vm.isLockedForStudent))+(_vm._ssrClass(null,[\n          'toolbar-button-item toolbar-button-pointer cursor-pointer',\n          { selected: _vm.currentTool === 'pointer' } ]))+\" data-v-53b5e223><svg width=\\\"32\\\" height=\\\"34\\\" viewBox=\\\"0 0 32 34\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#pointer\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info\\\" data-v-53b5e223>\"+_vm._ssrEscape(_vm._s(_vm.$t('default_cursor')))+\"</div></li> <li class=\\\"toolbar-button-wrapper toolbar-button-wrapper-pencil\\\" data-v-53b5e223><button\"+(_vm._ssrAttr(\"disabled\",_vm.isLockedForStudent))+(_vm._ssrClass(null,[\n          'toolbar-button-item toolbar-button-hand cursor-pointer',\n          {\n            selected:\n              _vm.currentTool === 'line' ||\n              _vm.currentTool === 'circle' ||\n              _vm.currentTool === 'triangle' ||\n              _vm.currentTool === 'square' ||\n              _vm.currentTool === 'pen',\n          } ]))+\" data-v-53b5e223><svg width=\\\"33\\\" height=\\\"33\\\" viewBox=\\\"0 0 33 33\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#pencil\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info\\\" data-v-53b5e223>\"+_vm._ssrEscape(_vm._s(_vm.$t('drawing')))+\"</div> <div\"+(_vm._ssrClass(null,[\n          'toolbar-buttons-horizontal',\n          { 'toolbar-show': _vm.currentHorizontalMenu === 'toolbar-horizontal' } ]))+\" data-v-53b5e223><ul data-v-53b5e223><li class=\\\"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-draw-line\\\" data-v-53b5e223><button data-toolbar-tool-line\"+(_vm._ssrClass(null,[\n                'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',\n                { selected: _vm.currentTool === 'line' } ]))+\" data-v-53b5e223><svg width=\\\"39\\\" height=\\\"37\\\" viewBox=\\\"0 0 39 37\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#draw-line\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info hover-horizontal-button\\\" data-v-53b5e223>\"+_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('draw_line'))+\"\\n            \")+\"</div></li> <li class=\\\"toolbar-button-wrapper-horizontal\\\" data-v-53b5e223><button data-toolbar-tool-circle\"+(_vm._ssrClass(null,[\n                'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',\n                { selected: _vm.currentTool === 'circle' } ]))+\" data-v-53b5e223><svg width=\\\"36\\\" height=\\\"37\\\" viewBox=\\\"0 0 39 40\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#draw-circle\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info hover-horizontal-button\\\" data-v-53b5e223>\"+_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('draw_circle'))+\"\\n            \")+\"</div></li> <li class=\\\"toolbar-button-wrapper-horizontal\\\" data-v-53b5e223><button data-toolbar-tool-triangle\"+(_vm._ssrClass(null,[\n                'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',\n                { selected: _vm.currentTool === 'triangle' } ]))+\" data-v-53b5e223><svg width=\\\"41\\\" height=\\\"34\\\" viewBox=\\\"0 0 41 34\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#draw-triangle\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info hover-horizontal-button\\\" data-v-53b5e223>\"+_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('draw_triangle'))+\"\\n            \")+\"</div></li> <li class=\\\"toolbar-button-wrapper-horizontal\\\" data-v-53b5e223><button data-toolbar-tool-square\"+(_vm._ssrClass(null,[\n                'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',\n                { selected: _vm.currentTool === 'square' } ]))+\" data-v-53b5e223><svg width=\\\"36\\\" height=\\\"38\\\" viewBox=\\\"0 0 36 38\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#draw-square\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info hover-horizontal-button\\\" data-v-53b5e223>\"+_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('draw_square'))+\"\\n            \")+\"</div></li> <li class=\\\"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-draw-pencil\\\" data-v-53b5e223><button data-toolbar-tool-pen\"+(_vm._ssrClass(null,[\n                'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',\n                { selected: _vm.currentTool === 'pen' } ]))+\" data-v-53b5e223><svg width=\\\"33\\\" height=\\\"33\\\" viewBox=\\\"0 0 33 33\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#pencil\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info hover-horizontal-button\\\" data-v-53b5e223>\"+_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('enable_drawing_tool'))+\"\\n            \")+\"</div></li></ul></div></li> <li class=\\\"toolbar-button-wrapper\\\" data-v-53b5e223><button data-toolbar-eraser\"+(_vm._ssrAttr(\"disabled\",_vm.isLockedForStudent))+(_vm._ssrClass(null,[\n          'toolbar-button-item cursor-pointer',\n          { selected: _vm.currentTool === 'eraser' } ]))+\" data-v-53b5e223><svg width=\\\"35\\\" height=\\\"31\\\" viewBox=\\\"0 0 35 31\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#lastic\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info\\\" data-v-53b5e223>\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.$t('enable_erasing_tool'))+\"\\n      \")+\"</div></li> <li class=\\\"toolbar-button-wrapper\\\" data-v-53b5e223><button id=\\\"toolbar-button-video\\\" data-toolbar-add-video\"+(_vm._ssrAttr(\"disabled\",_vm.isLockedForStudent))+\" class=\\\"toolbar-button-item cursor-pointer\\\" data-v-53b5e223><svg width=\\\"39\\\" height=\\\"31\\\" viewBox=\\\"0 0 39 31\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#play\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info\\\" data-v-53b5e223>\"+_vm._ssrEscape(_vm._s(_vm.$t('add_video')))+\"</div></li> \"+((_vm.isTeacher)?(\"<li class=\\\"toolbar-button-wrapper\\\" data-v-53b5e223><button data-toolbar-buzz-student\"+(_vm._ssrAttr(\"disabled\",_vm.alertDisabled))+\" class=\\\"toolbar-button-item cursor-pointer\\\" data-v-53b5e223><svg width=\\\"35\\\" height=\\\"38\\\" viewBox=\\\"0 0 35 38\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#ring\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info\\\" data-v-53b5e223>\"+_vm._ssrEscape(_vm._s(_vm.$t('buzz_student')))+\"</div></li>\"):\"<!---->\")+\" <li class=\\\"toolbar-button-wrapper\\\" data-v-53b5e223><button class=\\\"toolbar-button-item toolbar-button-hand cursor-pointer\\\" data-v-53b5e223><svg width=\\\"29\\\" height=\\\"38\\\" viewBox=\\\"0 0 29 38\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#library\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info\\\" data-v-53b5e223>\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.$t('library'))+\"\\n      \")+\"</div> <div\"+(_vm._ssrClass(null,[\n          'toolbar-buttons-horizontal toolbar-buttons-horizontal-file',\n          {\n            'toolbar-show':\n              _vm.currentHorizontalMenu === 'toolbar-horizontal-file',\n          } ]))+\" data-v-53b5e223><ul data-v-53b5e223><li class=\\\"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-books\\\" data-v-53b5e223><button id=\\\"load-files-library\\\" data-toolbar-library class=\\\"toolbar-button-item toolbar-button-item-horizontal cursor-pointer\\\" data-v-53b5e223><svg width=\\\"38\\\" height=\\\"38\\\" viewBox=\\\"0 0 38 38\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#books\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info hover-horizontal-button\\\" data-v-53b5e223>\"+_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('select_from_library'))+\"\\n            \")+\"</div></li> <li class=\\\"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-laptop\\\" data-v-53b5e223><button data-toolbar-computer class=\\\"toolbar-button-item toolbar-button-item-horizontal cursor-pointer\\\" data-v-53b5e223><label class=\\\"popup-load-files-label-upload popup-load-files-label-upload-laptop\\\" data-v-53b5e223><svg width=\\\"41\\\" height=\\\"34\\\" viewBox=\\\"0 0 41 34\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#laptop\")))+\" data-v-53b5e223></use></svg> <input id=\\\"upload-library-files-laptop\\\" type=\\\"file\\\" multiple=\\\"multiple\\\"\"+(_vm._ssrAttr(\"accept\",_vm.acceptedFilesStr))+\" class=\\\"popup-load-files-btn-upload\\\" data-v-53b5e223></label></button> <div class=\\\"hover-btn-info hover-horizontal-button\\\" data-v-53b5e223>\"+_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('upload_from_computer'))+\"\\n            \")+\"</div></li></ul></div></li> \"+((_vm.isTeacher)?(\"<li class=\\\"toolbar-button-wrapper\\\" data-v-53b5e223><button data-toolbar-lock class=\\\"toolbar-button-item cursor-pointer\\\" data-v-53b5e223><svg width=\\\"38\\\" height=\\\"50\\\" viewBox=\\\"0 0 38 50\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#\" + (_vm.isLocked ? 'lock' : 'unlock'))))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info\\\" data-v-53b5e223>\"+((_vm.isLocked)?(_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('enable_moving_resizing_drawing_for_student'))+\"\\n        \")):(_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('disable_moving_resizing_drawing_for_student'))+\"\\n        \")))+\"</div></li>\"):\"<!---->\")+\" <li class=\\\"toolbar-button-wrapper toolbar-button-wrapper-reset\\\" data-v-53b5e223><button data-toolbar-reset\"+(_vm._ssrAttr(\"disabled\",_vm.isLockedForStudent))+\" class=\\\"toolbar-button-item cursor-pointer\\\" data-v-53b5e223><svg width=\\\"36\\\" height=\\\"36\\\" viewBox=\\\"0 0 37 37\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#restore\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info\\\" data-v-53b5e223>\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.$t('restore_whiteboard_video_to_original_positions'))+\"\\n      \")+\"</div></li> <li class=\\\"toolbar-button-wrapper toolbar-button-wrapper-exit\\\" data-v-53b5e223><button data-toolbar-exit class=\\\"toolbar-button-item cursor-pointer\\\" data-v-53b5e223><svg width=\\\"36\\\" height=\\\"36\\\" viewBox=\\\"0 0 37 37\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#exit\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info\\\" data-v-53b5e223>\"+_vm._ssrEscape(_vm._s(_vm.$t('exit_class')))+\"</div></li> \"+((_vm.isTeacher)?(\"<li class=\\\"toolbar-button-wrapper\\\" data-v-53b5e223><button\"+(_vm._ssrAttr(\"disabled\",_vm.isLessonFinished || !_vm.isFinishedAllowed))+\" class=\\\"toolbar-button-item toolbar-button-hand cursor-pointer\\\" data-v-53b5e223><svg width=\\\"43\\\" height=\\\"38\\\" viewBox=\\\"0 0 43 38\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#tick\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info\\\" data-v-53b5e223>\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm.$t(_vm.isLessonFinished ? 'class_finished' : 'finish_class'))+\"\\n      \")+\"</div> <div\"+(_vm._ssrClass(null,[\n          'toolbar-buttons-horizontal toolbar-buttons-horizontal-file',\n          {\n            'toolbar-show':\n              _vm.currentHorizontalMenu === 'toolbar-horizontal-finish',\n          } ]))+\" data-v-53b5e223><ul data-v-53b5e223><li class=\\\"toolbar-button-wrapper-horizontal toolbar-button-wrapper-finish\\\" data-v-53b5e223><button data-toolbar-finish type=\\\"submit\\\" class=\\\"toolbar-button-item toolbar-button-item-horizontal cursor-pointer\\\" data-v-53b5e223><svg width=\\\"43\\\" height=\\\"38\\\" viewBox=\\\"0 0 43 38\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#tick\")))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info hover-horizontal-button\\\" data-v-53b5e223>\"+_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('finish_class'))+\"\\n            \")+\"</div></li></ul></div></li>\"):\"<!---->\")+\" \"+((_vm.isStudent)?(\"<li class=\\\"toolbar-button-wrapper\\\" data-v-53b5e223><button disabled=\\\"disabled\\\" class=\\\"toolbar-button-item cursor-pointer\\\" data-v-53b5e223><svg width=\\\"38\\\" height=\\\"50\\\" viewBox=\\\"0 0 38 50\\\" class=\\\"toolbar-button-icon\\\" data-v-53b5e223><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/toolbar.svg')) + \"#\" + (_vm.isLocked ? 'lock' : 'unlock'))))+\" data-v-53b5e223></use></svg></button> <div class=\\\"hover-btn-info\\\" data-v-53b5e223>\"+((_vm.isLocked)?(_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('moving_resizing_drawing_are_disabled'))+\"\\n        \")):(_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('classroom_controls_are_unlocked'))+\"\\n        \")))+\"</div></li>\"):\"<!---->\")+\"</ul>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {\n  defaultWidth,\n  mainCanvasWidth,\n  mainCanvasHeight,\n} from '~/helpers/constants'\nimport SetTool from '~/mixins/SetTool'\nimport UploadFiles from '~/mixins/UploadFiles'\nimport StatusOnline from '~/mixins/StatusOnline'\n\nexport default {\n  name: 'Toolbar',\n  mixins: [SetTool, UploadFiles, StatusOnline],\n  props: {\n    studentId: {\n      type: String,\n      required: true,\n    },\n    file: {\n      type: Object,\n      required: true,\n    },\n    viewportWidth: {\n      type: Number,\n      required: true,\n    },\n    viewportHeight: {\n      type: Number,\n      required: true,\n    },\n    scale: {\n      type: Number,\n      default: 1,\n    },\n    minZoom: {\n      type: Number,\n      required: true,\n    },\n    isFinishedAllowed: {\n      type: Boolean,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      buzzed: false,\n      currentTool: 'pointer',\n      currentHorizontalMenu: null,\n      offset: 5,\n    }\n  },\n  computed: {\n    isCanvasOversizeX() {\n      return mainCanvasWidth > this.viewportWidth\n    },\n    isScaledCanvasOversizeX() {\n      return mainCanvasWidth * this.scale > this.viewportWidth\n    },\n    isCanvasOversizeY() {\n      return mainCanvasHeight > this.viewportHeight\n    },\n    isScaledCanvasOversizeY() {\n      return mainCanvasHeight * this.scale > this.viewportHeight\n    },\n    style() {\n      return {\n        bottom: this.isScaledCanvasOversizeY\n          ? '10px'\n          : `${\n              this.viewportHeight -\n              mainCanvasHeight * this.scale +\n              this.offset * 2\n            }px`,\n        right: this.isScaledCanvasOversizeX\n          ? '10px'\n          : `${\n              this.viewportWidth -\n              mainCanvasWidth * this.scale +\n              this.offset * 2\n            }px`,\n      }\n    },\n    studentStatus() {\n      // return this.$store.state.socket.connectedUserIds.includes(this.studentId)\n      let status = 'offline'\n\n      if (\n        Object.prototype.hasOwnProperty.call(\n          this.userStatuses,\n          this.studentId?.toString()\n        )\n      ) {\n        status = this.userStatuses[this.studentId]\n      }\n\n      return status\n    },\n    alertDisabled() {\n      return this.buzzed || this.studentStatus !== 'online'\n    },\n    maxIndex() {\n      return this.$store.state.classroom.maxIndex + 100\n    },\n    isLocked() {\n      return this.file?.asset?.isLocked\n    },\n    isTeacher() {\n      return this.$store.getters['user/isTeacher']\n    },\n    isStudent() {\n      return this.$store.getters['user/isStudent']\n    },\n    isLockedForStudent() {\n      return this.isLocked && this.isStudent\n    },\n    lessonId() {\n      return this.$store.state.classroom.lessonId\n    },\n    isLessonFinished() {\n      return this.$store.getters['classroom/isLessonFinished']\n    },\n    defaultZoomIndex() {\n      return this.minZoom > 1 ? this.minZoom : 1\n    },\n    acceptedFilesStr() {\n      return this.$store.getters['classroom/acceptedFilesStr']\n    },\n  },\n  watch: {\n    isLockedForStudent(newValue, oldValue) {\n      if (newValue) {\n        this.resetCurrentValues()\n        this.$store.commit('classroom/closeVideoInput')\n      }\n    },\n  },\n  beforeMount() {\n    this.arrStatusId = [this.studentId]\n\n    this.refreshStatusOnline()\n  },\n  beforeDestroy() {\n    this.resetCurrentValues()\n  },\n  methods: {\n    selectToolClickHandler(toolName, cursorName) {\n      this.currentTool = toolName\n      this.currentHorizontalMenu = null\n\n      this.setTool(toolName, cursorName)\n    },\n    uploadFromComputer(event) {\n      this.currentHorizontalMenu = null\n\n      this.uploadFiles(event.target.files)\n    },\n    buzz() {\n      this.buzzed = true\n\n      setTimeout(() => {\n        this.buzzed = false\n      }, 30000)\n\n      this.$store.dispatch('classroom/buzz', this.lessonId)\n    },\n    reset() {\n      let height, ratio\n      let i = 1\n      let offsetX = 0\n      let offsetY = 0\n\n      this.$store.state.classroom.assets.slice(0).forEach((asset) => {\n        const _asset = { ...asset.asset }\n\n        i++\n\n        switch (_asset.type) {\n          case 'shape':\n          case 'lock':\n            break\n          case 'editor':\n            _asset.width =\n              (this.isCanvasOversizeX ? this.viewportWidth : mainCanvasWidth) *\n              0.66\n\n            height =\n              (this.isCanvasOversizeY\n                ? this.viewportHeight\n                : mainCanvasHeight) * 0.8\n\n            if (height > 1200) {\n              height = 1200\n            }\n\n            if (height < 400) {\n              height = 400\n            }\n\n            _asset.height = height - this.offset * 2\n            _asset.top = this.offset\n            _asset.left = this.offset\n            _asset.index = 1\n            break\n          case 'whereby':\n            _asset.width = 400\n            _asset.height = 300\n            _asset.top = this.offset\n            _asset.left =\n              (this.isCanvasOversizeX ? this.viewportWidth : mainCanvasWidth) -\n              _asset.width -\n              this.offset\n            _asset.index = i\n            break\n          case 'pdf':\n          case 'image':\n          case 'video':\n          case 'audio':\n            ratio = defaultWidth / asset.asset.width\n\n            _asset.width = defaultWidth\n            _asset.height = _asset.height * ratio\n            _asset.top =\n              this.$store.getters['classroom/zoomAsset'].asset.y + offsetY + 100\n            _asset.left =\n              this.viewportWidth / 2 +\n              this.$store.getters['classroom/zoomAsset'].asset.x +\n              offsetX -\n              250\n            _asset.index = i\n            offsetX += 50\n            offsetY += 50\n            break\n          case 'zoom':\n            _asset.zoomIndex = this.defaultZoomIndex\n            _asset.x = 0\n            _asset.y = 0\n            break\n          default:\n        }\n\n        this.$store.commit('classroom/moveAsset', {\n          id: asset.id,\n          asset: _asset,\n        })\n        this.$store.dispatch('classroom/moveAsset', {\n          id: asset.id,\n          lessonId: asset.lessonId,\n          asset: _asset,\n        })\n      })\n    },\n    toggleVideoInput() {\n      this.$store.commit('classroom/toggleVideoInput')\n    },\n    openLibrary() {\n      this.currentHorizontalMenu = null\n\n      this.$store.commit('classroom/toggleLibrary')\n    },\n    toggleStudentRoomStatus() {\n      const asset = { isLocked: !this.isLocked }\n\n      this.$store.commit('classroom/moveAsset', {\n        id: this.file.id,\n        asset,\n      })\n\n      this.$store.dispatch('classroom/moveAsset', {\n        id: this.file.id,\n        lessonId: this.lessonId,\n        asset,\n      })\n    },\n    resetCurrentValues() {\n      this.currentTool = 'pointer'\n      this.currentHorizontalMenu = null\n    },\n    finishLesson() {\n      this.$store\n        .dispatch('lesson/finishLesson', this.lessonId)\n        .then(() => {\n          this.exitLesson()\n        })\n        .catch((e) => {\n          this.$store.dispatch('snackbar/error')\n\n          console.info(e)\n        })\n    },\n    exitLesson() {\n      window.location = '/user/lessons' // window.location is needed to trigger event 'user-left-classroom'\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Toolbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Toolbar.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Toolbar.vue?vue&type=template&id=53b5e223&scoped=true&\"\nimport script from \"./Toolbar.vue?vue&type=script&lang=js&\"\nexport * from \"./Toolbar.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./Toolbar.vue?vue&type=style&index=0&id=53b5e223&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"53b5e223\",\n  \"0789be0a\"\n  \n)\n\nexport default component.exports", "\nexport default {\n  computed: {\n    role() {\n      return this.$store.getters['classroom/role']\n    },\n  },\n  methods: {\n    setTool(toolName, cursorName) {\n      this.$store.commit(\n        'classroom/enableContainerComponent',\n        toolName === 'pointer'\n      )\n      this.$socket.emit('cursor-moved', {\n        tool: toolName,\n        cursor: cursorName.replace(/(-cursor|cursor-)/i, ''),\n        lessonId: this.$store.state.classroom.lessonId,\n      })\n\n      this.$store.commit('classroom/setUserTool', toolName)\n      this.$store.commit('classroom/setUserCursor', cursorName)\n\n      const el = document.body\n      const classList = el.classList\n\n      this.removeCursors(classList)\n      el.classList.add(`${this.role}-${cursorName}`)\n\n      this.classList = el.classList\n    },\n    removeCursors(classList) {\n      classList.forEach((item) => {\n        if (item.includes('cursor')) {\n          document.body.classList.remove(item)\n        }\n      })\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./SetTool.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./SetTool.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./SetTool.vue?vue&type=script&lang=js&\"\nexport * from \"./SetTool.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"2e32be2e\"\n  \n)\n\nexport default component.exports", "\nexport default {\n  data() {\n    return {\n      timeoutId: null,\n      userStatuses: {},\n      arrStatusId: [],\n    }\n  },\n  computed: {\n    preparedArr() {\n      return [...new Set(this.arrStatusId)]\n    },\n  },\n  mounted() {\n    this.timeoutId = window.setInterval(() => {\n      this.refreshStatusOnline()\n\n      if (!this.arrStatusId.length) {\n        this.clearInterval()\n      }\n    }, 10000)\n  },\n  beforeDestroy() {\n    if (this.timeoutId) {\n      this.clearInterval()\n    }\n  },\n  methods: {\n    refreshStatusOnline() {\n      if (this.arrStatusId.length) {\n        this.$store\n          .dispatch('user/refreshStatusOnline', this.preparedArr)\n          .then((res) => (this.userStatuses = res))\n      }\n    },\n    clearInterval() {\n      window.clearInterval(this.timeoutId)\n      this.timeoutId = null\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StatusOnline.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StatusOnline.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./StatusOnline.vue?vue&type=script&lang=js&\"\nexport * from \"./StatusOnline.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"2b0aab01\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;;;;;;;;;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAVA;AAWA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AALA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;AAEA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AAIA;AARA;AAHA;AAkBA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AA5FA;AAZA;;ACJA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACrCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAzBA;AACA;AA6BA;AACA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAOA;AARA;AAgBA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AA5EA;AA6EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA;AACA;AAOA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAIA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAKA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AA7DA;AACA;AA+DA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAFA;AAKA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AApJA;AAtIA;;ACjkBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;ACtBA;AACA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AAIA;AACA;AACA;AACA;AAHA;AAMA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA9BA;AANA;;ACDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjBA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAZA;AA3BA;;ACDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}