exports.ids = [147];
exports.modules = {

/***/ 1508:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/messages/_recipientId/new/index.vue?vue&type=script&lang=js&
/* harmony default export */ var newvue_type_script_lang_js_ = ({
  middleware: ['authenticated', 'threadExisted']
});
// CONCATENATED MODULE: ./pages/user/messages/_recipientId/new/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var _recipientId_newvue_type_script_lang_js_ = (newvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./pages/user/messages/_recipientId/new/index.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  _recipientId_newvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "646f3464"
  
)

/* harmony default export */ var _recipientId_new = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=index.js.map