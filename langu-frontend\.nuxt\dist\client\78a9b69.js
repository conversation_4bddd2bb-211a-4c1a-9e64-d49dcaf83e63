(window.webpackJsonp=window.webpackJsonp||[]).push([[82],{1415:function(e,t,n){var content=n(1455);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("12bcaf99",content,!0,{sourceMap:!1})},1421:function(e,t,n){"use strict";n.r(t);n(63);var r={name:"LessonTimeNotice",props:{dark:{type:Boolean,default:!1},oneLine:{type:Boolean,default:!1}},data:function(){return{currentTime:null,intervalId:null}},computed:{isUserLogged:function(){return this.$store.getters["user/isUserLogged"]},timezone:function(){return this.$store.getters["user/timeZone"]}},created:function(){var e=this;this.setCurrentTime(),this.intervalId=setInterval((function(){e.setCurrentTime()}),1e4)},beforeDestroy:function(){window.clearInterval(this.intervalId)},methods:{setCurrentTime:function(){this.currentTime=this.$dayjs().tz(this.timezone)},showLoginSidebarClickHandler:function(){this.$emit("show-login-sidebar"),this.$store.commit("SET_IS_LOGIN_SIDEBAR",!0)}}},o=(n(1454),n(22)),component=Object(o.a)(r,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.currentTime?n("div",{class:["time-notice",{"time-notice--dark":e.dark}]},[e._v("\n  "+e._s(e.$t("lesson_times_displayed_based_on_your_current_local_time"))+":\n  "+e._s(e.currentTime.format("LT"))+" ("+e._s(e.currentTime.format("z"))+").\n  "),e.isUserLogged?e._e():[e.oneLine?e._e():n("br"),e._v(" "),n("span",{class:{"text--gradient":!e.dark},on:{click:function(t){return t.stopPropagation(),t.preventDefault(),e.showLoginSidebarClickHandler.apply(null,arguments)}}},[e._v("\n      "+e._s(e.$t("log_in"))+"\n    ")]),e._v("\n    "+e._s(e.$t("to_change_your_time_zone"))+".\n  ")]],2):e._e()}),[],!1,null,"372f019a",null);t.default=component.exports},1454:function(e,t,n){"use strict";n(1415)},1455:function(e,t,n){var r=n(18)(!1);r.push([e.i,'.time-notice[data-v-372f019a]{padding-bottom:1px}.time-notice span[data-v-372f019a]{display:inline-block;cursor:pointer;transition:color .3s}.time-notice span.text--gradient[data-v-372f019a]{position:relative}.time-notice span.text--gradient[data-v-372f019a]:after{content:"";position:absolute;width:100%;height:1px;left:0;bottom:-1px;background:linear-gradient(75deg,var(--v-success-base),var(--v-primary-base))}.time-notice--dark span[data-v-372f019a]{color:#fff}.time-notice--dark span[data-v-372f019a]:hover{color:var(--v-success-base)}',""]),e.exports=r}}]);