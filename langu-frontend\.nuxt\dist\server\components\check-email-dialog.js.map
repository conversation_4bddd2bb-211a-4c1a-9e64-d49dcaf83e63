{"version": 3, "file": "components/check-email-dialog.js", "sources": ["webpack:///./components/CheckEmailDialog.vue?cd6e", "webpack:///./components/CheckEmailDialog.vue?a730", "webpack:///./components/CheckEmailDialog.vue?9e5b", "webpack:///./components/CheckEmailDialog.vue?ba64", "webpack:///./components/CheckEmailDialog.vue", "webpack:///./components/CheckEmailDialog.vue?a350", "webpack:///./components/CheckEmailDialog.vue?10d3"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckEmailDialog.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"03106de9\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckEmailDialog.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".check-email-title{color:var(--v-dark-base)!important;font-size:20px;font-weight:600}@media only screen and (max-width:639px){.check-email-title{text-align:center;font-size:18px}}.check-email-button{text-align:right}@media only screen and (max-width:639px){.check-email-button{text-align:center}}.check-email-button .v-btn{border-radius:16px!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',{attrs:{\"dialog\":_vm.showCheckEmailDialog,\"hide-close-button\":\"\",\"max-width\":\"418\",\"custom-class\":\"check-email\"}},[_c('div',[_c('div',{staticClass:\"check-email-title\"},[_vm._v(\"\\n      \"+_vm._s(_vm.$t('almost_there'))+\"\\n    \")]),_vm._v(\" \"),_c('div',{staticClass:\"check-email-text mt-3\"},[_vm._v(\"\\n      \"+_vm._s(_vm.$t(\n          'please_check_your_email_to_verify_your_email_address_and_create_password'\n        ))+\"\\n    \")]),_vm._v(\" \"),_c('div',{staticClass:\"check-email-button mt-4\"},[_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"orange\",\"min-width\":\"178\",\"x-large\":\"\"},on:{\"click\":_vm.closeCheckEmailDialog}},[_vm._v(\"\\n        OK\\n      \")])],1)])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport LDialog from '~/components/LDialog'\n\nexport default {\n  name: 'CheckEmailDialog',\n  components: { LDialog },\n  props: {\n    showCheckEmailDialog: {\n      type: Boolean,\n      required: true,\n    },\n  },\n  methods: {\n    closeCheckEmailDialog() {\n      this.$emit('close')\n      this.$router.replace({ path: '/teacher-listing/1' })\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckEmailDialog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./CheckEmailDialog.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./CheckEmailDialog.vue?vue&type=template&id=957ee8b4&\"\nimport script from \"./CheckEmailDialog.vue?vue&type=script&lang=js&\"\nexport * from \"./CheckEmailDialog.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./CheckEmailDialog.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"a7672618\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {LDialog: require('D:/languworks/langu-frontend/components/LDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\ninstallComponents(component, {VBtn})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;;;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AADA;AAMA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AALA;AATA;;ACpCA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}