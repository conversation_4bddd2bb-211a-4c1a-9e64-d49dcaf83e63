exports.ids = [164];
exports.modules = {

/***/ 1065:
/***/ (function(module, exports, __webpack_require__) {

var map = {
	"./illustration-1.svg": 550,
	"./illustration-10.svg": 551,
	"./illustration-11.svg": 552,
	"./illustration-12.svg": 553,
	"./illustration-13.svg": 554,
	"./illustration-14.svg": 555,
	"./illustration-15.svg": 556,
	"./illustration-16.svg": 557,
	"./illustration-17.svg": 558,
	"./illustration-18.svg": 559,
	"./illustration-19.svg": 560,
	"./illustration-2.svg": 561,
	"./illustration-20.svg": 562,
	"./illustration-21.svg": 563,
	"./illustration-22.svg": 564,
	"./illustration-3.svg": 565,
	"./illustration-4.svg": 566,
	"./illustration-5.svg": 567,
	"./illustration-6.svg": 568,
	"./illustration-7.svg": 569,
	"./illustration-8.svg": 570,
	"./illustration-9.svg": 571
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = 1065;

/***/ }),

/***/ 1263:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1360);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("65bba574", content, true, context)
};

/***/ }),

/***/ 1359:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CourseItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1263);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CourseItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CourseItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CourseItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CourseItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1360:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".course-item{position:relative;display:flex;min-height:222px;margin-bottom:16px;padding:28px 0 28px 40px;border-radius:16px;color:var(--v-greyDark-base);overflow:hidden}@media only screen and (max-width:1215px){.course-item{padding:28px 0 28px 22px}}@media only screen and (max-width:991px){.course-item{margin-bottom:24px;padding:24px 16px}}.course-item:nth-child(3n+1):before{background:linear-gradient(118.56deg,var(--v-success-base) 3.04%,var(--v-primary-base) 27.45%),#c4c4c4;opacity:.1}.course-item:nth-child(3n+2):before{background:linear-gradient(122.42deg,#d67b7f,#f9c176);opacity:.16}.course-item:nth-child(3n+3):before{background:linear-gradient(126.15deg,#80b622,#fbb03b 102.93%),#c4c4c4;opacity:.12}.course-item-left{position:relative;width:calc(100% - 170px)}@media only screen and (max-width:1215px){.course-item-left{width:calc(100% - 130px)}}@media only screen and (max-width:991px){.course-item-left{width:100%}}.course-item-left--top{width:100%}.course-item-right{position:relative;flex:0 0 190px}.course-item:before{content:\"\";position:absolute;top:0;left:0;width:100%;height:100%}.course-item:last-child{margin-bottom:0}@media only screen and (max-width:1215px){.course-item-title{flex-direction:column}}@media only screen and (max-width:991px){.course-item-title{padding-right:140px}}@media only screen and (max-width:479px){.course-item-title{padding-right:100px}}.course-item-title h4{font-size:20px;font-weight:700;line-height:1.2}@media only screen and (max-width:991px){.course-item-title h4{font-size:18px}}@media only screen and (max-width:767px){.course-item-title h4{font-size:16px}}.course-item-title--favorite{position:relative;padding-left:24px}.course-item-title--favorite svg{position:absolute;left:0;top:2px}.course-item-title .course-item-features{display:none}@media only screen and (max-width:1215px){.course-item-title .course-item-features{display:block}}.course-item-language{font-size:16px;line-height:1.2;margin-top:3px}@media only screen and (min-width:1216px){.course-item-language{padding-left:12px}}@media only screen and (max-width:1215px){.course-item-language{margin-top:18px;font-size:14px}}.course-item-language .flag{border-radius:50%;overflow:hidden}.course-item-features{display:none}@media only screen and (min-width:1216px){.course-item-features{display:block;flex:0 0 210px;margin:4px 0 0 20px}}@media only screen and (max-width:1215px){.course-item-features{margin:15px 0 0}}.course-item-features>div{position:relative;margin-bottom:15px;padding-left:28px;font-size:16px;line-height:1.2;font-weight:500}.course-item-features>div:last-child{margin-bottom:0}@media only screen and (max-width:1215px){.course-item-features>div{padding-left:24px;font-size:14px}}@media only screen and (max-width:479px){.course-item-features>div{margin-bottom:12px}}.course-item-features>div .v-image{position:absolute;left:0;top:1px}@media only screen and (max-width:1215px){.course-item-features>div .v-image{top:-1px}}.course-item-features>div .v-image.flag{height:18px;border-radius:50%;overflow:hidden}.course-item-description{display:flex;margin-bottom:18px;font-size:17px;font-weight:300;line-height:1.5;letter-spacing:-.002em}@media only screen and (min-width:1216px){.course-item-description{justify-content:space-between;min-height:102px}}@media only screen and (max-width:1215px){.course-item-description{flex-direction:column-reverse;margin-bottom:24px;font-size:14px}}.course-item-details{height:0;font-size:17px;font-weight:300;line-height:1.57;letter-spacing:-.002em;overflow:hidden}@media only screen and (max-width:1215px){.course-item-details{font-size:14px}}.course-item-details h5{margin-bottom:16px;font-size:18px;font-weight:700;line-height:1.3333}@media only screen and (max-width:1215px){.course-item-details h5{margin-bottom:12px;font-size:16px}}.course-item-details p{margin-bottom:12px!important}@media only screen and (max-width:1215px){.course-item-details p{margin-bottom:8px!important}}.course-item-image{max-width:200px}@media only screen and (max-width:991px){.course-item-image{position:absolute;right:0;top:0;max-width:140px}}@media only screen and (max-width:479px){.course-item-image{max-width:100px}}.course-item-image img{display:block;width:100%}.course-item-structure strong{font-weight:700!important}.course-item-footer{display:flex;align-items:center}.course-item-footer .v-btn{min-width:132px!important}.course-item-footer-show{display:flex;align-items:center;margin-left:24px;color:var(--v-orange-base);font-size:14px;font-weight:900;cursor:pointer;transition:color .3s}.course-item-footer-show:hover{color:var(--v-dark-base)}.course-item--open{height:auto}.course-item--open .course-item-description{margin-bottom:24px}@media only screen and (max-width:479px){.course-item--open .course-item-description{margin-bottom:24px}}.course-item--open .course-item-details{height:auto}.course-item--open .course-item-footer{padding-top:8px}.course-item--open .course-item-footer-show svg{transform:rotate(180deg)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1465:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/teacher-profile/CourseItem.vue?vue&type=template&id=3d0b046f&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:['course-item', { 'course-item--open': _vm.isOpened }]},[_vm._ssrNode("<div class=\"course-item-left d-flex flex-column justify-space-between\">","</div>",[_vm._ssrNode("<div class=\"d-flex justify-space-between\">","</div>",[_vm._ssrNode("<div class=\"course-item-left--top\">","</div>",[_vm._ssrNode("<div class=\"course-item-title d-flex justify-space-between mb-2\">","</div>",[_vm._ssrNode("<h4 class=\"text--gradient\"><a"+(_vm._ssrAttr("href",_vm.localePath(("/teacher/" + _vm.username + "#" + (_vm.item.slug)))))+">"+_vm._ssrEscape("\n              "+_vm._s(_vm.item.name)+"\n            ")+"</a></h4> "),_vm._ssrNode("<div class=\"course-item-features\">","</div>",[_vm._ssrNode("<div>","</div>",[_vm._ssrNode("<div class=\"mr-1\">","</div>",[_c('v-img',{staticClass:"flag",attrs:{"src":__webpack_require__(101)("./" + (_vm.item.language.isoCode) + ".svg"),"width":"18","height":"18"}})],1),_vm._ssrNode(_vm._ssrEscape("\n\n              "+_vm._s(_vm.item.language.name)+"\n            "))],2),_vm._ssrNode(" "),_vm._ssrNode("<div>","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(504),"width":"18","height":"18"}}),_vm._ssrNode(_vm._ssrEscape("\n              "+_vm._s(_vm.$tc('lessons_count', _vm.item.lessons))+"\n            "))],2),_vm._ssrNode(" "),_vm._ssrNode("<div>","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(504),"width":"18","height":"18"}}),_vm._ssrNode(_vm._ssrEscape("\n              "+_vm._s(_vm.$tc('minutes_count', _vm.item.length))+"\n            "))],2),_vm._ssrNode(" "),_vm._ssrNode("<div>","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(579),"width":"18","height":"18"}}),_vm._ssrNode(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('total_price'))+":\n              ")+((_vm.currentCurrencyHtmlSymbol)?("<span>"+(_vm._s(_vm.currentCurrencyHtmlSymbol))+"</span>"):"<!---->")+_vm._ssrEscape(_vm._s((_vm.item.lessons * _vm.item.price).toFixed(2))+"\n            "))],2)],2)],2),_vm._ssrNode(" <div class=\"course-item-description\"><div>"+_vm._ssrEscape(_vm._s(_vm.item.shortDescription))+"</div></div>")],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"course-item-features\">","</div>",[_vm._ssrNode("<div>","</div>",[_vm._ssrNode("<div class=\"mr-1\">","</div>",[_c('v-img',{staticClass:"flag",attrs:{"src":__webpack_require__(101)("./" + (_vm.item.language.isoCode) + ".svg"),"width":"18","height":"18"}})],1),_vm._ssrNode(_vm._ssrEscape("\n\n          "+_vm._s(_vm.item.language.name)+"\n        "))],2),_vm._ssrNode(" "),_vm._ssrNode("<div>","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(504),"width":"18","height":"18"}}),_vm._ssrNode(_vm._ssrEscape("\n          "+_vm._s(_vm.$tc('lessons_count', _vm.item.lessons))+"\n        "))],2),_vm._ssrNode(" "),_vm._ssrNode("<div>","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(504),"width":"18","height":"18"}}),_vm._ssrNode(_vm._ssrEscape("\n          "+_vm._s(_vm.$tc('minutes_count', _vm.item.length))+"\n        "))],2),_vm._ssrNode(" "),_vm._ssrNode("<div>","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(579),"width":"18","height":"18"}}),_vm._ssrNode(_vm._ssrEscape("\n          "+_vm._s(_vm.$t('total_price'))+":\n          ")+((_vm.currentCurrencyHtmlSymbol)?("<span>"+(_vm._s(_vm.currentCurrencyHtmlSymbol))+"</span>"):"<!---->")+_vm._ssrEscape(_vm._s((_vm.item.lessons * _vm.item.price).toFixed(2))+"\n        "))],2)],2),_vm._ssrNode(" "),(_vm.$vuetify.breakpoint.smAndDown)?_vm._ssrNode("<div class=\"course-item-image\">","</div>",[_c('v-img',{attrs:{"eager":"","src":__webpack_require__(1065)("./" + (_vm.item.image) + ".svg")}})],1):_vm._e()],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"course-item-details\""+(_vm._ssrStyle(null,null, { display: (_vm.isOpened) ? '' : 'none' }))+">","</div>",[(_vm.item.youtube)?_c('youtube',{staticClass:"mt-0 mt-md-3 mt-lg-4 mb-3 mb-lg-4",attrs:{"video-link":_vm.item.youtube}}):_vm._e(),_vm._ssrNode(" "+((_vm.item.introductionToCourse)?("<div class=\"mb-3 mb-lg-4\"><h5 class=\"text--gradient\">Description:</h5> <div>"+(_vm._s(_vm.item.introductionToCourse))+"</div></div>"):"<!---->")+" "+((_vm.item.courseStructure)?("<div class=\"course-item-structure mb-3 mb-lg-4\"><h5 class=\"text--gradient\">\n          Here’s how the course will be structured:\n        </h5> <div>"+(_vm._s(_vm.item.courseStructure))+"</div></div>"):"<!---->"))],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"course-item-footer\">","</div>",[(_vm.hasFreeSlots && _vm.acceptNewStudents)?_c('v-btn',{staticClass:"gradient",on:{"click":_vm.chooseCourse}},[_c('div',{staticClass:"text--gradient"},[_vm._v(_vm._s(_vm.$t('book_course')))])]):_vm._e(),_vm._ssrNode(" <div class=\"course-item-footer-show\"><span class=\"mr-1\">"+_vm._ssrEscape("\n          "+_vm._s(_vm.$t(_vm.isOpened ? 'show_less' : 'show_more'))+"\n        ")+"</span> <svg width=\"12\" height=\"12\" viewBox=\"0 0 12 12\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#chevron-down")))+"></use></svg></div>")],2)],2),_vm._ssrNode(" "),(_vm.$vuetify.breakpoint.mdAndUp)?_vm._ssrNode("<div class=\"course-item-right d-none d-md-block\">","</div>",[_vm._ssrNode("<div class=\"course-item-image\">","</div>",[(_vm.item.image)?_c('v-img',{attrs:{"eager":"","src":__webpack_require__(1065)("./" + (_vm.item.image) + ".svg")}}):_vm._e()],1)]):_vm._e()],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/teacher-profile/CourseItem.vue?vue&type=template&id=3d0b046f&

// EXTERNAL MODULE: ./components/Youtube.vue + 4 modules
var Youtube = __webpack_require__(1150);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/teacher-profile/CourseItem.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var CourseItemvue_type_script_lang_js_ = ({
  name: 'CourseItem',
  components: {
    Youtube: Youtube["default"]
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    openCourses: {
      type: Array,
      required: true
    },
    username: {
      type: String,
      required: true
    }
  },
  computed: {
    isOpened() {
      return this.openCourses.includes(this.index);
    },

    currentCurrencyHtmlSymbol() {
      return this.$store.getters['currency/currentCurrencyHtmlSymbol'];
    },

    hasFreeSlots() {
      var _this$$store$state$te;

      return (_this$$store$state$te = this.$store.state.teacher_profile.item) === null || _this$$store$state$te === void 0 ? void 0 : _this$$store$state$te.hasFreeSlots;
    },

    acceptNewStudents() {
      var _this$$store$state$te2;

      return (_this$$store$state$te2 = this.$store.state.teacher_profile.item) === null || _this$$store$state$te2 === void 0 ? void 0 : _this$$store$state$te2.acceptNewStudents;
    }

  },
  methods: {
    chooseCourse() {
      this.$store.dispatch('teacher_profile/setSelectedCourse', this.item);
      this.$emit('show-time-picker-dialog');
    }

  }
});
// CONCATENATED MODULE: ./components/teacher-profile/CourseItem.vue?vue&type=script&lang=js&
 /* harmony default export */ var teacher_profile_CourseItemvue_type_script_lang_js_ = (CourseItemvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/teacher-profile/CourseItem.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1359)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  teacher_profile_CourseItemvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "670794c4"
  
)

/* harmony default export */ var CourseItem = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {Youtube: __webpack_require__(1150).default})


/* vuetify-loader */



installComponents_default()(component, {VBtn: VBtn["a" /* default */],VImg: VImg["a" /* default */]})


/***/ })

};;
//# sourceMappingURL=164.js.map