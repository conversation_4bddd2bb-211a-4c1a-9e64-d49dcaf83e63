exports.ids = [39];
exports.modules = {

/***/ 1257:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1347);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("7fb752ea", content, true, context)
};

/***/ }),

/***/ 1346:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1257);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1347:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".about{padding-top:80px}@media only screen and (min-width:992px){.about{overflow:hidden}}.about .section-head{margin-bottom:130px}@media only screen and (max-width:991px){.about .section-head{margin-bottom:80px}}.about-wrap{position:relative}@media only screen and (max-width:991px){.about-wrap{padding-bottom:62px!important}}.about-wrap .about-bg{position:absolute;left:58%;top:25px;width:90%;height:100%;transform:translateX(-50%)}@media only screen and (max-width:1439px){.about-wrap .about-bg{top:12px;left:68%;height:104%}}@media only screen and (max-width:991px){.about-wrap .about-bg{width:100%;height:100%;top:135px;left:0;transform:translateX(0)}}.about-item{position:relative;max-width:330px;padding-bottom:70px}@media only screen and (max-width:991px){.about-item{max-width:380px;margin:0 auto}}.about-item-image{margin-bottom:5px}@media only screen and (max-width:991px){.about-item-image{display:flex;justify-content:center;margin-bottom:18px;margin-left:auto;margin-right:auto}}.about-item-more{margin-top:12px}.about-item-i1{margin-top:25px}@media only screen and (max-width:991px){.about-item-i1{margin-top:0}}.about-item-i1 .about-item-image{max-width:280px}@media only screen and (max-width:991px){.about-item-i1 .about-item-image{max-width:304px}}.about-item-i2 .about-item-image{max-width:242px}@media only screen and (max-width:991px){.about-item-i2 .about-item-image{max-width:293px}}.about-item-i3{top:-240px;left:30px}@media only screen and (max-width:1439px){.about-item-i3{top:0;left:20px;margin-top:-590px}}@media only screen and (max-width:991px){.about-item-i3{top:auto;left:auto;margin-top:0}}.about-item-i3 .about-item-image{max-width:248px}@media only screen and (max-width:991px){.about-item-i3 .about-item-image{max-width:280px}}.about-item-i4{top:-30px}@media only screen and (max-width:1439px){.about-item-i4{top:-140px;margin-top:127px}}@media only screen and (max-width:991px){.about-item-i4{top:auto;margin-top:0}}.about-item-i4 .about-item-image{max-width:255px}.about-item-i5{top:-250px;max-width:620px;padding-bottom:0}@media only screen and (max-width:1439px){.about-item-i5{top:-160px;max-width:575px}}@media only screen and (max-width:991px){.about-item-i5{top:15px;margin-left:30px}}@media only screen and (max-width:479px){.about-item-i5{max-width:371px;top:-50px;margin-left:0;margin-top:80px;padding-top:130px}}.about-item-i5 .about-item-image{position:absolute;top:50%;left:0;width:100%;transform:translateY(-50%)}@media only screen and (max-width:479px){.about-item-i5 .about-item-image{top:0;left:-30px;transform:none}.about-item-i5 .about-item-image .type-1{display:none}}.about-item-i5 .about-item-image .type-2{display:none}@media only screen and (max-width:479px){.about-item-i5 .about-item-image .type-2{display:block}}.about-item-i5 .about-item-title{font-size:20px}@media only screen and (max-width:639px){.about-item-i5 .about-item-title{font-size:18px}}.about-item-i5 .about-item-text{font-size:16px}.about-item-i5 .about-item-more,.about-item-i5 .about-item-text,.about-item-i5 .about-item-title{padding-left:200px}@media only screen and (max-width:639px){.about-item-i5 .about-item-more,.about-item-i5 .about-item-text,.about-item-i5 .about-item-title{padding-left:32%}}@media only screen and (max-width:479px){.about-item-i5 .about-item-more,.about-item-i5 .about-item-text,.about-item-i5 .about-item-title{padding-left:0;padding-right:30px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1413:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/AboutSection.vue?vue&type=template&id=9e893b70&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"about"},[_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{staticClass:"section-head-title",staticStyle:{"color":"#262626","-webkit-text-fill-color":"#262626"}},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.about_section_title'))+"\n          ")])])])],1)],1),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"about-content\">","</div>",[_c('v-container',{staticClass:"py-0"},[_c('v-row',{attrs:{"no-gutters":""}},[_c('v-col',{staticClass:"col-xl-10 offset-xl-1 about-wrap"},[_c('div',{staticClass:"about-bg d-none d-md-block"},[_c('v-img',{attrs:{"src":__webpack_require__(433),"options":{ rootMargin: '50%' },"contain":"","height":"100%"}})],1),_vm._v(" "),_c('div',{staticClass:"about-bg d-md-none"},[_c('v-img',{attrs:{"src":__webpack_require__(128),"options":{ rootMargin: '50%' },"contain":"","height":"100%"}})],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-4 py-0"},[_c('div',{staticClass:"about-item about-item-i1"},[_c('div',{staticClass:"about-item-image"},[_c('v-img',{attrs:{"src":__webpack_require__(122),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"about-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_1_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_1_text'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-more"},[_c('nuxt-link',{staticClass:"link-more",attrs:{"to":"/teacher-listing/1/motivation,2;speciality,22"}},[_vm._v("\n                    "+_vm._s(_vm.$t('learn_more'))+"\n                  ")])],1)])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-4 offset-md-4 py-0"},[_c('div',{staticClass:"about-item about-item-i2"},[_c('div',{staticClass:"about-item-image"},[_c('v-img',{attrs:{"src":__webpack_require__(123),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"about-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_2_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_2_text'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-more"},[_c('nuxt-link',{staticClass:"link-more",attrs:{"to":"/education"}},[_vm._v("\n                    "+_vm._s(_vm.$t('learn_more'))+"\n                  ")])],1)])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"order-md-2 col-12 col-md-4 offset-md-8 py-0"},[_c('div',{staticClass:"about-item about-item-i3"},[_c('div',{staticClass:"about-item-image"},[_c('v-img',{attrs:{"src":__webpack_require__(124),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"about-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_3_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_3_text'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-more"},[_c('nuxt-link',{staticClass:"link-more",attrs:{"to":"/teacher-listing/1/motivation,3"}},[_vm._v("\n                    "+_vm._s(_vm.$t('learn_more'))+"\n                  ")])],1)])]),_vm._v(" "),_c('v-col',{staticClass:"order-md-1 col-12 col-md-4 offset-md-1 offset-lg-4 py-0"},[_c('div',{staticClass:"about-item about-item-i4"},[_c('div',{staticClass:"about-item-image"},[_c('v-img',{attrs:{"src":__webpack_require__(125),"contain":"","options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"about-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_4_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_4_text'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-more"},[_c('nuxt-link',{staticClass:"link-more",attrs:{"to":"/teacher-listing/1/motivation,1"}},[_vm._v("\n                    "+_vm._s(_vm.$t('learn_more'))+"\n                  ")])],1)])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"about-item about-item-i5"},[_c('div',{staticClass:"about-item-image"},[_c('v-img',{staticClass:"type-1",attrs:{"src":__webpack_require__(127),"contain":"","options":{ rootMargin: '50%' }}}),_vm._v(" "),_c('v-img',{staticClass:"type-2",attrs:{"src":__webpack_require__(126),"contain":"","max-width":"371","options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"about-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_5_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_5_text'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-more"},[_c('nuxt-link',{staticClass:"link-more",attrs:{"to":"/business"}},[_vm._v("\n                    "+_vm._s(_vm.$t('learn_more'))+"\n                  ")])],1)])])],1)],1)],1)],1)],1)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/AboutSection.vue?vue&type=template&id=9e893b70&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/AboutSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var AboutSectionvue_type_script_lang_js_ = ({
  name: 'AboutSection'
});
// CONCATENATED MODULE: ./components/homepage/AboutSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_AboutSectionvue_type_script_lang_js_ = (AboutSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/AboutSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1346)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_AboutSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "455c4d1e"
  
)

/* harmony default export */ var AboutSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */





installComponents_default()(component, {VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ })

};;
//# sourceMappingURL=homepage-about-section.js.map