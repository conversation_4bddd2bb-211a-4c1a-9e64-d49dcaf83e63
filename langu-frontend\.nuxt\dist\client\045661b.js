(window.webpackJsonp=window.webpackJsonp||[]).push([[5],{1528:function(t,e,n){"use strict";n.r(e);n(7),n(8),n(14),n(6),n(15);var o=n(13),h=n(2),r=(n(31),n(20),n(1390),n(37),n(1391),n(1392),n(1393),n(1394),n(1395),n(1396),n(1397),n(1398),n(1399),n(1400),n(1401),n(1402),n(1403),n(1404),n(1405),n(1406),n(44),n(9),n(24),n(23),n(38),n(713),n(35),n(80),n(1855)),l=n(1856),c=n(266);function d(object,t){var e=Object.keys(object);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(object);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(object,t).enumerable}))),e.push.apply(e,n)}return e}var m={start:"mousedown",move:"mousemove",stop:"mouseup"},f={start:"touchstart",move:"touchmove",stop:"touchend"},v={userSelect:"none",MozUserSelect:"none",WebkitUserSelect:"none",MsUserSelect:"none"},z={userSelect:"auto",MozUserSelect:"auto",WebkitUserSelect:"auto",MsUserSelect:"auto"},x=m,w={replace:!0,name:"VueDraggableResizable",props:{childHeaderHeight:{type:Number,default:0},zoomIndex:{type:Number,required:!0},zoomX:{type:Number,required:!0},zoomY:{type:Number,required:!0},className:{type:String,default:"vdr"},classNameDraggable:{type:String,default:"draggable"},classNameResizable:{type:String,default:"resizable"},classNameDragging:{type:String,default:"dragging"},classNameResizing:{type:String,default:"resizing"},classNameActive:{type:String,default:"active"},classNameHandle:{type:String,default:"handle"},disableUserSelect:{type:Boolean,default:!0},enableNativeDrag:{type:Boolean,default:!1},preventDeactivation:{type:Boolean,default:!1},active:{type:Boolean,default:!1},draggable:{type:Boolean,default:!0},resizable:{type:Boolean,default:!0},lockAspectRatio:{type:Boolean,default:!1},w:{type:[Number,String],default:200,validator:function(t){return"number"==typeof t?t>0:"auto"===t}},h:{type:[Number,String],default:200,validator:function(t){return"number"==typeof t?t>0:"auto"===t}},minWidth:{type:Number,default:320,validator:function(t){return t>=0}},minHeight:{type:Number,default:200,validator:function(t){return t>=0}},maxWidth:{type:Number,default:null,validator:function(t){return t>=0}},maxHeight:{type:Number,default:null,validator:function(t){return t>=0}},x:{type:Number,default:0},y:{type:Number,default:0},z:{type:[String,Number],default:"auto",validator:function(t){return"string"==typeof t?"auto"===t:t>=0}},handles:{type:Array,default:function(){return["tl","tm","tr","mr","br","bm","bl","ml"]},validator:function(t){var s=new Set(["tl","tm","tr","mr","br","bm","bl","ml"]);return new Set(t.filter((function(t){return s.has(t)}))).size===t.length}},dragHandle:{type:String,default:null},dragCancel:{type:String,default:null},axis:{type:String,default:"both",validator:function(t){return["x","y","both"].includes(t)}},grid:{type:Array,default:function(){return[1,1]}},parent:{type:Boolean,default:!0},scale:{type:[Number,Array],default:1,validator:function(t){return"number"==typeof t?t>0:2===t.length&&t[0]>0&&t[1]>0}},onDragStart:{type:Function,default:function(){return!0}},onDrag:{type:Function,default:function(){return!0}},onResizeStart:{type:Function,default:function(){return!0}},onResize:{type:Function,default:function(){return!0}}},data:function(){return{left:null,top:null,right:null,bottom:null,width:null,height:null,widthTouched:!1,heightTouched:!1,aspectFactor:null,parentWidth:null,parentHeight:null,minW:this.minWidth,minH:this.minHeight,maxW:this.maxWidth,maxH:this.maxHeight,handle:null,enabled:this.active,resizing:!1,dragging:!1,dragEnable:!1,resizeEnable:!1,zIndex:this.z,originalHandle:null}},computed:{computedLeft:function(){return this.left*this.zoomIndex-this.width*(1-this.zoomIndex)/2},computedTop:function(){return this.top*this.zoomIndex-this.height*(1-this.zoomIndex)/2},style:function(){return function(t){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(e){Object(h.a)(t,e,source[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(source,e))}))}return t}({transform:"translate(".concat(this.computedLeft,"px, ").concat(this.computedTop,"px) scale(").concat(this.zoomIndex,")"),width:this.computedWidth,height:this.computedHeight,zIndex:this.zIndex},this.dragging&&this.disableUserSelect?v:z)},actualHandles:function(){return this.resizable?this.handles:[]},computedWidth:function(){return"auto"!==this.w||this.widthTouched?this.width+"px":"auto"},computedHeight:function(){return"auto"!==this.h||this.heightTouched?this.height+"px":"auto"},resizingOnX:function(){return Boolean(this.handle)&&(this.handle.includes("l")||this.handle.includes("r"))},resizingOnY:function(){return Boolean(this.handle)&&(this.handle.includes("t")||this.handle.includes("b"))},isCornerHandle:function(){return Boolean(this.handle)&&["tl","tr","br","bl"].includes(this.handle)}},watch:{active:function(t){this.enabled=t,t?this.$emit("activated"):this.$emit("deactivated")},z:function(t){(t>=0||"auto"===t)&&(this.zIndex=t)},w:function(t){this.resizing||this.dragging||(this.parent&&(this.bounds=this.calcResizeLimits()),this.changeWidth(t))},h:function(t){this.resizing||this.dragging||(this.parent&&(this.bounds=this.calcResizeLimits()),this.changeHeight(t))},x:function(t){this.resizing||this.dragging||(this.parent&&(this.bounds=this.calcDragLimits()),this.moveHorizontally(t))},y:function(t){this.resizing||this.dragging||(this.parent&&(this.bounds=this.calcDragLimits()),this.moveVertically(t))},lockAspectRatio:function(t){this.aspectFactor=t?this.width/(this.height-this.childHeaderHeight):void 0},minWidth:function(t){t>0&&t<=this.width&&(this.minW=t)},minHeight:function(t){t>0&&t<=this.height&&(this.minH=t)},maxWidth:function(t){this.maxW=t},maxHeight:function(t){this.maxH=t}},created:function(){this.maxWidth&&this.minWidth>this.maxWidth&&console.warn("[Vdr warn]: Invalid prop: minWidth cannot be greater than maxWidth"),this.maxWidth&&this.minHeight>this.maxHeight&&console.warn("[Vdr warn]: Invalid prop: minHeight cannot be greater than maxHeight"),this.resetBoundsAndMouseState()},mounted:function(){var t=this;this.$nextTick((function(){t.left=t.x,t.top=t.y,t.enableNativeDrag||(t.$el.ondragstart=function(){return!1});var e=t.getParentSize(),n=Object(o.a)(e,2),h=n[0],l=n[1];t.parentWidth=h,t.parentHeight=l;var d=Object(r.b)(t.$el),m=Object(o.a)(d,2),f=m[0],v=m[1];t.aspectFactor=("auto"!==t.w?t.w:f)/(("auto"!==t.h?t.h:v)-t.childHeaderHeight),t.width="auto"!==t.w?t.w:f,t.height="auto"!==t.h?t.h:v,t.right=t.parentWidth-t.width-t.left,t.bottom=t.parentHeight-t.height-t.top,t.active&&t.$emit("activated"),t.computedLeft+t.width>c.n+c.l+t.zoomX&&t.$emit("update-asset",c.n-c.l+t.zoomX-t.width,t.top),Object(r.a)(document.documentElement,"mousedown",t.deselect),Object(r.a)(document.documentElement,"touchend touchcancel",t.deselect),Object(r.a)(window,"resize",t.checkParentSize)}))},beforeDestroy:function(){Object(r.d)(document.documentElement,"mousedown",this.deselect),Object(r.d)(document.documentElement,"touchstart",this.handleUp),Object(r.d)(document.documentElement,"mousemove",this.move),Object(r.d)(document.documentElement,"touchmove",this.move),Object(r.d)(document.documentElement,"mouseup",this.handleUp),Object(r.d)(document.documentElement,"touchend touchcancel",this.deselect),Object(r.d)(window,"resize",this.checkParentSize)},methods:{resetBoundsAndMouseState:function(){this.mouseClickPosition={mouseX:0,mouseY:0,x:0,y:0,w:0,h:0},this.bounds={minLeft:null,maxLeft:null,minRight:null,maxRight:null,minTop:null,maxTop:null,minBottom:null,maxBottom:null}},checkParentSize:function(){if(this.parent){var t=this.getParentSize(),e=Object(o.a)(t,2),n=e[0],h=e[1];this.parentWidth=n,this.parentHeight=h,this.right=this.parentWidth-this.width-this.left,this.bottom=this.parentHeight-this.height-this.top}},getParentSize:function(){return this.parent?[c.n,c.k]:[null,null]},elementTouchDown:function(t){x=f,this.elementDown(t)},elementMouseDown:function(t){x=m,this.elementDown(t)},elementDown:function(t){if(!(t instanceof MouseEvent&&1!==t.which)){var e=t.target||t.srcElement;if(this.$el.contains(e)){if(!1===this.onDragStart(t))return;if(this.dragHandle&&!Object(r.c)(e,this.dragHandle,this.$el)||this.dragCancel&&Object(r.c)(e,this.dragCancel,this.$el))return void(this.dragging=!1);this.enabled||(this.enabled=!0,this.$emit("activated"),this.$emit("update:active",!0)),this.draggable&&(this.dragEnable=!0),this.mouseClickPosition.mouseX=t.touches?t.touches[0].pageX:t.pageX,this.mouseClickPosition.mouseY=t.touches?t.touches[0].pageY:t.pageY,this.mouseClickPosition.left=this.left,this.mouseClickPosition.right=this.right,this.mouseClickPosition.top=this.top,this.mouseClickPosition.bottom=this.bottom,this.parent&&(this.bounds=this.calcDragLimits()),Object(r.a)(document.documentElement,x.move,this.move),Object(r.a)(document.documentElement,x.stop,this.handleUp)}}},calcDragLimits:function(){return{minLeft:c.l-this.zoomX,maxLeft:this.parentWidth+c.l-this.zoomX-this.width,minRight:this.right%this.grid[0],maxRight:Math.floor((this.parentWidth-this.width-this.right)/this.grid[0])*this.grid[0]+this.right,minTop:c.m-this.zoomY,maxTop:this.parentHeight+c.m-this.zoomY-this.height,minBottom:this.bottom%this.grid[1],maxBottom:Math.floor((this.parentHeight-this.height-this.bottom)/this.grid[1])*this.grid[1]+this.bottom}},deselect:function(t){var e=t.target||t.srcElement,n=new RegExp(this.className+"-([trmbl]{2})","");this.$el.contains(e)||n.test(e.className)||(this.enabled&&!this.preventDeactivation&&(this.enabled=!1,this.$emit("deactivated"),this.$emit("update:active",!1)),Object(r.d)(document.documentElement,x.move,this.handleResize)),this.resetBoundsAndMouseState()},handleTouchDown:function(t,e){x=f,this.handleDown(t,e)},handleDown:function(t,e){e instanceof MouseEvent&&1!==e.which||!1!==this.onResizeStart(t,e)&&(e.stopPropagation&&e.stopPropagation(),this.originalHandle=t,this.lockAspectRatio&&!t.includes("m")?this.handle="m"+t.substring(1):this.handle=t,this.resizeEnable=!0,this.mouseClickPosition.mouseX=e.touches?e.touches[0].pageX:e.pageX,this.mouseClickPosition.mouseY=e.touches?e.touches[0].pageY:e.pageY,this.mouseClickPosition.left=this.left,this.mouseClickPosition.right=this.right,this.mouseClickPosition.top=this.top,this.mouseClickPosition.bottom=this.bottom,this.bounds=this.calcResizeLimits(),Object(r.a)(document.documentElement,x.move,this.handleResize),Object(r.a)(document.documentElement,x.stop,this.handleUp))},calcResizeLimits:function(){var t=this.minW,e=this.minH,n=this.maxW,h=this.maxH,r=this.aspectFactor,l=Object(o.a)(this.grid,2),c=l[0],d=l[1],m=this.width,f=this.height,v=this.left,z=this.top,x=this.right,w=this.bottom;this.lockAspectRatio&&(e=t/r+this.childHeaderHeight,n&&h?(n=Math.min(n,r*h),h=Math.min(h,n/r)):n?h=n/r:h&&(n=r*h)),n-=n%c,h-=h%d;var y={minLeft:null,maxLeft:null,minTop:null,maxTop:null,minRight:null,maxRight:null,minBottom:null,maxBottom:null};return this.parent?(y.minLeft=v%c,y.maxLeft=v+Math.floor((m-t)/c)*c,y.minTop=z%d,y.maxTop=z+Math.floor((f-e)/d)*d,y.minRight=x%c,y.maxRight=x+Math.floor((m-t)/c)*c,y.minBottom=w%d,y.maxBottom=w+Math.floor((f-e)/d)*d,n&&(y.minLeft=Math.max(y.minLeft,this.parentWidth-x-n),y.minRight=Math.max(y.minRight,this.parentWidth-v-n)),h&&(y.minTop=Math.max(y.minTop,this.parentHeight-w-h),y.minBottom=Math.max(y.minBottom,this.parentHeight-z-h))):(y.minLeft=null,y.maxLeft=v+Math.floor((m-t)/c)*c,y.minTop=null,y.maxTop=z+Math.floor((f-e)/d)*d,y.minRight=null,y.maxRight=x+Math.floor((m-t)/c)*c,y.minBottom=null,y.maxBottom=w+Math.floor((f-e)/d)*d,n&&(y.minLeft=-(x+n),y.minRight=-(v+n)),h&&(y.minTop=-(w+h),y.minBottom=-(z+h)),this.lockAspectRatio&&n&&h&&(y.minLeft=Math.min(y.minLeft,-(x+n)),y.minTop=Math.min(y.minTop,-(h+w)),y.minRight=Math.min(y.minRight,-v-n),y.minBottom=Math.min(y.minBottom,-z-h))),y},move:function(t){this.resizing?this.handleResize(t):this.dragEnable&&this.handleDrag(t)},handleDrag:function(t){var e=this.axis,n=this.grid,h=this.bounds,r=this.mouseClickPosition,c=e&&"y"!==e?(r.mouseX-(t.touches?t.touches[0].pageX:t.pageX))/this.zoomIndex:0,d=e&&"x"!==e?(r.mouseY-(t.touches?t.touches[0].pageY:t.pageY))/this.zoomIndex:0,m=Object(l.e)(n,c,d,this.scale),f=Object(o.a)(m,2),v=f[0],z=f[1],x=Object(l.d)(r.left-v,h.minLeft,h.maxLeft),w=Object(l.d)(r.top-z,h.minTop,h.maxTop);if(!1!==this.onDrag(x,w)){var y=Object(l.d)(r.right+v,h.minRight,h.maxRight),O=Object(l.d)(r.bottom+z,h.minBottom,h.maxBottom);this.left=x,this.top=w,this.right=y,this.bottom=O,this.$emit("dragging",this.left,this.top),this.dragging=!0}},moveHorizontally:function(t){var e=Object(l.e)(this.grid,t,this.top,1),n=Object(o.a)(e,2),h=n[0],r=(n[1],Object(l.d)(h,this.bounds.minLeft,this.bounds.maxLeft));this.left=r,this.right=this.parentWidth-this.width-r},moveVertically:function(t){var e=Object(l.e)(this.grid,this.left,t,1),n=Object(o.a)(e,2),h=(n[0],n[1]),r=Object(l.d)(h,this.bounds.minTop,this.bounds.maxTop);this.top=r,this.bottom=this.parentHeight-this.height-r},handleResize:function(t){var e=this.left,n=this.top,h=this.right,r=this.bottom,c=this.mouseClickPosition,d=this.aspectFactor,m=(c.mouseX-(t.touches?t.touches[0].pageX:t.pageX))/this.zoomIndex,f=(c.mouseY-(t.touches?t.touches[0].pageY:t.pageY))/this.zoomIndex;!this.widthTouched&&m&&(this.widthTouched=!0),!this.heightTouched&&f&&(this.heightTouched=!0);var v=Object(l.e)(this.grid,m,f,this.scale),z=Object(o.a)(v,2),x=z[0],w=z[1];this.handle.includes("b")?(r=Object(l.d)(c.bottom+w,this.bounds.minBottom,this.bounds.maxBottom),this.lockAspectRatio&&this.resizingOnY&&("bm"===this.originalHandle?(h=this.right-(this.bottom-r)*(d/2),e=this.left-(this.bottom-r)*(d/2)):"br"===this.originalHandle?h=this.right-(this.bottom-r)*d:e=this.left-(this.bottom-r)*d)):this.handle.includes("t")&&(n=Object(l.d)(c.top-w,this.bounds.minTop,this.bounds.maxTop),this.lockAspectRatio&&this.resizingOnY&&("tm"===this.originalHandle?(e=this.left-(this.top-n)*(d/2),h=this.right-(this.top-n)*(d/2)):"tr"===this.originalHandle?h=this.right-(this.top-n)*d:e=this.left-(this.top-n)*d)),this.handle.includes("r")?(h=Object(l.d)(c.right+x,this.bounds.minRight,this.bounds.maxRight),this.lockAspectRatio&&this.resizingOnX&&("mr"===this.originalHandle?(r=this.bottom-(this.right-h)/(2*d),n=this.top-(this.right-h)/(2*d)):"br"===this.originalHandle?r=this.bottom-(this.right-h)/d:n=this.top-(this.right-h)/d)):this.handle.includes("l")&&(e=Object(l.d)(c.left-x,this.bounds.minLeft,this.bounds.maxLeft),this.lockAspectRatio&&this.resizingOnX&&("ml"===this.originalHandle?(n=this.top-(this.left-e)/(2*d),r=this.bottom-(this.left-e)/(2*d)):"tl"===this.originalHandle?n=this.top-(this.left-e)/d:r=this.bottom-(this.left-e)/d));var y=Object(l.b)(this.parentWidth,e,h),O=Object(l.a)(this.parentHeight,n,r);!1!==this.onResize(this.handle,e,n,y,O)&&(this.left=e,this.top=n,this.right=h,this.bottom=r,this.width=y,this.height=O,this.$emit("resizing",this.left,this.top,this.width,this.height,t.target.className),this.resizing=!0)},changeWidth:function(t){var e=Object(l.e)(this.grid,t,0,1),n=Object(o.a)(e,2),h=n[0],r=(n[1],Object(l.d)(this.parentWidth-h-this.left,this.bounds.minRight,this.bounds.maxRight)),c=this.bottom;this.lockAspectRatio&&(c=this.bottom-(this.right-r)/this.aspectFactor);var d=Object(l.b)(this.parentWidth,this.left,r),m=Object(l.a)(this.parentHeight,this.top,c);this.right=r,this.bottom=c,this.width=d,this.height=m},changeHeight:function(t){this.height=t,this.bottom=this.parentHeight-this.height-this.top,this.aspectFactor=this.width/(this.height-this.childHeaderHeight)},handleUp:function(t){this.handle=null,this.resetBoundsAndMouseState(),this.dragEnable=!1,this.resizeEnable=!1,this.resizing&&(this.resizing=!1,this.$emit("resizestop",this.left,this.top,this.width,this.height)),this.dragging&&(this.dragging=!1,this.$emit("dragstop",this.left,this.top)),Object(r.d)(document.documentElement,x.move,this.handleResize)}}},y=n(22),component=Object(y.a)(w,(function(){var t,e=this,n=e.$createElement,o=e._self._c||n;return o("div",{class:[(t={},t[e.classNameActive]=e.enabled,t[e.classNameDragging]=e.dragging,t[e.classNameResizing]=e.resizing,t[e.classNameDraggable]=e.draggable,t[e.classNameResizable]=e.resizable,t),e.className],style:e.style,on:{mousedown:e.elementMouseDown,touchstart:e.elementTouchDown}},[e._l(e.actualHandles,(function(t){return o("div",{key:t,class:[e.classNameHandle,e.classNameHandle+"-"+t],style:{display:e.enabled?"block":"none"},on:{mousedown:function(n){return n.stopPropagation(),n.preventDefault(),e.handleDown(t,n)},touchstart:function(n){return n.stopPropagation(),n.preventDefault(),e.handleTouchDown(t,n)}}},[e._t(t)],2)})),e._v(" "),e._t("default")],2)}),[],!1,null,null,null);e.default=component.exports},1855:function(t,e,n){"use strict";n.d(e,"c",(function(){return h})),n.d(e,"b",(function(){return r})),n.d(e,"a",(function(){return l})),n.d(e,"d",(function(){return c}));n(71);var o=n(1856);function h(t,e,n){var h=t,r=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"].find((function(t){return Object(o.c)(h[t])}));if(!Object(o.c)(h[r]))return!1;do{if(h[r](e))return!0;if(h===n)return!1;h=h.parentNode}while(h);return!1}function r(t){var style=window.getComputedStyle(t);return[parseFloat(style.getPropertyValue("width"),10),parseFloat(style.getPropertyValue("height"),10)]}function l(t,e,n){t&&(t.attachEvent?t.attachEvent("on"+e,n):t.addEventListener?t.addEventListener(e,n,!0):t["on"+e]=n)}function c(t,e,n){t&&(t.detachEvent?t.detachEvent("on"+e,n):t.removeEventListener?t.removeEventListener(e,n,!0):t["on"+e]=null)}},1856:function(t,e,n){"use strict";n.d(e,"c",(function(){return h})),n.d(e,"e",(function(){return r})),n.d(e,"b",(function(){return l})),n.d(e,"a",(function(){return c})),n.d(e,"d",(function(){return d}));var o=n(13);n(20);function h(t){return"function"==typeof t||"[object Function]"===Object.prototype.toString.call(t)}function r(t,e,n){var h=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,r="number"==typeof h?[h,h]:h,l=Object(o.a)(r,2),c=l[0],d=l[1],m=Math.round(e/c/t[0])*t[0],f=Math.round(n/d/t[1])*t[1];return[m,f]}function l(t,e,n){return t-e-n}function c(t,e,n){return t-e-n}function d(t,e,n){return null!==e&&t<e?e:null!==n&&n<t?n:t}}}]);