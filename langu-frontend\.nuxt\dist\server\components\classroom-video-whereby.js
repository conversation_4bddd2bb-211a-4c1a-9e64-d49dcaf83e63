exports.ids = [30,15,32];
exports.modules = {

/***/ 1020:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(68);
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(513);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".vdr{touch-action:none;position:absolute;box-sizing:border-box;border:none}.handle{display:block!important}.resizable.vdr:not(.hide-resize-icon):after{content:\"\";position:absolute;bottom:4px;right:2px;width:8px;height:8px;background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");background-size:contain;background-repeat:no-repeat;background-position:50%;opacity:.9;z-index:99}.vdr .handle{box-sizing:border-box;position:absolute;width:12px;height:12px;border:none;background-color:transparent;z-index:10}.vdr .handle-tl{top:-7px;left:-7px}.vdr .handle-tm{top:-7px;margin-left:-5px}.vdr .handle-tr{top:-7px;right:-7px}.vdr .handle-ml{margin-top:-5px;left:-7px}.vdr .handle-mr{margin-top:-5px;right:-7px}.vdr .handle-bl{bottom:-7px;left:-7px}.vdr .handle-bm{bottom:-7px;margin-left:-5px}.vdr .handle-br{bottom:-7px;right:-7px}@media only screen and (max-width:768px){[class*=handle-]:before{content:\"\";left:-10px;right:-10px;bottom:-10px;top:-10px;position:absolute}}.vdr .handle-bm,.vdr .handle-tm{width:100%;left:5px}.vdr .handle-ml,.vdr .handle-mr{height:100%;top:5px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:14px;height:14px;z-index:11}@media only screen and (max-width:1199px){.vdr .handle-bm,.vdr .handle-tm{height:15px}.vdr .handle-ml,.vdr .handle-mr{width:15px}.vdr .handle-bl,.vdr .handle-br,.vdr .handle-tl,.vdr .handle-tr{width:15px;height:15px}.vdr .handle-tl{top:-10px;left:-10px}.vdr .handle-tm{top:-10px;margin-left:-5px}.vdr .handle-tr{top:-10px;right:-10px}.vdr .handle-ml{margin-top:-5px;left:-10px}.vdr .handle-mr{margin-top:-5px;right:-10px}.vdr .handle-bl{bottom:-10px;left:-10px}.vdr .handle-bm{bottom:-10px;margin-left:-5px}.vdr .handle-br{bottom:-10px;right:-10px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1190:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(URLSearchParams) {/* harmony import */ var _helpers_check_device__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(145);
/* harmony import */ var _helpers_whereby_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(1213);
/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(501);
/* harmony import */ var _components_classroom_ClassroomContainer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(960);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ __webpack_exports__["a"] = ({
  name: 'Whereby',
  components: {
    ClassroomContainer: _components_classroom_ClassroomContainer__WEBPACK_IMPORTED_MODULE_3__["default"]
  },
  props: {
    file: {
      type: Object,
      required: true
    },
    screenShareAsset: {
      type: Object,
      required: true
    },
    zoomOtherAsset: {
      type: Object,
      required: true
    }
  },
  data: () => ({
    localStreamContainer: null,
    screenShareStreamContainer: null,
    isLocalScreenShareEnabled: false,
    isRemoteScreenShareEnabled: false,
    screenSharingNotSupported: !Object(_helpers_check_device__WEBPACK_IMPORTED_MODULE_0__[/* isSupportedScreenShare */ "c"])(),
    isJoined: false,
    currentRole: 'participant',
    // Will be determined based on classroom role
    wherebyRoom: null,
    // Store the created room information
    isCreatingRoom: false,
    // Loading state for room creation
    settings: {
      isFullscreenEnabled: false,
      isScreenShareEnabled: false
    },
    // Properties for Whereby features (not used in UI but may be referenced)
    isHandRaised: false,
    isChatEnabled: false,
    isParticipantsEnabled: false
  }),
  computed: {
    lessonId() {
      return this.file.lessonId || this.$store.state.classroom.lessonId;
    },

    teacherId() {
      return this.$store.state.classroom.teacherId;
    },

    studentId() {
      return this.$store.state.classroom.studentId;
    },

    role() {
      return this.$store.getters['classroom/role'];
    },

    isVideoEnabled() {
      var _this$file$asset$sett, _this$file$asset, _this$file$asset$sett2, _this$file$asset$sett3;

      return (_this$file$asset$sett = (_this$file$asset = this.file.asset) === null || _this$file$asset === void 0 ? void 0 : (_this$file$asset$sett2 = _this$file$asset.settings) === null || _this$file$asset$sett2 === void 0 ? void 0 : (_this$file$asset$sett3 = _this$file$asset$sett2[this.role]) === null || _this$file$asset$sett3 === void 0 ? void 0 : _this$file$asset$sett3.isVideoEnabled) !== null && _this$file$asset$sett !== void 0 ? _this$file$asset$sett : true;
    },

    isMuted() {
      var _this$file$asset$sett4, _this$file$asset2, _this$file$asset2$set, _this$file$asset2$set2;

      return (_this$file$asset$sett4 = (_this$file$asset2 = this.file.asset) === null || _this$file$asset2 === void 0 ? void 0 : (_this$file$asset2$set = _this$file$asset2.settings) === null || _this$file$asset2$set === void 0 ? void 0 : (_this$file$asset2$set2 = _this$file$asset2$set[this.role]) === null || _this$file$asset2$set2 === void 0 ? void 0 : _this$file$asset2$set2.isMuted) !== null && _this$file$asset$sett4 !== void 0 ? _this$file$asset$sett4 : false;
    },

    maxIndex() {
      return this.$store.state.classroom.maxIndex;
    }

  },

  mounted() {
    this.$nextTick(() => {
      // Add debugging to check if elements exist
      this.localStreamContainer = document.getElementById('whereby-video-container');
      this.screenShareStreamContainer = document.getElementById('whereby-screenshare-placeholder'); // Only initialize if container exists

      if (this.localStreamContainer) {
        this.initializeWhereby();
      } else {
        // eslint-disable-next-line no-console
        console.error('whereby-video-container not found, retrying in 500ms');
        setTimeout(() => {
          this.localStreamContainer = document.getElementById('whereby-video-container');

          if (this.localStreamContainer) {
            this.initializeWhereby();
          } else {
            // eslint-disable-next-line no-console
            console.error('whereby-video-container still not found after retry');
          }
        }, 500);
      }
    });
    window.addEventListener('beforeunload', this.closeStream);
    window.addEventListener('pagehide', this.closeStream);
    document.addEventListener('fullscreenchange', this.fullscreenChangeHandler);
  },

  beforeDestroy() {
    document.removeEventListener('fullscreenchange', this.fullscreenChangeHandler);
    this.closeStream();
  },

  methods: {
    async initializeWhereby() {
      try {
        // Check if container exists
        if (!this.localStreamContainer) {
          // eslint-disable-next-line no-console
          console.error('localStreamContainer is null');
          return;
        } // Determine role based on classroom role


        const isTeacher = this.role === 'teacher';
        this.currentRole = isTeacher ? 'host' : 'participant'; // Show loading state

        this.isCreatingRoom = true;
        this.localStreamContainer.innerHTML = `
          <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">
            <div style="margin-bottom: 20px;">
              <h3 style="color: #333; margin-bottom: 10px;">Creating Whereby Room...</h3>
              <p style="color: #666; margin-bottom: 20px;">Please wait while we set up your video call</p>
            </div>
            <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #5E72E4; border-radius: 50%; animation: spin 1s linear infinite;"></div>
            <style>
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            </style>
          </div>
        `; // Create or get existing room

        if (!this.wherebyRoom) {
          // Dynamic room creation - this will return existing room if already created
          this.wherebyRoom = await Object(_helpers_whereby_api__WEBPACK_IMPORTED_MODULE_1__[/* createWherebyRoom */ "a"])({
            lessonId: this.lessonId,
            teacherId: this.teacherId,
            studentId: this.studentId,
            isRecurring: false
          });
        } // Hide loading state


        this.isCreatingRoom = false; // Create iframe for inline embedding like options A and B

        const iframe = document.createElement('iframe'); // Determine the URL based on role

        const baseUrl = this.currentRole === 'host' ? this.wherebyRoom.hostRoomUrl : this.wherebyRoom.roomUrl; // Build embed parameters - explicitly disable requested features

        const embedParams = new URLSearchParams({
          embed: '',
          displayName: this.$store.getters['classroom/userName'] || 'User',
          audio: !this.isMuted ? 'on' : 'off',
          video: this.isVideoEnabled ? 'on' : 'off',
          // Explicitly disable requested features
          chat: 'off',
          people: 'off',
          screenshare: 'on',
          reactions: 'on',
          handRaise: 'on',
          leaveButton: 'off',
          background: 'on',
          recording: 'off',
          breakoutRooms: 'on',
          whiteboard: 'on',
          minimal: 'false',
          // Direct entry without knocking
          skipMediaPermissionPrompt: 'true',
          autoJoin: 'true'
        }); // Determine if we need to add & or ? for parameters

        const separator = baseUrl.includes('?') ? '&' : '?';
        iframe.src = `${baseUrl}${separator}${embedParams.toString()}`;
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.border = 'none';
        iframe.style.borderRadius = '8px';
        iframe.style.pointerEvents = 'auto'; // Allow iframe to receive events when not being dragged

        iframe.allow = 'camera; microphone; fullscreen; display-capture; autoplay';
        iframe.allowFullscreen = true; // Clear container and add iframe

        this.localStreamContainer.innerHTML = '';
        this.localStreamContainer.appendChild(iframe); // No need for complex iframe event handling since we have a header for dragging
        // Set up message listener for iframe communication

        window.addEventListener('message', this.handleWherebyMessage); // Mark as joined after a short delay

        setTimeout(() => {
          this.isJoined = true;
        }, 1000);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to initialize Whereby:', error);
        this.isCreatingRoom = false; // Check if container exists before setting innerHTML

        if (this.localStreamContainer) {
          this.localStreamContainer.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">
              <div style="margin-bottom: 20px;">
                <h3 style="color: #d32f2f; margin-bottom: 10px;">Failed to Create Room</h3>
                <p style="color: #666; margin-bottom: 20px;">Unable to create Whereby room. Please try switching to another video provider.</p>
              </div>
              <button
                onclick="this.parentElement.parentElement.parentElement.querySelector('.toolbar-button-item').click()"
                style="
                  background: #5E72E4;
                  color: white;
                  border: none;
                  padding: 12px 20px;
                  border-radius: 6px;
                  font-size: 14px;
                  cursor: pointer;
                  font-weight: 600;
                "
              >
                Switch to Provider A
              </button>
            </div>
          `;
        }
      }
    },

    handleWherebyMessage(event) {
      // Handle messages from Whereby iframe
      if (!event.origin.includes('whereby.com')) {
        return;
      }

      try {
        const data = JSON.parse(event.data);

        switch (data.type) {
          case 'participant_join':
            break;

          case 'participant_leave':
            break;

          case 'screenshare_start':
            this.isRemoteScreenShareEnabled = true;
            break;

          case 'screenshare_stop':
            this.isRemoteScreenShareEnabled = false;
            break;

          case 'error':
            // eslint-disable-next-line no-console
            console.error('Whereby error:', data.error);
            this.handleMediaError(data.error);
            break;
        }
      } catch (e) {// Ignore non-JSON messages
      }
    },

    handleMediaError(error) {
      // eslint-disable-next-line no-console
      console.error('Whereby media error:', error);
      alert('Could not connect to video call. Please check your camera and microphone permissions.');
    },

    toggleVideo() {
      this.updateData(this.file.id, {
        settings: { ...this.file.asset.settings,
          [this.role]: {
            isVideoEnabled: !this.isVideoEnabled,
            isMuted: this.isMuted
          }
        }
      }); // Note: With iframe approach, video/audio controls are handled within the Whereby interface
      // The iframe will respect the initial settings passed in the URL parameters
    },

    toggleAudio() {
      this.updateData(this.file.id, {
        settings: { ...this.file.asset.settings,
          [this.role]: {
            isVideoEnabled: this.isVideoEnabled,
            isMuted: !this.isMuted
          }
        }
      }); // Note: With iframe approach, video/audio controls are handled within the Whereby interface
      // The iframe will respect the initial settings passed in the URL parameters
    },

    toggleFullScreen() {
      this.settings.isFullscreenEnabled = !this.settings.isFullscreenEnabled;
      Object(_helpers__WEBPACK_IMPORTED_MODULE_2__["switchFullScreen"])(this.settings.isFullscreenEnabled);
    },

    toggleScreenShare() {
      this.settings.isScreenShareEnabled = !this.settings.isScreenShareEnabled;

      if (this.settings.isScreenShareEnabled) {
        this.isLocalScreenShareEnabled = false;
        this.screenShareStreamContainer.innerHTML = '';
      } else {
        // Note: With iframe approach, screen sharing is handled within the Whereby interface
        // Users can use the screen share button within the Whereby room
        this.updateData(this.screenShareAsset.id, {
          index: this.maxIndex + 1
        });
        this.isLocalScreenShareEnabled = true;
      }
    },

    toggleHandRaise() {
      this.isHandRaised = !this.isHandRaised; // Note: Actual hand raising is handled by Whereby iframe
      // This just updates the UI state for the external controls
    },

    toggleChat() {
      this.isChatEnabled = !this.isChatEnabled; // Note: Actual chat toggle is handled by Whereby iframe
      // This just updates the UI state for the external controls
    },

    toggleParticipants() {
      this.isParticipantsEnabled = !this.isParticipantsEnabled; // Note: Actual participants panel is handled by Whereby iframe
      // This just updates the UI state for the external controls
    },

    // Video player switching disabled - Whereby is now the only option

    /*
    switchVideoPlayer(type) {
      this.$store.dispatch('classroom/deleteAsset', this.file)
      this.$store.dispatch('classroom/createAsset', {
        ...this.file.asset,
        type,
      })
    },
    */
    fullscreenChangeHandler() {
      if (!document.fullscreenElement) {
        this.settings.isFullscreenEnabled = false;
      }
    },

    updateData(id, asset) {
      this.$store.commit('classroom/moveAsset', {
        id,
        asset
      });
      this.$store.dispatch('classroom/moveAsset', {
        id,
        lessonId: this.file.lessonId,
        asset
      });
    },

    closeStream() {
      // Clean up iframe and event listeners
      if (this.localStreamContainer) {
        this.localStreamContainer.innerHTML = '';
      }

      window.removeEventListener('message', this.handleWherebyMessage);
    }

  }
});
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(427)["URLSearchParams"]))

/***/ }),

/***/ 1213:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(URLSearchParams) {/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return createWherebyRoom; });
/* unused harmony export getWherebyRoom */
/* unused harmony export endWherebyRoom */
/* unused harmony export generateWherebyUrls */
/* unused harmony export openWherebyWindow */
/* unused harmony export isRoomActive */
/* unused harmony export getRoomTimeRemaining */
/**
 * Whereby API helper functions for classroom integration
 */

/**
 * Create a new Whereby room for a classroom session
 * @param {Object} params - Room creation parameters
 * @param {string} params.lessonId - The lesson ID
 * @param {string} params.teacherId - The teacher ID
 * @param {string} params.studentId - The student ID
 * @param {boolean} params.isRecurring - Whether this is a recurring lesson
 * @returns {Promise<Object>} Room information
 */
async function createWherebyRoom({
  lessonId,
  teacherId,
  studentId,
  isRecurring = false
}) {
  try {
    const response = await fetch('/_nuxt/api/whereby/create-room', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        lessonId,
        teacherId,
        studentId,
        isRecurring
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create room');
    }

    const data = await response.json();
    return data.room;
  } catch (error) {
    console.error('Error creating Whereby room:', error);
    throw error;
  }
}
/**
 * Get room information for a lesson
 * @param {string} lessonId - The lesson ID
 * @returns {Promise<Object>} Room information
 */

async function getWherebyRoom(lessonId) {
  try {
    const response = await fetch(`/api/whereby/room/${lessonId}`);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get room info');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching Whereby room:', error);
    throw error;
  }
}
/**
 * End a Whereby room
 * @param {string} meetingId - The Whereby meeting ID
 * @returns {Promise<Object>} Success response
 */

async function endWherebyRoom(meetingId) {
  try {
    const response = await fetch(`/api/whereby/room/${meetingId}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to end room');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error ending Whereby room:', error);
    throw error;
  }
}
/**
 * Generate room URLs with parameters for embedding
 * @param {Object} room - Room information from createWherebyRoom
 * @param {string} role - 'host' or 'participant'
 * @param {Object} options - Additional options
 * @returns {Object} URLs and parameters
 */

function generateWherebyUrls(room, role = 'participant', options = {}) {
  const {
    displayName = 'User',
    audio = 'on',
    video = 'on',
    chat = 'off',
    // Explicitly disabled
    people = 'off',
    // Explicitly disabled
    screenshare = 'on',
    reactions = 'on',
    handRaise = 'on',
    leaveButton = 'off',
    // Explicitly disabled
    background = 'on',
    recording = 'off',
    // Explicitly disabled
    breakoutRooms = 'on',
    whiteboard = 'on',
    minimal = 'false'
  } = options; // Use appropriate URL based on role

  const baseUrl = role === 'host' ? room.hostRoomUrl : room.roomUrl; // Build embed parameters - explicitly disable requested features

  const embedParams = new URLSearchParams({
    displayName,
    audio,
    video,
    chat,
    people,
    screenshare,
    reactions,
    handRaise,
    leaveButton,
    background,
    recording,
    breakoutRooms,
    whiteboard,
    minimal
  }); // Determine if we need to add & or ? for parameters

  const separator = baseUrl.includes('?') ? '&' : '?';
  const fullUrl = `${baseUrl}${separator}${embedParams.toString()}`;
  return {
    baseUrl,
    fullUrl,
    embedParams: embedParams.toString(),
    role,
    meetingId: room.meetingId
  };
}
/**
 * Open Whereby room in a new window
 * @param {Object} room - Room information
 * @param {string} role - 'host' or 'participant'
 * @param {Object} options - Display options
 * @returns {Window|null} Reference to opened window
 */

function openWherebyWindow(room, role = 'participant', options = {}) {
  const {
    displayName = 'User',
    windowFeatures = 'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no'
  } = options;

  try {
    const {
      fullUrl
    } = generateWherebyUrls(room, role, {
      displayName,
      ...options
    });
    const windowName = `whereby-${room.lessonId}-${role}`;
    const newWindow = window.open(fullUrl, windowName, windowFeatures);

    if (newWindow) {
      newWindow.focus();
      return newWindow;
    } else {
      throw new Error('Failed to open window - popup blocked?');
    }
  } catch (error) {
    console.error('Error opening Whereby window:', error);
    throw error;
  }
}
/**
 * Check if a room is still active
 * @param {Object} room - Room information
 * @returns {boolean} Whether the room is still active
 */

function isRoomActive(room) {
  if (!room || !room.endDate) return false;
  const now = new Date();
  const endDate = new Date(room.endDate);
  return now < endDate;
}
/**
 * Get time remaining for a room
 * @param {Object} room - Room information
 * @returns {number} Minutes remaining (0 if expired)
 */

function getRoomTimeRemaining(room) {
  if (!room || !room.endDate) return 0;
  const now = new Date();
  const endDate = new Date(room.endDate);
  if (now >= endDate) return 0;
  const diffMs = endDate - now;
  return Math.floor(diffMs / (1000 * 60)); // Convert to minutes
}
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(427)["URLSearchParams"]))

/***/ }),

/***/ 1248:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1333);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("02017f86", content, true, context)
};

/***/ }),

/***/ 1332:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Whereby_vue_vue_type_style_index_0_id_08cfee22_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1248);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Whereby_vue_vue_type_style_index_0_id_08cfee22_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Whereby_vue_vue_type_style_index_0_id_08cfee22_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Whereby_vue_vue_type_style_index_0_id_08cfee22_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Whereby_vue_vue_type_style_index_0_id_08cfee22_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1333:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".whereby-stream .whereby-component[data-v-08cfee22]{position:relative;background:#000;border-radius:8px;overflow:hidden}.whereby-stream .whereby-component .whereby-header[data-v-08cfee22]{position:absolute;top:0;left:0;right:0;height:20px;background:rgba(0,0,0,.7);display:flex;align-items:center;padding:0 12px;z-index:10;border-radius:8px 8px 0 0}.whereby-stream .whereby-component .whereby-header .whereby-header-title[data-v-08cfee22]{color:#fff;font-size:12px;font-weight:500;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.whereby-stream .whereby-component .whereby-header[data-v-08cfee22]:hover{background:rgba(0,0,0,.8)}.whereby-stream .whereby-component .whereby-video-container[data-v-08cfee22]{width:100%;height:100%;min-height:200px}.whereby-stream .whereby-component.screenshare-component .user-name[data-v-08cfee22]{position:absolute;top:10px;left:10px;background:rgba(0,0,0,.7);color:#fff;padding:5px 10px;border-radius:4px;font-size:12px;z-index:10}.whereby-stream .whereby-component.screenshare-component .screenshare-stream[data-v-08cfee22]{width:100%;height:100%;min-height:200px}.whereby-stream .whereby-component.video-window--is-fullscreen[data-v-08cfee22]{position:fixed;top:0;left:0;width:100vw!important;height:100vh!important;z-index:9999;border-radius:0}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1419:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/video/Whereby.vue?vue&type=template&id=08cfee22&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"whereby-stream"},[_c('classroom-container',{attrs:{"asset":_vm.file,"hover-enabled":true,"lock-aspect-ratio":false}},[_c('div',{class:[
        'whereby-component cursor-before-grab',
        { 'video-window--is-fullscreen': _vm.settings.isFullscreenEnabled } ],attrs:{"id":"video-window"}},[_c('div',{staticClass:"whereby-header cursor-before-grab"},[_c('div',{staticClass:"whereby-header-title"})]),_vm._v(" "),_c('div',{staticClass:"whereby-video-container",attrs:{"id":"whereby-video-container"}})])]),_vm._ssrNode(" "),_c('classroom-container',{directives:[{name:"show",rawName:"v-show",value:(_vm.isLocalScreenShareEnabled || _vm.isRemoteScreenShareEnabled),expression:"isLocalScreenShareEnabled || isRemoteScreenShareEnabled"}],attrs:{"asset":_vm.screenShareAsset,"hover-enabled":true}},[_c('div',{staticClass:"whereby-component screenshare-component cursor-before-grab"},[_c('div',{staticClass:"user-name"},[(_vm.isLocalScreenShareEnabled)?[_vm._v("\n          "+_vm._s(_vm.$t('my_screen'))+"\n        ")]:_vm._e(),_vm._v(" "),(_vm.isRemoteScreenShareEnabled)?[_vm._v("\n          "+_vm._s(_vm.$t('classroom_user_screen', {
              username: _vm.zoomOtherAsset.asset.username,
            }))+"\n        ")]:_vm._e()],2),_vm._v(" "),_c('div',{staticClass:"screenshare-stream",attrs:{"id":"whereby-screenshare-placeholder"}})])])],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/classroom/video/Whereby.vue?vue&type=template&id=08cfee22&scoped=true&

// EXTERNAL MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/video/Whereby.vue?vue&type=script&lang=js&
var Wherebyvue_type_script_lang_js_ = __webpack_require__(1190);

// CONCATENATED MODULE: ./components/classroom/video/Whereby.vue?vue&type=script&lang=js&
 /* harmony default export */ var video_Wherebyvue_type_script_lang_js_ = (Wherebyvue_type_script_lang_js_["a" /* default */]); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/classroom/video/Whereby.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1332)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  video_Wherebyvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "08cfee22",
  "09e7796c"
  
)

/* harmony default export */ var Whereby = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents(component, {ClassroomContainer: __webpack_require__(960).default})


/***/ }),

/***/ 960:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/ClassroomContainer.vue?vue&type=template&id=5f513aff&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{on:{"mouseenter":_vm.mouseenter,"mouseleave":_vm.mouseleave}},[_c('vue-draggable-resizable',{ref:"vueDraggableResizable",class:{
      student: _vm.role === 'student',
      teacher: _vm.role === 'teacher',
      'hide-resize-icon': _vm.hideResizeIcon,
    },style:({
      outline:
        _vm.isHoveredByAsset || _vm.isHovered
          ? ("3px solid " + _vm.getRoleHoverColor)
          : 'none',
    }),attrs:{"draggable":_vm.enabledDraggable,"resizable":_vm.enabledResizeable,"w":_vm.width,"h":_vm.height + _vm.childHeaderHeight,"x":_vm.leftScale,"y":_vm.topScale,"z":_vm.index,"zoom-index":_vm.scalable ? _vm.zoom.zoomIndex : 1,"zoom-x":_vm.zoom.x,"zoom-y":_vm.zoom.y,"child-header-height":_vm.childHeaderHeight,"lock-aspect-ratio":_vm.lockAspectRatio,"handles":_vm.handles},on:{"dragging":_vm.onDrag,"resizing":_vm.onResize,"dragstop":_vm.onDragStop,"resizestop":_vm.onResizeStop,"update-asset":_vm.updateAsset},nativeOn:{"click":function($event){return _vm.onIndex.apply(null, arguments)}}},[_vm._t("default",null,{"onIndex":_vm.onIndex})],2)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/classroom/ClassroomContainer.vue?vue&type=template&id=5f513aff&

// EXTERNAL MODULE: ./components/classroom/vue-draggable-resizable/VueDraggableResizable.vue + 4 modules
var VueDraggableResizable = __webpack_require__(991);

// EXTERNAL MODULE: ./helpers/constants.js
var constants = __webpack_require__(69);

// EXTERNAL MODULE: ./mixins/SetTool.vue + 2 modules
var SetTool = __webpack_require__(966);

// EXTERNAL MODULE: ./components/classroom/vue-draggable-resizable/vue-draggable-resizable.css
var vue_draggable_resizable = __webpack_require__(995);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/ClassroomContainer.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var ClassroomContainervue_type_script_lang_js_ = ({
  components: {
    VueDraggableResizable: VueDraggableResizable["default"]
  },
  mixins: [SetTool["a" /* default */]],
  props: {
    asset: {
      type: Object,
      required: true
    },
    childHeaderHeight: {
      type: Number,
      default: 0
    },
    lockAspectRatio: {
      type: Boolean,
      default: true
    },
    scalable: {
      type: Boolean,
      default: true
    },
    hoverEnabled: {
      type: Boolean,
      default: true
    },
    isDraggableProp: {
      type: Boolean,
      default: true
    },
    hideResizeIcon: {
      type: Boolean,
      default: false
    },
    handles: {
      type: Array,
      default: () => ['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml']
    }
  },

  data() {
    return {
      width: constants["j" /* defaultWidth */],
      height: constants["i" /* defaultHeight */],
      top: 50,
      left: 500,
      isHovered: false,
      isHoveredByAsset: false,
      index: 5,
      eventBodyClass: null,
      allowIndexChange: true,
      resizable: true,
      draggable: true,
      synchronizeable: true,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight,
      offset: 5
    };
  },

  computed: {
    isCanvasOversizeX() {
      return constants["n" /* mainCanvasWidth */] > this.viewportWidth;
    },

    isCanvasOversizeY() {
      return constants["k" /* mainCanvasHeight */] > this.viewportHeight;
    },

    isScaledCanvasOversizeY() {
      return constants["k" /* mainCanvasHeight */] * this.zoomIndex > this.viewportHeight;
    },

    getRoleHoverColor() {
      return this.role === 'teacher' ? 'var(--v-teacherColor-base)' : 'var(--v-studentColor-base)';
    },

    enabledResizeable() {
      return this.resizable && this.$store.state.classroom.containerComponentEnabled && !this.isLockedForStudent;
    },

    enabledDraggable() {
      return this.isDraggableProp && this.draggable && this.$store.state.classroom.containerComponentEnabled && !this.isLockedForStudent;
    },

    maxIndex() {
      return this.$store.state.classroom.maxIndex;
    },

    zoom() {
      return this.$store.getters['classroom/zoomAsset'].asset;
    },

    zoomIndex() {
      var _this$zoom$zoomIndex, _this$zoom;

      return (_this$zoom$zoomIndex = (_this$zoom = this.zoom) === null || _this$zoom === void 0 ? void 0 : _this$zoom.zoomIndex) !== null && _this$zoom$zoomIndex !== void 0 ? _this$zoom$zoomIndex : 1;
    },

    type() {
      var _this$asset, _this$asset$asset;

      return (_this$asset = this.asset) === null || _this$asset === void 0 ? void 0 : (_this$asset$asset = _this$asset.asset) === null || _this$asset$asset === void 0 ? void 0 : _this$asset$asset.type;
    },

    topScale() {
      if (this.type === 'toolbar') {
        return this.top < 0 || this.top + this.height > this.viewportHeight ? this.viewportHeight - this.height - 70 : this.top;
      }

      return this.synchronizeable ? this.top - this.zoom.y : this.top;
    },

    leftScale() {
      if (this.type === 'toolbar') {
        return this.left + this.width > this.viewportWidth || this.left < 0 ? this.viewportWidth - this.width - 15 : this.left;
      }

      return this.synchronizeable ? this.left - this.zoom.x : this.left;
    },

    isLockedForStudent() {
      return this.$store.getters['classroom/isLocked'] && this.role === 'student';
    },

    isSocketConnected() {
      return this.$store.state.socket.isConnected;
    }

  },
  watch: {
    'asset.asset'(asset) {
      this.move(asset);
    }

  },

  mounted() {
    this.move(this.asset.asset);
  },

  methods: {
    mouseenter() {
      if (this.hoverEnabled && this.synchronizeable) {
        this.isHovered = true;
        this.socketAssetMoved({
          isHovered: true
        });
      }

      if (this.type === 'twilio' || this.type === 'tokbox' || this.type === 'editor') {
        var _this$$store$getters$, _this$$store$getters$2;

        this.$store.commit('classroom/setCursorNameBeforeChange', ((_this$$store$getters$ = this.$store.getters['classroom/userParams']) === null || _this$$store$getters$ === void 0 ? void 0 : _this$$store$getters$.cursor) || 'cursor-pointer');
        this.$store.commit('classroom/setToolNameBeforeChange', ((_this$$store$getters$2 = this.$store.getters['classroom/userParams']) === null || _this$$store$getters$2 === void 0 ? void 0 : _this$$store$getters$2.tool) || 'pointer');
        this.setTool('pointer', 'cursor-pointer');
      }
    },

    mouseleave() {
      if (this.hoverEnabled && this.synchronizeable) {
        this.isHovered = false;
        this.socketAssetMoved({
          isHovered: false
        });
      }

      if (this.type === 'twilio' || this.type === 'tokbox' || this.type === 'editor') {
        this.setTool(this.$store.state.classroom.toolNameBeforeChange, this.$store.state.classroom.cursorNameBeforeChange);
      }
    },

    onIndex() {
      this.index = this.maxIndex + 1;
      this.$store.commit('classroom/setMaxIndex', this.index);
      this.$store.commit('classroom/moveAsset', {
        id: this.asset.id,
        asset: {
          index: this.index
        }
      });
      this.$store.dispatch('classroom/moveAsset', {
        id: this.asset.id,
        lessonId: this.asset.lessonId,
        asset: {
          index: this.index
        }
      });
    },

    updateAsset(left, top) {
      this.$store.commit('classroom/moveAsset', {
        id: this.asset.id,
        asset: {
          left: this.synchronizeable ? left + this.zoom.x : left,
          top: this.synchronizeable ? top + this.zoom.y : top,
          index: this.index
        }
      });
    },

    onDrag(left, top) {
      if (this.synchronizeable) {
        this.left = left + this.zoom.x;
        this.top = top + this.zoom.y;

        if (this.allowIndexChange) {
          const el = document.body;

          if (!el.classList.contains(this.eventBodyClass)) {
            this.eventBodyClass = 'dragging';
            el.classList.add(this.eventBodyClass);
          }

          this.allowIndexChange = false;
          this.index = this.maxIndex + 1;
          this.$store.commit('classroom/setMaxIndex', this.index);
        }

        const asset = {
          left: this.left,
          top: this.top,
          index: this.index
        };
        this.$store.commit('classroom/moveAsset', {
          id: this.asset.id,
          asset
        });
        this.socketAssetMoved(asset);
      }
    },

    onDragStop(left, top) {
      const el = document.body;

      if (el.classList.contains(this.eventBodyClass)) {
        el.classList.remove(this.eventBodyClass);
        this.eventBodyClass = null;
      }

      this.allowIndexChange = true;
      this.$store.commit('classroom/setMaxIndex', this.index);
      this.$store.dispatch('classroom/moveAsset', {
        id: this.asset.id,
        lessonId: this.asset.lessonId,
        asset: {
          left: this.synchronizeable ? left + this.zoom.x : left,
          top: this.synchronizeable ? top + this.zoom.y : top,
          index: this.index
        }
      });
    },

    onResize(left, top, width, height, className) {
      if (this.synchronizeable) {
        this.left = left + this.zoom.x;
        this.top = top + this.zoom.y;
        this.width = width;
        this.height = height - this.childHeaderHeight;

        if (this.allowIndexChange) {
          const el = document.body;

          if (!el.classList.contains(this.eventBodyClass)) {
            this.eventBodyClass = className.split(' ')[1];
            el.classList.add(this.eventBodyClass);
          }

          this.allowIndexChange = false;
          this.index = this.maxIndex + 1;
          this.$store.commit('classroom/setMaxIndex', this.index);
        }

        const asset = {
          left: this.left,
          top: this.top,
          width: this.width,
          height: this.height,
          index: this.index
        };
        this.$store.commit('classroom/moveAsset', {
          id: this.asset.id,
          asset
        });
        this.socketAssetMoved(asset);
      }
    },

    onResizeStop(left, top, width, height) {
      if (this.eventBodyClass) {
        document.body.classList.remove(this.eventBodyClass);
      }

      this.allowIndexChange = true;
      this.$store.dispatch('classroom/moveAsset', {
        id: this.asset.id,
        lessonId: this.asset.lessonId,
        asset: {
          left: this.synchronizeable ? left + this.zoom.x : left,
          top: this.synchronizeable ? top + this.zoom.y : top,
          width,
          height: height - this.childHeaderHeight,
          index: this.index
        }
      });
    },

    socketAssetMoved(asset) {
      if (this.isSocketConnected) {
        this.$socket.emit('asset-moved', {
          id: this.asset.id,
          lessonId: this.asset.lessonId,
          asset
        });
      }
    },

    move(asset) {
      if (asset.width !== undefined) {
        this.width = asset.width;
      } else {
        // if (this.type === 'toolbar') {
        //   this.width = this.$refs.vueDraggableResizable.$el.clientWidth
        // }
        // eslint-disable-next-line no-lonely-if
        if (this.type === 'editor') {
          this.width = (this.isCanvasOversizeX ? this.viewportWidth : constants["n" /* mainCanvasWidth */]) * 0.66;
        }
      }

      if (asset.height !== undefined) {
        this.height = asset.height;
      } else {
        // if (this.type === 'toolbar') {
        //   this.height = this.$refs.vueDraggableResizable.$el.clientHeight
        // }
        // eslint-disable-next-line no-lonely-if
        if (this.type === 'editor') {
          let height = (this.isCanvasOversizeY ? this.viewportHeight : constants["k" /* mainCanvasHeight */]) * 0.8;

          if (height > 1200) {
            height = 1200;
          }

          if (height < 400) {
            height = 400;
          }

          this.height = height - this.offset * 2;
        }

        if (this.type === 'audio') {
          this.height = 0;
        }
      }

      if (asset.top !== undefined) {
        this.top = asset.top;
      } else {
        // if (this.type === 'toolbar') {
        //   this.top = (this.isScaledCanvasOversizeY ? this.viewportHeight : mainCanvasHeight * this.zoomIndex) - this.height - this.offset
        // }
        // eslint-disable-next-line no-lonely-if
        if (this.type === 'twilio' || this.type === 'tokbox' || this.type === 'editor') {
          this.top = this.offset;
        }
      }

      if (asset.left !== undefined) {
        this.left = asset.left;
      } else {
        // if (this.type === 'toolbar') {
        //   this.left = (this.isScaledCanvasOversizeX ? this.viewportWidth : mainCanvasWidth ) - this.width - this.offset
        // }
        if (this.type === 'twilio' || this.type === 'tokbox') {
          this.left = (this.isCanvasOversizeX ? this.viewportWidth : constants["n" /* mainCanvasWidth */]) - this.width - this.offset;
        }

        if (this.type === 'editor') {
          this.left = this.offset;
        }
      }

      if (asset.index !== undefined) {
        this.index = asset.index;
      }

      if (asset.resizable !== undefined) {
        this.resizable = asset.resizable;
      }

      if (asset.draggable !== undefined) {
        this.draggable = asset.draggable;
      }

      if (asset.synchronizeable !== undefined) {
        this.synchronizeable = asset.synchronizeable;
      }

      if (asset.isHovered !== undefined) {
        this.isHoveredByAsset = asset.isHovered;
      }
    }

  }
});
// CONCATENATED MODULE: ./components/classroom/ClassroomContainer.vue?vue&type=script&lang=js&
 /* harmony default export */ var classroom_ClassroomContainervue_type_script_lang_js_ = (ClassroomContainervue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/classroom/ClassroomContainer.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  classroom_ClassroomContainervue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "5ca5812f"
  
)

/* harmony default export */ var ClassroomContainer = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 961:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "c", function() { return isFunction; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "e", function() { return snapToGrid; });
/* unused harmony export getSize */
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "b", function() { return computeWidth; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return computeHeight; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "d", function() { return restrictToBounds; });
function isFunction(func) {
  return typeof func === 'function' || Object.prototype.toString.call(func) === '[object Function]';
}
function snapToGrid(grid, pendingX, pendingY, scale = 1) {
  const [scaleX, scaleY] = typeof scale === 'number' ? [scale, scale] : scale;
  const x = Math.round(pendingX / scaleX / grid[0]) * grid[0];
  const y = Math.round(pendingY / scaleY / grid[1]) * grid[1];
  return [x, y];
}
function getSize(el) {
  const rect = el.getBoundingClientRect();
  return [parseInt(rect.width), parseInt(rect.height)];
}
function computeWidth(parentWidth, left, right) {
  return parentWidth - left - right;
}
function computeHeight(parentHeight, top, bottom) {
  return parentHeight - top - bottom;
}
function restrictToBounds(value, min, max) {
  if (min !== null && value < min) {
    return min;
  }

  if (max !== null && max < value) {
    return max;
  }

  return value;
}

/***/ }),

/***/ 966:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/SetTool.vue?vue&type=script&lang=js&
/* harmony default export */ var SetToolvue_type_script_lang_js_ = ({
  computed: {
    role() {
      return this.$store.getters['classroom/role'];
    }

  },
  methods: {
    setTool(toolName, cursorName) {
      this.$store.commit('classroom/enableContainerComponent', toolName === 'pointer');
      this.$socket.emit('cursor-moved', {
        tool: toolName,
        cursor: cursorName.replace(/(-cursor|cursor-)/i, ''),
        lessonId: this.$store.state.classroom.lessonId
      });
      this.$store.commit('classroom/setUserTool', toolName);
      this.$store.commit('classroom/setUserCursor', cursorName);
      const el = document.body;
      const classList = el.classList;
      this.removeCursors(classList);
      el.classList.add(`${this.role}-${cursorName}`);
      this.classList = el.classList;
    },

    removeCursors(classList) {
      classList.forEach(item => {
        if (item.includes('cursor')) {
          document.body.classList.remove(item);
        }
      });
    }

  }
});
// CONCATENATED MODULE: ./mixins/SetTool.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_SetToolvue_type_script_lang_js_ = (SetToolvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/SetTool.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_SetToolvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "2e32be2e"
  
)

/* harmony default export */ var SetTool = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 991:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/vue-draggable-resizable/VueDraggableResizable.vue?vue&type=template&id=3affd017&
var render = function () {
var _obj;
var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[
    ( _obj = {}, _obj[_vm.classNameActive] = _vm.enabled, _obj[_vm.classNameDragging] = _vm.dragging, _obj[_vm.classNameResizing] = _vm.resizing, _obj[_vm.classNameDraggable] = _vm.draggable, _obj[_vm.classNameResizable] = _vm.resizable, _obj ),
    _vm.className ],style:(_vm.style),on:{"mousedown":_vm.elementMouseDown,"touchstart":_vm.elementTouchDown}},[_vm._l((_vm.actualHandles),function(handle){return _vm._ssrNode("<div"+(_vm._ssrClass(null,[_vm.classNameHandle, _vm.classNameHandle + '-' + handle]))+(_vm._ssrStyle(null,{ display: _vm.enabled ? 'block' : 'none' }, null))+">","</div>",[_vm._t(handle)],2)}),_vm._ssrNode(" "),_vm._t("default")],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/classroom/vue-draggable-resizable/VueDraggableResizable.vue?vue&type=template&id=3affd017&

// EXTERNAL MODULE: external "core-js/modules/esnext.set.add-all.js"
var esnext_set_add_all_js_ = __webpack_require__(836);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.delete-all.js"
var esnext_set_delete_all_js_ = __webpack_require__(837);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.difference.js"
var esnext_set_difference_js_ = __webpack_require__(838);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.every.js"
var esnext_set_every_js_ = __webpack_require__(839);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.filter.js"
var esnext_set_filter_js_ = __webpack_require__(840);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.find.js"
var esnext_set_find_js_ = __webpack_require__(841);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.intersection.js"
var esnext_set_intersection_js_ = __webpack_require__(842);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.is-disjoint-from.js"
var esnext_set_is_disjoint_from_js_ = __webpack_require__(843);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.is-subset-of.js"
var esnext_set_is_subset_of_js_ = __webpack_require__(844);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.is-superset-of.js"
var esnext_set_is_superset_of_js_ = __webpack_require__(845);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.join.js"
var esnext_set_join_js_ = __webpack_require__(846);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.map.js"
var esnext_set_map_js_ = __webpack_require__(847);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.reduce.js"
var esnext_set_reduce_js_ = __webpack_require__(848);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.some.js"
var esnext_set_some_js_ = __webpack_require__(849);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.symmetric-difference.js"
var esnext_set_symmetric_difference_js_ = __webpack_require__(850);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.union.js"
var esnext_set_union_js_ = __webpack_require__(851);

// EXTERNAL MODULE: ./helpers/dom.js
var dom = __webpack_require__(999);

// EXTERNAL MODULE: ./helpers/fns.js
var fns = __webpack_require__(961);

// EXTERNAL MODULE: ./helpers/constants.js
var constants = __webpack_require__(69);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/vue-draggable-resizable/VueDraggableResizable.vue?vue&type=script&lang=js&
















//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



const events = {
  mouse: {
    start: 'mousedown',
    move: 'mousemove',
    stop: 'mouseup'
  },
  touch: {
    start: 'touchstart',
    move: 'touchmove',
    stop: 'touchend'
  }
};
const userSelectNone = {
  userSelect: 'none',
  MozUserSelect: 'none',
  WebkitUserSelect: 'none',
  MsUserSelect: 'none'
};
const userSelectAuto = {
  userSelect: 'auto',
  MozUserSelect: 'auto',
  WebkitUserSelect: 'auto',
  MsUserSelect: 'auto'
};
let eventsFor = events.mouse;
/* harmony default export */ var VueDraggableResizablevue_type_script_lang_js_ = ({
  replace: true,
  name: 'VueDraggableResizable',
  props: {
    childHeaderHeight: {
      type: Number,
      default: 0
    },
    zoomIndex: {
      type: Number,
      required: true
    },
    zoomX: {
      type: Number,
      required: true
    },
    zoomY: {
      type: Number,
      required: true
    },
    className: {
      type: String,
      default: 'vdr'
    },
    classNameDraggable: {
      type: String,
      default: 'draggable'
    },
    classNameResizable: {
      type: String,
      default: 'resizable'
    },
    classNameDragging: {
      type: String,
      default: 'dragging'
    },
    classNameResizing: {
      type: String,
      default: 'resizing'
    },
    classNameActive: {
      type: String,
      default: 'active'
    },
    classNameHandle: {
      type: String,
      default: 'handle'
    },
    disableUserSelect: {
      type: Boolean,
      default: true
    },
    enableNativeDrag: {
      type: Boolean,
      default: false
    },
    preventDeactivation: {
      type: Boolean,
      default: false
    },
    active: {
      type: Boolean,
      default: false
    },
    draggable: {
      type: Boolean,
      default: true
    },
    resizable: {
      type: Boolean,
      default: true
    },
    lockAspectRatio: {
      type: Boolean,
      default: false
    },
    w: {
      type: [Number, String],
      default: 200,
      validator: val => {
        if (typeof val === 'number') {
          return val > 0;
        }

        return val === 'auto';
      }
    },
    h: {
      type: [Number, String],
      default: 200,
      validator: val => {
        if (typeof val === 'number') {
          return val > 0;
        }

        return val === 'auto';
      }
    },
    minWidth: {
      type: Number,
      default: 320,
      validator: val => val >= 0
    },
    minHeight: {
      type: Number,
      default: 200,
      validator: val => val >= 0
    },
    maxWidth: {
      type: Number,
      default: null,
      validator: val => val >= 0
    },
    maxHeight: {
      type: Number,
      default: null,
      validator: val => val >= 0
    },
    x: {
      type: Number,
      default: 0
    },
    y: {
      type: Number,
      default: 0
    },
    z: {
      type: [String, Number],
      default: 'auto',
      validator: val => typeof val === 'string' ? val === 'auto' : val >= 0
    },
    handles: {
      type: Array,
      default: () => ['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml'],
      validator: val => {
        const s = new Set(['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml']);
        return new Set(val.filter(h => s.has(h))).size === val.length;
      }
    },
    dragHandle: {
      type: String,
      default: null
    },
    dragCancel: {
      type: String,
      default: null
    },
    axis: {
      type: String,
      default: 'both',
      validator: val => ['x', 'y', 'both'].includes(val)
    },
    grid: {
      type: Array,
      default: () => [1, 1]
    },
    parent: {
      type: Boolean,
      default: true
    },
    scale: {
      type: [Number, Array],
      default: 1,
      validator: val => {
        if (typeof val === 'number') {
          return val > 0;
        }

        return val.length === 2 && val[0] > 0 && val[1] > 0;
      }
    },
    onDragStart: {
      type: Function,
      default: () => true
    },
    onDrag: {
      type: Function,
      default: () => true
    },
    onResizeStart: {
      type: Function,
      default: () => true
    },
    onResize: {
      type: Function,
      default: () => true
    }
  },

  data() {
    return {
      left: null,
      top: null,
      right: null,
      bottom: null,
      width: null,
      height: null,
      widthTouched: false,
      heightTouched: false,
      aspectFactor: null,
      parentWidth: null,
      parentHeight: null,
      minW: this.minWidth,
      minH: this.minHeight,
      maxW: this.maxWidth,
      maxH: this.maxHeight,
      handle: null,
      enabled: this.active,
      resizing: false,
      dragging: false,
      dragEnable: false,
      resizeEnable: false,
      zIndex: this.z,
      originalHandle: null
    };
  },

  computed: {
    computedLeft() {
      return this.left * this.zoomIndex - this.width * (1 - this.zoomIndex) / 2;
    },

    computedTop() {
      return this.top * this.zoomIndex - this.height * (1 - this.zoomIndex) / 2;
    },

    style() {
      return {
        transform: `translate(${this.computedLeft}px, ${this.computedTop}px) scale(${this.zoomIndex})`,
        width: this.computedWidth,
        height: this.computedHeight,
        zIndex: this.zIndex,
        ...(this.dragging && this.disableUserSelect ? userSelectNone : userSelectAuto)
      };
    },

    actualHandles() {
      if (!this.resizable) return [];
      return this.handles;
    },

    computedWidth() {
      if (this.w === 'auto') {
        if (!this.widthTouched) {
          return 'auto';
        }
      }

      return this.width + 'px';
    },

    computedHeight() {
      if (this.h === 'auto') {
        if (!this.heightTouched) {
          return 'auto';
        }
      }

      return this.height + 'px';
    },

    resizingOnX() {
      return Boolean(this.handle) && (this.handle.includes('l') || this.handle.includes('r'));
    },

    resizingOnY() {
      return Boolean(this.handle) && (this.handle.includes('t') || this.handle.includes('b'));
    },

    isCornerHandle() {
      return Boolean(this.handle) && ['tl', 'tr', 'br', 'bl'].includes(this.handle);
    }

  },
  watch: {
    active(val) {
      this.enabled = val;

      if (val) {
        this.$emit('activated');
      } else {
        this.$emit('deactivated');
      }
    },

    z(val) {
      if (val >= 0 || val === 'auto') {
        this.zIndex = val;
      }
    },

    w(val) {
      if (this.resizing || this.dragging) {
        return;
      }

      if (this.parent) {
        this.bounds = this.calcResizeLimits();
      }

      this.changeWidth(val);
    },

    h(val) {
      if (this.resizing || this.dragging) {
        return;
      }

      if (this.parent) {
        this.bounds = this.calcResizeLimits();
      }

      this.changeHeight(val);
    },

    x(val) {
      if (this.resizing || this.dragging) {
        return;
      }

      if (this.parent) {
        this.bounds = this.calcDragLimits();
      }

      this.moveHorizontally(val);
    },

    y(val) {
      if (this.resizing || this.dragging) {
        return;
      }

      if (this.parent) {
        this.bounds = this.calcDragLimits();
      }

      this.moveVertically(val);
    },

    lockAspectRatio(val) {
      if (val) {
        this.aspectFactor = this.width / (this.height - this.childHeaderHeight);
      } else {
        this.aspectFactor = undefined;
      }
    },

    minWidth(val) {
      if (val > 0 && val <= this.width) {
        this.minW = val;
      }
    },

    minHeight(val) {
      if (val > 0 && val <= this.height) {
        this.minH = val;
      }
    },

    maxWidth(val) {
      this.maxW = val;
    },

    maxHeight(val) {
      this.maxH = val;
    }

  },

  created() {
    // eslint-disable-next-line
    if (this.maxWidth && this.minWidth > this.maxWidth) console.warn('[Vdr warn]: Invalid prop: minWidth cannot be greater than maxWidth'); // eslint-disable-next-line

    if (this.maxWidth && this.minHeight > this.maxHeight) console.warn('[Vdr warn]: Invalid prop: minHeight cannot be greater than maxHeight');
    this.resetBoundsAndMouseState();
  },

  mounted() {
    this.$nextTick(() => {
      this.left = this.x;
      this.top = this.y;

      if (!this.enableNativeDrag) {
        this.$el.ondragstart = () => false;
      }

      const [parentWidth, parentHeight] = this.getParentSize();
      this.parentWidth = parentWidth;
      this.parentHeight = parentHeight;
      const [width, height] = Object(dom["b" /* getComputedSize */])(this.$el);
      this.aspectFactor = (this.w !== 'auto' ? this.w : width) / ((this.h !== 'auto' ? this.h : height) - this.childHeaderHeight);
      this.width = this.w !== 'auto' ? this.w : width;
      this.height = this.h !== 'auto' ? this.h : height;
      this.right = this.parentWidth - this.width - this.left;
      this.bottom = this.parentHeight - this.height - this.top;

      if (this.active) {
        this.$emit('activated');
      }

      if (this.computedLeft + this.width > constants["n" /* mainCanvasWidth */] + constants["l" /* mainCanvasOffsetX */] + this.zoomX) {
        this.$emit('update-asset', constants["n" /* mainCanvasWidth */] - constants["l" /* mainCanvasOffsetX */] + this.zoomX - this.width, this.top);
      }

      Object(dom["a" /* addEvent */])(document.documentElement, 'mousedown', this.deselect);
      Object(dom["a" /* addEvent */])(document.documentElement, 'touchend touchcancel', this.deselect);
      Object(dom["a" /* addEvent */])(window, 'resize', this.checkParentSize);
    });
  },

  beforeDestroy() {
    Object(dom["d" /* removeEvent */])(document.documentElement, 'mousedown', this.deselect);
    Object(dom["d" /* removeEvent */])(document.documentElement, 'touchstart', this.handleUp);
    Object(dom["d" /* removeEvent */])(document.documentElement, 'mousemove', this.move);
    Object(dom["d" /* removeEvent */])(document.documentElement, 'touchmove', this.move);
    Object(dom["d" /* removeEvent */])(document.documentElement, 'mouseup', this.handleUp);
    Object(dom["d" /* removeEvent */])(document.documentElement, 'touchend touchcancel', this.deselect);
    Object(dom["d" /* removeEvent */])(window, 'resize', this.checkParentSize);
  },

  methods: {
    resetBoundsAndMouseState() {
      this.mouseClickPosition = {
        mouseX: 0,
        mouseY: 0,
        x: 0,
        y: 0,
        w: 0,
        h: 0
      };
      this.bounds = {
        minLeft: null,
        maxLeft: null,
        minRight: null,
        maxRight: null,
        minTop: null,
        maxTop: null,
        minBottom: null,
        maxBottom: null
      };
    },

    checkParentSize() {
      if (this.parent) {
        const [newParentWidth, newParentHeight] = this.getParentSize();
        this.parentWidth = newParentWidth;
        this.parentHeight = newParentHeight;
        this.right = this.parentWidth - this.width - this.left;
        this.bottom = this.parentHeight - this.height - this.top;
      }
    },

    getParentSize() {
      if (this.parent) {
        return [constants["n" /* mainCanvasWidth */], constants["k" /* mainCanvasHeight */]];
      }

      return [null, null];
    },

    elementTouchDown(e) {
      eventsFor = events.touch;
      this.elementDown(e);
    },

    elementMouseDown(e) {
      eventsFor = events.mouse;
      this.elementDown(e);
    },

    elementDown(e) {
      if (e instanceof MouseEvent && e.which !== 1) {
        return;
      }

      const target = e.target || e.srcElement;

      if (this.$el.contains(target)) {
        if (this.onDragStart(e) === false) {
          return;
        }

        if (this.dragHandle && !Object(dom["c" /* matchesSelectorToParentElements */])(target, this.dragHandle, this.$el) || this.dragCancel && Object(dom["c" /* matchesSelectorToParentElements */])(target, this.dragCancel, this.$el)) {
          this.dragging = false;
          return;
        }

        if (!this.enabled) {
          this.enabled = true;
          this.$emit('activated');
          this.$emit('update:active', true);
        }

        if (this.draggable) {
          this.dragEnable = true;
        }

        this.mouseClickPosition.mouseX = e.touches ? e.touches[0].pageX : e.pageX;
        this.mouseClickPosition.mouseY = e.touches ? e.touches[0].pageY : e.pageY;
        this.mouseClickPosition.left = this.left;
        this.mouseClickPosition.right = this.right;
        this.mouseClickPosition.top = this.top;
        this.mouseClickPosition.bottom = this.bottom;

        if (this.parent) {
          this.bounds = this.calcDragLimits();
        }

        Object(dom["a" /* addEvent */])(document.documentElement, eventsFor.move, this.move);
        Object(dom["a" /* addEvent */])(document.documentElement, eventsFor.stop, this.handleUp);
      }
    },

    calcDragLimits() {
      return {
        minLeft: constants["l" /* mainCanvasOffsetX */] - this.zoomX,
        maxLeft: this.parentWidth + constants["l" /* mainCanvasOffsetX */] - this.zoomX - this.width,
        minRight: this.right % this.grid[0],
        maxRight: Math.floor((this.parentWidth - this.width - this.right) / this.grid[0]) * this.grid[0] + this.right,
        minTop: constants["m" /* mainCanvasOffsetY */] - this.zoomY,
        maxTop: this.parentHeight + constants["m" /* mainCanvasOffsetY */] - this.zoomY - this.height,
        minBottom: this.bottom % this.grid[1],
        maxBottom: Math.floor((this.parentHeight - this.height - this.bottom) / this.grid[1]) * this.grid[1] + this.bottom
      };
    },

    deselect(e) {
      const target = e.target || e.srcElement;
      const regex = new RegExp(this.className + '-([trmbl]{2})', '');

      if (!this.$el.contains(target) && !regex.test(target.className)) {
        if (this.enabled && !this.preventDeactivation) {
          this.enabled = false;
          this.$emit('deactivated');
          this.$emit('update:active', false);
        }

        Object(dom["d" /* removeEvent */])(document.documentElement, eventsFor.move, this.handleResize);
      }

      this.resetBoundsAndMouseState();
    },

    handleTouchDown(handle, e) {
      eventsFor = events.touch;
      this.handleDown(handle, e);
    },

    handleDown(handle, e) {
      if (e instanceof MouseEvent && e.which !== 1) {
        return;
      }

      if (this.onResizeStart(handle, e) === false) {
        return;
      }

      if (e.stopPropagation) e.stopPropagation(); // Here we avoid a dangerous recursion by faking
      // corner handles as middle handles

      this.originalHandle = handle;

      if (this.lockAspectRatio && !handle.includes('m')) {
        this.handle = 'm' + handle.substring(1);
      } else {
        this.handle = handle;
      }

      this.resizeEnable = true;
      this.mouseClickPosition.mouseX = e.touches ? e.touches[0].pageX : e.pageX;
      this.mouseClickPosition.mouseY = e.touches ? e.touches[0].pageY : e.pageY;
      this.mouseClickPosition.left = this.left;
      this.mouseClickPosition.right = this.right;
      this.mouseClickPosition.top = this.top;
      this.mouseClickPosition.bottom = this.bottom;
      this.bounds = this.calcResizeLimits();
      Object(dom["a" /* addEvent */])(document.documentElement, eventsFor.move, this.handleResize);
      Object(dom["a" /* addEvent */])(document.documentElement, eventsFor.stop, this.handleUp);
    },

    calcResizeLimits() {
      const minW = this.minW;
      let minH = this.minH;
      let maxW = this.maxW;
      let maxH = this.maxH;
      const aspectFactor = this.aspectFactor;
      const [gridX, gridY] = this.grid;
      const width = this.width;
      const height = this.height;
      const left = this.left;
      const top = this.top;
      const right = this.right;
      const bottom = this.bottom;

      if (this.lockAspectRatio) {
        minH = minW / aspectFactor + this.childHeaderHeight; // if (minW / minH > aspectFactor) {
        //   minH = minW / aspectFactor
        // } else {
        //   minW = aspectFactor * minH
        // }

        if (maxW && maxH) {
          maxW = Math.min(maxW, aspectFactor * maxH);
          maxH = Math.min(maxH, maxW / aspectFactor);
        } else if (maxW) {
          maxH = maxW / aspectFactor;
        } else if (maxH) {
          maxW = aspectFactor * maxH;
        }
      }

      maxW = maxW - maxW % gridX;
      maxH = maxH - maxH % gridY;
      const limits = {
        minLeft: null,
        maxLeft: null,
        minTop: null,
        maxTop: null,
        minRight: null,
        maxRight: null,
        minBottom: null,
        maxBottom: null
      };

      if (this.parent) {
        limits.minLeft = left % gridX;
        limits.maxLeft = left + Math.floor((width - minW) / gridX) * gridX;
        limits.minTop = top % gridY;
        limits.maxTop = top + Math.floor((height - minH) / gridY) * gridY;
        limits.minRight = right % gridX;
        limits.maxRight = right + Math.floor((width - minW) / gridX) * gridX;
        limits.minBottom = bottom % gridY;
        limits.maxBottom = bottom + Math.floor((height - minH) / gridY) * gridY;

        if (maxW) {
          limits.minLeft = Math.max(limits.minLeft, this.parentWidth - right - maxW);
          limits.minRight = Math.max(limits.minRight, this.parentWidth - left - maxW);
        }

        if (maxH) {
          limits.minTop = Math.max(limits.minTop, this.parentHeight - bottom - maxH);
          limits.minBottom = Math.max(limits.minBottom, this.parentHeight - top - maxH);
        } // if (this.lockAspectRatio) {
        //   limits.minLeft = Math.max(limits.minLeft, left - top * aspectFactor)
        //   limits.minTop = Math.max(limits.minTop, top - left / aspectFactor)
        //   limits.minRight = Math.max(limits.minRight, right - bottom * aspectFactor)
        //   limits.minBottom = Math.max(limits.minBottom, bottom - right / aspectFactor)
        // }

      } else {
        limits.minLeft = null;
        limits.maxLeft = left + Math.floor((width - minW) / gridX) * gridX;
        limits.minTop = null;
        limits.maxTop = top + Math.floor((height - minH) / gridY) * gridY;
        limits.minRight = null;
        limits.maxRight = right + Math.floor((width - minW) / gridX) * gridX;
        limits.minBottom = null;
        limits.maxBottom = bottom + Math.floor((height - minH) / gridY) * gridY;

        if (maxW) {
          limits.minLeft = -(right + maxW);
          limits.minRight = -(left + maxW);
        }

        if (maxH) {
          limits.minTop = -(bottom + maxH);
          limits.minBottom = -(top + maxH);
        }

        if (this.lockAspectRatio && maxW && maxH) {
          limits.minLeft = Math.min(limits.minLeft, -(right + maxW));
          limits.minTop = Math.min(limits.minTop, -(maxH + bottom));
          limits.minRight = Math.min(limits.minRight, -left - maxW);
          limits.minBottom = Math.min(limits.minBottom, -top - maxH);
        }
      }

      return limits;
    },

    move(e) {
      if (this.resizing) {
        this.handleResize(e);
      } else if (this.dragEnable) {
        this.handleDrag(e);
      }
    },

    handleDrag(e) {
      const axis = this.axis;
      const grid = this.grid;
      const bounds = this.bounds;
      const mouseClickPosition = this.mouseClickPosition;
      const tmpDeltaX = axis && axis !== 'y' ? (mouseClickPosition.mouseX - (e.touches ? e.touches[0].pageX : e.pageX)) / this.zoomIndex : 0;
      const tmpDeltaY = axis && axis !== 'x' ? (mouseClickPosition.mouseY - (e.touches ? e.touches[0].pageY : e.pageY)) / this.zoomIndex : 0;
      const [deltaX, deltaY] = Object(fns["e" /* snapToGrid */])(grid, tmpDeltaX, tmpDeltaY, this.scale);
      const left = Object(fns["d" /* restrictToBounds */])(mouseClickPosition.left - deltaX, bounds.minLeft, bounds.maxLeft);
      const top = Object(fns["d" /* restrictToBounds */])(mouseClickPosition.top - deltaY, bounds.minTop, bounds.maxTop);

      if (this.onDrag(left, top) === false) {
        return;
      }

      const right = Object(fns["d" /* restrictToBounds */])(mouseClickPosition.right + deltaX, bounds.minRight, bounds.maxRight);
      const bottom = Object(fns["d" /* restrictToBounds */])(mouseClickPosition.bottom + deltaY, bounds.minBottom, bounds.maxBottom);
      this.left = left;
      this.top = top;
      this.right = right;
      this.bottom = bottom;
      this.$emit('dragging', this.left, this.top);
      this.dragging = true;
    },

    moveHorizontally(val) {
      // should calculate with scale 1.
      const [deltaX, _] = Object(fns["e" /* snapToGrid */])(this.grid, val, this.top, 1);
      const left = Object(fns["d" /* restrictToBounds */])(deltaX, this.bounds.minLeft, this.bounds.maxLeft);
      this.left = left;
      this.right = this.parentWidth - this.width - left;
    },

    moveVertically(val) {
      // should calculate with scale 1.
      const [_, deltaY] = Object(fns["e" /* snapToGrid */])(this.grid, this.left, val, 1);
      const top = Object(fns["d" /* restrictToBounds */])(deltaY, this.bounds.minTop, this.bounds.maxTop);
      this.top = top;
      this.bottom = this.parentHeight - this.height - top;
    },

    handleResize(e) {
      let left = this.left;
      let top = this.top;
      let right = this.right;
      let bottom = this.bottom;
      const mouseClickPosition = this.mouseClickPosition;
      const aspectFactor = this.aspectFactor;
      const tmpDeltaX = (mouseClickPosition.mouseX - (e.touches ? e.touches[0].pageX : e.pageX)) / this.zoomIndex;
      const tmpDeltaY = (mouseClickPosition.mouseY - (e.touches ? e.touches[0].pageY : e.pageY)) / this.zoomIndex;

      if (!this.widthTouched && tmpDeltaX) {
        this.widthTouched = true;
      }

      if (!this.heightTouched && tmpDeltaY) {
        this.heightTouched = true;
      }

      const [deltaX, deltaY] = Object(fns["e" /* snapToGrid */])(this.grid, tmpDeltaX, tmpDeltaY, this.scale);

      if (this.handle.includes('b')) {
        bottom = Object(fns["d" /* restrictToBounds */])(mouseClickPosition.bottom + deltaY, this.bounds.minBottom, this.bounds.maxBottom);

        if (this.lockAspectRatio && this.resizingOnY) {
          if (this.originalHandle === 'bm') {
            right = this.right - (this.bottom - bottom) * (aspectFactor / 2);
            left = this.left - (this.bottom - bottom) * (aspectFactor / 2);
          } else if (this.originalHandle === 'br') {
            right = this.right - (this.bottom - bottom) * aspectFactor;
          } else {
            left = this.left - (this.bottom - bottom) * aspectFactor;
          }
        }
      } else if (this.handle.includes('t')) {
        top = Object(fns["d" /* restrictToBounds */])(mouseClickPosition.top - deltaY, this.bounds.minTop, this.bounds.maxTop);

        if (this.lockAspectRatio && this.resizingOnY) {
          if (this.originalHandle === 'tm') {
            left = this.left - (this.top - top) * (aspectFactor / 2);
            right = this.right - (this.top - top) * (aspectFactor / 2);
          } else if (this.originalHandle === 'tr') {
            right = this.right - (this.top - top) * aspectFactor;
          } else {
            left = this.left - (this.top - top) * aspectFactor;
          }
        }
      }

      if (this.handle.includes('r')) {
        right = Object(fns["d" /* restrictToBounds */])(mouseClickPosition.right + deltaX, this.bounds.minRight, this.bounds.maxRight);

        if (this.lockAspectRatio && this.resizingOnX) {
          if (this.originalHandle === 'mr') {
            bottom = this.bottom - (this.right - right) / (aspectFactor * 2);
            top = this.top - (this.right - right) / (aspectFactor * 2);
          } else if (this.originalHandle === 'br') {
            bottom = this.bottom - (this.right - right) / aspectFactor;
          } else {
            top = this.top - (this.right - right) / aspectFactor;
          }
        }
      } else if (this.handle.includes('l')) {
        left = Object(fns["d" /* restrictToBounds */])(mouseClickPosition.left - deltaX, this.bounds.minLeft, this.bounds.maxLeft);

        if (this.lockAspectRatio && this.resizingOnX) {
          if (this.originalHandle === 'ml') {
            top = this.top - (this.left - left) / (aspectFactor * 2);
            bottom = this.bottom - (this.left - left) / (aspectFactor * 2);
          } else if (this.originalHandle === 'tl') {
            top = this.top - (this.left - left) / aspectFactor;
          } else {
            bottom = this.bottom - (this.left - left) / aspectFactor;
          }
        }
      }

      const width = Object(fns["b" /* computeWidth */])(this.parentWidth, left, right);
      const height = Object(fns["a" /* computeHeight */])(this.parentHeight, top, bottom);

      if (this.onResize(this.handle, left, top, width, height) === false) {
        return;
      }

      this.left = left;
      this.top = top;
      this.right = right;
      this.bottom = bottom;
      this.width = width;
      this.height = height;
      this.$emit('resizing', this.left, this.top, this.width, this.height, e.target.className);
      this.resizing = true;
    },

    changeWidth(val) {
      // should calculate with scale 1.
      const [newWidth, _] = Object(fns["e" /* snapToGrid */])(this.grid, val, 0, 1);
      const right = Object(fns["d" /* restrictToBounds */])(this.parentWidth - newWidth - this.left, this.bounds.minRight, this.bounds.maxRight);
      let bottom = this.bottom;

      if (this.lockAspectRatio) {
        bottom = this.bottom - (this.right - right) / this.aspectFactor;
      }

      const width = Object(fns["b" /* computeWidth */])(this.parentWidth, this.left, right);
      const height = Object(fns["a" /* computeHeight */])(this.parentHeight, this.top, bottom);
      this.right = right;
      this.bottom = bottom;
      this.width = width;
      this.height = height;
    },

    changeHeight(val) {
      // should calculate with scale 1.
      // const [_, newHeight] = snapToGrid(this.grid, 0, val, 1)
      // let bottom = restrictToBounds(
      //     (this.parentHeight - newHeight - this.top),
      //     this.bounds.minBottom,
      //     this.bounds.maxBottom
      // )
      // let right = this.right
      // if (this.lockAspectRatio) {
      //   right = this.right - (this.bottom - bottom) * this.aspectFactor
      // }
      // const width = computeWidth(this.parentWidth, this.left, right)
      // const height = computeHeight(this.parentHeight, this.top, bottom)
      // this.right = right
      // this.bottom = bottom
      this.height = val;
      this.bottom = this.parentHeight - this.height - this.top;
      this.aspectFactor = this.width / (this.height - this.childHeaderHeight); // this.width = width
      // console.log('valvalval', val, this.height, this.bottom, this.aspectFactor)
    },

    handleUp(e) {
      this.handle = null;
      this.resetBoundsAndMouseState();
      this.dragEnable = false;
      this.resizeEnable = false;

      if (this.resizing) {
        this.resizing = false;
        this.$emit('resizestop', this.left, this.top, this.width, this.height);
      }

      if (this.dragging) {
        this.dragging = false;
        this.$emit('dragstop', this.left, this.top);
      }

      Object(dom["d" /* removeEvent */])(document.documentElement, eventsFor.move, this.handleResize);
    }

  }
});
// CONCATENATED MODULE: ./components/classroom/vue-draggable-resizable/VueDraggableResizable.vue?vue&type=script&lang=js&
 /* harmony default export */ var vue_draggable_resizable_VueDraggableResizablevue_type_script_lang_js_ = (VueDraggableResizablevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/classroom/vue-draggable-resizable/VueDraggableResizable.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  vue_draggable_resizable_VueDraggableResizablevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "c2bf3b4a"
  
)

/* harmony default export */ var VueDraggableResizable = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 995:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1020);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("07a1f444", content, true)

/***/ }),

/***/ 999:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "c", function() { return matchesSelectorToParentElements; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "b", function() { return getComputedSize; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return addEvent; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "d", function() { return removeEvent; });
/* harmony import */ var _fns__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(961);

function matchesSelectorToParentElements(el, selector, baseNode) {
  let node = el;
  const matchesSelectorFunc = ['matches', 'webkitMatchesSelector', 'mozMatchesSelector', 'msMatchesSelector', 'oMatchesSelector'].find(func => Object(_fns__WEBPACK_IMPORTED_MODULE_0__[/* isFunction */ "c"])(node[func]));
  if (!Object(_fns__WEBPACK_IMPORTED_MODULE_0__[/* isFunction */ "c"])(node[matchesSelectorFunc])) return false;

  do {
    if (node[matchesSelectorFunc](selector)) return true;
    if (node === baseNode) return false;
    node = node.parentNode;
  } while (node);

  return false;
}
function getComputedSize($el) {
  const style = window.getComputedStyle($el);
  return [parseFloat(style.getPropertyValue('width'), 10), parseFloat(style.getPropertyValue('height'), 10)];
}
function addEvent(el, event, handler) {
  if (!el) {
    return;
  }

  if (el.attachEvent) {
    el.attachEvent('on' + event, handler);
  } else if (el.addEventListener) {
    el.addEventListener(event, handler, true);
  } else {
    el['on' + event] = handler;
  }
}
function removeEvent(el, event, handler) {
  if (!el) {
    return;
  }

  if (el.detachEvent) {
    el.detachEvent('on' + event, handler);
  } else if (el.removeEventListener) {
    el.removeEventListener(event, handler, true);
  } else {
    el['on' + event] = null;
  }
}

/***/ })

};;
//# sourceMappingURL=classroom-video-whereby.js.map