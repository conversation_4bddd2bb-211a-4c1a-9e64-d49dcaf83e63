exports.ids = [121,104];
exports.modules = {

/***/ 1387:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/SummaryInfo.vue?vue&type=template&id=0a38b2cd&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.item)?_c('user-setting-template',{attrs:{"title":_vm.$t('summary'),"submit-func":_vm.submitData}},[_c('div',{staticClass:"mb-md-2"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('short_summary'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n            "+_vm._s(_vm.$t(
                'provide_short_and_snappy_description_of_what_you_specialise_in_teaching'
              ))+"\n          ")]),_vm._v(" "),_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.item.shortSummary,"no-resize":"","height":"78","counter":"120","solo":"","dense":"","rules":_vm.shortSummaryRules},on:{"input":function($event){return _vm.updateValue($event, 'shortSummary')}}})],1)])],1)],1),_vm._v(" "),_c('div',{staticClass:"mb-md-2"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('longer_summary'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n            "+_vm._s(_vm.$t('provide_longer_introduction_of_yourself_and_your_teaching'))+"\n          ")]),_vm._v(" "),_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.item.longSummary,"no-resize":"","height":"140","solo":"","dense":"","counter":"500","rules":_vm.longSummaryRules},on:{"input":function($event){return _vm.updateValue($event, 'longSummary')}}})],1)])],1)],1),_vm._v(" "),_c('div',[_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n          "+_vm._s(_vm.$t('interesting_facts'))+"\n        ")]),_vm._v(" "),_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n          "+_vm._s(_vm.$t(
              'what_are_unique_facts_about_you_that_students_might_find_interesting'
            ))+"\n        ")])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 mb-2"},[_c('div',{staticClass:"input-wrap"},[_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.factsAbout[0],"no-resize":"","height":"80","solo":"","dense":"","hide-details":"","placeholder":_vm.$t('Interesting fact number 1')},on:{"input":function($event){return _vm.updateFact($event, 0)}}})],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 mb-2"},[_c('div',{staticClass:"input-wrap"},[_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.factsAbout[1],"no-resize":"","height":"80","solo":"","dense":"","hide-details":"","placeholder":_vm.$t('Interesting fact number 2')},on:{"input":function($event){return _vm.updateFact($event, 1)}}})],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6"},[_c('div',{staticClass:"input-wrap"},[_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.factsAbout[2],"no-resize":"","height":"80","solo":"","dense":"","hide-details":"","placeholder":_vm.$t('Interesting fact number 3')},on:{"input":function($event){return _vm.updateFact($event, 2)}}})],1)])],1)],1)]):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/SummaryInfo.vue?vue&type=template&id=0a38b2cd&

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/SummaryInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var SummaryInfovue_type_script_lang_js_ = ({
  name: 'SummaryInfo',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"]
  },

  data() {
    return {
      shortSummaryRules: [v => v === null || (v === null || v === void 0 ? void 0 : v.length) <= 120],
      longSummaryRules: [v => v === null || (v === null || v === void 0 ? void 0 : v.length) <= 500]
    };
  },

  computed: {
    item() {
      return this.$store.state.settings.summaryItem;
    },

    factsAbout() {
      return this.item.factsAbout || [];
    }

  },

  beforeCreate() {
    this.$store.dispatch('settings/getSummary');
  },

  methods: {
    updateFact(value, index) {
      const facts = [...this.factsAbout];
      facts[index] = value;
      this.updateValue(facts, 'factsAbout');
    },

    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_SUMMARY_ITEM', {
        [property]: value
      });
    },

    submitData() {
      this.$store.dispatch('settings/updateSummary');
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/SummaryInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_SummaryInfovue_type_script_lang_js_ = (SummaryInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(897);

// CONCATENATED MODULE: ./components/user-settings/SummaryInfo.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_SummaryInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "273fbd95"
  
)

/* harmony default export */ var SummaryInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */




installComponents_default()(component, {VCol: VCol["a" /* default */],VRow: VRow["a" /* default */],VTextarea: VTextarea["a" /* default */]})


/***/ }),

/***/ 929:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/UserSettingTemplate.vue?vue&type=template&id=6326778e&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-form',{ref:"form",attrs:{"value":_vm.formValid},on:{"validate":_vm.validate,"submit":function($event){$event.preventDefault();return _vm.submit.apply(null, arguments)},"input":function($event){_vm.formValid = $event}}},[_c('div',{staticClass:"user-settings-panel"},[_c('div',{staticClass:"panel"},[(_vm.$vuetify.breakpoint.smAndUp)?_c('div',{staticClass:"panel-head d-none d-sm-block"},[_c('div',{staticClass:"panel-head-title font-weight-medium mb-2 mb-sm-3 mb-md-5"},[_vm._v("\n          "+_vm._s(_vm.title)+"\n        ")])]):_vm._e(),_vm._v(" "),_c('div',{staticClass:"panel-body"},[_vm._t("default")],2),_vm._v(" "),(!_vm.hideFooter)?_c('div',{staticClass:"panel-footer d-flex justify-center justify-sm-end"},[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"primary","type":"submit","disabled":!_vm.valid}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"18","height":"18","viewBox":"0 0 18 18"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#save-icon")}})]),_vm._v("\n          "+_vm._s(_vm.$t('save_changes'))+"\n        ")])],1):_vm._e()])])])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/UserSettingTemplate.vue?vue&type=template&id=6326778e&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/UserSettingTemplate.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var UserSettingTemplatevue_type_script_lang_js_ = ({
  name: 'UserSettingTemplate',
  props: {
    title: {
      type: String,
      required: true
    },
    hideFooter: {
      type: Boolean,
      default: false
    },
    customValid: {
      type: Boolean,
      default: true
    },
    submitFunc: {
      type: Function,
      default: () => {}
    }
  },

  data() {
    return {
      formValid: true
    };
  },

  computed: {
    valid() {
      return this.formValid && this.customValid;
    }

  },

  mounted() {
    this.validate();
  },

  methods: {
    validate() {
      this.$refs.form.validate();
    },

    submit() {
      if (!this.valid) return;
      this.submitFunc();
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/UserSettingTemplate.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_UserSettingTemplatevue_type_script_lang_js_ = (UserSettingTemplatevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(896);

// CONCATENATED MODULE: ./components/user-settings/UserSettingTemplate.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(988)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_UserSettingTemplatevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "ed2bb580"
  
)

/* harmony default export */ var UserSettingTemplate = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */



installComponents_default()(component, {VBtn: VBtn["a" /* default */],VForm: VForm["a" /* default */]})


/***/ }),

/***/ 951:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(989);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("419d3f06", content, true, context)
};

/***/ }),

/***/ 988:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingTemplate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(951);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingTemplate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingTemplate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingTemplate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingTemplate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 989:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-settings-panel{padding:44px;border-radius:20px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1)}@media only screen and (max-width:1439px){.user-settings-panel{padding:24px}}@media only screen and (max-width:767px){.user-settings-panel{padding:0;box-shadow:none}}.user-settings-panel .row{margin:0 -14px!important}.user-settings-panel .col{padding:0 14px!important}.user-settings-panel .panel{color:var(--v-greyDark-base)}.user-settings-panel .panel-head-title{font-size:24px;line-height:1.333;color:var(--v-darkLight-base)}@media only screen and (max-width:1439px){.user-settings-panel .panel-head-title{font-size:20px}}.user-settings-panel .panel-body .chips>div{margin-top:6px}.user-settings-panel .panel-body .price-input .v-text-field--outlined.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot{min-height:32px!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr>th{color:var(--v-dark-base)}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr:last-child>th{border:none!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:hover:not(.v-data-table__expanded__content):not(.v-data-table__empty-wrapper){background:none}.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tbody>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tfoot>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>thead>tr>td{height:38px;color:var(--v-greyDark-base)}.user-settings-panel .panel-footer{margin-top:115px}@media only screen and (max-width:1439px){.user-settings-panel .panel-footer{margin-top:56px}}.user-settings-panel .panel-footer .v-btn{letter-spacing:.1px}@media only screen and (max-width:479px){.user-settings-panel .panel-footer .v-btn{width:100%!important}}.user-settings-panel .l-checkbox .v-input--selection-controls__input{width:16px;height:16px;margin-top:2px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

};;
//# sourceMappingURL=user-settings-summary-info.js.map