exports.ids = [60];
exports.modules = {

/***/ 1071:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1105);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("20c2c1c7", content, true)

/***/ }),

/***/ 1105:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".slick-track[data-v-e4caeaf8]{position:relative;top:0;left:0;display:block;transform:translateZ(0)}.slick-track.slick-center[data-v-e4caeaf8]{margin-left:auto;margin-right:auto}.slick-track[data-v-e4caeaf8]:after,.slick-track[data-v-e4caeaf8]:before{display:table;content:\"\"}.slick-track[data-v-e4caeaf8]:after{clear:both}.slick-loading .slick-track[data-v-e4caeaf8]{visibility:hidden}.slick-slide[data-v-e4caeaf8]{display:none;float:left;height:100%;min-height:1px}[dir=rtl] .slick-slide[data-v-e4caeaf8]{float:right}.slick-slide img[data-v-e4caeaf8]{display:block}.slick-slide.slick-loading img[data-v-e4caeaf8]{display:none}.slick-slide.dragging img[data-v-e4caeaf8]{pointer-events:none}.slick-initialized .slick-slide[data-v-e4caeaf8]{display:block}.slick-loading .slick-slide[data-v-e4caeaf8]{visibility:hidden}.slick-vertical .slick-slide[data-v-e4caeaf8]{display:block;height:auto;border:1px solid transparent}.slick-arrow.slick-hidden[data-v-21137603]{display:none}.slick-slider[data-v-3d1a4f76]{position:relative;display:block;box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;touch-action:pan-y;-webkit-tap-highlight-color:transparent}.slick-list[data-v-3d1a4f76]{position:relative;display:block;overflow:hidden;margin:0;padding:0;transform:translateZ(0)}.slick-list[data-v-3d1a4f76]:focus{outline:none}.slick-list.dragging[data-v-3d1a4f76]{cursor:pointer;cursor:hand}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1211:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1280);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("335dfb13", content, true, context)
};

/***/ }),

/***/ 1279:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TestimonialsSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1211);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TestimonialsSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TestimonialsSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TestimonialsSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TestimonialsSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1280:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".trusted-slider{max-width:1640px;margin:50px auto;background:none!important;position:relative!important;height:-webkit-max-content;height:-moz-max-content;height:max-content}@media(max-width:1170px){.trusted-slider{margin:30px auto 50px}}@media only screen and (max-width:991px){.trusted-slider{margin:30px auto 0}}.trusted-slider:before{content:none!important}.trusted-slider .slick-track{display:flex!important;align-items:center}.trusted-slider .slick-slide{padding:0 15px;transition:all .5s ease-in}@media only screen and (max-width:639px){.trusted-slider .slick-slide{padding:0 10px}}.trusted-slider .slick-slide:not(.slick-center){transform:translateZ(0) scale(.8)!important;opacity:.7}@media only screen and (max-width:991px){.trusted-slider .slick-slide:not(.slick-center){transform:translateZ(0) scale(.9)!important;opacity:.6}}.trusted-slider .slick-slide .slider-elem{display:flex;width:100%;max-width:1000px;height:-webkit-max-content;height:-moz-max-content;height:max-content;margin:0 auto;padding:40px;background:#fcc062;border-radius:15px}@media only screen and (max-width:991px){.trusted-slider .slick-slide .slider-elem{padding:35px}}@media only screen and (max-width:767px){.trusted-slider .slick-slide .slider-elem{padding:25px}}@media only screen and (max-width:639px){.trusted-slider .slick-slide .slider-elem{flex-direction:column;align-items:center}}.trusted-slider .slick-slide .slider-elem__title{margin-bottom:20px;color:#fff;font-weight:700;font-size:25px}@media only screen and (max-width:767px){.trusted-slider .slick-slide .slider-elem__title{margin-bottom:12px;font-size:22px}}@media only screen and (max-width:639px){.trusted-slider .slick-slide .slider-elem__title{margin-bottom:8px;text-align:center;font-size:18px}}@media only screen and (max-width:479px){.trusted-slider .slick-slide .slider-elem__title{font-size:16px}}.trusted-slider .slick-slide .slider-elem__text{color:#fff;font-weight:500;font-size:20px;line-height:1.4}@media only screen and (max-width:767px){.trusted-slider .slick-slide .slider-elem__text{font-size:16px}}@media only screen and (max-width:639px){.trusted-slider .slick-slide .slider-elem__text{font-size:15px}}@media only screen and (max-width:479px){.trusted-slider .slick-slide .slider-elem__text{font-size:14px}}.trusted-slider .slick-slide .slider-elem__content{display:flex;flex-direction:column;justify-content:center}.trusted-slider .slick-slide .slider-elem__img{height:178px;width:178px;-o-object-fit:cover;object-fit:cover;border-radius:50%;margin-right:52px}@media only screen and (max-width:991px){.trusted-slider .slick-slide .slider-elem__img{margin-right:35px}}@media only screen and (max-width:767px){.trusted-slider .slick-slide .slider-elem__img{height:100px;width:100px;margin-right:20px}}@media only screen and (max-width:639px){.trusted-slider .slick-slide .slider-elem__img{height:76px;width:76px;margin-right:0;margin-bottom:14px}}.trusted-slider .slick-arrow{position:absolute;top:50%;background-color:rgba(252,192,98,.95);transform:translateY(-50%)}.trusted-slider .slick-arrow.slick-next{right:8%}.trusted-slider .slick-arrow.slick-prev{left:8%}.trusted-slider--dark .slick-slide .slider-elem{background:var(--v-darkLight-base)}.trusted-slider--dark .slick-slide .slider-elem__title{color:#f9af48}.trusted-slider--dark .slick-arrow{background-color:#000}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1384:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/landing-page/TestimonialsSlider.vue?vue&type=template&id=0dd1e3c5&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:['trusted-slider', { 'trusted-slider--dark': _vm.dark }]},[_c('client-only',[_c('VueSlickCarousel',_vm._b({},'VueSlickCarousel',_vm.sliderSettings,false),_vm._l((_vm.data),function(item,index){return _c('div',{key:index},[_c('div',{staticClass:"slider-elem"},[_c('div',{staticClass:"slider-elem__img-wrap"},[_c('img',{staticClass:"slider-elem__img",attrs:{"src":_vm.getSrcAvatar(item.profileImage),"alt":""}})]),_vm._v(" "),_c('div',{staticClass:"slider-elem__content"},[_c('div',{staticClass:"slider-elem__title"},[_vm._v(_vm._s(item.information))]),_vm._v(" "),_c('div',{staticClass:"slider-elem__text"},[_vm._v(_vm._s(item.description))])])])])}),0)],1)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/landing-page/TestimonialsSlider.vue?vue&type=template&id=0dd1e3c5&

// EXTERNAL MODULE: external "vue-slick-carousel"
var external_vue_slick_carousel_ = __webpack_require__(859);
var external_vue_slick_carousel_default = /*#__PURE__*/__webpack_require__.n(external_vue_slick_carousel_);

// EXTERNAL MODULE: ./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css
var vue_slick_carousel = __webpack_require__(1071);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/landing-page/TestimonialsSlider.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var TestimonialsSlidervue_type_script_lang_js_ = ({
  name: 'TestimonialsSlider',
  components: {
    VueSlickCarousel: external_vue_slick_carousel_default.a
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    dark: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      slider: null,
      sliderSettings: {
        centerMode: true,
        centerPadding: '210px',
        dots: false,
        focusOnSelect: true,
        infinite: true,
        speed: 800,
        slidesToShow: 1,
        slidesToScroll: 1,
        responsive: [{
          breakpoint: 1099,
          settings: {
            centerPadding: '170px'
          }
        }, {
          breakpoint: 991,
          settings: {
            arrows: false,
            centerPadding: '80px'
          }
        }, {
          breakpoint: 639,
          settings: {
            arrows: false,
            centerPadding: '60px'
          }
        }, {
          breakpoint: 480,
          settings: {
            arrows: false,
            centerPadding: '45px'
          }
        }]
      }
    };
  },

  methods: {
    getSrcAvatar(image, defaultImage = 'avatar.png') {
      return image || __webpack_require__(511)(`./${defaultImage}`);
    }

  }
});
// CONCATENATED MODULE: ./components/landing-page/TestimonialsSlider.vue?vue&type=script&lang=js&
 /* harmony default export */ var landing_page_TestimonialsSlidervue_type_script_lang_js_ = (TestimonialsSlidervue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/landing-page/TestimonialsSlider.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1279)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  landing_page_TestimonialsSlidervue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "014cf678"
  
)

/* harmony default export */ var TestimonialsSlider = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=landing-page-testimonials-slider.js.map