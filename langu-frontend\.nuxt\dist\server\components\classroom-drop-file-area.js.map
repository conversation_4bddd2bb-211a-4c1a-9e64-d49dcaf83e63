{"version": 3, "file": "components/classroom-drop-file-area.js", "sources": ["webpack:///./mixins/UploadFiles.vue", "webpack:///./mixins/UploadFiles.vue?903b", "webpack:///./mixins/UploadFiles.vue?17e1", "webpack:///./components/classroom/DropFileArea.vue?f3a0", "webpack:///./components/classroom/DropFileArea.vue?f476", "webpack:///./components/classroom/DropFileArea.vue?7b3c", "webpack:///./components/classroom/DropFileArea.vue?abb9", "webpack:///./components/classroom/DropFileArea.vue", "webpack:///./components/classroom/DropFileArea.vue?aafa", "webpack:///./components/classroom/DropFileArea.vue?7a11"], "sourcesContent": ["\nimport { getFileExtension } from '~/helpers'\nimport { MAX_FILE_SIZE } from '~/helpers/constants'\n\nexport default {\n  computed: {\n    lessonId() {\n      return this.$store.state.classroom.lessonId\n    },\n    role() {\n      return this.$store.getters['classroom/role']\n    },\n    acceptedFiles() {\n      return this.$store.state.classroom.acceptedFiles\n    },\n  },\n  methods: {\n    async uploadFiles(files) {\n      files = [...files]\n\n      const formData = new FormData()\n\n      for (let i = 0; i <= files.length - 1; i++) {\n        const file = files[i]\n        const fileExtension = getFileExtension(file.name)\n\n        if (file.size > MAX_FILE_SIZE) {\n          await this.$store.dispatch('snackbar/error', {\n            errorMessage: this.$t('filename_size_should_be_less_than', {\n              fileName: file.name,\n              value: `${(MAX_FILE_SIZE / 8 / 1000).toFixed(0)} Mb`,\n            }),\n            timeout: 5000,\n          })\n\n          continue\n        }\n\n        if (this.acceptedFiles.officeTypes.includes(fileExtension)) {\n          const { data, fileName } = await this.$store.dispatch(\n            'classroom/convertOfficeToPdf',\n            file\n          )\n\n          formData.append(i.toString(), new Blob([data]), fileName)\n        } else {\n          formData.append(i.toString(), file)\n        }\n      }\n\n      this.$store\n        .dispatch('classroom/uploadFiles', formData)\n        .then((assets) => {\n          let offsetX = 0\n          let offsetY = 0\n\n          this.$store.commit('classroom/addAssets', assets)\n\n          assets.forEach((asset) => {\n            const item = {\n              id: asset.id,\n              lessonId: this.lessonId,\n              asset: {\n                ...asset.asset,\n                index: this.$store.state.classroom.maxIndex + 1,\n                owner: this.role,\n                top:\n                  this.$store.getters['classroom/zoomAsset'].asset.y +\n                  offsetY +\n                  100,\n                left:\n                  this.viewportWidth / 2 +\n                  this.$store.getters['classroom/zoomAsset'].asset.x +\n                  offsetX -\n                  250,\n              },\n            }\n            const ext = getFileExtension(item.asset.path)\n\n            let type\n\n            if (this.acceptedFiles?.pdfTypes.includes(ext)) {\n              type = 'pdf'\n            } else if (this.acceptedFiles?.imageTypes.includes(ext)) {\n              type = 'image'\n            } else if (this.acceptedFiles?.audioTypes.includes(ext)) {\n              type = 'audio'\n            } else {\n              return\n            }\n\n            item.asset.type = type\n\n            this.$store.commit('classroom/moveAsset', item)\n            this.$store.dispatch('classroom/moveAsset', item)\n\n            this.$socket.emit('asset-added', item)\n\n            offsetX += 50\n            offsetY += 50\n          })\n        })\n        .catch((e) => {\n          // @TODO classroom\n          // Bugsnag.notify(e)\n          throw e\n        })\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UploadFiles.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UploadFiles.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./UploadFiles.vue?vue&type=script&lang=js&\"\nexport * from \"./UploadFiles.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"20b20e0a\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DropFileArea.vue?vue&type=style&index=0&id=190879a5&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"88facf08\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DropFileArea.vue?vue&type=style&index=0&id=190879a5&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".popup-load-files-drop-wrap[data-v-190879a5]{position:relative!important;display:flex;justify-content:center;align-items:center;height:100vh!important;background-color:hsla(0,0%,100%,.9);z-index:999999;pointer-events:none}.popup-load-files-drop-wrap .drop-area--wrapper[data-v-190879a5]{padding:30px}.popup-load-files-drop-wrap .drop-area--wrapper__dropbox-img[data-v-190879a5]{width:100%;height:auto}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{on:{\"drop\":function($event){$event.preventDefault();return _vm.drop.apply(null, arguments)},\"dragleave\":function($event){$event.preventDefault();return _vm.dragleave.apply(null, arguments)}}},[_vm._ssrNode(\"<div class=\\\"popup-load-files-drop-wrap\\\"\"+(_vm._ssrStyle(null,null, { display: (_vm.isDragging) ? '' : 'none' }))+\" data-v-190879a5><div class=\\\"drop-area--wrapper\\\" data-v-190879a5><img\"+(_vm._ssrAttr(\"src\",require('~/assets/images/classroom/dropfiles.svg')))+\" alt class=\\\"drop-area--wrapper__dropbox-img\\\" data-v-190879a5></div></div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport UploadFiles from '~/mixins/UploadFiles'\n\nexport default {\n  name: 'DropFileArea',\n  mixins: [UploadFiles],\n  props: {\n    viewportWidth: {\n      type: Number,\n      required: true,\n    },\n  },\n  computed: {\n    isDragging() {\n      return this.$store.state.classroom.isDragging\n    },\n  },\n  mounted() {\n    document.addEventListener('dragover', this.dragover)\n  },\n  beforeDestroy() {\n    document.removeEventListener('dragover', this.dragover)\n  },\n  methods: {\n    dragover(e) {\n      e.preventDefault()\n\n      this.$store.commit('classroom/isDraggingTrigger', true)\n    },\n    dragleave() {\n      this.$store.commit('classroom/isDraggingTrigger', false)\n    },\n    drop(e) {\n      this.$store.commit('classroom/isDraggingTrigger', false)\n      this.uploadFiles(e.dataTransfer.files)\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DropFileArea.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./DropFileArea.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./DropFileArea.vue?vue&type=template&id=190879a5&scoped=true&\"\nimport script from \"./DropFileArea.vue?vue&type=script&lang=js&\"\nexport * from \"./DropFileArea.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./DropFileArea.vue?vue&type=style&index=0&id=190879a5&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"190879a5\",\n  \"143d43b9\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;;;;;;;;;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAVA;AAWA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AALA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;AAEA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AAIA;AARA;AAHA;AAkBA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AA5FA;AAZA;;ACJA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AADA;AAMA;AACA;AACA;AACA;AACA;AAJA;AACA;AAIA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAbA;AApBA;;ACjBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}