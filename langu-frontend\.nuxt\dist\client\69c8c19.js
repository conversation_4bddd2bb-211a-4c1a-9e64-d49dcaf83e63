(window.webpackJsonp=window.webpackJsonp||[]).push([[97],{1507:function(e,t,r){var content=r(1508);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(19).default)("12a190a6",content,!0,{sourceMap:!1})},1508:function(e,t,r){var n=r(18)(!1);n.push([e.i,".v-input--checkbox.v-input--indeterminate.v-input--is-disabled{opacity:.6}",""]),e.exports=n},1611:function(e,t,r){"use strict";r(7),r(8),r(9),r(14),r(6),r(15);var n=r(2),o=(r(20),r(80),r(1507),r(1479),r(263)),l=r(117),c=r(1480);function d(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}function h(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(t){Object(n.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}t.a=c.a.extend({name:"v-checkbox",props:{indeterminate:Boolean,indeterminateIcon:{type:String,default:"$checkboxIndeterminate"},offIcon:{type:String,default:"$checkboxOff"},onIcon:{type:String,default:"$checkboxOn"}},data:function(){return{inputIndeterminate:this.indeterminate}},computed:{classes:function(){return h(h({},l.a.options.computed.classes.call(this)),{},{"v-input--selection-controls":!0,"v-input--checkbox":!0,"v-input--indeterminate":this.inputIndeterminate})},computedIcon:function(){return this.inputIndeterminate?this.indeterminateIcon:this.isActive?this.onIcon:this.offIcon},validationState:function(){if(!this.isDisabled||this.inputIndeterminate)return this.hasError&&this.shouldValidate?"error":this.hasSuccess?"success":null!==this.hasColor?this.computedColor:void 0}},watch:{indeterminate:function(e){var t=this;this.$nextTick((function(){return t.inputIndeterminate=e}))},inputIndeterminate:function(e){this.$emit("update:indeterminate",e)},isActive:function(){this.indeterminate&&(this.inputIndeterminate=!1)}},methods:{genCheckbox:function(){return this.$createElement("div",{staticClass:"v-input--selection-controls__input"},[this.$createElement(o.a,this.setTextColor(this.validationState,{props:{dense:this.dense,dark:this.dark,light:this.light}}),this.computedIcon),this.genInput("checkbox",h(h({},this.attrs$),{},{"aria-checked":this.inputIndeterminate?"mixed":this.isActive.toString()})),this.genRipple(this.setTextColor(this.rippleState))])},genDefaultSlot:function(){return[this.genCheckbox(),this.genLabel()]}}})},1657:function(e,t,r){var content=r(1764);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,r(19).default)("a55f04de",content,!0,{sourceMap:!1})},1763:function(e,t,r){"use strict";r(1657)},1764:function(e,t,r){var n=r(18)(!1);n.push([e.i,".v-application .v-dialog.schedule-lesson-dialog>.v-card{padding:32px 40px!important}@media only screen and (max-width:991px){.v-application .v-dialog.schedule-lesson-dialog>.v-card{padding:50px 18px 74px!important}.v-application .v-dialog.schedule-lesson-dialog>.v-card .dialog-content,.v-application .v-dialog.schedule-lesson-dialog>.v-card .schedule-lesson-dialog-body,.v-application .v-dialog.schedule-lesson-dialog>.v-card .v-form{height:100%}.v-application .v-dialog.schedule-lesson-dialog>.v-card .schedule-lesson-dialog-body{overflow-y:auto}}@media only screen and (min-width:768px){.v-application .v-dialog.schedule-lesson-dialog .details{padding-right:15px}}.v-application .v-dialog.schedule-lesson-dialog .details-row{display:flex;margin-top:16px}@media only screen and (max-width:767px){.v-application .v-dialog.schedule-lesson-dialog .details-row{margin-top:8px}}.v-application .v-dialog.schedule-lesson-dialog .details-row:first-child{margin-top:0}.v-application .v-dialog.schedule-lesson-dialog .details-row .property{width:115px;line-height:1.2!important}.v-application .v-dialog.schedule-lesson-dialog .details-row .value{padding-left:5px}.v-application .v-dialog.schedule-lesson-dialog .details-row .value--icon{position:relative;padding-left:26px}.v-application .v-dialog.schedule-lesson-dialog .details-row .value--icon .v-image{position:absolute;left:0;top:3px;border-radius:50%;overflow:hidden}.v-application .v-dialog.schedule-lesson-dialog .notice{position:relative;margin-top:4px;color:#a4a4a4}.v-application .v-dialog.schedule-lesson-dialog .notice p{margin:10px 0}.v-application .v-dialog.schedule-lesson-dialog .notice a{color:inherit;text-decoration:none}.v-application .v-dialog.schedule-lesson-dialog .notice a:hover{color:var(--v-orange-base)}.v-application .v-dialog.schedule-lesson-dialog .notice .spinner{position:absolute;bottom:-70px;left:50%;transform:translateX(-50%)}.v-application .v-dialog.schedule-lesson-dialog .details-notice{color:#969696}.v-application .v-dialog.schedule-lesson-dialog .l-checkbox .v-label{font-size:12px!important}.v-application .v-dialog.schedule-lesson-dialog .l-checkbox .v-input--selection-controls__input{margin-top:3px}.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-header{display:inline-block;padding-right:60px;font-size:20px;font-weight:700;line-height:1.1}@media only screen and (max-width:991px){.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-header{position:absolute;top:0;left:0;width:100%;height:50px;display:flex;align-items:center;padding-left:18px;font-size:18px}}.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-body .row .col:first-child{padding-right:20px}.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-body .row .col:last-child{padding-left:20px}@media only screen and (min-width:992px){.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-footer{margin-top:28px}}@media only screen and (max-width:991px){.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-footer{position:absolute;bottom:0;left:0;width:100%;height:74px;padding:0 18px}}.v-application .v-dialog.schedule-lesson-dialog .schedule-lesson-dialog-footer .prev-button{color:var(--v-orange-base);cursor:pointer}",""]),e.exports=n},1935:function(e,t,r){"use strict";r.r(t);r(7),r(8),r(9),r(14),r(6),r(15);var n=r(10),o=r(2),l=(r(62),r(39),r(174),r(40),r(96),r(23),r(149)),c=r(973);function d(object,e){var t=Object.keys(object);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(object);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(object,e).enumerable}))),t.push.apply(t,r)}return t}function h(e){for(var i=1;i<arguments.length;i++){var source=null!=arguments[i]?arguments[i]:{};i%2?d(Object(source),!0).forEach((function(t){Object(o.a)(e,t,source[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(source)):d(Object(source)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(source,t))}))}return e}var _={name:"ScheduleLessonDialog",components:{LDialog:l.default},props:{isShownSummaryLessonDialog:{type:Boolean,required:!0},username:{type:String,required:!0},query:{type:Object,required:!0}},data:function(){var e=this;return{valid:!0,isAgree:!1,messageCounter:100,messageRules:[function(t){return(null==t?void 0:t.length)>=100||e.messageHint}],agreeRules:[function(t){return!!t||e.$t("field_required")}]}},computed:{timezone:function(){return this.$store.getters["user/timeZone"]},isStudent:function(){return this.$store.getters["user/isStudent"]},teacher:function(){return this.$store.state.teacher_profile.item},paymentMethods:function(){return this.$store.state.purchase.paymentMethods},trialPackage:function(){return this.$store.getters["teacher_profile/trialPackage"]},isSelectedTrial:function(){return this.$store.getters["teacher_profile/isSelectedTrial"]},isFreeTrialPackage:function(){return this.isSelectedTrial&&this.trialPackage.isFreeTrialLesson},selectedCourse:function(){return this.$store.state.teacher_profile.selectedCourse},selectedLanguage:function(){return this.$store.state.teacher_profile.selectedLanguage},selectedSlots:function(){return this.$store.state.teacher_profile.selectedSlots.map((function(e){return e[0].date})).sort((function(a,b){return new Date(a)-new Date(b)}))},currentCurrencySymbol:function(){return this.$store.getters["currency/currentCurrencySymbol"]},totalPrice:function(){return this.$store.getters["teacher_profile/totalPrice"]},lessonsLeft:function(){return this.selectedCourse.lessons-this.selectedSlots.length},message:{get:function(){return this.$store.state.purchase.message},set:function(e){this.$store.commit("purchase/SET_MESSAGE",e)}},selectedPaymentMethod:{get:function(){return this.$store.state.purchase.selectedPaymentMethod},set:function(e){this.$store.commit("purchase/SET_SELECTED_PAYMENT_METHOD",e)}},additionalCredits:function(){return this.$store.getters["purchase/additionalCredits"]},isEnoughCredits:function(){return this.additionalCredits>=this.totalPrice},totalDuePrice:function(){return(this.isEnoughCredits?0:this.totalPrice-this.additionalCredits).toFixed(2)},additionalCreditsLeft:function(){return this.currentCurrencySymbol+(this.isEnoughCredits?this.additionalCredits-this.totalPrice:"0.00")},messageHint:function(){return this.$t("please_write_at_least_characters",{value:100})},userCurrency:function(){return this.$store.getters["user/currency"]},getFormattedDate:function(){return(new Date).toLocaleString("en-US",{year:"numeric",month:"short",day:"2-digit",hour:"numeric",minute:"numeric",hour12:!0})||"Jan 01, 2000, 12:00 AM"}},methods:{prevStep:function(){this.$router.push({path:this.$route.path,query:h(h({},this.query),{},{step:"schedule-lessons"})}),this.$emit("prev-step")},getLabelPayment:function(e){var t,label;switch(e.id){case 1:label=4!==(null===(t=this.userCurrency)||void 0===t?void 0:t.id)?this.$t("debit_or_credit_card"):this.$t("debit_or_credit_card_pl_version");break;case 2:label="Przelewy24/BLIK";break;default:label=e.name}return label},scheduleLessons:function(){var e=this;return Object(n.a)(regeneratorRuntime.mark((function t(){var r,n,o,l,d,h,_;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.$store.state.user.tidioData||{},n=null,t.prev=2,t.next=5,e.$store.dispatch("payments/fetchUserData");case 5:n=t.sent,t.next=11;break;case 8:t.prev=8,t.t0=t.catch(2),console.error("Error fetching user data from API:",t.t0);case 11:n&&n.email||(n=e.$store.state.user.item||{}),o=r.email||"",l="".concat(n.firstName||""," ").concat(n.lastName||"").trim(),d=Object(c.hashUserData)(o),h=Object(c.hashUserData)(l),_=null,_=e.isSelectedTrial&&e.trialPackage.isFreeTrialLesson?{event:"purchase_free_trial",ecommerce:{transaction_id_free_trial:"T_12345",items:[{item_id_free_trial:e.$store.state.teacher_profile.item.id||"1234",teacher_name_free_trial:"".concat(e.$store.state.teacher_profile.item.firstName.trim()," ").concat(e.$store.state.teacher_profile.item.lastName.trim()),language_free_trial:e.$store.state.teacher_profile.selectedLanguage.name||"English",lesson_length_free_trial:"".concat(e.selectedCourse.length," minutes")||!1,lesson_time_free_trial:e.getFormattedDate,package_type_free_trial:"free_trial",package_free_trial:"".concat(e.selectedCourse.lessons," Lesson")||!1,user_name:h,email_id:d}]}}:e.isSelectedTrial&&!e.trialPackage.isFreeTrialLesson?{event:"purchase_paid_trial",ecommerce:{transaction_id_paid_trial:"T_12345",value_paid_trial:e.$store.getters["teacher_profile/totalPrice"]||0,tax_paid_trial:null,currency_paid_trial:e.$store.getters["user/currency"].isoCode||"USD",items:[{item_id_paid_trial:e.$store.state.teacher_profile.item.id||"1234",teacher_name_paid_trial:"".concat(e.$store.state.teacher_profile.item.firstName.trim()," ").concat(e.$store.state.teacher_profile.item.lastName.trim()),total_price_paid_trial:e.$store.getters["teacher_profile/totalPrice"]||0,currency_paid_trial:e.$store.getters["user/currency"].isoCode||"USD",language_paid_trial:e.$store.state.teacher_profile.selectedLanguage.name||"English",lesson_length_paid_trial:"".concat(e.selectedCourse.length," minutes")||!1,lesson_time_paid_trial:e.getFormattedDate,package_type_paid_trial:"paid trial",package_paid_trial:"".concat(e.selectedCourse.lessons," Lesson")||!1,payment_type_paid_trial:"credit",user_name:h,email_id:d}]}}:{event:"purchase",ecommerce:{transaction_id:"T_12345",value:e.$store.getters["teacher_profile/totalPrice"]||0,tax:null,currency:e.$store.getters["user/currency"].isoCode||"USD",user_id:e.$store.getters["user/getUserId"]||"0",items:[{item_id:e.$store.state.teacher_profile.item.id||"1234",teacher_name:"".concat(e.$store.state.teacher_profile.item.firstName.trim()," ").concat(e.$store.state.teacher_profile.item.lastName.trim()),total_price:e.$store.getters["teacher_profile/totalPrice"]||120,currency:e.$store.getters["user/currency"].isoCode||"USD",language:e.$store.state.teacher_profile.selectedLanguage.name||"English",lesson_length:"".concat(e.selectedCourse.length," minutes")||!1,lesson_time:e.getFormattedDate,package_type:"Paid",package:"".concat(e.selectedCourse.lessons," Lesson")||!1,payment_type:"credit",user_name:h,email_id:d}]}},localStorage.setItem("event_data",JSON.stringify(_)),2===e.selectedPaymentMethod&&window.localStorage.setItem("teacher-username",e.username),e.$store.dispatch("loadingStart"),e.$store.dispatch("purchase/scheduleLessons",e.username);case 22:case"end":return t.stop()}}),t,null,[[2,8]])})))()}}},v=(r(1763),r(22)),m=r(42),f=r.n(m),y=r(1327),C=r(1611),$=r(1360),w=r(1363),x=r(261),k=r(2192),S=r(2193),P=r(1361),O=r(1366),component=Object(v.a)(_,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("l-dialog",e._g({attrs:{dialog:e.isShownSummaryLessonDialog,"max-width":"725","custom-class":"schedule-lesson-dialog",persistent:"",fullscreen:e.$vuetify.breakpoint.smAndDown}},e.$listeners),[n("v-form",{on:{submit:function(t){return t.preventDefault(),e.scheduleLessons.apply(null,arguments)}},model:{value:e.valid,callback:function(t){e.valid=t},expression:"valid"}},[n("div",{staticClass:"schedule-lesson-dialog-header text--gradient"},[e._v("\n      "+e._s(e.$t("lesson_summary"))+":\n    ")]),e._v(" "),n("div",{class:["schedule-lesson-dialog-body pt-2 pt-sm-4",{"l-scroll l-scroll--grey l-scroll--large":e.$vuetify.breakpoint.xsOnly}]},[n("v-row",{attrs:{"no-gutters":""}},[n("v-col",{staticClass:"col-12 col-sm-6"},[n("div",{staticClass:"details"},[n("div",{staticClass:"details-row"},[n("div",{staticClass:"subtitle-2 property font-weight-medium"},[e._v("\n                "+e._s(e.$t("language"))+":\n              ")]),e._v(" "),n("div",{staticClass:"body-1 value value--icon"},[e.selectedLanguage.isoCode?n("div",{staticClass:"icon mr-1"},[n("v-img",{attrs:{src:r(369)("./"+e.selectedLanguage.isoCode+".svg"),width:"18",height:"18"}})],1):e._e(),e._v("\n                "+e._s(e.selectedLanguage.name)+"\n              ")])]),e._v(" "),n("div",{staticClass:"details-row"},[n("div",{staticClass:"subtitle-2 property font-weight-medium"},[e._v("\n                "+e._s(e.$t("teacher_capitalize"))+":\n              ")]),e._v(" "),n("div",{staticClass:"body-1 value"},[e._v("\n                "+e._s(e.teacher.firstName)+" "+e._s(e.teacher.lastName)+"\n              ")])]),e._v(" "),e.selectedCourse.isCourse?n("div",{staticClass:"details-row"},[n("div",{staticClass:"subtitle-2 property font-weight-medium"},[e._v("\n                "+e._s(e.$t("course"))+":\n              ")]),e._v(" "),n("div",{staticClass:"body-1 value"},[e._v("\n                "+e._s(e.selectedCourse.name)+"\n                "),n("span",{staticClass:"body-2 greyDark--text"},[e._v("("+e._s(e.$tc("lessons_count",e.selectedCourse.lessons))+")")])])]):n("div",{staticClass:"details-row"},[n("div",{staticClass:"subtitle-2 property font-weight-medium"},[e._v("\n                "+e._s(e.$t("package"))+":\n              ")]),e._v(" "),n("div",{staticClass:"body-1 value"},[e.isSelectedTrial?[e._v("\n                  "+e._s(e.$t("trial"))+"\n                ")]:[e._v("\n                  "+e._s(e.$tc("lessons_count",e.selectedCourse.lessons))+"\n                ")]],2)]),e._v(" "),n("div",{staticClass:"details-row"},[n("div",{staticClass:"subtitle-2 property font-weight-medium"},[e._v("\n                "+e._s(e.$t("length"))+":\n              ")]),e._v(" "),n("div",{staticClass:"body-1 value"},[e._v("\n                "+e._s(e.$tc("minutes_count",e.selectedCourse.length))+"\n              ")])]),e._v(" "),e.selectedSlots.length?[n("div",{staticClass:"details-row"},[n("div",{staticClass:"subtitle-2 property font-weight-medium"},[e._v("\n                  "+e._s(e.$t("lesson_time"))+":\n                ")]),e._v(" "),n("div",{staticClass:"body-1 value"},e._l(e.selectedSlots,(function(t,r){return n("div",{key:r},[e._v("\n                    "+e._s(e.$dayjs(t).add(e.$dayjs(t).tz(e.timezone).utcOffset(),"minute").format("ll, LT"))+"\n                  ")])})),0)])]:e._e(),e._v(" "),n("div",{staticClass:"details-notice notice caption mt-2"},[e._v("\n              "+e._s(e.$t("time_listed_are_in_timezone",{timezone:e.timezone}))+"\n            ")]),e._v(" "),!e.isFreeTrialPackage&&e.lessonsLeft>0?n("div",{staticClass:"details-notice notice caption mt-2"},[e._v("\n              "+e._s(e.$t("you_can_schedule_your_remaining_lessons",{count:e.$tc("remaining_lessons_count",e.lessonsLeft)}))+"\n            ")]):e._e()],2)]),e._v(" "),n("v-col",{staticClass:"col-12 col-sm-6 mt-3 mt-sm-0"},[e.isSelectedTrial?n("div",{staticClass:"message mb-4"},[n("div",{staticClass:"subtitle-2 font-weight-medium"},[e._v("\n              "+e._s(e.$t("write_message_to_your_teacher"))+":\n            ")]),e._v(" "),n("div",{staticClass:"mt-1"},[n("v-textarea",{staticClass:"l-textarea",attrs:{"no-resize":"",height:"100",counter:e.messageCounter,solo:"",dense:"",rules:e.messageRules,hint:e.messageHint,"persistent-hint":"",placeholder:e.$t("briefly_introduce_yourself_write_your_teacher_few_words")},scopedSlots:e._u([{key:"counter",fn:function(t){var r=t.props;return[n("div",{directives:[{name:"show",rawName:"v-show",value:r.value>0,expression:"props.value > 0"}],staticClass:"v-counter theme--light"},[e._v("\n                    "+e._s(r.value)+"\n                  ")])]}}],null,!1,3865712920),model:{value:e.message,callback:function(t){e.message=t},expression:"message"}})],1),e._v(" "),e.isFreeTrialPackage?n("div",{staticClass:"mt-3"},[n("v-checkbox",{staticClass:"l-checkbox caption",attrs:{value:e.isAgree,label:e.$t("i_understand_that_my_teacher_is_making_time_for_this_trial"),ripple:!1,rules:e.agreeRules},on:{change:function(t){e.isAgree=!0}}})],1):e._e()]):e._e(),e._v(" "),e.isFreeTrialPackage||e.isEnoughCredits?e._e():n("div",{staticClass:"payment"},[n("div",{staticClass:"subtitle-2 font-weight-medium"},[e._v("\n              "+e._s(e.$t("choose_payment_method"))+":\n            ")]),e._v(" "),n("div",{staticClass:"mt-1 mt-sm-2"},[n("v-radio-group",{staticClass:"mt-0 pt-0",attrs:{"hide-details":""},model:{value:e.selectedPaymentMethod,callback:function(t){e.selectedPaymentMethod=t},expression:"selectedPaymentMethod"}},e._l(e.paymentMethods,(function(t){return n("v-radio",{key:t.id,staticClass:"l-radio-button",attrs:{label:e.getLabelPayment(t),ripple:!1,value:t.id}})})),1)],1)]),e._v(" "),n("div",{staticClass:"details-row mt-3"},[n("div",{staticClass:"subtitle-2 property font-weight-medium"},[e._v("\n              "+e._s(e.$t("total_price"))+":\n            ")]),e._v(" "),n("div",{staticClass:"body-1 value"},[e.isFreeTrialPackage?[e._v("\n                "+e._s(e.$t("free"))+"\n              ")]:[e._v("\n                "+e._s(e.currentCurrencySymbol)+e._s(e.totalPrice)+"\n              ")]],2)]),e._v(" "),!e.isFreeTrialPackage&&e.additionalCredits?[n("div",{staticClass:"details-row mt-1"},[n("div",{staticClass:"subtitle-2 property font-weight-medium"},[e._v("\n                "+e._s(e.$t("langu_credit"))+":\n              ")]),e._v(" "),n("div",{staticClass:"body-1 value"},[e._v("\n                -"+e._s(e.currentCurrencySymbol)+e._s(e.isEnoughCredits?e.totalPrice:e.additionalCredits)+"\n              ")])]),e._v(" "),n("div",{staticClass:"details-row mt-1"},[n("div",{staticClass:"subtitle-2 property font-weight-medium"},[e._v("\n                "+e._s(e.$t("total_due"))+":\n              ")]),e._v(" "),n("div",{staticClass:"body-1 value"},[e._v("\n                "+e._s(e.currentCurrencySymbol)+e._s(e.totalDuePrice)+"\n              ")])])]:e._e(),e._v(" "),e.isFreeTrialPackage?e._e():n("div",{staticClass:"details-notice notice caption mt-2"},[e._v("\n            "+e._s(e.$t("your_teacher_will_receive_your_payment_once_lesson_has_successfully_concluded"))+"\n          ")]),e._v(" "),!e.isFreeTrialPackage&&e.additionalCredits?n("div",{staticClass:"details-notice notice caption mt-1"},[e._v("\n            "+e._s(e.$t("after_this_purchase_you_will_have_credit_remaining",{value:e.additionalCreditsLeft}))+"\n          ")]):e._e()],2)],1)],1),e._v(" "),n("div",{staticClass:"schedule-lesson-dialog-footer d-flex justify-space-between align-center"},[n("div",{staticClass:"prev-button body-1",on:{click:e.prevStep}},[n("svg",{staticClass:"mr-1",attrs:{width:"17",height:"12",viewBox:"0 0 17 12"}},[n("use",{attrs:{"xlink:href":r(91)+"#arrow-prev"}})]),e._v("\n        "+e._s(e.$t("go_back"))+"\n      ")]),e._v(" "),n("div",[e.isStudent?n("v-btn",{attrs:{id:"continue_trialOrPurchase",small:"",color:"primary",type:"submit",disabled:!e.valid}},[e.isSelectedTrial&&e.isFreeTrialPackage?[e._v("\n            "+e._s(e.$t("book_trial"))+"!\n          ")]:e.isEnoughCredits?[e._v("\n            "+e._s(e.$t("complete_purchase"))+"\n          ")]:[e._v("\n            "+e._s(e.$t("continue_to_purchase"))+"\n          ")]],2):e._e()],1)])])],1)}),[],!1,null,null,null);t.default=component.exports;f()(component,{LDialog:r(149).default}),f()(component,{VBtn:y.a,VCheckbox:C.a,VCol:$.a,VForm:w.a,VImg:x.a,VRadio:k.a,VRadioGroup:S.a,VRow:P.a,VTextarea:O.a})}}]);