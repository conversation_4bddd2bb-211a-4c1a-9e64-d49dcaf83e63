exports.ids = [132,59,60,76];
exports.modules = {

/***/ 1028:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StarRating_vue_vue_type_style_index_0_id_1645fb89_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(968);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StarRating_vue_vue_type_style_index_0_id_1645fb89_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StarRating_vue_vue_type_style_index_0_id_1645fb89_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StarRating_vue_vue_type_style_index_0_id_1645fb89_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StarRating_vue_vue_type_style_index_0_id_1645fb89_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1029:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".score[data-v-1645fb89]{display:flex;align-items:center;height:18px;font-size:12px;line-height:.8;font-weight:700;letter-spacing:.1px;color:var(--v-orange-base)}@media only screen and (max-width:1215px){.score[data-v-1645fb89]{justify-content:flex-end}}.score>div[data-v-1645fb89]{width:65px;display:flex;margin-left:2px}@media only screen and (max-width:1215px){.score>div[data-v-1645fb89]{width:auto}}.score svg[data-v-1645fb89]:not(:first-child){margin-left:1px}.score--large[data-v-1645fb89]{font-size:18px}@media only screen and (max-width:1215px){.score--large[data-v-1645fb89]{font-size:16px}}.score--large>div[data-v-1645fb89]{width:112px;margin-left:8px}@media only screen and (max-width:1215px){.score--large>div[data-v-1645fb89]{width:84px}}.score--large svg[data-v-1645fb89]:not(:first-child){margin-left:3px}@media only screen and (max-width:1215px){.score--large svg[data-v-1645fb89]:not(:first-child){margin-left:1px}}@media only screen and (max-width:1215px){.score--large svg[data-v-1645fb89]{width:16px!important;height:16px!important}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1071:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1105);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("20c2c1c7", content, true)

/***/ }),

/***/ 1105:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".slick-track[data-v-e4caeaf8]{position:relative;top:0;left:0;display:block;transform:translateZ(0)}.slick-track.slick-center[data-v-e4caeaf8]{margin-left:auto;margin-right:auto}.slick-track[data-v-e4caeaf8]:after,.slick-track[data-v-e4caeaf8]:before{display:table;content:\"\"}.slick-track[data-v-e4caeaf8]:after{clear:both}.slick-loading .slick-track[data-v-e4caeaf8]{visibility:hidden}.slick-slide[data-v-e4caeaf8]{display:none;float:left;height:100%;min-height:1px}[dir=rtl] .slick-slide[data-v-e4caeaf8]{float:right}.slick-slide img[data-v-e4caeaf8]{display:block}.slick-slide.slick-loading img[data-v-e4caeaf8]{display:none}.slick-slide.dragging img[data-v-e4caeaf8]{pointer-events:none}.slick-initialized .slick-slide[data-v-e4caeaf8]{display:block}.slick-loading .slick-slide[data-v-e4caeaf8]{visibility:hidden}.slick-vertical .slick-slide[data-v-e4caeaf8]{display:block;height:auto;border:1px solid transparent}.slick-arrow.slick-hidden[data-v-21137603]{display:none}.slick-slider[data-v-3d1a4f76]{position:relative;display:block;box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;touch-action:pan-y;-webkit-tap-highlight-color:transparent}.slick-list[data-v-3d1a4f76]{position:relative;display:block;overflow:hidden;margin:0;padding:0;transform:translateZ(0)}.slick-list[data-v-3d1a4f76]:focus{outline:none}.slick-list.dragging[data-v-3d1a4f76]{cursor:pointer;cursor:hand}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1211:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1280);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("335dfb13", content, true, context)
};

/***/ }),

/***/ 1212:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1282);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("66d4ff73", content, true, context)
};

/***/ }),

/***/ 1279:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TestimonialsSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1211);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TestimonialsSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TestimonialsSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TestimonialsSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TestimonialsSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1280:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".trusted-slider{max-width:1640px;margin:50px auto;background:none!important;position:relative!important;height:-webkit-max-content;height:-moz-max-content;height:max-content}@media(max-width:1170px){.trusted-slider{margin:30px auto 50px}}@media only screen and (max-width:991px){.trusted-slider{margin:30px auto 0}}.trusted-slider:before{content:none!important}.trusted-slider .slick-track{display:flex!important;align-items:center}.trusted-slider .slick-slide{padding:0 15px;transition:all .5s ease-in}@media only screen and (max-width:639px){.trusted-slider .slick-slide{padding:0 10px}}.trusted-slider .slick-slide:not(.slick-center){transform:translateZ(0) scale(.8)!important;opacity:.7}@media only screen and (max-width:991px){.trusted-slider .slick-slide:not(.slick-center){transform:translateZ(0) scale(.9)!important;opacity:.6}}.trusted-slider .slick-slide .slider-elem{display:flex;width:100%;max-width:1000px;height:-webkit-max-content;height:-moz-max-content;height:max-content;margin:0 auto;padding:40px;background:#fcc062;border-radius:15px}@media only screen and (max-width:991px){.trusted-slider .slick-slide .slider-elem{padding:35px}}@media only screen and (max-width:767px){.trusted-slider .slick-slide .slider-elem{padding:25px}}@media only screen and (max-width:639px){.trusted-slider .slick-slide .slider-elem{flex-direction:column;align-items:center}}.trusted-slider .slick-slide .slider-elem__title{margin-bottom:20px;color:#fff;font-weight:700;font-size:25px}@media only screen and (max-width:767px){.trusted-slider .slick-slide .slider-elem__title{margin-bottom:12px;font-size:22px}}@media only screen and (max-width:639px){.trusted-slider .slick-slide .slider-elem__title{margin-bottom:8px;text-align:center;font-size:18px}}@media only screen and (max-width:479px){.trusted-slider .slick-slide .slider-elem__title{font-size:16px}}.trusted-slider .slick-slide .slider-elem__text{color:#fff;font-weight:500;font-size:20px;line-height:1.4}@media only screen and (max-width:767px){.trusted-slider .slick-slide .slider-elem__text{font-size:16px}}@media only screen and (max-width:639px){.trusted-slider .slick-slide .slider-elem__text{font-size:15px}}@media only screen and (max-width:479px){.trusted-slider .slick-slide .slider-elem__text{font-size:14px}}.trusted-slider .slick-slide .slider-elem__content{display:flex;flex-direction:column;justify-content:center}.trusted-slider .slick-slide .slider-elem__img{height:178px;width:178px;-o-object-fit:cover;object-fit:cover;border-radius:50%;margin-right:52px}@media only screen and (max-width:991px){.trusted-slider .slick-slide .slider-elem__img{margin-right:35px}}@media only screen and (max-width:767px){.trusted-slider .slick-slide .slider-elem__img{height:100px;width:100px;margin-right:20px}}@media only screen and (max-width:639px){.trusted-slider .slick-slide .slider-elem__img{height:76px;width:76px;margin-right:0;margin-bottom:14px}}.trusted-slider .slick-arrow{position:absolute;top:50%;background-color:rgba(252,192,98,.95);transform:translateY(-50%)}.trusted-slider .slick-arrow.slick-next{right:8%}.trusted-slider .slick-arrow.slick-prev{left:8%}.trusted-slider--dark .slick-slide .slider-elem{background:var(--v-darkLight-base)}.trusted-slider--dark .slick-slide .slider-elem__title{color:#f9af48}.trusted-slider--dark .slick-arrow{background-color:#000}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1281:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachersSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1212);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachersSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachersSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachersSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachersSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1282:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, "@media(max-width:850px){.lp-teachers-slider .slick-slide{padding:0 15px;transition:all .5s ease-in}}@media only screen and (max-width:850px)and (max-width:639px){.lp-teachers-slider .slick-slide{padding:0}}@media(max-width:850px){.lp-teachers-slider .slick-slide:not(.slick-center){transform:translateZ(0) scale(.8)!important;opacity:.7}}@media only screen and (max-width:850px)and (max-width:991px){.lp-teachers-slider .slick-slide:not(.slick-center){transform:translateZ(0) scale(.9)!important;opacity:.6}}.lp-teachers-slider .slider{padding:15px 30px;margin-top:50px;display:flex;align-items:center;justify-content:space-between}.lp-teachers-slider .slider-card{width:100%;max-width:450px;margin:0 auto;position:relative;box-shadow:0 0 20px rgba(0,0,0,.15);border-radius:20px;display:flex;flex-direction:column;overflow:hidden}.lp-teachers-slider .slider-card__image{width:100%;height:430px;max-height:330px}.lp-teachers-slider .slider-card__img-point{position:absolute;top:20px;right:20px}.lp-teachers-slider .slider-card__content{color:#fff;padding:20px;background:linear-gradient(180.39deg,rgba(171,19,92,.8) -80.41%,rgba(247,173,72,.8) 86.01%)}.lp-teachers-slider .slider-card__content-star{color:#fff}.lp-teachers-slider .slider-card__content-star p{font-size:14px}.lp-teachers-slider .slider-card__content-star #text{display:inline}.lp-teachers-slider .slider-card__content-name{font-size:20px;font-weight:700;line-height:1.3;color:#fff!important;text-decoration:none}.lp-teachers-slider .slider-card__content-text{font-size:18px;line-height:20px;font-weight:300;margin:15px 0}@media only screen and (max-width:639px){.lp-teachers-slider .slider-card__content-text{font-size:16px}}.lp-teachers-slider .slider-card__content-tlabel{color:#fff;font-size:18px}@media only screen and (max-width:639px){.lp-teachers-slider .slider-card__content-tlabel{font-size:16px}}@media only screen and (max-width:479px){.lp-teachers-slider .slider-card__content-tlabel{font-size:14px}}.lp-teachers-slider .slider-card__content-ttext{color:#fff;font-size:18px;font-weight:300;padding-left:10px}@media only screen and (max-width:639px){.lp-teachers-slider .slider-card__content-ttext{font-size:16px}}@media only screen and (max-width:479px){.lp-teachers-slider .slider-card__content-ttext{font-size:14px}}.lp-teachers-slider .slider-card td{padding-top:10px;vertical-align:baseline}.lp-teachers-slider .slider-card .flags-area{position:absolute;right:0;text-align:right;top:0;width:100%}.lp-teachers-slider .slider-card .flag-icon{display:inline-block;font-size:30px;margin:10px}@media(max-width:1439px){.lp-teachers-slider .slider{display:flex;justify-content:center;padding:15px}.lp-teachers-slider .slider-card{max-width:480px;margin:0 auto}.lp-teachers-slider .slider-card__content-text{line-height:20px}}@media(max-width:1170px){.lp-teachers-slider .slider-card{max-width:400px}.lp-teachers-slider .slider-card__image{width:inherit;background-position:0 15%}}@media(max-width:900px){.lp-teachers-slider .slider-card{max-width:480px}}@media only screen and (max-width:639px){.lp-teachers-slider .slider{flex-direction:column;padding:0}}@media(max-width:480px){.lp-teachers-slider .slider-card__image{background-position:50%}}.lp-teachers-slider .slick-arrow{position:absolute;top:50%;background-color:#000;transform:translateY(-50%)}.lp-teachers-slider .slick-arrow.slick-next{right:30px}.lp-teachers-slider .slick-arrow.slick-prev{left:30px}.lp-teachers-slider .slick-dots{margin-top:10px!important}.lp-teachers-slider--dark .slider-card__content{color:#fff;background:var(--v-darkLight-base)}.lp-teachers-slider--dark .slider-card__content-star{color:var(--v-orangeLight-base)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1365:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1430);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("f74b8a86", content, true, context)
};

/***/ }),

/***/ 1384:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/landing-page/TestimonialsSlider.vue?vue&type=template&id=0dd1e3c5&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:['trusted-slider', { 'trusted-slider--dark': _vm.dark }]},[_c('client-only',[_c('VueSlickCarousel',_vm._b({},'VueSlickCarousel',_vm.sliderSettings,false),_vm._l((_vm.data),function(item,index){return _c('div',{key:index},[_c('div',{staticClass:"slider-elem"},[_c('div',{staticClass:"slider-elem__img-wrap"},[_c('img',{staticClass:"slider-elem__img",attrs:{"src":_vm.getSrcAvatar(item.profileImage),"alt":""}})]),_vm._v(" "),_c('div',{staticClass:"slider-elem__content"},[_c('div',{staticClass:"slider-elem__title"},[_vm._v(_vm._s(item.information))]),_vm._v(" "),_c('div',{staticClass:"slider-elem__text"},[_vm._v(_vm._s(item.description))])])])])}),0)],1)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/landing-page/TestimonialsSlider.vue?vue&type=template&id=0dd1e3c5&

// EXTERNAL MODULE: external "vue-slick-carousel"
var external_vue_slick_carousel_ = __webpack_require__(859);
var external_vue_slick_carousel_default = /*#__PURE__*/__webpack_require__.n(external_vue_slick_carousel_);

// EXTERNAL MODULE: ./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css
var vue_slick_carousel = __webpack_require__(1071);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/landing-page/TestimonialsSlider.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var TestimonialsSlidervue_type_script_lang_js_ = ({
  name: 'TestimonialsSlider',
  components: {
    VueSlickCarousel: external_vue_slick_carousel_default.a
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    dark: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      slider: null,
      sliderSettings: {
        centerMode: true,
        centerPadding: '210px',
        dots: false,
        focusOnSelect: true,
        infinite: true,
        speed: 800,
        slidesToShow: 1,
        slidesToScroll: 1,
        responsive: [{
          breakpoint: 1099,
          settings: {
            centerPadding: '170px'
          }
        }, {
          breakpoint: 991,
          settings: {
            arrows: false,
            centerPadding: '80px'
          }
        }, {
          breakpoint: 639,
          settings: {
            arrows: false,
            centerPadding: '60px'
          }
        }, {
          breakpoint: 480,
          settings: {
            arrows: false,
            centerPadding: '45px'
          }
        }]
      }
    };
  },

  methods: {
    getSrcAvatar(image, defaultImage = 'avatar.png') {
      return image || __webpack_require__(511)(`./${defaultImage}`);
    }

  }
});
// CONCATENATED MODULE: ./components/landing-page/TestimonialsSlider.vue?vue&type=script&lang=js&
 /* harmony default export */ var landing_page_TestimonialsSlidervue_type_script_lang_js_ = (TestimonialsSlidervue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/landing-page/TestimonialsSlider.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1279)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  landing_page_TestimonialsSlidervue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "014cf678"
  
)

/* harmony default export */ var TestimonialsSlider = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1385:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/landing-page/TeachersSlider.vue?vue&type=template&id=29ad2fba&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.data.length)?_c('div',{class:['lp-teachers-slider', { 'lp-teachers-slider--dark': _vm.dark }]},[_c('client-only',[_c('VueSlickCarousel',_vm._b({},'VueSlickCarousel',_vm.sliderSettings,false),_vm._l((_vm.data),function(item,index){return _c('div',{key:index},[_c('div',{staticClass:"slider"},[_c('div',{staticClass:"slider-card"},[_c('nuxt-link',{attrs:{"to":{ path: ("/teacher/" + (item.username)) },"target":"_blank"}},[_c('v-img',{staticClass:"slider-card__image",attrs:{"src":_vm.getSrcAvatar(item.picture),"position":"50% 30%"}})],1),_vm._v(" "),(item.languagesTaught.length)?_c('div',{staticClass:"flags-area d-flex justify-end"},_vm._l((item.languagesTaught),function(languageTaught){return _c('div',{key:languageTaught.isoCode,staticClass:"flags-item ma-1 elevation-2 rounded overflow-hidden"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (languageTaught.isoCode) + ".svg"),"width":"40","height":"30","contain":"","options":{ rootMargin: '50%' }}})],1)}),0):_vm._e(),_vm._v(" "),_c('div',{staticClass:"slider-card__content"},[_c('div',{staticClass:"d-flex justify-space-between align-center"},[_c('nuxt-link',{staticClass:"slider-card__content-name text-uppercase",attrs:{"to":{ path: ("/teacher/" + (item.username)) },"target":"_blank"}},[_vm._v("\n                  "+_vm._s(item.firstName)+" "+_vm._s(item.lastName)+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"slider-card__content-star"},[_c('star-rating',{staticClass:"mr-1 mr-sm-2 mr-md-0",attrs:{"value":item.averageRatings}}),_vm._v(" "),_c('p',{staticClass:"mb-0"},[_vm._v("\n                    ("+_vm._s(_vm.$tc('review', item.countFeedbacks))+")\n                  ")])],1)],1),_vm._v(" "),_c('div',{staticClass:"slider-card__content-text"},[_vm._v("\n                "+_vm._s(item.shortSummary)+"\n              ")]),_vm._v(" "),_c('table',[_c('tr',[_c('td',{class:[
                      'slider-card__content-tlabel',
                      { 'dark--text': !_vm.dark } ]},[_vm._v("\n                    "+_vm._s(_vm.$t('teaches'))+":\n                  ")]),_vm._v(" "),_c('td',{class:[
                      'slider-card__content-ttext',
                      { 'dark--text': !_vm.dark } ]},[_vm._v("\n                    "+_vm._s(item.languagesTaught.map(function (i) { return i.name; }).join(', '))+"\n                  ")])]),_vm._v(" "),_c('tr',[_c('td',{class:[
                      'slider-card__content-tlabel',
                      { 'dark--text': !_vm.dark } ]},[_vm._v("\n                    "+_vm._s(_vm.$t('specialities'))+":\n                  ")]),_vm._v(" "),_c('td',{class:[
                      'slider-card__content-ttext',
                      { 'dark--text': !_vm.dark } ]},[_vm._v("\n                    "+_vm._s(item.specialities)+"\n                  ")])])])])],1)])])}),0)],1)],1):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/landing-page/TeachersSlider.vue?vue&type=template&id=29ad2fba&

// EXTERNAL MODULE: external "vue-slick-carousel"
var external_vue_slick_carousel_ = __webpack_require__(859);
var external_vue_slick_carousel_default = /*#__PURE__*/__webpack_require__.n(external_vue_slick_carousel_);

// EXTERNAL MODULE: ./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css
var vue_slick_carousel = __webpack_require__(1071);

// EXTERNAL MODULE: ./components/StarRating.vue + 4 modules
var StarRating = __webpack_require__(996);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/landing-page/TeachersSlider.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



/* harmony default export */ var TeachersSlidervue_type_script_lang_js_ = ({
  name: 'TeachersSlider',
  components: {
    VueSlickCarousel: external_vue_slick_carousel_default.a,
    StarRating: StarRating["default"]
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    dark: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      slider: null,
      sliderSettings: {
        dots: false,
        focusOnSelect: true,
        infinite: true,
        speed: 800,
        slidesToShow: 1,
        slidesToScroll: 1,
        responsive: [{
          breakpoint: 900,
          settings: {
            arrows: false,
            dots: true
          }
        }, {
          breakpoint: 850,
          settings: {
            arrows: false,
            dots: true,
            centerMode: true,
            centerPadding: '100px'
          }
        }, {
          breakpoint: 639,
          settings: {
            arrows: false,
            dots: true,
            centerMode: true,
            centerPadding: '45px'
          }
        }, {
          breakpoint: 479,
          settings: {
            arrows: false,
            dots: true,
            centerMode: true,
            centerPadding: '30px'
          }
        }]
      }
    };
  },

  methods: {
    getSrcAvatar(image, defaultImage = 'avatar.png') {
      return image || __webpack_require__(511)(`./${defaultImage}`);
    }

  }
});
// CONCATENATED MODULE: ./components/landing-page/TeachersSlider.vue?vue&type=script&lang=js&
 /* harmony default export */ var landing_page_TeachersSlidervue_type_script_lang_js_ = (TeachersSlidervue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/landing-page/TeachersSlider.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1281)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  landing_page_TeachersSlidervue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "4baed921"
  
)

/* harmony default export */ var TeachersSlider = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {StarRating: __webpack_require__(996).default})


/* vuetify-loader */


installComponents_default()(component, {VImg: VImg["a" /* default */]})


/***/ }),

/***/ 1427:
/***/ (function(module, exports, __webpack_require__) {

var map = {
	"./img1.svg": 631,
	"./img2.svg": 632,
	"./img3.svg": 633,
	"./img4.svg": 634,
	"./img5.svg": 635,
	"./img6.svg": 636
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = 1427;

/***/ }),

/***/ 1428:
/***/ (function(module, exports, __webpack_require__) {

var map = {
	"./img1.svg": 637,
	"./img2.svg": 638,
	"./img3.svg": 639
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = 1428;

/***/ }),

/***/ 1429:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_64f3439c_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1365);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_64f3439c_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_64f3439c_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_64f3439c_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_64f3439c_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1430:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".row-education[data-v-64f3439c]{max-width:1560px;margin:0 auto;padding:0 15px}.flex-lg-row-reverse[data-v-64f3439c]{display:flex;flex-direction:row-reverse}@media(max-width:1170px){.flex-lg-row-reverse[data-v-64f3439c]{flex-wrap:wrap;justify-content:center}}.section-1[data-v-64f3439c]{padding:220px 10% 250px;position:relative;z-index:2!important}@media(max-width:1750px){.section-1[data-v-64f3439c]{padding:190px 6% 200px}}@media(max-width:1440px){.section-1__img[data-v-64f3439c]{width:65%;-o-object-fit:contain;object-fit:contain;position:absolute;top:0;right:2px;z-index:-1}.section-1__label[data-v-64f3439c]{font-size:37px;margin-bottom:20px}.section-1[data-v-64f3439c]{padding:224px 6% 160px}.section-1__sublabel[data-v-64f3439c]{margin:0 0 25px;font-size:20px;line-height:28px}}@media(max-width:1250px){.section-1[data-v-64f3439c]{padding:120px 4% 160px}}@media(max-width:991px){.section-1[data-v-64f3439c]{padding:120px 15px 160px}}@media(max-width:650px){.section-1[data-v-64f3439c]{padding:120px 15px 100px}}@media(max-width:440px){.section-1[data-v-64f3439c]{padding:224px 85px 259px}}.section-1__content[data-v-64f3439c]{width:660px;display:flex;flex-direction:column;align-items:flex-start}@media(max-width:1250px){.section-1__content[data-v-64f3439c]{width:40%}}@media(max-width:1100px){.section-1__content[data-v-64f3439c]{width:55%}.section-1 .section-1__sublabel[data-v-64f3439c]{max-width:450px!important}}@media(max-width:1270px){.type-content__list[data-v-64f3439c]{transform:scale(.9)}}@media(max-width:600px){.section-1__content[data-v-64f3439c]{width:75%}}.section1__img[data-v-64f3439c]{width:58%;-o-object-fit:contain;object-fit:contain;position:absolute;top:-21px;right:-10px;z-index:-1}@media(max-width:800px){.section1__img[data-v-64f3439c]{width:100%;right:-30%}}.section-1__label[data-v-64f3439c]{max-width:673px;font-size:48px;font-weight:700;margin-top:0;margin-bottom:0;line-height:1.125;background:#ab135c;background:linear-gradient(to bottom right,#ab135c 0,#fbb03b);-webkit-background-clip:text;-webkit-text-fill-color:transparent}@media(max-width:1100px){.section-1__label[data-v-64f3439c]{width:68%;font-size:23px;line-height:1.5}}.section-1__sublabel[data-v-64f3439c]{color:#2d2d2d;font-size:20px;margin:24px 0 36px;font-weight:300;line-height:1.4}@media(max-width:1250px){.section-1__label[data-v-64f3439c]{font-size:33px}.section-1__sublabel[data-v-64f3439c]{font-size:20px;line-height:28px}}@media(max-width:1100px){.section-1__sublabel[data-v-64f3439c]{font-size:20px}}@media(max-width:900px){.section-1__content[data-v-64f3439c]{width:80%}}@media(max-width:679px){.section-1[data-v-64f3439c]{padding:113px 15px 77px}.section-1__content[data-v-64f3439c]{width:100%}.section-1__label[data-v-64f3439c]{font-size:23px;line-height:30px;width:70%}.section-1__sublabel[data-v-64f3439c]{font-size:15px;line-height:23px;color:#2d2d2d;opacity:.75;width:70%}}.business-label[data-v-64f3439c]{width:100%;display:flex;align-items:center;margin-left:20px;font-weight:500;font-size:32px}.business-label__point[data-v-64f3439c]{width:10px;height:55px;background:#ffb202;border-radius:10px;margin-right:25px}.number[data-v-64f3439c]{width:33.3333%;position:relative;min-height:144px;margin-bottom:87px}.number-helper[data-v-64f3439c]{position:relative;padding:0 30px 0 0}.number-helper[data-v-64f3439c]:before{content:\"\";position:absolute;top:0;left:0;width:82px;height:144px;background:linear-gradient(179.61deg,rgba(171,19,92,.1) -22.73%,rgba(247,173,72,.1) 72.1%);border-radius:10px}.number-label[data-v-64f3439c]{font-size:62px;line-height:1.1;padding:16px 0 0 16px}.number-text[data-v-64f3439c]{max-width:230px;margin-top:15px;padding-left:16px;font-size:18px;letter-spacing:.1px}@media(max-width:850px){.number-label[data-v-64f3439c]{font-size:38px}.number-wrap[data-v-64f3439c]{margin:auto!important}.number-text[data-v-64f3439c]{font-size:14px;opacity:.6;line-height:30px}}@media(max-width:479px){.number-helper[data-v-64f3439c]{padding:0 15px 0 0}.number-label[data-v-64f3439c]{top:-8px!important;padding-left:10px;font-size:36px}.number-text[data-v-64f3439c]{max-width:100%;padding-left:10px}}.card[data-v-64f3439c]{background:#fff;box-shadow:0 2px 10px rgba(0,0,0,.1);border-radius:15px;display:flex;padding:50px;flex-direction:column;align-items:center;margin-bottom:30px;padding:82px 65px}.card-label[data-v-64f3439c]{text-align:center;font-size:20px;font-weight:700;line-height:28px;margin:27px 0 15px}.card-text[data-v-64f3439c]{font-size:17px;line-height:25px}.section-2[data-v-64f3439c]{position:relative;margin-bottom:102px;padding-top:45px}@media(max-width:1170px){.section-2[data-v-64f3439c]{margin-bottom:60px}}@media only screen and (max-width:991px){.section-2 .business-label[data-v-64f3439c]{margin-bottom:45px}}@media only screen and (max-width:767px){.section-2 .business-label[data-v-64f3439c]{margin-bottom:30px}}.cards-cont[data-v-64f3439c]{padding:15px!important}@media(max-width:750px){.section-1__label[data-v-64f3439c]{margin-top:50px}}.section-2__bottom[data-v-64f3439c]{display:flex;justify-content:flex-end}@media only screen and (max-width:991px){.section-2__bottom[data-v-64f3439c]{padding-left:15px;padding-right:15px}}.section-2__more[data-v-64f3439c]{width:100%;right:100px;min-height:175px;max-width:810px;padding:44px 43px;display:flex;align-items:center;background:linear-gradient(179.59deg,rgba(171,19,92,.24) -27.72%,rgba(247,173,72,.36) 68.88%);border-radius:20px}.section-2__more-img[data-v-64f3439c]{margin-right:20px;width:73px}.section-2__more-label[data-v-64f3439c]{font-weight:700;font-size:22px}.section-2__more-text[data-v-64f3439c]{font-size:17px;line-height:25px}.section-2__img[data-v-64f3439c]{width:76%;-o-object-fit:contain;object-fit:contain;position:absolute;top:-380px;left:-10px;z-index:-1}@media(max-width:950px){.section-2__img[data-v-64f3439c]{width:80%}}.cards-cont .card[data-v-64f3439c]{height:100%}@media only screen and (min-width:992px){.cards-cont[data-v-64f3439c]:first-child{margin-top:130px}.cards-cont[data-v-64f3439c]:nth-child(2){margin-top:55px}.cards-cont:nth-child(2) .card[data-v-64f3439c]{height:calc(100% - 75px)}.cards-cont[data-v-64f3439c]:nth-child(3){margin-top:-20px}.cards-cont:nth-child(3) .card[data-v-64f3439c]{height:calc(100% - 150px)}.cards-cont[data-v-64f3439c]:nth-child(5){margin-top:-75px}.cards-cont:nth-child(5) .card[data-v-64f3439c]{height:calc(100% - 75px)}.cards-cont[data-v-64f3439c]:nth-child(6){margin-top:-150px}.cards-cont:nth-child(6) .card[data-v-64f3439c]{height:calc(100% - 150px)}}.number-li[data-v-64f3439c]{width:78px;height:100%;background:linear-gradient(179.61deg,rgba(171,19,92,.1) -22.73%,rgba(247,173,72,.1) 72.1%);border-radius:10px;position:absolute;top:0;left:13px}.number-label[data-v-64f3439c]{color:#c91460;font-size:60px;font-weight:800}.number-text[data-v-64f3439c]{color:#262626;font-size:17px;line-height:25.4px;opacity:.6}@media(max-width:1440px){.business-label[data-v-64f3439c]{font-size:30px}.card[data-v-64f3439c]{padding:65px 53px}.card-label[data-v-64f3439c]{font-size:20px;line-height:25px}.card-text[data-v-64f3439c]{font-size:17px}.section-2__more[data-v-64f3439c]{padding:35px}.section-2__more-img[data-v-64f3439c]{width:60px}}@media(max-width:679px){.business-label[data-v-64f3439c]{font-size:26px;margin-bottom:40px}.business-label__point[data-v-64f3439c]{margin-right:10px}.card[data-v-64f3439c]{padding:45px 35px 41px!important;margin-bottom:30px!important}.card-label[data-v-64f3439c]{margin:19px 0 10px!important}.section-2__more[data-v-64f3439c]{position:relative;max-width:414px;min-height:180px;right:-20px;padding:32px 15px 32px 35px;border-radius:20px 0 0 20px}.section-2__more-label[data-v-64f3439c]{font-size:20px;line-height:21px;margin-bottom:10px}}@media(max-width:479px){.section-2[data-v-64f3439c]{margin-bottom:75px}.section-2__more[data-v-64f3439c]{min-height:150px;padding:10px 15px}.section-2__more-label[data-v-64f3439c]{font-size:18px}.section-2__more-text[data-v-64f3439c]{font-size:15px}}@media(max-width:340px){.mobile-padding-right[data-v-64f3439c]{padding-right:100px}}.section-3[data-v-64f3439c]{position:relative}@media(max-width:1250px){.section-3[data-v-64f3439c]{padding:40px 0 30px}}@media(max-width:650px){.section-3[data-v-64f3439c]{padding:20px 0}}@media(max-width:440px){.section-3[data-v-64f3439c]{padding:20px 0}}@media(max-width:750px){.section-3[data-v-64f3439c]{padding:0}}.section-3__img[data-v-64f3439c]{position:absolute;bottom:-480px;right:0}@media(min-width:849px){.section-3__img[data-v-64f3439c]{width:70%}}.number-wrap[data-v-64f3439c]{position:relative;width:100%;max-width:1000px;display:flex;flex-wrap:wrap;justify-content:center;margin-left:120px;padding-top:80px;z-index:2}@media(max-width:1440px){.number-wrap[data-v-64f3439c]{margin-top:4px;margin-left:15px;max-width:1064px}}@media(max-width:650px){.number[data-v-64f3439c]{width:50%;margin-bottom:30px}.number[data-v-64f3439c]:nth-child(3){width:100%;padding-left:22%}.number-wrap[data-v-64f3439c]{max-width:435px;padding-top:60px}}.section-4[data-v-64f3439c]{position:relative;padding-top:45px}.section-4 .business-label[data-v-64f3439c]{margin-bottom:60px}.cards-cont-4[data-v-64f3439c]{display:flex;justify-content:space-between}.section-4 .card[data-v-64f3439c]{padding:100px 38px}@media(min-width:968px){.col-card[data-v-64f3439c]{width:32%!important}}.col-card .card[data-v-64f3439c]{height:100%}.col-card .card-text[data-v-64f3439c]{font-size:15px}.col-btn[data-v-64f3439c]{display:flex;align-items:center;justify-content:center;margin:30px 0}@media(max-width:1440px){.section-4[data-v-64f3439c]{padding-top:56px}.section-4 .business-label[data-v-64f3439c]{margin-bottom:55px}.section-5 .business-label[data-v-64f3439c]{margin-bottom:30px}.section-4 .card[data-v-64f3439c]{padding:50px 30px}.section-4 .card-label[data-v-64f3439c]{font-size:18px}.section-4 .card-text[data-v-64f3439c]{font-size:15px}}@media(max-width:967px){.section-4[data-v-64f3439c]{padding-top:15px}.section-4 .business-label[data-v-64f3439c]{margin-bottom:40px}.cards-cont-4[data-v-64f3439c]{flex-wrap:wrap;justify-content:center}.col-card[data-v-64f3439c]{margin-bottom:10px}}.section-5[data-v-64f3439c]{padding-top:100px;position:relative;margin-bottom:100px}.section-5 .business-label[data-v-64f3439c]{margin-bottom:30px}.section-5-row[data-v-64f3439c]{display:flex;flex-direction:row-reverse;align-items:center}.section-5-row table[data-v-64f3439c]{color:#fff;margin-bottom:20px}@media(max-width:750px){.section-5[data-v-64f3439c]{padding-top:0}}.teacher[data-v-64f3439c]{display:flex;align-items:center;margin:30px 0}.teacher-img[data-v-64f3439c]{margin-right:25px}.teacher-content__label[data-v-64f3439c]{color:#262626;font-size:30px;font-weight:700}.teacher-content__text[data-v-64f3439c]{color:#262626;font-size:20px;width:350px;line-height:26.1px}.skills-cont[data-v-64f3439c]{display:flex;flex-direction:column;justify-content:center}@media(max-width:1440px){.section-5[data-v-64f3439c]{padding-top:95px}}@media(max-width:1080px){.section-5[data-v-64f3439c]{padding-top:60px}.section-3__img[data-v-64f3439c]{bottom:-400px}.section-5-row[data-v-64f3439c]{flex-direction:column}}@media(max-width:1200px){.teacher-content__text[data-v-64f3439c]{width:100%}}@media(max-width:850px){.number[data-v-64f3439c]{min-height:90px;margin-bottom:60px}.number-label[data-v-64f3439c]{padding:10px 0 0 15px;font-size:38px;line-height:36px}.number-text[data-v-64f3439c]{margin-top:0;padding-left:15px;line-height:15px;font-size:14px}.section-3__img[data-v-64f3439c]{bottom:-200%}.number-helper[data-v-64f3439c]:before{width:55px;height:90px}}@media(max-width:676px){.section-5[data-v-64f3439c]{padding-top:120px}.teacher-img[data-v-64f3439c]{width:72px;height:72px}.teacher-content__label[data-v-64f3439c]{font-size:20px}.teacher-content__text[data-v-64f3439c]{font-size:17px}.swiper-n2 .swiper-slide[data-v-64f3439c]{width:80%!important;margin:25px 10%}}.section-6[data-v-64f3439c]{position:relative;margin-top:60px}.section-6__imgl[data-v-64f3439c]{position:absolute;left:-167px;bottom:-400px}.section-6__imgr[data-v-64f3439c]{position:absolute;right:-167px;top:-500px}@media(max-width:1440px){.section-6__imgl[data-v-64f3439c]{left:-75px}.section-6__imgr[data-v-64f3439c]{right:-75px}}.type[data-v-64f3439c]{width:100%;display:flex;align-items:center;justify-content:space-around;margin:30px 0}@media(max-width:850px){.type[data-v-64f3439c]{flex-direction:column}}@media(max-width:850px){.type.reverse[data-v-64f3439c]{flex-direction:column-reverse}}.type img[data-v-64f3439c]{margin:30px;width:100%;max-width:410px}.type-content[data-v-64f3439c]{width:100%;max-width:555px;margin-left:40px}.type-content__label[data-v-64f3439c]{font-size:25px;font-weight:700}.type-content__list[data-v-64f3439c]{font-size:20px;margin-left:-2em}.type-content__list li[data-v-64f3439c]{margin:19px 0;line-height:25px}.type-content__list li[data-v-64f3439c]::marker{content:\"•\";color:#fbb03b;font-size:28px}.type-content__list li p[data-v-64f3439c]{margin-left:15px}.section-7[data-v-64f3439c]{display:flex;align-items:center;justify-content:center;flex-direction:column;z-index:2}.section-7[data-v-64f3439c],.section-7 .row-education[data-v-64f3439c]{position:relative}.section-7__label[data-v-64f3439c]{position:relative;margin-top:40px;font-size:32px;font-weight:500}@media(max-width:1440px){.section-7__label[data-v-64f3439c]{font-size:30px}}@media(max-width:679px){.section-7__label[data-v-64f3439c]{font-size:26px}}.section-7__label-point[data-v-64f3439c]{position:absolute;right:-10px;top:0;background:linear-gradient(179.61deg,rgba(171,19,92,.1) -22.73%,rgba(247,173,72,.1) 72.1%);border-radius:10px;height:100%;width:50%}@media(max-width:1080px){.type img[data-v-64f3439c]{margin:0;width:50%}}@media(max-width:850px){.type img[data-v-64f3439c]{margin:30px;width:100%}}.section-7__image[data-v-64f3439c]{display:block;width:100%;margin:75px 0}@media(max-width:950px){.section-7__image[data-v-64f3439c]{display:none}}.section-7__list[data-v-64f3439c]{margin-top:40px;margin-bottom:40px}@media(max-width:450px){.section-7__list[data-v-64f3439c]{display:flex}}.section-7__item[data-v-64f3439c]{position:absolute;max-width:284px;width:25%;font-size:17px;line-height:1.2;font-weight:400}@media(max-width:1170px){.section-7__item[data-v-64f3439c]{max-width:195px}}.section-7__item img[data-v-64f3439c]{margin-right:20px}.section-7__item-1[data-v-64f3439c]{top:23%;left:12%}.section-7__item-2[data-v-64f3439c]{top:61%;left:28%}.section-7__item-3[data-v-64f3439c]{top:21%;left:60%}.section-7__item-4[data-v-64f3439c]{top:56%;left:79%}@media(max-width:1440px){.section-7__item[data-v-64f3439c]{font-size:15px}}@media(max-width:950px){.section-7__item[data-v-64f3439c]{display:none}}@media(max-width:679px){.col-card .card[data-v-64f3439c]{height:95%}}.form-image[data-v-64f3439c]{min-width:350px;max-width:700px;margin:30px 0;position:relative;display:none}.form-image img[data-v-64f3439c]{width:100%;display:block}.form-image__text-wrap[data-v-64f3439c]{position:absolute;display:flex;flex-direction:column;justify-content:space-between;top:0;bottom:0}.form-image__text[data-v-64f3439c]{font-size:15px;padding:20px 5px 20px 30%;line-height:1.3}.form-image__text.left[data-v-64f3439c]{padding-left:5px;padding-right:30%;text-align:right}@media(max-width:950px){.form-image[data-v-64f3439c]{display:block}}@media(max-width:1100px){.section-1 .section-1__label[data-v-64f3439c]{max-width:364px;font-size:25px}.section-1 .section-1__sublabel[data-v-64f3439c]{margin:0 0 50px;font-size:18px}.cards-cont-4[data-v-64f3439c]{margin-bottom:25px}}@media(max-width:679px){.section-5[data-v-64f3439c]{padding-top:50px;margin-bottom:50px}.section-6[data-v-64f3439c]{margin-top:0}.col-12.col-btn[data-v-64f3439c]{transform:scale(1.1)!important}}@media(max-width:425px){.section-1[data-v-64f3439c]{padding-top:80px}.section1__img[data-v-64f3439c]{top:-5px}}.type.reverse .type-content[data-v-64f3439c]{max-width:416px}@media(max-width:600px){.section-1__label[data-v-64f3439c]{margin-bottom:35px}.section-1 .section-1__sublabel[data-v-64f3439c]{margin:0 0 60px}}@media(max-width:480px){.section1__img[data-v-64f3439c]{width:95%}.section-1__label[data-v-64f3439c]{margin-top:-10px;width:75%}.section-1 .section-1__label[data-v-64f3439c]{font-size:23px}.section-1 .section-1__sublabel[data-v-64f3439c]{font-size:16px;margin:0 0 50px}}@media(max-width:425px){.section1__img[data-v-64f3439c]{width:90%}.section-1__label[data-v-64f3439c]{margin-top:5px;width:88%}.section-1 .section-1__sublabel[data-v-64f3439c]{width:88%}}@media(max-width:360px){.section-1 .section-1__label[data-v-64f3439c]{font-size:21px}.section-1__label[data-v-64f3439c]{width:97%;margin-top:-15px}.section1__img[data-v-64f3439c]{width:85%}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1471:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/education/index.vue?vue&type=template&id=64f3439c&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-row',[_c('v-col',{staticClass:"col-12 px-0"},[_c('section',{staticClass:"section section-1"},[_c('div',{staticClass:"section-1__content"},[_c('h3',{staticClass:"section-1__label"},[_vm._v(_vm._s(_vm.pageData.title))]),_vm._v(" "),_c('h4',{staticClass:"section-1__sublabel"},[_vm._v(_vm._s(_vm.pageData.intro))]),_vm._v(" "),_c('div',[_c('v-btn',{attrs:{"large":"","color":"primary","to":"/teacher-listing/1/motivation,3;sortOption,9"}},[_vm._v("\n            "+_vm._s(_vm.$t('lets_get_started'))+"\n          ")])],1)]),_vm._v(" "),_c('img',{staticClass:"section1__img",attrs:{"src":__webpack_require__(630),"alt":""}})]),_vm._v(" "),_c('section',{staticClass:"section-2"},[_c('div',{staticClass:"row-education"},[_c('div',{staticClass:"business-label"},[_c('div',{staticClass:"business-label__point"}),_vm._v("\n          "+_vm._s(_vm.$t('education_page.selection_title'))+"\n        ")]),_vm._v(" "),_c('div',{staticClass:"cards-cont-list"},[_c('v-container',{attrs:{"fluid":""}},[_c('v-row',_vm._l((6),function(i){return _c('v-col',{key:i,staticClass:"col-12 col-sm-6 col-md-4 cards-cont"},[_c('div',{staticClass:"card mb-0"},[_c('img',{staticClass:"card-img",attrs:{"src":__webpack_require__(1427)("./img" + i + ".svg"),"alt":""}}),_vm._v(" "),_c('div',{staticClass:"card-label text-left"},[_vm._v("\n                    "+_vm._s(_vm.$t(("education_page.col_card_label_" + i)))+"\n                  ")]),_vm._v(" "),_c('div',{staticClass:"card-text"},[_vm._v("\n                    "+_vm._s(_vm.$t(("education_page.col_card_text_" + i)))+"\n                  ")])])])}),1)],1)],1),_vm._v(" "),_c('div',{staticClass:"section-2__bottom mt-1 mt-sm-3"},[_c('div',{staticClass:"section-2__more"},[_c('img',{staticClass:"section-2__more-img",attrs:{"src":__webpack_require__(629),"alt":""}}),_vm._v(" "),_c('div',{staticClass:"mobile-padding-right"},[_c('div',{staticClass:"section-2__more-label"},[_vm._v("\n                "+_vm._s(_vm.$t('education_page.col_card_label_7'))+"\n              ")]),_vm._v(" "),_c('div',{staticClass:"section-2__more-text"},[_vm._v("\n                "+_vm._s(_vm.$t('education_page.col_card_text_7'))+"\n              ")])])])])]),_vm._v(" "),_c('img',{staticClass:"section-2__img",attrs:{"src":__webpack_require__(666),"alt":""}})]),_vm._v(" "),_c('section',{staticClass:"section-3"},[_c('div',{staticClass:"row-education"},[_c('div',{staticClass:"business-label"},[_c('div',{staticClass:"business-label__point"}),_vm._v("\n          "+_vm._s(_vm.$t('education_page.selection_title_3'))+"\n        ")])]),_vm._v(" "),(_vm.ratings.length > 0)?_c('testimonials-slider',{attrs:{"data":_vm.ratings,"dark":false}}):_vm._e(),_vm._v(" "),_c('div',{staticClass:"row-education"},[_c('div',{staticClass:"number-wrap"},_vm._l((5),function(i){return _c('div',{key:i,class:['number', ("number-" + i)]},[_c('div',{staticClass:"number-helper"},[_c('div',{staticClass:"number-label"},[_vm._v("\n                "+_vm._s(_vm.$t(("education_page.gold_text_" + i)))+"\n              ")]),_vm._v(" "),_c('div',{staticClass:"number-text"},[_vm._v("\n                "+_vm._s(_vm.$t(("education_page.number_text_" + i)))+"\n              ")])])])}),0)]),_vm._v(" "),_c('img',{staticClass:"section-3__img",attrs:{"src":__webpack_require__(667),"alt":""}})],1),_vm._v(" "),_c('section',{staticClass:"section-4 justify-center"},[_c('div',{staticClass:"row-education"},[_c('div',{staticClass:"business-label"},[_c('div',{staticClass:"business-label__point"}),_vm._v("\n          "+_vm._s(_vm.$t('education_page.selection_title_4'))+"\n        ")])]),_vm._v(" "),_c('div',{staticClass:"row-education"},[_c('div',{staticClass:"cards-cont-4"},[_c('v-container',{staticClass:"py-0",attrs:{"fluid":""}},[_c('v-row',{attrs:{"justify":"center"}},[_vm._l((3),function(i){return _c('v-col',{key:i,staticClass:"col-12 col-sm-6 col-md-4 col-card"},[_c('div',{staticClass:"card"},[_c('img',{staticClass:"card-img",attrs:{"src":__webpack_require__(1428)("./img" + i + ".svg"),"alt":""}}),_vm._v(" "),_c('div',{staticClass:"card-label text-left"},[_vm._v("\n                    "+_vm._s(_vm.$t(("education_page.selection_4_card_label_" + i)))+"\n                  ")]),_vm._v(" "),_c('div',{staticClass:"card-text"},[_vm._v("\n                    "+_vm._s(_vm.$t(("education_page.selection_4_card_text_" + i)))+"\n                  ")])])])}),_vm._v(" "),_c('v-col',{staticClass:"col-12 d-flex justify-center mt-2"},[_c('v-btn',{attrs:{"large":"","color":"primary","to":"/teacher-listing/1/motivation,1;speciality,21"}},[_vm._v("\n                  "+_vm._s(_vm.$t('step_1_title'))+"\n                ")])],1)],2)],1)],1)])]),_vm._v(" "),_c('section',{staticClass:"section-5"},[_c('div',{staticClass:"row-education"},[_c('div',{staticClass:"business-label"},[_c('div',{staticClass:"business-label__point"}),_vm._v("\n          "+_vm._s(_vm.$t('education_page.selection_title_5'))+"\n        ")])]),_vm._v(" "),_c('div',{staticClass:"row-education"},[_c('v-row',{staticClass:"flex-lg-row-reverse"},[_c('v-col',{staticClass:"col-12 col-md-6 col-lg-5 skills-cont"},[_c('div',{staticClass:"teacher"},[_c('img',{staticClass:"teacher-img",attrs:{"src":__webpack_require__(640),"alt":""}}),_vm._v(" "),_c('div',{staticClass:"teacher-content"},[_c('div',{staticClass:"teacher-content__label"},[_vm._v("\n                  "+_vm._s(_vm.$t('education_page.verified'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"teacher-content__text"},[_vm._v("\n                  "+_vm._s(_vm.$t('education_page.verified_describe'))+"\n                ")])])]),_vm._v(" "),_c('div',{staticClass:"teacher"},[_c('img',{staticClass:"teacher-img",attrs:{"src":__webpack_require__(641),"alt":""}}),_vm._v(" "),_c('div',{staticClass:"teacher-content"},[_c('div',{staticClass:"teacher-content__label"},[_vm._v("\n                  "+_vm._s(_vm.$t('education_page.trusted'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"teacher-content__text"},[_vm._v("\n                  "+_vm._s(_vm.$t('education_page.feedbacks_describe'))+"\n                ")])])]),_vm._v(" "),_c('div',{staticClass:"teacher"},[_c('img',{staticClass:"teacher-img",attrs:{"src":__webpack_require__(642),"alt":""}}),_vm._v(" "),_c('div',{staticClass:"teacher-content"},[_c('div',{staticClass:"teacher-content__label"},[_vm._v("\n                  "+_vm._s(_vm.$t('education_page.specialised'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"teacher-content__text"},[_vm._v("\n                  "+_vm._s(_vm.$t('education_page.specialised_describe'))+"\n                ")])])])]),_vm._v(" "),_c('v-spacer',{staticClass:"d-none d-lg-block"}),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-6 col-lg-6 px-0 pt-0"},[_c('teachers-slider',{attrs:{"data":_vm.teachers,"dark":false}})],1)],1)],1)]),_vm._v(" "),_c('section',{staticClass:"section-6"},[_c('div',{staticClass:"row-education"},[_c('div',{staticClass:"business-label"},[_c('div',{staticClass:"business-label__point"}),_vm._v("\n          "+_vm._s(_vm.$t('education_page.selection_title_6'))+"\n        ")])]),_vm._v(" "),_c('div',{staticClass:"row-education"},[_c('div',{staticClass:"type"},[_c('img',{attrs:{"src":__webpack_require__(643),"alt":""}}),_vm._v(" "),_c('div',{staticClass:"type-content"},[_c('div',{staticClass:"type-content__label"},[_vm._v("\n              "+_vm._s(_vm.$t('education_page.selection_6_card_label_1'))+"\n            ")]),_vm._v(" "),_c('ul',{staticClass:"type-content__list"},[_c('li',[_c('p',[_vm._v("\n                  "+_vm._s(_vm.$t('education_page.selection_6_card_text_1'))+"\n                ")])]),_vm._v(" "),_c('li',[_c('p',[_vm._v(_vm._s(_vm.$t('education_page.selection_6_card_text_2')))])]),_vm._v(" "),_c('li',[_c('p',[_vm._v(_vm._s(_vm.$t('education_page.selection_6_card_text_3')))])]),_vm._v(" "),_c('li',[_c('p',[_vm._v(_vm._s(_vm.$t('education_page.selection_6_card_text_4')))])])])])]),_vm._v(" "),_c('div',{staticClass:"type reverse"},[_c('div',{staticClass:"type-content"},[_c('div',{staticClass:"type-content__label"},[_vm._v("\n              "+_vm._s(_vm.$t('education_page.selection_6_card_label_2'))+"\n            ")]),_vm._v(" "),_c('ul',{staticClass:"type-content__list"},[_c('li',[_c('p',[_vm._v(_vm._s(_vm.$t('education_page.selection_6_card_text_5')))])]),_vm._v(" "),_c('li',[_c('p',[_vm._v(_vm._s(_vm.$t('education_page.selection_6_card_text_6')))])]),_vm._v(" "),_c('li',[_c('p',[_vm._v(_vm._s(_vm.$t('education_page.selection_6_card_text_7')))])]),_vm._v(" "),_c('li',[_c('p',[_vm._v(_vm._s(_vm.$t('education_page.selection_6_card_text_8')))])])])]),_vm._v(" "),_c('img',{attrs:{"src":__webpack_require__(644),"alt":""}})])]),_vm._v(" "),_c('img',{staticClass:"section-6__imgl",attrs:{"src":__webpack_require__(668),"alt":""}}),_vm._v(" "),_c('img',{staticClass:"section-6__imgr",attrs:{"src":__webpack_require__(669),"alt":""}})]),_vm._v(" "),_c('section',{staticClass:"section-7"},[_c('div',{staticClass:"row-education"},[_c('div',{staticClass:"section-7__label"},[_vm._v("\n          "+_vm._s(_vm.$t('education_page.selection_title_7'))+"\n          "),_c('div',{staticClass:"section-7__label-point"})])]),_vm._v(" "),_c('div',{staticClass:"row-education"},[_c('div',{staticStyle:{"width":"100%","padding-right":"21%"}},[_c('img',{staticClass:"section-7__image",attrs:{"src":__webpack_require__(645),"alt":""}})]),_vm._v(" "),_c('div',{staticClass:"form-image"},[_c('img',{attrs:{"src":__webpack_require__(646),"alt":""}}),_vm._v(" "),_c('div',{staticClass:"form-image__text-wrap"},[_c('div',{staticClass:"form-image__text form-image__text-item-1"},[_vm._v("\n              "+_vm._s(_vm.$t('education_page.selection_7_text_1'))+"\n            ")]),_vm._v(" "),_c('div',{staticClass:"form-image__text left form-image__text-item-2"},[_vm._v("\n              "+_vm._s(_vm.$t('education_page.selection_7_text_2'))+"\n            ")]),_vm._v(" "),_c('div',{staticClass:"form-image__text form-image__text-item-3"},[_vm._v("\n              "+_vm._s(_vm.$t('education_page.selection_7_text_3'))+"\n            ")]),_vm._v(" "),_c('div',{staticClass:"form-image__text left form-image__text-item-4"},[_vm._v("\n              "+_vm._s(_vm.$t('education_page.selection_7_text_4'))+"\n            ")])])]),_vm._v(" "),_c('div',{staticClass:"section-7__item section-7__item-1 section-7__top-content"},[_vm._v("\n          "+_vm._s(_vm.$t('education_page.selection_7_text_1'))+"\n        ")]),_vm._v(" "),_c('div',{staticClass:"section-7__item section-7__item-2 section-7__bottom-content"},[_vm._v("\n          "+_vm._s(_vm.$t('education_page.selection_7_text_2'))+"\n        ")]),_vm._v(" "),_c('div',{staticClass:"section-7__item section-7__item-3 section-7__top-content"},[_vm._v("\n          "+_vm._s(_vm.$t('education_page.selection_7_text_3'))+"\n        ")]),_vm._v(" "),_c('div',{staticClass:"section-7__item section-7__item-4 section-7__bottom-content"},[_vm._v("\n          "+_vm._s(_vm.$t('education_page.selection_7_text_4'))+"\n        ")]),_vm._v(" "),_c('div',{staticClass:"d-flex justify-center"},[_c('v-btn',{attrs:{"large":"","color":"primary","to":"/teacher-listing/1/motivation,3;sortOption,9"}},[_vm._v("\n            "+_vm._s(_vm.$t('education_page.selection_7_text_button'))+"\n          ")])],1)])])])],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/education/index.vue?vue&type=template&id=64f3439c&scoped=true&

// EXTERNAL MODULE: ./components/landing-page/TestimonialsSlider.vue + 4 modules
var TestimonialsSlider = __webpack_require__(1384);

// EXTERNAL MODULE: ./components/landing-page/TeachersSlider.vue + 4 modules
var TeachersSlider = __webpack_require__(1385);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/education/index.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var educationvue_type_script_lang_js_ = ({
  name: 'EducationPage',
  components: {
    TestimonialsSlider: TestimonialsSlider["default"],
    TeachersSlider: TeachersSlider["default"]
  },

  async asyncData({
    store
  }) {
    let pageData, teachers, ratings;
    await Promise.allSettled([store.dispatch('education_page/getPageData'), store.dispatch('education_page/getTeachers'), store.dispatch('education_page/getTestimonials')]).then(res => {
      var _res$, _res$2, _res$3;

      pageData = (_res$ = res[0]) === null || _res$ === void 0 ? void 0 : _res$.value;
      teachers = (_res$2 = res[1]) === null || _res$2 === void 0 ? void 0 : _res$2.value;
      ratings = (_res$3 = res[2]) === null || _res$3 === void 0 ? void 0 : _res$3.value;
    });
    return {
      pageData,
      teachers,
      ratings
    };
  },

  data() {
    return {};
  },

  head() {
    var _this$pageData$seoTit, _this$pageData, _this$pageData$seoDes, _this$pageData2, _this$pageData$seoTit2, _this$pageData3, _this$pageData$seoDes2, _this$pageData4;

    return {
      title: (_this$pageData$seoTit = (_this$pageData = this.pageData) === null || _this$pageData === void 0 ? void 0 : _this$pageData.seoTitle) !== null && _this$pageData$seoTit !== void 0 ? _this$pageData$seoTit : 'Private Online Language Lessons for Kids & Teenagers',
      meta: [{
        hid: 'description',
        name: 'description',
        content: (_this$pageData$seoDes = (_this$pageData2 = this.pageData) === null || _this$pageData2 === void 0 ? void 0 : _this$pageData2.seoDescription) !== null && _this$pageData$seoDes !== void 0 ? _this$pageData$seoDes : ''
      }, {
        hid: 'og:title',
        name: 'og:title',
        property: 'og:title',
        content: (_this$pageData$seoTit2 = (_this$pageData3 = this.pageData) === null || _this$pageData3 === void 0 ? void 0 : _this$pageData3.seoTitle) !== null && _this$pageData$seoTit2 !== void 0 ? _this$pageData$seoTit2 : 'Private Online Language Lessons for Kids & Teenagers'
      }, {
        property: 'og:description',
        content: (_this$pageData$seoDes2 = (_this$pageData4 = this.pageData) === null || _this$pageData4 === void 0 ? void 0 : _this$pageData4.seoDescription) !== null && _this$pageData$seoDes2 !== void 0 ? _this$pageData$seoDes2 : ''
      }, {
        hid: 'og:image',
        property: 'og:image',
        content: this.previewImage
      }, {
        hid: 'og:image:width',
        property: 'og:image:width',
        content: 772
      }, {
        hid: 'og:image:height',
        property: 'og:image:height',
        content: 564
      }, {
        hid: 'og:image:type',
        property: 'og:image:type',
        content: 'svg+xml'
      }],
      bodyAttrs: {
        class: 'education-page'
      }
    };
  },

  computed: {
    previewImage() {
      return "'http://localhost:3000'" + __webpack_require__(576);
    }

  }
});
// CONCATENATED MODULE: ./pages/education/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var pages_educationvue_type_script_lang_js_ = (educationvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VSpacer.js
var VSpacer = __webpack_require__(895);

// CONCATENATED MODULE: ./pages/education/index.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1429)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  pages_educationvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "64f3439c",
  "00650ff8"
  
)

/* harmony default export */ var education = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */






installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VRow: VRow["a" /* default */],VSpacer: VSpacer["a" /* default */]})


/***/ }),

/***/ 968:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1029);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("1f907d7b", content, true, context)
};

/***/ }),

/***/ 996:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/StarRating.vue?vue&type=template&id=1645fb89&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:['score', { 'score--large': _vm.large }]},[_vm._ssrNode("<span data-v-1645fb89>"+_vm._ssrEscape(_vm._s(_vm.value_.toFixed(1)))+"</span> <div data-v-1645fb89>"+(_vm._ssrList((_vm.stars),function(i){return ("<svg"+(_vm._ssrAttr("width",_vm.width))+(_vm._ssrAttr("height",_vm.height))+" viewBox=\"0 0 12 12\" data-v-1645fb89><use"+(_vm._ssrAttr("xlink:href",_vm.iconFilledStar))+" data-v-1645fb89></use></svg>")}))+" "+((_vm.isHasHalf)?("<svg"+(_vm._ssrAttr("width",_vm.width))+(_vm._ssrAttr("height",_vm.height))+" viewBox=\"0 0 12 12\" data-v-1645fb89><use"+(_vm._ssrAttr("xlink:href",_vm.iconFilledHalfStar))+" data-v-1645fb89></use></svg>"):"<!---->")+"</div>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/StarRating.vue?vue&type=template&id=1645fb89&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/StarRating.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var StarRatingvue_type_script_lang_js_ = ({
  name: 'StarRating',
  props: {
    value: {
      type: Number,
      required: true
    },
    large: {
      type: Boolean,
      required: false
    }
  },

  data() {
    return {
      iconFilledStar: `${__webpack_require__(14)}#filledStar`,
      iconFilledHalfStar: `${__webpack_require__(14)}#filledHalfStar`
    };
  },

  computed: {
    width() {
      return this.large ? 20 : 12;
    },

    height() {
      return this.large ? 20 : 12;
    },

    value_() {
      return Math.round(this.value * 10) / 10;
    },

    isRoundToLess() {
      const rest = Math.round(this.value_ % 1 * 10);
      return rest <= 5 && rest !== 0;
    },

    roundToLessHalf() {
      return this.isRoundToLess ? Math.floor(this.value_ * 2) / 2 : Math.ceil(this.value_ * 2) / 2;
    },

    stars() {
      return this.isRoundToLess ? Math.floor(this.roundToLessHalf) : Math.ceil(this.roundToLessHalf);
    },

    isHasHalf() {
      return this.isRoundToLess && this.value_ !== 5 || this.value_ < 0.5;
    }

  }
});
// CONCATENATED MODULE: ./components/StarRating.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_StarRatingvue_type_script_lang_js_ = (StarRatingvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/StarRating.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1028)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_StarRatingvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "1645fb89",
  "743e07b2"
  
)

/* harmony default export */ var StarRating = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=index.js.map