{"version": 3, "file": "pages/user/lessons/confirmation/index.js", "sources": ["webpack:///./components/CalendarDate.vue?b16c", "webpack:///./components/CalendarDate.vue?8562", "webpack:///./components/user-lessons/TimePickerDialog.vue?cb26", "webpack:///./components/user-lessons/TimePickerDialog.vue?9c62", "webpack:///./components/Steps.vue?d5f4", "webpack:///./components/Steps.vue?5dea", "webpack:///./components/user-lessons/TimePickerDialog.vue?a819", "webpack:///./components/user-lessons/TimePickerDialog.vue", "webpack:///./components/user-lessons/TimePickerDialog.vue?14ab", "webpack:///./components/user-lessons/TimePickerDialog.vue?8604", "webpack:///./components/Calendar.vue?bb58", "webpack:///./components/Calendar.vue?7f8a", "webpack:///./node_modules/vuetify/src/components/VCalendar/mixins/calendar-with-events.sass?4b20", "webpack:///./node_modules/vuetify/src/components/VCalendar/mixins/calendar-with-events.sass", "webpack:///./node_modules/vuetify/src/components/VCalendar/VCalendarWeekly.sass", "webpack:///./node_modules/vuetify/src/components/VCalendar/VCalendarDaily.sass?52f7", "webpack:///./node_modules/vuetify/src/components/VCalendar/VCalendarDaily.sass", "webpack:///./node_modules/vuetify/src/components/VCalendar/VCalendarCategory.sass?8c44", "webpack:///./node_modules/vuetify/src/components/VCalendar/VCalendarCategory.sass", "webpack:///./components/user-lessons/UpcomingLesson.vue?317b", "webpack:///./components/user-lessons/UpcomingLesson.vue", "webpack:///./components/user-lessons/UpcomingLesson.vue?58ef", "webpack:///./components/user-lessons/UpcomingLesson.vue?1a32", "webpack:///./components/user-lessons/PastLesson.vue?e104", "webpack:///./components/user-lessons/PastLesson.vue", "webpack:///./components/user-lessons/PastLesson.vue?d2a2", "webpack:///./components/user-lessons/PastLesson.vue?4102", "webpack:///./components/user-lessons/UnscheduledLesson.vue?514f", "webpack:///./components/user-lessons/UnscheduledLesson.vue", "webpack:///./components/user-lessons/UnscheduledLesson.vue?d8bb", "webpack:///./components/user-lessons/UnscheduledLesson.vue?8e00", "webpack:///./components/user-lessons/LessonsPage.vue?025c", "webpack:///./components/user-lessons/LessonsPage.vue?7393", "webpack:///./components/user-lessons/LessonsPage.vue?e1d1", "webpack:///./components/user-lessons/LessonsPage.vue", "webpack:///./components/user-lessons/LessonsPage.vue?f028", "webpack:///./components/user-lessons/LessonsPage.vue?a173", "webpack:///./pages/user/lessons/confirmation/index.vue?dc6f", "webpack:///./pages/user/lessons/confirmation/index.vue", "webpack:///./pages/user/lessons/confirmation/index.vue?3e5e", "webpack:///./pages/user/lessons/confirmation/index.vue?a6a0", "webpack:///../../../src/components/VBtn/index.ts", "webpack:///./components/form/Editor.vue?b6fd", "webpack:///./components/UserStatus.vue?1220", "webpack:///./components/UserStatus.vue", "webpack:///./components/UserStatus.vue?34a5", "webpack:///./components/UserStatus.vue?d7af", "webpack:///./components/Pagination.vue?0246", "webpack:///./components/Pagination.vue?f157", "webpack:///./components/Pagination.vue", "webpack:///./components/Pagination.vue?0e64", "webpack:///./components/Pagination.vue?ea7a", "webpack:///./components/form/SearchInput.vue?0840", "webpack:///./mixins/Avatars.vue", "webpack:///./mixins/Avatars.vue?9044", "webpack:///./mixins/Avatars.vue?7fa3", "webpack:///./components/UserStatus.vue?669a", "webpack:///./components/form/Editor.vue?03ad", "webpack:///./components/form/Editor.vue?6e6d", "webpack:///./components/MessageDialog.vue?1cf5", "webpack:///./components/TimePickerItem.vue?de41", "webpack:///./components/form/Editor.vue?3107", "webpack:///./components/form/Editor.vue", "webpack:///./components/form/Editor.vue?13f3", "webpack:///./components/form/Editor.vue?9998", "webpack:///./components/form/SearchInput.vue?47cd", "webpack:///./components/form/SearchInput.vue", "webpack:///./components/form/SearchInput.vue?67df", "webpack:///./components/form/SearchInput.vue?7b4d", "webpack:///./components/user-lessons/LessonItem.vue?48ff", "webpack:///./components/user-lessons/LessonItem.vue", "webpack:///./components/user-lessons/LessonItem.vue?8e8d", "webpack:///./components/user-lessons/LessonItem.vue?eb62", "webpack:///./components/TimePickerItem.vue?8a6c", "webpack:///./components/TimePickerItem.vue", "webpack:///./components/TimePickerItem.vue?0a32", "webpack:///./components/TimePickerItem.vue?6b63", "webpack:///./components/Pagination.vue?89ad", "webpack:///./components/Pagination.vue?8991", "webpack:///./components/TimePicker.vue?b078", "webpack:///./components/MessageDialog.vue?d1e2", "webpack:///./components/MessageDialog.vue", "webpack:///./components/MessageDialog.vue?1807", "webpack:///./components/MessageDialog.vue?3fce", "webpack:///./components/form/SearchInput.vue?84f4", "webpack:///./components/form/SearchInput.vue?618a", "webpack:///./components/TimePicker.vue?6f18", "webpack:///./components/TimePicker.vue", "webpack:///./components/TimePicker.vue?a8a1", "webpack:///./components/TimePicker.vue?e495", "webpack:///./components/user-lessons/LessonItem.vue?3c78", "webpack:///./components/UserStatus.vue?80fc", "webpack:///./components/UserStatus.vue?41dd", "webpack:///./components/CalendarDate.vue?abbb", "webpack:///./components/user-lessons/TimePickerDialog.vue?9c7b", "webpack:///./components/Steps.vue?8045", "webpack:///./components/CalendarDate.vue?01bb", "webpack:///./components/CalendarDate.vue", "webpack:///./components/CalendarDate.vue?f7f8", "webpack:///./components/CalendarDate.vue?13d4", "webpack:///./components/Calendar.vue?bf9e", "webpack:///./node_modules/vuetify/src/components/VCalendar/VCalendarWeekly.sass?7083", "webpack:///./components/MessageDialog.vue?974f", "webpack:///./components/MessageDialog.vue?219a", "webpack:///./components/TimePickerItem.vue?60b5", "webpack:///./components/TimePickerItem.vue?674c", "webpack:///./components/Steps.vue?9897", "webpack:///./components/Steps.vue", "webpack:///./components/Steps.vue?d37c", "webpack:///./components/Steps.vue?d334", "webpack:///./components/TimePicker.vue?62ca", "webpack:///./components/TimePicker.vue?3b77", "webpack:///./components/Calendar.vue?2219", "webpack:///./components/Calendar.vue", "webpack:///./components/Calendar.vue?f755", "webpack:///../../../src/mixins/localable/index.ts", "webpack:///../../../../src/components/VCalendar/mixins/mouse.ts", "webpack:///../../src/util/dateTimeUtils.ts", "webpack:///../../../../src/components/VCalendar/util/timestamp.ts", "webpack:///../../../../src/components/VCalendar/mixins/times.ts", "webpack:///../../../../src/components/VCalendar/modes/common.ts", "webpack:///../../../../src/components/VCalendar/modes/stack.ts", "webpack:///../../../../src/components/VCalendar/modes/column.ts", "webpack:///../../../../src/components/VCalendar/modes/index.ts", "webpack:///../../../../src/components/VCalendar/util/props.ts", "webpack:///../../../../src/components/VCalendar/mixins/calendar-base.ts", "webpack:///../../../../src/components/VCalendar/util/events.ts", "webpack:///../../../../src/components/VCalendar/mixins/calendar-with-events.ts", "webpack:///../../../src/components/VCalendar/VCalendarWeekly.ts", "webpack:///../../../src/components/VCalendar/VCalendarMonthly.ts", "webpack:///../../../../src/components/VCalendar/mixins/calendar-with-intervals.ts", "webpack:///../../../src/components/VCalendar/VCalendarDaily.ts", "webpack:///../../../../src/components/VCalendar/util/parser.ts", "webpack:///../../../src/components/VCalendar/VCalendarCategory.ts", "webpack:///../../../src/components/VCalendar/VCalendar.ts", "webpack:///./components/Calendar.vue?b306", "webpack:///./mixins/StatusOnline.vue", "webpack:///./mixins/StatusOnline.vue?c046", "webpack:///./mixins/StatusOnline.vue?62a8", "webpack:///./components/user-lessons/LessonsPage.vue?f819", "webpack:///./components/user-lessons/LessonItem.vue?0c19", "webpack:///./components/user-lessons/LessonItem.vue?c122"], "sourcesContent": ["export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./CalendarDate.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".calendar-date{position:relative}.calendar-date-marker{position:absolute;left:50%;bottom:3px;width:4px;height:4px;margin-left:-2px;border-radius:2px;background-color:transparent}.calendar-date-marker.calendar-date-marker--free{background-color:var(--v-green-base)}.calendar-date-marker.calendar-date-marker--some-free{background-color:var(--v-primary-base)}.calendar-date-marker.calendar-date-marker--occupied{background-color:var(--v-orange-base)}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TimePickerDialog.vue?vue&type=style&index=0&id=3bb5b460&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".selects-language[data-v-3bb5b460]{width:auto!important;min-width:120px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Steps.vue?vue&type=style&index=0&id=307c13c8&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_GET_URL_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/getUrl.js\");\nvar ___CSS_LOADER_URL_IMPORT_0___ = require(\"../assets/images/step-bg.svg\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\nvar ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"@media only screen and (min-width:992px){.steps[data-v-307c13c8]{margin-bottom:20px}}@media only screen and (max-width:991px){.steps-wrap[data-v-307c13c8]{width:calc(100% + 30px);height:50px;margin-left:-15px;margin-bottom:24px;overflow:hidden}}@media only screen and (max-width:991px){.steps-helper[data-v-307c13c8]{padding-bottom:17px;overflow-x:auto;overflow-y:hidden;box-sizing:content-box}}.steps-list[data-v-307c13c8]{display:flex;justify-content:space-between}@media only screen and (max-width:991px){.steps-list[data-v-307c13c8]{height:50px}}.steps .step-item[data-v-307c13c8]{position:relative;display:flex;align-items:center;width:100%;max-width:312px;margin-right:20px;padding:10px 22px;letter-spacing:.3px}@media only screen and (min-width:992px){.steps .step-item[data-v-307c13c8]{height:52px}.steps .step-item[data-v-307c13c8]:last-child{margin-right:0}}@media only screen and (min-width:992px)and (max-width:1439px){.steps .step-item[data-v-307c13c8]{margin-right:10px;padding:10px}}@media only screen and (max-width:991px){.steps .step-item[data-v-307c13c8]{flex:1 0 220px;width:220px;margin:0 0 0 15px}}@media only screen and (max-width:639px){.steps .step-item[data-v-307c13c8]{flex:1 0 280px;width:280px;padding:10px 5px 10px 12px}}.steps .step-item a[data-v-307c13c8]{position:absolute;top:0;left:0;width:100%;height:100%;z-index:2}.steps .step-item-helper[data-v-307c13c8]{position:relative;padding-left:48px}@media only screen and (max-width:639px){.steps .step-item-helper[data-v-307c13c8]{padding-left:45px}}.steps .step-item-number[data-v-307c13c8]{position:absolute;top:50%;left:0;display:flex;align-items:center;justify-content:center;width:33px;height:33px;padding:0 0 3px 2px;border-radius:50%;background:linear-gradient(126.15deg,rgba(128,182,34,.72),rgba(60,135,248,.72) 102.93%);transform:translateY(-50%)}.steps .step-item-number span[data-v-307c13c8]{position:relative;display:inline-block;font-size:16px;font-weight:700}.steps .step-item-title[data-v-307c13c8]{font-size:14px;font-weight:700;line-height:1.28}.steps .step-item-text[data-v-307c13c8]{font-size:12px;line-height:1.5}.steps .step-item:not(.step-item--active) .step-item-number span[data-v-307c13c8]{color:var(--v-success-base);background:linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));background:-webkit-linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));-webkit-background-clip:text;-webkit-text-fill-color:transparent}.steps .step-item:not(.step-item--active) .step-item-number[data-v-307c13c8]:before{content:\\\"\\\";position:absolute;top:1px;left:1px;width:calc(100% - 2px);height:calc(100% - 2px);background-color:var(--v-greyBg-base);border-radius:50%}.steps .step-item--active[data-v-307c13c8],.steps .step-item--link[data-v-307c13c8]:hover{background-image:url(\" + ___CSS_LOADER_URL_REPLACEMENT_0___ + \");background-repeat:no-repeat;background-size:100% 100%;background-position:50%}.steps .step-item--active .step-item-number[data-v-307c13c8],.steps .step-item--link:hover .step-item-number[data-v-307c13c8]{color:#fff}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{\"dialog\":_vm.isShownTimePickerDialog,\"max-width\":\"1030\",\"custom-class\":\"time-picker\",\"persistent\":\"\",\"fullscreen\":_vm.$vuetify.breakpoint.smAndDown}},_vm.$listeners),[_c('div',{staticClass:\"time-picker-header text--gradient\"},[_vm._v(\"\\n    \"+_vm._s(_vm.$t('schedule_your_lessons'))+\":\\n  \")]),_vm._v(\" \"),_c('div',{staticClass:\"wrap\"},[_c('div',{staticClass:\"time-picker-body\"},[_c('div',{staticClass:\"time-picker-selects mb-md-2\"},[_c('div',{staticClass:\"selects\"},[_c('div',{staticClass:\"selects-language\"},[_c('div',{staticClass:\"selects-lesson-value d-flex align-center\"},[_c('div',{staticClass:\"icon icon-flag elevation-2\"},[(_vm.language.isoCode)?_c('v-img',{attrs:{\"src\":require((\"~/assets/images/flags/\" + (_vm.language.isoCode) + \".svg\")),\"width\":\"18\",\"height\":\"18\"}}):_vm._e()],1),_vm._v(\"\\n              \"+_vm._s(_vm.language.name)+\"\\n            \")])]),_vm._v(\" \"),(_vm.course)?_c('div',{staticClass:\"selects-course\"},[_vm._v(\"\\n            \"+_vm._s(_vm.course)+\"\\n          \")]):_vm._e(),_vm._v(\" \"),_c('div',{staticClass:\"selects-lesson\"},[_c('div',{staticClass:\"selects-lesson-value d-flex align-center\"},[_c('div',{staticClass:\"icon\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/clock-gradient.svg'),\"width\":\"18\",\"height\":\"18\"}})],1),_vm._v(\"\\n\\n              \"+_vm._s(_vm.$tc('lessons_count', _vm.countLessons))+\"\\n            \")])]),_vm._v(\" \"),_c('div',{staticClass:\"selects-duration\"},[_c('div',{staticClass:\"selects-lesson-value d-flex align-center\"},[_c('div',{staticClass:\"icon\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/clock-gradient.svg'),\"width\":\"18\",\"height\":\"18\"}})],1),_vm._v(\"\\n\\n              \"+_vm._s(_vm.$tc('minutes_count', _vm.lessonLength))+\"\\n            \")])])])]),_vm._v(\" \"),_c('time-picker',_vm._g({attrs:{\"username\":_vm.username,\"lesson-length\":_vm.lessonLength,\"quantity-lessons\":_vm.countLessons,\"current-time\":_vm.currentTime,\"is-shown-time-picker-dialog\":_vm.isShownTimePickerDialog}},_vm.$listeners))],1)]),_vm._v(\" \"),_c('div',{staticClass:\"time-picker-footer d-flex justify-md-end\"},[_c('v-btn',{attrs:{\"small\":\"\",\"color\":\"primary\",\"disabled\":_vm.isNotValid},on:{\"click\":_vm.scheduleLessons}},[_vm._v(\"\\n      \"+_vm._s(_vm.$t('schedule_now'))+\"\\n    \")])],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport LDialog from '~/components/LDialog'\nimport TimePicker from '~/components/TimePicker'\n\nexport default {\n  name: 'TimePickerDialog',\n  components: { LDialog, TimePicker },\n  props: {\n    isShownTimePickerDialog: {\n      type: Boolean,\n      required: true,\n    },\n    username: {\n      type: String,\n      required: true,\n    },\n    language: {\n      type: Object,\n      required: true,\n    },\n    currentTime: {\n      type: Object,\n      required: true,\n    },\n    lessonLength: {\n      type: Number,\n      required: true,\n    },\n    countLessons: {\n      type: Number,\n      required: true,\n    },\n    purchaseId: {\n      type: Number,\n      required: true,\n    },\n    course: {\n      type: String,\n      required: true,\n    },\n  },\n  computed: {\n    selectedSlots() {\n      return this.$store.state.teacher_profile.selectedSlots || []\n    },\n    isNotValid() {\n      return this.selectedSlots.length === 0\n    },\n  },\n  methods: {\n    scheduleLessons() {\n      const slots = []\n\n      if (this.selectedSlots.length) {\n        this.selectedSlots.forEach((item) => {\n          item.forEach((el) => slots.push(el.id))\n        })\n      }\n\n      if (slots.length) {\n        this.$store\n          .dispatch('lesson/scheduleLesson', {\n            purchaseId: this.purchaseId,\n            slots,\n          })\n          .then(() => {\n            this.$store.commit('user/DECREASE_UNSCHEDULED_LESSONS_NUMBER')\n            this.$emit('close-dialog')\n            this.$store.commit('teacher_profile/RESET_SELECTED_SLOTS')\n            this.$router.push({ path: '/user/lessons' })\n          })\n          .catch((e) => {\n            this.$store.dispatch('snackbar/error')\n\n            console.info(e)\n          })\n      }\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TimePickerDialog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TimePickerDialog.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TimePickerDialog.vue?vue&type=template&id=3bb5b460&scoped=true&\"\nimport script from \"./TimePickerDialog.vue?vue&type=script&lang=js&\"\nexport * from \"./TimePickerDialog.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./TimePickerDialog.vue?vue&type=style&index=0&id=3bb5b460&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"3bb5b460\",\n  \"6e78877e\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {TimePicker: require('D:/languworks/langu-frontend/components/TimePicker.vue').default,LDialog: require('D:/languworks/langu-frontend/components/LDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VBtn,VImg})\n", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Calendar.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".calendar{padding:26px 22px 38px;color:var(--v-greyBg-base);background-color:var(--v-darkLight-base);box-shadow:0 30px 84px rgba(19,10,46,.08),0 8px 32px rgba(19,10,46,.07),0 3px 14px rgba(19,10,46,.03),0 1px 3px rgba(19,10,46,.13);border-radius:16px}@media only screen and (max-width:1439px){.calendar{padding:24px 12px 32px}}.calendar-title{font-size:18px}.calendar .theme--light.v-calendar-weekly{border:none!important;background-color:transparent!important}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday{font-weight:700;color:var(--v-greyBg-base)!important;text-overflow:unset;text-transform:capitalize;border:inherit!important}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday.v-outside{background-color:inherit!important}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day{border:inherit!important;background-color:inherit!important}@media only screen and (max-width:1215px){.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day-label{margin-top:8px}}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day .v-btn{min-width:38px!important;width:38px!important;height:38px!important;font-size:14px!important;color:#fff!important}@media only screen and (max-width:1215px){.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day .v-btn{min-width:32px!important;width:32px!important;height:32px!important;font-size:12px!important}}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day.v-outside{opacity:.7}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day.v-outside .v-btn{color:var(--v-greyBg-darken2)!important}.calendar .theme--light.v-calendar-weekly .v-calendar-weekly__day.v-present .v-btn{font-weight:600!important;background:linear-gradient(126.15deg,rgba(128,182,34,.48),rgba(60,135,248,.48) 102.93%)!important}.calendar--read-only .v-calendar-weekly__day>*{cursor:auto!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./calendar-with-events.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../../vue-style-loader/lib/addStylesServer.js\").default(\"0769cd4a\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".theme--light.v-calendar-events .v-event-timed{border:1px solid!important}.theme--light.v-calendar-events .v-event-more{background-color:#fff}.theme--light.v-calendar-events .v-event-more.v-outside{background-color:#f7f7f7}.theme--dark.v-calendar-events .v-event-timed{border:1px solid!important}.theme--dark.v-calendar-events .v-event-more{background-color:#303030}.theme--dark.v-calendar-events .v-event-more.v-outside{background-color:#202020}.v-calendar .v-event{line-height:20px;margin-right:-1px;border-radius:4px}.v-calendar .v-event,.v-calendar .v-event-more{position:relative;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:12px;cursor:pointer;z-index:1}.v-calendar .v-event-more{font-weight:700}.v-calendar .v-event-timed-container{position:absolute;top:0;bottom:0;left:0;right:0;margin-right:10px;pointer-events:none}.v-calendar .v-event-timed{position:absolute;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:12px;cursor:pointer;border-radius:4px;pointer-events:all}.v-calendar.v-calendar-events .v-calendar-weekly__head-weekday{margin-right:-1px}.v-calendar.v-calendar-events .v-calendar-weekly__day{overflow:visible;margin-right:-1px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".theme--light.v-calendar-weekly{background-color:#fff;border-top:1px solid #e0e0e0;border-left:1px solid #e0e0e0}.theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday{border-right:1px solid #e0e0e0;color:#000}.theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday.v-past{color:rgba(0,0,0,.38)}.theme--light.v-calendar-weekly .v-calendar-weekly__head-weekday.v-outside{background-color:#f7f7f7}.theme--light.v-calendar-weekly .v-calendar-weekly__head-weeknumber{background-color:#f1f3f4;border-right:1px solid #e0e0e0}.theme--light.v-calendar-weekly .v-calendar-weekly__day{border-right:1px solid #e0e0e0;border-bottom:1px solid #e0e0e0;color:#000}.theme--light.v-calendar-weekly .v-calendar-weekly__day.v-outside{background-color:#f7f7f7}.theme--light.v-calendar-weekly .v-calendar-weekly__weeknumber{background-color:#f1f3f4;border-right:1px solid #e0e0e0;border-bottom:1px solid #e0e0e0;color:#000}.theme--dark.v-calendar-weekly{background-color:#303030;border-top:1px solid #9e9e9e;border-left:1px solid #9e9e9e}.theme--dark.v-calendar-weekly .v-calendar-weekly__head-weekday{border-right:1px solid #9e9e9e;color:#fff}.theme--dark.v-calendar-weekly .v-calendar-weekly__head-weekday.v-past{color:hsla(0,0%,100%,.5)}.theme--dark.v-calendar-weekly .v-calendar-weekly__head-weekday.v-outside{background-color:#202020}.theme--dark.v-calendar-weekly .v-calendar-weekly__head-weeknumber{background-color:#202020;border-right:1px solid #9e9e9e}.theme--dark.v-calendar-weekly .v-calendar-weekly__day{border-right:1px solid #9e9e9e;border-bottom:1px solid #9e9e9e;color:#fff}.theme--dark.v-calendar-weekly .v-calendar-weekly__day.v-outside{background-color:#202020}.theme--dark.v-calendar-weekly .v-calendar-weekly__weeknumber{background-color:#202020;border-right:1px solid #9e9e9e;border-bottom:1px solid #9e9e9e;color:#fff}.v-calendar-weekly{width:100%;height:100%;display:flex;flex-direction:column;min-height:0}.v-calendar-weekly__head{display:flex}.v-calendar-weekly__head,.v-calendar-weekly__head-weekday{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-calendar-weekly__head-weekday{flex:1 0 20px;padding:0 2px 6px;font-size:13px;overflow:hidden;text-align:center;text-overflow:ellipsis;text-transform:uppercase;white-space:nowrap}.v-calendar-weekly__head-weeknumber{position:relative;flex:0 0 24px}.v-calendar-weekly__week{display:flex;flex:1;height:unset;min-height:0}.v-calendar-weekly__weeknumber{display:flex;flex:0 0 24px;height:unset;min-height:0;padding-top:14.5px;text-align:center}.v-calendar-weekly__weeknumber>small{width:100%!important}.v-calendar-weekly__day{flex:1;width:0;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;padding:0;min-width:0}.v-calendar-weekly__day.v-present .v-calendar-weekly__day-month{color:currentColor}.v-calendar-weekly__day-label{text-decoration:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer;box-shadow:none;text-align:center;margin:14px 0 0}.v-calendar-weekly__day-label .v-btn{font-size:12px;text-transform:none}.v-calendar-weekly__day-month{position:absolute;text-decoration:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;box-shadow:none;top:0;left:36px;height:32px;line-height:32px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./VCalendarDaily.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"1ffbeeca\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".theme--light.v-calendar-daily{background-color:#fff;border-left:1px solid #e0e0e0;border-top:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__intervals-head{border-right:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__intervals-head:after{background:#e0e0e0;background:linear-gradient(90deg,transparent,#e0e0e0)}.theme--light.v-calendar-daily .v-calendar-daily_head-day{border-right:1px solid #e0e0e0;border-bottom:1px solid #e0e0e0;color:#000}.theme--light.v-calendar-daily .v-calendar-daily_head-day.v-past .v-calendar-daily_head-day-label,.theme--light.v-calendar-daily .v-calendar-daily_head-day.v-past .v-calendar-daily_head-weekday{color:rgba(0,0,0,.38)}.theme--light.v-calendar-daily .v-calendar-daily__intervals-body{border-right:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__intervals-body .v-calendar-daily__interval-text{color:#424242}.theme--light.v-calendar-daily .v-calendar-daily__day{border-right:1px solid #e0e0e0;border-bottom:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__day-interval{border-top:1px solid #e0e0e0}.theme--light.v-calendar-daily .v-calendar-daily__day-interval:first-child{border-top:none!important}.theme--light.v-calendar-daily .v-calendar-daily__interval:after{border-top:1px solid #e0e0e0}.theme--dark.v-calendar-daily{background-color:#303030;border-left:1px solid #9e9e9e;border-top:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__intervals-head{border-right:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__intervals-head:after{background:#9e9e9e;background:linear-gradient(90deg,transparent,#9e9e9e)}.theme--dark.v-calendar-daily .v-calendar-daily_head-day{border-right:1px solid #9e9e9e;border-bottom:1px solid #9e9e9e;color:#fff}.theme--dark.v-calendar-daily .v-calendar-daily_head-day.v-past .v-calendar-daily_head-day-label,.theme--dark.v-calendar-daily .v-calendar-daily_head-day.v-past .v-calendar-daily_head-weekday{color:hsla(0,0%,100%,.5)}.theme--dark.v-calendar-daily .v-calendar-daily__intervals-body{border-right:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__intervals-body .v-calendar-daily__interval-text{color:#eee}.theme--dark.v-calendar-daily .v-calendar-daily__day{border-right:1px solid #9e9e9e;border-bottom:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__day-interval{border-top:1px solid #9e9e9e}.theme--dark.v-calendar-daily .v-calendar-daily__day-interval:first-child{border-top:none!important}.theme--dark.v-calendar-daily .v-calendar-daily__interval:after{border-top:1px solid #9e9e9e}.v-calendar-daily{display:flex;flex-direction:column;overflow:hidden;height:100%}.v-calendar-daily__head{flex:none;display:flex}.v-calendar-daily__intervals-head{flex:none;position:relative}.v-calendar-daily__intervals-head:after{position:absolute;bottom:0;height:1px;left:0;right:0;content:\\\"\\\"}.v-calendar-daily_head-day{flex:1 1 auto;width:0;position:relative}.v-calendar-daily_head-weekday{padding:3px 0 0;font-size:11px;text-transform:uppercase}.v-calendar-daily_head-day-label,.v-calendar-daily_head-weekday{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-align:center}.v-calendar-daily_head-day-label{padding:0 0 3px;cursor:pointer}.v-calendar-daily__body{flex:1 1 60%;overflow:hidden;display:flex;position:relative;flex-direction:column}.v-calendar-daily__scroll-area{overflow-y:scroll;flex:1 1 auto;display:flex;align-items:flex-start}.v-calendar-daily__pane{width:100%;overflow-y:hidden;flex:none;display:flex;align-items:flex-start}.v-calendar-daily__day-container{display:flex;flex:1;width:100%;height:100%}.v-calendar-daily__intervals-body{flex:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-calendar-daily__interval{text-align:right;padding-right:8px;border-bottom:none;position:relative}.v-calendar-daily__interval:after{width:8px;position:absolute;height:1px;display:block;content:\\\"\\\";right:0;bottom:-1px}.v-calendar-daily__interval-text{display:block;position:relative;top:-6px;font-size:10px;padding-right:4px}.v-calendar-daily__day{flex:1;width:0;position:relative}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./VCalendarCategory.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"2b825c72\", content, true)", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../../css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".theme--light.v-calendar-category .v-calendar-category__column,.theme--light.v-calendar-category .v-calendar-category__column-header{border-right:1px solid #e0e0e0}.theme--dark.v-calendar-category .v-calendar-category__column,.theme--dark.v-calendar-category .v-calendar-category__column-header{border-right:1px solid #9e9e9e}.v-calendar-category .v-calendar-category__category{text-align:center}.v-calendar-category .v-calendar-daily__day-container .v-calendar-category__columns{position:absolute;height:100%;width:100%;top:0}.v-calendar-category .v-calendar-category__columns{display:flex}.v-calendar-category .v-calendar-category__columns .v-calendar-category__column,.v-calendar-category .v-calendar-category__columns .v-calendar-category__column-header{flex:1 1 auto;width:0;position:relative}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('lesson-item',{attrs:{\"item\":_vm.item,\"user-statuses\":_vm.userStatuses},scopedSlots:_vm._u([{key:\"lessonAdditionalActionsTop\",fn:function(){return [_c('div',[_c('nuxt-link',{attrs:{\"to\":\"/user/settings#calendar\"}},[(_vm.item.isSyncedWithCalendar)?[_c('v-img',{attrs:{\"src\":require('~/assets/images/check-gradient.svg'),\"width\":\"12\",\"height\":\"13\"}}),_vm._v(\"\\n          \"+_vm._s(_vm.$t('synced_with_my_calendar'))+\"\\n        \")]:[_c('v-img',{attrs:{\"src\":require('~/assets/images/gear-icon-gradient.svg'),\"width\":\"13\",\"height\":\"14\"}}),_vm._v(\"\\n          \"+_vm._s(_vm.$t('configure_calendar_sync'))+\"\\n        \")]],2)],1),_vm._v(\" \"),_c('div',[_c('a',{attrs:{\"href\":(\"/lesson/\" + (_vm.item.lessonId) + \"/icalendar/get\")}},[_c('v-img',{attrs:{\"src\":require('~/assets/images/download-icon-gradient.svg'),\"width\":\"14\",\"height\":\"14\"}}),_vm._v(\"\\n        \"+_vm._s(_vm.$t('download_ics_calendar_file'))+\"\\n      \")],1)])]},proxy:true}])})}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport LessonItem from '~/components/user-lessons/LessonItem'\n\nexport default {\n  name: 'UpcomingLesson',\n  components: { LessonItem },\n  props: {\n    item: {\n      type: Object,\n      required: true,\n    },\n    userStatuses: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UpcomingLesson.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UpcomingLesson.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./UpcomingLesson.vue?vue&type=template&id=bc3603d2&\"\nimport script from \"./UpcomingLesson.vue?vue&type=script&lang=js&\"\nexport * from \"./UpcomingLesson.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"32560e4f\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VImg})\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('lesson-item',{attrs:{\"item\":_vm.item,\"user-statuses\":_vm.userStatuses},scopedSlots:_vm._u([(_vm.item.isInitialized || _vm.item.isFinished)?{key:\"lessonAdditionalActionsTop\",fn:function(){return [_c('div',[_c('span',{staticClass:\"action\",on:{\"click\":_vm.downloadPdfClickHandler}},[_c('v-img',{attrs:{\"src\":require('~/assets/images/download-icon-gradient.svg'),\"width\":\"16\",\"height\":\"16\"}}),_vm._v(\"\\n        \"+_vm._s(_vm.$t('whiteboard_pdf'))+\"\\n      \")],1)])]},proxy:true}:null,{key:\"lessonAdditionalActionsBottom\",fn:function(scope){return [(_vm.allowedRate)?_c('div',[_c('span',{staticClass:\"action\",on:{\"click\":_vm.rateLesson}},[_c('v-img',{staticStyle:{\"left\":\"1px\"},attrs:{\"src\":require('~/assets/images/star-icon-gradient.svg'),\"width\":\"14\",\"height\":\"14\"}}),_vm._v(\"\\n        \"+_vm._s(_vm.$t('rate_lesson'))+\"\\n      \")],1)]):_vm._e(),_vm._v(\" \"),(_vm.isTeacher)?[(_vm.item.isFinished)?[_c('div',[_c('div',[_c('v-img',{attrs:{\"src\":require('~/assets/images/coins-icon-gradient.svg'),\"width\":\"16\",\"height\":\"16\"}}),_vm._v(\"\\n            \"+_vm._s(_vm.$t('class_finished'))+\"\\n          \")],1)])]:[_c('div',[_c('span',{staticClass:\"action\",on:{\"click\":function($event){return _vm.showFinishDialog(scope)}}},[_c('v-img',{staticStyle:{\"left\":\"3px\"},attrs:{\"src\":require('~/assets/images/check-gradient.svg'),\"width\":\"12\",\"height\":\"13\"}}),_vm._v(\"\\n            \"+_vm._s(_vm.$t('mark_as_finished'))+\"\\n          \")],1)])]]:_vm._e()]}},{key:\"dialog\",fn:function(scope){return [(_vm.dialogType === 'finishDialog')?[_c('div',{staticClass:\"lesson-dialog-title\"},[_c('div',{staticClass:\"lesson-dialog-title-icon\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/dollar-coins-gradient.svg'),\"width\":\"20\",\"height\":\"20\"}})],1),_vm._v(\" \"),_c('span',{staticClass:\"font-weight-medium text--gradient\"},[_vm._v(\"\\n          \"+_vm._s(_vm.$t('do_you_want_to_finish_this_class'))+\"\\n        \")])]),_vm._v(\" \"),_c('div',{staticClass:\"lesson-dialog-content l-scroll l-scroll--grey\"},[_vm._v(\"\\n        \"+_vm._s(_vm.$t('this_will_move_class_to_past_lessons_and_trigger_payment'))+\"\\n      \")]),_vm._v(\" \"),_c('div',{staticClass:\"lesson-dialog-buttons\"},[_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"greyDark\",\"outlined\":\"\"},on:{\"click\":function($event){return _vm.closeDialog(scope)}}},[_vm._v(\"\\n          \"+_vm._s(_vm.$t('do_not_finish_class'))+\"\\n        \")]),_vm._v(\" \"),_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"primary\"},on:{\"click\":function($event){return _vm.finishClickHandler(scope)}}},[_vm._v(\"\\n          \"+_vm._s(_vm.$t('finish_class'))+\"\\n        \")])],1)]:_vm._e()]}}],null,true)},[_vm._v(\" \"),_vm._v(\" \"),_vm._v(\" \"),(_vm.allowedRate)?_c('lesson-evaluation-dialog',{attrs:{\"item\":_vm.feedbackLessonData,\"dialog\":_vm.isShownEvaluationDialog,\"close-button\":\"\",\"persistent\":false},on:{\"close\":_vm.closeEvaluationDialog}}):_vm._e()],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport LessonItem from '~/components/user-lessons/LessonItem'\nimport LessonEvaluationDialog from '~/components/user-lessons/LessonEvaluationDialog'\n\nexport default {\n  name: 'PastLesson',\n  components: { LessonItem, LessonEvaluationDialog },\n  props: {\n    item: {\n      type: Object,\n      required: true,\n    },\n    userStatuses: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  data() {\n    return {\n      dialogType: null,\n      feedbackLessonData: {},\n      isShownEvaluationDialog: false,\n    }\n  },\n  computed: {\n    isTeacher() {\n      return this.$store.getters['user/isTeacher']\n    },\n    isStudent() {\n      return this.$store.getters['user/isStudent']\n    },\n    allowedRate() {\n      return (\n        this.isStudent && this.item.isSkippedFeedback && this.item.isFinished\n      )\n    },\n  },\n  methods: {\n    downloadPdfClickHandler() {\n      this.$store.dispatch('lesson/generatePdf', this.item.lessonId)\n    },\n    finishClickHandler(scope) {\n      this.$store\n        .dispatch('lesson/finishLesson', this.item.lessonId)\n        .then(() => {\n          this.$store.commit('lesson/UPDATE_LESSON', {\n            lessonId: this.item.lessonId,\n            lessonStatus: 2,\n          })\n          this.$store.dispatch('snackbar/success', {\n            successMessage: 'class_successfully_finished',\n          })\n        })\n        .catch((e) => {\n          this.$store.dispatch('loadingStop')\n          this.$store.dispatch('snackbar/error')\n\n          console.info(e)\n        })\n        .finally(() => this.closeDialog(scope))\n    },\n    showFinishDialog(scope) {\n      this.dialogType = 'finishDialog'\n\n      scope.showDialog()\n    },\n    closeDialog(scope) {\n      scope.closeDialog()\n\n      setTimeout(() => {\n        this.dialogType = null\n      }, 500)\n    },\n    rateLesson() {\n      this.$store\n        .dispatch('lesson/getFeedbackItem', this.item.lessonId)\n        .then((data) => {\n          this.feedbackLessonData = data\n          this.isShownEvaluationDialog = true\n        })\n    },\n    closeEvaluationDialog() {\n      this.isShownEvaluationDialog = false\n      this.feedbackLessonData = {}\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PastLesson.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PastLesson.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./PastLesson.vue?vue&type=template&id=5eda8af2&\"\nimport script from \"./PastLesson.vue?vue&type=script&lang=js&\"\nexport * from \"./PastLesson.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"6e07c745\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VBtn,VImg})\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('lesson-item',{attrs:{\"item\":_vm.item,\"user-statuses\":_vm.userStatuses},scopedSlots:_vm._u([{key:\"date\",fn:function(){return [_c('div',{staticClass:\"date\"},[_vm._v(\"\\n      \"+_vm._s(_vm.item.availableLessons)+\" \"+_vm._s(_vm.$t('of'))+\" \"+_vm._s(_vm.item.countLessons)+\"\\n    \")]),_vm._v(\" \"),_c('div',{staticClass:\"remaining\",domProps:{\"innerHTML\":_vm._s(_vm.$t('remaining_lessons'))}})]},proxy:true},{key:\"lessonInfo\",fn:function(){return [_c('div',[_c('span',[_vm._v(_vm._s(_vm.$t('purchase_date'))+\":\")]),_vm._v(\" \"),_c('span',{staticClass:\"text--gradient font-weight-bold text-no-wrap\"},[_vm._v(\"\\n        \"+_vm._s(_vm.$dayjs(_vm.item.createdDate).tz(_vm.timeZone).format('LL'))+\"\\n      \")])]),_vm._v(\" \"),(_vm.isTeacher)?_c('div',[_c('span',[_vm._v(_vm._s(_vm.$t('price_per_lesson'))+\":\")]),_vm._v(\" \"),_c('span',{staticClass:\"text--gradient font-weight-bold text-no-wrap\"},[_vm._v(\"\\n        \"+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.getPrice(_vm.item.priceForOneLesson))+\"\\n      \")])]):_vm._e()]},proxy:true},{key:\"lessonActions\",fn:function(){return [(_vm.isStudent && !_vm.userIsDeleted)?_c('v-btn',{staticClass:\"font-weight-medium ml-1 mb-1\",attrs:{\"color\":\"primary\"},on:{\"click\":_vm.schedule}},[_c('svg',{staticClass:\"mr-1\",attrs:{\"width\":\"20\",\"height\":\"20\",\"viewBox\":\"0 0 20 20\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#calendar\")}})]),_vm._v(\"\\n      \"+_vm._s(_vm.$t('schedule'))+\"\\n    \")]):_vm._e()]},proxy:true}])},[_vm._v(\" \"),_vm._v(\" \"),_vm._v(\" \"),_c('time-picker-dialog',{attrs:{\"is-shown-time-picker-dialog\":_vm.isShownTimePickerDialog,\"username\":_vm.item.teacherUsername,\"language\":_vm.item.language,\"course\":_vm.item.course,\"lesson-length\":_vm.item.lessonLength,\"count-lessons\":_vm.item.availableLessons,\"purchase-id\":_vm.item.purchaseId,\"current-time\":_vm.currentTime},on:{\"update-current-time\":function($event){_vm.currentTime = $event},\"close-dialog\":_vm.closeTimePickerDialog}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getPrice } from '~/helpers'\n\nimport LessonItem from '~/components/user-lessons/LessonItem'\nimport TimePickerDialog from '~/components/user-lessons/TimePickerDialog'\n\nexport default {\n  name: 'UnscheduledLesson',\n  components: { LessonItem, TimePickerDialog },\n  props: {\n    item: {\n      type: Object,\n      required: true,\n    },\n    userStatuses: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  data() {\n    return {\n      getPrice,\n      currentTime: this.$dayjs(),\n      isShownTimePickerDialog: false,\n    }\n  },\n  computed: {\n    isTeacher() {\n      return this.$store.getters['user/isTeacher']\n    },\n    isStudent() {\n      return this.$store.getters['user/isStudent']\n    },\n    currentCurrencySymbol() {\n      return this.$store.getters['currency/currentCurrencySymbol']\n    },\n    userIsDeleted() {\n      return this.item.userIsDeleted\n    },\n    timeZone() {\n      return this.$store.getters['user/timeZone']\n    },\n  },\n  methods: {\n    closeTimePickerDialog() {\n      this.isShownTimePickerDialog = false\n\n      this.$store.commit('teacher_profile/RESET_SELECTED_SLOTS')\n    },\n    schedule() {\n      this.$store\n        .dispatch('teacher_profile/getSlots', {\n          slug: this.item.teacherUsername,\n          date: this.currentTime.day(1),\n        })\n        .then(() => {\n          this.isShownTimePickerDialog = true\n        })\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UnscheduledLesson.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./UnscheduledLesson.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./UnscheduledLesson.vue?vue&type=template&id=20ef4ce4&\"\nimport script from \"./UnscheduledLesson.vue?vue&type=script&lang=js&\"\nexport * from \"./UnscheduledLesson.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"ddda4bfe\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\ninstallComponents(component, {VBtn})\n", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonsPage.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".user-lessons{--sidebar-width:345px;padding-bottom:140px}@media only screen and (max-width:1439px){.user-lessons{--sidebar-width:325px}}@media only screen and (max-width:1215px){.user-lessons{--sidebar-width:298px}}@media only screen and (max-width:991px){.user-lessons{padding-bottom:60px}}.user-lessons-wrap{max-width:1360px;padding-bottom:25px}@media only screen and (min-width:1216px){.user-lessons-header{display:flex;align-items:center;justify-content:space-between}}@media only screen and (max-width:1215px){.user-lessons-header{flex-wrap:wrap}.user-lessons-header>div{width:100%}}.user-lessons-title{position:relative}@media only screen and (min-width:992px){.user-lessons-title{margin-right:24px}}@media only screen and (max-width:1215px){.user-lessons-title{margin-right:0}}.user-lessons-title h1{white-space:nowrap;font-size:24px;line-height:1.333}@media only screen and (max-width:479px){.user-lessons-title h1{font-size:20px}}.user-lessons-credits{position:absolute;left:0;bottom:-20px;font-size:13px;color:var(--v-dark-lighten3)}.user-lessons-controls{justify-content:space-between;align-items:center;flex-grow:1}@media only screen and (max-width:1215px){.user-lessons-controls{margin-top:18px}}@media only screen and (min-width:1216px){.user-lessons-controls{max-width:970px}}@media only screen and (max-width:991px){.user-lessons-controls{max-width:100%;flex-wrap:wrap}}.user-lessons-search{width:100%}@media only screen and (min-width:992px){.user-lessons-search{min-width:240px;flex-basis:380px}}.user-lessons-nav{min-width:508px;margin-left:18px;padding:4px;background-color:#fff;box-shadow:0 4px 14px rgba(217,225,236,.47);border-radius:16px}@media only screen and (max-width:1439px){.user-lessons-nav{min-width:455px}}@media only screen and (max-width:991px){.user-lessons-nav{width:100%;min-width:auto;margin:12px 0 0}}.user-lessons-nav>a:not(:last-child){margin-right:4px}.user-lessons-nav .v-btn.nav-btn{flex-grow:1;border-radius:14px;background-color:transparent!important}@media only screen and (max-width:991px){.user-lessons-nav .v-btn.nav-btn{width:33.3333%;min-width:70px!important;text-align:center}}@media only screen and (max-width:639px){.user-lessons-nav .v-btn.nav-btn{font-size:13px!important;font-weight:400!important}}.user-lessons-nav .v-btn:before{background-color:transparent}.user-lessons-nav .v-btn:not(.v-btn--active){color:var(--v-greyDark-base)}.user-lessons-nav .v-btn--active:before,.user-lessons-nav .v-btn--active:hover:before{background:linear-gradient(126.15deg,rgba(128,182,34,.16),rgba(60,135,248,.16) 102.93%);opacity:1}@media only screen and (min-width:768px){.user-lessons-body{display:flex}}.user-lessons-content{display:flex;flex-direction:column;justify-content:space-between;width:calc(100% - var(--sidebar-width))}@media only screen and (max-width:991px){.user-lessons-content{width:100%}}.user-lessons-content h1{font-size:32px}.user-lessons-content .lessons-list>div:not(:last-child){margin-bottom:20px}@media only screen and (max-width:767px){.user-lessons-content .lessons-list>div:not(:last-child){margin-bottom:10px}}.user-lessons-sidebar{width:var(--sidebar-width)}.user-lessons-sidebar-helper{padding-left:20px}@media only screen and (max-width:1215px){.user-lessons-sidebar-helper{padding-left:16px}}.user-lessons-sidebar-helper>div:not(:last-child){margin-bottom:10px}@media only screen and (max-width:1215px){.user-lessons--student .user-lessons-title{margin-bottom:32px}}.user-lessons .lessons-list-empty .steps{border-bottom:none!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-col',{staticClass:\"col-12 px-0\"},[_c('div',{class:[\n      'user-lessons',\n      (\"user-lessons--\" + (_vm.isTeacher ? 'teacher' : 'student')) ]},[_c('v-container',{staticClass:\"pa-0\",attrs:{\"fluid\":\"\"}},[_c('v-row',{attrs:{\"no-gutters\":\"\"}},[_c('v-col',{staticClass:\"col-12\"},[_c('div',{staticClass:\"user-lessons-wrap mx-auto\"},[_c('div',{staticClass:\"user-lessons-header mb-2 mb-md-4 mb-lg-5\"},[_c('div',{staticClass:\"user-lessons-title\"},[_c('h1',{staticClass:\"font-weight-medium\"},[_vm._v(_vm._s(_vm.$t('my_lessons'))+\" 📚\")]),_vm._v(\" \"),(_vm.isStudent && _vm.userCredit)?_c('div',{staticClass:\"user-lessons-credits text-no-wrap\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('langu_credit'))+\": \"+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.getPrice(_vm.userCredit))+\"\\n                \")]):_vm._e()]),_vm._v(\" \"),_c('div',{staticClass:\"user-lessons-controls d-flex\"},[_c('div',{staticClass:\"user-lessons-search\"},[_c('search-input',{staticClass:\"search-input--inner-border\",attrs:{\"placeholder\":_vm.isTeacher ? 'search_for_student' : 'search_for_teacher'},on:{\"submit\":_vm.searchSubmitHandler},model:{value:(_vm.searchQuery),callback:function ($$v) {_vm.searchQuery=(typeof $$v === 'string'? $$v.trim(): $$v)},expression:\"searchQuery\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"user-lessons-nav d-flex\"},[_c('v-btn',{staticClass:\"nav-btn font-weight-medium\",attrs:{\"to\":_vm.localePath({ path: '/user/lessons' }),\"height\":\"48\"}},[_vm._v(\"\\n                    \"+_vm._s(_vm.$t('upcoming_lessons'))+\"\\n                  \")]),_vm._v(\" \"),_c('v-btn',{staticClass:\"nav-btn font-weight-medium\",attrs:{\"to\":_vm.localePath({ path: '/user/past-lessons' }),\"height\":\"48\"}},[_vm._v(\"\\n                    \"+_vm._s(_vm.$t('past_lessons'))+\"\\n                  \")]),_vm._v(\" \"),_c('v-btn',{staticClass:\"nav-btn font-weight-medium\",attrs:{\"to\":_vm.localePath({ path: '/user/unscheduled-lessons' }),\"height\":\"48\"}},[_vm._v(\"\\n                    \"+_vm._s(_vm.$t('unscheduled_lessons'))+\" (\"+_vm._s(_vm.totalNumberUnscheduledLessons)+\")\\n                  \")])],1)])]),_vm._v(\" \"),_c('div',{staticClass:\"user-lessons-body\"},[_c('div',{staticClass:\"user-lessons-content\"},[_c('div',[_c('div',{staticClass:\"user-lessons-filters d-flex\"},[(_vm.$route.query.search)?_c('l-chip',{staticClass:\"mb-1 mr-1\",attrs:{\"clickable\":true,\"label\":_vm.$route.query.search,\"light\":\"\"},on:{\"click:close\":_vm.resetSearch}}):_vm._e(),_vm._v(\" \"),(_vm.selectedDate)?_c('l-chip',{staticClass:\"mb-1 mr-1\",attrs:{\"label\":_vm.$dayjs(_vm.selectedDate, 'YYYY-MM-DD').format('D MMMM'),\"light\":\"\"},on:{\"click:close\":_vm.resetSelectedDate}}):_vm._e()],1),_vm._v(\" \"),(_vm.lessons.length)?[_c('div',{staticClass:\"lessons-list\"},[_vm._l((_vm.lessons),function(lesson){return [_c(_vm.lessonComponents[lesson.type],_vm._b({key:lesson.id,tag:\"component\"},'component',{ item: lesson, userStatuses: _vm.userStatuses },false))]})],2)]:[(_vm.isStudent)?_c('div',{staticClass:\"lessons-list-empty\"},[_c('steps',{attrs:{\"active-item-id\":0,\"item-link\":{ id: 1, path: '/teacher-listing' }}})],1):_vm._e()]],2),_vm._v(\" \"),(_vm.totalPages > 1)?_c('div',{staticClass:\"mt-3 mt-md-5 text-center\"},[_c('pagination',{attrs:{\"current-page\":_vm.page,\"total-pages\":_vm.totalPages,\"route\":_vm.route}})],1):_vm._e()]),_vm._v(\" \"),(_vm.$vuetify.breakpoint.mdAndUp)?_c('aside',{staticClass:\"user-lessons-sidebar d-none d-md-block\"},[_c('div',{staticClass:\"user-lessons-sidebar-helper\"},_vm._l((4),function(i){return _c('calendar',{key:i,attrs:{\"type\":_vm.type,\"items\":_vm.calendarItems,\"current-date\":_vm.$dayjs().add(i - 1, 'month')},on:{\"click-date\":_vm.clickDate}})}),1)]):_vm._e()])])])],1)],1)],1)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { hashUserData } from '@/utils/hash'\nimport { getPrice } from '~/helpers'\n\nimport Avatars from '~/mixins/Avatars'\nimport StatusOnline from '~/mixins/StatusOnline'\nimport SearchInput from '~/components/form/SearchInput'\nimport Calendar from '~/components/Calendar'\nimport UpcomingLesson from '~/components/user-lessons/UpcomingLesson'\nimport PastLesson from '~/components/user-lessons/PastLesson'\nimport UnscheduledLesson from '~/components/user-lessons/UnscheduledLesson'\nimport Steps from '~/components/Steps'\nimport Pagination from '~/components/Pagination'\nimport LChip from '~/components/LChip'\n\nexport default {\n  name: 'LessonsPage',\n  components: {\n    SearchInput,\n    Calendar,\n    Steps,\n    Pagination,\n    LChip,\n  },\n  mixins: [Avatars, StatusOnline],\n  props: {\n    page: {\n      type: Number,\n      default: 1,\n    },\n    route: {\n      type: String,\n      required: true,\n    },\n    type: {\n      type: String,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      getPrice,\n      lessonComponents: {\n        upcoming: UpcomingLesson,\n        past: PastLesson,\n        unscheduled: UnscheduledLesson,\n      },\n      searchQuery: '',\n      now: this.$dayjs(),\n      selectedDate: null,\n    }\n  },\n  computed: {\n    isTeacher() {\n      return this.$store.getters['user/isTeacher']\n    },\n    isStudent() {\n      return this.$store.getters['user/isStudent']\n    },\n    lessons() {\n      return this.$store.getters['lesson/lessons']\n    },\n    calendarItems() {\n      return this.$store.state.lesson.calendarItems\n    },\n    totalPages() {\n      return Math.ceil(\n        this.$store.state.lesson.totalQuantity / process.env.NUXT_ENV_PER_PAGE\n      )\n    },\n    totalNumberUnscheduledLessons() {\n      return this.$store.getters['user/totalNumberUnscheduledLessons']\n    },\n    currentCurrencySymbol() {\n      return this.$store.getters['currency/currentCurrencySymbol']\n    },\n    userCredit() {\n      return this.$store.getters['user/userCredit']\n    },\n  },\n  watch: {\n    selectedDate() {\n      this.setArrStatusId()\n    },\n    '$route.params.search': {\n      handler() {\n        this.setArrStatusId()\n      },\n      deep: true,\n    },\n  },\n  beforeMount() {\n    this.searchQuery = this.$route.query?.search ?? ''\n\n    // Check for paid trial event data\n    if (localStorage.getItem('event_data') !== null) {\n      try {\n        const eventData = JSON.parse(localStorage.getItem('event_data'))\n        if (eventData.event === 'purchase_paid_trial') {\n          // Get user data\n          const tidioData = this.$store.state.user.tidioData || {}\n          const userData = this.$store.state.user.item || {}\n          const userEmail = tidioData.email || ''\n          const userName = `${userData.firstName || ''} ${\n            userData.lastName || ''\n          }`.trim()\n\n          // Add hashed user data to the items array\n          if (\n            eventData.ecommerce &&\n            eventData.ecommerce.items &&\n            eventData.ecommerce.items.length > 0\n          ) {\n            eventData.ecommerce.items.forEach((item) => {\n              if (!item.user_name) {\n                item.user_name = hashUserData(userName)\n              }\n              if (!item.email_id) {\n                item.email_id = hashUserData(userEmail)\n              }\n            })\n          }\n\n          // Ensure dataLayer is initialized\n          window.dataLayer = window.dataLayer || []\n\n          // Push event to dataLayer\n          window.dataLayer.push({\n            ecommerce: null,\n          })\n          window.dataLayer.push(eventData)\n\n          // Remove event data from localStorage\n          localStorage.removeItem('event_data')\n        }\n      } catch (error) {\n        console.log(error)\n      }\n    }\n\n    this.setArrStatusId()\n    this.refreshStatusOnline()\n  },\n  methods: {\n    setArrStatusId() {\n      const prop = this.isTeacher ? 'studentId' : 'teacherId'\n\n      this.arrStatusId = this.lessons.map((item) => item[prop])\n    },\n    async searchSubmitHandler() {\n      if (this.selectedDate) {\n        await this.resetSelectedDate()\n      }\n\n      await this.$router.push({\n        name: this.$route.name,\n        params: { page: '1' },\n        query: this.searchQuery ? { search: this.searchQuery } : {},\n      })\n    },\n    async clickDate(date) {\n      if (this.searchQuery.length) {\n        await this.resetSearch()\n      }\n\n      // if (this.type === 'past' || this.type === 'upcoming') {\n      const data = { date }\n\n      // if (this.type === 'past') data.type = 'past'\n      if (this.type === 'upcoming') data.type = 'upcoming'\n\n      this.$store\n        .dispatch('lesson/getLessonsByDate', data)\n        .then(() => (this.selectedDate = date))\n      // }\n    },\n    resetSelectedDate() {\n      return new Promise((resolve) => {\n        const data = {\n          page: this.page,\n          perPage: process.env.NUXT_ENV_PER_PAGE,\n          type: this.type,\n        }\n\n        this.$store.dispatch('lesson/getUpcomingLessons', data).then(() => {\n          this.selectedDate = null\n\n          resolve()\n        })\n      })\n    },\n    resetSearch() {\n      return new Promise((resolve) => {\n        this.searchQuery = ''\n\n        this.$router.push({\n          name: this.$route.name,\n          params: { page: '1' },\n          query: {},\n        })\n\n        setTimeout(() => {\n          resolve()\n        })\n      })\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonsPage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonsPage.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./LessonsPage.vue?vue&type=template&id=1a996957&\"\nimport script from \"./LessonsPage.vue?vue&type=script&lang=js&\"\nexport * from \"./LessonsPage.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./LessonsPage.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"3bb66f9f\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {LChip: require('D:/languworks/langu-frontend/components/LChip.vue').default,Steps: require('D:/languworks/langu-frontend/components/Steps.vue').default,Pagination: require('D:/languworks/langu-frontend/components/Pagination.vue').default,Calendar: require('D:/languworks/langu-frontend/components/Calendar.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VContainer } from 'vuetify/lib/components/VGrid';\nimport { VRow } from 'vuetify/lib/components/VGrid';\ninstallComponents(component, {VBtn,VCol,VContainer,VRow})\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('lessons-page',{attrs:{\"route\":\"/user/lessons\",\"type\":_vm.type}})}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n\nimport { hashUserData } from '@/utils/hash'\nimport LessonsPage from '~/components/user-lessons/LessonsPage'\n\nexport default {\n  name: 'UpcomingLessons',\n  components: { LessonsPage },\n  middleware: ['authenticated', 'confirmationPageAllowed'],\n  async asyncData({ store, query }) {\n    const type = 'upcoming'\n    const searchQuery = query?.search\n\n    await Promise.all([\n      store.dispatch('lesson/getUpcomingLessons', {\n        page: 1,\n        perPage: process.env.NUXT_ENV_PER_PAGE,\n        type,\n        searchQuery,\n      }),\n      store.dispatch('lesson/getCalendarItems'),\n    ])\n\n    return { type }\n  },\n  head() {\n    return {\n      title: this.$t('user_upcoming_lessons_page.seo_title'),\n      meta: [\n        {\n          hid: 'description',\n          name: 'description',\n          content: this.$t('user_upcoming_lessons_page.seo_description'),\n        },\n        {\n          hid: 'og:title',\n          name: 'og:title',\n          property: 'og:title',\n          content: this.$t('user_upcoming_lessons_page.seo_title'),\n        },\n        {\n          property: 'og:description',\n          content: this.$t('user_upcoming_lessons_page.seo_description'),\n        },\n      ],\n      bodyAttrs: {\n        class: `${this.locale} user-lessons-page user-upcoming-lessons-page`,\n      },\n    }\n  },\n  watchQuery: true,\n  async beforeMount() {\n    this.$cookiz.remove('confirmation_page_allowed', {\n      domain: process.env.NUXT_ENV_COOKIE_DOMAIN,\n      path: '/',\n    })\n\n    // Ensure dataLayer is properly initialized\n    window.dataLayer = window.dataLayer || []\n\n    if (localStorage.getItem('event_data') !== null) {\n      try {\n        const eventData = JSON.parse(localStorage.getItem('event_data'))\n        // Try to get user data from the API for the most up-to-date information\n        let userData = null\n        try {\n          userData = await this.$store.dispatch('payments/fetchUserData')\n        } catch (error) {\n          console.error('Error fetching user data from API:', error)\n        }\n\n        // If API call fails, fall back to store state\n        if (!userData || !userData.email) {\n          userData = this.$store.state.user.item || {}\n        }\n\n        const tidioData = this.$store.state.user.tidioData || {}\n        const userEmail = tidioData.email || ''\n        const userName = `${userData.firstName || ''} ${\n          userData.lastName || ''\n        }`.trim()\n        const hashedEmail = hashUserData(userEmail)\n        const hashedName = hashUserData(userName)\n        // Add hashed user data to the items array\n        if (\n          eventData.ecommerce &&\n          eventData.ecommerce.items &&\n          eventData.ecommerce.items.length > 0\n        ) {\n          eventData.ecommerce.items.forEach((item) => {\n            if (!item.user_name) {\n              item.user_name = hashedName\n            }\n            if (!item.email_id) {\n              item.email_id = hashedEmail\n            }\n          })\n        }\n\n        // Push event to dataLayer with a slight delay to ensure GTM is ready\n        setTimeout(() => {\n          // First push a clear ecommerce object to prevent data leakage\n          window.dataLayer.push({\n            ecommerce: null,\n          })\n\n          // Then push the actual event\n          window.dataLayer.push(eventData)\n        }, 500)\n\n        // Remove event data from localStorage\n        localStorage.removeItem('event_data')\n      } catch (error) {\n        // eslint-disable-next-line no-console\n        console.error('Error pushing event to dataLayer:', error)\n      }\n    }\n  },\n}\n", "import mod from \"-!../../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=09fa69a0&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"e1da0592\"\n  \n)\n\nexport default component.exports", "import VBtn from './VBtn'\n\nexport { VBtn }\nexport default VBtn\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Editor.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"a98bb618\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[\n    'user-status',\n    (\"user-status--\" + _vm.status),\n    { 'user-status--large': _vm.large } ]},[])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'UserStatus',\n  props: {\n    userId: {\n      type: Number,\n      default: 0,\n    },\n    large: {\n      type: Boolean,\n      default: false,\n    },\n    userStatuses: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  computed: {\n    status() {\n      let status = 'offline'\n\n      if (\n        Object.prototype.hasOwnProperty.call(\n          this.userStatuses,\n          this.userId?.toString()\n        )\n      ) {\n        status = this.userStatuses[this.userId]\n      }\n\n      return status\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserStatus.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserStatus.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./UserStatus.vue?vue&type=template&id=652352c7&scoped=true&\"\nimport script from \"./UserStatus.vue?vue&type=script&lang=js&\"\nexport * from \"./UserStatus.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./UserStatus.vue?vue&type=style&index=0&id=652352c7&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"652352c7\",\n  \"4c070a35\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Pagination.vue?vue&type=style&index=0&id=18a8bda5&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"ef3a6480\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('nav',{staticClass:\"pagination\"},[_vm._ssrNode(\"<ul class=\\\"pagination-list d-flex justify-center align-center\\\" data-v-18a8bda5>\",\"</ul>\",[_vm._ssrNode(\"<li\"+(_vm._ssrClass(null,['pagination-item pagination-item-prev']))+\" data-v-18a8bda5><div class=\\\"icon next-prev-icon\\\" data-v-18a8bda5><svg width=\\\"17\\\" height=\\\"12\\\" viewBox=\\\"0 0 17 12\\\" data-v-18a8bda5><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#arrow-prev\")))+\" data-v-18a8bda5></use></svg></div> <span class=\\\"d-none d-sm-inline ml-2\\\" data-v-18a8bda5>\"+_vm._ssrEscape(_vm._s(_vm.$t('previous')))+\"</span></li> \"),_vm._l((_vm.pages),function(page,index){return _vm._ssrNode(\"<li class=\\\"pagination-item\\\" data-v-18a8bda5>\",\"</li>\",[(page !== 0)?[_c('nuxt-link',{class:{ current: _vm.currentPage === page },attrs:{\"to\":_vm.getUrl(page)}},[_vm._v(\"\\n          \"+_vm._s(page)+\"\\n        \")])]:_vm._ssrNode(\"<span class=\\\"dots\\\" data-v-18a8bda5>...</span>\")],2)}),_vm._ssrNode(\" <li\"+(_vm._ssrClass(null,['pagination-item pagination-item-next']))+\" data-v-18a8bda5><span class=\\\"d-none d-sm-inline mr-2\\\" data-v-18a8bda5>\"+_vm._ssrEscape(_vm._s(_vm.$t('next')))+\"</span> <div class=\\\"icon\\\" data-v-18a8bda5><svg width=\\\"17\\\" height=\\\"12\\\" viewBox=\\\"0 0 17 12\\\" data-v-18a8bda5><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#arrow-next\")))+\" data-v-18a8bda5></use></svg></div></li>\")],2)])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'Pagination',\n  props: {\n    currentPage: {\n      type: Number,\n      required: true,\n    },\n    totalPages: {\n      type: Number,\n      required: true,\n    },\n    route: {\n      type: String,\n      required: true,\n    },\n    params: {\n      type: String,\n      default: '',\n    },\n  },\n  data() {\n    return {\n      key: 1,\n    }\n  },\n  computed: {\n    isFirstCurrentPage() {\n      return this.currentPage <= 1\n    },\n    isLastCurrentPage() {\n      return this.currentPage >= this.totalPages\n    },\n    pages() {\n      const pages = []\n\n      for (let i = 1; i <= this.totalPages; i++) {\n        pages.push(i)\n      }\n\n      let pagination = pages.slice()\n\n      if (this.totalPages > 6) {\n        let left = []\n        let right = []\n        let center = []\n\n        if (this.currentPage < 3 || this.currentPage > this.totalPages - 3) {\n          left = pages.slice(0, 3)\n          right = pages.slice(-3)\n\n          pagination = [...left, 0, ...right]\n        }\n\n        if (this.currentPage === 3) {\n          left = pages.slice(0, 5)\n          right = pages.slice(-1)\n\n          pagination = [...left, 0, ...right]\n        }\n\n        if (this.currentPage > 3 && this.currentPage < this.totalPages - 2) {\n          left = pages.slice(0, 1)\n          right = pages.slice(-1)\n          center = pages.slice(this.currentPage - 2, this.currentPage + 1)\n\n          pagination = [...left, 0, ...center, 0, ...right]\n        }\n\n        if (this.currentPage === this.totalPages - 2) {\n          left = pages.slice(0, 1)\n          right = pages.slice(-5)\n\n          pagination = [...left, 0, ...right]\n        }\n      }\n\n      return pagination\n    },\n    queryStr() {\n      const { query } = this.$route\n      const keys = Object.keys(query)\n\n      let str = ''\n\n      if (keys.length) {\n        str += '?'\n\n        for (let i = 0; i < keys.length; i++) {\n          str += `${keys[i]}=${query[keys[i]]}`\n\n          if (i < keys.length - 1) {\n            str += '&'\n          }\n        }\n      }\n\n      return str\n    },\n  },\n  watch: {\n    currentPage() {\n      this.key++\n    },\n  },\n  methods: {\n    getUrl(page) {\n      let url = this.route\n\n      if (page > 1 || this.params.length) {\n        url += `/${page}${this.params.length ? '/' + this.params : ''}`\n      }\n\n      if (this.queryStr.length) {\n        url += this.queryStr\n      }\n\n      return url\n    },\n    prevPageClickHandler() {\n      if (!this.isFirstCurrentPage) {\n        this.$router.push({ path: this.getUrl(this.currentPage - 1) })\n      }\n    },\n    nextPageClickHandler() {\n      if (!this.isLastCurrentPage) {\n        this.$router.push({ path: this.getUrl(this.currentPage + 1) })\n      }\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Pagination.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Pagination.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Pagination.vue?vue&type=template&id=18a8bda5&scoped=true&\"\nimport script from \"./Pagination.vue?vue&type=script&lang=js&\"\nexport * from \"./Pagination.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./Pagination.vue?vue&type=style&index=0&id=18a8bda5&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"18a8bda5\",\n  \"18cd97b2\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SearchInput.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"637a1dfc\", content, true, context)\n};", "\nexport default {\n  methods: {\n    getSrcAvatar(images, property, defaultImage = 'avatar.png') {\n      return images?.[property]\n        ? images[property]\n        : require(`~/assets/images/homepage/${defaultImage}`)\n    },\n    getSrcSetAvatar(images, property1, property2) {\n      return images?.[property1] && images?.[property2]\n        ? `\n            ${images[property1]} 1x,\n            ${images[property2]} 2x,\n          `\n        : ''\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Avatars.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Avatars.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./Avatars.vue?vue&type=script&lang=js&\"\nexport * from \"./Avatars.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"0af9ff4e\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserStatus.vue?vue&type=style&index=0&id=652352c7&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"006007e9\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Editor.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".text-editor{position:relative}.text-editor-buttons{position:absolute;right:18px;top:8px;z-index:2}.text-editor-buttons button{display:inline-flex;justify-content:center;align-items:center;width:24px;height:24px;margin-left:8px;border-radius:2px;border:1px solid transparent}.text-editor-buttons button.is-active{background-color:var(--v-greyBg-base);border-color:var(--v-greyLight-base)}.text-editor .ProseMirror{min-height:280px;margin-bottom:4px;padding:40px 12px 12px;border:1px solid #bebebe;font-size:13px;border-radius:16px;line-height:1.23}.text-editor .ProseMirror>*{position:relative}.text-editor .ProseMirror p{margin-bottom:0}.text-editor .ProseMirror ul{padding-left:28px}.text-editor .ProseMirror ul>li p{margin-bottom:0}.text-editor .ProseMirror strong{font-weight:700!important}.text-editor .ProseMirror.focus-visible,.text-editor .ProseMirror:focus,.text-editor .ProseMirror:focus-visible{outline:none!important}.text-editor .ProseMirror-focused:before{content:\\\"\\\";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:16px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}.text-editor .v-text-field__details{padding:0 14px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./MessageDialog.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"0f94d031\", content, true, context)\n};", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TimePickerItem.vue?vue&type=style&index=0&id=7467ec82&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"13082346\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"text-editor\"},[_vm._ssrNode(((_vm.editor)?(\"<div class=\\\"text-editor-buttons\\\"><button\"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bold') }))+\"><svg width=\\\"16\\\" height=\\\"16\\\" viewBox=\\\"0 0 16 16\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#editor-bold-icon\")))+\"></use></svg></button> <button\"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bulletList') }))+\"><svg width=\\\"16\\\" height=\\\"16\\\" viewBox=\\\"0 0 16 16\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#editor-list-icon\")))+\"></use></svg></button></div>\"):\"<!---->\")+\" \"),_c('editor-content',{attrs:{\"editor\":_vm.editor}}),_vm._ssrNode(\" \"+((_vm.counter)?(\"<div class=\\\"v-text-field__details\\\"><div class=\\\"v-messages theme--light\\\"><div class=\\\"v-messages__wrapper\\\"></div></div> <div\"+(_vm._ssrClass(null,[\n        'v-counter theme--light',\n        { 'error--text': !_vm.isValid && _vm.isDirty } ]))+\">\"+_vm._ssrEscape(\"\\n      \"+_vm._s(_vm.text.length)+\" / \"+_vm._s(_vm.limit)+\"\\n    \")+\"</div></div>\"):\"<!---->\"))],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { Editor, EditorContent } from '@tiptap/vue-2'\nimport StarterKit from '@tiptap/starter-kit'\nimport CharacterCount from '@tiptap/extension-character-count'\nimport Link from '@tiptap/extension-link'\n\nexport default {\n  name: 'Editor',\n  components: {\n    EditorContent,\n  },\n  props: {\n    value: {\n      type: String,\n      required: true,\n    },\n    counter: {\n      type: Boolean,\n      default: false,\n    },\n    autoLink: {\n      type: Boolean,\n      default: false,\n    },\n    limit: {\n      type: Number,\n      default: null,\n    },\n  },\n  data() {\n    return {\n      editor: null,\n      text: '',\n      isValid: true,\n      editorEl: null,\n      keysPressed: {},\n      isDirty: false,\n    }\n  },\n  watch: {\n    value(value) {\n      const isSame = this.editor.getHTML() === value\n\n      if (isSame) {\n        return\n      }\n\n      this.editor.commands.setContent(value, false)\n    },\n  },\n  mounted() {\n    this.editor = new Editor({\n      content: this.value,\n      extensions: [\n        StarterKit,\n        CharacterCount.configure({\n          limit: this.limit,\n        }),\n        Link.configure({\n          autolink: true,\n        }),\n      ],\n    })\n\n    this.editor.on('create', ({ editor }) => {\n      this.text = editor.getText()\n\n      this.$nextTick(() => {\n        this.editorEl = document.getElementsByClassName('ProseMirror')[0]\n\n        if (this.editorEl) {\n          this.editorEl.addEventListener('keydown', this.keydownHandler)\n          this.editorEl.addEventListener('keyup', this.keyupHandler)\n        }\n      })\n\n      this.validation()\n    })\n\n    this.editor.on('update', ({ editor }) => {\n      this.isDirty = true\n      this.text = editor.getText()\n\n      this.validation()\n      this.$emit('update', this.text ? editor.getHTML() : '')\n    })\n  },\n  beforeDestroy() {\n    if (this.editorEl) {\n      this.editorEl.removeEventListener('keydown', this.keydownHandler)\n      this.editorEl.removeEventListener('keyup', this.keyupHandler)\n    }\n\n    this.editor.destroy()\n  },\n  methods: {\n    keydownHandler(e) {\n      this.keysPressed[e.keyCode] = true\n\n      if (\n        (e.ctrlKey ||\n          this.keysPressed[17] ||\n          this.keysPressed[91] ||\n          this.keysPressed[93] ||\n          this.keysPressed[224]) &&\n        this.keysPressed[13]\n      ) {\n        e.preventDefault()\n\n        this.$emit('submit')\n\n        this.keysPressed = {}\n      } else if (e.keyCode === 13 && !e.shiftKey) {\n        e.preventDefault()\n        this.editor.commands.enter()\n      }\n    },\n    keyupHandler(e) {\n      delete this.keysPressed[e.keyCode]\n    },\n    validation() {\n      const strLength = this.text.trim().length\n\n      this.isValid = !!strLength\n\n      if (!!strLength && this.limit) {\n        this.isValid = strLength <= this.limit\n      }\n\n      this.$emit('validation', this.isValid)\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Editor.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Editor.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Editor.vue?vue&type=template&id=23b137ee&\"\nimport script from \"./Editor.vue?vue&type=script&lang=js&\"\nexport * from \"./Editor.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./Editor.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"0bb70d5d\"\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-form',{on:{\"submit\":function($event){$event.preventDefault();return _vm.submit.apply(null, arguments)}}},[_c('text-input',{class:['search-input', { 'search-input--small': _vm.small }],attrs:{\"value\":_vm.value,\"type-class\":\"border-gradient\",\"hide-details\":\"\",\"disabled\":_vm.disabled,\"placeholder\":_vm.$t(_vm.placeholder)},on:{\"input\":function($event){return _vm.$emit('input', $event)}},scopedSlots:_vm._u([{key:\"append\",fn:function(){return [_c('div',{staticStyle:{\"margin-top\":\"6px\",\"cursor\":\"pointer\"},on:{\"click\":_vm.submit}},[_c('v-img',{attrs:{\"src\":require('~/assets/images/search-icon.svg')}})],1)]},proxy:true}])})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport TextInput from '~/components/form/TextInput'\n\nexport default {\n  name: 'SearchInput',\n  components: { TextInput },\n  props: {\n    value: {\n      type: String,\n      default: '',\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    placeholder: {\n      type: String,\n      required: true,\n    },\n    small: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  methods: {\n    submit() {\n      this.$emit('submit')\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SearchInput.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SearchInput.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./SearchInput.vue?vue&type=template&id=8bfec74e&\"\nimport script from \"./SearchInput.vue?vue&type=script&lang=js&\"\nexport * from \"./SearchInput.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./SearchInput.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"86c5c87c\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VForm } from 'vuetify/lib/components/VForm';\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VForm,VImg})\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"lesson d-sm-flex\"},[_vm._ssrNode(\"<div class=\\\"lesson-date d-flex text-center\\\">\",\"</div>\",[_vm._ssrNode(\"<div>\",\"</div>\",[(_vm.$slots.date)?[_vm._t(\"date\")]:_vm._ssrNode(((_vm.$vuetify.breakpoint.smAndUp)?(\"<div class=\\\"weekday d-none d-sm-block\\\">\"+_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$dayjs(_vm.startDate).format('dddd'))+\"\\n          \")+\"</div> <div class=\\\"date d-none d-sm-block\\\">\"+_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$dayjs(_vm.startDate).format('DD MMM'))+\"\\n          \")+\"</div> <div class=\\\"time d-none d-sm-block\\\">\"+_vm._ssrEscape(_vm._s(_vm.startTime))+\"</div>\"):(\"<div class=\\\"d-sm-none\\\">\"+_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$dayjs(_vm.startDate).format('dddd'))+\",\\n            \"+_vm._s(_vm.$dayjs(_vm.startDate).format('DD MMM'))+\" - \"+_vm._s(_vm.startTime)+\"\\n          \")+\"</div>\")))],2),_vm._ssrNode(\" \"+((_vm.$vuetify.breakpoint.smAndUp)?(\"<div class=\\\"duration d-none d-sm-block\\\"><div class=\\\"duration-icon\\\"><svg width=\\\"18\\\" height=\\\"18\\\" viewBox=\\\"0 0 18 18\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#clock-thin\")))+\"></use></svg></div>\"+_vm._ssrEscape(\"\\n      \"+_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.item.lessonLength }))+\"\\n    \")+\"</div>\"):(\"<div class=\\\"duration d-sm-none\\\">\"+_vm._ssrEscape(\"\\n       (\"+_vm._s(_vm.$t('lessonLength_mins', { lessonLength: _vm.item.lessonLength }))+\")\\n    \")+\"</div>\")))],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"lesson-content\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"lesson-details\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"avatar mr-2\\\">\",\"</div>\",[_c('v-avatar',{attrs:{\"width\":\"110\",\"height\":\"110\"}},[_c('v-img',{attrs:{\"src\":_vm.getSrcAvatar(_vm.item.userAvatars, 'user_thumb_110x110'),\"srcset\":_vm.getSrcSetAvatar(\n                _vm.item.userAvatars,\n                'user_thumb_110x110',\n                'user_thumb_220x220'\n              ),\"options\":{ rootMargin: '50%' }}}),_vm._v(\" \"),(_vm.teacherLink)?_c('nuxt-link',{attrs:{\"to\":_vm.teacherLink}}):_vm._e()],1),_vm._ssrNode(\" \"),_c('user-status',{attrs:{\"user-id\":_vm.userId,\"user-statuses\":_vm.userStatuses,\"large\":\"\"}})],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"details\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"user-info\\\"><div class=\\\"user-info-name\\\">\"+_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.userName)+\"\\n            \")+((_vm.isTeacher)?(\"<div><svg width=\\\"16\\\" height=\\\"16\\\" viewBox=\\\"0 0 16 16\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#info\")))+\"></use></svg></div>\"):\"<!---->\")+\"</div> <div\"+(_vm._ssrClass(null,[\n              'user-info-status',\n              (\"user-info-status--\" + _vm.status),\n              { 'text--red-gradient': _vm.status === 'idle' } ]))+\">\"+((_vm.status === 'online')?(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('online_now'))+\"\\n            \")):(_vm.status === 'idle')?(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('online_but_idle'))+\"\\n            \")):(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('offline_now'))+\"\\n            \")))+\"</div></div> \"),_vm._ssrNode(\"<div class=\\\"lesson-info\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"avatar mr-2\\\">\",\"</div>\",[_c('v-avatar',{attrs:{\"width\":\"85\",\"height\":\"85\"}},[_c('v-img',{attrs:{\"src\":_vm.getSrcAvatar(_vm.item.userAvatars, 'user_thumb_110x110'),\"srcset\":_vm.getSrcSetAvatar(\n                    _vm.item.userAvatars,\n                    'user_thumb_110x110',\n                    'user_thumb_220x220'\n                  ),\"options\":{ rootMargin: '50%' }}}),_vm._v(\" \"),(_vm.teacherLink)?_c('nuxt-link',{attrs:{\"to\":_vm.teacherLink}}):_vm._e()],1),_vm._ssrNode(\" \"),_c('user-status',{attrs:{\"user-id\":_vm.userId,\"user-statuses\":_vm.userStatuses,\"large\":\"\"}})],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div>\",\"</div>\",[_vm._t(\"lessonInfo\"),_vm._ssrNode(\" \"+((!_vm.isUnscheduled)?(\"<div><span class=\\\"text-capitalize\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('lesson'))+\":\")+\"</span> <span class=\\\"text--gradient font-weight-bold text-no-wrap\\\">\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.item.lessonType)+\"\\n              \")+\"</span></div>\"):\"<!---->\")+\" <div><span class=\\\"text-capitalize\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('language'))+\":\")+\"</span> <span class=\\\"text--gradient font-weight-bold text-no-wrap\\\">\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.item.language.name)+\"\\n              \")+\"</span></div> \"+((_vm.item.courseName && _vm.item.courseName.length)?(\"<div><span class=\\\"text-capitalize\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('course'))+\":\")+\"</span> <span class=\\\"text--gradient font-weight-bold\\\">\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.item.courseName)+\"\\n              \")+\"</span></div>\"):\"<!---->\")+\" \"),_vm._ssrNode(\"<div class=\\\"lesson-actions-additional\\\">\",\"</div>\",[_vm._t(\"lessonAdditionalActionsTop\"),_vm._ssrNode(\" \"),(!_vm.isUnscheduleButtonHidden)?[_vm._ssrNode(\"<div>\",\"</div>\",[_vm._ssrNode(\"<span class=\\\"action\\\">\",\"</span>\",[_c('v-img',{staticStyle:{\"left\":\"3px\"},attrs:{\"src\":require('~/assets/images/close-gradient-2.svg'),\"width\":\"11\",\"height\":\"11\"}}),_vm._ssrNode(_vm._ssrEscape(\"\\n                    \"+_vm._s(_vm.$t('unschedule_lesson'))+\"\\n                  \"))],2)])]:_vm._e(),_vm._ssrNode(\" \"),_vm._t(\"lessonAdditionalActionsBottom\",null,{\"showDialog\":_vm.showDialog})],2)],2)],2)],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"lesson-actions\\\">\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"lesson-actions-buttons\\\">\",\"</div>\",[(!_vm.item.userIsDeleted)?_c('v-btn',{staticClass:\"btn-add gradient font-weight-medium ml-1 mb-1\",attrs:{\"width\":\"158\"},on:{\"click\":_vm.showMessageDialog}},[_c('div',{staticClass:\"mr-1\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/message-icon-gradient.svg'),\"width\":\"20\",\"height\":\"20\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"text--gradient\"},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('message'))+\"\\n          \")])]):_vm._e(),_vm._ssrNode(\" \"),_vm._t(\"lessonActions\"),_vm._ssrNode(\" \"),(!_vm.isUnscheduled)?[(_vm.isTeacher || (_vm.isStudent && !_vm.item.isCreated))?[_c('v-btn',{staticClass:\"go-to-class-btn font-weight-medium ml-1 mb-1\",attrs:{\"href\":(\"/lesson/\" + (_vm.item.lessonId) + \"/classroom\"),\"color\":\"primary\",\"width\":\"158\"}},[_c('svg',{staticClass:\"mr-1\",attrs:{\"width\":\"18\",\"height\":\"18\",\"viewBox\":\"0 0 18 18\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#save-icon\")}})]),_vm._v(\"\\n              \"+_vm._s(_vm.$t('go_to_class'))+\"\\n            \")])]:[_c('v-btn',{staticClass:\"go-to-class-btn go-to-class-btn--disabled primary--light font-weight-medium ml-1 mb-1\",attrs:{\"color\":\"primary\",\"width\":\"158\"},on:{\"click\":_vm.showInitializeDialog}},[_c('svg',{staticClass:\"icon--rotated mr-1\",attrs:{\"width\":\"18\",\"height\":\"18\",\"viewBox\":\"0 0 18 18\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#save-icon\")}})]),_vm._v(\"\\n              \"+_vm._s(_vm.$t('go_to_class'))+\"\\n              \"),_c('svg',{staticClass:\"icon--right\",attrs:{\"width\":\"18\",\"height\":\"18\",\"viewBox\":\"0 0 16 16\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#info\")}})])])]]:_vm._e()],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"lesson-actions-additional\\\">\",\"</div>\",[_vm._t(\"lessonAdditionalActionsTop\"),_vm._ssrNode(\" \"),(!_vm.isUnscheduleButtonHidden)?[_vm._ssrNode(\"<div>\",\"</div>\",[_vm._ssrNode(\"<span class=\\\"action\\\">\",\"</span>\",[_c('v-img',{staticStyle:{\"left\":\"3px\"},attrs:{\"src\":require('~/assets/images/close-gradient-2.svg'),\"width\":\"11\",\"height\":\"11\"}}),_vm._ssrNode(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('unschedule_lesson'))+\"\\n            \"))],2)])]:_vm._e(),_vm._ssrNode(\" \"),_vm._t(\"lessonAdditionalActionsBottom\",null,{\"showDialog\":_vm.showDialog})],2)],2),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div\"+(_vm._ssrClass(null,[\n        'lesson-dialog d-flex flex-column justify-center',\n        {\n          'lesson-dialog--shown': _vm.isLessonDialogShown,\n        },\n        {\n          'lesson-dialog--student-info': _vm.dialogType === 'studentInfoDialog',\n        } ]))+\">\",\"</div>\",[_vm._t(\"dialog\",null,{\"closeDialog\":_vm.closeDialog}),_vm._ssrNode(\" \"),(_vm.dialogType === 'unscheduledDialog')?[_vm._ssrNode(\"<div class=\\\"lesson-dialog-title font-weight-medium text--red-gradient\\\">\"+_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('are_you_sure_you_want_to_unschedule_this_lesson_with', {\n              name: _vm.item.userFirstName,\n            }))+\"\\n        \")+\"</div> <div class=\\\"lesson-dialog-content l-scroll l-scroll--grey\\\">\"+((_vm.isStudent)?(((_vm.item.isFreeTrial)?(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t(\n                  'you_will_be_able_to_reschedule_this_lesson_from_your_teachers_profile_page'\n                ))+\"\\n            \")):(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t(\n                  'you_will_receive_credit_and_can_reschedule_lesson_for_anytime_your_teacher_is_available'\n                ))+\"\\n            \")))):(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('student_will_be_given_credit_for_lesson'))+\"\\n          \")))+\"</div> \"),_vm._ssrNode(\"<div class=\\\"lesson-dialog-buttons\\\">\",\"</div>\",[_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"greyDark\",\"outlined\":\"\"},on:{\"click\":_vm.closeDialog}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('do_not_cancel_lesson'))+\"\\n          \")]),_vm._ssrNode(\" \"),_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"error\"},on:{\"click\":_vm.cancelClickHandler}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('cancel_lesson'))+\"\\n          \")])],2)]:_vm._e(),_vm._ssrNode(\" \"),(_vm.dialogType === 'initializeDialog')?[_vm._ssrNode(\"<div class=\\\"lesson-dialog-title font-weight-medium text--gradient\\\"><div class=\\\"lesson-dialog-title-icon\\\"><svg width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 12 12\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#attention\")))+\"></use></svg></div>\"+_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('your_teacher_will_enter_classroom_first'))+\"\\n        \")+\"</div> <div class=\\\"lesson-dialog-content l-scroll l-scroll--grey\\\">\"+(_vm._s(\n            _vm.$t(\n              'after_your_teacher_enters_go_to_class_button_will_become_clickable_so_you_can_enter_as_well'\n            )\n          ))+\"</div> \"),_vm._ssrNode(\"<div class=\\\"lesson-dialog-buttons\\\">\",\"</div>\",[_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"primary\",\"max-width\":\"158\"},on:{\"click\":_vm.closeDialog}},[_vm._v(\"\\n            OK!\\n          \")])],1)]:_vm._e(),_vm._ssrNode(\" \"),(_vm.dialogType === 'studentInfoDialog')?[_vm._ssrNode(\"<div class=\\\"lesson-dialog-title font-weight-medium text--gradient\\\"><div class=\\\"lesson-dialog-title-icon\\\"><svg width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 16 16\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#info\")))+\"></use></svg></div>\"+_vm._ssrEscape(\"\\n          \"+_vm._s(_vm.$t('student_info'))+\"\\n        \")+\"</div> <div class=\\\"lesson-dialog-content l-scroll l-scroll--grey\\\"><div><div><ul><li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('name'))+\":\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.studentInfo.name)+\"\\n                \")+\"</li> <li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('lifetime_free_trials_scheduled'))+\":\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.studentInfo.freeTrialScheduled)+\"\\n                \")+\"</li> <li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('lifetime_lessons_purchased'))+\":\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.studentInfo.lessonsPurchased)+\"\\n                \")+\"</li> <li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('lifetime_teachers_booked_with'))+\":\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.studentInfo.teachersBookedWith)+\"\\n                \")+\"</li></ul></div> <div class=\\\"pl-1\\\"><ul><li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('current_time'))+\":\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.$dayjs().tz(_vm.studentInfo.timezone).format('LT'))+\"\\n                  (\"+_vm._s(_vm.$dayjs().tz(_vm.studentInfo.timezone).format('z'))+\",\\n                  \"+_vm._s(_vm.studentInfo.timezone)+\")\\n                \")+\"</li> <li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('total_spent_with_you'))+\" (\"+_vm._s(_vm.currencyIsoCode)+\"):\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.currencySymbol)+_vm._s(_vm.getPrice(_vm.studentInfo.totalSpendWithTeacher))+\"\\n                \")+\"</li> <li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('date_registered_on_langu'))+\":\")+\"</span>\"+_vm._ssrEscape(\"\\n                  \"+_vm._s(_vm.$dayjs(_vm.studentInfo.dateRegistered)\n                      .tz(_vm.timeZone)\n                      .format('ll, LT'))+\"\\n                \")+\"</li> <li><span class=\\\"font-weight-medium\\\">\"+_vm._ssrEscape(_vm._s(_vm.$t('last_online'))+\":\")+\"</span> \"+((_vm.status === 'online')?(_vm._ssrEscape(\"\\n                    \"+_vm._s(_vm.$t('online_now'))+\"\\n                  \")):(_vm.status === 'idle')?(_vm._ssrEscape(\"\\n                    \"+_vm._s(_vm.$t('online_but_idle'))+\"\\n                  \")):(_vm._ssrEscape(\"\\n                    \"+_vm._s(_vm.$dayjs(_vm.studentInfo.lastLoginDate)\n                        .tz(_vm.timeZone)\n                        .format('ll, LT'))+\"\\n                  \")))+\"</li></ul></div></div></div> \"),_vm._ssrNode(\"<div class=\\\"lesson-dialog-buttons\\\">\",\"</div>\",[_c('v-btn',{staticClass:\"font-weight-medium\",attrs:{\"color\":\"primary\",\"max-width\":\"158\"},on:{\"click\":_vm.closeDialog}},[_vm._v(\"\\n            OK\\n          \")])],1)]:_vm._e()],2)],2),_vm._ssrNode(\" \"),(_vm.isShownMessageDialog)?_c('message-dialog',{attrs:{\"recipient-id\":_vm.userId,\"recipient-name\":((_vm.item.userFirstName) + \" \" + (_vm.item.userLastName)),\"is-shown-message-dialog\":_vm.isShownMessageDialog},on:{\"close-dialog\":function($event){_vm.isShownMessageDialog = false}}}):_vm._e(),_vm._ssrNode(\" \"),_vm._t(\"default\")],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getPrice } from '~/helpers'\n\nimport Avatars from '~/mixins/Avatars'\nimport UserStatus from '~/components/UserStatus'\nimport MessageDialog from '~/components/MessageDialog'\n\nexport default {\n  name: 'LessonItem',\n  components: { UserStatus, MessageDialog },\n  mixins: [Avatars],\n  props: {\n    item: {\n      type: Object,\n      required: true,\n    },\n    userStatuses: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  data() {\n    return {\n      getPrice,\n      isLessonDialogShown: false,\n      dialogType: null,\n      isShownMessageDialog: false,\n      studentInfo: null,\n    }\n  },\n  computed: {\n    isTeacher() {\n      return this.$store.getters['user/isTeacher']\n    },\n    isStudent() {\n      return this.$store.getters['user/isStudent']\n    },\n    userId() {\n      return this.item[this.isTeacher ? 'studentId' : 'teacherId']\n    },\n    userIsDeleted() {\n      return this.item.userIsDeleted\n    },\n    userName() {\n      return this.userIsDeleted\n        ? this.$t('deleted_user')\n        : `${this.item.userFirstName} ${this.item.userLastName}`\n    },\n    currencyIsoCode() {\n      return this.$store.state.currency.item.isoCode\n    },\n    currencySymbol() {\n      return this.$store.getters['currency/currentCurrencySymbol']\n    },\n    status() {\n      let status = 'offline'\n\n      if (\n        Object.prototype.hasOwnProperty.call(\n          this.userStatuses,\n          this.userId?.toString()\n        )\n      ) {\n        status = this.userStatuses[this.userId]\n      }\n\n      return status\n    },\n    isPast() {\n      return this.item.type === 'past'\n    },\n    isUnscheduled() {\n      return this.item.type === 'unscheduled'\n    },\n    timeZone() {\n      return this.$store.getters['user/timeZone']\n    },\n    startDate() {\n      return this.$dayjs(this.item.startDate).tz(this.timeZone)\n    },\n    startTime() {\n      return this.startDate.format('LT')\n    },\n    isUnscheduleButtonHidden() {\n      return (\n        this.isUnscheduled ||\n        this.item.isFinished ||\n        (this.isStudent &&\n          (this.isPast ||\n            this.$dayjs().add(1, 'day').isAfter(this.startDate, 'minute')))\n      )\n    },\n    teacherLink() {\n      return this.isStudent &&\n        !this.item.userIsDeleted &&\n        this.item.teacherUsername\n        ? `/teacher/${this.item.teacherUsername}`\n        : null\n    },\n  },\n  methods: {\n    showMessageDialog() {\n      this.$store\n        .dispatch('message/checkConversation', this.userId)\n        .then((res) => {\n          if (res.threadId) {\n            this.$store.dispatch('loadingStart')\n            this.$router.push({ path: `/user/messages/${res.threadId}/view` })\n          } else {\n            this.isShownMessageDialog = true\n          }\n        })\n    },\n    cancelClickHandler() {\n      this.$store.dispatch('loadingStart')\n      this.$store\n        .dispatch('lesson/cancelLesson', this.item.lessonId)\n        .then(() => {\n          location.reload()\n        })\n        .catch((e) => {\n          this.$store.dispatch('loadingStop')\n          this.$store.dispatch('snackbar/error')\n\n          console.info(e)\n        })\n    },\n    studentInfoClickHandler() {\n      this.$store\n        .dispatch('lesson/getStudentInfo', this.item.studentId)\n        .then((res) => {\n          this.studentInfo = res\n          this.dialogType = 'studentInfoDialog'\n\n          this.showDialog()\n        })\n    },\n    showInitializeDialog() {\n      this.dialogType = 'initializeDialog'\n\n      this.showDialog()\n    },\n    showUnscheduledDialog() {\n      this.dialogType = 'unscheduledDialog'\n\n      this.showDialog()\n    },\n    showDialog() {\n      this.isLessonDialogShown = true\n    },\n    closeDialog() {\n      this.isLessonDialogShown = false\n\n      setTimeout(() => {\n        this.dialogType = null\n      }, 500)\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonItem.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonItem.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./LessonItem.vue?vue&type=template&id=1f692612&\"\nimport script from \"./LessonItem.vue?vue&type=script&lang=js&\"\nexport * from \"./LessonItem.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./LessonItem.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"7dfe9174\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {UserStatus: require('D:/languworks/langu-frontend/components/UserStatus.vue').default,MessageDialog: require('D:/languworks/langu-frontend/components/MessageDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VAvatar } from 'vuetify/lib/components/VAvatar';\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VAvatar,VBtn,VImg})\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[\n    'time-picker-item',\n    { active: _vm.isActive },\n    { selected: _vm.item.isSelected },\n    { free: _vm.item.isAvailable },\n    { unavailable: _vm.item.isUnavailable } ],attrs:{\"id\":_vm.elId},on:{\"mouseover\":_vm.mouseoverHandler,\"mouseleave\":_vm.mouseleaveHandler,\"click\":function($event){$event.stopPropagation();return _vm.clickHandler.apply(null, arguments)}}},[])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { isTouchDevice } from '~/helpers/check_device'\n\nexport default {\n  name: 'TimePickerItem',\n  props: {\n    idDefined: {\n      type: Boolean,\n      default: false,\n    },\n    item: {\n      type: Object,\n      required: true,\n    },\n    allowedToSelect: {\n      type: Boolean,\n      required: true,\n    },\n    activeItems: {\n      type: Array,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      isTouchDevice: isTouchDevice(),\n    }\n  },\n  computed: {\n    timezone() {\n      return this.$store.getters['user/timeZone']\n    },\n    elId() {\n      return this.idDefined\n        ? `h-${this.$dayjs(this.item.date)\n            .add(\n              this.$dayjs(this.item.date).tz(this.timezone).utcOffset(),\n              'minute'\n            )\n            .format('HH-mm')}`\n        : null\n    },\n    isActive() {\n      return this.item.isAvailable && this.activeItems.includes(this.item)\n    },\n  },\n  methods: {\n    clickHandler() {\n      if (this.item.isAvailable) {\n        this.$emit('click-item', this.item)\n      }\n    },\n    mouseoverHandler() {\n      if (\n        !this.isTouchDevice &&\n        this.item.isAvailable &&\n        !this.item.isSelected &&\n        this.allowedToSelect\n      ) {\n        this.$emit('mouseover-item', this.item)\n      }\n    },\n    mouseleaveHandler() {\n      if (\n        this.item.isAvailable &&\n        !this.item.isSelected &&\n        this.allowedToSelect\n      ) {\n        this.$emit('mouseleave-item')\n      }\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TimePickerItem.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TimePickerItem.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TimePickerItem.vue?vue&type=template&id=7467ec82&scoped=true&\"\nimport script from \"./TimePickerItem.vue?vue&type=script&lang=js&\"\nexport * from \"./TimePickerItem.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./TimePickerItem.vue?vue&type=style&index=0&id=7467ec82&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"7467ec82\",\n  \"d1fa2cf4\"\n  \n)\n\nexport default component.exports", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Pagination.vue?vue&type=style&index=0&id=18a8bda5&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".pagination-list[data-v-18a8bda5]{padding-left:0;list-style-type:none}.pagination-item a[data-v-18a8bda5]{display:flex;justify-content:center;align-items:center;width:35px;height:35px;font-size:16px;font-weight:700;border-radius:4px;color:var(--v-darkLight-base);text-decoration:none;transition:color .3s;margin:0 10px}@media only screen and (max-width:639px){.pagination-item a[data-v-18a8bda5]{width:38px;height:38px}}@media only screen and (max-width:479px){.pagination-item a[data-v-18a8bda5]{width:36px;height:36px;font-size:14px;border-radius:2px}}.pagination-item a.current[data-v-18a8bda5]{background:var(--v-orange-base)}.pagination-item a[data-v-18a8bda5]:not(.current):hover{color:var(--v-orange-base)}.pagination-item-next[data-v-18a8bda5],.pagination-item-prev[data-v-18a8bda5]{display:flex;align-items:center;font-size:16px;font-weight:500;border-radius:50%;transition:color .3s}.pagination-item-next.disabled[data-v-18a8bda5],.pagination-item-prev.disabled[data-v-18a8bda5]{opacity:.6}.pagination-item-next[data-v-18a8bda5]:not(.disabled),.pagination-item-prev[data-v-18a8bda5]:not(.disabled){cursor:pointer}.pagination-item-next[data-v-18a8bda5]:not(.disabled):hover,.pagination-item-prev[data-v-18a8bda5]:not(.disabled):hover{color:var(--v-orange-base)}.pagination-item-prev[data-v-18a8bda5]{margin-right:15px}@media only screen and (max-width:639px){.pagination-item-prev[data-v-18a8bda5]{margin-right:10px}}@media only screen and (max-width:479px){.pagination-item-prev[data-v-18a8bda5]{margin-right:5px}}.pagination-item-prev .icon[data-v-18a8bda5]{margin-right:12px}.pagination-item-next[data-v-18a8bda5]{margin-left:15px}@media only screen and (max-width:639px){.pagination-item-next[data-v-18a8bda5]{margin-left:10px}}@media only screen and (max-width:479px){.pagination-item-next[data-v-18a8bda5]{margin-left:5px}}.pagination-item-next .icon[data-v-18a8bda5]{margin-left:12px}.pagination-item .dots[data-v-18a8bda5]{display:inline-block;width:64px;text-align:center}@media only screen and (max-width:639px){.pagination-item .dots[data-v-18a8bda5]{width:30px}}@media only screen and (max-width:479px){.pagination-item .dots[data-v-18a8bda5]{width:25px}}.pagination-item-prev[data-v-18a8bda5]{color:#000;fill:#000}.pagination-item-prev span[data-v-18a8bda5]{color:#000;fill:#000;font-weight:700}.pagination-item-next[data-v-18a8bda5]{color:#000;fill:#000}.pagination-item-next span[data-v-18a8bda5]{color:#000;fill:#000;font-weight:700}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TimePicker.vue?vue&type=style&index=0&id=69022ce1&lang=scss&scoped=true&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"1fdd5634\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{\"dialog\":_vm.isShownMessageDialog,\"max-width\":\"725\",\"custom-class\":\"message-dialog\",\"persistent\":\"\",\"fullscreen\":_vm.$vuetify.breakpoint.xsOnly}},_vm.$listeners),[_c('v-form',{on:{\"submit\":function($event){$event.preventDefault();return _vm.submitHandler.apply(null, arguments)}}},[_c('div',{staticClass:\"message-dialog-header text--gradient\"},[_vm._v(\"\\n      \"+_vm._s(_vm.$t(\n          _vm.$vuetify.breakpoint.xsOnly\n            ? 'ask_question'\n            : 'questions_ask_teacher_directly'\n        ))+\"\\n    \")]),_vm._v(\" \"),_c('div',{class:[\n        'message-dialog-body pt-0 pt-sm-3',\n        {\n          'l-scroll l-scroll--grey l-scroll--large':\n            _vm.$vuetify.breakpoint.xsOnly,\n        } ]},[_c('v-row',{attrs:{\"no-gutters\":\"\"}},[_c('v-col',{staticClass:\"col-12 col-sm-5 col-md-6\"},[_c('div',{staticClass:\"message-dialog-text pr-0 pr-sm-2\"},[_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.$vuetify.breakpoint.xsOnly),expression:\"$vuetify.breakpoint.xsOnly\"}]},[_c('span',{staticClass:\"text-capitalize\"},[_vm._v(_vm._s(_vm.$t('teacher')))]),_vm._v(\": \"+_vm._s(_vm.recipientName)),_c('br'),_vm._v(\" \"),_c('br')]),_vm._v(\" \"),_c('div',{domProps:{\"innerHTML\":_vm._s(_vm.$t('message_dialog_text', { recipientName: _vm.recipientName }))}})])]),_vm._v(\" \"),_c('v-col',{staticClass:\"col-12 col-sm-7 col-md-6 mt-3 mt-sm-0\"},[_c('editor',{attrs:{\"value\":_vm.newMessage,\"limit\":6000},on:{\"update\":function($event){_vm.newMessage = $event},\"validation\":function($event){_vm.isMessageValid = $event}}})],1)],1)],1),_vm._v(\" \"),_c('div',{staticClass:\"message-dialog-footer d-flex justify-space-between align-center\"},[_c('div',{staticClass:\"prev-button body-1\",on:{\"click\":function($event){return _vm.$emit('close-dialog')}}},[_c('svg',{staticClass:\"mr-1\",attrs:{\"width\":\"17\",\"height\":\"12\",\"viewBox\":\"0 0 17 12\"}},[_c('use',{attrs:{\"xlink:href\":((require('~/assets/images/icon-sprite.svg')) + \"#arrow-prev\")}})]),_vm._v(_vm._s(_vm.$t('go_back'))+\"\\n      \")]),_vm._v(\" \"),_c('div',[_c('v-btn',{attrs:{\"small\":\"\",\"color\":\"primary\",\"type\":\"submit\"}},[_vm._v(\"\\n          \"+_vm._s(_vm.$t('send_message'))+\"\\n        \")])],1)])])],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport LDialog from '~/components/LDialog'\nimport Editor from '~/components/form/Editor'\n\nexport default {\n  name: 'MessageDialog',\n  components: { LDialog, Editor },\n  props: {\n    recipientId: {\n      type: Number,\n      required: true,\n    },\n    recipientName: {\n      type: String,\n      required: true,\n    },\n    isShownMessageDialog: {\n      type: Boolean,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      newMessage: '',\n      isMessageValid: false,\n    }\n  },\n  beforeDestroy() {\n    this.newMessage = ''\n    this.isMessageValid = false\n  },\n  methods: {\n    submitHandler() {\n      if (this.isMessageValid) {\n        this.$store\n          .dispatch('message/sendNewMessage', {\n            recipientId: this.recipientId,\n            message: this.newMessage,\n          })\n          .then((res) => {\n            this.newMessage = ''\n\n            this.$router.push({ path: `/user/messages/${res.id}/view` })\n          })\n          .finally(() => this.$emit('close-dialog'))\n      }\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./MessageDialog.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./MessageDialog.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./MessageDialog.vue?vue&type=template&id=01f70911&\"\nimport script from \"./MessageDialog.vue?vue&type=script&lang=js&\"\nexport * from \"./MessageDialog.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./MessageDialog.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"06ad70c7\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {LDialog: require('D:/languworks/langu-frontend/components/LDialog.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VBtn } from 'vuetify/lib/components/VBtn';\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VForm } from 'vuetify/lib/components/VForm';\nimport { VRow } from 'vuetify/lib/components/VGrid';\ninstallComponents(component, {VBtn,VCol,VForm,VRow})\n", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SearchInput.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".search-input .v-input{background-color:#fff;border-radius:50px!important;box-shadow:0 4px 14px rgba(217,225,236,.47)}@media only screen and (max-width:767px){.search-input .v-input{border-radius:10px!important}}.search-input .v-input input::-moz-placeholder{color:var(--v-greyLight-darken2)}.search-input .v-input input:-ms-input-placeholder{color:var(--v-greyLight-darken2)}.search-input .v-input input::placeholder{color:var(--v-greyLight-darken2)}.search-input .v-input .v-input__control>.v-input__slot{height:56px!important}@media only screen and (max-width:767px){.search-input .v-input .v-input__control>.v-input__slot{height:40px!important}}.search-input .v-input .v-input__append-inner{margin-top:9px!important}@media only screen and (max-width:767px){.search-input .v-input .v-input__append-inner{margin-top:6px!important}}.search-input .v-input .v-input__append-inner .v-image{width:26px!important;height:26px!important}@media only screen and (max-width:767px){.search-input .v-input .v-input__append-inner .v-image{width:20px!important;height:20px!important}}.search-input .v-input.v-input--is-focused .v-input__control>.v-input__slot:before{border-radius:16px!important}.search-input .v-input.v-input.v-text-field--outlined fieldset{border-color:transparent!important;box-shadow:0 4px 14px rgba(217,225,236,.47)}.search-input--inner-border .v-input .v-input__control>.v-input__slot{padding-top:5px!important;padding-left:5px!important;padding-bottom:5px!important}.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot{position:relative;padding:0 16px;background-color:transparent!important}@media only screen and (max-width:1215px){.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot{padding:0 12px}}@media only screen and (max-width:767px){.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot{padding:0 10px}}.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot:before{display:none!important;content:\\\"\\\";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:15px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}@media only screen and (max-width:767px){.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot:before{border-radius:9px}}.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot input{position:relative;z-index:2}.search-input--inner-border .v-input .v-input__append-inner{margin-top:4px!important;padding-left:15px}@media only screen and (max-width:767px){.search-input--inner-border .v-input .v-input__append-inner{margin-top:0!important}}.search-input--inner-border .v-input.v-input--is-focused .v-input__control>.v-input__slot:before{display:none!important}.search-input--inner-border .v-input.v-input--is-focused .v-input__control>.v-input__slot>.v-text-field__slot:before{display:block!important}.search-input--small .v-input .v-input__control>.v-input__slot{height:44px!important}.search-input--small .v-input .v-input__append-inner{margin-top:6px!important}.search-input--small .v-input .v-input__append-inner .v-image{width:20px!important;height:20px!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"time-picker unselected\"},[_vm._ssrNode(\"<div class=\\\"time-picker-toggle\\\" data-v-69022ce1>\",\"</div>\",[_vm._ssrNode(\"<div\"+(_vm._ssrClass(null,['btn btn-prev', { 'btn--disabled': _vm.isPrevButtonDisabled }]))+\" data-v-69022ce1>\",\"</div>\",[_c('v-icon',{attrs:{\"color\":\"greyDark\"}},[_vm._v(_vm._s(_vm.mdiChevronLeft))])],1),_vm._ssrNode(\" <div class=\\\"period text-center\\\" data-v-69022ce1>\"+_vm._ssrEscape(\"\\n      \"+_vm._s(_vm.firstDayOfWeek.format('D MMM'))+\" -\\n      \"+_vm._s(_vm.lastDayOfWeek.format('D MMM'))+\"\\n    \")+\"</div> \"),_vm._ssrNode(\"<div class=\\\"btn btn-next\\\" data-v-69022ce1>\",\"</div>\",[_c('v-icon',{attrs:{\"color\":\"greyDark\"}},[_vm._v(_vm._s(_vm.mdiChevronRight))])],1)],2),_vm._ssrNode(\" <div class=\\\"time-picker-top-bar\\\" data-v-69022ce1><div class=\\\"time-picker-top-bar-helper mx-auto\\\" data-v-69022ce1>\"+(_vm._ssrList((7),function(i){return (\"<div class=\\\"item\\\" data-v-69022ce1>\"+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm._f(\"dayFormat\")(_vm.getDayOfWeek(i),'ddd,'))+\"\\n        \")+((_vm.$vuetify.breakpoint.xsOnly)?(\"<br class=\\\"d-sm-none\\\" data-v-69022ce1>\"):\"<!---->\")+_vm._ssrEscape(\"\\n        \"+_vm._s(_vm._f(\"dayFormat\")(_vm.getDayOfWeek(i),'MMM D'))+\"\\n      \")+\"</div>\")}))+\"</div></div> \"),_vm._ssrNode(\"<div class=\\\"time-picker-wrap l-scroll l-scroll--grey l-scroll--large\\\" data-v-69022ce1>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"time-picker-wrap-helper\\\" data-v-69022ce1>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"time-picker-left-bar\\\" data-v-69022ce1>\"+((_vm.$i18n.locale === 'en')?(\"<div class=\\\"item\\\" data-v-69022ce1>12 AM</div> \"+(_vm._ssrList((11),function(i){return (\"<div class=\\\"item\\\" data-v-69022ce1>\"+_vm._ssrEscape(_vm._s(i)+\" AM\")+\"</div>\")}))+\" <div class=\\\"item\\\" data-v-69022ce1>12 PM</div> \"+(_vm._ssrList((11),function(i){return (\"<div class=\\\"item\\\" data-v-69022ce1>\"+_vm._ssrEscape(_vm._s(i)+\" PM\")+\"</div>\")}))):((_vm._ssrList((24),function(i){return (\"<div class=\\\"item\\\" data-v-69022ce1>\"+_vm._ssrEscape(_vm._s(i - 1)+\":00\")+\"</div>\")}))))+\"</div> \"),_vm._ssrNode(\"<div class=\\\"time-picker-graph\\\" data-v-69022ce1>\",\"</div>\",_vm._l((_vm.calendar),function(day,idx){return _vm._ssrNode(\"<div class=\\\"day\\\" data-v-69022ce1>\",\"</div>\",[_c('client-only',_vm._l((day),function(item,index){return _c('time-picker-item',{key:(idx + \"-\" + index),class:index % 2 ? '' : 'first-half',attrs:{\"id-defined\":\"\",\"item\":item,\"allowed-to-select\":_vm.allowedToSelect,\"active-items\":_vm.activeItems},on:{\"mouseover-item\":function($event){return _vm.mouseoverItem($event)},\"mouseleave-item\":_vm.mouseleaveItem,\"click-item\":function($event){return _vm.clickItem($event)}}})}),1)],1)}),0),_vm._ssrNode(\" <div class=\\\"time-picker-right-bar\\\" data-v-69022ce1>\"+((_vm.$i18n.locale === 'en')?(\"<div class=\\\"item\\\" data-v-69022ce1>12 AM</div> \"+(_vm._ssrList((11),function(i){return (\"<div class=\\\"item\\\" data-v-69022ce1>\"+_vm._ssrEscape(_vm._s(i)+\" AM\")+\"</div>\")}))+\" <div class=\\\"item\\\" data-v-69022ce1>12 PM</div> \"+(_vm._ssrList((11),function(i){return (\"<div class=\\\"item\\\" data-v-69022ce1>\"+_vm._ssrEscape(_vm._s(i)+\" PM\")+\"</div>\")}))):((_vm._ssrList((24),function(i){return (\"<div class=\\\"item\\\" data-v-69022ce1>\"+_vm._ssrEscape(_vm._s(i - 1)+\":00\")+\"</div>\")}))))+\"</div>\")],2)]),_vm._ssrNode(\" \"),_c('loader',{attrs:{\"is-loading\":_vm.isLoading,\"absolute\":\"\"}})],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { mdiChevronLeft, mdiChevronRight } from '@mdi/js'\nimport TimePickerItem from '~/components/TimePickerItem'\nimport Loader from '~/components/Loader'\n\nconst STATUS_FREE = 0\nconst STATUS_RESERVED = 1\nconst STATUS_OCCUPIED = 2\n// const STATUS_SOME_AVAILABILITY = 4\n\nexport default {\n  name: 'TimePicker',\n  components: { TimePickerItem, Loader },\n  filters: {\n    dayFormat(time, format = 'HH:mm') {\n      return time.format(format)\n    },\n  },\n  props: {\n    username: {\n      type: String,\n      required: true,\n    },\n    lessonLength: {\n      type: Number,\n      required: true,\n    },\n    quantityLessons: {\n      type: Number,\n      required: true,\n    },\n    currentTime: {\n      type: Object,\n      required: true,\n    },\n    isShownTimePickerDialog: {\n      type: Boolean,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      mdiChevronLeft,\n      mdiChevronRight,\n      key: 1,\n      now: this.$dayjs(),\n      items: [],\n      activeItems: [],\n      isLoading: false,\n      mounted: false,\n    }\n  },\n  computed: {\n    firstDayOfWeek() {\n      return this.currentTime.day(1)\n    },\n    lastDayOfWeek() {\n      return this.currentTime.day(7)\n    },\n    slots() {\n      return this.$store.state.teacher_profile.slots\n    },\n    selectedSlots() {\n      return this.$store.state.teacher_profile.selectedSlots || []\n    },\n    isPrevButtonDisabled() {\n      return this.now.day(1).isSameOrAfter(this.firstDayOfWeek, 'day')\n    },\n    quantityItemsPerLesson() {\n      return this.lessonLength / 30\n    },\n    allowedToSelect() {\n      return (\n        this.selectedSlots.length < this.quantityLessons ||\n        this.quantityLessons === 1\n      )\n    },\n    calendar() {\n      const result = []\n\n      for (let i = 0; i < 7; i++) {\n        result.push(this.items.slice(i * 48, 48 * (i + 1)))\n      }\n\n      return result\n    },\n    isUserLogged() {\n      return this.$store.getters['user/isUserLogged']\n    },\n    timeZone() {\n      return this.$store.getters['user/timeZone']\n    },\n  },\n  watch: {\n    lessonLength() {\n      this.reset()\n    },\n    quantityLessons() {\n      this.reset()\n    },\n    slots: {\n      handler() {\n        if (!this.isShownTimePickerDialog) {\n          setTimeout(this.getItems)\n        }\n      },\n      deep: true,\n    },\n    isShownTimePickerDialog(newValue, oldValue) {\n      if (this.mounted && newValue) {\n        this.scroll()\n      }\n    },\n  },\n  async mounted() {\n    const selectedSlots = window.localStorage.getItem('selected-slots')\n      ? JSON.parse(window.localStorage.getItem('selected-slots'))\n      : null\n\n    if (selectedSlots) {\n      this.$store.commit('teacher_profile/SET_SELECTED_SLOTS', selectedSlots)\n\n      window.localStorage.removeItem('selected-slots')\n    }\n\n    await this.getItems()\n\n    this.scroll()\n\n    this.mounted = true\n  },\n  methods: {\n    getItems() {\n      const result = []\n\n      let selectedSlots = []\n\n      this.selectedSlots.forEach(\n        (item) =>\n          (selectedSlots = selectedSlots.concat(item.map((el) => el.date)))\n      )\n\n      for (let d = 1; d <= 7; d++) {\n        for (let h = 0; h < 48; h++) {\n          const date = this.getDayOfWeek(d)\n            .hour(Math.floor(h / 2))\n            .minute(h % 2 ? 30 : 0)\n            .second(0)\n          const dateOffset = date.tz(this.timeZone).utcOffset()\n\n          let sameItem\n\n          for (let s = 0; s < this.slots.length; s++) {\n            const dateObj = this.$dayjs(this.slots[s].date)\n\n            if (date.isSame(dateObj.add(dateOffset, 'minute'), 'minute')) {\n              sameItem = this.slots[s]\n\n              break\n            }\n          }\n\n          const dateByUTC = date\n            .add(this.$dayjs(date).tz(this.timeZone).utcOffset() * -1, 'minute')\n            .format()\n\n          result.push({\n            id: sameItem?.id,\n            status: sameItem?.status,\n            date: dateByUTC,\n            isSelected: selectedSlots.includes(dateByUTC),\n            isAvailable:\n              sameItem?.status === STATUS_FREE &&\n              !this.now.add(1, 'day').isSameOrAfter(date, 'minute'),\n            isUnavailable:\n              sameItem?.status === STATUS_OCCUPIED ||\n              sameItem?.status === STATUS_RESERVED ||\n              (sameItem?.status === STATUS_FREE &&\n                this.now.add(1, 'day').isSameOrAfter(date, 'minute')),\n          })\n        }\n      }\n\n      this.items = result\n    },\n    async toggleWeek(day) {\n      const date = this.firstDayOfWeek.add(day, 'day')\n\n      await this.$store.dispatch('loadingAllow', false)\n\n      this.isLoading = true\n\n      this.$store\n        .dispatch('teacher_profile/getSlots', {\n          slug: this.username,\n          date,\n        })\n        .then(() => {\n          this.$emit('update-current-time', date)\n\n          this.$nextTick(this.getItems)\n        })\n        .then(() => {\n          this.scroll()\n        })\n        .finally(() => {\n          this.isLoading = false\n\n          this.$store.dispatch('loadingAllow', true)\n        })\n    },\n    getDayOfWeek(day) {\n      return this.currentTime.day(day)\n    },\n    checkDirection(item, index) {\n      let direction = 0\n\n      for (let i = 1; i < this.quantityItemsPerLesson; i++) {\n        if (\n          this.items[index + i].isAvailable &&\n          !this.items[index + i].isSelected\n        ) {\n          direction = 1\n        } else {\n          direction = 0\n\n          break\n        }\n      }\n\n      if (direction === 0) {\n        for (let i = 1; i < this.quantityItemsPerLesson; i++) {\n          if (\n            this.items[index - i].isAvailable &&\n            !this.items[index - i].isSelected\n          ) {\n            direction = -1\n          } else {\n            direction = 0\n\n            break\n          }\n        }\n      }\n\n      return direction\n    },\n    clickItem(item) {\n      if (!item.isSelected && this.allowedToSelect) {\n        const arr = [item]\n\n        if (this.quantityItemsPerLesson === 1 && this.quantityLessons === 1) {\n          this.activeItems = []\n\n          this.resetSelectedSlots()\n        }\n\n        if (this.quantityItemsPerLesson > 1) {\n          const index = this.items.indexOf(item)\n          const direction = this.checkDirection(item, index)\n\n          if (direction) {\n            if (this.quantityLessons === 1) {\n              this.resetSelectedSlots()\n            }\n\n            for (let i = 1; i < this.quantityItemsPerLesson; i++) {\n              arr.push(this.items[index + i * direction])\n            }\n          } else {\n            return\n          }\n        }\n\n        arr.forEach((item) => (item.isSelected = true))\n\n        this.$store.commit('teacher_profile/ADD_SELECTED_SLOT', arr)\n      } else {\n        for (let i = 0; i < this.selectedSlots.length; i++) {\n          const ids = this.selectedSlots[i]?.map((el) => el.id)\n\n          if (ids.includes(item.id)) {\n            this.$store.commit('teacher_profile/REMOVE_SELECTED_SLOT', i)\n\n            this.items.forEach((arr) => {\n              if (ids.includes(arr.id)) {\n                arr.isSelected = false\n              }\n            })\n\n            break\n          }\n        }\n      }\n\n      if (!this.isUserLogged) {\n        window.setTimeout(() => {\n          this.$emit('next-step')\n        }, 300)\n      }\n    },\n    mouseoverItem(item) {\n      if (this.quantityItemsPerLesson === 1) {\n        this.activeItems.push(item)\n\n        return\n      }\n\n      if (this.quantityItemsPerLesson > 1) {\n        const index = this.items.indexOf(item)\n        const direction = this.checkDirection(item, index)\n\n        if (direction) {\n          this.activeItems.push(item)\n\n          for (let i = 1; i < this.quantityItemsPerLesson; i++) {\n            this.activeItems.push(this.items[index + i * direction])\n          }\n        }\n      }\n    },\n    mouseleaveItem() {\n      this.activeItems = []\n    },\n    resetSelectedSlots() {\n      this.$store.commit('teacher_profile/RESET_SELECTED_SLOTS')\n\n      this.items.forEach((arr) => {\n        arr.isSelected = false\n      })\n    },\n    reset() {\n      this.activeItems = []\n\n      this.resetSelectedSlots()\n      this.key++\n    },\n    scroll() {\n      const options = {\n        top: 560,\n        behavior: 'instant',\n      }\n\n      if (this.selectedSlots.length) {\n        const [earliestTime] = this.selectedSlots\n          .flat()\n          .map((slot) => {\n            const dateObj = this.$dayjs(slot.date)\n\n            return dateObj\n              .add(dateObj.tz(this.timeZone).utcOffset(), 'minute')\n              .format()\n          })\n          .filter((date) =>\n            this.$dayjs(date).isBetween(this.firstDayOfWeek, this.lastDayOfWeek)\n          )\n          .map((date) => this.$dayjs(date).format('HH-mm'))\n          .sort()\n        const el = document.getElementById(`h-${earliestTime}`)\n\n        if (el) {\n          options.top = el.offsetTop - 84\n        }\n      }\n\n      setTimeout(() => {\n        this.$refs.timePickerWrap.scroll(options)\n      })\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TimePicker.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TimePicker.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./TimePicker.vue?vue&type=template&id=69022ce1&scoped=true&\"\nimport script from \"./TimePicker.vue?vue&type=script&lang=js&\"\nexport * from \"./TimePicker.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./TimePicker.vue?vue&type=style&index=0&id=69022ce1&lang=scss&scoped=true&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"69022ce1\",\n  \"637ca153\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {TimePickerItem: require('D:/languworks/langu-frontend/components/TimePickerItem.vue').default,Loader: require('D:/languworks/langu-frontend/components/Loader.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VIcon } from 'vuetify/lib/components/VIcon';\ninstallComponents(component, {VIcon})\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonItem.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"a8b919c2\", content, true, context)\n};", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./UserStatus.vue?vue&type=style&index=0&id=652352c7&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".user-status[data-v-652352c7]{width:16px;height:16px;border-radius:50%;border:2px solid #fff;background:#636363;z-index:2}.user-status--idle[data-v-652352c7]{background:linear-gradient(122.42deg,var(--v-redLight-base),var(--v-orangeLight2-base))}.user-status--online[data-v-652352c7]{background:var(--v-success-base)}.user-status--large[data-v-652352c7]{width:25px;height:25px}@media only screen and (max-width:991px){.user-status--large[data-v-652352c7]{width:23px;height:23px}}@media only screen and (max-width:639px){.user-status--large[data-v-652352c7]{width:21px;height:21px}}@media only screen and (max-width:479px){.user-status--large[data-v-652352c7]{width:19px;height:19px}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./CalendarDate.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"c8420d6e\", content, true, context)\n};", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./TimePickerDialog.vue?vue&type=style&index=0&id=3bb5b460&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"8eac9684\", content, true, context)\n};", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Steps.vue?vue&type=style&index=0&id=307c13c8&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"9e60533c\", content, true, context)\n};", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"calendar-date\",style:(_vm.styles),on:{\"click\":_vm.click<PERSON><PERSON>ler}},[_vm._ssrNode(\"<div class=\\\"v-btn\\\">\"+_vm._ssrEscape(_vm._s(_vm.$dayjs(_vm.date).format('D')))+\"</div> <div\"+(_vm._ssrClass(null,[\n      'calendar-date-marker',\n      { 'calendar-date-marker--free': _vm.isFree },\n      { 'calendar-date-marker--some-free': _vm.isSomeFree },\n      { 'calendar-date-marker--occupied': _vm.isOccupied } ]))+\"></div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nconst STATUS_PAST = -1\nconst STATUS_FREE = 0\nconst STATUS_OCCUPIED = 2\nconst STATUS_EMPTY = 3\nconst STATUS_SOME_FREE = 4\n\nexport default {\n  name: 'CalendarDate',\n  props: {\n    date: {\n      type: String,\n      required: true,\n    },\n    item: {\n      type: Object,\n      default: () => ({}),\n    },\n    type: {\n      type: String,\n      required: true,\n    },\n  },\n  computed: {\n    isPast() {\n      return this.item.status === STATUS_PAST\n    },\n    isFree() {\n      return this.item.status === STATUS_FREE\n    },\n    isOccupied() {\n      return this.item.status === STATUS_OCCUPIED\n    },\n    isEmpty() {\n      return this.item.status === STATUS_EMPTY\n    },\n    isSomeFree() {\n      return this.item.status === STATUS_SOME_FREE\n    },\n    hasAction() {\n      return (this.isOccupied || this.isSomeFree) && this.type === 'upcoming'\n    },\n    styles() {\n      return { cursor: this.hasAction ? 'pointer' : 'auto' }\n    },\n  },\n  methods: {\n    clickHandler() {\n      if (this.hasAction) {\n        this.$emit('click-date', this.item.date)\n      }\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./CalendarDate.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./CalendarDate.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./CalendarDate.vue?vue&type=template&id=76a41d38&\"\nimport script from \"./CalendarDate.vue?vue&type=script&lang=js&\"\nexport * from \"./CalendarDate.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./CalendarDate.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"7e29c724\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Calendar.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"43d316a6\", content, true, context)\n};", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../../sass-loader/dist/cjs.js??ref--6-oneOf-1-3!./VCalendarWeekly.sass\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\nrequire(\"!../../../../vue-style-loader/lib/addStylesServer.js\").default(\"37cc6ed2\", content, true)", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./MessageDialog.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v-application .v-dialog.message-dialog>.v-card{padding:32px 40px!important}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog>.v-card{padding:50px 18px 74px!important}.v-application .v-dialog.message-dialog>.v-card .dialog-content,.v-application .v-dialog.message-dialog>.v-card .message-dialog-body,.v-application .v-dialog.message-dialog>.v-card .v-form{height:100%}.v-application .v-dialog.message-dialog>.v-card .message-dialog-body{overflow-y:auto}}.v-application .v-dialog.message-dialog .message-dialog-header{display:inline-block;padding-right:60px;font-size:20px;font-weight:700;line-height:1.1}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-header{position:absolute;top:0;left:0;width:100%;height:50px;display:flex;align-items:center;padding-left:18px;font-size:18px}}.v-application .v-dialog.message-dialog .message-dialog-body .row .col:first-child{padding-right:20px}.v-application .v-dialog.message-dialog .message-dialog-body .row .col:last-child{padding-left:20px}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-text div:first-child{font-size:16px;font-weight:600}}.v-application .v-dialog.message-dialog .message-dialog-text ul{padding-left:20px}@media only screen and (min-width:992px){.v-application .v-dialog.message-dialog .message-dialog-footer{margin-top:28px}}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-footer{position:absolute;bottom:0;left:0;width:100%;height:74px;padding:0 18px}}.v-application .v-dialog.message-dialog .message-dialog-footer .prev-button{color:var(--v-orange-base);cursor:pointer}.v-application .v-dialog.message-dialog .text-editor .ProseMirror{min-height:248px}.v-application .v-dialog.message-dialog .text-editor-buttons{left:8px;right:auto}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TimePickerItem.vue?vue&type=style&index=0&id=7467ec82&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".time-picker-item[data-v-7467ec82]{position:relative;height:32px;box-shadow:inset -1px -1px 0 #e0e0e0}.time-picker-item.free[data-v-7467ec82]{background-color:var(--v-success-base);cursor:pointer}.time-picker-item.active[data-v-7467ec82]{background:#fff repeating-linear-gradient(45deg,rgba(251,176,59,.6),rgba(251,176,59,.6) 7px,var(--v-orange-base) 0,var(--v-orange-base) 20px)}.time-picker-item.selected[data-v-7467ec82]{background-color:var(--v-orange-base)}.time-picker-item.unavailable[data-v-7467ec82]{background-color:#636363}.time-picker-item.first-half[data-v-7467ec82]:after{content:\\\"\\\";position:absolute;left:0;bottom:0;width:100%;height:1px;box-shadow:inset 0 -1px 0 #f7f7f7}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"steps\"},[_vm._ssrNode(\"<div class=\\\"steps-wrap\\\" data-v-307c13c8>\",\"</div>\",[_vm._ssrNode(\"<div id=\\\"steps-helper\\\" class=\\\"steps-helper\\\" data-v-307c13c8>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"steps-list\\\" data-v-307c13c8>\",\"</div>\",_vm._l((_vm.steps),function(step){return _vm._ssrNode(\"<div\"+(_vm._ssrAttr(\"id\",(\"step-\" + (step.id))))+(_vm._ssrClass(null,[\n            'step-item',\n            { 'step-item--active': step.id === _vm.activeItemId },\n            { 'step-item--link': _vm.itemLink.id === step.id } ]))+\" data-v-307c13c8>\",\"</div>\",[_vm._ssrNode(\"<div class=\\\"step-item-helper\\\" data-v-307c13c8><div class=\\\"step-item-number\\\" data-v-307c13c8><span data-v-307c13c8>\"+_vm._ssrEscape(_vm._s(step.id)+\".\")+\"</span></div> <div class=\\\"step-item-title\\\" data-v-307c13c8>\"+_vm._ssrEscape(_vm._s(_vm.$t(step.title)))+\"</div></div> \"),(_vm.itemLink.id === step.id)?_c('nuxt-link',{attrs:{\"to\":_vm.itemLink.path}}):_vm._e()],2)}),0)])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'Steps',\n  props: {\n    activeItemId: {\n      type: Number,\n      default: 1,\n    },\n    itemLink: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  mounted() {\n    const el = document.getElementById('steps-helper')\n    const activeEl = document.getElementById(`step-${this.activeItemId}`)\n\n    if (this.$vuetify.breakpoint.smAndDown && el && activeEl) {\n      const x = activeEl.getBoundingClientRect().left - 15\n\n      el.scrollTo({ left: x, behavior: 'smooth' })\n    }\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Steps.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Steps.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Steps.vue?vue&type=template&id=307c13c8&scoped=true&\"\nimport script from \"./Steps.vue?vue&type=script&lang=js&\"\nexport * from \"./Steps.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./Steps.vue?vue&type=style&index=0&id=307c13c8&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"307c13c8\",\n  \"7c31ffbf\"\n  \n)\n\nexport default component.exports", "export * from \"-!../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./TimePicker.vue?vue&type=style&index=0&id=69022ce1&lang=scss&scoped=true&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".time-picker[data-v-69022ce1]{--timepicker-sidebar-width:48px;position:relative;display:flex;flex-direction:column;flex-grow:1}@media only screen and (max-width:767px){.time-picker[data-v-69022ce1]{--timepicker-sidebar-width:30px}}.time-picker-helper[data-v-69022ce1],.time-picker-toggle[data-v-69022ce1]{display:flex}.time-picker-toggle[data-v-69022ce1]{justify-content:center;align-items:center;margin-bottom:12px}.time-picker-toggle .btn[data-v-69022ce1]{margin:0 10px;cursor:pointer}.time-picker-toggle .btn--disabled[data-v-69022ce1]{cursor:auto;opacity:.4}.time-picker-toggle .period[data-v-69022ce1]{min-width:120px;font-size:16px;font-weight:700;line-height:22px}.time-picker-wrap[data-v-69022ce1]{flex-grow:1;height:132px;overflow-y:auto;overflow-x:hidden}.time-picker-wrap-helper[data-v-69022ce1]{display:flex}.time-picker-left-bar .item[data-v-69022ce1],.time-picker-right-bar .item[data-v-69022ce1],.time-picker-top-bar .item[data-v-69022ce1]{font-size:12px;color:#575757;text-align:center;line-height:1.333;white-space:nowrap}@media only screen and (max-width:991px){.time-picker-left-bar .item[data-v-69022ce1],.time-picker-right-bar .item[data-v-69022ce1],.time-picker-top-bar .item[data-v-69022ce1]{font-size:10px}}.time-picker-left-bar[data-v-69022ce1],.time-picker-right-bar[data-v-69022ce1]{width:var(--timepicker-sidebar-width)}.time-picker-left-bar .item[data-v-69022ce1],.time-picker-right-bar .item[data-v-69022ce1]{height:64px}.time-picker-top-bar[data-v-69022ce1]{padding-right:8px}.time-picker-top-bar-helper[data-v-69022ce1]{display:flex;justify-content:space-around;width:calc(100% - var(--timepicker-sidebar-width)*2 - 2px)}.time-picker-top-bar .item[data-v-69022ce1]{display:flex;flex-wrap:wrap;justify-content:center;align-items:center;flex-basis:14.2857%;height:32px}@media only screen and (max-width:479px){.time-picker-top-bar .item[data-v-69022ce1]{font-size:10px}}.time-picker-graph[data-v-69022ce1]{width:calc(100% - 48px);display:flex;border-color:#e0e0e0;border-style:solid;border-width:1px 0 0 1px}@media only screen and (max-width:767px){.time-picker-graph[data-v-69022ce1]{width:calc(100% - 24px)}}.time-picker-graph .day[data-v-69022ce1]{flex-grow:1}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"calendar calendar--read-only\"},[_vm._ssrNode(\"<div class=\\\"calendar-title text-center font-weight-bold mb-2 mb-lg-3\\\">\"+_vm._ssrEscape(\"\\n    \"+_vm._s(_vm.$dayjs(_vm.currentDate).format('MMMM YYYY'))+\"\\n  \")+\"</div> \"),_c('v-calendar',{attrs:{\"weekdays\":_vm.weekday,\"locale\":_vm.locale,\"start\":_vm.$dayjs(_vm.currentDate).format('YYYY-MM-DD')},scopedSlots:_vm._u([{key:\"day-label\",fn:function(ref){\nvar date = ref.date;\nreturn [_c('calendar-date',{attrs:{\"date\":date,\"type\":_vm.type,\"item\":_vm.getDate(date)},on:{\"click-date\":function($event){return _vm.$emit('click-date', $event)}}})]}}])})],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport CalendarDate from '@/components/CalendarDate'\n\nexport default {\n  name: 'Calendar',\n  components: { CalendarDate },\n  props: {\n    currentDate: {\n      type: Object,\n      required: true,\n    },\n    items: {\n      type: Array,\n      required: true,\n    },\n    type: {\n      type: String,\n      required: true,\n    },\n  },\n  data() {\n    return {\n      weekday: [1, 2, 3, 4, 5, 6, 0],\n    }\n  },\n  computed: {\n    locale() {\n      return this.$i18n.locale\n    },\n  },\n  methods: {\n    getDate(date) {\n      return this.items.find((item) => item.date === date)\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Calendar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./Calendar.vue?vue&type=script&lang=js&\"", "import Vue from 'vue'\n\nexport default Vue.extend({\n  name: 'localable',\n\n  props: {\n    locale: String,\n  },\n\n  computed: {\n    currentLocale (): string {\n      return this.locale || this.$vuetify.lang.current\n    },\n  },\n})\n", "import Vue from 'vue'\n\nexport type <PERSON><PERSON><PERSON><PERSON> = (e: MouseEvent | TouchEvent) => any\n\nexport type MouseEvents = {\n  [event: string]: {\n    event: string\n    passive?: boolean\n    capture?: boolean\n    once?: boolean\n    stop?: boolean\n    prevent?: boolean\n    button?: number\n    result?: any\n  }\n}\n\nexport type MouseEventsMap = {\n  [event: string]: MouseHandler | MouseHandler[]\n}\n\nexport default Vue.extend({\n  name: 'mouse',\n\n  methods: {\n    getDefaultMouseEventHandlers (suffix: string, getEvent: MouseHandler): MouseEventsMap {\n      return this.getMouseEventHandlers({\n        ['click' + suffix]: { event: 'click' },\n        ['contextmenu' + suffix]: { event: 'contextmenu', prevent: true, result: false },\n        ['mousedown' + suffix]: { event: 'mousedown' },\n        ['mousemove' + suffix]: { event: 'mousemove' },\n        ['mouseup' + suffix]: { event: 'mouseup' },\n        ['mouseenter' + suffix]: { event: 'mouseenter' },\n        ['mouseleave' + suffix]: { event: 'mouseleave' },\n        ['touchstart' + suffix]: { event: 'touchstart' },\n        ['touchmove' + suffix]: { event: 'touchmove' },\n        ['touchend' + suffix]: { event: 'touchend' },\n      }, getEvent)\n    },\n    getMouseEventHandlers (events: MouseEvents, getEvent: MouseHandler): MouseEventsMap {\n      const on: MouseEventsMap = {}\n\n      for (const event in events) {\n        const eventOptions = events[event]\n\n        if (!this.$listeners[event]) continue\n\n        // TODO somehow pull in modifiers\n\n        const prefix = eventOptions.passive ? '&' : ((eventOptions.once ? '~' : '') + (eventOptions.capture ? '!' : ''))\n        const key = prefix + eventOptions.event\n\n        const handler: MouseHandler = e => {\n          const mouseEvent: MouseEvent = e as MouseEvent\n          if (eventOptions.button === undefined || (mouseEvent.buttons > 0 && mouseEvent.button === eventOptions.button)) {\n            if (eventOptions.prevent) {\n              e.preventDefault()\n            }\n            if (eventOptions.stop) {\n              e.stopPropagation()\n            }\n            this.$emit(event, getEvent(e))\n          }\n\n          return eventOptions.result\n        }\n\n        if (key in on) {\n          /* istanbul ignore next */\n          if (Array.isArray(on[key])) {\n            (on[key] as MouseHandler[]).push(handler)\n          } else {\n            on[key] = [on[key], handler] as MouseHandler[]\n          }\n        } else {\n          on[key] = handler\n        }\n      }\n\n      return on\n    },\n  },\n})\n", "function createUTCDate (year: number, month = 0, day = 1) {\n  let date\n  if (year < 100 && year >= 0) {\n    date = new Date(Date.UTC(year, month, day))\n    if (isFinite(date.getUTCFullYear())) {\n      date.setUTCFullYear(year)\n    }\n  } else {\n    date = new Date(Date.UTC(year, month, day))\n  }\n\n  return date\n}\n\nfunction firstWeekOffset (year: number, firstDayOfWeek: number, firstDayOfYear: number) {\n  const firstWeekDayInFirstWeek = 7 + firstDayOfWeek - firstDayOfYear\n  const firstWeekDayOfYear = (7 + createUTCDate(year, 0, firstWeekDayInFirstWeek).getUTCDay() - firstDayOfWeek) % 7\n\n  return -firstWeekDayOfYear + firstWeekDayInFirstWeek - 1\n}\n\nfunction dayOfYear (year: number, month: number, day: number, firstDayOfWeek: number) {\n  let dayOfYear = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334][month]\n  if (month > 1 && isLeapYear(year)) {\n    dayOfYear++\n  }\n\n  return dayOfYear + day\n}\n\nfunction weeksInYear (year: number, firstDayOfWeek: number, firstDayOfYear: number) {\n  const weekOffset = firstWeekOffset(year, firstDayOfWeek, firstDayOfYear)\n  const weekOffsetNext = firstWeekOffset(year + 1, firstDayOfWeek, firstDayOfYear)\n  const daysInYear = isLeapYear(year) ? 366 : 365\n\n  return (daysInYear - weekOffset + weekOffsetNext) / 7\n}\n\nexport function weekNumber (year: number, month: number, day: number, firstDayOfWeek: number, localeFirstDayOfYear: number): number {\n  const weekOffset = firstWeekOffset(year, firstDayOfWeek, localeFirstDayOfYear)\n  const week = Math.ceil((dayOfYear(year, month, day, firstDayOfWeek) - weekOffset) / 7)\n\n  if (week < 1) {\n    return week + weeksInYear(year - 1, firstDayOfWeek, localeFirstDayOfYear)\n  } else if (week > weeksInYear(year, firstDayOfWeek, localeFirstDayOfYear)) {\n    return week - weeksInYear(year, firstDayOfWeek, localeFirstDayOfYear)\n  } else {\n    return week\n  }\n}\n\nexport function isLeapYear (year: number): boolean {\n  return ((year % 4 === 0) && (year % 100 !== 0)) || (year % 400 === 0)\n}\n", "import { CalendarTimestamp, CalendarFormatter } from 'vuetify/types'\nimport { isLeapYear } from '../../../util/dateTimeUtils'\n\nexport const PARSE_REGEX = /^(\\d{4})-(\\d{1,2})(-(\\d{1,2}))?([^\\d]+(\\d{1,2}))?(:(\\d{1,2}))?(:(\\d{1,2}))?$/\nexport const PARSE_TIME = /(\\d\\d?)(:(\\d\\d?)|)(:(\\d\\d?)|)/\n\nexport const DAYS_IN_MONTH: number[] = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]\nexport const DAYS_IN_MONTH_LEAP: number[] = [0, 31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]\nexport const DAYS_IN_MONTH_MIN = 28\nexport const DAYS_IN_MONTH_MAX = 31\nexport const MONTH_MAX = 12\nexport const MONTH_MIN = 1\nexport const DAY_MIN = 1\nexport const DAYS_IN_WEEK = 7\nexport const MINUTES_IN_HOUR = 60\nexport const MINUTE_MAX = 59\nexport const MINUTES_IN_DAY = 24 * 60\nexport const HOURS_IN_DAY = 24\nexport const HOUR_MAX = 23\nexport const FIRST_HOUR = 0\nexport const OFFSET_YEAR = 10000\nexport const OFFSET_MONTH = 100\nexport const OFFSET_HOUR = 100\nexport const OFFSET_TIME = 10000\n\ntype CalendarTimestampFormatOptions = (timestamp: CalendarTimestamp, short: boolean) => object\ntype CalendarTimestampOperation = (timestamp: CalendarTimestamp) => CalendarTimestamp\nexport type VTime = number | string | {\n  hour: number\n  minute: number\n}\n\nexport type VTimestampInput = number | string | Date;\n\nexport function getStartOfWeek (timestamp: CalendarTimestamp, weekdays: number[], today?: CalendarTimestamp): CalendarTimestamp {\n  const start = copyTimestamp(timestamp)\n  findWeekday(start, weekdays[0], prevDay)\n  updateFormatted(start)\n  if (today) {\n    updateRelative(start, today, start.hasTime)\n  }\n\n  return start\n}\n\nexport function getEndOfWeek (timestamp: CalendarTimestamp, weekdays: number[], today?: CalendarTimestamp): CalendarTimestamp {\n  const end = copyTimestamp(timestamp)\n  findWeekday(end, weekdays[weekdays.length - 1])\n  updateFormatted(end)\n  if (today) {\n    updateRelative(end, today, end.hasTime)\n  }\n\n  return end\n}\n\nexport function getStartOfMonth (timestamp: CalendarTimestamp): CalendarTimestamp {\n  const start = copyTimestamp(timestamp)\n  start.day = DAY_MIN\n  updateWeekday(start)\n  updateFormatted(start)\n\n  return start\n}\n\nexport function getEndOfMonth (timestamp: CalendarTimestamp): CalendarTimestamp {\n  const end = copyTimestamp(timestamp)\n  end.day = daysInMonth(end.year, end.month)\n  updateWeekday(end)\n  updateFormatted(end)\n\n  return end\n}\n\nexport function validateTime (input: any): input is VTime {\n  return (typeof input === 'number' && isFinite(input)) ||\n    (!!PARSE_TIME.exec(input)) ||\n    (typeof input === 'object' && isFinite(input.hour) && isFinite(input.minute))\n}\n\nexport function parseTime (input: any): number | false {\n  if (typeof input === 'number') {\n    // when a number is given, it's minutes since 12:00am\n    return input\n  } else if (typeof input === 'string') {\n    // when a string is given, it's a hh:mm:ss format where seconds are optional\n    const parts = PARSE_TIME.exec(input)\n    if (!parts) {\n      return false\n    }\n\n    return parseInt(parts[1]) * 60 + parseInt(parts[3] || 0)\n  } else if (typeof input === 'object') {\n    // when an object is given, it must have hour and minute\n    if (typeof input.hour !== 'number' || typeof input.minute !== 'number') {\n      return false\n    }\n\n    return input.hour * 60 + input.minute\n  } else {\n    // unsupported type\n    return false\n  }\n}\n\nexport function validateTimestamp (input: any): input is VTimestampInput {\n  return (typeof input === 'number' && isFinite(input)) ||\n    (typeof input === 'string' && !!PARSE_REGEX.exec(input)) ||\n    (input instanceof Date)\n}\n\nexport function parseTimestamp (input: VTimestampInput, required?: false, now?: CalendarTimestamp): CalendarTimestamp | null\nexport function parseTimestamp (input: VTimestampInput, required: true, now?: CalendarTimestamp): CalendarTimestamp\nexport function parseTimestamp (input: VTimestampInput, required = false, now?: CalendarTimestamp): CalendarTimestamp | null {\n  if (typeof input === 'number' && isFinite(input)) {\n    input = new Date(input)\n  }\n\n  if (input instanceof Date) {\n    const date: CalendarTimestamp = parseDate(input)\n\n    if (now) {\n      updateRelative(date, now, date.hasTime)\n    }\n\n    return date\n  }\n\n  if (typeof input !== 'string') {\n    if (required) {\n      throw new Error(`${input} is not a valid timestamp. It must be a Date, number of seconds since Epoch, or a string in the format of YYYY-MM-DD or YYYY-MM-DD hh:mm. Zero-padding is optional and seconds are ignored.`)\n    }\n    return null\n  }\n\n  // YYYY-MM-DD hh:mm:ss\n  const parts = PARSE_REGEX.exec(input)\n\n  if (!parts) {\n    if (required) {\n      throw new Error(`${input} is not a valid timestamp. It must be a Date, number of seconds since Epoch, or a string in the format of YYYY-MM-DD or YYYY-MM-DD hh:mm. Zero-padding is optional and seconds are ignored.`)\n    }\n\n    return null\n  }\n\n  const timestamp: CalendarTimestamp = {\n    date: input,\n    time: '',\n    year: parseInt(parts[1]),\n    month: parseInt(parts[2]),\n    day: parseInt(parts[4]) || 1,\n    hour: parseInt(parts[6]) || 0,\n    minute: parseInt(parts[8]) || 0,\n    weekday: 0,\n    hasDay: !!parts[4],\n    hasTime: !!(parts[6] && parts[8]),\n    past: false,\n    present: false,\n    future: false,\n  }\n\n  updateWeekday(timestamp)\n  updateFormatted(timestamp)\n\n  if (now) {\n    updateRelative(timestamp, now, timestamp.hasTime)\n  }\n\n  return timestamp\n}\n\nexport function parseDate (date: Date): CalendarTimestamp {\n  return updateFormatted({\n    date: '',\n    time: '',\n    year: date.getFullYear(),\n    month: date.getMonth() + 1,\n    day: date.getDate(),\n    weekday: date.getDay(),\n    hour: date.getHours(),\n    minute: date.getMinutes(),\n    hasDay: true,\n    hasTime: true,\n    past: false,\n    present: true,\n    future: false,\n  })\n}\n\nexport function getDayIdentifier (timestamp: { year: number, month: number, day: number }): number {\n  return timestamp.year * OFFSET_YEAR + timestamp.month * OFFSET_MONTH + timestamp.day\n}\n\nexport function getTimeIdentifier (timestamp: { hour: number, minute: number }): number {\n  return timestamp.hour * OFFSET_HOUR + timestamp.minute\n}\n\nexport function getTimestampIdentifier (timestamp: CalendarTimestamp): number {\n  return getDayIdentifier(timestamp) * OFFSET_TIME + getTimeIdentifier(timestamp)\n}\n\nexport function updateRelative (timestamp: CalendarTimestamp, now: CalendarTimestamp, time = false): CalendarTimestamp {\n  let a = getDayIdentifier(now)\n  let b = getDayIdentifier(timestamp)\n  let present = a === b\n\n  if (timestamp.hasTime && time && present) {\n    a = getTimeIdentifier(now)\n    b = getTimeIdentifier(timestamp)\n    present = a === b\n  }\n\n  timestamp.past = b < a\n  timestamp.present = present\n  timestamp.future = b > a\n\n  return timestamp\n}\n\nexport function isTimedless (input: VTimestampInput): input is (Date | number) {\n  return (input instanceof Date) || (typeof input === 'number' && isFinite(input))\n}\n\nexport function updateHasTime (timestamp: CalendarTimestamp, hasTime: boolean, now?: CalendarTimestamp): CalendarTimestamp {\n  if (timestamp.hasTime !== hasTime) {\n    timestamp.hasTime = hasTime\n    if (!hasTime) {\n      timestamp.hour = HOUR_MAX\n      timestamp.minute = MINUTE_MAX\n      timestamp.time = getTime(timestamp)\n    }\n    if (now) {\n      updateRelative(timestamp, now, timestamp.hasTime)\n    }\n  }\n\n  return timestamp\n}\n\nexport function updateMinutes (timestamp: CalendarTimestamp, minutes: number, now?: CalendarTimestamp): CalendarTimestamp {\n  timestamp.hasTime = true\n  timestamp.hour = Math.floor(minutes / MINUTES_IN_HOUR)\n  timestamp.minute = minutes % MINUTES_IN_HOUR\n  timestamp.time = getTime(timestamp)\n  if (now) {\n    updateRelative(timestamp, now, true)\n  }\n\n  return timestamp\n}\n\nexport function updateWeekday (timestamp: CalendarTimestamp): CalendarTimestamp {\n  timestamp.weekday = getWeekday(timestamp)\n\n  return timestamp\n}\n\nexport function updateFormatted (timestamp: CalendarTimestamp): CalendarTimestamp {\n  timestamp.time = getTime(timestamp)\n  timestamp.date = getDate(timestamp)\n\n  return timestamp\n}\n\nexport function getWeekday (timestamp: CalendarTimestamp): number {\n  if (timestamp.hasDay) {\n    const _ = Math.floor\n    const k = timestamp.day\n    const m = ((timestamp.month + 9) % MONTH_MAX) + 1\n    const C = _(timestamp.year / 100)\n    const Y = (timestamp.year % 100) - (timestamp.month <= 2 ? 1 : 0)\n\n    return (((k + _(2.6 * m - 0.2) - 2 * C + Y + _(Y / 4) + _(C / 4)) % 7) + 7) % 7\n  }\n\n  return timestamp.weekday\n}\n\nexport function daysInMonth (year: number, month: number) {\n  return isLeapYear(year) ? DAYS_IN_MONTH_LEAP[month] : DAYS_IN_MONTH[month]\n}\n\nexport function copyTimestamp (timestamp: CalendarTimestamp): CalendarTimestamp {\n  const { date, time, year, month, day, weekday, hour, minute, hasDay, hasTime, past, present, future } = timestamp\n\n  return { date, time, year, month, day, weekday, hour, minute, hasDay, hasTime, past, present, future }\n}\n\nexport function padNumber (x: number, length: number): string {\n  let padded = String(x)\n  while (padded.length < length) {\n    padded = '0' + padded\n  }\n\n  return padded\n}\n\nexport function getDate (timestamp: CalendarTimestamp): string {\n  let str = `${padNumber(timestamp.year, 4)}-${padNumber(timestamp.month, 2)}`\n\n  if (timestamp.hasDay) str += `-${padNumber(timestamp.day, 2)}`\n\n  return str\n}\n\nexport function getTime (timestamp: CalendarTimestamp): string {\n  if (!timestamp.hasTime) {\n    return ''\n  }\n\n  return `${padNumber(timestamp.hour, 2)}:${padNumber(timestamp.minute, 2)}`\n}\n\nexport function nextMinutes (timestamp: CalendarTimestamp, minutes: number): CalendarTimestamp {\n  timestamp.minute += minutes\n  while (timestamp.minute > MINUTES_IN_HOUR) {\n    timestamp.minute -= MINUTES_IN_HOUR\n    timestamp.hour++\n    if (timestamp.hour >= HOURS_IN_DAY) {\n      nextDay(timestamp)\n      timestamp.hour = FIRST_HOUR\n    }\n  }\n\n  return timestamp\n}\n\nexport function nextDay (timestamp: CalendarTimestamp): CalendarTimestamp {\n  timestamp.day++\n  timestamp.weekday = (timestamp.weekday + 1) % DAYS_IN_WEEK\n  if (timestamp.day > DAYS_IN_MONTH_MIN && timestamp.day > daysInMonth(timestamp.year, timestamp.month)) {\n    timestamp.day = DAY_MIN\n    timestamp.month++\n    if (timestamp.month > MONTH_MAX) {\n      timestamp.month = MONTH_MIN\n      timestamp.year++\n    }\n  }\n\n  return timestamp\n}\n\nexport function prevDay (timestamp: CalendarTimestamp): CalendarTimestamp {\n  timestamp.day--\n  timestamp.weekday = (timestamp.weekday + 6) % DAYS_IN_WEEK\n  if (timestamp.day < DAY_MIN) {\n    timestamp.month--\n    if (timestamp.month < MONTH_MIN) {\n      timestamp.year--\n      timestamp.month = MONTH_MAX\n    }\n    timestamp.day = daysInMonth(timestamp.year, timestamp.month)\n  }\n\n  return timestamp\n}\n\nexport function relativeDays (\n  timestamp: CalendarTimestamp,\n  mover: CalendarTimestampOperation = nextDay,\n  days = 1\n): CalendarTimestamp {\n  while (--days >= 0) mover(timestamp)\n\n  return timestamp\n}\n\nexport function diffMinutes (min: CalendarTimestamp, max: CalendarTimestamp) {\n  const Y = (max.year - min.year) * 525600\n  const M = (max.month - min.month) * 43800\n  const D = (max.day - min.day) * 1440\n  const h = (max.hour - min.hour) * 60\n  const m = (max.minute - min.minute)\n\n  return Y + M + D + h + m\n}\n\nexport function findWeekday (timestamp: CalendarTimestamp, weekday: number,\n  mover: CalendarTimestampOperation = nextDay, maxDays = 6): CalendarTimestamp {\n  while (timestamp.weekday !== weekday && --maxDays >= 0) mover(timestamp)\n\n  return timestamp\n}\n\nexport function getWeekdaySkips (weekdays: number[]): number[] {\n  const skips: number[] = [1, 1, 1, 1, 1, 1, 1]\n  const filled: number[] = [0, 0, 0, 0, 0, 0, 0]\n  for (let i = 0; i < weekdays.length; i++) {\n    filled[weekdays[i]] = 1\n  }\n  for (let k = 0; k < DAYS_IN_WEEK; k++) {\n    let skip = 1\n    for (let j = 1; j < DAYS_IN_WEEK; j++) {\n      const next = (k + j) % DAYS_IN_WEEK\n      if (filled[next]) {\n        break\n      }\n      skip++\n    }\n    skips[k] = filled[k] * skip\n  }\n\n  return skips\n}\n\nexport function timestampToDate (timestamp: CalendarTimestamp): Date {\n  const time = `${padNumber(timestamp.hour, 2)}:${padNumber(timestamp.minute, 2)}`\n  const date = timestamp.date\n\n  return new Date(`${date}T${time}:00+00:00`)\n}\n\nexport function createDayList (\n  start: CalendarTimestamp,\n  end: CalendarTimestamp,\n  now: CalendarTimestamp,\n  weekdaySkips: number[],\n  max = 42,\n  min = 0\n): CalendarTimestamp[] {\n  const stop = getDayIdentifier(end)\n  const days: CalendarTimestamp[] = []\n  let current = copyTimestamp(start)\n  let currentIdentifier = 0\n  let stopped = currentIdentifier === stop\n\n  if (stop < getDayIdentifier(start)) {\n    throw new Error('End date is earlier than start date.')\n  }\n\n  while ((!stopped || days.length < min) && days.length < max) {\n    currentIdentifier = getDayIdentifier(current)\n    stopped = stopped || currentIdentifier === stop\n    if (weekdaySkips[current.weekday] === 0) {\n      current = nextDay(current)\n      continue\n    }\n    const day = copyTimestamp(current)\n    updateFormatted(day)\n    updateRelative(day, now)\n    days.push(day)\n    current = relativeDays(current, nextDay, weekdaySkips[current.weekday])\n  }\n\n  if (!days.length) throw new Error('No dates found using specified start date, end date, and weekdays.')\n\n  return days\n}\n\nexport function createIntervalList (timestamp: CalendarTimestamp, first: number,\n  minutes: number, count: number, now?: CalendarTimestamp): CalendarTimestamp[] {\n  const intervals: CalendarTimestamp[] = []\n\n  for (let i = 0; i < count; i++) {\n    const mins = first + (i * minutes)\n    const int = copyTimestamp(timestamp)\n    intervals.push(updateMinutes(int, mins, now))\n  }\n\n  return intervals\n}\n\nexport function createNativeLocaleFormatter (locale: string, getOptions: CalendarTimestampFormatOptions): CalendarFormatter {\n  const emptyFormatter: CalendarFormatter = (_t, _s) => ''\n\n  if (typeof Intl === 'undefined' || typeof Intl.DateTimeFormat === 'undefined') {\n    return emptyFormatter\n  }\n\n  return (timestamp, short) => {\n    try {\n      const intlFormatter = new Intl.DateTimeFormat(locale || undefined, getOptions(timestamp, short))\n\n      return intlFormatter.format(timestampToDate(timestamp))\n    } catch (e) {\n      return ''\n    }\n  }\n}\n", "import Vue from 'vue'\n\nimport {\n  validateTimestamp,\n  parseTimestamp,\n  parseDate,\n} from '../util/timestamp'\nimport { CalendarTimestamp } from 'vuetify/types'\n\nexport default Vue.extend({\n  name: 'times',\n\n  props: {\n    now: {\n      type: String,\n      validator: validateTimestamp,\n    },\n  },\n\n  data: () => ({\n    times: {\n      now: parseTimestamp('0000-00-00 00:00', true),\n      today: parseTimestamp('0000-00-00', true),\n    },\n  }),\n\n  computed: {\n    parsedNow (): CalendarTimestamp | null {\n      return this.now ? parseTimestamp(this.now, true) : null\n    },\n  },\n\n  watch: {\n    parsedNow: 'updateTimes',\n  },\n\n  created () {\n    this.updateTimes()\n    this.setPresent()\n  },\n\n  methods: {\n    setPresent (): void {\n      this.times.now.present = this.times.today.present = true\n      this.times.now.past = this.times.today.past = false\n      this.times.now.future = this.times.today.future = false\n    },\n    updateTimes (): void {\n      const now: CalendarTimestamp = this.parsedNow || this.getNow()\n      this.updateDay(now, this.times.now)\n      this.updateTime(now, this.times.now)\n      this.updateDay(now, this.times.today)\n    },\n    getNow (): CalendarTimestamp {\n      return parseDate(new Date())\n    },\n    updateDay (now: CalendarTimestamp, target: CalendarTimestamp): void {\n      if (now.date !== target.date) {\n        target.year = now.year\n        target.month = now.month\n        target.day = now.day\n        target.weekday = now.weekday\n        target.date = now.date\n      }\n    },\n    updateTime (now: CalendarTimestamp, target: CalendarTimestamp): void {\n      if (now.time !== target.time) {\n        target.hour = now.hour\n        target.minute = now.minute\n        target.time = now.time\n      }\n    },\n  },\n})\n", "import { CalendarEventParsed, CalendarEventVisual, CalendarTimestamp } from 'vuetify/types'\nimport { getTimestampIdentifier } from '../util/timestamp'\n\nconst MILLIS_IN_DAY = 86400000\n\nexport type GetRange = (event: CalendarEventParsed) => [number, number]\n\nexport function getVisuals (events: CalendarEventParsed[], minStart = 0): CalendarEventVisual[] {\n  const visuals = events.map(event => ({\n    event,\n    columnCount: 0,\n    column: 0,\n    left: 0,\n    width: 100,\n  }))\n\n  visuals.sort((a, b) => {\n    return (Math.max(minStart, a.event.startTimestampIdentifier) - Math.max(minStart, b.event.startTimestampIdentifier)) ||\n           (b.event.endTimestampIdentifier - a.event.endTimestampIdentifier)\n  })\n\n  return visuals\n}\n\nexport interface ColumnGroup {\n  start: number\n  end: number\n  visuals: CalendarEventVisual[]\n}\n\nexport function hasOverlap (s0: number, e0: number, s1: number, e1: number, exclude = true): boolean {\n  return exclude ? !(s0 >= e1 || e0 <= s1) : !(s0 > e1 || e0 < s1)\n}\n\nexport function setColumnCount (groups: ColumnGroup[]) {\n  groups.forEach(group => {\n    group.visuals.forEach(groupVisual => {\n      groupVisual.columnCount = groups.length\n    })\n  })\n}\n\nexport function getRange (event: CalendarEventParsed): [number, number] {\n  return [event.startTimestampIdentifier, event.endTimestampIdentifier]\n}\n\nexport function getDayRange (event: CalendarEventParsed): [number, number] {\n  return [event.startIdentifier, event.endIdentifier]\n}\n\nexport function getNormalizedRange (event: CalendarEventParsed, dayStart: number): [number, number] {\n  return [Math.max(dayStart, event.startTimestampIdentifier), Math.min(dayStart + MILLIS_IN_DAY, event.endTimestampIdentifier)]\n}\n\nexport function getOpenGroup (groups: ColumnGroup[], start: number, end: number, timed: boolean) {\n  for (let i = 0; i < groups.length; i++) {\n    const group = groups[i]\n    let intersected = false\n\n    if (hasOverlap(start, end, group.start, group.end, timed)) {\n      for (let k = 0; k < group.visuals.length; k++) {\n        const groupVisual = group.visuals[k]\n        const [groupStart, groupEnd] = timed ? getRange(groupVisual.event) : getDayRange(groupVisual.event)\n\n        if (hasOverlap(start, end, groupStart, groupEnd, timed)) {\n          intersected = true\n          break\n        }\n      }\n    }\n\n    if (!intersected) {\n      return i\n    }\n  }\n\n  return -1\n}\n\nexport function getOverlapGroupHandler (firstWeekday: number) {\n  const handler = {\n    groups: [] as ColumnGroup[],\n    min: -1,\n    max: -1,\n    reset: () => {\n      handler.groups = []\n      handler.min = handler.max = -1\n    },\n    getVisuals: (day: CalendarTimestamp, dayEvents: CalendarEventParsed[], timed: boolean, reset = false) => {\n      if (day.weekday === firstWeekday || reset) {\n        handler.reset()\n      }\n\n      const dayStart = getTimestampIdentifier(day)\n      const visuals = getVisuals(dayEvents, dayStart)\n\n      visuals.forEach(visual => {\n        const [start, end] = timed ? getRange(visual.event) : getDayRange(visual.event)\n\n        if (handler.groups.length > 0 && !hasOverlap(start, end, handler.min, handler.max, timed)) {\n          setColumnCount(handler.groups)\n          handler.reset()\n        }\n\n        let targetGroup = getOpenGroup(handler.groups, start, end, timed)\n\n        if (targetGroup === -1) {\n          targetGroup = handler.groups.length\n\n          handler.groups.push({ start, end, visuals: [] })\n        }\n\n        const target = handler.groups[targetGroup]\n        target.visuals.push(visual)\n        target.start = Math.min(target.start, start)\n        target.end = Math.max(target.end, end)\n\n        visual.column = targetGroup\n\n        if (handler.min === -1) {\n          handler.min = start\n          handler.max = end\n        } else {\n          handler.min = Math.min(handler.min, start)\n          handler.max = Math.max(handler.max, end)\n        }\n      })\n\n      setColumnCount(handler.groups)\n\n      if (timed) {\n        handler.reset()\n      }\n\n      return visuals\n    },\n  }\n\n  return handler\n}\n", "import { CalendarEventOverlapMode, CalendarEventVisual } from 'vuetify/types'\nimport { getOverlapGroupHandler, getVisuals, hasOverlap, getNormalizedRange } from './common'\nimport { getTimestampIdentifier } from '../util/timestamp'\n\ninterface Group {\n  start: number\n  end: number\n  visuals: CalendarEventVisual[]\n}\n\ninterface Node {\n  parent: Node | null\n  sibling: boolean\n  index: number\n  visual: CalendarEventVisual\n  start: number\n  end: number\n  children: Node[]\n}\n\nconst FULL_WIDTH = 100\n\nconst DEFAULT_OFFSET = 5\n\nconst WIDTH_MULTIPLIER = 1.7\n\n/**\n * Variation of column mode where events can be stacked. The priority of this\n * mode is to stack events together taking up the least amount of space while\n * trying to ensure the content of the event is always visible as well as its\n * start and end. A sibling column has intersecting event content and must be\n * placed beside each other. Non-sibling columns are offset by 5% from the\n * previous column. The width is scaled by 1.7 so the events overlap and\n * whitespace is reduced. If there is a hole in columns the event width is\n * scaled up so it intersects with the next column. The columns have equal\n * width in the space they are given. If the event doesn't have any to the\n * right of it that intersect with it's content it's right side is extended\n * to the right side.\n */\n\nexport const stack: CalendarEventOverlapMode = (events, firstWeekday, overlapThreshold) => {\n  const handler = getOverlapGroupHandler(firstWeekday)\n\n  // eslint-disable-next-line max-statements\n  return (day, dayEvents, timed, reset) => {\n    if (!timed) {\n      return handler.getVisuals(day, dayEvents, timed, reset)\n    }\n\n    const dayStart = getTimestampIdentifier(day)\n    const visuals = getVisuals(dayEvents, dayStart)\n    const groups = getGroups(visuals, dayStart)\n\n    for (const group of groups) {\n      const nodes: Node[] = []\n\n      for (const visual of group.visuals) {\n        const child = getNode(visual, dayStart)\n        const index = getNextIndex(child, nodes)\n\n        if (index === false) {\n          const parent = getParent(child, nodes)\n          if (parent) {\n            child.parent = parent\n            child.sibling = hasOverlap(child.start, child.end, parent.start, addTime(parent.start, overlapThreshold))\n            child.index = parent.index + 1\n            parent.children.push(child)\n          }\n        } else {\n          const [parent] = getOverlappingRange(child, nodes, index - 1, index - 1)\n          const children = getOverlappingRange(child, nodes, index + 1, index + nodes.length, true)\n\n          child.children = children\n          child.index = index\n\n          if (parent) {\n            child.parent = parent\n            child.sibling = hasOverlap(child.start, child.end, parent.start, addTime(parent.start, overlapThreshold))\n            parent.children.push(child)\n          }\n\n          for (const grand of children) {\n            if (grand.parent === parent) {\n              grand.parent = child\n            }\n\n            const grandNext = grand.index - child.index <= 1\n            if (grandNext && child.sibling &&\n              hasOverlap(child.start, addTime(child.start, overlapThreshold), grand.start, grand.end)) {\n              grand.sibling = true\n            }\n          }\n        }\n\n        nodes.push(child)\n      }\n\n      calculateBounds(nodes, overlapThreshold)\n    }\n\n    visuals.sort((a, b) => (a.left - b.left) || (a.event.startTimestampIdentifier - b.event.startTimestampIdentifier))\n\n    return visuals\n  }\n}\n\nfunction calculateBounds (nodes: Node[], overlapThreshold: number) {\n  for (const node of nodes) {\n    const { visual, parent } = node\n    const columns = getMaxChildIndex(node) + 1\n    const spaceLeft = parent ? parent.visual.left : 0\n    const spaceWidth = FULL_WIDTH - spaceLeft\n    const offset = Math.min(DEFAULT_OFFSET, FULL_WIDTH / columns)\n    const columnWidthMultiplier = getColumnWidthMultiplier(node, nodes)\n    const columnOffset = spaceWidth / (columns - node.index + 1)\n    const columnWidth = spaceWidth / (columns - node.index + (node.sibling ? 1 : 0)) * columnWidthMultiplier\n\n    if (parent) {\n      visual.left = node.sibling\n        ? spaceLeft + columnOffset\n        : spaceLeft + offset\n    }\n\n    visual.width = hasFullWidth(node, nodes, overlapThreshold)\n      ? FULL_WIDTH - visual.left\n      : Math.min(FULL_WIDTH - visual.left, columnWidth * WIDTH_MULTIPLIER)\n  }\n}\n\nfunction getColumnWidthMultiplier (node: Node, nodes: Node[]): number {\n  if (!node.children.length) {\n    return 1\n  }\n\n  const maxColumn = node.index + nodes.length\n  const minColumn = node.children.reduce((min, c) => Math.min(min, c.index), maxColumn)\n\n  return minColumn - node.index\n}\n\nfunction getOverlappingIndices (node: Node, nodes: Node[]): number[] {\n  const indices: number[] = []\n  for (const other of nodes) {\n    if (hasOverlap(node.start, node.end, other.start, other.end)) {\n      indices.push(other.index)\n    }\n  }\n  return indices\n}\n\nfunction getNextIndex (node: Node, nodes: Node[]): number | false {\n  const indices = getOverlappingIndices(node, nodes)\n  indices.sort()\n\n  for (let i = 0; i < indices.length; i++) {\n    if (i < indices[i]) {\n      return i\n    }\n  }\n  return false\n}\n\nfunction getOverlappingRange (node: Node, nodes: Node[], indexMin: number, indexMax: number, returnFirstColumn = false): Node[] {\n  const overlapping: Node[] = []\n  for (const other of nodes) {\n    if (other.index >= indexMin && other.index <= indexMax && hasOverlap(node.start, node.end, other.start, other.end)) {\n      overlapping.push(other)\n    }\n  }\n  if (returnFirstColumn && overlapping.length > 0) {\n    const first = overlapping.reduce((min, n) => Math.min(min, n.index), overlapping[0].index)\n    return overlapping.filter(n => n.index === first)\n  }\n  return overlapping\n}\n\nfunction getParent (node: Node, nodes: Node[]): Node | null {\n  let parent: Node | null = null\n  for (const other of nodes) {\n    if (hasOverlap(node.start, node.end, other.start, other.end) && (parent === null || other.index > parent.index)) {\n      parent = other\n    }\n  }\n  return parent\n}\n\nfunction hasFullWidth (node: Node, nodes: Node[], overlapThreshold: number): boolean {\n  for (const other of nodes) {\n    if (other !== node &&\n      other.index > node.index &&\n      hasOverlap(node.start, addTime(node.start, overlapThreshold), other.start, other.end)) {\n      return false\n    }\n  }\n\n  return true\n}\n\nfunction getGroups (visuals: CalendarEventVisual[], dayStart: number): Group[] {\n  const groups: Group[] = []\n\n  for (const visual of visuals) {\n    const [start, end] = getNormalizedRange(visual.event, dayStart)\n    let added = false\n\n    for (const group of groups) {\n      if (hasOverlap(start, end, group.start, group.end)) {\n        group.visuals.push(visual)\n        group.end = Math.max(group.end, end)\n        added = true\n        break\n      }\n    }\n\n    if (!added) {\n      groups.push({ start, end, visuals: [visual] })\n    }\n  }\n\n  return groups\n}\n\nfunction getNode (visual: CalendarEventVisual, dayStart: number): Node {\n  const [start, end] = getNormalizedRange(visual.event, dayStart)\n\n  return {\n    parent: null,\n    sibling: true,\n    index: 0,\n    visual,\n    start,\n    end,\n    children: [],\n  }\n}\n\nfunction getMaxChildIndex (node: Node): number {\n  let max = node.index\n  for (const child of node.children) {\n    const childMax = getMaxChildIndex(child)\n    if (childMax > max) {\n      max = childMax\n    }\n  }\n  return max\n}\n\nfunction addTime (identifier: number, minutes: number): number {\n  const removeMinutes = identifier % 100\n  const totalMinutes = removeMinutes + minutes\n  const addHours = Math.floor(totalMinutes / 60)\n  const addMinutes = totalMinutes % 60\n\n  return identifier - removeMinutes + addHours * 100 + addMinutes\n}\n", "import { CalendarEventOverlapMode } from 'vuetify/types'\nimport { getOverlapGroupHandler } from './common'\n\nconst FULL_WIDTH = 100\n\nexport const column: CalendarEventOverlapMode = (events, firstWeekday, overlapThreshold) => {\n  const handler = getOverlapGroupHandler(firstWeekday)\n\n  return (day, dayEvents, timed, reset) => {\n    const visuals = handler.getVisuals(day, dayEvents, timed, reset)\n\n    if (timed) {\n      visuals.forEach(visual => {\n        visual.left = visual.column * FULL_WIDTH / visual.columnCount\n        visual.width = FULL_WIDTH / visual.columnCount\n      })\n    }\n\n    return visuals\n  }\n}\n", "import { CalendarEventOverlapMode } from 'vuetify/types'\nimport { stack } from './stack'\nimport { column } from './column'\n\nexport const CalendarEventOverlapModes: Record<string, CalendarEventOverlapMode> = {\n  stack,\n  column,\n}\n", "\nimport { validateTimestamp, parseDate, DAYS_IN_WEEK, validateTime } from './timestamp'\nimport { PropType } from 'vue'\nimport { CalendarEvent, CalendarFormatter, CalendarTimestamp, CalendarEventOverlapMode, CalendarEventNameFunction, CalendarEventColorFunction, CalendarEventCategoryFunction, CalendarEventTimedFunction, CalendarCategoryTextFunction, CalendarCategory } from 'vuetify/types'\nimport { CalendarEventOverlapModes } from '../modes'\nimport { PropValidator } from 'vue/types/options'\n\nexport default {\n  base: {\n    start: {\n      type: [String, Number, Date],\n      validate: validateTimestamp,\n      default: () => parseDate(new Date()).date,\n    },\n    end: {\n      type: [String, Number, Date],\n      validate: validateTimestamp,\n    },\n    weekdays: {\n      type: [Array, String] as PropType<number[] | string>,\n      default: () => [0, 1, 2, 3, 4, 5, 6],\n      validate: validateWeekdays,\n    },\n    hideHeader: {\n      type: Boolean,\n    },\n    shortWeekdays: {\n      type: Boolean,\n      default: true,\n    },\n    weekdayFormat: {\n      type: Function as PropType<CalendarFormatter>,\n      default: null,\n    },\n    dayFormat: {\n      type: Function as PropType<CalendarFormatter>,\n      default: null,\n    },\n  },\n  intervals: {\n    maxDays: {\n      type: Number,\n      default: 7,\n    },\n    shortIntervals: {\n      type: Boolean,\n      default: true,\n    },\n    intervalHeight: {\n      type: [Number, String],\n      default: 48,\n      validate: validateNumber,\n    },\n    intervalWidth: {\n      type: [Number, String],\n      default: 60,\n      validate: validateNumber,\n    },\n    intervalMinutes: {\n      type: [Number, String],\n      default: 60,\n      validate: validateNumber,\n    },\n    firstInterval: {\n      type: [Number, String],\n      default: 0,\n      validate: validateNumber,\n    },\n    firstTime: {\n      type: [Number, String, Object],\n      validate: validateTime,\n    },\n    intervalCount: {\n      type: [Number, String],\n      default: 24,\n      validate: validateNumber,\n    },\n    intervalFormat: {\n      type: Function as PropType<CalendarFormatter>,\n      default: null,\n    },\n    intervalStyle: {\n      type: Function as PropType<(interval: CalendarTimestamp) => object>,\n      default: null,\n    },\n    showIntervalLabel: {\n      type: Function as PropType<(interval: CalendarTimestamp) => boolean>,\n      default: null,\n    },\n  },\n  weeks: {\n    localeFirstDayOfYear: {\n      type: [String, Number],\n      default: 0,\n    },\n    minWeeks: {\n      validate: validateNumber,\n      default: 1,\n    },\n    shortMonths: {\n      type: Boolean,\n      default: true,\n    },\n    showMonthOnFirst: {\n      type: Boolean,\n      default: true,\n    },\n    showWeek: Boolean,\n    monthFormat: {\n      type: Function as PropType<CalendarFormatter>,\n      default: null,\n    },\n  },\n  calendar: {\n    type: {\n      type: String,\n      default: 'month',\n    },\n    value: {\n      type: [String, Number, Date] as PropType<string | number | Date>,\n      validate: validateTimestamp,\n    },\n  },\n  category: {\n    categories: {\n      type: [Array, String] as PropType<CalendarCategory[] | string>,\n      default: '',\n    },\n    categoryText: {\n      type: [String, Function] as PropType<string | CalendarCategoryTextFunction>,\n    },\n    categoryHideDynamic: {\n      type: Boolean,\n    },\n    categoryShowAll: {\n      type: Boolean,\n    },\n    categoryForInvalid: {\n      type: String,\n      default: '',\n    },\n    categoryDays: {\n      type: [Number, String],\n      default: 1,\n      validate: (x: any) => isFinite(parseInt(x)) && parseInt(x) > 0,\n    },\n  },\n  events: {\n    events: {\n      type: Array,\n      default: () => [],\n    } as PropValidator<CalendarEvent[]>,\n    eventStart: {\n      type: String,\n      default: 'start',\n    },\n    eventEnd: {\n      type: String,\n      default: 'end',\n    },\n    eventTimed: {\n      type: [String, Function] as PropType<string | CalendarEventTimedFunction>,\n      default: 'timed',\n    },\n    eventCategory: {\n      type: [String, Function] as PropType<string | CalendarEventCategoryFunction>,\n      default: 'category',\n    },\n    eventHeight: {\n      type: Number,\n      default: 20,\n    },\n    eventColor: {\n      type: [String, Function] as PropType<string | CalendarEventColorFunction>,\n      default: 'primary',\n    },\n    eventTextColor: {\n      type: [String, Function] as PropType<string | CalendarEventColorFunction>,\n      default: 'white',\n    },\n    eventName: {\n      type: [String, Function] as PropType<string | CalendarEventNameFunction>,\n      default: 'name',\n    },\n    eventOverlapThreshold: {\n      type: [String, Number],\n      default: 60,\n    },\n    eventOverlapMode: {\n      type: [String, Function],\n      default: 'stack',\n      validate: (mode: any) => mode in CalendarEventOverlapModes || typeof mode === 'function',\n    } as PropValidator<'stack' | 'column' | CalendarEventOverlapMode>,\n    eventMore: {\n      type: Boolean,\n      default: true,\n    },\n    eventMoreText: {\n      type: String,\n      default: '$vuetify.calendar.moreEvents',\n    },\n    eventRipple: {\n      type: [Boolean, Object],\n      default: null,\n    },\n    eventMarginBottom: {\n      type: Number,\n      default: 1,\n    },\n  },\n}\n\nexport function validateNumber (input: any): boolean {\n  return isFinite(parseInt(input))\n}\n\nexport function validateWeekdays (input: string | (number | string)[]): boolean {\n  if (typeof input === 'string') {\n    input = input.split(',')\n  }\n\n  if (Array.isArray(input)) {\n    const ints = input.map(x => parseInt(x))\n\n    if (ints.length > DAYS_IN_WEEK || ints.length === 0) {\n      return false\n    }\n\n    const visited: Record<number, boolean> = {}\n    let wrapped = false\n\n    for (let i = 0; i < ints.length; i++) {\n      const x = ints[i]\n\n      if (!isFinite(x) || x < 0 || x >= DAYS_IN_WEEK) {\n        return false\n      }\n\n      if (i > 0) {\n        const d = x - ints[i - 1]\n        if (d < 0) {\n          if (wrapped) {\n            return false\n          }\n          wrapped = true\n        } else if (d === 0) {\n          return false\n        }\n      }\n\n      if (visited[x]) {\n        return false\n      }\n      visited[x] = true\n    }\n\n    return true\n  }\n\n  return false\n}\n", "\n// Mixins\nimport mixins from '../../../util/mixins'\nimport Colorable from '../../../mixins/colorable'\nimport Localable from '../../../mixins/localable'\nimport Mouse from './mouse'\nimport Themeable from '../../../mixins/themeable'\nimport Times from './times'\n\n// Directives\nimport Resize from '../../../directives/resize'\n\n// Util\nimport props from '../util/props'\nimport {\n  parseTimestamp,\n  getWeekdaySkips,\n  createDayList,\n  createNativeLocaleFormatter,\n  getStartOfWeek,\n  getEndOfWeek,\n  getTimestampIdentifier,\n} from '../util/timestamp'\nimport { CalendarTimestamp, CalendarFormatter } from 'vuetify/types'\n\nexport default mixins(\n  Colorable,\n  Localable,\n  Mouse,\n  Themeable,\n  Times\n/* @vue/component */\n).extend({\n  name: 'calendar-base',\n\n  directives: {\n    Resize,\n  },\n\n  props: props.base,\n\n  computed: {\n    parsedWeekdays (): number[] {\n      return Array.isArray(this.weekdays)\n        ? this.weekdays\n        : (this.weekdays || '').split(',').map(x => parseInt(x, 10))\n    },\n    weekdaySkips (): number[] {\n      return getWeekdaySkips(this.parsedWeekdays)\n    },\n    weekdaySkipsReverse (): number [] {\n      const reversed = this.weekdaySkips.slice()\n      reversed.reverse()\n      return reversed\n    },\n    parsedStart (): CalendarTimestamp {\n      return parseTimestamp(this.start, true)\n    },\n    parsedEnd (): CalendarTimestamp {\n      const start = this.parsedStart\n      const end: CalendarTimestamp = this.end ? parseTimestamp(this.end) || start : start\n\n      return getTimestampIdentifier(end) < getTimestampIdentifier(start) ? start : end\n    },\n    days (): CalendarTimestamp[] {\n      return createDayList(\n        this.parsedStart,\n        this.parsedEnd,\n        this.times.today,\n        this.weekdaySkips\n      )\n    },\n    dayFormatter (): CalendarFormatter {\n      if (this.dayFormat) {\n        return this.dayFormat as CalendarFormatter\n      }\n\n      const options = { timeZone: 'UTC', day: 'numeric' }\n\n      return createNativeLocaleFormatter(\n        this.currentLocale,\n        (_tms, _short) => options\n      )\n    },\n    weekdayFormatter (): CalendarFormatter {\n      if (this.weekdayFormat) {\n        return this.weekdayFormat as CalendarFormatter\n      }\n\n      const longOptions = { timeZone: 'UTC', weekday: 'long' }\n      const shortOptions = { timeZone: 'UTC', weekday: 'short' }\n\n      return createNativeLocaleFormatter(\n        this.currentLocale,\n        (_tms, short) => short ? shortOptions : longOptions\n      )\n    },\n  },\n\n  methods: {\n    getRelativeClasses (timestamp: CalendarTimestamp, outside = false): object {\n      return {\n        'v-present': timestamp.present,\n        'v-past': timestamp.past,\n        'v-future': timestamp.future,\n        'v-outside': outside,\n      }\n    },\n    getStartOfWeek (timestamp: CalendarTimestamp): CalendarTimestamp {\n      return getStartOfWeek(timestamp, this.parsedWeekdays, this.times.today)\n    },\n    getEndOfWeek (timestamp: CalendarTimestamp): CalendarTimestamp {\n      return getEndOfWeek(timestamp, this.parsedWeekdays, this.times.today)\n    },\n    getFormatter (options: object): CalendarFormatter {\n      return createNativeLocaleFormatter(\n        this.locale,\n        (_tms, _short) => options\n      )\n    },\n  },\n})\n", "import {\n  parseTimestamp,\n  getDayIdentifier,\n  getTimestampIdentifier,\n  OFFSET_TIME,\n  isTimedless,\n  updateHasTime,\n} from './timestamp'\nimport { CalendarTimestamp, CalendarEvent, CalendarEventParsed } from 'vuetify/types'\n\nexport function parseEvent (\n  input: CalendarEvent,\n  index: number,\n  startProperty: string,\n  endProperty: string,\n  timed = false,\n  category: string | false = false,\n): CalendarEventParsed {\n  const startInput = input[startProperty]\n  const endInput = input[endProperty]\n  const startParsed: CalendarTimestamp = parseTimestamp(startInput, true)\n  const endParsed: CalendarTimestamp = (endInput ? parseTimestamp(endInput, true) : startParsed)\n  const start: CalendarTimestamp = isTimedless(startInput)\n    ? updateHasTime(startParsed, timed)\n    : startParsed\n  const end: CalendarTimestamp = isTimedless(endInput)\n    ? updateHasTime(endParsed, timed)\n    : endParsed\n  const startIdentifier: number = getDayIdentifier(start)\n  const startTimestampIdentifier: number = getTimestampIdentifier(start)\n  const endIdentifier: number = getDayIdentifier(end)\n  const endOffset: number = start.hasTime ? 0 : 2359\n  const endTimestampIdentifier: number = getTimestampIdentifier(end) + endOffset\n  const allDay = !start.hasTime\n\n  return { input, start, startIdentifier, startTimestampIdentifier, end, endIdentifier, endTimestampIdentifier, allDay, index, category }\n}\n\nexport function isEventOn (event: CalendarEventParsed, dayIdentifier: number): boolean {\n  return dayIdentifier >= event.startIdentifier &&\n    dayIdentifier <= event.endIdentifier &&\n    dayIdentifier * OFFSET_TIME !== event.endTimestampIdentifier\n}\n\nexport function isEventStart (event: CalendarEventParsed, day: CalendarTimestamp, dayIdentifier: number, firstWeekday: number): boolean {\n  return dayIdentifier === event.startIdentifier || (firstWeekday === day.weekday && isEventOn(event, dayIdentifier))\n}\n\nexport function isEventOverlapping (event: CalendarEventParsed, startIdentifier: number, endIdentifier: number): boolean {\n  return startIdentifier <= event.endIdentifier && endIdentifier >= event.startIdentifier\n}\n", "// Styles\nimport './calendar-with-events.sass'\n\n// Types\nimport { VNode, VNodeData } from 'vue'\n\n// Directives\nimport ripple from '../../../directives/ripple'\n\n// Mixins\nimport CalendarBase from './calendar-base'\n\n// Helpers\nimport { escapeHTML } from '../../../util/helpers'\n\n// Util\nimport props from '../util/props'\nimport {\n  CalendarEventOverlapModes,\n} from '../modes'\nimport {\n  getDayIdentifier, diffMinutes,\n} from '../util/timestamp'\nimport {\n  parseEvent,\n  isEventStart,\n  isEventOn,\n  isEventOverlapping,\n} from '../util/events'\nimport {\n  CalendarTimestamp,\n  CalendarEventParsed,\n  CalendarEventVisual,\n  CalendarEventColorFunction,\n  CalendarEventNameFunction,\n  CalendarEventTimedFunction,\n  CalendarDaySlotScope,\n  CalendarDayBodySlotScope,\n  CalendarEventOverlapMode,\n  CalendarEvent,\n  CalendarEventCategoryFunction,\n  CalendarCategory,\n} from 'vuetify/types'\n\n// Types\ntype VEventGetter<D> = (day: D) => CalendarEventParsed[]\n\ntype VEventVisualToNode<D> = (visual: CalendarEventVisual, day: D) => VNode | false\n\ntype VEventsToNodes = <D extends CalendarDaySlotScope>(\n  day: D,\n  getter: VEventGetter<D>,\n  mapper: VEventVisualToNode<D>,\n  timed: boolean) => VNode[] | undefined\n\ntype VDailyEventsMap = {\n  [date: string]: {\n    parent: HTMLElement\n    more: HTMLElement | null\n    events: HTMLElement[]\n  }\n}\n\ninterface VEventScopeInput {\n  eventParsed: CalendarEventParsed\n  day: CalendarDaySlotScope\n  start: boolean\n  end: boolean\n  timed: boolean\n}\n\nconst WIDTH_FULL = 100\nconst WIDTH_START = 95\nconst MINUTES_IN_DAY = 1440\n\n/* @vue/component */\nexport default CalendarBase.extend({\n  name: 'calendar-with-events',\n\n  directives: {\n    ripple,\n  },\n\n  props: {\n    ...props.events,\n    ...props.calendar,\n    ...props.category,\n  },\n\n  computed: {\n    noEvents (): boolean {\n      return this.events.length === 0\n    },\n    parsedEvents (): CalendarEventParsed[] {\n      return this.events.map(this.parseEvent)\n    },\n    parsedEventOverlapThreshold (): number {\n      return parseInt(this.eventOverlapThreshold)\n    },\n    eventTimedFunction (): CalendarEventTimedFunction {\n      return typeof this.eventTimed === 'function'\n        ? this.eventTimed\n        : event => !!event[this.eventTimed as string]\n    },\n    eventCategoryFunction (): CalendarEventCategoryFunction {\n      return typeof this.eventCategory === 'function'\n        ? this.eventCategory\n        : event => event[this.eventCategory as string]\n    },\n    eventTextColorFunction (): CalendarEventColorFunction {\n      return typeof this.eventTextColor === 'function'\n        ? this.eventTextColor\n        : () => this.eventTextColor as string\n    },\n    eventNameFunction (): CalendarEventNameFunction {\n      return typeof this.eventName === 'function'\n        ? this.eventName\n        : (event, timedEvent) => escapeHTML(event.input[this.eventName as string] as string || '')\n    },\n    eventModeFunction (): CalendarEventOverlapMode {\n      return typeof this.eventOverlapMode === 'function'\n        ? this.eventOverlapMode\n        : CalendarEventOverlapModes[this.eventOverlapMode]\n    },\n    eventWeekdays (): number[] {\n      return this.parsedWeekdays\n    },\n    categoryMode (): boolean {\n      return this.type === 'category'\n    },\n  },\n\n  methods: {\n    eventColorFunction (e: CalendarEvent): string {\n      return typeof this.eventColor === 'function'\n        ? this.eventColor(e)\n        : e.color || this.eventColor\n    },\n    parseEvent (input: CalendarEvent, index = 0): CalendarEventParsed {\n      return parseEvent(\n        input,\n        index,\n        this.eventStart,\n        this.eventEnd,\n        this.eventTimedFunction(input),\n        this.categoryMode ? this.eventCategoryFunction(input) : false,\n      )\n    },\n    formatTime (withTime: CalendarTimestamp, ampm: boolean): string {\n      const formatter = this.getFormatter({\n        timeZone: 'UTC',\n        hour: 'numeric',\n        minute: withTime.minute > 0 ? 'numeric' : undefined,\n      })\n\n      return formatter(withTime, true)\n    },\n    updateEventVisibility () {\n      if (this.noEvents || !this.eventMore) {\n        return\n      }\n\n      const eventHeight = this.eventHeight\n      const eventsMap = this.getEventsMap()\n\n      for (const date in eventsMap) {\n        const { parent, events, more } = eventsMap[date]\n        if (!more) {\n          break\n        }\n\n        const parentBounds = parent.getBoundingClientRect()\n        const last = events.length - 1\n        let hide = false\n        let hidden = 0\n\n        for (let i = 0; i <= last; i++) {\n          if (!hide) {\n            const eventBounds = events[i].getBoundingClientRect()\n            hide = i === last\n              ? (eventBounds.bottom > parentBounds.bottom)\n              : (eventBounds.bottom + eventHeight > parentBounds.bottom)\n          }\n          if (hide) {\n            events[i].style.display = 'none'\n            hidden++\n          }\n        }\n\n        if (hide) {\n          more.style.display = ''\n          more.innerHTML = this.$vuetify.lang.t(this.eventMoreText, hidden)\n        } else {\n          more.style.display = 'none'\n        }\n      }\n    },\n    getEventsMap (): VDailyEventsMap {\n      const eventsMap: VDailyEventsMap = {}\n      const elements = this.$refs.events as HTMLElement[]\n\n      if (!elements || !elements.forEach) {\n        return eventsMap\n      }\n\n      elements.forEach(el => {\n        const date = el.getAttribute('data-date')\n        if (el.parentElement && date) {\n          if (!(date in eventsMap)) {\n            eventsMap[date] = {\n              parent: el.parentElement,\n              more: null,\n              events: [],\n            }\n          }\n          if (el.getAttribute('data-more')) {\n            eventsMap[date].more = el\n          } else {\n            eventsMap[date].events.push(el)\n            el.style.display = ''\n          }\n        }\n      })\n\n      return eventsMap\n    },\n    genDayEvent ({ event }: CalendarEventVisual, day: CalendarDaySlotScope): VNode {\n      const eventHeight = this.eventHeight\n      const eventMarginBottom = this.eventMarginBottom\n      const dayIdentifier = getDayIdentifier(day)\n      const week = day.week\n      const start = dayIdentifier === event.startIdentifier\n      let end = dayIdentifier === event.endIdentifier\n      let width = WIDTH_START\n\n      if (!this.categoryMode) {\n        for (let i = day.index + 1; i < week.length; i++) {\n          const weekdayIdentifier = getDayIdentifier(week[i])\n          if (event.endIdentifier >= weekdayIdentifier) {\n            width += WIDTH_FULL\n            end = end || weekdayIdentifier === event.endIdentifier\n          } else {\n            end = true\n            break\n          }\n        }\n      }\n      const scope = { eventParsed: event, day, start, end, timed: false }\n\n      return this.genEvent(event, scope, false, {\n        staticClass: 'v-event',\n        class: {\n          'v-event-start': start,\n          'v-event-end': end,\n        },\n        style: {\n          height: `${eventHeight}px`,\n          width: `${width}%`,\n          'margin-bottom': `${eventMarginBottom}px`,\n        },\n        attrs: {\n          'data-date': day.date,\n        },\n        key: event.index,\n        ref: 'events',\n        refInFor: true,\n      })\n    },\n    genTimedEvent ({ event, left, width }: CalendarEventVisual, day: CalendarDayBodySlotScope): VNode | false {\n      if (day.timeDelta(event.end) <= 0 || day.timeDelta(event.start) >= 1) {\n        return false\n      }\n\n      const dayIdentifier = getDayIdentifier(day)\n      const start = event.startIdentifier >= dayIdentifier\n      const end = event.endIdentifier > dayIdentifier\n      const top = start ? day.timeToY(event.start) : 0\n      const bottom = end ? day.timeToY(MINUTES_IN_DAY) : day.timeToY(event.end)\n      const height = Math.max(this.eventHeight, bottom - top)\n      const scope = { eventParsed: event, day, start, end, timed: true }\n\n      return this.genEvent(event, scope, true, {\n        staticClass: 'v-event-timed',\n        style: {\n          top: `${top}px`,\n          height: `${height}px`,\n          left: `${left}%`,\n          width: `${width}%`,\n        },\n      })\n    },\n    genEvent (event: CalendarEventParsed, scopeInput: VEventScopeInput, timedEvent: boolean, data: VNodeData): VNode {\n      const slot = this.$scopedSlots.event\n      const text = this.eventTextColorFunction(event.input)\n      const background = this.eventColorFunction(event.input)\n      const overlapsNoon = event.start.hour < 12 && event.end.hour >= 12\n      const singline = diffMinutes(event.start, event.end) <= this.parsedEventOverlapThreshold\n      const formatTime = this.formatTime\n      const timeSummary = () => formatTime(event.start, overlapsNoon) + ' - ' + formatTime(event.end, true)\n      const eventSummary = () => {\n        const name = this.eventNameFunction(event, timedEvent)\n\n        if (event.start.hasTime) {\n          if (timedEvent) {\n            const time = timeSummary()\n            const delimiter = singline ? ', ' : '<br>'\n\n            return `<strong>${name}</strong>${delimiter}${time}`\n          } else {\n            const time = formatTime(event.start, true)\n\n            return `<strong>${time}</strong> ${name}`\n          }\n        }\n\n        return name\n      }\n\n      const scope = {\n        ...scopeInput,\n        event: event.input,\n        outside: scopeInput.day.outside,\n        singline,\n        overlapsNoon,\n        formatTime,\n        timeSummary,\n        eventSummary,\n      }\n\n      return this.$createElement('div',\n        this.setTextColor(text,\n          this.setBackgroundColor(background, {\n            on: this.getDefaultMouseEventHandlers(':event', nativeEvent => ({ ...scope, nativeEvent })),\n            directives: [{\n              name: 'ripple',\n              value: this.eventRipple ?? true,\n            }],\n            ...data,\n          })\n        ), slot\n          ? slot(scope)\n          : [this.genName(eventSummary)]\n      )\n    },\n    genName (eventSummary: () => string): VNode {\n      return this.$createElement('div', {\n        staticClass: 'pl-1',\n        domProps: {\n          innerHTML: eventSummary(),\n        },\n      })\n    },\n    genPlaceholder (day: CalendarTimestamp): VNode {\n      const height = this.eventHeight + this.eventMarginBottom\n\n      return this.$createElement('div', {\n        style: {\n          height: `${height}px`,\n        },\n        attrs: {\n          'data-date': day.date,\n        },\n        ref: 'events',\n        refInFor: true,\n      })\n    },\n    genMore (day: CalendarDaySlotScope): VNode {\n      const eventHeight = this.eventHeight\n      const eventMarginBottom = this.eventMarginBottom\n\n      return this.$createElement('div', {\n        staticClass: 'v-event-more pl-1',\n        class: {\n          'v-outside': day.outside,\n        },\n        attrs: {\n          'data-date': day.date,\n          'data-more': 1,\n        },\n        directives: [{\n          name: 'ripple',\n          value: this.eventRipple ?? true,\n        }],\n        on: {\n          click: () => this.$emit('click:more', day),\n        },\n        style: {\n          display: 'none',\n          height: `${eventHeight}px`,\n          'margin-bottom': `${eventMarginBottom}px`,\n        },\n        ref: 'events',\n        refInFor: true,\n      })\n    },\n    getVisibleEvents (): CalendarEventParsed[] {\n      const start = getDayIdentifier(this.days[0])\n      const end = getDayIdentifier(this.days[this.days.length - 1])\n\n      return this.parsedEvents.filter(\n        event => isEventOverlapping(event, start, end)\n      )\n    },\n    isEventForCategory (event: CalendarEventParsed, category: CalendarCategory): boolean {\n      return !this.categoryMode ||\n        (typeof category === 'object' && category.categoryName &&\n        category.categoryName === event.category) ||\n        (typeof event.category !== 'string' && category === null)\n    },\n    getEventsForDay (day: CalendarDaySlotScope): CalendarEventParsed[] {\n      const identifier = getDayIdentifier(day)\n      const firstWeekday = this.eventWeekdays[0]\n\n      return this.parsedEvents.filter(\n        event => isEventStart(event, day, identifier, firstWeekday)\n      )\n    },\n    getEventsForDayAll (day: CalendarDaySlotScope): CalendarEventParsed[] {\n      const identifier = getDayIdentifier(day)\n      const firstWeekday = this.eventWeekdays[0]\n\n      return this.parsedEvents.filter(\n        event => event.allDay &&\n          (this.categoryMode ? isEventOn(event, identifier) : isEventStart(event, day, identifier, firstWeekday)) &&\n          this.isEventForCategory(event, day.category)\n      )\n    },\n    getEventsForDayTimed (day: CalendarDaySlotScope): CalendarEventParsed[] {\n      const identifier = getDayIdentifier(day)\n\n      return this.parsedEvents.filter(\n        event => !event.allDay &&\n          isEventOn(event, identifier) &&\n          this.isEventForCategory(event, day.category)\n      )\n    },\n    getScopedSlots () {\n      if (this.noEvents) {\n        return { ...this.$scopedSlots }\n      }\n\n      const mode = this.eventModeFunction(\n        this.parsedEvents,\n        this.eventWeekdays[0],\n        this.parsedEventOverlapThreshold\n      )\n\n      const isNode = (input: VNode | false): input is VNode => !!input\n      const getSlotChildren: VEventsToNodes = (day, getter, mapper, timed) => {\n        const events = getter(day)\n        const visuals = mode(day, events, timed, this.categoryMode)\n\n        if (timed) {\n          return visuals.map(visual => mapper(visual, day)).filter(isNode)\n        }\n\n        const children: VNode[] = []\n\n        visuals.forEach((visual, index) => {\n          while (children.length < visual.column) {\n            children.push(this.genPlaceholder(day))\n          }\n\n          const mapped = mapper(visual, day)\n          if (mapped) {\n            children.push(mapped)\n          }\n        })\n\n        return children\n      }\n\n      const slots = this.$scopedSlots\n      const slotDay = slots.day\n      const slotDayHeader = slots['day-header']\n      const slotDayBody = slots['day-body']\n\n      return {\n        ...slots,\n        day: (day: CalendarDaySlotScope) => {\n          let children = getSlotChildren(day, this.getEventsForDay, this.genDayEvent, false)\n          if (children && children.length > 0 && this.eventMore) {\n            children.push(this.genMore(day))\n          }\n          if (slotDay) {\n            const slot = slotDay(day)\n            if (slot) {\n              children = children ? children.concat(slot) : slot\n            }\n          }\n          return children\n        },\n        'day-header': (day: CalendarDaySlotScope) => {\n          let children = getSlotChildren(day, this.getEventsForDayAll, this.genDayEvent, false)\n\n          if (slotDayHeader) {\n            const slot = slotDayHeader(day)\n            if (slot) {\n              children = children ? children.concat(slot) : slot\n            }\n          }\n          return children\n        },\n        'day-body': (day: CalendarDayBodySlotScope) => {\n          const events = getSlotChildren(day, this.getEventsForDayTimed, this.genTimedEvent, true)\n          let children: VNode[] = [\n            this.$createElement('div', {\n              staticClass: 'v-event-timed-container',\n            }, events),\n          ]\n\n          if (slotDayBody) {\n            const slot = slotDayBody(day)\n            if (slot) {\n              children = children.concat(slot)\n            }\n          }\n          return children\n        },\n      }\n    },\n  },\n})\n", "// Styles\nimport './VCalendarWeekly.sass'\n\n// Types\nimport { VNode } from 'vue'\n\n// Components\nimport VBtn from '../VBtn'\n\n// Mixins\nimport CalendarBase from './mixins/calendar-base'\n\n// Util\nimport { getSlot } from '../../util/helpers'\nimport { weekNumber } from '../../util/dateTimeUtils'\nimport props from './util/props'\nimport {\n  createDayList,\n  getDayIdentifier,\n  createNativeLocaleFormatter,\n} from './util/timestamp'\nimport { CalendarTimestamp, CalendarFormatter } from 'vuetify/types'\n\n/* @vue/component */\nexport default CalendarBase.extend({\n  name: 'v-calendar-weekly',\n\n  props: props.weeks,\n\n  computed: {\n    staticClass (): string {\n      return 'v-calendar-weekly'\n    },\n    classes (): object {\n      return this.themeClasses\n    },\n    parsedMinWeeks (): number {\n      return parseInt(this.minWeeks)\n    },\n    days (): CalendarTimestamp[] {\n      const minDays = this.parsedMinWeeks * this.parsedWeekdays.length\n      const start = this.getStartOfWeek(this.parsedStart)\n      const end = this.getEndOfWeek(this.parsedEnd)\n\n      return createDayList(\n        start,\n        end,\n        this.times.today,\n        this.weekdaySkips,\n        Number.MAX_SAFE_INTEGER,\n        minDays\n      )\n    },\n    todayWeek (): CalendarTimestamp[] {\n      const today = this.times.today\n      const start = this.getStartOfWeek(today)\n      const end = this.getEndOfWeek(today)\n\n      return createDayList(\n        start,\n        end,\n        today,\n        this.weekdaySkips,\n        this.parsedWeekdays.length,\n        this.parsedWeekdays.length\n      )\n    },\n    monthFormatter (): CalendarFormatter {\n      if (this.monthFormat) {\n        return this.monthFormat as CalendarFormatter\n      }\n\n      const longOptions = { timeZone: 'UTC', month: 'long' }\n      const shortOptions = { timeZone: 'UTC', month: 'short' }\n\n      return createNativeLocaleFormatter(\n        this.currentLocale,\n        (_tms, short) => short ? shortOptions : longOptions\n      )\n    },\n  },\n\n  methods: {\n    isOutside (day: CalendarTimestamp): boolean {\n      const dayIdentifier = getDayIdentifier(day)\n\n      return dayIdentifier < getDayIdentifier(this.parsedStart) ||\n             dayIdentifier > getDayIdentifier(this.parsedEnd)\n    },\n    genHead (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-calendar-weekly__head',\n      }, this.genHeadDays())\n    },\n    genHeadDays (): VNode[] {\n      const header = this.todayWeek.map(this.genHeadDay)\n\n      if (this.showWeek) {\n        header.unshift(this.$createElement('div', {\n          staticClass: 'v-calendar-weekly__head-weeknumber',\n        }))\n      }\n\n      return header\n    },\n    genHeadDay (day: CalendarTimestamp, index: number): VNode {\n      const outside = this.isOutside(this.days[index])\n      const color = day.present ? this.color : undefined\n\n      return this.$createElement('div', this.setTextColor(color, {\n        key: day.date,\n        staticClass: 'v-calendar-weekly__head-weekday',\n        class: this.getRelativeClasses(day, outside),\n      }), this.weekdayFormatter(day, this.shortWeekdays))\n    },\n    genWeeks (): VNode[] {\n      const days = this.days\n      const weekDays = this.parsedWeekdays.length\n      const weeks: VNode[] = []\n\n      for (let i = 0; i < days.length; i += weekDays) {\n        weeks.push(this.genWeek(days.slice(i, i + weekDays), this.getWeekNumber(days[i])))\n      }\n\n      return weeks\n    },\n    genWeek (week: CalendarTimestamp[], weekNumber: number): VNode {\n      const weekNodes = week.map((day, index) => this.genDay(day, index, week))\n\n      if (this.showWeek) {\n        weekNodes.unshift(this.genWeekNumber(weekNumber))\n      }\n\n      return this.$createElement('div', {\n        key: week[0].date,\n        staticClass: 'v-calendar-weekly__week',\n      }, weekNodes)\n    },\n    getWeekNumber (determineDay: CalendarTimestamp) {\n      return weekNumber(\n        determineDay.year,\n        determineDay.month - 1,\n        determineDay.day,\n        this.parsedWeekdays[0],\n        parseInt(this.localeFirstDayOfYear)\n      )\n    },\n    genWeekNumber (weekNumber: number) {\n      return this.$createElement('div', {\n        staticClass: 'v-calendar-weekly__weeknumber',\n      }, [\n        this.$createElement('small', String(weekNumber)),\n      ])\n    },\n    genDay (day: CalendarTimestamp, index: number, week: CalendarTimestamp[]): VNode {\n      const outside = this.isOutside(day)\n\n      return this.$createElement('div', {\n        key: day.date,\n        staticClass: 'v-calendar-weekly__day',\n        class: this.getRelativeClasses(day, outside),\n        on: this.getDefaultMouseEventHandlers(':day', _e => day),\n      }, [\n        this.genDayLabel(day),\n        ...(getSlot(this, 'day', () => ({ outside, index, week, ...day })) || []),\n      ])\n    },\n    genDayLabel (day: CalendarTimestamp): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-calendar-weekly__day-label',\n      }, getSlot(this, 'day-label', day) || [this.genDayLabelButton(day)])\n    },\n    genDayLabelButton (day: CalendarTimestamp): VNode {\n      const color = day.present ? this.color : 'transparent'\n      const hasMonth = day.day === 1 && this.showMonthOnFirst\n\n      return this.$createElement(VBtn, {\n        props: {\n          color,\n          fab: true,\n          depressed: true,\n          small: true,\n        },\n        on: this.getMouseEventHandlers({\n          'click:date': { event: 'click', stop: true },\n          'contextmenu:date': { event: 'contextmenu', stop: true, prevent: true, result: false },\n        }, _e => day),\n      }, hasMonth\n        ? this.monthFormatter(day, this.shortMonths) + ' ' + this.dayFormatter(day, false)\n        : this.dayFormatter(day, false)\n      )\n    },\n    genDayMonth (day: CalendarTimestamp): VNode | string {\n      const color = day.present ? this.color : undefined\n\n      return this.$createElement('div', this.setTextColor(color, {\n        staticClass: 'v-calendar-weekly__day-month',\n      }), getSlot(this, 'day-month', day) || this.monthFormatter(day, this.shortMonths))\n    },\n  },\n\n  render (h): VNode {\n    return h('div', {\n      staticClass: this.staticClass,\n      class: this.classes,\n      on: {\n        dragstart: (e: MouseEvent) => {\n          e.preventDefault()\n        },\n      },\n    }, [\n      !this.hideHeader ? this.genHead() : '',\n      ...this.genWeeks(),\n    ])\n  },\n})\n", "// Styles\nimport './VCalendarWeekly.sass'\n\n// Mixins\nimport VCalendarWeekly from './VCalendarWeekly'\n\n// Util\nimport { parseTimestamp, getStartOfMonth, getEndOfMonth } from './util/timestamp'\nimport { CalendarTimestamp } from 'vuetify/types'\n\n/* @vue/component */\nexport default VCalendarWeekly.extend({\n  name: 'v-calendar-monthly',\n\n  computed: {\n    staticClass (): string {\n      return 'v-calendar-monthly v-calendar-weekly'\n    },\n    parsedStart (): CalendarTimestamp {\n      return getStartOfMonth(parseTimestamp(this.start, true))\n    },\n    parsedEnd (): CalendarTimestamp {\n      return getEndOfMonth(parseTimestamp(this.end, true))\n    },\n  },\n\n})\n", "\n// Mixins\nimport CalendarBase from './calendar-base'\n\n// Util\nimport props from '../util/props'\nimport {\n  parseTime,\n  copyTimestamp,\n  updateMinutes,\n  createDayList,\n  createIntervalList,\n  createNativeLocaleFormatter,\n  VTime,\n  MINUTES_IN_DAY,\n} from '../util/timestamp'\nimport { CalendarTimestamp, CalendarFormatter, CalendarDayBodySlotScope } from 'vuetify/types'\n\n/* @vue/component */\nexport default CalendarBase.extend({\n  name: 'calendar-with-intervals',\n\n  props: props.intervals,\n\n  computed: {\n    parsedFirstInterval (): number {\n      return parseInt(this.firstInterval)\n    },\n    parsedIntervalMinutes (): number {\n      return parseInt(this.intervalMinutes)\n    },\n    parsedIntervalCount (): number {\n      return parseInt(this.intervalCount)\n    },\n    parsedIntervalHeight (): number {\n      return parseFloat(this.intervalHeight)\n    },\n    parsedFirstTime (): number | false {\n      return parseTime(this.firstTime)\n    },\n    firstMinute (): number {\n      const time = this.parsedFirstTime\n\n      return time !== false && time >= 0 && time <= MINUTES_IN_DAY\n        ? time\n        : this.parsedFirstInterval * this.parsedIntervalMinutes\n    },\n    bodyHeight (): number {\n      return this.parsedIntervalCount * this.parsedIntervalHeight\n    },\n    days (): CalendarTimestamp[] {\n      return createDayList(\n        this.parsedStart,\n        this.parsedEnd,\n        this.times.today,\n        this.weekdaySkips,\n        this.maxDays\n      )\n    },\n    intervals (): CalendarTimestamp[][] {\n      const days: CalendarTimestamp[] = this.days\n      const first: number = this.firstMinute\n      const minutes: number = this.parsedIntervalMinutes\n      const count: number = this.parsedIntervalCount\n      const now: CalendarTimestamp = this.times.now\n\n      return days.map(d => createIntervalList(d, first, minutes, count, now))\n    },\n    intervalFormatter (): CalendarFormatter {\n      if (this.intervalFormat) {\n        return this.intervalFormat as CalendarFormatter\n      }\n\n      const longOptions = { timeZone: 'UTC', hour: '2-digit', minute: '2-digit' }\n      const shortOptions = { timeZone: 'UTC', hour: 'numeric', minute: '2-digit' }\n      const shortHourOptions = { timeZone: 'UTC', hour: 'numeric' }\n\n      return createNativeLocaleFormatter(\n        this.currentLocale,\n        (tms, short) => short ? (tms.minute === 0 ? shortHourOptions : shortOptions) : longOptions\n      )\n    },\n  },\n\n  methods: {\n    showIntervalLabelDefault (interval: CalendarTimestamp): boolean {\n      const first: CalendarTimestamp = this.intervals[0][0]\n      const isFirst: boolean = first.hour === interval.hour && first.minute === interval.minute\n      return !isFirst\n    },\n    intervalStyleDefault (_interval: CalendarTimestamp): object | undefined {\n      return undefined\n    },\n    getTimestampAtEvent (e: MouseEvent | TouchEvent, day: CalendarTimestamp): CalendarTimestamp {\n      const timestamp: CalendarTimestamp = copyTimestamp(day)\n      const bounds = (e.currentTarget as HTMLElement).getBoundingClientRect()\n      const baseMinutes: number = this.firstMinute\n      const touchEvent: TouchEvent = e as TouchEvent\n      const mouseEvent: MouseEvent = e as MouseEvent\n      const touches: TouchList = touchEvent.changedTouches || touchEvent.touches\n      const clientY: number = touches && touches[0] ? touches[0].clientY : mouseEvent.clientY\n      const addIntervals: number = (clientY - bounds.top) / this.parsedIntervalHeight\n      const addMinutes: number = Math.floor(addIntervals * this.parsedIntervalMinutes)\n      const minutes: number = baseMinutes + addMinutes\n\n      return updateMinutes(timestamp, minutes, this.times.now)\n    },\n    getSlotScope (timestamp: CalendarTimestamp): CalendarDayBodySlotScope {\n      const scope = copyTimestamp(timestamp) as any\n      scope.timeToY = this.timeToY\n      scope.timeDelta = this.timeDelta\n      scope.minutesToPixels = this.minutesToPixels\n      scope.week = this.days\n      return scope\n    },\n    scrollToTime (time: VTime): boolean {\n      const y = this.timeToY(time)\n      const pane = this.$refs.scrollArea as HTMLElement\n\n      if (y === false || !pane) {\n        return false\n      }\n\n      pane.scrollTop = y\n\n      return true\n    },\n    minutesToPixels (minutes: number): number {\n      return minutes / this.parsedIntervalMinutes * this.parsedIntervalHeight\n    },\n    timeToY (time: VTime, clamp = true): number | false {\n      let y = this.timeDelta(time)\n\n      if (y !== false) {\n        y *= this.bodyHeight\n\n        if (clamp) {\n          if (y < 0) {\n            y = 0\n          }\n          if (y > this.bodyHeight) {\n            y = this.bodyHeight\n          }\n        }\n      }\n\n      return y\n    },\n    timeDelta (time: VTime): number | false {\n      const minutes = parseTime(time)\n\n      if (minutes === false) {\n        return false\n      }\n\n      const min: number = this.firstMinute\n      const gap: number = this.parsedIntervalCount * this.parsedIntervalMinutes\n\n      return (minutes - min) / gap\n    },\n  },\n})\n", "// Styles\nimport './VCalendarDaily.sass'\n\n// Types\nimport { VNode } from 'vue'\n\n// Directives\nimport Resize from '../../directives/resize'\n\n// Components\nimport VBtn from '../VBtn'\n\n// Mixins\nimport CalendarWithIntervals from './mixins/calendar-with-intervals'\n\n// Util\nimport { convertToUnit, getSlot } from '../../util/helpers'\nimport { CalendarTimestamp } from 'vuetify/types'\n\n/* @vue/component */\nexport default CalendarWithIntervals.extend({\n  name: 'v-calendar-daily',\n\n  directives: { Resize },\n\n  data: () => ({\n    scrollPush: 0,\n  }),\n\n  computed: {\n    classes (): object {\n      return {\n        'v-calendar-daily': true,\n        ...this.themeClasses,\n      }\n    },\n  },\n\n  mounted () {\n    this.init()\n  },\n\n  methods: {\n    init () {\n      this.$nextTick(this.onResize)\n    },\n    onResize () {\n      this.scrollPush = this.getScrollPush()\n    },\n    getScrollPush (): number {\n      const area = this.$refs.scrollArea as HTMLElement\n      const pane = this.$refs.pane as HTMLElement\n\n      return area && pane ? (area.offsetWidth - pane.offsetWidth) : 0\n    },\n    genHead (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-calendar-daily__head',\n        style: {\n          marginRight: this.scrollPush + 'px',\n        },\n      }, [\n        this.genHeadIntervals(),\n        ...this.genHeadDays(),\n      ])\n    },\n    genHeadIntervals (): VNode {\n      const width: string | undefined = convertToUnit(this.intervalWidth)\n\n      return this.$createElement('div', {\n        staticClass: 'v-calendar-daily__intervals-head',\n        style: {\n          width,\n        },\n      }, getSlot(this, 'interval-header'))\n    },\n    genHeadDays (): VNode[] {\n      return this.days.map(this.genHeadDay)\n    },\n    genHeadDay (day: CalendarTimestamp, index: number): VNode {\n      return this.$createElement('div', {\n        key: day.date,\n        staticClass: 'v-calendar-daily_head-day',\n        class: this.getRelativeClasses(day),\n        on: this.getDefaultMouseEventHandlers(':day', _e => {\n          return this.getSlotScope(day)\n        }),\n      }, [\n        this.genHeadWeekday(day),\n        this.genHeadDayLabel(day),\n        ...this.genDayHeader(day, index),\n      ])\n    },\n    genDayHeader (day: CalendarTimestamp, index: number): VNode[] {\n      return getSlot(this, 'day-header', () => ({\n        week: this.days, ...day, index,\n      })) || []\n    },\n    genHeadWeekday (day: CalendarTimestamp): VNode {\n      const color = day.present ? this.color : undefined\n\n      return this.$createElement('div', this.setTextColor(color, {\n        staticClass: 'v-calendar-daily_head-weekday',\n      }), this.weekdayFormatter(day, this.shortWeekdays))\n    },\n    genHeadDayLabel (day: CalendarTimestamp): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-calendar-daily_head-day-label',\n      }, getSlot(this, 'day-label-header', day) || [this.genHeadDayButton(day)])\n    },\n    genHeadDayButton (day: CalendarTimestamp): VNode {\n      const color = day.present ? this.color : 'transparent'\n\n      return this.$createElement(VBtn, {\n        props: {\n          color,\n          fab: true,\n          depressed: true,\n        },\n        on: this.getMouseEventHandlers({\n          'click:date': { event: 'click', stop: true },\n          'contextmenu:date': { event: 'contextmenu', stop: true, prevent: true, result: false },\n        }, _e => {\n          return day\n        }),\n      }, this.dayFormatter(day, false))\n    },\n    genBody (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-calendar-daily__body',\n      }, [\n        this.genScrollArea(),\n      ])\n    },\n    genScrollArea (): VNode {\n      return this.$createElement('div', {\n        ref: 'scrollArea',\n        staticClass: 'v-calendar-daily__scroll-area',\n      }, [\n        this.genPane(),\n      ])\n    },\n    genPane (): VNode {\n      return this.$createElement('div', {\n        ref: 'pane',\n        staticClass: 'v-calendar-daily__pane',\n        style: {\n          height: convertToUnit(this.bodyHeight),\n        },\n      }, [\n        this.genDayContainer(),\n      ])\n    },\n    genDayContainer (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-calendar-daily__day-container',\n      }, [\n        this.genBodyIntervals(),\n        ...this.genDays(),\n      ])\n    },\n    genDays (): VNode[] {\n      return this.days.map(this.genDay)\n    },\n    genDay (day: CalendarTimestamp, index: number): VNode {\n      return this.$createElement('div', {\n        key: day.date,\n        staticClass: 'v-calendar-daily__day',\n        class: this.getRelativeClasses(day),\n        on: this.getDefaultMouseEventHandlers(':time', e => {\n          return this.getSlotScope(this.getTimestampAtEvent(e, day))\n        }),\n      }, [\n        ...this.genDayIntervals(index),\n        ...this.genDayBody(day),\n      ])\n    },\n    genDayBody (day: CalendarTimestamp): VNode[] {\n      return getSlot(this, 'day-body', () => this.getSlotScope(day)) || []\n    },\n    genDayIntervals (index: number): VNode[] {\n      return this.intervals[index].map(this.genDayInterval)\n    },\n    genDayInterval (interval: CalendarTimestamp): VNode {\n      const height: string | undefined = convertToUnit(this.intervalHeight)\n      const styler = this.intervalStyle || this.intervalStyleDefault\n\n      const data = {\n        key: interval.time,\n        staticClass: 'v-calendar-daily__day-interval',\n        style: {\n          height,\n          ...styler(interval),\n        },\n      }\n\n      const children = getSlot(this, 'interval', () => this.getSlotScope(interval))\n\n      return this.$createElement('div', data, children)\n    },\n    genBodyIntervals (): VNode {\n      const width: string | undefined = convertToUnit(this.intervalWidth)\n      const data = {\n        staticClass: 'v-calendar-daily__intervals-body',\n        style: {\n          width,\n        },\n        on: this.getDefaultMouseEventHandlers(':interval', e => {\n          return this.getTimestampAtEvent(e, this.parsedStart)\n        }),\n      }\n\n      return this.$createElement('div', data, this.genIntervalLabels())\n    },\n    genIntervalLabels (): VNode[] | null {\n      if (!this.intervals.length) return null\n\n      return this.intervals[0].map(this.genIntervalLabel)\n    },\n    genIntervalLabel (interval: CalendarTimestamp): VNode {\n      const height: string | undefined = convertToUnit(this.intervalHeight)\n      const short: boolean = this.shortIntervals\n      const shower = this.showIntervalLabel || this.showIntervalLabelDefault\n      const show = shower(interval)\n      const label = show ? this.intervalFormatter(interval, short) : undefined\n\n      return this.$createElement('div', {\n        key: interval.time,\n        staticClass: 'v-calendar-daily__interval',\n        style: {\n          height,\n        },\n      }, [\n        this.$createElement('div', {\n          staticClass: 'v-calendar-daily__interval-text',\n        }, label),\n      ])\n    },\n  },\n\n  render (h): VNode {\n    return h('div', {\n      class: this.classes,\n      on: {\n        dragstart: (e: MouseEvent) => {\n          e.preventDefault()\n        },\n      },\n      directives: [{\n        modifiers: { quiet: true },\n        name: 'resize',\n        value: this.onResize,\n      }],\n    }, [\n      !this.hideHeader ? this.genHead() : '',\n      this.genBody(),\n    ])\n  },\n})\n", "import { CalendarCategory, CalendarCategoryTextFunction } from 'types'\n\nexport function parsedCategoryText (\n  category: CalendarCategory,\n  categoryText: string | CalendarCategoryTextFunction\n): string {\n  return typeof categoryText === 'string' && typeof category === 'object' && category\n    ? category[categoryText]\n    : typeof categoryText === 'function'\n      ? categoryText(category)\n      : category\n}\n\nexport function getParsedCategories (\n  categories: CalendarCategory | CalendarCategory[],\n  categoryText: string | CalendarCategoryTextFunction\n): CalendarCategory[] {\n  if (typeof categories === 'string') return categories.split(/\\s*,\\s/)\n  if (Array.isArray(categories)) {\n    return categories.map((category: CalendarCategory) => {\n      if (typeof category === 'string') return { categoryName: category }\n\n      const categoryName = typeof category.categoryName === 'string'\n        ? category.categoryName\n        : parsedCategoryText(category, categoryText)\n      return { ...category, categoryName }\n    })\n  }\n  return []\n}\n", "// Styles\nimport './VCalendarCategory.sass'\n\n// Types\nimport { VNode } from 'vue'\n\n// Mixins\nimport VCalendarDaily from './VCalendarDaily'\n\n// Util\nimport { convertToUnit, getSlot } from '../../util/helpers'\nimport { CalendarCategory, CalendarTimestamp } from 'types'\nimport props from './util/props'\nimport { getParsedCategories } from './util/parser'\n\n/* @vue/component */\nexport default VCalendarDaily.extend({\n  name: 'v-calendar-category',\n\n  props: props.category,\n\n  computed: {\n    classes (): object {\n      return {\n        'v-calendar-daily': true,\n        'v-calendar-category': true,\n        ...this.themeClasses,\n      }\n    },\n    parsedCategories (): CalendarCategory[] {\n      return getParsedCategories(this.categories, this.categoryText)\n    },\n  },\n  methods: {\n    genDayHeader (day: CalendarTimestamp, index: number): VNode[] {\n      const data = {\n        staticClass: 'v-calendar-category__columns',\n      }\n      const scope = {\n        week: this.days, ...day, index,\n      }\n\n      const children = this.parsedCategories.map(category => {\n        return this.genDayHeaderCategory(day, this.getCategoryScope(scope, category))\n      })\n\n      return [this.$createElement('div', data, children)]\n    },\n    getCategoryScope (scope: any, category: CalendarCategory) {\n      const cat = typeof category === 'object' && category &&\n          category.categoryName === this.categoryForInvalid ? null : category\n      return {\n        ...scope,\n        category: cat,\n      }\n    },\n    genDayHeaderCategory (day: CalendarTimestamp, scope: any): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-calendar-category__column-header',\n        on: this.getDefaultMouseEventHandlers(':day-category', e => {\n          return this.getCategoryScope(this.getSlotScope(day), scope.category)\n        }),\n      }, [\n        getSlot(this, 'category', scope) || this.genDayHeaderCategoryTitle(scope.category && scope.category.categoryName),\n        getSlot(this, 'day-header', scope),\n      ])\n    },\n    genDayHeaderCategoryTitle (categoryName: string | null) {\n      return this.$createElement('div', {\n        staticClass: 'v-calendar-category__category',\n      }, categoryName === null ? this.categoryForInvalid : categoryName)\n    },\n    genDays (): VNode[] {\n      const days: VNode[] = []\n      this.days.forEach(d => {\n        const day = new Array(this.parsedCategories.length || 1)\n        day.fill(d)\n        days.push(...day.map((v, i) => this.genDay(v, 0, i)))\n      })\n      return days\n    },\n    genDay (day: CalendarTimestamp, index: number, categoryIndex: number): VNode {\n      const category = this.parsedCategories[categoryIndex]\n      return this.$createElement('div', {\n        key: day.date + '-' + categoryIndex,\n        staticClass: 'v-calendar-daily__day',\n        class: this.getRelativeClasses(day),\n        on: this.getDefaultMouseEventHandlers(':time', e => {\n          return this.getSlotScope(this.getTimestampAtEvent(e, day))\n        }),\n      }, [\n        ...this.genDayIntervals(index, category),\n        ...this.genDayBody(day, category),\n      ])\n    },\n    genDayIntervals (index: number, category: CalendarCategory): VNode[] {\n      return this.intervals[index].map(v => this.genDayInterval(v, category))\n    },\n    genDayInterval (interval: CalendarTimestamp, category: CalendarCategory): VNode {\n      const height: string | undefined = convertToUnit(this.intervalHeight)\n      const styler = this.intervalStyle || this.intervalStyleDefault\n\n      const data = {\n        key: interval.time,\n        staticClass: 'v-calendar-daily__day-interval',\n        style: {\n          height,\n          ...styler({ ...interval, category }),\n        },\n      }\n\n      const children = getSlot(this, 'interval', () =>\n        this.getCategoryScope(this.getSlotScope(interval), category)\n      )\n\n      return this.$createElement('div', data, children)\n    },\n    genDayBody (day: CalendarTimestamp, category: CalendarCategory): VNode[] {\n      const data = {\n        staticClass: 'v-calendar-category__columns',\n      }\n\n      const children = [this.genDayBodyCategory(day, category)]\n\n      return [this.$createElement('div', data, children)]\n    },\n    genDayBodyCategory (day: CalendarTimestamp, category: CalendarCategory): VNode {\n      const data = {\n        staticClass: 'v-calendar-category__column',\n        on: this.getDefaultMouseEventHandlers(':time-category', e => {\n          return this.getCategoryScope(this.getSlotScope(this.getTimestampAtEvent(e, day)), category)\n        }),\n      }\n\n      const children = getSlot(this, 'day-body', () => this.getCategoryScope(this.getSlotScope(day), category))\n\n      return this.$createElement('div', data, children)\n    },\n  },\n})\n", "// Styles\n// import '../../stylus/components/_calendar-daily.styl'\n\n// Types\nimport { VNode, Component } from 'vue'\n\n// Mixins\nimport CalendarWithEvents from './mixins/calendar-with-events'\n\n// Util\nimport props from './util/props'\nimport {\n  DAYS_IN_MONTH_MAX,\n  DAY_MIN,\n  DAYS_IN_WEEK,\n  parseTimestamp,\n  validateTimestamp,\n  relativeDays,\n  nextDay,\n  prevDay,\n  copyTimestamp,\n  updateFormatted,\n  updateWeekday,\n  updateRelative,\n  getStartOfMonth,\n  getEndOfMonth,\n  VTime,\n  VTimestampInput,\n  timestampToDate,\n} from './util/timestamp'\n\n// Calendars\nimport VCalendarMonthly from './VCalendarMonthly'\nimport VCalendarDaily from './VCalendarDaily'\nimport VCalendarWeekly from './VCalendarWeekly'\nimport VCalendarCategory from './VCalendarCategory'\nimport { CalendarTimestamp, CalendarFormatter, CalendarCategory } from 'vuetify/types'\nimport { getParsedCategories } from './util/parser'\n\n// Types\ninterface VCalendarRenderProps {\n  start: CalendarTimestamp\n  end: CalendarTimestamp\n  component: string | Component\n  maxDays: number\n  weekdays: number[]\n  categories: CalendarCategory[]\n}\n\n/* @vue/component */\nexport default CalendarWithEvents.extend({\n  name: 'v-calendar',\n\n  props: {\n    ...props.calendar,\n    ...props.weeks,\n    ...props.intervals,\n    ...props.category,\n  },\n\n  data: () => ({\n    lastStart: null as CalendarTimestamp | null,\n    lastEnd: null as CalendarTimestamp | null,\n  }),\n\n  computed: {\n    parsedValue (): CalendarTimestamp {\n      return (validateTimestamp(this.value)\n        ? parseTimestamp(this.value, true)\n        : (this.parsedStart || this.times.today))\n    },\n    parsedCategoryDays (): number {\n      return parseInt(this.categoryDays) || 1\n    },\n    renderProps (): VCalendarRenderProps {\n      const around = this.parsedValue\n      let component: any = null\n      let maxDays = this.maxDays\n      let weekdays = this.parsedWeekdays\n      let categories = this.parsedCategories\n      let start = around\n      let end = around\n      switch (this.type) {\n        case 'month':\n          component = VCalendarMonthly\n          start = getStartOfMonth(around)\n          end = getEndOfMonth(around)\n          break\n        case 'week':\n          component = VCalendarDaily\n          start = this.getStartOfWeek(around)\n          end = this.getEndOfWeek(around)\n          maxDays = 7\n          break\n        case 'day':\n          component = VCalendarDaily\n          maxDays = 1\n          weekdays = [start.weekday]\n          break\n        case '4day':\n          component = VCalendarDaily\n          end = relativeDays(copyTimestamp(end), nextDay, 3)\n          updateFormatted(end)\n          maxDays = 4\n          weekdays = [\n            start.weekday,\n            (start.weekday + 1) % 7,\n            (start.weekday + 2) % 7,\n            (start.weekday + 3) % 7,\n          ]\n          break\n        case 'custom-weekly':\n          component = VCalendarWeekly\n          start = this.parsedStart || around\n          end = this.parsedEnd\n          break\n        case 'custom-daily':\n          component = VCalendarDaily\n          start = this.parsedStart || around\n          end = this.parsedEnd\n          break\n        case 'category':\n          const days = this.parsedCategoryDays\n\n          component = VCalendarCategory\n          end = relativeDays(copyTimestamp(end), nextDay, days)\n          updateFormatted(end)\n          maxDays = days\n          weekdays = []\n\n          for (let i = 0; i < days; i++) {\n            weekdays.push((start.weekday + i) % 7)\n          }\n\n          categories = this.getCategoryList(categories)\n          break\n        default:\n          throw new Error(this.type + ' is not a valid Calendar type')\n      }\n\n      return { component, start, end, maxDays, weekdays, categories }\n    },\n    eventWeekdays (): number[] {\n      return this.renderProps.weekdays\n    },\n    categoryMode (): boolean {\n      return this.type === 'category'\n    },\n    title (): string {\n      const { start, end } = this.renderProps\n      const spanYears = start.year !== end.year\n      const spanMonths = spanYears || start.month !== end.month\n\n      if (spanYears) {\n        return this.monthShortFormatter(start, true) + ' ' + start.year + ' - ' + this.monthShortFormatter(end, true) + ' ' + end.year\n      }\n\n      if (spanMonths) {\n        return this.monthShortFormatter(start, true) + ' - ' + this.monthShortFormatter(end, true) + ' ' + end.year\n      } else {\n        return this.monthLongFormatter(start, false) + ' ' + start.year\n      }\n    },\n    monthLongFormatter (): CalendarFormatter {\n      return this.getFormatter({\n        timeZone: 'UTC', month: 'long',\n      })\n    },\n    monthShortFormatter (): CalendarFormatter {\n      return this.getFormatter({\n        timeZone: 'UTC', month: 'short',\n      })\n    },\n    parsedCategories (): CalendarCategory[] {\n      return getParsedCategories(this.categories, this.categoryText)\n    },\n  },\n\n  watch: {\n    renderProps: 'checkChange',\n  },\n\n  mounted () {\n    this.updateEventVisibility()\n    this.checkChange()\n  },\n\n  updated () {\n    window.requestAnimationFrame(this.updateEventVisibility)\n  },\n\n  methods: {\n    checkChange (): void {\n      const { lastStart, lastEnd } = this\n      const { start, end } = this.renderProps\n      if (!lastStart || !lastEnd ||\n        start.date !== lastStart.date ||\n        end.date !== lastEnd.date) {\n        this.lastStart = start\n        this.lastEnd = end\n        this.$emit('change', { start, end })\n      }\n    },\n    move (amount = 1): void {\n      const moved = copyTimestamp(this.parsedValue)\n      const forward = amount > 0\n      const mover = forward ? nextDay : prevDay\n      const limit = forward ? DAYS_IN_MONTH_MAX : DAY_MIN\n      let times = forward ? amount : -amount\n\n      while (--times >= 0) {\n        switch (this.type) {\n          case 'month':\n            moved.day = limit\n            mover(moved)\n            break\n          case 'week':\n            relativeDays(moved, mover, DAYS_IN_WEEK)\n            break\n          case 'day':\n            relativeDays(moved, mover, 1)\n            break\n          case '4day':\n            relativeDays(moved, mover, 4)\n            break\n          case 'category':\n            relativeDays(moved, mover, this.parsedCategoryDays)\n            break\n        }\n      }\n\n      updateWeekday(moved)\n      updateFormatted(moved)\n      updateRelative(moved, this.times.now)\n\n      if (this.value instanceof Date) {\n        this.$emit('input', timestampToDate(moved))\n      } else if (typeof this.value === 'number') {\n        this.$emit('input', timestampToDate(moved).getTime())\n      } else {\n        this.$emit('input', moved.date)\n      }\n\n      this.$emit('moved', moved)\n    },\n    next (amount = 1): void {\n      this.move(amount)\n    },\n    prev (amount = 1): void {\n      this.move(-amount)\n    },\n    timeToY (time: VTime, clamp = true): number | false {\n      const c = this.$children[0] as any\n\n      if (c && c.timeToY) {\n        return c.timeToY(time, clamp)\n      } else {\n        return false\n      }\n    },\n    timeDelta (time: VTime): number | false {\n      const c = this.$children[0] as any\n\n      if (c && c.timeDelta) {\n        return c.timeDelta(time)\n      } else {\n        return false\n      }\n    },\n    minutesToPixels (minutes: number): number {\n      const c = this.$children[0] as any\n\n      if (c && c.minutesToPixels) {\n        return c.minutesToPixels(minutes)\n      } else {\n        return -1\n      }\n    },\n    scrollToTime (time: VTime): boolean {\n      const c = this.$children[0] as any\n\n      if (c && c.scrollToTime) {\n        return c.scrollToTime(time)\n      } else {\n        return false\n      }\n    },\n    parseTimestamp (input: VTimestampInput, required?: false): CalendarTimestamp | null {\n      return parseTimestamp(input, required, this.times.now)\n    },\n    timestampToDate (timestamp: CalendarTimestamp): Date {\n      return timestampToDate(timestamp)\n    },\n    getCategoryList (categories: CalendarCategory[]): CalendarCategory[] {\n      if (!this.noEvents) {\n        const categoryMap: any = categories.reduce((map: any, category, index) => {\n          if (typeof category === 'object' && category.categoryName) map[category.categoryName] = { index, count: 0 }\n\n          return map\n        }, {})\n\n        if (!this.categoryHideDynamic || !this.categoryShowAll) {\n          let categoryLength = categories.length\n\n          this.parsedEvents.forEach(ev => {\n            let category = ev.category\n\n            if (typeof category !== 'string') {\n              category = this.categoryForInvalid\n            }\n\n            if (!category) {\n              return\n            }\n\n            if (category in categoryMap) {\n              categoryMap[category].count++\n            } else if (!this.categoryHideDynamic) {\n              categoryMap[category] = {\n                index: categoryLength++,\n                count: 1,\n              }\n            }\n          })\n        }\n\n        if (!this.categoryShowAll) {\n          for (const category in categoryMap) {\n            if (categoryMap[category].count === 0) {\n              delete categoryMap[category]\n            }\n          }\n        }\n\n        categories = categories.filter((category: CalendarCategory) => {\n          if (typeof category === 'object' && category.categoryName) {\n            return categoryMap.hasOwnProperty(category.categoryName)\n          }\n          return false\n        })\n      }\n      return categories\n    },\n  },\n\n  render (h): VNode {\n    const { start, end, maxDays, component, weekdays, categories } = this.renderProps\n\n    return h(component, {\n      staticClass: 'v-calendar',\n      class: {\n        'v-calendar-events': !this.noEvents,\n      },\n      props: {\n        ...this.$props,\n        start: start.date,\n        end: end.date,\n        maxDays,\n        weekdays,\n        categories,\n      },\n      directives: [{\n        modifiers: { quiet: true },\n        name: 'resize',\n        value: this.updateEventVisibility,\n      }],\n      on: {\n        ...this.$listeners,\n        'click:date': (day: CalendarTimestamp) => {\n          if (this.$listeners.input) {\n            this.$emit('input', day.date)\n          }\n          if (this.$listeners['click:date']) {\n            this.$emit('click:date', day)\n          }\n        },\n      },\n      scopedSlots: this.getScopedSlots(),\n    })\n  },\n})\n", "import { render, staticRenderFns } from \"./Calendar.vue?vue&type=template&id=e37c7c60&\"\nimport script from \"./Calendar.vue?vue&type=script&lang=js&\"\nexport * from \"./Calendar.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./Calendar.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"fafced54\"\n  \n)\n\nexport default component.exports\n\n/* nuxt-component-imports */\ninstallComponents(component, {CalendarDate: require('D:/languworks/langu-frontend/components/CalendarDate.vue').default})\n\n\n/* vuetify-loader */\nimport installComponents from \"!../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VCalendar } from 'vuetify/lib/components/VCalendar';\ninstallComponents(component, {VCalendar})\n", "\nexport default {\n  data() {\n    return {\n      timeoutId: null,\n      userStatuses: {},\n      arrStatusId: [],\n    }\n  },\n  computed: {\n    preparedArr() {\n      return [...new Set(this.arrStatusId)]\n    },\n  },\n  mounted() {\n    this.timeoutId = window.setInterval(() => {\n      this.refreshStatusOnline()\n\n      if (!this.arrStatusId.length) {\n        this.clearInterval()\n      }\n    }, 10000)\n  },\n  beforeDestroy() {\n    if (this.timeoutId) {\n      this.clearInterval()\n    }\n  },\n  methods: {\n    refreshStatusOnline() {\n      if (this.arrStatusId.length) {\n        this.$store\n          .dispatch('user/refreshStatusOnline', this.preparedArr)\n          .then((res) => (this.userStatuses = res))\n      }\n    },\n    clearInterval() {\n      window.clearInterval(this.timeoutId)\n      this.timeoutId = null\n    },\n  },\n}\n", "import mod from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StatusOnline.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/babel-loader/lib/index.js??ref--2-0!../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./StatusOnline.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./StatusOnline.vue?vue&type=script&lang=js&\"\nexport * from \"./StatusOnline.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"2b0aab01\"\n  \n)\n\nexport default component.exports", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonsPage.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"6d38e372\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonItem.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".lesson{position:relative;min-height:128px;padding-left:142px}@media only screen and (max-width:1215px){.lesson{min-height:130px;padding-left:85px}}@media only screen and (min-width:768px){.lesson{background:#fff;border-radius:16px;box-shadow:0 6px 10px rgba(17,46,90,.08)}}@media only screen and (max-width:991px){.lesson{padding-left:112px}}@media only screen and (max-width:767px){.lesson{padding-left:0}}.lesson-date{justify-content:center;align-items:center;width:142px;padding:11px;font-size:16px;font-weight:600;background:linear-gradient(126.15deg,rgba(128,182,34,.74),rgba(60,135,248,.74) 102.93%);border-radius:16px;color:#fff}@media only screen and (min-width:768px){.lesson-date{position:absolute;left:0;top:0;height:100%;flex-direction:column;justify-content:space-between;box-shadow:4px 5px 8px hsla(0,0%,40%,.25);z-index:3}}@media only screen and (max-width:1215px){.lesson-date{width:85px;padding:14px 6px;font-size:15px}}@media only screen and (max-width:991px){.lesson-date{width:112px;padding:10px 4px;font-size:13px}}@media only screen and (max-width:767px){.lesson-date{width:100%;padding:7px 10px 32px;font-size:16px!important;font-weight:600!important;border-radius:10px 10px 0 0}.lesson-date>div{display:flex;align-items:center}}@media only screen and (max-width:480px){.lesson-date{font-size:14px!important;font-weight:400!important}}.lesson-date .time,.lesson-date .weekday{font-size:13px;font-weight:700;line-height:1}.lesson-date .date{font-weight:700;font-size:24px;line-height:1.2;white-space:nowrap}@media only screen and (max-width:1215px){.lesson-date .date{margin-bottom:4px;font-size:20px}}@media only screen and (max-width:767px){.lesson-date .date{margin-bottom:0;font-weight:inherit;font-size:inherit}}.lesson-date .remaining{line-height:1.23}@media only screen and (min-width:768px){.lesson-date .remaining{font-size:13px}}@media only screen and (max-width:767px){.lesson-date .remaining{padding:0 3px}}.lesson-date .duration{white-space:nowrap}@media only screen and (min-width:1216px){.lesson-date .duration{padding-left:22px}}@media only screen and (min-width:768px){.lesson-date .duration{position:relative;font-weight:400;color:#e8f1f7}}.lesson-date .duration-icon{position:absolute;left:0;top:50%;width:18px;height:18px;margin-top:-9px;color:var(--v-dark-lighten3)}@media only screen and (max-width:1215px){.lesson-date .duration-icon{display:none}}.lesson-content{position:relative;display:flex;justify-content:space-between;width:100%;border-radius:0 10px 10px 0;overflow:hidden}@media only screen and (max-width:767px){.lesson-content{min-height:128px;margin-top:-25px;border-radius:10px;background:#fff;box-shadow:0 1px 5px hsla(0,0%,51%,.3);z-index:2}}@media only screen and (max-width:639px){.lesson-content{flex-direction:column;padding:12px 12px 4px}}.lesson-details{display:flex;padding:8px 10px 8px 18px}@media only screen and (max-width:1215px){.lesson-details{padding:12px 4px 8px 12px}.lesson-details>.avatar{display:none}}@media only screen and (max-width:767px){.lesson-details{padding-top:10px}}@media only screen and (max-width:639px){.lesson-details{position:relative;padding:0 0 0 100px}}.lesson-details .avatar{position:relative}@media only screen and (max-width:639px){.lesson-details .avatar{position:absolute;left:0;top:0}}.lesson-details .avatar .user-status{position:absolute;top:0;right:0}@media only screen and (max-width:479px){.lesson-details .avatar .user-status{top:1px;right:1px}}.lesson-details .avatar a{position:absolute;top:0;left:0;width:100%;height:100%}.lesson-details .details{display:flex;flex-direction:column;justify-content:space-between}.lesson-details .details .user-info{line-height:1.1}@media only screen and (max-width:1439px){.lesson-details .details .user-info{margin-bottom:8px}}@media only screen and (min-width:1216px){.lesson-details .details .user-info{padding-top:2px}}.lesson-details .details .user-info-name{display:inline;font-size:24px}@media only screen and (min-width:1440px){.lesson-details .details .user-info-name{margin-right:6px}}@media only screen and (max-width:1439px){.lesson-details .details .user-info-name{font-size:22px}}@media only screen and (max-width:1215px){.lesson-details .details .user-info-name{font-size:20px}}@media only screen and (max-width:479px){.lesson-details .details .user-info-name{font-size:18px}}.lesson-details .details .user-info-name div{position:relative;top:-2px;display:inline-block;height:16px;color:var(--v-dark-lighten3);cursor:pointer}.lesson-details .details .user-info-status{display:inline;font-size:13px;color:var(--v-dark-lighten3);text-transform:lowercase}.lesson-details .details .user-info-status--online{color:var(--v-green-lighten1)}.lesson-details .details .lesson-info{font-size:16px;color:var(--v-dark-lighten3);line-height:1.1}@media only screen and (min-width:768px){.lesson-details .details .lesson-info{padding-bottom:4px}}@media only screen and (min-width:1216px){.lesson-details .details .lesson-info .avatar{display:none!important}}@media only screen and (max-width:1215px){.lesson-details .details .lesson-info{display:flex;align-items:flex-end;font-size:14px}}.lesson-details .details .lesson-info>div:not(.avatar)>div:not(:last-child){margin-bottom:3px}@media only screen and (max-width:639px){.lesson-details .details .lesson-info>div:not(.avatar)>div:not(:last-child){margin-bottom:5px}}@media only screen and (min-width:640px){.lesson-details .details .lesson-info .lesson-actions-additional{display:none!important}}.lesson-actions .v-btn:not(.v-btn--icon).v-size--default,.lesson-dialog .v-btn:not(.v-btn--icon).v-size--default{min-width:158px!important;padding:0 10px!important}.lesson-actions{display:flex;width:350px;padding:18px 18px 10px 0}@media only screen and (max-width:1215px){.lesson-actions{padding:12px 12px 8px 0}}@media only screen and (max-width:767px){.lesson-actions{padding-top:10px}}@media only screen and (min-width:640px){.lesson-actions{flex-direction:column;align-items:flex-end;justify-content:space-between;flex-grow:1}}@media only screen and (max-width:639px){.lesson-actions{width:100%;margin-top:8px;padding:8px 0 0;border-top:1px solid rgba(0,0,0,.08)}}.lesson-actions-buttons{display:flex;justify-content:flex-end}@media only screen and (max-width:639px){.lesson-actions-buttons{justify-content:space-between;width:100%}.lesson-actions-buttons .v-btn{width:158px!important;margin-left:0!important;margin-right:0!important}}.lesson-actions-buttons .go-to-class-btn .icon--right{margin-left:3px}.lesson-actions-buttons .go-to-class-btn .icon--rotated{transform:rotate(-90deg)}.lesson-actions-additional{min-width:158px;font-size:13px;line-height:1.333}@media only screen and (max-width:639px){.lesson-actions-additional{font-size:inherit;line-height:inherit}}@media only screen and (max-width:639px){.lesson-actions-additional>div{margin-top:5px}}.lesson-actions-additional>div>div,.lesson-actions-additional a,.lesson-actions-additional span.action{position:relative;display:inline-block;padding-left:20px;color:var(--v-darkLight-lighten3)!important;text-decoration:none;transition:color .3s}.lesson-actions-additional>div>div .v-image,.lesson-actions-additional a .v-image,.lesson-actions-additional span.action .v-image{position:absolute;left:0;top:50%;transform:translateY(-50%)}.lesson-actions-additional a,.lesson-actions-additional span.action{cursor:pointer}.lesson-actions-additional a:hover,.lesson-actions-additional span.action:hover{color:var(--v-success-base)!important}@media only screen and (max-width:639px){.lesson-actions .lesson-actions-additional{display:none!important}}.lesson-dialog{position:absolute;top:0;left:100%;width:100%;height:100%;padding:10px 18px 12px;font-size:13px;line-height:1.2;background-color:#fff;border-radius:0 16px 16px 0;z-index:2;transition:all .3s ease-in}@media only screen and (max-width:1215px){.lesson-dialog{padding:8px 12px}}@media only screen and (max-width:991px){.lesson-dialog{left:0;top:100%}}@media only screen and (max-width:639px){.lesson-dialog.justify-center{justify-content:space-between!important}}.lesson-dialog-title{display:flex;margin-bottom:5px;font-size:20px}@media only screen and (max-width:639px){.lesson-dialog-title{margin-bottom:12px;font-size:18px}}.lesson-dialog-title-icon{display:flex;align-items:center;padding-right:4px}.lesson-dialog-content{color:var(--v-dark-lighten3);overflow-y:auto}@media only screen and (max-width:639px){.lesson-dialog-buttons{display:flex;justify-content:space-between;margin-top:8px;border-top:1px solid rgba(0,0,0,.08)}}.lesson-dialog-buttons .v-btn{max-width:246px;margin-top:8px}@media only screen and (min-width:640px){.lesson-dialog-buttons .v-btn{width:100%}}.lesson-dialog-buttons>:first-child{margin-right:16px}@media only screen and (max-width:639px){.lesson-dialog-buttons>:first-child{margin-left:4px}}.lesson-dialog--student-info .lesson-dialog-content{display:flex}@media only screen and (min-width:640px){.lesson-dialog--student-info .lesson-dialog-content{margin-right:168px}}.lesson-dialog--student-info .lesson-dialog-content>div{display:flex;flex-grow:1}@media only screen and (max-width:639px){.lesson-dialog--student-info .lesson-dialog-content>div{width:100%;max-width:100%}}@media only screen and (max-width:479px){.lesson-dialog--student-info .lesson-dialog-content>div{flex-wrap:wrap}}@media only screen and (max-width:479px){.lesson-dialog--student-info .lesson-dialog-content>div>div{padding:0!important}}@media only screen and (min-width:480px){.lesson-dialog--student-info .lesson-dialog-content>div>div:first-child{padding-right:45px;border-right:1px solid var(--v-dark-lighten3)}}.lesson-dialog--student-info .lesson-dialog-content>div>div ul{padding:0;list-style-type:none;line-height:1.2}@media only screen and (min-width:1216px){.lesson-dialog--student-info .lesson-dialog-buttons{right:18px;bottom:12px}}@media only screen and (min-width:640px){.lesson-dialog--student-info .lesson-dialog-buttons{position:absolute;right:12px;bottom:8px}}@media only screen and (max-width:639px){.lesson-dialog--student-info .lesson-dialog-buttons{display:flex;justify-content:flex-end;align-items:flex-end}}.lesson-dialog--student-info .lesson-dialog-buttons .v-btn{margin-right:0!important}.lesson-dialog--shown{left:0;transition:all .3s ease-out}@media only screen and (max-width:991px){.lesson-dialog--shown{top:0}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACTA;AACA;AACA;;;;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA7BA;AAkCA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAFA;AAKA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AA7BA;AA7CA;;AC7FA;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AAHA;;ACxCA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACvBA;AACA;AACA;;;;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAZA;AAaA;AACA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAhDA;AAjCA;;AC/GA;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACxBA;AACA;AACA;;;;;;;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAhBA;AAiBA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAFA;AAKA;AACA;AACA;AACA;AAhBA;AArCA;;AC7DA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AATA;AACA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AATA;AAWA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AA3BA;AA4BA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAJA;AAJA;AACA;AAUA;AAAA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AAKA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AADA;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAHA;AAKA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AAEA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAHA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAHA;AAMA;AACA;AACA;AACA;AACA;AACA;AA/DA;AAhIA;;ACzJA;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACnCA;AACA;AACA;;;;;;;;;;;ACFA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAJA;AASA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AAHA;AAMA;AACA;AACA;AACA;AAJA;AAOA;AACA;AAFA;AAKA;AACA;AADA;AAnBA;AAuBA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAFA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjHA;;ACRA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AClBA;AAAA;AAEA;AACA;;;;;;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AATA;AAcA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AAfA;AAhBA;;ACXA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AACA;AAiBA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzEA;AA0EA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAxBA;AAxGA;;AC/CA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;ACVA;AACA;AACA;AACA;AAGA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAdA;AADA;;ACDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AACA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAQA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;AACA;AAUA;AACA;AACA;AACA;AAGA;AADA;AAIA;AADA;AAPA;AAaA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAQA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApCA;AAzFA;;AC/CA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACvBA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AACA;AACA;AACA;AAJA;AArBA;;ACvBA;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AClCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAOA;AACA;AAAA;AACA;AAKA;AACA;AArEA;AAsEA;AACA;AACA;AAGA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAzDA;AA7FA;;AC/dA;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AClCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AACA;AAiBA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAQA;AACA;AAAA;AACA;AACA;AACA;AAjBA;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAMA;AACA;AACA;AACA;AAAA;AACA;AAKA;AACA;AACA;AACA;AAzBA;AA3CA;;ACnBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AATA;AACA;AAaA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAFA;AAKA;AAEA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAhBA;AA3BA;;ACzEA;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACnCA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;;;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAjBA;AACA;AAqBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;AAUA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAxCA;AAyCA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AANA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AApBA;AACA;AAoBA;AACA;AACA;AAGA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AACA;AACA;AAEA;AAEA;AACA;AAIA;AACA;AAAA;AACA;AAAA;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AAGA;AARA;AAcA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AAEA;AAEA;AAEA;AACA;AAFA;AAKA;AAEA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAFA;AACA;AAIA;AACA;AAGA;AAEA;AAGA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA9OA;AAzHA;;AC5FA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AChCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AATA;AAcA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAtBA;AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAvCA;;ACrBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;ACXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACPA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;ACNA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;;;;;ACLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AACA;AASA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAtBA;;ACrCA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACvBA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;;;;;;;;ACJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AATA;AACA;AAaA;AACA;AACA;AADA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AAKA;AACA;AACA;AACA;AACA;AAJA;AA3BA;;ACzBA;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;AAEA;AACA;AAEA;AACA;AADA;AAIA;AACA;AACA;AACA;AACA;AAJA;AAPA;;ACFA;AAqBA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAVA;AAFA;AACA;AAcA;AACA;AACA;AACA;AACA;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAZA;AACA;AAcA;AACA;AACA;AACA;AADA;AAGA;AACA;AANA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AAzDA;AAHA;;;;;ACrBA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AAJA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AAGA;AADA;AAGA;AACA;AACA;AAEA;AACA;AACA;;ACpDA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAWA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAGA;AAEA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAPA;AASA;AACA;AACA;AACA;AACA;AACA;AANA;AAQA;AACA;AACA;AACA;AAEA;AACA;AAGA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAnBA;AACA;AACA;AAqBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;AAeA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAHA;AAKA;AACA;AAPA;AASA;;AC/dA;AAEA;AAOA;AACA;AAEA;AACA;AACA;AACA;AAFA;AADA;AAOA;AACA;AACA;AACA;AAFA;AADA;AAOA;AACA;AACA;AACA;AACA;AAJA;AAMA;AACA;AADA;AACA;AAGA;AACA;AACA;AA7BA;AACA;AA+BA;AACA;AACA;AACA;AACA;AAJA;AACA;AAKA;AACA;AACA;AACA;AACA;AAVA;AACA;AAWA;AACA;AAbA;AACA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA;AACA;AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA;AAhCA;;;;;ACRA;AAEA;AAIA;AACA;AAAA;AAEA;AACA;AACA;AACA;AALA;AAQA;AACA;AADA;AAKA;AACA;AAQA;AACA;AACA;AAEA;AACA;AACA;AACA;AADA;AADA;AAKA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AA7BA;AAgCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAvDA;AA0DA;AACA;;AC1IA;AACA;AAkBA;AAEA;AAEA;AAEA;;;;;;;;;;;;;;AAcA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAPA;AASA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AA1DA;AAJA;AACA;AAiEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAHA;AAAA;AAAA;AAOA;AAPA;AASA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;AC7PA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAVA;AAHA;;ACJA;AACA;AAEA;AAAA;AAEA;AAFA;;ACHA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAHA;AAKA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA1BA;AA+BA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA9CA;AAmDA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAFA;AAlBA;AAuBA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AALA;AAUA;AACA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AADA;AAGA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAHA;AAlBA;AAwBA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AA1DA;AA5IA;AA6MA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAJA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACnQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAWA;AAMA;AANA;AAQA;AAEA;AACA;AADA;AAIA;AAEA;AACA;AACA;AAFA;AACA;AAKA;AACA;AAPA;AACA;AAQA;AACA;AACA;AACA;AAZA;AACA;AAaA;AACA;AAfA;AACA;AAgBA;AACA;AACA;AAEA;AArBA;AACA;AAsBA;AACA;AAxBA;AACA;AA8BA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAtCA;AACA;AA0CA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAIA;AACA;AAxDA;AA0DA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AAFA;AACA;AAQA;AACA;AAVA;AACA;AAWA;AACA;AAbA;AACA;AAcA;AACA;AAIA;AACA;AArBA;AAnEA;;;;;AChCA;AAUA;AAQA;AACA;AACA;AACA;AACA;AAGA;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAGA;AAEA;AACA;AACA;AAEA;AACA;AACA;;AClDA;AACA;AACA;AAKA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAGA;AAGA;AAgDA;AACA;AACA;AAEA;AACA;AAAA;AACA;AAEA;AACA;AADA;AAIA;AAEA;AACA;AAHA;AAMA;AACA;AACA;AAFA;AACA;AAGA;AACA;AALA;AACA;AAMA;AACA;AARA;AACA;AASA;AACA;AAXA;AACA;AAcA;AACA;AAhBA;AACA;AAmBA;AACA;AArBA;AACA;AAwBA;AACA;AA1BA;AACA;AA6BA;AACA;AA/BA;AACA;AAkCA;AACA;AApCA;AACA;AAqCA;AACA;AACA;AACA;AAzCA;AA2CA;AACA;AACA;AAFA;AACA;AAKA;AACA;AAPA;AACA;AAeA;AACA;AACA;AACA;AACA;AAHA;AAMA;AAvBA;AACA;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AA/DA;AACA;AAgEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAAA;AACA;AADA;AAGA;AACA;AACA;AACA;AAhBA;AAmBA;AA5FA;AACA;AA6FA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAHA;AAKA;AACA;AADA;AAGA;AACA;AACA;AAhBA;AArHA;AACA;AAuIA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAJA;AAFA;AArJA;AACA;AA8JA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAJA;AAMA;AAEA;AACA;AACA;AACA;AACA;AAhBA;AACA;AAkBA;AAEA;AACA;AAHA;AAAA;AAAA;AAAA;AAQA;AARA;AAWA;AAGA;AAAA;AAAA;AACA;AACA;AACA;AAFA;AAIA;AANA;AAvMA;AACA;AAmNA;AACA;AACA;AACA;AACA;AADA;AAFA;AArNA;AACA;AA2NA;AACA;AAEA;AACA;AACA;AADA;AAGA;AACA;AADA;AAGA;AACA;AARA;AA/NA;AACA;AAyOA;AAAA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AADA;AAGA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAtBA;AA9OA;AACA;AAsQA;AACA;AACA;AAEA;AA3QA;AACA;AA8QA;AACA;AAhRA;AACA;AAoRA;AACA;AACA;AAEA;AAzRA;AACA;AA4RA;AACA;AACA;AAEA;AAjSA;AACA;AAsSA;AACA;AAEA;AA1SA;AACA;AA+SA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAKA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AARA;AAWA;AArBA;AACA;AAuBA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAbA;AAeA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAxBA;AA0BA;AACA;AACA;AAEA;AADA;AACA;AAIA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAzCA;AA2CA;AACA;AArYA;AAxDA;;;;;;;;AC5EA;AACA;AACA;AAKA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAOA;AACA;AAAA;AACA;AAEA;AAEA;AACA;AACA;AAFA;AACA;AAGA;AACA;AALA;AACA;AAMA;AACA;AARA;AACA;AASA;AACA;AACA;AACA;AAEA;AAfA;AACA;AAuBA;AACA;AACA;AACA;AAEA;AA7BA;AACA;AAqCA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAIA;AACA;AAnDA;AAqDA;AACA;AACA;AAEA;AAJA;AACA;AAMA;AACA;AACA;AADA;AARA;AACA;AAWA;AACA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AArBA;AACA;AAsBA;AACA;AACA;AAEA;AACA;AACA;AACA;AAHA;AA3BA;AACA;AAgCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA1CA;AACA;AA2CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA;AAnDA;AACA;AAuDA;AACA;AAzDA;AACA;AAgEA;AACA;AACA;AADA;AAlEA;AACA;AAuEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAJA;AAOA;AAAA;AAAA;AAAA;AAAA;AAlFA;AACA;AAoFA;AACA;AACA;AADA;AAtFA;AACA;AAyFA;AACA;AACA;AAEA;AACA;AAAA;AAEA;AACA;AACA;AAJA;AAMA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;AAPA;AA9FA;AACA;AA6GA;AACA;AAEA;AACA;AADA;AAGA;AACA;AArHA;AACA;AAsHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAHA;AAYA;AACA;AA/LA;;ACxBA;AACA;AACA;AAEA;AACA;AAEA;AAGA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAFA;AACA;AAGA;AACA;AALA;AACA;AAMA;AACA;AACA;AACA;AAVA;AAHA;;;;;ACVA;AACA;AACA;AAEA;AACA;AAYA;AACA;AAAA;AACA;AAEA;AAEA;AACA;AACA;AAFA;AACA;AAGA;AACA;AALA;AACA;AAMA;AACA;AARA;AACA;AASA;AACA;AAXA;AACA;AAYA;AACA;AAdA;AACA;AAeA;AACA;AAEA;AAnBA;AACA;AAsBA;AACA;AAxBA;AACA;AAyBA;AACA;AA3BA;AACA;AAkCA;AACA;AACA;AACA;AACA;AACA;AAEA;AA1CA;AACA;AA2CA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AAIA;AACA;AA1DA;AA4DA;AACA;AACA;AACA;AACA;AAJA;AACA;AAKA;AACA;AAPA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AArBA;AACA;AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AA7BA;AACA;AA8BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAzCA;AACA;AA0CA;AACA;AA5CA;AACA;AA6CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AA9DA;AACA;AA+DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AA5EA;AAjEA;;ACnBA;AACA;AACA;AAKA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AAGA;AACA;AAAA;AACA;AAEA;AAAA;AAAA;AAEA;AACA;AADA;AAIA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAPA;AACA;AAQA;AACA;AAnBA;AACA;AAqBA;AACA;AACA;AAFA;AACA;AAGA;AACA;AALA;AACA;AAMA;AACA;AACA;AAEA;AAXA;AACA;AAYA;AACA;AACA;AACA;AACA;AADA;AAFA;AAdA;AACA;AAuBA;AACA;AAEA;AACA;AACA;AACA;AADA;AAFA;AA3BA;AACA;AAiCA;AACA;AAnCA;AACA;AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AAJA;AAtCA;AACA;AAkDA;AACA;AACA;AAAA;AAAA;AADA;AApDA;AACA;AAuDA;AACA;AAEA;AACA;AADA;AA3DA;AACA;AA8DA;AACA;AACA;AADA;AAhEA;AACA;AAmEA;AACA;AAEA;AACA;AAAA;AAEA;AACA;AAHA;AAKA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;AAIA;AAJA;AANA;AAvEA;AACA;AAoFA;AACA;AACA;AADA;AAtFA;AACA;AA2FA;AACA;AACA;AACA;AAFA;AA7FA;AACA;AAmGA;AACA;AACA;AACA;AACA;AACA;AADA;AAHA;AArGA;AACA;AA8GA;AACA;AACA;AADA;AAhHA;AACA;AAsHA;AACA;AAxHA;AACA;AAyHA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AAJA;AA3HA;AACA;AAsIA;AACA;AAxIA;AACA;AAyIA;AACA;AA3IA;AACA;AA4IA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAEA;AAFA;AAHA;AASA;AAEA;AA5JA;AACA;AA6JA;AACA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AADA;AALA;AAUA;AA1KA;AACA;AA2KA;AACA;AAEA;AA/KA;AACA;AAgLA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AADA;AAHA;AAQA;AADA;AAIA;AACA;AApMA;AACA;AAqMA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAAA;AAAA;AACA;AACA;AAHA;AAPA;AAgBA;AACA;AA9OA;;;;;AClBA;AAIA;AAKA;AAEA;AAIA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAEA;AAGA;AAAA;AAAA;AANA;AAQA;AACA;AAAA;AACA;;AC7BA;AACA;AACA;AAKA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AAAA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAHA;AAFA;AACA;AAOA;AACA;AACA;AACA;AAXA;AAYA;AACA;AACA;AACA;AADA;AAGA;AACA;AAAA;AAAA;AADA;AAIA;AACA;AADA;AAIA;AAbA;AACA;AAcA;AACA;AAEA;AAEA;AAFA;AAlBA;AACA;AAsBA;AACA;AACA;AACA;AACA;AADA;AAFA;AAxBA;AACA;AAiCA;AACA;AACA;AADA;AAnCA;AACA;AAsCA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AA9CA;AACA;AA+CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AAJA;AAlDA;AACA;AA6DA;AACA;AA/DA;AACA;AAgEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AAAA;AAFA;AAHA;AASA;AAIA;AAlFA;AACA;AAmFA;AACA;AACA;AADA;AAIA;AAEA;AA3FA;AACA;AA4FA;AACA;AACA;AACA;AACA;AADA;AAFA;AAOA;AAEA;AACA;AACA;AAzGA;AAjBA;;AChBA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AAoBA;AACA;AACA;AACA;AAEA;AAYA;AACA;AAAA;AACA;AAEA;AAEA;AACA;AACA;AAJA;AAOA;AACA;AACA;AAFA;AAKA;AACA;AACA;AAFA;AACA;AAKA;AACA;AAPA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAvDA;AACA;AAyDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA3EA;AACA;AA4EA;AACA;AA9EA;AACA;AA+EA;AACA;AAjFA;AACA;AAkFA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AAhGA;AACA;AAiGA;AACA;AACA;AAAA;AADA;AAnGA;AACA;AAsGA;AACA;AACA;AAAA;AADA;AAxGA;AACA;AA2GA;AACA;AACA;AACA;AA/GA;AAiHA;AACA;AADA;AACA;AAGA;AACA;AACA;AAtIA;AACA;AAwIA;AACA;AA1IA;AACA;AA4IA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAVA;AACA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAhBA;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AAGA;AADA;AAGA;AACA;AACA;AACA;AApDA;AACA;AAqDA;AACA;AAvDA;AACA;AAwDA;AACA;AA1DA;AACA;AA2DA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AAnEA;AACA;AAoEA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AA5EA;AACA;AA6EA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AArFA;AACA;AAsFA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AA9FA;AACA;AA+FA;AACA;AAjGA;AACA;AAkGA;AACA;AApGA;AACA;AAqGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAHA;AACA;AAKA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AADA;AAGA;AACA;AACA;AAFA;AAIA;AAlBA;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAJA;AAMA;AACA;AAAA;AACA;AACA;AAxJA;AACA;AAyJA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AADA;AAGA;AAEA;AACA;AAHA;AAAA;AAMA;AANA;AAQA;AACA;AAAA;AAAA;AACA;AACA;AAHA;AAKA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AATA;AAWA;AA7BA;AA+BA;AACA;AA1UA;;AClDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/BA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AAAA;AACA;AACA;AACA;AACA;AAJA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAZA;AA3BA;;ACDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}