exports.ids = [24];
exports.modules = {

/***/ 1205:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./helpers/index.js
var helpers = __webpack_require__(501);

// EXTERNAL MODULE: ./helpers/constants.js
var constants = __webpack_require__(69);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/UploadFiles.vue?vue&type=script&lang=js&


/* harmony default export */ var UploadFilesvue_type_script_lang_js_ = ({
  computed: {
    lessonId() {
      return this.$store.state.classroom.lessonId;
    },

    role() {
      return this.$store.getters['classroom/role'];
    },

    acceptedFiles() {
      return this.$store.state.classroom.acceptedFiles;
    }

  },
  methods: {
    async uploadFiles(files) {
      files = [...files];
      const formData = new FormData();

      for (let i = 0; i <= files.length - 1; i++) {
        const file = files[i];
        const fileExtension = Object(helpers["getFileExtension"])(file.name);

        if (file.size > constants["a" /* MAX_FILE_SIZE */]) {
          await this.$store.dispatch('snackbar/error', {
            errorMessage: this.$t('filename_size_should_be_less_than', {
              fileName: file.name,
              value: `${(constants["a" /* MAX_FILE_SIZE */] / 8 / 1000).toFixed(0)} Mb`
            }),
            timeout: 5000
          });
          continue;
        }

        if (this.acceptedFiles.officeTypes.includes(fileExtension)) {
          const {
            data,
            fileName
          } = await this.$store.dispatch('classroom/convertOfficeToPdf', file);
          formData.append(i.toString(), new Blob([data]), fileName);
        } else {
          formData.append(i.toString(), file);
        }
      }

      this.$store.dispatch('classroom/uploadFiles', formData).then(assets => {
        let offsetX = 0;
        let offsetY = 0;
        this.$store.commit('classroom/addAssets', assets);
        assets.forEach(asset => {
          var _this$acceptedFiles, _this$acceptedFiles2, _this$acceptedFiles3;

          const item = {
            id: asset.id,
            lessonId: this.lessonId,
            asset: { ...asset.asset,
              index: this.$store.state.classroom.maxIndex + 1,
              owner: this.role,
              top: this.$store.getters['classroom/zoomAsset'].asset.y + offsetY + 100,
              left: this.viewportWidth / 2 + this.$store.getters['classroom/zoomAsset'].asset.x + offsetX - 250
            }
          };
          const ext = Object(helpers["getFileExtension"])(item.asset.path);
          let type;

          if ((_this$acceptedFiles = this.acceptedFiles) !== null && _this$acceptedFiles !== void 0 && _this$acceptedFiles.pdfTypes.includes(ext)) {
            type = 'pdf';
          } else if ((_this$acceptedFiles2 = this.acceptedFiles) !== null && _this$acceptedFiles2 !== void 0 && _this$acceptedFiles2.imageTypes.includes(ext)) {
            type = 'image';
          } else if ((_this$acceptedFiles3 = this.acceptedFiles) !== null && _this$acceptedFiles3 !== void 0 && _this$acceptedFiles3.audioTypes.includes(ext)) {
            type = 'audio';
          } else {
            return;
          }

          item.asset.type = type;
          this.$store.commit('classroom/moveAsset', item);
          this.$store.dispatch('classroom/moveAsset', item);
          this.$socket.emit('asset-added', item);
          offsetX += 50;
          offsetY += 50;
        });
      }).catch(e => {
        // @TODO classroom
        // Bugsnag.notify(e)
        throw e;
      });
    }

  }
});
// CONCATENATED MODULE: ./mixins/UploadFiles.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_UploadFilesvue_type_script_lang_js_ = (UploadFilesvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/UploadFiles.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_UploadFilesvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "20b20e0a"
  
)

/* harmony default export */ var UploadFiles = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1251:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1339);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("3a5e080a", content, true, context)
};

/***/ }),

/***/ 1338:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Toolbar_vue_vue_type_style_index_0_id_53b5e223_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1251);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Toolbar_vue_vue_type_style_index_0_id_53b5e223_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Toolbar_vue_vue_type_style_index_0_id_53b5e223_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Toolbar_vue_vue_type_style_index_0_id_53b5e223_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Toolbar_vue_vue_type_style_index_0_id_53b5e223_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1339:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".toolbar[data-v-53b5e223]{position:fixed;z-index:99999!important}label.popup-load-files-label-upload[data-v-53b5e223]{margin-right:0}.toolbar-buttons-wrapper[data-v-53b5e223]{position:absolute;top:50%;right:2%;transform:translateY(-50%)}.cursor-pointer[data-v-53b5e223],.cursor-pointer *[data-v-53b5e223]{cursor:pointer!important}.toolbar-buttons[data-v-53b5e223]{margin-bottom:0;padding:8px 0}.toolbar-buttons-horizontal-file.toolbar-show[data-v-53b5e223],.toolbar-buttons-horizontal.toolbar-show[data-v-53b5e223]{display:flex!important}.toolbar-buttons li[data-v-53b5e223]{list-style:none}.toolbar-button-wrapper form[data-v-53b5e223]{display:inline-block;width:100%}.toolbar-button-wrapper-horizontal[data-v-53b5e223]{width:40px;height:40px;display:flex;justify-content:center;position:relative}.toolbar-button-wrapper-pencil>button[data-v-53b5e223]{padding:9px}.toolbar-button-wrapper-exit button[data-v-53b5e223]{padding-left:7px;padding-right:10px}.toolbar-button-wrapper-reset button[data-v-53b5e223]{padding-left:10px;padding-right:10px}.toolbar-button-wrapper-finish button[data-v-53b5e223]{padding-right:7px}.toolbar-button-wrapper-horizontal-books button[data-v-53b5e223]{padding:9px}.toolbar-button-wrapper-horizontal-laptop button[data-v-53b5e223]{padding-top:10px}.toolbar-buttons-horizontal>ul[data-v-53b5e223]{display:flex;padding:0 10px;background-color:#fff;border-radius:6px;box-shadow:0 2px 10px rgba(0,0,0,.25)}.toolbar-button-wrapper-horizontal-draw-line button[data-v-53b5e223]{padding:10px 5px 6px 10px}.toolbar-button-wrapper-horizontal-draw-pencil button[data-v-53b5e223]{padding:9px}.toolbar-button-item-hand[data-v-53b5e223]{padding-left:0}.toolbar-buttons-horizontal>ul li[data-v-53b5e223]:first-child,.toolbar-buttons-horizontal>ul li:first-child button[data-v-53b5e223]{border-bottom-left-radius:6px!important;border-top-left-radius:6px!important}.toolbar-buttons-horizontal>ul li[data-v-53b5e223]:last-child,.toolbar-buttons-horizontal>ul li:last-child button[data-v-53b5e223]{border-bottom-right-radius:6px!important;border-top-right-radius:6px!important}#toolbar-switch[data-v-53b5e223]{border-top-left-radius:4px;border-top-right-radius:4px}.toolbar--student .toolbar-button-item.selected:not(:disabled) svg[data-v-53b5e223],.toolbar--student .toolbar-button-wrapper-horizontal:hover svg[data-v-53b5e223],.toolbar--student .toolbar-button-wrapper:hover>button:enabled>svg[data-v-53b5e223],.toolbar--student .toolbar-button-wrapper:hover>form>button:enabled>svg[data-v-53b5e223]{color:var(--v-studentColor-base)!important}.toolbar--teacher .toolbar-button-item.selected:not(:disabled) svg[data-v-53b5e223],.toolbar--teacher .toolbar-button-wrapper-horizontal:hover svg[data-v-53b5e223],.toolbar--teacher .toolbar-button-wrapper:hover>button:enabled>svg[data-v-53b5e223],.toolbar--teacher .toolbar-button-wrapper:hover>form>button:enabled>svg[data-v-53b5e223]{color:var(--v-teacherColor-base)!important}.toolbar-button-wrapper .toolbar-button-item:disabled svg[data-v-53b5e223]{color:#c6c6c6!important;fill:#c6c6c6!important;stroke:#c6c6c6!important}.toolbar-button-item svg[data-v-53b5e223]{color:var(--v-darkLight-base)}.hover-btn-info-horizontal[data-v-53b5e223]{top:-20px;right:-60%}.toolbar-button-replace+.hover-btn-info[data-v-53b5e223]{top:40%}.selected[data-v-53b5e223]{border-bottom:none}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1409:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/Toolbar.vue?vue&type=template&id=53b5e223&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:['toolbar', ("toolbar--" + _vm.role)],style:(_vm.style)},[_vm._ssrNode("<ul id=\"toolbar-buttons\" class=\"toolbar-buttons\" data-v-53b5e223><li class=\"toolbar-button-wrapper\" data-v-53b5e223><button id=\"toolbar-switch\" data-toolbar-default-cursor"+(_vm._ssrAttr("disabled",_vm.isLockedForStudent))+(_vm._ssrClass(null,[
          'toolbar-button-item toolbar-button-pointer cursor-pointer',
          { selected: _vm.currentTool === 'pointer' } ]))+" data-v-53b5e223><svg width=\"32\" height=\"34\" viewBox=\"0 0 32 34\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#pointer")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info\" data-v-53b5e223>"+_vm._ssrEscape(_vm._s(_vm.$t('default_cursor')))+"</div></li> <li class=\"toolbar-button-wrapper toolbar-button-wrapper-pencil\" data-v-53b5e223><button"+(_vm._ssrAttr("disabled",_vm.isLockedForStudent))+(_vm._ssrClass(null,[
          'toolbar-button-item toolbar-button-hand cursor-pointer',
          {
            selected:
              _vm.currentTool === 'line' ||
              _vm.currentTool === 'circle' ||
              _vm.currentTool === 'triangle' ||
              _vm.currentTool === 'square' ||
              _vm.currentTool === 'pen',
          } ]))+" data-v-53b5e223><svg width=\"33\" height=\"33\" viewBox=\"0 0 33 33\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#pencil")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info\" data-v-53b5e223>"+_vm._ssrEscape(_vm._s(_vm.$t('drawing')))+"</div> <div"+(_vm._ssrClass(null,[
          'toolbar-buttons-horizontal',
          { 'toolbar-show': _vm.currentHorizontalMenu === 'toolbar-horizontal' } ]))+" data-v-53b5e223><ul data-v-53b5e223><li class=\"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-draw-line\" data-v-53b5e223><button data-toolbar-tool-line"+(_vm._ssrClass(null,[
                'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                { selected: _vm.currentTool === 'line' } ]))+" data-v-53b5e223><svg width=\"39\" height=\"37\" viewBox=\"0 0 39 37\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#draw-line")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info hover-horizontal-button\" data-v-53b5e223>"+_vm._ssrEscape("\n              "+_vm._s(_vm.$t('draw_line'))+"\n            ")+"</div></li> <li class=\"toolbar-button-wrapper-horizontal\" data-v-53b5e223><button data-toolbar-tool-circle"+(_vm._ssrClass(null,[
                'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                { selected: _vm.currentTool === 'circle' } ]))+" data-v-53b5e223><svg width=\"36\" height=\"37\" viewBox=\"0 0 39 40\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#draw-circle")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info hover-horizontal-button\" data-v-53b5e223>"+_vm._ssrEscape("\n              "+_vm._s(_vm.$t('draw_circle'))+"\n            ")+"</div></li> <li class=\"toolbar-button-wrapper-horizontal\" data-v-53b5e223><button data-toolbar-tool-triangle"+(_vm._ssrClass(null,[
                'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                { selected: _vm.currentTool === 'triangle' } ]))+" data-v-53b5e223><svg width=\"41\" height=\"34\" viewBox=\"0 0 41 34\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#draw-triangle")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info hover-horizontal-button\" data-v-53b5e223>"+_vm._ssrEscape("\n              "+_vm._s(_vm.$t('draw_triangle'))+"\n            ")+"</div></li> <li class=\"toolbar-button-wrapper-horizontal\" data-v-53b5e223><button data-toolbar-tool-square"+(_vm._ssrClass(null,[
                'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                { selected: _vm.currentTool === 'square' } ]))+" data-v-53b5e223><svg width=\"36\" height=\"38\" viewBox=\"0 0 36 38\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#draw-square")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info hover-horizontal-button\" data-v-53b5e223>"+_vm._ssrEscape("\n              "+_vm._s(_vm.$t('draw_square'))+"\n            ")+"</div></li> <li class=\"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-draw-pencil\" data-v-53b5e223><button data-toolbar-tool-pen"+(_vm._ssrClass(null,[
                'toolbar-button-item toolbar-button-item-horizontal cursor-pointer',
                { selected: _vm.currentTool === 'pen' } ]))+" data-v-53b5e223><svg width=\"33\" height=\"33\" viewBox=\"0 0 33 33\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#pencil")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info hover-horizontal-button\" data-v-53b5e223>"+_vm._ssrEscape("\n              "+_vm._s(_vm.$t('enable_drawing_tool'))+"\n            ")+"</div></li></ul></div></li> <li class=\"toolbar-button-wrapper\" data-v-53b5e223><button data-toolbar-eraser"+(_vm._ssrAttr("disabled",_vm.isLockedForStudent))+(_vm._ssrClass(null,[
          'toolbar-button-item cursor-pointer',
          { selected: _vm.currentTool === 'eraser' } ]))+" data-v-53b5e223><svg width=\"35\" height=\"31\" viewBox=\"0 0 35 31\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#lastic")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info\" data-v-53b5e223>"+_vm._ssrEscape("\n        "+_vm._s(_vm.$t('enable_erasing_tool'))+"\n      ")+"</div></li> <li class=\"toolbar-button-wrapper\" data-v-53b5e223><button id=\"toolbar-button-video\" data-toolbar-add-video"+(_vm._ssrAttr("disabled",_vm.isLockedForStudent))+" class=\"toolbar-button-item cursor-pointer\" data-v-53b5e223><svg width=\"39\" height=\"31\" viewBox=\"0 0 39 31\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#play")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info\" data-v-53b5e223>"+_vm._ssrEscape(_vm._s(_vm.$t('add_video')))+"</div></li> "+((_vm.isTeacher)?("<li class=\"toolbar-button-wrapper\" data-v-53b5e223><button data-toolbar-buzz-student"+(_vm._ssrAttr("disabled",_vm.alertDisabled))+" class=\"toolbar-button-item cursor-pointer\" data-v-53b5e223><svg width=\"35\" height=\"38\" viewBox=\"0 0 35 38\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#ring")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info\" data-v-53b5e223>"+_vm._ssrEscape(_vm._s(_vm.$t('buzz_student')))+"</div></li>"):"<!---->")+" <li class=\"toolbar-button-wrapper\" data-v-53b5e223><button class=\"toolbar-button-item toolbar-button-hand cursor-pointer\" data-v-53b5e223><svg width=\"29\" height=\"38\" viewBox=\"0 0 29 38\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#library")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info\" data-v-53b5e223>"+_vm._ssrEscape("\n        "+_vm._s(_vm.$t('library'))+"\n      ")+"</div> <div"+(_vm._ssrClass(null,[
          'toolbar-buttons-horizontal toolbar-buttons-horizontal-file',
          {
            'toolbar-show':
              _vm.currentHorizontalMenu === 'toolbar-horizontal-file',
          } ]))+" data-v-53b5e223><ul data-v-53b5e223><li class=\"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-books\" data-v-53b5e223><button id=\"load-files-library\" data-toolbar-library class=\"toolbar-button-item toolbar-button-item-horizontal cursor-pointer\" data-v-53b5e223><svg width=\"38\" height=\"38\" viewBox=\"0 0 38 38\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#books")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info hover-horizontal-button\" data-v-53b5e223>"+_vm._ssrEscape("\n              "+_vm._s(_vm.$t('select_from_library'))+"\n            ")+"</div></li> <li class=\"toolbar-button-wrapper-horizontal toolbar-button-wrapper-horizontal-laptop\" data-v-53b5e223><button data-toolbar-computer class=\"toolbar-button-item toolbar-button-item-horizontal cursor-pointer\" data-v-53b5e223><label class=\"popup-load-files-label-upload popup-load-files-label-upload-laptop\" data-v-53b5e223><svg width=\"41\" height=\"34\" viewBox=\"0 0 41 34\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#laptop")))+" data-v-53b5e223></use></svg> <input id=\"upload-library-files-laptop\" type=\"file\" multiple=\"multiple\""+(_vm._ssrAttr("accept",_vm.acceptedFilesStr))+" class=\"popup-load-files-btn-upload\" data-v-53b5e223></label></button> <div class=\"hover-btn-info hover-horizontal-button\" data-v-53b5e223>"+_vm._ssrEscape("\n              "+_vm._s(_vm.$t('upload_from_computer'))+"\n            ")+"</div></li></ul></div></li> "+((_vm.isTeacher)?("<li class=\"toolbar-button-wrapper\" data-v-53b5e223><button data-toolbar-lock class=\"toolbar-button-item cursor-pointer\" data-v-53b5e223><svg width=\"38\" height=\"50\" viewBox=\"0 0 38 50\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#" + (_vm.isLocked ? 'lock' : 'unlock'))))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info\" data-v-53b5e223>"+((_vm.isLocked)?(_vm._ssrEscape("\n          "+_vm._s(_vm.$t('enable_moving_resizing_drawing_for_student'))+"\n        ")):(_vm._ssrEscape("\n          "+_vm._s(_vm.$t('disable_moving_resizing_drawing_for_student'))+"\n        ")))+"</div></li>"):"<!---->")+" <li class=\"toolbar-button-wrapper toolbar-button-wrapper-reset\" data-v-53b5e223><button data-toolbar-reset"+(_vm._ssrAttr("disabled",_vm.isLockedForStudent))+" class=\"toolbar-button-item cursor-pointer\" data-v-53b5e223><svg width=\"36\" height=\"36\" viewBox=\"0 0 37 37\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#restore")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info\" data-v-53b5e223>"+_vm._ssrEscape("\n        "+_vm._s(_vm.$t('restore_whiteboard_video_to_original_positions'))+"\n      ")+"</div></li> <li class=\"toolbar-button-wrapper toolbar-button-wrapper-exit\" data-v-53b5e223><button data-toolbar-exit class=\"toolbar-button-item cursor-pointer\" data-v-53b5e223><svg width=\"36\" height=\"36\" viewBox=\"0 0 37 37\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#exit")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info\" data-v-53b5e223>"+_vm._ssrEscape(_vm._s(_vm.$t('exit_class')))+"</div></li> "+((_vm.isTeacher)?("<li class=\"toolbar-button-wrapper\" data-v-53b5e223><button"+(_vm._ssrAttr("disabled",_vm.isLessonFinished || !_vm.isFinishedAllowed))+" class=\"toolbar-button-item toolbar-button-hand cursor-pointer\" data-v-53b5e223><svg width=\"43\" height=\"38\" viewBox=\"0 0 43 38\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#tick")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info\" data-v-53b5e223>"+_vm._ssrEscape("\n        "+_vm._s(_vm.$t(_vm.isLessonFinished ? 'class_finished' : 'finish_class'))+"\n      ")+"</div> <div"+(_vm._ssrClass(null,[
          'toolbar-buttons-horizontal toolbar-buttons-horizontal-file',
          {
            'toolbar-show':
              _vm.currentHorizontalMenu === 'toolbar-horizontal-finish',
          } ]))+" data-v-53b5e223><ul data-v-53b5e223><li class=\"toolbar-button-wrapper-horizontal toolbar-button-wrapper-finish\" data-v-53b5e223><button data-toolbar-finish type=\"submit\" class=\"toolbar-button-item toolbar-button-item-horizontal cursor-pointer\" data-v-53b5e223><svg width=\"43\" height=\"38\" viewBox=\"0 0 43 38\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#tick")))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info hover-horizontal-button\" data-v-53b5e223>"+_vm._ssrEscape("\n              "+_vm._s(_vm.$t('finish_class'))+"\n            ")+"</div></li></ul></div></li>"):"<!---->")+" "+((_vm.isStudent)?("<li class=\"toolbar-button-wrapper\" data-v-53b5e223><button disabled=\"disabled\" class=\"toolbar-button-item cursor-pointer\" data-v-53b5e223><svg width=\"38\" height=\"50\" viewBox=\"0 0 38 50\" class=\"toolbar-button-icon\" data-v-53b5e223><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(505)) + "#" + (_vm.isLocked ? 'lock' : 'unlock'))))+" data-v-53b5e223></use></svg></button> <div class=\"hover-btn-info\" data-v-53b5e223>"+((_vm.isLocked)?(_vm._ssrEscape("\n          "+_vm._s(_vm.$t('moving_resizing_drawing_are_disabled'))+"\n        ")):(_vm._ssrEscape("\n          "+_vm._s(_vm.$t('classroom_controls_are_unlocked'))+"\n        ")))+"</div></li>"):"<!---->")+"</ul>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/classroom/Toolbar.vue?vue&type=template&id=53b5e223&scoped=true&

// EXTERNAL MODULE: ./helpers/constants.js
var constants = __webpack_require__(69);

// EXTERNAL MODULE: ./mixins/SetTool.vue + 2 modules
var SetTool = __webpack_require__(966);

// EXTERNAL MODULE: ./mixins/UploadFiles.vue + 2 modules
var UploadFiles = __webpack_require__(1205);

// EXTERNAL MODULE: ./mixins/StatusOnline.vue + 2 modules
var StatusOnline = __webpack_require__(992);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/Toolbar.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var Toolbarvue_type_script_lang_js_ = ({
  name: 'Toolbar',
  mixins: [SetTool["a" /* default */], UploadFiles["a" /* default */], StatusOnline["a" /* default */]],
  props: {
    studentId: {
      type: String,
      required: true
    },
    file: {
      type: Object,
      required: true
    },
    viewportWidth: {
      type: Number,
      required: true
    },
    viewportHeight: {
      type: Number,
      required: true
    },
    scale: {
      type: Number,
      default: 1
    },
    minZoom: {
      type: Number,
      required: true
    },
    isFinishedAllowed: {
      type: Boolean,
      required: true
    }
  },

  data() {
    return {
      buzzed: false,
      currentTool: 'pointer',
      currentHorizontalMenu: null,
      offset: 5
    };
  },

  computed: {
    isCanvasOversizeX() {
      return constants["n" /* mainCanvasWidth */] > this.viewportWidth;
    },

    isScaledCanvasOversizeX() {
      return constants["n" /* mainCanvasWidth */] * this.scale > this.viewportWidth;
    },

    isCanvasOversizeY() {
      return constants["k" /* mainCanvasHeight */] > this.viewportHeight;
    },

    isScaledCanvasOversizeY() {
      return constants["k" /* mainCanvasHeight */] * this.scale > this.viewportHeight;
    },

    style() {
      return {
        bottom: this.isScaledCanvasOversizeY ? '10px' : `${this.viewportHeight - constants["k" /* mainCanvasHeight */] * this.scale + this.offset * 2}px`,
        right: this.isScaledCanvasOversizeX ? '10px' : `${this.viewportWidth - constants["n" /* mainCanvasWidth */] * this.scale + this.offset * 2}px`
      };
    },

    studentStatus() {
      var _this$studentId;

      // return this.$store.state.socket.connectedUserIds.includes(this.studentId)
      let status = 'offline';

      if (Object.prototype.hasOwnProperty.call(this.userStatuses, (_this$studentId = this.studentId) === null || _this$studentId === void 0 ? void 0 : _this$studentId.toString())) {
        status = this.userStatuses[this.studentId];
      }

      return status;
    },

    alertDisabled() {
      return this.buzzed || this.studentStatus !== 'online';
    },

    maxIndex() {
      return this.$store.state.classroom.maxIndex + 100;
    },

    isLocked() {
      var _this$file, _this$file$asset;

      return (_this$file = this.file) === null || _this$file === void 0 ? void 0 : (_this$file$asset = _this$file.asset) === null || _this$file$asset === void 0 ? void 0 : _this$file$asset.isLocked;
    },

    isTeacher() {
      return this.$store.getters['user/isTeacher'];
    },

    isStudent() {
      return this.$store.getters['user/isStudent'];
    },

    isLockedForStudent() {
      return this.isLocked && this.isStudent;
    },

    lessonId() {
      return this.$store.state.classroom.lessonId;
    },

    isLessonFinished() {
      return this.$store.getters['classroom/isLessonFinished'];
    },

    defaultZoomIndex() {
      return this.minZoom > 1 ? this.minZoom : 1;
    },

    acceptedFilesStr() {
      return this.$store.getters['classroom/acceptedFilesStr'];
    }

  },
  watch: {
    isLockedForStudent(newValue, oldValue) {
      if (newValue) {
        this.resetCurrentValues();
        this.$store.commit('classroom/closeVideoInput');
      }
    }

  },

  beforeMount() {
    this.arrStatusId = [this.studentId];
    this.refreshStatusOnline();
  },

  beforeDestroy() {
    this.resetCurrentValues();
  },

  methods: {
    selectToolClickHandler(toolName, cursorName) {
      this.currentTool = toolName;
      this.currentHorizontalMenu = null;
      this.setTool(toolName, cursorName);
    },

    uploadFromComputer(event) {
      this.currentHorizontalMenu = null;
      this.uploadFiles(event.target.files);
    },

    buzz() {
      this.buzzed = true;
      setTimeout(() => {
        this.buzzed = false;
      }, 30000);
      this.$store.dispatch('classroom/buzz', this.lessonId);
    },

    reset() {
      let height, ratio;
      let i = 1;
      let offsetX = 0;
      let offsetY = 0;
      this.$store.state.classroom.assets.slice(0).forEach(asset => {
        const _asset = { ...asset.asset
        };
        i++;

        switch (_asset.type) {
          case 'shape':
          case 'lock':
            break;

          case 'editor':
            _asset.width = (this.isCanvasOversizeX ? this.viewportWidth : constants["n" /* mainCanvasWidth */]) * 0.66;
            height = (this.isCanvasOversizeY ? this.viewportHeight : constants["k" /* mainCanvasHeight */]) * 0.8;

            if (height > 1200) {
              height = 1200;
            }

            if (height < 400) {
              height = 400;
            }

            _asset.height = height - this.offset * 2;
            _asset.top = this.offset;
            _asset.left = this.offset;
            _asset.index = 1;
            break;

          case 'whereby':
            _asset.width = 400;
            _asset.height = 300;
            _asset.top = this.offset;
            _asset.left = (this.isCanvasOversizeX ? this.viewportWidth : constants["n" /* mainCanvasWidth */]) - _asset.width - this.offset;
            _asset.index = i;
            break;

          case 'pdf':
          case 'image':
          case 'video':
          case 'audio':
            ratio = constants["j" /* defaultWidth */] / asset.asset.width;
            _asset.width = constants["j" /* defaultWidth */];
            _asset.height = _asset.height * ratio;
            _asset.top = this.$store.getters['classroom/zoomAsset'].asset.y + offsetY + 100;
            _asset.left = this.viewportWidth / 2 + this.$store.getters['classroom/zoomAsset'].asset.x + offsetX - 250;
            _asset.index = i;
            offsetX += 50;
            offsetY += 50;
            break;

          case 'zoom':
            _asset.zoomIndex = this.defaultZoomIndex;
            _asset.x = 0;
            _asset.y = 0;
            break;

          default:
        }

        this.$store.commit('classroom/moveAsset', {
          id: asset.id,
          asset: _asset
        });
        this.$store.dispatch('classroom/moveAsset', {
          id: asset.id,
          lessonId: asset.lessonId,
          asset: _asset
        });
      });
    },

    toggleVideoInput() {
      this.$store.commit('classroom/toggleVideoInput');
    },

    openLibrary() {
      this.currentHorizontalMenu = null;
      this.$store.commit('classroom/toggleLibrary');
    },

    toggleStudentRoomStatus() {
      const asset = {
        isLocked: !this.isLocked
      };
      this.$store.commit('classroom/moveAsset', {
        id: this.file.id,
        asset
      });
      this.$store.dispatch('classroom/moveAsset', {
        id: this.file.id,
        lessonId: this.lessonId,
        asset
      });
    },

    resetCurrentValues() {
      this.currentTool = 'pointer';
      this.currentHorizontalMenu = null;
    },

    finishLesson() {
      this.$store.dispatch('lesson/finishLesson', this.lessonId).then(() => {
        this.exitLesson();
      }).catch(e => {
        this.$store.dispatch('snackbar/error');
        console.info(e);
      });
    },

    exitLesson() {
      window.location = '/user/lessons'; // window.location is needed to trigger event 'user-left-classroom'
    }

  }
});
// CONCATENATED MODULE: ./components/classroom/Toolbar.vue?vue&type=script&lang=js&
 /* harmony default export */ var classroom_Toolbarvue_type_script_lang_js_ = (Toolbarvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/classroom/Toolbar.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1338)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  classroom_Toolbarvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "53b5e223",
  "0789be0a"
  
)

/* harmony default export */ var Toolbar = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 966:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/SetTool.vue?vue&type=script&lang=js&
/* harmony default export */ var SetToolvue_type_script_lang_js_ = ({
  computed: {
    role() {
      return this.$store.getters['classroom/role'];
    }

  },
  methods: {
    setTool(toolName, cursorName) {
      this.$store.commit('classroom/enableContainerComponent', toolName === 'pointer');
      this.$socket.emit('cursor-moved', {
        tool: toolName,
        cursor: cursorName.replace(/(-cursor|cursor-)/i, ''),
        lessonId: this.$store.state.classroom.lessonId
      });
      this.$store.commit('classroom/setUserTool', toolName);
      this.$store.commit('classroom/setUserCursor', cursorName);
      const el = document.body;
      const classList = el.classList;
      this.removeCursors(classList);
      el.classList.add(`${this.role}-${cursorName}`);
      this.classList = el.classList;
    },

    removeCursors(classList) {
      classList.forEach(item => {
        if (item.includes('cursor')) {
          document.body.classList.remove(item);
        }
      });
    }

  }
});
// CONCATENATED MODULE: ./mixins/SetTool.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_SetToolvue_type_script_lang_js_ = (SetToolvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/SetTool.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_SetToolvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "2e32be2e"
  
)

/* harmony default export */ var SetTool = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 992:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: external "core-js/modules/esnext.set.add-all.js"
var esnext_set_add_all_js_ = __webpack_require__(836);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.delete-all.js"
var esnext_set_delete_all_js_ = __webpack_require__(837);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.difference.js"
var esnext_set_difference_js_ = __webpack_require__(838);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.every.js"
var esnext_set_every_js_ = __webpack_require__(839);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.filter.js"
var esnext_set_filter_js_ = __webpack_require__(840);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.find.js"
var esnext_set_find_js_ = __webpack_require__(841);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.intersection.js"
var esnext_set_intersection_js_ = __webpack_require__(842);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.is-disjoint-from.js"
var esnext_set_is_disjoint_from_js_ = __webpack_require__(843);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.is-subset-of.js"
var esnext_set_is_subset_of_js_ = __webpack_require__(844);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.is-superset-of.js"
var esnext_set_is_superset_of_js_ = __webpack_require__(845);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.join.js"
var esnext_set_join_js_ = __webpack_require__(846);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.map.js"
var esnext_set_map_js_ = __webpack_require__(847);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.reduce.js"
var esnext_set_reduce_js_ = __webpack_require__(848);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.some.js"
var esnext_set_some_js_ = __webpack_require__(849);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.symmetric-difference.js"
var esnext_set_symmetric_difference_js_ = __webpack_require__(850);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.union.js"
var esnext_set_union_js_ = __webpack_require__(851);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/StatusOnline.vue?vue&type=script&lang=js&
















/* harmony default export */ var StatusOnlinevue_type_script_lang_js_ = ({
  data() {
    return {
      timeoutId: null,
      userStatuses: {},
      arrStatusId: []
    };
  },

  computed: {
    preparedArr() {
      return [...new Set(this.arrStatusId)];
    }

  },

  mounted() {
    this.timeoutId = window.setInterval(() => {
      this.refreshStatusOnline();

      if (!this.arrStatusId.length) {
        this.clearInterval();
      }
    }, 10000);
  },

  beforeDestroy() {
    if (this.timeoutId) {
      this.clearInterval();
    }
  },

  methods: {
    refreshStatusOnline() {
      if (this.arrStatusId.length) {
        this.$store.dispatch('user/refreshStatusOnline', this.preparedArr).then(res => this.userStatuses = res);
      }
    },

    clearInterval() {
      window.clearInterval(this.timeoutId);
      this.timeoutId = null;
    }

  }
});
// CONCATENATED MODULE: ./mixins/StatusOnline.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_StatusOnlinevue_type_script_lang_js_ = (StatusOnlinevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/StatusOnline.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_StatusOnlinevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "2b0aab01"
  
)

/* harmony default export */ var StatusOnline = __webpack_exports__["a"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=classroom-toolbar.js.map