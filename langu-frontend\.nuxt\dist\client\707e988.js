(window.webpackJsonp=window.webpackJsonp||[]).push([[133,120,131,132],{1375:function(t,e,n){"use strict";n.r(e);var r={name:"UserSettingTemplate",props:{title:{type:String,required:!0},hideFooter:{type:Boolean,default:!1},customValid:{type:Boolean,default:!0},submitFunc:{type:Function,default:function(){}}},data:function(){return{formValid:!0}},computed:{valid:function(){return this.formValid&&this.customValid}},mounted:function(){this.validate()},methods:{validate:function(){this.$refs.form.validate()},submit:function(){this.valid&&this.submitFunc()}}},l=(n(1419),n(22)),o=n(42),c=n.n(o),d=n(1327),m=n(1363),component=Object(l.a)(r,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("v-form",{ref:"form",attrs:{value:t.formValid},on:{validate:t.validate,submit:function(e){return e.preventDefault(),t.submit.apply(null,arguments)},input:function(e){t.formValid=e}}},[r("div",{staticClass:"user-settings-panel"},[r("div",{staticClass:"panel"},[t.$vuetify.breakpoint.smAndUp?r("div",{staticClass:"panel-head d-none d-sm-block"},[r("div",{staticClass:"panel-head-title font-weight-medium mb-2 mb-sm-3 mb-md-5"},[t._v("\n          "+t._s(t.title)+"\n        ")])]):t._e(),t._v(" "),r("div",{staticClass:"panel-body"},[t._t("default")],2),t._v(" "),t.hideFooter?t._e():r("div",{staticClass:"panel-footer d-flex justify-center justify-sm-end"},[r("v-btn",{staticClass:"font-weight-medium",attrs:{color:"primary",type:"submit",disabled:!t.valid}},[r("svg",{staticClass:"mr-1",attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[r("use",{attrs:{"xlink:href":n(91)+"#save-icon"}})]),t._v("\n          "+t._s(t.$t("save_changes"))+"\n        ")])],1)])])])}),[],!1,null,null,null);e.default=component.exports;c()(component,{VBtn:d.a,VForm:m.a})},1385:function(t,e,n){var content=n(1420);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("419d3f06",content,!0,{sourceMap:!1})},1419:function(t,e,n){"use strict";n(1385)},1420:function(t,e,n){var r=n(18)(!1);r.push([t.i,".user-settings-panel{padding:44px;border-radius:20px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1)}@media only screen and (max-width:1439px){.user-settings-panel{padding:24px}}@media only screen and (max-width:767px){.user-settings-panel{padding:0;box-shadow:none}}.user-settings-panel .row{margin:0 -14px!important}.user-settings-panel .col{padding:0 14px!important}.user-settings-panel .panel{color:var(--v-greyDark-base)}.user-settings-panel .panel-head-title{font-size:24px;line-height:1.333;color:var(--v-darkLight-base)}@media only screen and (max-width:1439px){.user-settings-panel .panel-head-title{font-size:20px}}.user-settings-panel .panel-body .chips>div{margin-top:6px}.user-settings-panel .panel-body .price-input .v-text-field--outlined.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot{min-height:32px!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr>th{color:var(--v-dark-base)}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr:last-child>th{border:none!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:hover:not(.v-data-table__expanded__content):not(.v-data-table__empty-wrapper){background:none}.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tbody>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tfoot>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>thead>tr>td{height:38px;color:var(--v-greyDark-base)}.user-settings-panel .panel-footer{margin-top:115px}@media only screen and (max-width:1439px){.user-settings-panel .panel-footer{margin-top:56px}}.user-settings-panel .panel-footer .v-btn{letter-spacing:.1px}@media only screen and (max-width:479px){.user-settings-panel .panel-footer .v-btn{width:100%!important}}.user-settings-panel .l-checkbox .v-input--selection-controls__input{width:16px;height:16px;margin-top:2px}",""]),t.exports=r},1523:function(t,e,n){"use strict";n.r(e);n(31);var r=n(370),l=/^\d+\.?\d{0,2}$/,o={name:"LessonPrice",components:{TextInput:r.default},props:{value:{type:[String,Number],required:!0},rules:{type:Array,default:function(){return[]}},length:{type:Number,required:!0,default:30},freeTrial:{type:Boolean,required:!1,default:!1}},data:function(){return{key:1,keyCode:null}},computed:{currentCurrencySymbol:function(){return this.$store.getters["currency/currentCurrencySymbol"]},currencyCode:function(){return{$:"USD","€":"EUR","£":"GBP","zł":"PLN",A$:"AUD",C$:"CAD"}[this.currentCurrencySymbol]||"USD"},value_:function(){return this.value||null},minimumPrice:function(){var t;return(null===(t={EUR:{30:7,60:11,90:16,120:21},GBP:{30:6,60:10,90:15,120:20},PLN:{30:30,60:50,90:70,120:85},USD:{30:8,60:12,90:17,120:22},AUD:{30:12,60:20,90:28,120:36},CAD:{30:11,60:18,90:25,120:32}}[this.currencyCode])||void 0===t?void 0:t[this.length])||10},minimumValidation:function(t){return 60===Number(length)||Number(t)>0?this.minimumPrice:0}},mounted:function(){var t;this.validation(null!==(t=this.value)&&void 0!==t?t:0,this.freeTrial)},methods:{updateValue:function(t){var e,n=this;l.test(t)||"Backspace"===this.keyCode||"Delete"===this.keyCode?e=t:(e=this.value,this.key++,this.$nextTick((function(){n.$refs.priceInput.focus()}))),this.keyCode=null,this.validation(e),this.$emit("input",e)},validation:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.minimumPrice;this.$emit("validation",!(!e&&(60===Number(length)&&Number(t)<n||Number(t)>0&&Number(t)<n)))},validatePrice:function(t){var e=this.minimumPrice;return!!this.$props.freeTrial||(60===Number(length)&&Number(t)<e?"Error: Minimum price is ".concat(e):!(Number(t)>0&&Number(t)<e)||"Error: Minimum price is ".concat(e))}}},c=n(22),component=Object(c.a)(o,(function(){var t=this,e=t.$createElement;return(t._self._c||e)("text-input",{key:t.key,ref:"priceInput",attrs:{value:t.value_,"type-class":"border-gradient",height:"32","hide-details":"",placeholder:"0.00",rules:t.rules.concat([t.validatePrice]),prefix:t.currentCurrencySymbol},on:{keydown:function(e){t.keyCode=e.code},input:function(e){return t.updateValue(e)}}})}),[],!1,null,null,null);e.default=component.exports},1639:function(t,e,n){var content=n(1744);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("36e4c324",content,!0,{sourceMap:!1})},1640:function(t,e,n){var content=n(1746);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("3ab5135c",content,!0,{sourceMap:!1})},1696:function(t,e,n){"use strict";n.r(e);n(31),n(71);var r={name:"PerLessonPrice",components:{LessonPrice:n(1523).default},props:{items:{type:Array,required:!0},length:{type:Number,required:!0},lessons:{type:Number,required:!0},rules:{type:Array,default:function(){return[]}}},data:function(){return{key:this.length+this.lessons,keyCode:null}},computed:{value:function(){var t,e=this;return null===(t=this.items.find((function(t){return t.length===e.length&&t.lessons===e.lessons})))||void 0===t?void 0:t.price}},methods:{updateValue:function(t){this.$store.commit("settings/UPDATE_LESSON_PRICE",{value:t,length:this.length,lessons:this.lessons})}}},l=n(22),component=Object(l.a)(r,(function(){var t=this,e=t.$createElement;return(t._self._c||e)("lesson-price",{attrs:{value:t.value,rules:t.rules,length:t.length},on:{input:t.updateValue}})}),[],!1,null,null,null);e.default=component.exports},1743:function(t,e,n){"use strict";n(1639)},1744:function(t,e,n){var r=n(18)(!1);r.push([t.i,'.current-currency[data-v-b41b2052]{font-size:16px}.currency-input[data-v-b41b2052]{width:84px}.lesson-pricing[data-v-b41b2052]{display:inline-block}@media only screen and (max-width:479px){.lesson-pricing[data-v-b41b2052]{width:calc(100% + 20px);margin-left:-10px}}.lesson-pricing-row[data-v-b41b2052]{position:relative;display:flex;align-items:center}.lesson-pricing-row[data-v-b41b2052]:before{content:"";position:absolute;bottom:0;right:0;width:calc(100% - 67px);height:1px;background-color:rgba(45,45,45,.06)}@media only screen and (max-width:991px){.lesson-pricing-row[data-v-b41b2052]:before{width:calc(100% - 35px)}}@media only screen and (max-width:479px){.lesson-pricing-row[data-v-b41b2052]:before{width:100%;left:0}}.lesson-pricing-row[data-v-b41b2052]:first-child{font-size:14px;font-weight:500}.lesson-pricing-row[data-v-b41b2052]:first-child:before,.lesson-pricing-row[data-v-b41b2052]:last-child:before{display:none}.lesson-pricing .item[data-v-b41b2052]{text-align:center}.lesson-pricing .item[data-v-b41b2052]:first-child{width:35px;font-size:14px;font-weight:500}.lesson-pricing .item[data-v-b41b2052]:not(:first-child){width:84px}.lesson-pricing .item_text[data-v-b41b2052]{display:flex;justify-content:flex-start;align-items:center;padding-left:20px;font-size:14px;min-width:196px;font-weight:400}@media(max-width:768px){.lesson-pricing .item_text[data-v-b41b2052]{justify-content:center;padding-left:0;padding-top:8px}}',""]),t.exports=r},1745:function(t,e,n){"use strict";n(1640)},1746:function(t,e,n){var r=n(18)(!1);r.push([t.i,".price-input .v-input__slot{padding:0 10px 0 8px!important}.price-input .v-text-field__prefix{letter-spacing:-.2px}",""]),t.exports=r},1928:function(t,e,n){"use strict";n.r(e);var r=n(2),l=n(13),o=(n(9),n(208)),c=n(1375),d=n(1523),m=n(1696),v={name:"PricingTableInfo",components:{UserSettingTemplate:c.default,LessonPrice:d.default,PerLessonPrice:m.default},data:function(){return{mdiInformationOutline:o.f,trialPriceRules:[function(t){return!!t}],priceRules:[function(t){return!!t}],trialLessonPricingItems:[{id:1,name:"i_offer_free_30_minute_trial_lesson_recommended",property:"freeTrialLesson"},{id:2,name:"i_offer_30_minute_trial_lesson_for",property:"trialLesson"}],newStudentsItems:[{id:1,name:"i_am_currently_accepting_new_students",value:!0},{id:2,name:"i_am_only_accepting_bookings_from_past_current_students",value:!1}],pricingMap:{EUR:{30:7,60:11,90:16,120:21},GBP:{30:6,60:10,90:15,120:20},PLN:{30:30,60:50,90:70,120:85},USD:{30:8,60:12,90:17,120:22},AUD:{30:12,60:20,90:28,120:36},CAD:{30:11,60:18,90:25,120:32}}}},computed:{item:function(){return this.$store.state.settings.pricingTableItem},lessonPrices:function(){return this.$store.state.settings.lessonPrices},selectedTrialLessonPricing:function(){var t,e,n=3;return null!==(t=this.item)&&void 0!==t&&t.freeTrialLesson?n=1:null!==(e=this.item)&&void 0!==e&&e.trialLesson&&(n=2),n},currentCurrency:function(){return this.$store.state.currency.item},currentCurrencyIsoCode:function(){var t,e;return null!==(t=this.$store.state.currency.item)&&void 0!==t&&t.isoCode?null===(e=this.$store.state.currency.item)||void 0===e?void 0:e.isoCode.toUpperCase():"USD"},getMinimumTextVisibility:function(){return this.$vuetify.breakpoint.mdAndUp}},watch:{selectedTrialLessonPricing:function(){this.$emit("validate")}},beforeCreate:function(){this.$store.dispatch("settings/getPricingTable")},methods:{updateTrial:function(t){var e=this.trialLessonPricingItems.filter((function(e){return e.id===t})),n=Object(l.a)(e,1)[0];"freeTrialLesson"===n.property&&(this.updateValue(!0,"freeTrialLesson"),this.updateValue(!1,"trialLesson")),"trialLesson"===n.property&&(this.updateValue(!1,"freeTrialLesson"),this.updateValue(!0,"trialLesson"))},updateValue:function(t,e){this.$store.commit("settings/UPDATE_PRICING_TABLE_ITEM",Object(r.a)({},e,t))},submitData:function(){this.$store.dispatch("settings/updatePricingTable")}}},_=(n(1743),n(1745),n(22)),f=n(42),h=n.n(f),y=n(1360),C=n(339),x=n(2192),w=n(2193),P=n(1361),component=Object(_.a)(v,(function(){var t=this,e=t.$createElement,r=t._self._c||e;return t.item?r("user-setting-template",{attrs:{title:t.$t("pricing_table"),"submit-func":t.submitData}},[r("div",{staticClass:"mb-3 mb-md-4"},[r("v-row",[r("v-col",{staticClass:"col-12 col-md-10"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[t._v("\n            "+t._s(t.$t("currency"))+"\n          ")]),t._v(" "),r("div",{staticClass:"input-wrap-label"},[t._v("\n            "+t._s(t.$t("once_saved_you_cannot_change_your_currency_if_you_need_to_change_your_payment_currency"))+"\n            "),r("a",{staticClass:"text--gradient",attrs:{href:"mailto:<EMAIL>"}},[t._v("<EMAIL>")]),t._v(".\n          ")]),t._v(" "),r("div",{staticClass:"current-currency d-flex align-center"},[r("svg",{staticClass:"mr-1",attrs:{width:"20",height:"20",viewBox:"0 0 20 20"}},[r("use",{attrs:{"xlink:href":n(91)+"#dollar-coin"}})]),t._v("\n            "+t._s(t.currentCurrency.isoCode)+"\n          ")])])])],1)],1),t._v(" "),r("div",{staticClass:"mb-3 mb-md-4"},[r("v-row",[r("v-col",{staticClass:"col-12 col-md-10"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[t._v("\n            "+t._s(t.$t("trial_lesson_pricing"))+"\n          ")]),t._v(" "),r("div",[r("v-radio-group",{staticClass:"mt-0 pt-0",attrs:{value:t.selectedTrialLessonPricing,"hide-details":""},on:{change:t.updateTrial}},t._l(t.trialLessonPricingItems,(function(e,n){return r("div",{key:e.id,class:{"mb-2":n<t.trialLessonPricingItems.length-1}},[r("div",{staticClass:"radiobutton d-flex align-center"},[r("v-radio",{staticClass:"d-flex align-center l-radio-button l-radio-button--type-2 mb-0",attrs:{color:"success",ripple:!1,value:e.id},scopedSlots:t._u([{key:"label",fn:function(){return[t._v("\n                      "+t._s(t.$t(e.name))+"\n                    ")]},proxy:!0}],null,!0)}),t._v(" "),2===e.id?[r("div",{staticClass:"currency-input price-input ml-2"},[r("lesson-price",{attrs:{value:t.item.priceTrialLesson,rules:2===t.selectedTrialLessonPricing?t.trialPriceRules:[],length:30,"free-trial":!0},on:{input:function(e){return t.updateValue(e,"priceTrialLesson")}}})],1)]:t._e()],2)])})),0)],1)])])],1)],1),t._v(" "),r("div",{staticClass:"mb-3 mb-md-4"},[r("v-row",[r("v-col",{staticClass:"col-12 col-md-10"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[t._v("\n            "+t._s(t.$t("new_students"))+"\n          ")]),t._v(" "),r("div",[r("v-radio-group",{staticClass:"mt-0 pt-0",attrs:{value:t.item.acceptNewStudents,"hide-details":""},on:{change:function(e){return t.updateValue(e,"acceptNewStudents")}}},t._l(t.newStudentsItems,(function(e,n){return r("div",{key:e.id,class:{"mb-2":n<t.newStudentsItems.length-1}},[r("div",{staticClass:"radiobutton"},[r("v-radio",{staticClass:"d-flex align-center l-radio-button l-radio-button--type-2",attrs:{color:"success",ripple:!1,value:e.value},scopedSlots:t._u([{key:"label",fn:function(){return[t._v("\n                      "+t._s(t.$t(e.name))+"\n                    ")]},proxy:!0}],null,!0)})],1)])})),0)],1)])])],1)],1),t._v(" "),r("div",[r("v-row",[r("v-col",{staticClass:"col-12 col-md-10"},[r("div",{staticClass:"input-wrap"},[r("div",{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[t._v("\n            "+t._s(t.$t("lesson_pricing"))+"\n            "),r("span",[t._v("("+t._s(t.$t("enter_prices_per_lesson"))+")")])]),t._v(" "),r("div",{staticClass:"input-wrap-label mb-3"},[t._v("\n            "+t._s(t.$t("all_60_pricing_options_are_required"))+"\n          ")]),t._v(" "),r("div",{staticClass:"lesson-pricing"},[r("div",{staticClass:"lesson-pricing-row"},[r("div",{staticClass:"d-flex align-center justify-center item mr-md-4"},[r("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 15 15"}},[r("use",{attrs:{"xlink:href":n(91)+"#clock"}})])]),t._v(" "),r("div",{staticClass:"item mr-1 mr-lg-2"},[t._v("\n                "+t._s(t.$tc("lessons_count",1))+"\n              ")]),t._v(" "),r("div",{staticClass:"item mr-1 mr-lg-2"},[t._v("\n                "+t._s(t.$tc("lessons_count",5))+"\n              ")]),t._v(" "),r("div",{staticClass:"item mr-1 mr-lg-2"},[t._v("\n                "+t._s(t.$tc("lessons_count",10))+"\n              ")]),t._v(" "),r("div",{staticClass:"item"},[t._v(t._s(t.$tc("lessons_count",20)))])]),t._v(" "),r("div",{staticClass:"lesson-pricing-row"},[r("div",{staticClass:"item mr-md-4"},[t._v("30’")]),t._v(" "),r("div",{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:30,lessons:1}})],1),t._v(" "),r("div",{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:30,lessons:5}})],1),t._v(" "),r("div",{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:30,lessons:10}})],1),t._v(" "),r("div",{staticClass:"item price-input py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:30,lessons:20}})],1),t._v(" "),t.getMinimumTextVisibility?r("div",{staticClass:"item_text py-2"},[r("v-icon",{attrs:{color:"greyDark"}},[t._v(t._s(t.mdiInformationOutline))]),t._v(" "),r("span",{staticClass:"pl-1"},[t._v("\n                  "+t._s(t.$t("minimum_Text"))+t._s(t.pricingMap[t.currentCurrencyIsoCode][30]+" "+t.currentCurrencyIsoCode)+"\n                ")])],1):t._e()]),t._v(" "),t.getMinimumTextVisibility?t._e():r("div",{staticClass:"item_text"},[r("v-icon",{attrs:{color:"greyDark"}},[t._v(t._s(t.mdiInformationOutline))]),t._v(" "),r("span",{staticClass:"pl-1"},[t._v("\n                "+t._s(t.$t("minimum_Text"))+t._s(t.pricingMap[t.currentCurrencyIsoCode][30]+" "+t.currentCurrencyIsoCode)+"\n              ")])],1),t._v(" "),r("div",{staticClass:"lesson-pricing-row"},[r("div",{staticClass:"item mr-md-4"},[t._v("60’")]),t._v(" "),r("div",{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:60,lessons:1,rules:t.priceRules}})],1),t._v(" "),r("div",{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:60,lessons:5,rules:t.priceRules}})],1),t._v(" "),r("div",{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:60,lessons:10,rules:t.priceRules}})],1),t._v(" "),r("div",{staticClass:"item price-input py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:60,lessons:20,rules:t.priceRules}})],1),t._v(" "),t.getMinimumTextVisibility?r("div",{staticClass:"item_text py-2"},[r("v-icon",{attrs:{color:"greyDark"}},[t._v(t._s(t.mdiInformationOutline))]),t._v(" "),r("span",{staticClass:"pl-1"},[t._v("\n                  "+t._s(t.$t("minimum_Text"))+t._s(t.pricingMap[t.currentCurrencyIsoCode][60]+" "+t.currentCurrencyIsoCode)+"\n                ")])],1):t._e()]),t._v(" "),t.getMinimumTextVisibility?t._e():r("div",{staticClass:"item_text"},[r("v-icon",{attrs:{color:"greyDark"}},[t._v(t._s(t.mdiInformationOutline))]),t._v(" "),r("span",{staticClass:"pl-1"},[t._v("\n                "+t._s(t.$t("minimum_Text"))+t._s(t.pricingMap[t.currentCurrencyIsoCode][60]+" "+t.currentCurrencyIsoCode)+"\n              ")])],1),t._v(" "),r("div",{staticClass:"lesson-pricing-row"},[r("div",{staticClass:"item mr-md-4"},[t._v("90’")]),t._v(" "),r("div",{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:90,lessons:1}})],1),t._v(" "),r("div",{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:90,lessons:5}})],1),t._v(" "),r("div",{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:90,lessons:10}})],1),t._v(" "),r("div",{staticClass:"item price-input py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:90,lessons:20}})],1),t._v(" "),t.getMinimumTextVisibility?r("div",{staticClass:"item_text py-2"},[r("v-icon",{attrs:{color:"greyDark"}},[t._v(t._s(t.mdiInformationOutline))]),t._v(" "),r("span",{staticClass:"pl-1"},[t._v("\n                  "+t._s(t.$t("minimum_Text"))+t._s(t.pricingMap[t.currentCurrencyIsoCode][90]+" "+t.currentCurrencyIsoCode)+"\n                ")])],1):t._e()]),t._v(" "),t.getMinimumTextVisibility?t._e():r("div",{staticClass:"item_text"},[r("v-icon",{attrs:{color:"greyDark"}},[t._v(t._s(t.mdiInformationOutline))]),t._v(" "),r("span",{staticClass:"pl-1"},[t._v("\n                "+t._s(t.$t("minimum_Text"))+t._s(t.pricingMap[t.currentCurrencyIsoCode][90]+" "+t.currentCurrencyIsoCode)+"\n              ")])],1),t._v(" "),r("div",{staticClass:"lesson-pricing-row"},[r("div",{staticClass:"item mr-md-4"},[t._v("120’")]),t._v(" "),r("div",{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:120,lessons:1}})],1),t._v(" "),r("div",{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:120,lessons:5}})],1),t._v(" "),r("div",{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:120,lessons:10}})],1),t._v(" "),r("div",{staticClass:"item price-input py-2"},[r("per-lesson-price",{attrs:{items:t.lessonPrices,length:120,lessons:20}})],1),t._v(" "),t.getMinimumTextVisibility?r("div",{staticClass:"item_text py-2"},[r("v-icon",{attrs:{color:"greyDark"}},[t._v(t._s(t.mdiInformationOutline))]),t._v(" "),r("span",{staticClass:"pl-1"},[t._v("\n                  "+t._s(t.$t("minimum_Text"))+t._s(t.pricingMap[t.currentCurrencyIsoCode][120]+" "+t.currentCurrencyIsoCode)+"\n                ")])],1):t._e()]),t._v(" "),t.getMinimumTextVisibility?t._e():r("div",{staticClass:"item_text"},[r("v-icon",{attrs:{color:"greyDark"}},[t._v(t._s(t.mdiInformationOutline))]),t._v(" "),r("span",{staticClass:"pl-1"},[t._v("\n                "+t._s(t.$t("minimum_Text"))+t._s(t.pricingMap[t.currentCurrencyIsoCode][120]+" "+t.currentCurrencyIsoCode)+"\n              ")])],1)])])])],1)],1)]):t._e()}),[],!1,null,"b41b2052",null);e.default=component.exports;h()(component,{UserSettingTemplate:n(1375).default}),h()(component,{VCol:y.a,VIcon:C.a,VRadio:x.a,VRadioGroup:w.a,VRow:P.a})}}]);