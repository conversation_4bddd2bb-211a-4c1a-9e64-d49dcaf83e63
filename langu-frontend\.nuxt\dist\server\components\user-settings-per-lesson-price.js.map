{"version": 3, "file": "components/user-settings-per-lesson-price.js", "sources": ["webpack:///./components/user-settings/LessonPrice.vue?3de7", "webpack:///./components/user-settings/LessonPrice.vue", "webpack:///./components/user-settings/LessonPrice.vue?3c69", "webpack:///./components/user-settings/LessonPrice.vue?3568", "webpack:///./components/user-settings/PerLessonPrice.vue?2820", "webpack:///./components/user-settings/PerLessonPrice.vue", "webpack:///./components/user-settings/PerLessonPrice.vue?3682", "webpack:///./components/user-settings/PerLessonPrice.vue?98c4"], "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('text-input',{key:_vm.key,ref:\"priceInput\",attrs:{\"value\":_vm.value_,\"type-class\":\"border-gradient\",\"height\":\"32\",\"hide-details\":\"\",\"placeholder\":\"0.00\",\"rules\":_vm.rules.concat( [_vm.validatePrice]),\"prefix\":_vm.currentCurrencySymbol},on:{\"keydown\":function($event){_vm.keyCode = $event.code},\"input\":function($event){return _vm.updateValue($event)}}})}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport TextInput from '@/components/form/TextInput'\n\nconst regex = /^\\d+\\.?\\d{0,2}$/\n\nexport default {\n  name: 'LessonPrice',\n  components: { TextInput },\n  props: {\n    value: {\n      type: [String, Number],\n      required: true,\n    },\n    rules: {\n      type: Array,\n      default: () => [],\n    },\n    length: {\n      type: Number,\n      required: true,\n      default: 30,\n    },\n    freeTrial: {\n      type: Boolean,\n      required: false,\n      default: false,\n    },\n  },\n  data() {\n    return {\n      key: 1,\n      keyCode: null,\n    }\n  },\n  computed: {\n    currentCurrencySymbol() {\n      return this.$store.getters['currency/currentCurrencySymbol']\n    },\n    currencyCode() {\n      const currencyMap = {\n        $: 'USD',\n        '€': 'EUR',\n        '£': 'GBP',\n        zł: 'PLN',\n        A$: 'AUD',\n        C$: 'CAD',\n      }\n      return currencyMap[this.currentCurrencySymbol] || 'USD'\n    },\n    value_() {\n      return this.value || null\n    },\n    minimumPrice() {\n      const pricingMap = {\n        EUR: { 30: 7, 60: 11, 90: 16, 120: 21 },\n        GBP: { 30: 6, 60: 10, 90: 15, 120: 20 },\n        PLN: { 30: 30, 60: 50, 90: 70, 120: 85 },\n        USD: { 30: 8, 60: 12, 90: 17, 120: 22 },\n        AUD: { 30: 12, 60: 20, 90: 28, 120: 36 },\n        CAD: { 30: 11, 60: 18, 90: 25, 120: 32 },\n      }\n\n      return pricingMap[this.currencyCode]?.[this.length] || 10\n    },\n    minimumValidation(value) {\n      if (Number(length) === 60 || Number(value) > 0) {\n        return this.minimumPrice\n      } else {\n        return 0\n      }\n    },\n  },\n  mounted() {\n    this.validation(this.value ?? 0, this.freeTrial)\n  },\n  methods: {\n    updateValue(event) {\n      let value\n\n      if (\n        regex.test(event) ||\n        this.keyCode === 'Backspace' ||\n        this.keyCode === 'Delete'\n      ) {\n        value = event\n      } else {\n        value = this.value\n\n        this.key++\n        this.$nextTick(() => {\n          this.$refs.priceInput.focus()\n        })\n      }\n\n      this.keyCode = null\n\n      this.validation(value)\n      this.$emit('input', value)\n    },\n    validation(value, isFreeTrial = false) {\n      const minPrice = this.minimumPrice\n      this.$emit(\n        'validation',\n        (function () {\n          if (isFreeTrial) {\n            return true\n          } else if (Number(length) === 60 && Number(value) < minPrice) {\n            return false\n          } else if (Number(value) > 0 && Number(value) < minPrice) {\n            return false\n          }\n          return true\n        })()\n      )\n    },\n    validatePrice(value) {\n      const minPrice = this.minimumPrice\n      if (this.$props.freeTrial) {\n        return true\n      } else if (Number(length) === 60 && Number(value) < minPrice) {\n        return `Error: Minimum price is ${minPrice}`\n      } else if (Number(value) > 0 && Number(value) < minPrice) {\n        return `Error: Minimum price is ${minPrice}`\n      }\n      return true\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonPrice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./LessonPrice.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./LessonPrice.vue?vue&type=template&id=1ecf865e&\"\nimport script from \"./LessonPrice.vue?vue&type=script&lang=js&\"\nexport * from \"./LessonPrice.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"fa29dedc\"\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('lesson-price',{attrs:{\"value\":_vm.value,\"rules\":_vm.rules,\"length\":_vm.length},on:{\"input\":_vm.updateValue}})}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport LessonPrice from '@/components/user-settings/LessonPrice'\n\nexport default {\n  name: 'PerLessonPrice',\n  components: { LessonPrice },\n  props: {\n    items: {\n      type: Array,\n      required: true,\n    },\n    length: {\n      type: Number,\n      required: true,\n    },\n    lessons: {\n      type: Number,\n      required: true,\n    },\n    rules: {\n      type: Array,\n      default: () => [],\n    },\n  },\n  data() {\n    return {\n      key: this.length + this.lessons,\n      keyCode: null,\n    }\n  },\n  computed: {\n    value() {\n      return this.items.find(\n        (item) => item.length === this.length && item.lessons === this.lessons\n      )?.price\n    },\n  },\n  methods: {\n    updateValue(value) {\n      this.$store.commit('settings/UPDATE_LESSON_PRICE', {\n        value,\n        length: this.length,\n        lessons: this.lessons,\n      })\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PerLessonPrice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./PerLessonPrice.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./PerLessonPrice.vue?vue&type=template&id=b047eada&\"\nimport script from \"./PerLessonPrice.vue?vue&type=script&lang=js&\"\nexport * from \"./PerLessonPrice.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"50089bfa\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AAHA;AAKA;AACA;AACA;AACA;AAHA;AAdA;AACA;AAmBA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA;AAQA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;AASA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArCA;AACA;AAqCA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAnDA;AAtEA;;ACrBA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AClBA;AACA;AACA;;;;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AACA;AAiBA;AACA;AACA;AACA;AAFA;AAIA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAGA;AACA;AANA;AAOA;AACA;AACA;AACA;AACA;AACA;AAHA;AAKA;AACA;AARA;AAlCA;;ACZA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}