exports.ids = [31];
exports.modules = {

/***/ 1240:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1318);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("ab02e688", content, true, context)
};

/***/ }),

/***/ 1317:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Viewport_vue_vue_type_style_index_0_id_4a5272da_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1240);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Viewport_vue_vue_type_style_index_0_id_4a5272da_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Viewport_vue_vue_type_style_index_0_id_4a5272da_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Viewport_vue_vue_type_style_index_0_id_4a5272da_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Viewport_vue_vue_type_style_index_0_id_4a5272da_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1318:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".viewport-component[data-v-4a5272da]{position:absolute;border-width:4px;border-style:solid}.user-name[data-v-4a5272da]{position:absolute;top:0;right:0;padding:4px 13px;line-height:1;background:#fff;font-size:12px;font-weight:500;box-shadow:0 0 5px rgba(0,0,0,.2);border:none;border-bottom-left-radius:6px;z-index:2}.user-name--tl[data-v-4a5272da]{top:0;right:auto;left:0;border-bottom-left-radius:0;border-bottom-right-radius:6px}.user-name--br[data-v-4a5272da]{right:0;border-top-left-radius:6px}.user-name--bl[data-v-4a5272da],.user-name--br[data-v-4a5272da]{top:auto;bottom:0;border-bottom-left-radius:0}.user-name--bl[data-v-4a5272da]{right:auto;left:0;border-top-right-radius:6px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1400:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/Viewport.vue?vue&type=template&id=4a5272da&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.isOtherScreenAllowed)?_c('div',{staticClass:"viewport-component",style:(_vm.styles)},[_vm._ssrNode("<div"+(_vm._ssrClass(null,[
      'user-name',
      {
        'user-name--br': _vm.isTopOffset && !_vm.isRightOffset,
        'user-name--tl': _vm.isRightOffset && !_vm.isTopOffset,
        'user-name--bl': _vm.isTopOffset && _vm.isRightOffset,
      } ]))+" data-v-4a5272da>"+_vm._ssrEscape("\n    "+_vm._s(_vm.$t('classroom_user_screen', {
        username: _vm.username,
      }))+"\n  ")+"</div>")]):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/classroom/Viewport.vue?vue&type=template&id=4a5272da&scoped=true&

// EXTERNAL MODULE: ./helpers/check_device.js
var check_device = __webpack_require__(145);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/classroom/Viewport.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var Viewportvue_type_script_lang_js_ = ({
  name: 'Viewport',
  props: {
    zoomAsset: {
      type: Object,
      required: true
    },
    zoomOtherAsset: {
      type: Object,
      required: true
    },
    viewportWidth: {
      type: Number,
      required: true
    },
    viewportHeight: {
      type: Number,
      required: true
    }
  },

  data() {
    return {
      isDevice: Object(check_device["a" /* isDevice */])()
    };
  },

  computed: {
    role() {
      return this.$store.getters['classroom/role'];
    },

    color() {
      return this.role === 'teacher' ? 'rgba(60, 135, 248, 0.4)' : 'rgba(127, 184, 2, 0.4)';
    },

    isOtherUserJoinedClassroom() {
      return this.$store.getters['classroom/isOtherUserJoinedClassroom'];
    },

    otherScreenTop() {
      return (this.zoomOtherAsset.asset.y - this.zoomAsset.asset.y) * this.zoomAsset.asset.zoomIndex;
    },

    otherScreenLeft() {
      return (this.zoomOtherAsset.asset.x - this.zoomAsset.asset.x) * this.zoomAsset.asset.zoomIndex;
    },

    otherScreenWidth() {
      return this.zoomOtherAsset.asset.screen.width / this.zoomOtherAsset.asset.zoomIndex * this.zoomAsset.asset.zoomIndex;
    },

    otherScreenHeight() {
      return this.zoomOtherAsset.asset.screen.height / this.zoomOtherAsset.asset.zoomIndex * this.zoomAsset.asset.zoomIndex;
    },

    isOtherScreenAllowed() {
      var _this$zoomOtherAsset, _this$zoomOtherAsset$;

      return !this.isDevice && !!((_this$zoomOtherAsset = this.zoomOtherAsset) !== null && _this$zoomOtherAsset !== void 0 && (_this$zoomOtherAsset$ = _this$zoomOtherAsset.asset) !== null && _this$zoomOtherAsset$ !== void 0 && _this$zoomOtherAsset$.screen) && this.isOtherUserJoinedClassroom && this.zoomAsset.asset.screen.width > this.zoomOtherAsset.asset.screen.width;
    },

    styles() {
      return {
        top: `${this.otherScreenTop}px`,
        left: `${this.otherScreenLeft}px`,
        width: `${this.otherScreenWidth}px`,
        height: `${this.otherScreenHeight}px`,
        borderColor: this.color
      };
    },

    username() {
      return this.zoomOtherAsset.asset.username;
    },

    isTopOffset() {
      return this.otherScreenTop < 0;
    },

    isLeftOffset() {
      return this.otherScreenLeft < 0;
    },

    isBottomOffset() {
      return this.viewportHeight + this.zoomAsset.asset.y < this.otherScreenHeight + this.zoomOtherAsset.asset.y;
    },

    isRightOffset() {
      return this.viewportWidth + this.zoomAsset.asset.x < this.otherScreenWidth + this.zoomOtherAsset.asset.x;
    }

  }
});
// CONCATENATED MODULE: ./components/classroom/Viewport.vue?vue&type=script&lang=js&
 /* harmony default export */ var classroom_Viewportvue_type_script_lang_js_ = (Viewportvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/classroom/Viewport.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1317)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  classroom_Viewportvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "4a5272da",
  "332e3cb2"
  
)

/* harmony default export */ var Viewport = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=classroom-viewport.js.map