exports.ids = [134,39,40,41,42,43,44,45,46,47,48,53,58];
exports.modules = {

/***/ 1000:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/LExpansionPanels.vue?vue&type=template&id=26541f2f&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-expansion-panels',{attrs:{"accordion":"","multiple":"","flat":_vm.flat},model:{value:(_vm.value),callback:function ($$v) {_vm.value=$$v},expression:"value"}},_vm._l((_vm.items),function(i){return _c('v-expansion-panel',{key:i.id,class:_vm.flat ? 'mb-2 mb-sm-3' : ''},[_c('v-expansion-panel-header',{attrs:{"id":i.selectorId || null,"disable-icon-rotate":"","hide-actions":""},scopedSlots:_vm._u([{key:"default",fn:function(ref){
var open = ref.open;
return [_c('div',{class:[
          'font-weight-medium',
          open ? 'orange--text' : 'darkLight--text' ]},[_vm._v("\n        "+_vm._s(i.title)+"\n      ")]),_vm._v(" "),_c('div',{staticClass:"v-expansion-panel-header__icon v-expansion-panel-header__icon--disable-rotate"},[_c('v-icon',{staticClass:"ml-auto",attrs:{"color":open ? 'orange' : 'greyLight'}},[_vm._v("\n          "+_vm._s(open ? _vm.mdiMinus : _vm.mdiPlus)+"\n        ")])],1),_vm._v(" "),(_vm.link)?_c('a',{staticClass:"d-block",attrs:{"href":("/faq#faq" + (i.id))},on:{"click":function($event){return _vm.changeURL($event, ("faq" + (i.id)), open)}}}):_vm._e()]}}],null,true)}),_vm._v(" "),_c('v-expansion-panel-content',[_c('div',{domProps:{"innerHTML":_vm._s(i.description)}})])],1)}),1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/LExpansionPanels.vue?vue&type=template&id=26541f2f&

// EXTERNAL MODULE: external "@mdi/js"
var js_ = __webpack_require__(48);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/LExpansionPanels.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var LExpansionPanelsvue_type_script_lang_js_ = ({
  name: 'LExpansionPanels',
  props: {
    items: {
      type: Array,
      required: true
    },
    panels: {
      type: Array,
      default: () => []
    },
    flat: {
      type: Boolean,
      default: false
    },
    link: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      mdiPlus: js_["mdiPlus"],
      mdiMinus: js_["mdiMinus"],
      value: undefined
    };
  },

  mounted() {
    this.value = [...this.panels];
  },

  methods: {
    changeURL(e, hash, isOpened) {
      e.preventDefault();
      const currentHash = window.location.hash;

      if (!isOpened) {
        window.location.hash = hash;
        return;
      }

      if (currentHash === '#' + hash) {
        history.replaceState({}, document.title, '/faq');
      }
    }

  }
});
// CONCATENATED MODULE: ./components/LExpansionPanels.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_LExpansionPanelsvue_type_script_lang_js_ = (LExpansionPanelsvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanel.js
var VExpansionPanel = __webpack_require__(1072);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelContent.js
var VExpansionPanelContent = __webpack_require__(1073);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelHeader.js
var VExpansionPanelHeader = __webpack_require__(1074);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanels.js
var VExpansionPanels = __webpack_require__(1092);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(87);

// CONCATENATED MODULE: ./components/LExpansionPanels.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1048)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_LExpansionPanelsvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "a25d7acc"
  
)

/* harmony default export */ var LExpansionPanels = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */






installComponents_default()(component, {VExpansionPanel: VExpansionPanel["a" /* default */],VExpansionPanelContent: VExpansionPanelContent["a" /* default */],VExpansionPanelHeader: VExpansionPanelHeader["a" /* default */],VExpansionPanels: VExpansionPanels["a" /* default */],VIcon: VIcon["a" /* default */]})


/***/ }),

/***/ 1048:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LExpansionPanels_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(993);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LExpansionPanels_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LExpansionPanels_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LExpansionPanels_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LExpansionPanels_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1049:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-expansion-panels .v-expansion-panel{font-size:16px}.v-expansion-panels .v-expansion-panel-header{opacity:.7}@media only screen and (max-width:991px){.v-expansion-panels .v-expansion-panel-header{min-height:58px!important;padding:16px!important;font-size:16px!important}}.v-expansion-panels .v-expansion-panel-header a{position:absolute;top:0;left:0;width:100%;height:100%}.v-expansion-panels .v-expansion-panel-content{line-height:1.5}@media only screen and (max-width:991px){.v-expansion-panels .v-expansion-panel-content{font-size:14px;line-height:1.4}}@media only screen and (max-width:991px){.v-expansion-panels .v-expansion-panel-content__wrap{padding:0 16px 16px}}.v-expansion-panels .v-expansion-panel:before{box-shadow:0 4px 10px rgba(71,68,68,.1)!important}.v-expansion-panels--flat .v-expansion-panel{border-radius:8px!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1071:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1105);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("20c2c1c7", content, true)

/***/ }),

/***/ 1072:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _mixins_groupable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(47);
/* harmony import */ var _mixins_registrable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(0);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2);
// Mixins

 // Utilities



/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(Object(_mixins_groupable__WEBPACK_IMPORTED_MODULE_0__[/* factory */ "a"])('expansionPanels', 'v-expansion-panel', 'v-expansion-panels'), Object(_mixins_registrable__WEBPACK_IMPORTED_MODULE_1__[/* provide */ "b"])('expansionPanel', true)
/* @vue/component */
).extend({
  name: 'v-expansion-panel',
  props: {
    disabled: Boolean,
    readonly: Boolean
  },

  data() {
    return {
      content: null,
      header: null,
      nextIsActive: false
    };
  },

  computed: {
    classes() {
      return {
        'v-expansion-panel--active': this.isActive,
        'v-expansion-panel--next-active': this.nextIsActive,
        'v-expansion-panel--disabled': this.isDisabled,
        ...this.groupClasses
      };
    },

    isDisabled() {
      return this.expansionPanels.disabled || this.disabled;
    },

    isReadonly() {
      return this.expansionPanels.readonly || this.readonly;
    }

  },
  methods: {
    registerContent(vm) {
      this.content = vm;
    },

    unregisterContent() {
      this.content = null;
    },

    registerHeader(vm) {
      this.header = vm;
      vm.$on('click', this.onClick);
    },

    unregisterHeader() {
      this.header = null;
    },

    onClick(e) {
      if (e.detail) this.header.$el.blur();
      this.$emit('click', e);
      this.isReadonly || this.isDisabled || this.toggle();
    },

    toggle() {
      /* istanbul ignore else */
      if (this.content) this.content.isBooted = true;
      this.$nextTick(() => this.$emit('change'));
    }

  },

  render(h) {
    return h('div', {
      staticClass: 'v-expansion-panel',
      class: this.classes,
      attrs: {
        'aria-expanded': String(this.isActive)
      }
    }, Object(_util_helpers__WEBPACK_IMPORTED_MODULE_2__[/* getSlot */ "n"])(this));
  }

}));

/***/ }),

/***/ 1073:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _transitions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67);
/* harmony import */ var _mixins_bootable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(103);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(9);
/* harmony import */ var _mixins_registrable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(29);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(0);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(2);
 // Mixins



 // Utilities



const baseMixins = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(_mixins_bootable__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], _mixins_colorable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"], Object(_mixins_registrable__WEBPACK_IMPORTED_MODULE_3__[/* inject */ "a"])('expansionPanel', 'v-expansion-panel-content', 'v-expansion-panel'));
/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (baseMixins.extend().extend({
  name: 'v-expansion-panel-content',
  computed: {
    isActive() {
      return this.expansionPanel.isActive;
    }

  },

  created() {
    this.expansionPanel.registerContent(this);
  },

  beforeDestroy() {
    this.expansionPanel.unregisterContent();
  },

  render(h) {
    return h(_transitions__WEBPACK_IMPORTED_MODULE_0__[/* VExpandTransition */ "a"], this.showLazyContent(() => [h('div', this.setBackgroundColor(this.color, {
      staticClass: 'v-expansion-panel-content',
      directives: [{
        name: 'show',
        value: this.isActive
      }]
    }), [h('div', {
      class: 'v-expansion-panel-content__wrap'
    }, Object(_util_helpers__WEBPACK_IMPORTED_MODULE_4__[/* getSlot */ "n"])(this))])]));
  }

}));

/***/ }),

/***/ 1074:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _transitions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(66);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(9);
/* harmony import */ var _mixins_registrable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(29);
/* harmony import */ var _directives_ripple__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(22);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(0);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(2);
// Components

 // Mixins


 // Directives

 // Utilities



const baseMixins = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"])(_mixins_colorable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"], Object(_mixins_registrable__WEBPACK_IMPORTED_MODULE_3__[/* inject */ "a"])('expansionPanel', 'v-expansion-panel-header', 'v-expansion-panel'));
/* harmony default export */ __webpack_exports__["a"] = (baseMixins.extend().extend({
  name: 'v-expansion-panel-header',
  directives: {
    ripple: _directives_ripple__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"]
  },
  props: {
    disableIconRotate: Boolean,
    expandIcon: {
      type: String,
      default: '$expand'
    },
    hideActions: Boolean,
    ripple: {
      type: [Boolean, Object],
      default: false
    }
  },
  data: () => ({
    hasMousedown: false
  }),
  computed: {
    classes() {
      return {
        'v-expansion-panel-header--active': this.isActive,
        'v-expansion-panel-header--mousedown': this.hasMousedown
      };
    },

    isActive() {
      return this.expansionPanel.isActive;
    },

    isDisabled() {
      return this.expansionPanel.isDisabled;
    },

    isReadonly() {
      return this.expansionPanel.isReadonly;
    }

  },

  created() {
    this.expansionPanel.registerHeader(this);
  },

  beforeDestroy() {
    this.expansionPanel.unregisterHeader();
  },

  methods: {
    onClick(e) {
      this.$emit('click', e);
    },

    genIcon() {
      const icon = Object(_util_helpers__WEBPACK_IMPORTED_MODULE_5__[/* getSlot */ "n"])(this, 'actions') || [this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], this.expandIcon)];
      return this.$createElement(_transitions__WEBPACK_IMPORTED_MODULE_0__[/* VFadeTransition */ "d"], [this.$createElement('div', {
        staticClass: 'v-expansion-panel-header__icon',
        class: {
          'v-expansion-panel-header__icon--disable-rotate': this.disableIconRotate
        },
        directives: [{
          name: 'show',
          value: !this.isDisabled
        }]
      }, icon)]);
    }

  },

  render(h) {
    return h('button', this.setBackgroundColor(this.color, {
      staticClass: 'v-expansion-panel-header',
      class: this.classes,
      attrs: {
        tabindex: this.isDisabled ? -1 : null,
        type: 'button'
      },
      directives: [{
        name: 'ripple',
        value: this.ripple
      }],
      on: { ...this.$listeners,
        click: this.onClick,
        mousedown: () => this.hasMousedown = true,
        mouseup: () => this.hasMousedown = false
      }
    }), [Object(_util_helpers__WEBPACK_IMPORTED_MODULE_5__[/* getSlot */ "n"])(this, 'default', {
      open: this.isActive
    }, true), this.hideActions || this.genIcon()]);
  }

}));

/***/ }),

/***/ 1092:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VExpansionPanel_VExpansionPanel_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(969);
/* harmony import */ var _src_components_VExpansionPanel_VExpansionPanel_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VExpansionPanel_VExpansionPanel_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(902);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3);
// Styles
 // Components

 // Utilities


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (_VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_1__[/* BaseItemGroup */ "a"].extend({
  name: 'v-expansion-panels',

  provide() {
    return {
      expansionPanels: this
    };
  },

  props: {
    accordion: Boolean,
    disabled: Boolean,
    flat: Boolean,
    hover: Boolean,
    focusable: Boolean,
    inset: Boolean,
    popout: Boolean,
    readonly: Boolean,
    tile: Boolean
  },
  computed: {
    classes() {
      return { ..._VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_1__[/* BaseItemGroup */ "a"].options.computed.classes.call(this),
        'v-expansion-panels': true,
        'v-expansion-panels--accordion': this.accordion,
        'v-expansion-panels--flat': this.flat,
        'v-expansion-panels--hover': this.hover,
        'v-expansion-panels--focusable': this.focusable,
        'v-expansion-panels--inset': this.inset,
        'v-expansion-panels--popout': this.popout,
        'v-expansion-panels--tile': this.tile
      };
    }

  },

  created() {
    /* istanbul ignore next */
    if (this.$attrs.hasOwnProperty('expand')) {
      Object(_util_console__WEBPACK_IMPORTED_MODULE_2__[/* breaking */ "a"])('expand', 'multiple', this);
    }
    /* istanbul ignore next */


    if (Array.isArray(this.value) && this.value.length > 0 && typeof this.value[0] === 'boolean') {
      Object(_util_console__WEBPACK_IMPORTED_MODULE_2__[/* breaking */ "a"])(':value="[true, false, true]"', ':value="[0, 2]"', this);
    }
  },

  methods: {
    updateItem(item, index) {
      const value = this.getValue(item, index);
      const nextValue = this.getValue(item, index + 1);
      item.isActive = this.toggleMethod(value);
      item.nextIsActive = this.toggleMethod(nextValue);
    }

  }
}));

/***/ }),

/***/ 1105:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".slick-track[data-v-e4caeaf8]{position:relative;top:0;left:0;display:block;transform:translateZ(0)}.slick-track.slick-center[data-v-e4caeaf8]{margin-left:auto;margin-right:auto}.slick-track[data-v-e4caeaf8]:after,.slick-track[data-v-e4caeaf8]:before{display:table;content:\"\"}.slick-track[data-v-e4caeaf8]:after{clear:both}.slick-loading .slick-track[data-v-e4caeaf8]{visibility:hidden}.slick-slide[data-v-e4caeaf8]{display:none;float:left;height:100%;min-height:1px}[dir=rtl] .slick-slide[data-v-e4caeaf8]{float:right}.slick-slide img[data-v-e4caeaf8]{display:block}.slick-slide.slick-loading img[data-v-e4caeaf8]{display:none}.slick-slide.dragging img[data-v-e4caeaf8]{pointer-events:none}.slick-initialized .slick-slide[data-v-e4caeaf8]{display:block}.slick-loading .slick-slide[data-v-e4caeaf8]{visibility:hidden}.slick-vertical .slick-slide[data-v-e4caeaf8]{display:block;height:auto;border:1px solid transparent}.slick-arrow.slick-hidden[data-v-21137603]{display:none}.slick-slider[data-v-3d1a4f76]{position:relative;display:block;box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;touch-action:pan-y;-webkit-tap-highlight-color:transparent}.slick-list[data-v-3d1a4f76]{position:relative;display:block;overflow:hidden;margin:0;padding:0;transform:translateZ(0)}.slick-list[data-v-3d1a4f76]:focus{outline:none}.slick-list.dragging[data-v-3d1a4f76]{cursor:pointer;cursor:hand}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1146:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_2_0_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HomePageIntroImage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1147);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_2_0_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HomePageIntroImage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_babel_loader_lib_index_js_ref_2_0_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HomePageIntroImage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_babel_loader_lib_index_js_ref_2_0_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HomePageIntroImage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_babel_loader_lib_index_js_ref_2_0_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HomePageIntroImage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_ref_2_0_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HomePageIntroImage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 1147:
/***/ (function(module, exports) {

//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/***/ }),

/***/ 1170:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1253);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("f5c476ac", content, true, context)
};

/***/ }),

/***/ 1252:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SelectLanguage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1170);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SelectLanguage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SelectLanguage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SelectLanguage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SelectLanguage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1253:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".select-language{display:flex}@media only screen and (max-width:1439px){.select-language{justify-content:center}}@media only screen and (max-width:639px){.select-language{display:block}.select-language .v-btn{width:100%}}.select-language .v-select.v-text-field--outlined{border-radius:24px}.select-language .v-select.v-text-field--outlined .v-text-field__details,.select-language .v-select.v-text-field--outlined>.v-input__control>.v-input__slot{padding:0 12px 0 16px!important}.select-language .v-select.v-text-field--outlined.v-input--has-state fieldset,.select-language .v-select.v-text-field--outlined.v-input--is-focused fieldset,.select-language .v-select.v-text-field--outlined fieldset{border:1px solid var(--v-orange-base)!important}.select-language .v-select .v-icon{color:var(--v-orange-base)!important}.select-language .v-select .v-select__slot{display:flex;align-items:center}.select-language .v-select .v-select__selections{color:#fff!important;font-size:16px!important;font-weight:300!important}.select-language .v-select input::-moz-placeholder{color:#fff!important;font-size:16px!important;font-weight:300!important;opacity:1}.select-language .v-select input:-ms-input-placeholder{opacity:1}.select-language .v-select input::placeholder{color:#fff!important;font-size:16px!important;font-weight:300!important;opacity:1}.select-language .v-select input:-ms-input-placeholder{color:#fff!important;font-size:16px!important;font-weight:300!important}.select-language .v-select input::-ms-input-placeholder{color:#fff!important;font-size:16px!important;font-weight:300!important}.select-language-list{border:1px solid var(--v-orange-base);border-radius:24px!important;box-shadow:none!important}.select-language-list .v-select-list{padding:5px 0!important;background-color:var(--v-darkLight-base)!important}.select-language-list .theme--light.v-list-item:not(.v-list-item--active):not(.v-list-item--disabled){color:#fff!important}.select-language-list .v-list-item{min-height:42px!important;padding:0 15px!important}.select-language-list .v-list-item a{color:#fff!important;text-decoration:none}.select-language-list .v-list-item--active,.select-language-list .v-list-item:hover{color:#fff!important}.select-language-list .v-list-item--active:before,.select-language-list .v-list-item:hover:before{background-color:var(--v-orangeDark-base);border-radius:2px!important;opacity:1!important}.select-language-list .v-list-item--active .v-list-item__content:after,.select-language-list .v-list-item:hover .v-list-item__content:after{display:none}.select-language-list .v-list-item:last-child .v-list-item__content{border-bottom:none}.select-language-list .v-list-item__content{position:relative;min-height:42px;padding:0!important;border-bottom:1px solid rgba(251,176,59,.2)}.select-language-list .v-list-item__title{font-size:16px!important;font-weight:300!important;color:#fff!important}.select-language-list .v-list-item__icon{position:absolute;top:50%;right:0;width:25px;height:25px!important;margin-top:-12.5px!important;border-radius:50%;overflow:hidden}@media screen and (max-width:768px){#intro-select{margin-right:0!important;margin-bottom:20px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1254:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1341);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("6e311995", content, true, context)
};

/***/ }),

/***/ 1255:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1343);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("44e291ec", content, true, context)
};

/***/ }),

/***/ 1256:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1345);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("4eb1170e", content, true, context)
};

/***/ }),

/***/ 1257:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1347);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("7fb752ea", content, true, context)
};

/***/ }),

/***/ 1258:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1349);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("368f37aa", content, true, context)
};

/***/ }),

/***/ 1259:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1351);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("6e05f7b8", content, true, context)
};

/***/ }),

/***/ 1260:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1353);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("71b1d682", content, true, context)
};

/***/ }),

/***/ 1261:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1355);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("d03a2236", content, true, context)
};

/***/ }),

/***/ 1262:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1358);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("422de75c", content, true, context)
};

/***/ }),

/***/ 1266:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXPORTS
__webpack_require__.d(__webpack_exports__, "a", function() { return /* reexport */ render; });
__webpack_require__.d(__webpack_exports__, "b", function() { return /* reexport */ staticRenderFns; });

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/images/HomePageIntroImage.vue?vue&type=template&id=e04cb670&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('svg',{attrs:{"xmlns":"http://www.w3.org/2000/svg","width":"100%","height":"100%","viewBox":"0 0 516 535","fill":"none"}},[_vm._ssrNode("<path d=\"M-0.000534058 292.987C-0.00569153 295.388 0.482788 297.764 1.43459 299.969C2.38638 302.174 3.7812 304.159 5.53276 305.804C8.5316 308.65 12.4024 310.404 16.5215 310.785C17.2908 310.85 18.0642 310.85 18.8335 310.785L147.866 308.191C152.813 308.137 157.544 306.158 161.052 302.674C164.559 299.19 166.567 294.477 166.648 289.536C166.658 287.136 166.176 284.759 165.234 282.551C164.291 280.342 162.907 278.349 161.167 276.694C157.584 273.292 152.809 271.429 147.866 271.505H143.476C144.384 268.323 144.847 265.032 144.853 261.724C144.86 256.995 143.901 252.315 142.034 247.97C140.166 243.625 137.431 239.706 133.994 236.453C130.961 233.557 127.445 231.212 123.603 229.526C118.625 227.36 113.237 226.298 107.808 226.413L96.8454 226.646C90.0255 226.785 83.3681 228.751 77.569 232.338C71.7699 235.926 67.042 241.003 63.8794 247.039C60.7181 245.537 57.2475 244.8 53.748 244.885H50.9163C45.0435 244.945 39.4258 247.291 35.2583 251.425C31.0908 255.558 28.7024 261.152 28.6012 267.016V268.08C28.6031 270.102 28.9007 272.112 29.4845 274.047L18.8075 274.255C13.8491 274.322 9.11177 276.315 5.60046 279.812C2.08914 283.309 0.0800781 288.035 -0.000534058 292.987Z\" fill=\"white\"></path> <path d=\"M134.019 275.344L143.397 271.789C144.304 268.608 144.768 265.316 144.773 262.008C144.781 257.28 143.821 252.599 141.954 248.254C140.087 243.909 137.351 239.99 133.915 236.738C130.882 233.841 127.365 231.497 123.523 229.811C149.475 252.019 134.019 275.344 134.019 275.344Z\" fill=\"url(#paint0_linear_14397_367)\"></path> <path d=\"M86.0688 300.543C86.0251 335.106 97.275 368.737 118.106 396.318L118.444 396.785C141.27 426.869 174.047 447.86 210.922 456.008C247.796 464.155 286.366 458.93 319.743 441.265C353.121 423.599 379.131 394.644 393.13 359.571C407.129 324.497 408.204 285.59 396.164 249.797C384.124 214.004 359.752 183.657 327.401 164.175C295.051 144.693 256.828 137.345 219.56 143.444C182.292 149.542 148.405 168.691 123.952 197.468C99.4989 226.246 86.0718 262.779 86.0688 300.543Z\" fill=\"#3C87F8\"></path> <path d=\"M296.357 222.235C305.111 225.841 314.775 226.697 323.763 229.655C332.752 232.613 341.766 238.58 344.078 247.816C345.273 252.461 344.65 257.624 346.936 261.827C349.975 267.483 357.093 269.61 361.406 274.28C369.615 283.309 364.211 298.798 354.184 305.777C344.156 312.756 331.349 313.82 319.347 315.87C316.063 316.131 312.932 317.369 310.359 319.424C303.371 326.092 311.112 338.156 308.151 347.341C306.488 352.53 301.578 356.084 299.344 361.065C295.058 370.561 301.552 381.043 305.839 390.539C313.57 407.862 314.268 427.506 307.787 445.334C308.086 445.764 308.416 446.172 308.774 446.553C325.87 439.061 341.527 428.648 355.041 415.783C368.212 397.336 378.629 377.229 377.538 355.124C376.94 343.138 372.94 331.566 372.186 319.632C370.939 300.251 377.953 279.21 368.446 262.294C363.25 252.928 353.119 242.653 358.782 233.573C368.783 235.026 379.564 235.259 388.734 231.783C377.482 208.371 360.669 188.061 339.759 172.623C318.85 157.185 294.479 147.087 268.768 143.207C258.896 171.383 266.742 209.781 296.357 222.235Z\" fill=\"#8ABA2E\"></path> <path d=\"M85.614 300.562C85.6032 310.635 86.5513 320.687 88.4457 330.58C92.6813 334.02 96.6499 337.776 100.318 341.815C112.917 356.551 115.645 376.996 117.697 396.299C117.697 396.714 117.697 397.129 117.827 397.518C117.827 397.259 117.827 397.025 118.035 396.766C119.037 391.808 121.2 387.159 124.347 383.196C127.932 379.331 134.297 377.411 138.661 380.446C140.739 375.257 142.826 370.112 144.922 365.009C157.911 365.009 171.315 365.009 183.525 360.417C195.735 355.825 205.84 347.029 215.608 338.442C214.326 323.561 214.604 308.587 216.439 293.765C178.496 289.652 143.4 271.724 117.853 243.406C140.7 242.268 163.353 248.105 182.798 260.14C186.954 262.735 191.163 265.641 196.021 266.056C199.099 266.133 202.169 265.712 205.113 264.81L236.52 256.845C240.443 236.66 226.441 216.397 208.438 206.409C194.929 198.937 178.537 200.389 165.574 193.851C156.69 189.389 154.092 181.58 146.325 175.846H146.169C127.3 190.717 112.049 209.664 101.562 231.264C91.0739 252.864 85.6216 276.557 85.614 300.562Z\" fill=\"#8ABA2E\"></path> <path d=\"M195.715 268.313C196.651 271.375 197.534 274.955 195.715 277.524C194.822 278.622 193.645 279.456 192.312 279.937C190.761 280.676 189.035 280.973 187.325 280.795C185.615 280.616 183.987 279.969 182.623 278.925C181.197 277.367 180.187 275.475 179.687 273.424C177.258 265.98 175.519 258.329 174.491 250.567C174.296 249.854 174.296 249.101 174.491 248.388C174.897 247.639 175.549 247.053 176.336 246.727C180.466 244.522 186.935 242.758 189.325 247.635C192.26 253.914 193.637 261.671 195.715 268.313Z\" fill=\"#F7AD7E\"></path> <path d=\"M183.221 271.868C182.349 271.748 181.461 271.828 180.623 272.101C180.053 272.417 179.557 272.85 179.169 273.373L170.206 283.413C168.466 285.359 166.647 288.032 167.868 290.341C168.298 290.983 168.867 291.522 169.532 291.917C170.197 292.312 170.942 292.553 171.713 292.624C176.054 293.02 180.418 292.165 184.286 290.159C187.04 288.966 189.768 287.565 192.443 286.267C194.432 285.366 196.17 283.994 197.509 282.272C199.561 279.158 200.107 271.686 196.522 269.299C194.678 268.132 193.067 268.962 191.326 269.766C188.838 271.12 186.055 271.842 183.221 271.868Z\" fill=\"#FBB03B\"></path> <path d=\"M146.041 302.51C145.971 303.087 146.033 303.673 146.223 304.223C146.533 304.756 146.982 305.196 147.522 305.494C149.226 306.666 151.223 307.34 153.289 307.44C154.321 307.478 155.348 307.279 156.29 306.855C157.232 306.432 158.063 305.798 158.718 305.001C159.675 303.491 160.254 301.774 160.407 299.994C161.168 296.166 161.543 292.272 161.524 288.37C161.569 288.141 161.564 287.905 161.509 287.678C161.454 287.451 161.349 287.238 161.203 287.056C161.058 286.873 160.874 286.724 160.664 286.62C160.455 286.515 160.225 286.457 159.991 286.45C156.77 285.776 150.354 284.09 148.717 288.033C147.05 292.683 146.146 297.572 146.041 302.51Z\" fill=\"#F7AD7E\"></path> <path d=\"M132.405 302.118C129.637 302.267 126.901 302.791 124.274 303.675C122.968 304.126 121.769 304.84 120.75 305.773C119.731 306.705 118.915 307.837 118.351 309.097C117.826 310.331 117.762 311.712 118.169 312.989C119.066 314.884 120.642 316.374 122.586 317.166C131.818 322.066 142.234 324.303 152.668 323.626C155.345 323.734 157.961 322.804 159.968 321.032C161.417 318.889 162.095 316.319 161.89 313.741L162.254 301.547C159.903 301.652 157.609 302.3 155.552 303.441C151.707 304.738 150.2 301.625 147.031 300.847C142.9 299.887 136.614 301.703 132.405 302.118Z\" fill=\"#FBB03B\"></path> <path d=\"M183.221 271.868C182.349 271.748 181.461 271.828 180.623 272.101C180.053 272.417 179.557 272.85 179.169 273.373L170.206 283.413C168.466 285.359 166.647 288.032 167.868 290.341C168.298 290.983 168.867 291.522 169.532 291.917C170.197 292.312 170.942 292.553 171.713 292.624C176.054 293.02 180.418 292.165 184.286 290.159C187.04 288.966 189.768 287.565 192.443 286.267C194.432 285.366 196.17 283.994 197.509 282.272C199.561 279.158 200.107 271.686 196.522 269.299C194.678 268.132 193.067 268.962 191.326 269.766C188.838 271.12 186.055 271.842 183.221 271.868Z\" fill=\"#2D2D2D\"></path> <path d=\"M132.405 302.118C129.637 302.267 126.901 302.791 124.274 303.675C122.968 304.126 121.769 304.84 120.75 305.773C119.731 306.705 118.915 307.837 118.351 309.097C117.826 310.331 117.762 311.712 118.169 312.989C119.066 314.884 120.642 316.374 122.586 317.166C131.818 322.066 142.234 324.303 152.668 323.626C155.345 323.734 157.961 322.804 159.968 321.032C161.417 318.889 162.095 316.319 161.89 313.741L162.254 301.547C159.903 301.652 157.609 302.3 155.552 303.441C151.707 304.738 150.2 301.625 147.031 300.847C142.9 299.887 136.614 301.703 132.405 302.118Z\" fill=\"#2D2D2D\"></path> <path d=\"M280.403 170.009C285.992 169.525 291.615 169.612 297.185 170.269C297.914 170.306 298.62 170.529 299.237 170.917C299.592 171.251 299.876 171.653 300.073 172.099C300.269 172.544 300.374 173.025 300.381 173.512C301.16 181.036 295.185 187.574 288.742 191.544C271.311 202.337 249.334 201.921 230.084 208.978C228.248 209.464 226.617 210.529 225.434 212.014C224.631 213.623 224.239 215.406 224.291 217.203C223.434 227.347 217.797 236.48 215.926 246.469C214.056 256.457 216.056 266.913 214.523 276.98C208.211 270.26 197.456 269.196 191.143 262.554C185.22 256.431 184.051 247.299 183.168 238.841L180.778 215.646C180.335 213.974 180.518 212.197 181.293 210.65C182.067 209.102 183.381 207.89 184.986 207.24C213.406 188.897 246.71 172.915 280.403 170.009Z\" fill=\"#358FEF\"></path> <path d=\"M165.475 167.673C162.849 168.199 160.416 169.427 158.435 171.227C156.404 174.052 155.479 177.523 155.837 180.982C155.899 205.6 160.559 229.991 169.58 252.901C169.664 253.707 169.946 254.479 170.398 255.151C170.851 255.822 171.462 256.373 172.177 256.754C172.892 257.136 173.69 257.336 174.501 257.339C175.311 257.341 176.11 257.145 176.828 256.767C182.23 255.311 187.553 253.579 192.778 251.578L185.712 204.878C185.375 203.582 185.447 202.213 185.92 200.96C186.442 200.071 187.173 199.323 188.05 198.781C196.109 192.926 204.622 187.72 213.508 183.214C220.6 179.633 228.732 175.43 230.654 167.854C231.053 166.56 231.134 165.189 230.889 163.856C230.645 162.524 230.083 161.27 229.251 160.201C228.128 159.098 226.805 158.218 225.354 157.606C220.652 155.297 214.729 152.417 209.326 152.884C204.988 153.325 203.325 155.816 199.714 157.658C189.687 162.64 176.36 164.508 165.475 167.673Z\" fill=\"#2D2D2D\"></path> <path d=\"M213.743 93.3954C210.262 102.969 208.547 113.113 205.482 122.791C204.811 125.356 203.607 127.751 201.949 129.822C199.745 131.933 197.076 133.497 194.156 134.388C190.086 135.962 185.947 137.337 181.738 138.513C178.932 139.266 175.581 140.563 175.27 143.417C175.203 144.369 175.401 145.32 175.841 146.167C176.927 148.1 178.49 149.723 180.382 150.881C182.273 152.039 184.431 152.694 186.648 152.783C191.073 152.901 195.481 152.18 199.637 150.656C203.421 149.891 206.893 148.022 209.613 145.285C211.159 143.009 212.244 140.453 212.808 137.761C217.071 122.647 222.986 108.047 230.447 94.2256C232.679 90.5755 234.425 86.6508 235.642 82.5505C237.357 75.3897 237.383 58.4218 229.979 64.6486C224.004 69.6819 216.367 86.2865 213.743 93.3954Z\" fill=\"#F7AD7E\"></path> <path d=\"M242.088 166.768C237.708 165.073 233.447 163.088 229.332 160.826C213.746 152.213 226.501 147.984 231.696 137.632C235.411 130.289 233.905 121.494 231.982 113.529C228.579 99.519 224.033 84.9899 227.306 71.0056C227.715 68.7361 228.601 66.5786 229.904 64.6751C230.857 63.4942 231.957 62.4393 233.177 61.5358C236.468 58.697 240.255 56.4893 244.348 55.0236C252.141 52.4292 260.974 54.3231 268.377 58.0332C297.473 72.6142 303.682 109.43 302.512 138.877C302.201 147.05 300.928 155.689 295.654 161.942C290.381 168.195 282.925 170.582 275.288 171.516C263.999 172.668 252.599 171.038 242.088 166.768Z\" fill=\"white\"></path> <path d=\"M285.466 150.109C285.231 152.873 284.341 155.541 282.868 157.892C280.27 161.551 275.49 162.9 271.1 163.963L251.331 168.841C248.921 169.571 246.419 169.946 243.901 169.957C238.498 169.697 233.847 166.247 229.561 162.926C228.466 162.177 227.515 161.237 226.755 160.15C237.666 159.112 248.551 158.022 259.41 156.439C265.8 155.505 272.555 154.208 277.257 149.824C279.686 147.427 281.534 144.507 282.66 141.288C283.439 139.29 283.803 134.205 286.063 133.764C286.632 139.213 286.431 144.715 285.466 150.109Z\" fill=\"url(#paint1_linear_14397_367)\"></path> <path d=\"M246.45 77.8286C246.452 78.6467 246.779 79.4305 247.359 80.008C247.899 80.3256 248.531 80.4535 249.152 80.3712C251.188 80.2759 253.179 79.746 254.993 78.8171C256.806 77.8883 258.399 76.582 259.664 74.9864C260.93 73.3908 261.838 71.543 262.328 69.5673C262.818 67.5916 262.878 65.534 262.505 63.533C262.037 60.9386 260.92 58.7073 260.219 56.2685C258.842 51.3909 259.075 45.8906 255.984 41.8691C253.308 47.9851 250.254 53.9292 246.84 59.6673C245.967 60.8083 245.361 62.1293 245.064 63.5342C244.767 64.939 244.788 66.3922 245.125 67.788L246.45 77.8286Z\" fill=\"#F7AD7E\"></path> <path d=\"M244.916 65.2698L246.085 73.0533C246.085 73.0533 259.463 66.4373 257.723 54.1655C255.982 41.8936 244.916 65.2698 244.916 65.2698Z\" fill=\"#D89469\"></path> <path d=\"M229.277 57.6923C229.963 60.1858 231.418 62.4005 233.434 64.0229C234.396 64.6739 235.47 65.1402 236.603 65.3979C240.581 66.1688 244.701 65.6628 248.373 63.9525C252.046 62.2421 255.082 59.4151 257.048 55.8762C258.9 52.2914 259.957 48.3497 260.145 44.32C260.334 40.2903 259.649 36.2674 258.139 32.5259C256.903 28.4224 254.105 24.9666 250.345 22.9004C248.29 22.091 246.067 21.8024 243.873 22.0605C241.679 22.3185 239.583 23.1151 237.772 24.3792C232.851 27.8106 229.048 32.6102 226.835 38.1819C224.939 43.8638 227.329 52.2699 229.277 57.6923Z\" fill=\"#F7AD7E\"></path> <path d=\"M170.542 200.288C167.212 204.522 164.581 209.261 162.748 214.324C152.532 238.93 145.892 264.87 143.031 291.354C151.372 292.392 159.81 292.392 168.152 291.354C168.805 272.657 171.692 254.105 176.75 236.092C177.644 232.215 179.181 228.514 181.297 225.143C185.011 219.772 191.116 216.659 196.883 213.753C210.378 207.204 224.482 201.99 238.994 198.186C245.125 196.5 251.307 194.943 257.334 192.997C262.4 191.285 276.61 185.006 280.714 181.608C282.865 179.979 284.665 177.933 286.004 175.592C287.343 173.251 288.195 170.664 288.508 167.987C288.508 169.336 248.917 172.034 246.086 171.723C236.552 170.711 225.303 165.262 215.899 169.128C208.548 172.138 201.819 178.416 195.117 182.749C186.986 187.834 177.062 192.764 170.542 200.288Z\" fill=\"#473F47\"></path> <path d=\"M213.927 198.288L262.246 174.704L205.588 150.705L157.243 171.798L213.927 198.288Z\" fill=\"#D69B40\"></path> <path d=\"M287.524 87.5559C287.524 88.464 287.757 89.3461 287.861 90.1504C289.29 103.434 287.991 116.458 287.498 129.664C287.186 137.837 286.121 153.404 277.418 157.581C261.156 165.364 229.775 162.407 229.775 162.407C226.136 161.824 222.417 161.974 218.838 162.848C216.889 163.444 215.097 164.456 213.201 165.183C206.342 167.777 198.679 166.48 191.457 165.183C190.213 165.035 189.011 164.638 187.924 164.015C186.664 162.897 185.895 161.329 185.783 159.649C185.671 157.969 186.225 156.313 187.326 155.038C189.671 152.578 192.72 150.902 196.055 150.239C205.175 147.265 214.944 146.888 224.267 149.149C225.459 149.55 226.673 149.879 227.904 150.135C229.347 150.368 230.807 150.473 232.268 150.446H243.335C256.064 150.446 268.066 150.602 269.988 135.735C270.698 130.229 270.837 124.665 270.404 119.131C269.651 109.142 267.027 99.2311 267.572 89.2423C267.605 86.0744 268.398 82.9605 269.885 80.1617C270.638 78.7719 271.736 77.5985 273.073 76.7536C274.41 75.9087 275.942 75.4206 277.522 75.3359C279.158 75.4579 280.735 75.9997 282.101 76.9087C283.466 77.8177 284.573 79.0633 285.315 80.5249C286.466 82.7183 287.213 85.0995 287.524 87.5559Z\" fill=\"#F7AD7E\"></path> <path d=\"M137.343 124.217L204.885 158.542L213.926 198.289L157.242 171.8L137.343 124.217Z\" fill=\"#FBB03B\"></path> <path d=\"M225.953 43.3731C226.027 40.903 226.943 38.5323 228.55 36.6534C229.366 35.7077 230.332 34.9014 231.408 34.2664C232.915 33.3843 233.278 33.3065 234.265 34.7335C235.538 36.5755 235.954 38.729 237.123 40.6489C238.402 42.7782 240.187 44.5603 242.319 45.8378C244.715 47.3228 247.502 48.0549 250.32 47.9394C251.439 47.7704 252.578 47.7704 253.697 47.9394C254.073 48.0104 254.429 48.1635 254.739 48.3878C255.048 48.612 255.305 48.9019 255.489 49.2366C255.758 50.207 255.854 51.2167 255.775 52.2202C256.009 54.8147 258.581 56.6568 260.01 58.914C261.438 61.1712 261.854 64.5181 263.569 66.9309C263.887 67.415 264.326 67.8084 264.842 68.0725C266.063 68.5914 267.439 67.9168 268.712 67.6833C272.479 67.0347 275.519 70.5373 278.74 72.6647C285.208 76.9197 293.677 75.83 301.315 74.5587C303.653 74.2762 305.932 73.6283 308.069 72.6388C310.377 71.2785 312.426 69.5219 314.122 67.4498C316.953 64.3365 319.759 61.1712 322.539 58.0059C323.572 56.9925 324.393 55.7844 324.955 54.4515C325.252 53.522 325.332 52.5368 325.188 51.5716C324.935 49.7699 324.4 48.019 323.604 46.3827C321.578 41.8423 319.214 36.9128 314.641 34.8632C310.069 32.8135 304.666 34.3443 299.626 33.8254C299.012 33.8241 298.413 33.6338 297.912 33.2806C297.51 32.8279 297.225 32.2849 297.08 31.6979C294.976 24.7188 292.352 17.1429 286.066 13.5366C281.221 11.2125 275.76 10.4944 270.479 11.4869C265.25 12.4738 259.914 12.779 254.606 12.395C252.008 11.9799 249.411 11.1237 246.813 10.6048C240.738 9.25668 234.379 10.1798 228.94 13.1993C226.256 14.7946 224.01 17.0309 222.405 19.7072C220.8 22.3835 219.885 25.4161 219.744 28.5327C219.848 29.7498 219.848 30.9737 219.744 32.1909C219.562 33.3324 219.068 34.4481 218.886 35.5896C218.78 36.5764 218.884 37.5744 219.192 38.5181C219.499 39.4618 220.003 40.3298 220.671 41.065C221.338 41.8002 222.154 42.386 223.064 42.7838C223.974 43.1816 224.959 43.3824 225.953 43.3731Z\" fill=\"#734A39\"></path> <path d=\"M289.881 94.9252C290.682 102.268 290.769 109.671 290.141 117.03C290.172 117.669 290.009 118.302 289.673 118.846C289.121 119.45 288.359 119.821 287.543 119.884C283.336 120.666 279.067 121.074 274.788 121.104C274.187 121.295 273.552 121.352 272.927 121.271C272.302 121.189 271.703 120.971 271.172 120.631C270.641 120.292 270.191 119.84 269.856 119.307C269.52 118.774 269.306 118.174 269.229 117.549C266.89 107.794 264.578 98.0386 262.812 88.1536C262.111 84.1841 261.617 79.7216 264.007 76.5044C267.462 71.8344 276.788 71.3155 281.282 74.7142C286.712 78.8913 289.024 88.6466 289.881 94.9252Z\" fill=\"white\"></path> <path d=\"M216.366 82.4719L209.014 100.062C208.65 100.746 208.505 101.525 208.598 102.294C208.785 102.925 209.145 103.491 209.637 103.928C213.229 107.721 217.491 110.817 222.211 113.061C226.562 109.178 230.094 104.468 232.602 99.2063C235.044 92.9536 234.498 86.0004 233.771 79.3326C233.329 75.1036 233.485 61.5085 226.289 66.7753C221.275 70.3816 218.626 77.0494 216.366 82.4719Z\" fill=\"white\"></path> <path d=\"M349.351 247.482C349.349 249.88 349.836 252.253 350.783 254.457C351.73 256.66 353.117 258.648 354.858 260.298C357.861 263.139 361.73 264.893 365.847 265.28C366.617 265.319 367.389 265.319 368.159 265.28L497.192 262.685C502.146 262.632 506.883 260.65 510.396 257.161C513.908 253.672 515.919 248.952 516 244.005C516.002 241.607 515.515 239.234 514.568 237.03C513.621 234.826 512.235 232.839 510.493 231.188C508.726 229.492 506.642 228.161 504.36 227.27C502.078 226.38 499.642 225.948 497.192 225.999H492.802C493.722 222.812 494.186 219.51 494.178 216.192C494.187 211.463 493.229 206.783 491.361 202.437C489.494 198.092 486.758 194.173 483.32 190.922C480.291 188.027 476.773 185.691 472.928 184.021C467.952 181.852 462.562 180.79 457.134 180.907L446.171 181.141C439.393 181.267 432.772 183.193 426.987 186.723C421.202 190.252 416.463 195.257 413.257 201.222C410.088 199.745 406.623 199.008 403.126 199.069H400.294C394.423 199.142 388.812 201.497 384.651 205.633C380.49 209.77 378.106 215.363 378.005 221.225V222.289C377.994 224.301 378.283 226.303 378.862 228.231L368.185 228.464C363.169 228.512 358.375 230.538 354.847 234.1C351.319 237.662 349.344 242.472 349.351 247.482Z\" fill=\"white\"></path> <path d=\"M365.847 265.279C366.617 265.318 367.389 265.318 368.159 265.279L497.192 262.684C502.146 262.631 506.883 260.649 510.396 257.16C513.908 253.671 515.919 248.951 516 244.004C516.002 241.606 515.515 239.233 514.568 237.029C513.621 234.826 512.235 232.838 510.493 231.188C515.558 262.996 482.307 258.455 482.307 258.455H398.476L365.847 265.279Z\" fill=\"url(#paint2_linear_14397_367)\"></path> <path opacity=\"0.1\" d=\"M483.447 229.84L492.799 226.285C493.719 223.098 494.183 219.796 494.176 216.478C494.184 211.749 493.226 207.069 491.359 202.723C489.491 198.378 486.755 194.459 483.317 191.208C480.288 188.313 476.77 185.977 472.926 184.307C498.826 206.515 483.447 229.84 483.447 229.84Z\" fill=\"#2D2D2D\"></path> <path d=\"M18.7536 98.4482C18.7515 100.849 19.2414 103.224 20.193 105.428C21.1446 107.632 22.5378 109.619 24.2869 111.265C27.2994 114.106 31.1765 115.859 35.3016 116.246C36.0709 116.312 36.8444 116.312 37.6136 116.246L166.62 113.652C171.532 113.586 176.227 111.623 179.719 108.173C183.212 104.724 185.23 100.057 185.35 95.1532C185.377 92.7309 184.905 90.329 183.962 88.0972C183.019 85.8654 181.626 83.8514 179.869 82.1808C176.275 78.7802 171.493 76.9179 166.542 76.9918H162.178C163.086 73.8104 163.549 70.5188 163.555 67.2107C163.562 62.4822 162.603 57.802 160.736 53.4568C158.868 49.1116 156.133 45.1928 152.696 41.9404C149.666 39.04 146.149 36.6951 142.305 35.0132C137.329 32.842 131.939 31.7796 126.51 31.8998L115.548 32.1333C108.77 32.2583 102.149 34.1878 96.367 37.7225C90.5855 41.2572 85.8535 46.269 82.6595 52.2405C79.4973 50.7418 76.0273 50.0042 72.5281 50.0871H69.6964C63.8236 50.1471 58.2059 52.4931 54.0384 56.6262C49.8709 60.7593 47.4825 66.3532 47.3813 72.218V73.2817C47.3832 75.3032 47.6808 77.3135 48.2646 79.2491L37.5876 79.4566C32.5801 79.5179 27.7978 81.5442 24.2738 85.0977C20.7497 88.6513 18.7669 93.4467 18.7536 98.4482Z\" fill=\"white\"></path> <path d=\"M25.5061 114.487C26.2753 114.553 27.0488 114.553 27.8181 114.487L156.825 111.893C161.741 111.834 166.442 109.873 169.94 106.423C173.438 102.973 175.46 98.3028 175.581 93.3943C175.608 90.972 175.135 88.5701 174.192 86.3383C173.249 84.1064 171.856 82.0925 170.1 80.4219C175.295 112.256 141.913 107.69 141.913 107.69H58.0825L25.5061 114.487Z\" fill=\"url(#paint3_linear_14397_367)\"></path> <path opacity=\"0.2\" d=\"M152.671 81.3163L162.049 77.7618C162.957 74.5804 163.42 71.2888 163.426 67.9807C163.433 63.2522 162.474 58.5721 160.606 54.2268C158.739 49.8816 156.004 45.9628 152.567 42.7105C149.537 39.8101 146.02 37.4652 142.176 35.7832C168.05 57.9919 152.671 81.3163 152.671 81.3163Z\" fill=\"#2D2D2D\"></path> <path d=\"M253.515 47.9898C253.891 48.0609 254.247 48.214 254.557 48.4382C254.867 48.6625 255.123 48.9523 255.308 49.2871C255.576 50.2575 255.672 51.2672 255.593 52.2707C255.827 54.8652 258.399 56.7073 259.828 58.9645C261.257 61.2217 261.672 64.5686 263.387 66.9814C263.705 67.4655 264.144 67.8589 264.66 68.123C265.881 68.6419 267.258 67.9673 268.53 67.7338C272.297 67.0852 275.337 70.5878 278.558 72.7152C285.027 76.9702 293.495 75.8805 301.133 74.6092C303.536 74.3292 305.878 73.6633 308.069 72.6374C310.377 71.2772 312.426 69.5205 314.122 67.4484C316.953 64.3351 319.759 61.1698 322.539 58.0045C323.572 56.9911 324.393 55.783 324.955 54.4501C325.252 53.5206 325.332 52.5354 325.188 51.5702C323.977 50.8397 322.719 50.1897 321.422 49.6244C317.439 47.4899 312.784 46.9785 308.433 48.1974C304.952 49.5465 302.484 52.634 299.496 54.8393C295.33 57.7526 290.262 59.0913 285.198 58.616C280.135 58.1407 275.405 55.8824 271.856 52.2448C270.547 50.5222 269.008 48.986 267.284 47.6785C264.192 45.9921 257.905 46.2515 253.515 47.9898Z\" fill=\"#734A39\"></path> <g filter=\"url(#filter0_d_14397_367)\"><g clip-path=\"url(#clip0_14397_367)\"><path d=\"M387.951 243.842C368.063 243.842 351.941 227.74 351.941 207.878C351.941 188.016 368.063 171.914 387.951 171.914C407.839 171.914 423.961 188.016 423.961 207.878C423.961 227.74 407.839 243.842 387.951 243.842Z\" fill=\"white\"></path> <path d=\"M351.941 207.877C351.941 192.414 361.713 179.232 375.426 174.15V241.605C361.713 236.523 351.941 223.341 351.941 207.877Z\" fill=\"#FE5A61\"></path> <path d=\"M423.961 207.878C423.961 223.341 414.189 236.523 400.476 241.605V174.15C414.189 179.232 423.961 192.414 423.961 207.878Z\" fill=\"#3C87F8\"></path></g></g> <g filter=\"url(#filter1_d_14397_367)\"><mask id=\"mask0_14397_367\" maskUnits=\"userSpaceOnUse\" x=\"53\" y=\"10\" width=\"62\" height=\"62\" style=\"mask-type: alpha\"><path d=\"M83.9127 71.3388C100.862 71.3388 114.603 57.6186 114.603 40.6938C114.603 23.7691 100.862 10.0488 83.9127 10.0488C66.963 10.0488 53.2227 23.7691 53.2227 40.6938C53.2227 57.6186 66.963 71.3388 83.9127 71.3388Z\" fill=\"white\"></path></mask> <g mask=\"url(#mask0_14397_367)\"><path d=\"M60.8731 14.5009L83.8906 6.20117L105.629 15.7777C112.769 15.7777 115.859 27.9633 115.859 35.0943V55.9023C115.859 63.0333 110.051 68.8346 102.911 68.8346H100.99L96.486 74.541C96.1349 74.9856 95.5997 75.2449 95.0332 75.2449C94.4667 75.2449 93.931 74.9856 93.5804 74.541L89.0769 68.8346H65.6692C58.5293 68.8346 52.7209 63.0333 52.7209 55.9023V35.0943C52.7209 27.9633 53.7332 14.5009 60.8731 14.5009Z\" fill=\"#6787F5\" fill-opacity=\"0.9\"></path> <path d=\"M91.2383 67.0858L89.0888 64.3633L91.2383 67.0858Z\" fill=\"white\"></path> <path d=\"M52.7209 32.8751H68.2738L56.6964 21.3147C59.0255 19.0766 58.6715 13.8666 62.1518 13.8666L64.07 11.9513L76.1213 25.0386V17.6981L92.4585 6.84375V25.0386L103.072 14.505L105.629 15.1434C109.11 15.1434 109.555 19.0766 111.884 21.3147L100.306 32.8751H115.859V49.1883H100.306L111.449 60.3144L111.881 60.7458C109.552 62.983 110.387 64.9425 106.908 64.9425L105.629 67.4962L103.711 70.05L92.6569 57.222V77.0728L76.1218 64.3601V57.045L68.8201 64.3601H65.6692C62.2053 64.3601 59.0549 62.995 56.7292 60.7752L57.1647 60.3384L68.294 49.1883L52.7209 49.1883V32.8751Z\" fill=\"white\"></path> <path d=\"M52.7355 32.868H68.2884L56.711 21.3076C59.0401 19.0695 62.2034 17.6909 65.6838 17.6909H68.7845L76.1359 25.0315V0.453125L84.3047 10.6681V64.3529L76.1364 77.7041V57.0378L63.4451 70.6813L59.6088 67.4891C56.1449 67.4891 59.0694 62.9878 56.7438 60.768L57.1792 60.3313L68.3086 49.1812H52.7355V32.868Z\" fill=\"white\"></path> <path d=\"M79.8213 36.5699V6.84538L88.7589 -4.64648V36.5699H115.859V45.4949H88.7589L89.0769 87.2324L79.8213 76.435V45.4949L52.7209 45.4949V36.5699L79.8213 36.5699Z\" fill=\"#FF337A\"></path> <path d=\"M80.8236 36.7981V17.6914H85.168V64.9358H80.8236L80.6923 45.1442H52.5598L50.6417 36.8445L80.8236 36.7981Z\" fill=\"#FF337A\"></path> <path d=\"M91.2134 34.1045V31.4927L104.879 17.8477C106.28 18.0631 107.608 18.5042 108.824 19.1319L93.8291 34.1045H91.2134Z\" fill=\"#FF337A\"></path> <path d=\"M59.7643 19.1319C60.9806 18.5042 62.3086 18.0631 63.7099 17.8477L77.375 31.4927V34.1045H74.7593L59.7643 19.1319Z\" fill=\"#FF337A\"></path> <path d=\"M108.82 62.9365C107.604 63.5637 106.275 64.0043 104.874 64.2198L91.2129 50.5786V47.9668H93.8286L108.82 62.9365Z\" fill=\"#FF337A\"></path> <path d=\"M77.375 47.9668V50.5863L63.7609 64.2266C62.3568 64.0159 61.0253 63.5801 59.8056 62.9563L74.7665 47.9668H77.375Z\" fill=\"#FF337A\"></path></g></g> <g filter=\"url(#filter2_d_14397_367)\"><g clip-path=\"url(#clip1_14397_367)\"><path d=\"M336.148 321.395C336.148 325.044 335.491 328.54 334.29 331.772L306.277 334.366L278.263 331.772C277.062 328.54 276.405 325.044 276.405 321.395C276.405 317.746 277.062 314.25 278.263 311.018L306.277 308.424L334.29 311.018C335.491 314.25 336.148 317.746 336.148 321.395Z\" fill=\"#FFDA44\"></path> <path d=\"M278.261 311.019C282.482 299.659 293.431 291.562 306.275 291.562C319.119 291.562 330.068 299.659 334.289 311.019H278.261Z\" fill=\"#FE5A61\"></path> <path d=\"M334.289 331.773C330.068 343.134 319.119 351.23 306.275 351.23C293.431 351.23 282.482 343.134 278.261 331.773H334.289Z\" fill=\"#FE5A61\"></path></g></g> <path opacity=\"0.25\" d=\"M256.674 544.139C354.647 544.139 434.07 533.084 434.07 519.446C434.07 505.809 354.647 494.754 256.674 494.754C158.7 494.754 79.2773 505.809 79.2773 519.446C79.2773 533.084 158.7 544.139 256.674 544.139Z\" fill=\"#E6E6E6\"></path> <g filter=\"url(#filter3_d_14397_367)\"><mask id=\"mask1_14397_367\" maskUnits=\"userSpaceOnUse\" x=\"137\" y=\"399\" width=\"70\" height=\"71\" style=\"mask-type: alpha\"><path d=\"M162.472 401.103C144.003 406.486 133.267 425.854 138.492 444.364C143.717 462.874 162.925 473.515 181.395 468.133C199.864 462.75 210.6 443.382 205.375 424.872C200.15 406.362 180.941 395.721 162.472 401.103Z\" fill=\"white\"></path></mask> <g mask=\"url(#mask1_14397_367)\"><mask id=\"mask2_14397_367\" maskUnits=\"userSpaceOnUse\" x=\"123\" y=\"399\" width=\"99\" height=\"75\" style=\"mask-type: alpha\"><rect width=\"71.8981\" height=\"97.6699\" rx=\"15\" transform=\"matrix(0.0139738 1.00437 0.995156 -0.0212463 123.219 401.412)\" fill=\"#C4C4C4\"></rect></mask> <g mask=\"url(#mask2_14397_367)\"><path d=\"M127.185 477.762C121.711 477.89 117.271 474.017 117.268 469.111L117.231 409.168C117.228 404.262 121.663 400.181 127.137 400.053L161.341 399.252L161.389 476.961L127.185 477.762Z\" fill=\"#8ABA2E\"></path> <path d=\"M182.835 472.465L161.172 472.973L161.125 397.814L182.789 397.306L182.835 472.465Z\" fill=\"white\"></path> <path d=\"M215.301 474.885L181.695 475.672L181.647 397.963L215.253 397.176C220.631 397.05 224.994 400.925 224.997 405.831L225.034 465.774C225.037 470.68 220.68 474.759 215.301 474.885Z\" fill=\"#FF4B55\"></path> <path d=\"M214.512 397.996L193.192 397.621C193.192 397.621 200.686 385.848 204.012 386.027C207.339 386.207 214.512 397.996 214.512 397.996Z\" fill=\"#FA5B5D\" stroke=\"#FA5B5D\"></path></g></g></g> <g clip-path=\"url(#clip2_14397_367)\" filter=\"url(#filter4_d_14397_367)\"><path d=\"M169.499 294.999C186.895 294.999 200.997 280.897 200.997 263.501C200.997 246.104 186.895 232.002 169.499 232.002C152.102 232.002 138 246.104 138 263.501C138 280.897 152.102 294.999 169.499 294.999Z\" fill=\"white\"></path> <path d=\"M199.197 252.998H179.789C180.632 259.727 180.637 267.141 179.805 273.879H199.239C200.373 270.628 200.997 267.138 200.997 263.501C200.997 259.816 200.359 256.283 199.197 252.998Z\" fill=\"#FF4B55\"></path> <path d=\"M179.791 252.997H199.199C199.196 252.987 199.192 252.979 199.189 252.97C195.157 241.602 184.788 233.239 172.328 232.129C177.15 234.772 178.558 243.146 179.791 252.997Z\" fill=\"#444444\"></path> <path d=\"M172.328 294.868C184.81 293.756 195.195 285.364 199.21 273.966C199.221 273.936 199.23 273.906 199.241 273.877H179.807C178.584 283.782 177.17 292.214 172.328 294.868Z\" fill=\"#FFDA44\"></path> <path d=\"M138 263.501C138 267.138 138.624 270.628 139.758 273.879H181.429C182.261 267.141 182.256 259.727 181.413 252.998H139.8C138.638 256.283 138 259.816 138 263.501Z\" fill=\"#FF4B55\"></path> <path d=\"M139.811 252.97C139.807 252.979 139.804 252.987 139.801 252.997H181.415C180.181 243.146 177.151 234.772 172.33 232.129C171.397 232.047 170.455 232 169.501 232C155.798 232 144.144 240.752 139.811 252.97Z\" fill=\"#444444\"></path> <path d=\"M181.429 273.877H139.758C139.768 273.906 139.778 273.936 139.788 273.966C144.104 286.216 155.774 294.999 169.499 294.999C170.453 294.999 171.396 294.952 172.328 294.868C177.17 292.214 180.206 283.782 181.429 273.877Z\" fill=\"#FFDA44\"></path></g> "),_vm._ssrNode("<defs>","</defs>",[_vm._ssrNode("<filter id=\"filter0_d_14397_367\" x=\"337.941\" y=\"161.914\" width=\"100.02\" height=\"99.9277\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\">","</filter>",[_c('feFlood',{attrs:{"flood-opacity":"0","result":"BackgroundImageFix"}}),_vm._ssrNode(" "),_c('feColorMatrix',{attrs:{"in":"SourceAlpha","type":"matrix","values":"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0","result":"hardAlpha"}}),_vm._ssrNode(" "),_c('feOffset',{attrs:{"dy":"4"}}),_vm._ssrNode(" "),_c('feGaussianBlur',{attrs:{"stdDeviation":"7"}}),_vm._ssrNode(" "),_c('feColorMatrix',{attrs:{"type":"matrix","values":"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}}),_vm._ssrNode(" "),_c('feBlend',{attrs:{"mode":"normal","in2":"BackgroundImageFix","result":"effect1_dropShadow_14397_367"}}),_vm._ssrNode(" "),_c('feBlend',{attrs:{"mode":"normal","in":"SourceGraphic","in2":"effect1_dropShadow_14397_367","result":"shape"}})],2),_vm._ssrNode(" "),_vm._ssrNode("<filter id=\"filter1_d_14397_367\" x=\"39.2227\" y=\"0.0488281\" width=\"89.3789\" height=\"89.2891\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\">","</filter>",[_c('feFlood',{attrs:{"flood-opacity":"0","result":"BackgroundImageFix"}}),_vm._ssrNode(" "),_c('feColorMatrix',{attrs:{"in":"SourceAlpha","type":"matrix","values":"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0","result":"hardAlpha"}}),_vm._ssrNode(" "),_c('feOffset',{attrs:{"dy":"4"}}),_vm._ssrNode(" "),_c('feGaussianBlur',{attrs:{"stdDeviation":"7"}}),_vm._ssrNode(" "),_c('feColorMatrix',{attrs:{"type":"matrix","values":"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}}),_vm._ssrNode(" "),_c('feBlend',{attrs:{"mode":"normal","in2":"BackgroundImageFix","result":"effect1_dropShadow_14397_367"}}),_vm._ssrNode(" "),_c('feBlend',{attrs:{"mode":"normal","in":"SourceGraphic","in2":"effect1_dropShadow_14397_367","result":"shape"}})],2),_vm._ssrNode(" "),_vm._ssrNode("<filter id=\"filter2_d_14397_367\" x=\"246.406\" y=\"261.562\" width=\"127.742\" height=\"127.668\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\">","</filter>",[_c('feFlood',{attrs:{"flood-opacity":"0","result":"BackgroundImageFix"}}),_vm._ssrNode(" "),_c('feColorMatrix',{attrs:{"in":"SourceAlpha","type":"matrix","values":"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0","result":"hardAlpha"}}),_vm._ssrNode(" "),_c('feOffset',{attrs:{"dx":"4","dy":"4"}}),_vm._ssrNode(" "),_c('feGaussianBlur',{attrs:{"stdDeviation":"17"}}),_vm._ssrNode(" "),_c('feColorMatrix',{attrs:{"type":"matrix","values":"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"}}),_vm._ssrNode(" "),_c('feBlend',{attrs:{"mode":"normal","in2":"BackgroundImageFix","result":"effect1_dropShadow_14397_367"}}),_vm._ssrNode(" "),_c('feBlend',{attrs:{"mode":"normal","in":"SourceGraphic","in2":"effect1_dropShadow_14397_367","result":"shape"}})],2),_vm._ssrNode(" "),_vm._ssrNode("<filter id=\"filter3_d_14397_367\" x=\"104.172\" y=\"374.705\" width=\"129.523\" height=\"129.824\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\">","</filter>",[_c('feFlood',{attrs:{"flood-opacity":"0","result":"BackgroundImageFix"}}),_vm._ssrNode(" "),_c('feColorMatrix',{attrs:{"in":"SourceAlpha","type":"matrix","values":"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0","result":"hardAlpha"}}),_vm._ssrNode(" "),_c('feOffset',{attrs:{"dx":"-3","dy":"5"}}),_vm._ssrNode(" "),_c('feGaussianBlur',{attrs:{"stdDeviation":"15"}}),_vm._ssrNode(" "),_c('feColorMatrix',{attrs:{"type":"matrix","values":"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.19 0"}}),_vm._ssrNode(" "),_c('feBlend',{attrs:{"mode":"normal","in2":"BackgroundImageFix","result":"effect1_dropShadow_14397_367"}}),_vm._ssrNode(" "),_c('feBlend',{attrs:{"mode":"normal","in":"SourceGraphic","in2":"effect1_dropShadow_14397_367","result":"shape"}})],2),_vm._ssrNode(" "),_vm._ssrNode("<filter id=\"filter4_d_14397_367\" x=\"98\" y=\"190\" width=\"143\" height=\"143\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\">","</filter>",[_c('feFlood',{attrs:{"flood-opacity":"0","result":"BackgroundImageFix"}}),_vm._ssrNode(" "),_c('feColorMatrix',{attrs:{"in":"SourceAlpha","type":"matrix","values":"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0","result":"hardAlpha"}}),_vm._ssrNode(" "),_c('feOffset',{attrs:{"dy":"-2"}}),_vm._ssrNode(" "),_c('feGaussianBlur',{attrs:{"stdDeviation":"20"}}),_vm._ssrNode(" "),_c('feColorMatrix',{attrs:{"type":"matrix","values":"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"}}),_vm._ssrNode(" "),_c('feBlend',{attrs:{"mode":"normal","in2":"BackgroundImageFix","result":"effect1_dropShadow_14397_367"}}),_vm._ssrNode(" "),_c('feBlend',{attrs:{"mode":"normal","in":"SourceGraphic","in2":"effect1_dropShadow_14397_367","result":"shape"}})],2),_vm._ssrNode(" "),_c('linearGradient',{attrs:{"id":"paint0_linear_14397_367","x1":"-3284.56","y1":"7090.28","x2":"-4546.77","y2":"6816.2","gradientUnits":"userSpaceOnUse"}},[_c('stop',{attrs:{"offset":"0.01"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"0.13","stop-opacity":"0.69"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"0.25","stop-opacity":"0.32"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"1","stop-opacity":"0"}})],1),_vm._ssrNode(" "),_c('linearGradient',{attrs:{"id":"paint1_linear_14397_367","x1":"-8940.35","y1":"2899.63","x2":"-7743.66","y2":"5771.2","gradientUnits":"userSpaceOnUse"}},[_c('stop',{attrs:{"offset":"0.01"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"0.13","stop-opacity":"0.69"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"0.25","stop-opacity":"0.32"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"1","stop-opacity":"0"}})],1),_vm._ssrNode(" "),_c('linearGradient',{attrs:{"id":"paint2_linear_14397_367","x1":"-10280","y1":"5845.8","x2":"-10244.6","y2":"3842.73","gradientUnits":"userSpaceOnUse"}},[_c('stop',{attrs:{"offset":"0.01"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"0.13","stop-opacity":"0.69"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"0.25","stop-opacity":"0.32"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"1","stop-opacity":"0"}})],1),_vm._ssrNode(" "),_c('linearGradient',{attrs:{"id":"paint3_linear_14397_367","x1":"-30277.8","y1":"3711.27","x2":"-30242.4","y2":"1708.9","gradientUnits":"userSpaceOnUse"}},[_c('stop',{attrs:{"offset":"0.01"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"0.13","stop-opacity":"0.69"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"0.25","stop-opacity":"0.32"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"1","stop-opacity":"0"}})],1),_vm._ssrNode(" <clipPath id=\"clip0_14397_367\"><rect width=\"72.02\" height=\"71.9281\" fill=\"white\" transform=\"matrix(-1 0 0 1 423.961 171.914)\"></rect></clipPath> <clipPath id=\"clip1_14397_367\"><rect width=\"59.7436\" height=\"59.6672\" fill=\"white\" transform=\"matrix(-1 0 0 1 336.148 291.562)\"></rect></clipPath> <clipPath id=\"clip2_14397_367\"><rect width=\"63\" height=\"63\" fill=\"white\" transform=\"translate(138 232)\"></rect></clipPath>")],2)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/images/HomePageIntroImage.vue?vue&type=template&id=e04cb670&


/***/ }),

/***/ 1268:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HomePageIntroImage_vue_vue_type_template_id_e04cb670___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1266);
/* harmony import */ var _HomePageIntroImage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(1146);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HomePageIntroImage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HomePageIntroImage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(6);





/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"])(
  _HomePageIntroImage_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _HomePageIntroImage_vue_vue_type_template_id_e04cb670___WEBPACK_IMPORTED_MODULE_0__[/* render */ "a"],
  _HomePageIntroImage_vue_vue_type_template_id_e04cb670___WEBPACK_IMPORTED_MODULE_0__[/* staticRenderFns */ "b"],
  false,
  null,
  null,
  "13e874b9"
  
)

/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1277:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/SelectLanguage.vue?vue&type=template&id=d447801e&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"select-language"},[_vm._ssrNode("<div id=\"intro-select\" class=\"intro-select mr-4\">","</div>",[_c('v-select',{attrs:{"items":_vm.items,"item-text":"name","item-value":"id","placeholder":_vm.$t('home_page.i_want_to_speak'),"dense":"","outlined":"","hide-details":"","height":"44","color":"orange","hide-selected":"","hide-no-data":"","menu-props":{
        bottom: true,
        offsetY: true,
        nudgeBottom: 5,
        contentClass: 'select-language-list',
        maxHeight: _vm.maxHeightDropdown,
      }},scopedSlots:_vm._u([{key:"item",fn:function(ref){
      var item = ref.item;
return [(item.id !== _vm.selectedItemId)?_c('div',{staticClass:"v-list-item__content"},[_c('div',{staticClass:"v-list-item__title"},[_vm._v("\n            "+_vm._s(_vm.$t(item.name))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"v-list-item__icon"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (item.isoCode) + ".svg")}})],1)]):_vm._e()]}},(_vm.items.length > 4)?{key:"append-item",fn:function(){return [_c('div',{staticClass:"v-list-item"},[_c('div',{staticClass:"v-list-item__content v-list-item__content--show-more"},[_c('div',{staticClass:"v-list-item__title"},[_c('nuxt-link',{attrs:{"to":_vm.localePath({ name: 'teacher-listing' })}},[_vm._v("\n                "+_vm._s(_vm.$t('see_more'))+"...\n              ")])],1)])])]},proxy:true}:null],null,true),model:{value:(_vm.selectedItemId),callback:function ($$v) {_vm.selectedItemId=$$v},expression:"selectedItemId"}})],1),_vm._ssrNode(" "),_c('v-btn',{attrs:{"large":"","to":_vm.link,"color":"primary"}},[_vm._v(_vm._s(_vm.$t('lets_go')))])],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/SelectLanguage.vue?vue&type=template&id=d447801e&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/SelectLanguage.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var SelectLanguagevue_type_script_lang_js_ = ({
  name: 'SelectLanguage',
  props: {
    items: {
      type: Array,
      required: true
    }
  },

  data() {
    return {
      selectedItemId: null
    };
  },

  computed: {
    link() {
      return this.selectedItemId ? `/teacher-listing/1/language,${this.selectedItemId}` : '/teacher-listing';
    },

    maxHeightDropdown() {
      return this.items.length > 4 ? 175 : 'auto';
    }

  }
});
// CONCATENATED MODULE: ./components/homepage/SelectLanguage.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_SelectLanguagevue_type_script_lang_js_ = (SelectLanguagevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VSelect/VSelect.js + 5 modules
var VSelect = __webpack_require__(941);

// CONCATENATED MODULE: ./components/homepage/SelectLanguage.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1252)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_SelectLanguagevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "183e3d0c"
  
)

/* harmony default export */ var SelectLanguage = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */




installComponents_default()(component, {VBtn: VBtn["a" /* default */],VImg: VImg["a" /* default */],VSelect: VSelect["a" /* default */]})


/***/ }),

/***/ 1340:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_IntroSection_vue_vue_type_style_index_0_id_0a29cf87_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1254);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_IntroSection_vue_vue_type_style_index_0_id_0a29cf87_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_IntroSection_vue_vue_type_style_index_0_id_0a29cf87_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_IntroSection_vue_vue_type_style_index_0_id_0a29cf87_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_IntroSection_vue_vue_type_style_index_0_id_0a29cf87_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1341:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".intro[data-v-0a29cf87]{--pb:170px;position:relative;height:calc(100vh - 74px);padding-bottom:var(--pb);color:#fff}@media only screen and (max-width:1439px){.intro[data-v-0a29cf87]{--pb:150px}}@media only screen and (max-width:991px){.intro[data-v-0a29cf87]{--pb:120px}}@media only screen and (max-width:639px){.intro[data-v-0a29cf87]{--pb:100px}}@media only screen and (max-width:1643px){.intro .container[data-v-0a29cf87]{max-width:80%!important}}@media only screen and (max-width:1439px){.intro .container[data-v-0a29cf87]{max-width:90%!important}}@media only screen and (min-width:640px){.intro[data-v-0a29cf87]{min-height:680px;max-height:740px}}@media only screen and (min-width:768px){.intro[data-v-0a29cf87]{min-height:780px;max-height:875px}}@media only screen and (max-width:639px){.intro[data-v-0a29cf87]{height:auto}}@media only screen and (max-width:479px){.intro .container[data-v-0a29cf87]{max-width:calc(100% - 30px)!important}}.intro-bg[data-v-0a29cf87]{position:absolute;width:100%;height:var(--pb);bottom:0;left:0;overflow:hidden}.intro-bg svg[data-v-0a29cf87]{position:absolute;width:1920px;height:100%;top:0;left:50%;transform:translateX(-50%)}@media only screen and (min-width:1921px){.intro-bg svg[data-v-0a29cf87]{width:100%;left:0;transform:none}}@media only screen and (max-width:991px){.intro-bg svg[data-v-0a29cf87]{width:1300px}}@media only screen and (max-width:639px){.intro-bg svg[data-v-0a29cf87]{width:1000px}}.intro-wrap[data-v-0a29cf87]{position:relative;height:100%;padding-top:90px;background-color:var(--v-darkLight-base);overflow:hidden}@media only screen and (min-width:992px){.intro-wrap[data-v-0a29cf87],.intro-wrap .col[data-v-0a29cf87]{height:100%}}.intro-wrap .container[data-v-0a29cf87],.intro-wrap .row[data-v-0a29cf87]{height:100%}@media only screen and (max-width:1439px){.intro-wrap[data-v-0a29cf87]{padding-top:50px}}@media only screen and (max-width:639px){.intro-wrap[data-v-0a29cf87]{padding:60px 0 50px}}.intro-content[data-v-0a29cf87]{max-width:570px}@media only screen and (max-width:991px){.intro-content[data-v-0a29cf87]{max-width:calc(100% - 340px)}}@media only screen and (max-width:639px){.intro-content[data-v-0a29cf87]{max-width:calc(100% - 160px)}}@media only screen and (max-width:479px){.intro-content[data-v-0a29cf87]{max-width:calc(100% - 135px)}}.intro-content h1[data-v-0a29cf87]{font-size:48px;line-height:1.125}@media only screen and (max-width:991px){.intro-content h1[data-v-0a29cf87]{font-size:34px}}@media only screen and (max-width:639px){.intro-content h1[data-v-0a29cf87]{font-size:24px}}@media only screen and (max-width:479px){.intro-content h1[data-v-0a29cf87]{font-size:22px}}.intro-content h2[data-v-0a29cf87]{margin-top:32px;font-size:20px;line-height:1.4}@media only screen and (max-width:991px){.intro-content h2[data-v-0a29cf87]{font-size:16px}}@media only screen and (max-width:639px){.intro-content h2[data-v-0a29cf87]{font-size:15px}}.intro-content-bottom[data-v-0a29cf87]{margin-top:56px}@media only screen and (max-width:991px){.intro-content-bottom[data-v-0a29cf87]{justify-content:center;margin-top:35px}}@media only screen and (max-width:639px){.intro-content-bottom[data-v-0a29cf87]{max-width:360px;flex-direction:column;margin-left:auto;margin-right:auto}}.intro-select[data-v-0a29cf87]{width:224px}@media only screen and (max-width:767px){.intro-select[data-v-0a29cf87]{width:260px}}@media only screen and (max-width:639px){.intro-select[data-v-0a29cf87]{width:100%;margin-bottom:24px}}.intro-img[data-v-0a29cf87]{display:flex;max-width:516px;height:100%}@media only screen and (max-width:991px){.intro-img[data-v-0a29cf87]{max-width:340px}}@media only screen and (max-width:639px){.intro-img[data-v-0a29cf87]{position:relative;width:256px;max-width:256px;margin-right:-105px}}@media only screen and (max-width:479px){.intro-img[data-v-0a29cf87]{width:236px;max-width:236px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1342:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StatSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1255);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StatSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StatSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StatSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StatSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1343:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(68);
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(141);
var ___CSS_LOADER_URL_IMPORT_1___ = __webpack_require__(142);
var ___CSS_LOADER_URL_IMPORT_2___ = __webpack_require__(143);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
var ___CSS_LOADER_URL_REPLACEMENT_1___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_1___);
var ___CSS_LOADER_URL_REPLACEMENT_2___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_2___);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".stat{padding-top:85px}@media only screen and (max-width:991px){.stat{padding-top:40px}}@media only screen and (max-width:479px){.stat{padding-top:10px}}.stat-item{min-height:138px}@media only screen and (min-width:768px){.stat-item{display:flex;justify-content:center}}@media only screen and (max-width:991px){.stat-item{min-height:172px}}.stat-item-wrap{position:relative;min-height:inherit}@media only screen and (max-width:991px){.stat-item-wrap{max-width:258px;margin-left:auto;margin-right:auto}}.stat-item-wrap:before{content:\"\";position:absolute;top:10px;left:-25px;width:100%;height:100%;background-size:contain;background-repeat:no-repeat}@media only screen and (max-width:991px){.stat-item-wrap:before{left:0;top:-10px}}.stat-item-i1 .stat-item-wrap:before{background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");background-position:0}@media only screen and (max-width:991px){.stat-item-i1 .stat-item-text{max-width:150px}}.stat-item-i2 .stat-item-wrap:before{background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_1___ + ");background-position:0}@media only screen and (max-width:991px){.stat-item-i2 .stat-item-text{max-width:166px}}.stat-item-i3 .stat-item-wrap:before{background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_2___ + ");background-position:0}@media only screen and (max-width:991px){.stat-item-i3 .stat-item-text{max-width:136px}}.stat-item-value{position:relative;margin-bottom:12px;font-size:64px;font-weight:800;line-height:1}@media only screen and (max-width:1439px){.stat-item-value{margin-bottom:0;font-size:56px}}@media only screen and (max-width:991px){.stat-item-value{font-size:54px;text-align:center}}.stat-item-text{position:relative;font-size:18px;color:var(--v-grey-base);line-height:1.5}.stat-item-text span{font-size:16px}@media only screen and (max-width:991px){.stat-item-text{margin:0 auto;font-size:15px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1344:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HowWorksSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1256);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HowWorksSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HowWorksSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HowWorksSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HowWorksSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1345:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(68);
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(138);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".how-works{position:relative;padding-top:105px}@media only screen and (max-width:767px){.how-works{padding-top:80px}}.how-works .section-head{margin-bottom:90px}@media only screen and (max-width:767px){.how-works .section-head{margin-bottom:60px}}.how-works-content{position:relative;color:#fff}@media only screen and (min-width:992px){.how-works-content{height:1px;min-height:730px}}@media only screen and (max-width:991px){.how-works-content{padding:146px 0 92px}}@media only screen and (max-width:639px){.how-works-content{padding:180px 0 65px}}.how-works-content:after{content:\"\";position:absolute;top:-230px;right:0;width:295px;height:648px;background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");background-size:contain;background-position:100%}@media only screen and (max-width:1439px){.how-works-content:after{top:-115px;width:200px;height:448px}}@media only screen and (max-width:991px){.how-works-content:after{top:0;width:110px;height:221px}}@media only screen and (max-width:639px){.how-works-content:after{top:-50px}}.how-works-item{position:relative;margin-bottom:75px}@media only screen and (max-width:991px){.how-works-item{max-width:80%;margin-bottom:0;padding-left:30px;padding-bottom:110px}}@media only screen and (max-width:639px){.how-works-item{max-width:100%;padding-left:25px}}@media only screen and (max-width:479px){.how-works-item{padding-bottom:150px}}.how-works-item-bg{position:absolute}.how-works-item-title{position:relative}.how-works-item-title:before{position:absolute;top:-8px;left:-28px;font-size:82px;font-weight:700;line-height:.8;color:var(--v-orange-base);opacity:.2}.how-works-item-i1 .how-works-item-bg{left:-265px;top:20px;width:210px;height:170px}@media only screen and (max-width:1439px){.how-works-item-i1 .how-works-item-bg{left:-225px;top:52px;width:180px;height:155px}}@media only screen and (max-width:991px){.how-works-item-i1 .how-works-item-bg{left:205px;top:60px;width:169px;height:142px}}@media only screen and (max-width:639px){.how-works-item-i1 .how-works-item-bg{left:180px;top:65px}}@media only screen and (max-width:479px){.how-works-item-i1 .how-works-item-bg{left:125px;height:150px;top:auto;bottom:0}}.how-works-item-i1 .details-item-title:before{content:\"1\"}.how-works-item-i2 .how-works-item-bg{right:-350px;top:8px;width:280px;height:80px}@media only screen and (max-width:1439px){.how-works-item-i2 .how-works-item-bg{right:-292px;top:22px;width:238px;height:66px}}@media only screen and (max-width:991px){.how-works-item-i2 .how-works-item-bg{left:70px;top:65px;width:167px;height:190px}}@media only screen and (max-width:639px){.how-works-item-i2 .how-works-item-bg{left:85px;top:70px}}@media only screen and (max-width:479px){.how-works-item-i2 .how-works-item-bg{height:150px;top:auto;bottom:0}}.how-works-item-i2 .details-item-title:before{content:\"2\"}.how-works-item-i3 .how-works-item-bg{left:-20px;top:calc(100% + 22px);width:178px;height:135px}@media only screen and (max-width:1439px){.how-works-item-i3 .how-works-item-bg{left:0;width:148px;height:115px}}@media only screen and (max-width:991px){.how-works-item-i3 .how-works-item-bg{left:auto;right:5px;top:75px;width:174px;height:140px}}@media only screen and (max-width:479px){.how-works-item-i3 .how-works-item-bg{height:150px;top:auto;bottom:0}}.how-works-item-i3 .details-item-title:before{content:\"3\"}.how-works-item-i4 .details-item-title:before{content:\"4\"}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1346:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1257);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1347:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".about{padding-top:80px}@media only screen and (min-width:992px){.about{overflow:hidden}}.about .section-head{margin-bottom:130px}@media only screen and (max-width:991px){.about .section-head{margin-bottom:80px}}.about-wrap{position:relative}@media only screen and (max-width:991px){.about-wrap{padding-bottom:62px!important}}.about-wrap .about-bg{position:absolute;left:58%;top:25px;width:90%;height:100%;transform:translateX(-50%)}@media only screen and (max-width:1439px){.about-wrap .about-bg{top:12px;left:68%;height:104%}}@media only screen and (max-width:991px){.about-wrap .about-bg{width:100%;height:100%;top:135px;left:0;transform:translateX(0)}}.about-item{position:relative;max-width:330px;padding-bottom:70px}@media only screen and (max-width:991px){.about-item{max-width:380px;margin:0 auto}}.about-item-image{margin-bottom:5px}@media only screen and (max-width:991px){.about-item-image{display:flex;justify-content:center;margin-bottom:18px;margin-left:auto;margin-right:auto}}.about-item-more{margin-top:12px}.about-item-i1{margin-top:25px}@media only screen and (max-width:991px){.about-item-i1{margin-top:0}}.about-item-i1 .about-item-image{max-width:280px}@media only screen and (max-width:991px){.about-item-i1 .about-item-image{max-width:304px}}.about-item-i2 .about-item-image{max-width:242px}@media only screen and (max-width:991px){.about-item-i2 .about-item-image{max-width:293px}}.about-item-i3{top:-240px;left:30px}@media only screen and (max-width:1439px){.about-item-i3{top:0;left:20px;margin-top:-590px}}@media only screen and (max-width:991px){.about-item-i3{top:auto;left:auto;margin-top:0}}.about-item-i3 .about-item-image{max-width:248px}@media only screen and (max-width:991px){.about-item-i3 .about-item-image{max-width:280px}}.about-item-i4{top:-30px}@media only screen and (max-width:1439px){.about-item-i4{top:-140px;margin-top:127px}}@media only screen and (max-width:991px){.about-item-i4{top:auto;margin-top:0}}.about-item-i4 .about-item-image{max-width:255px}.about-item-i5{top:-250px;max-width:620px;padding-bottom:0}@media only screen and (max-width:1439px){.about-item-i5{top:-160px;max-width:575px}}@media only screen and (max-width:991px){.about-item-i5{top:15px;margin-left:30px}}@media only screen and (max-width:479px){.about-item-i5{max-width:371px;top:-50px;margin-left:0;margin-top:80px;padding-top:130px}}.about-item-i5 .about-item-image{position:absolute;top:50%;left:0;width:100%;transform:translateY(-50%)}@media only screen and (max-width:479px){.about-item-i5 .about-item-image{top:0;left:-30px;transform:none}.about-item-i5 .about-item-image .type-1{display:none}}.about-item-i5 .about-item-image .type-2{display:none}@media only screen and (max-width:479px){.about-item-i5 .about-item-image .type-2{display:block}}.about-item-i5 .about-item-title{font-size:20px}@media only screen and (max-width:639px){.about-item-i5 .about-item-title{font-size:18px}}.about-item-i5 .about-item-text{font-size:16px}.about-item-i5 .about-item-more,.about-item-i5 .about-item-text,.about-item-i5 .about-item-title{padding-left:200px}@media only screen and (max-width:639px){.about-item-i5 .about-item-more,.about-item-i5 .about-item-text,.about-item-i5 .about-item-title{padding-left:32%}}@media only screen and (max-width:479px){.about-item-i5 .about-item-more,.about-item-i5 .about-item-text,.about-item-i5 .about-item-title{padding-left:0;padding-right:30px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1348:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LanguagesSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1258);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LanguagesSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LanguagesSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LanguagesSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LanguagesSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1349:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".home-page .languages{padding:90px 0 210px}@media only screen and (max-width:1439px){.home-page .languages{padding-bottom:155px}}@media only screen and (max-width:991px){.home-page .languages{padding-bottom:120px}}@media only screen and (max-width:767px){.home-page .languages{padding:30px 0}}@media only screen and (max-width:479px){.home-page .languages{padding:0 0 30px}}.home-page .languages-content{position:relative;margin-top:136px}@media only screen and (max-width:1439px){.home-page .languages-content{margin-top:96px}}@media only screen and (max-width:991px){.home-page .languages-content{margin-top:75px}}@media only screen and (max-width:767px){.home-page .languages-content{padding-left:28%}}@media only screen and (max-width:479px){.home-page .languages-content{margin-top:50px}}.home-page .languages-content-image{position:absolute;left:50%;max-width:932px;transform:translateX(-50%)}@media only screen and (max-width:1439px){.home-page .languages-content-image{max-width:760px}}@media only screen and (max-width:991px){.home-page .languages-content-image{max-width:550px}}@media only screen and (max-width:767px){.home-page .languages-content-image{position:relative;width:100%;height:0;left:auto;right:0;padding-bottom:168%;transform:none}.home-page .languages-content-image>.v-image{position:absolute;top:0;right:0;width:100%;height:100%}}.home-page .languages-lines-mobile{position:absolute;top:0;left:0;width:100%;height:100%}.home-page .languages-line{display:flex;justify-content:center;width:100%}@media only screen and (min-width:768px){.home-page .languages-line{position:relative}}.home-page .languages-line-r1>div{padding:0 45px}@media only screen and (max-width:1439px){.home-page .languages-line-r1>div{padding:0 28px}}@media only screen and (max-width:991px){.home-page .languages-line-r1>div{padding:0 14px}}@media only screen and (max-width:767px){.home-page .languages-line-r1>div{padding:0}}.home-page .languages-country{position:relative;display:flex;align-items:center;line-height:1.3}@media only screen and (max-width:767px){.home-page .languages-country{position:absolute;margin:0!important}}.home-page .languages-country-flag{display:flex;align-items:center;width:32px;margin-right:16px}@media only screen and (max-width:991px){.home-page .languages-country-flag{width:24px;margin-right:12px}}@media only screen and (max-width:479px){.home-page .languages-country-flag{margin-right:6px}}.home-page .languages-country-flag--double{width:49px}@media only screen and (max-width:991px){.home-page .languages-country-flag--double{width:39px}}.home-page .languages-country a{font-size:24px;font-weight:600;color:var(--v-darkLight-base)!important;text-decoration:none;transition:color .2s}@media only screen and (max-width:1439px){.home-page .languages-country a{font-size:18px}}@media only screen and (max-width:991px){.home-page .languages-country a{font-size:16px}}@media only screen and (max-width:767px){.home-page .languages-country a{white-space:nowrap}}.home-page .languages-country a:hover{color:var(--v-orange-base)!important}.home-page .languages-country-uk{top:-28px}@media only screen and (max-width:1439px){.home-page .languages-country-uk{top:-22px}}@media only screen and (max-width:767px){.home-page .languages-country-uk{top:auto;bottom:84%;right:60%;left:auto}}@media only screen and (max-width:479px){.home-page .languages-country-uk{right:65%}}@media only screen and (max-width:767px){.home-page .languages-country-fr{bottom:73%;right:79%;left:auto}}@media only screen and (max-width:767px){.home-page .languages-country-pl{bottom:100%;right:88%;left:auto}}.home-page .languages-country-it{margin-top:80px;left:-322px}@media only screen and (max-width:1439px){.home-page .languages-country-it{margin-top:55px;left:-266px}}@media only screen and (max-width:991px){.home-page .languages-country-it{margin-top:46px;left:-192px}}@media only screen and (max-width:767px){.home-page .languages-country-it{bottom:43%;right:69%;left:auto}}.home-page .languages-country-ar{margin-top:50px;left:408px}@media only screen and (max-width:1439px){.home-page .languages-country-ar{margin-top:16px;left:318px}}@media only screen and (max-width:991px){.home-page .languages-country-ar{margin-top:12px;left:238px}}@media only screen and (max-width:767px){.home-page .languages-country-ar{bottom:62%;right:68%;left:auto}}.home-page .languages-country-ge{margin-top:90px;left:-382px}@media only screen and (max-width:1439px){.home-page .languages-country-ge{margin-top:68px;left:-260px}}@media only screen and (max-width:991px){.home-page .languages-country-ge{margin-top:42px;left:-190px}}@media only screen and (max-width:767px){.home-page .languages-country-ge{bottom:12%;left:auto;right:38%}}@media only screen and (max-width:479px){.home-page .languages-country-ge{bottom:10%}}.home-page .languages-country-pr-br{margin-top:70px;left:435px}@media only screen and (max-width:1439px){.home-page .languages-country-pr-br{margin-top:50px;left:308px}}@media only screen and (max-width:991px){.home-page .languages-country-pr-br{margin-top:28px;left:245px}}@media only screen and (max-width:767px){.home-page .languages-country-pr-br{bottom:17%;right:68%;left:auto}}.home-page .languages-country-ru{margin-top:106px;left:-444px}@media only screen and (max-width:1439px){.home-page .languages-country-ru{margin-top:55px;left:-320px}}@media only screen and (max-width:991px){.home-page .languages-country-ru{margin-top:55px;left:-260px}}@media only screen and (max-width:767px){.home-page .languages-country-ru{bottom:18%;left:auto;right:6%}}.home-page .languages-country-jp{margin-top:95px;left:466px}@media only screen and (max-width:1439px){.home-page .languages-country-jp{margin-top:66px;left:352px}}@media only screen and (max-width:991px){.home-page .languages-country-jp{margin-top:40px;left:268px}}@media only screen and (max-width:767px){.home-page .languages-country-jp{bottom:31%;right:82%;left:auto}}.home-page .languages-country-sp{margin-top:140px;left:-415px}@media only screen and (max-width:1439px){.home-page .languages-country-sp{margin-top:135px;left:-330px}}@media only screen and (max-width:991px){.home-page .languages-country-sp{margin-top:80px;left:-245px}}@media only screen and (max-width:767px){.home-page .languages-country-sp{bottom:52%;right:85%;left:auto}}.home-page .languages-country-ch{margin-top:15px;left:415px}@media only screen and (max-width:1439px){.home-page .languages-country-ch{margin-top:12px;left:324px}}@media only screen and (max-width:991px){.home-page .languages-country-ch{margin-top:7px;left:248px}}@media only screen and (max-width:767px){.home-page .languages-country-ch{bottom:95%;right:46%;left:auto}}.home-page .languages-country-du{margin-top:60px;left:-260px}@media only screen and (max-width:1439px){.home-page .languages-country-du{margin-top:28px;left:-206px}}@media only screen and (max-width:991px){.home-page .languages-country-du{margin-top:22px;left:-145px}}@media only screen and (max-width:767px){.home-page .languages-country-du{bottom:28%;right:40%;left:auto}}@media only screen and (max-width:479px){.home-page .languages-country-du{bottom:26%;right:28%}}.home-page .languages-country-sw{margin-top:54px;left:290px}@media only screen and (max-width:1439px){.home-page .languages-country-sw{margin-top:42px;left:235px}}@media only screen and (max-width:991px){.home-page .languages-country-sw{margin-top:32px;left:170px}}@media only screen and (max-width:767px){.home-page .languages-country-sw{top:-3%;right:12%;left:auto}}@media only screen and (max-width:479px){.home-page .languages-country-sw{top:-6%;right:2%}}@media only screen and (min-width:1440px){.es.home-page .languages-country-uk{top:-50px}.es.home-page .languages-country-jp{margin-top:20px}.es.home-page .languages-country-pr-br{margin-top:35px}}@media only screen and (min-width:992px)and (max-width:1439px){.es.home-page .languages-country-uk{top:-40px}.es.home-page .languages-country-ge{margin-top:35px}.es.home-page .languages-country-jp{margin-top:50px}}@media only screen and (min-width:768px)and (max-width:991px){.es.home-page .languages-country-uk{top:-40px}.es.home-page .languages-country-ge{margin-top:26px}.es.home-page .languages-country-jp{margin-top:35px}.es.home-page .languages-country-pr-br{margin-top:12px}}@media only screen and (max-width:479px){.es.home-page .languages-country-uk{right:46%}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1350:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TutorsSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1259);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TutorsSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TutorsSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TutorsSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TutorsSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1351:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(68);
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(622);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".tutors{position:relative;padding:220px 0 132px}@media only screen and (max-width:1439px){.tutors{padding:150px 0 110px}}@media only screen and (max-width:991px){.tutors{padding:150px 0 80px}}@media only screen and (max-width:767px){.tutors{padding:90px 0 80px}}.tutors-decoration--after,.tutors-decoration--before{position:absolute;width:100%}.tutors-decoration--before{top:0;left:0;width:100%;max-width:874px;height:1015px;max-height:1085px}@media only screen and (max-width:1643px){.tutors-decoration--before{top:120px;max-width:690px;height:820px}}@media only screen and (max-width:1439px){.tutors-decoration--before{top:55px;width:calc(100% - 45px);max-width:480px;height:600px}}@media only screen and (max-width:991px){.tutors-decoration--before{top:0;height:400px}}@media only screen and (max-width:767px){.tutors-decoration--before{height:400px}}.tutors-decoration--before .v-image__image{background-position:0}.tutors-decoration--after{right:0;top:145px;max-width:437px;height:100%;max-height:679px}@media only screen and (max-width:1643px){.tutors-decoration--after{top:185px;max-width:305px;max-height:479px}}@media only screen and (max-width:1439px){.tutors-decoration--after{top:240px;max-width:190px;max-height:295px}}@media only screen and (max-width:991px){.tutors-decoration--after{top:auto;bottom:50px}}.tutors-decoration--after .v-image__image{background-position:100%}.tutors-carousel{position:relative;margin-top:96px;z-index:2}@media only screen and (max-width:1643px){.tutors-carousel{margin-top:50px}}@media only screen and (min-width:1440px){.tutors-carousel{margin-left:-48px;max-width:1262px}}@media only screen and (max-width:1439px){.tutors-carousel{width:calc(100% + 20px);padding-bottom:100px}}@media only screen and (max-width:991px){.tutors-carousel{width:calc(100% + 15px)}}.tutors-carousel .slick-slide{padding:0 37px}@media only screen and (max-width:1439px){.tutors-carousel .slick-slide{padding:0 20px 0 0}}@media only screen and (max-width:991px){.tutors-carousel .slick-slide{padding:0 15px 0 0}.tutors-carousel .slick-slide>div{display:flex;justify-content:center}}.tutors-carousel-item{display:inline-flex!important;color:#fff;background:linear-gradient(126.15deg,var(--v-green-base),var(--v-primary-base) 102.93%),#c4c4c4;border-radius:24px}@media only screen and (min-width:1440px){.tutors-carousel-item{max-width:594px}}@media only screen and (max-width:991px){.tutors-carousel-item{position:relative;flex-direction:column;max-width:640px}}.tutors-carousel-item-left{width:238px;padding:12px 32px 18px;border-right:1px solid hsla(0,0%,100%,.1)}@media only screen and (max-width:1439px){.tutors-carousel-item-left{width:215px;padding:20px 24px}}@media only screen and (max-width:991px){.tutors-carousel-item-left{width:100%;padding:20px 24px 6px;border-bottom:1px solid hsla(0,0%,100%,.1);border-right:none}.tutors-carousel-item-left>div{display:flex;justify-content:space-between}}@media only screen and (max-width:479px){.tutors-carousel-item-left{padding:20px 15px 6px}}.tutors-carousel-item-right{position:relative;width:calc(100% - 238px);padding:32px 32px 82px}@media only screen and (max-width:1439px){.tutors-carousel-item-right{padding:20px 24px 80px;width:calc(100% - 215px)}}@media only screen and (max-width:991px){.tutors-carousel-item-right{width:100%}}@media only screen and (max-width:479px){.tutors-carousel-item-right{padding:32px 15px 95px}}.tutors-carousel-item-image{position:relative;width:163px;height:163px;margin:0 auto 4px}@media only screen and (min-width:992px)and (max-width:1439px){.tutors-carousel-item-image{width:140px;height:140px}.tutors-carousel-item-image .v-avatar{width:140px!important;height:140px!important}}@media only screen and (max-width:991px){.tutors-carousel-item-image{margin:0 38px 0 0}}@media only screen and (max-width:479px){.tutors-carousel-item-image{width:90px;height:90px;margin:0 25px 0 0}.tutors-carousel-item-image .v-avatar{width:90px!important;height:90px!important}}.tutors-carousel-item-image .flags{position:absolute;bottom:5px;right:-24px}@media only screen and (max-width:479px){.tutors-carousel-item-image .flags{width:38px;bottom:0;right:-20px}}.tutors-carousel-item-image .flags-item{margin-top:2px;border-radius:8px;overflow:hidden}.tutors-carousel-item-name{font-size:24px;font-weight:700}@media only screen and (max-width:991px){.tutors-carousel-item-name{text-align:right}}@media only screen and (max-width:639px){.tutors-carousel-item-name{font-size:18px}}.tutors-carousel-item-name a{color:#fff!important;text-decoration:none}.tutors-carousel-item-rating{display:flex;align-items:center;margin-top:8px}@media only screen and (max-width:991px){.tutors-carousel-item-rating{flex-direction:column;align-items:flex-end}}.tutors-carousel-item-rating span{display:inline-block;margin-left:8px;font-size:13px;font-weight:700;line-height:.8;color:var(--v-orange-base)}@media only screen and (max-width:991px){.tutors-carousel-item-rating span{display:block;margin:10px 0 0}}.tutors-carousel-item-list{margin-top:12px;padding-left:0!important;list-style-type:none}@media only screen and (max-width:991px){.tutors-carousel-item-list{display:flex;flex-wrap:wrap;margin-top:18px;margin-left:-20px}}.tutors-carousel-item-list>li{position:relative;margin-top:2px;padding-left:19px;font-size:18px;line-height:1.55}@media only screen and (min-width:992px)and (max-width:1439px){.tutors-carousel-item-list>li{margin-top:4px;font-size:16px;line-height:1.2}}@media only screen and (max-width:991px){.tutors-carousel-item-list>li{margin:0 0 10px 24px}}.tutors-carousel-item-list>li:before{content:\"\";position:absolute;top:7px;left:0;width:11px;height:9px;background-size:contain;background-repeat:no-repeat;background-position:50%;background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ")}.tutors-carousel-item-text{font-size:18px;line-height:1.33}.tutors-carousel-item-text span{display:block;margin:25px 0 8px;font-weight:400}.tutors-carousel-item-languages{font-weight:300}.tutors-carousel-item-button{position:absolute;width:100%;left:0;bottom:20px}@media only screen and (max-width:479px){.tutors-carousel-item-button{padding:0 24px}}.tutors-carousel-item-button .v-btn{min-width:222px!important}@media only screen and (max-width:1439px){.tutors-carousel-item-button .v-btn{min-width:180px!important}}@media only screen and (max-width:479px){.tutors-carousel-item-button .v-btn{min-width:100%!important;width:100%!important}}.tutors .slick-arrow{position:absolute;top:50%;transform:translateY(-50%)}@media only screen and (max-width:1439px){.tutors .slick-arrow{top:auto;bottom:-100px;transform:translateX(-50%)}}.tutors .slick-prev{left:calc(50vw + 626px)}@media only screen and (max-width:1643px){.tutors .slick-prev{left:auto;right:-82px}}@media only screen and (max-width:1439px){.tutors .slick-prev{left:calc(50% - 65px);right:auto}}@media only screen and (max-width:991px){.tutors .slick-prev{left:calc(50% - 60px)}}.tutors .slick-next{left:calc(50vw + 706px)}@media only screen and (max-width:1643px){.tutors .slick-next{left:auto;right:-162px}}@media only screen and (max-width:1439px){.tutors .slick-next{left:calc(50% + 35px);right:auto}}@media only screen and (max-width:991px){.tutors .slick-next{left:calc(50% + 40px)}}.tutors-button{display:flex;justify-content:center;margin-top:90px}@media only screen and (max-width:1643px){.tutors-button{margin-top:80px}}@media only screen and (max-width:1439px){.tutors-button{margin-top:50px}}.tutors-button .v-btn{min-width:285px!important}@media only screen and (max-width:479px){.tutors-button .v-btn{min-width:100%!important;width:100%!important}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1352:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReviewSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1260);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReviewSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReviewSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReviewSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReviewSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1353:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(68);
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(620);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".home-page-reviews{position:relative;margin-top:157px;padding-bottom:45px}@media only screen and (max-width:1439px){.home-page-reviews{padding-bottom:110px}}@media only screen and (max-width:991px){.home-page-reviews{margin-top:130px;padding-bottom:100px}.home-page-reviews .section-bg{top:50%;height:84%;transform:translateY(-50%)}.home-page-reviews .section-bg .v-image__image{background-position:85% bottom!important}}@media only screen and (max-width:479px){.home-page-reviews{margin-top:80px}}.home-page-reviews-carousel{max-width:898px;margin-top:30px}@media only screen and (max-width:1439px){.home-page-reviews-carousel{max-width:100%;padding-left:90px}}@media only screen and (max-width:991px){.home-page-reviews-carousel{max-width:620px;margin-left:auto;margin-right:auto;padding-left:0}}@media only screen and (max-width:639px){.home-page-reviews-carousel{margin-top:15px}}@media only screen and (max-width:479px){.home-page-reviews-carousel{width:calc(100% + 15px)}}.home-page-reviews-carousel-item{position:relative;max-width:680px;padding:16px 0 38px 23px}@media only screen and (max-width:991px){.home-page-reviews-carousel-item{padding:16px 15px 0}}@media only screen and (max-width:639px){.home-page-reviews-carousel-item{max-width:100%;padding-top:25px}.home-page-reviews-carousel-item:before{content:\"\";position:absolute;top:43px;right:48px;width:40px;height:36px;background-size:cover;background-repeat:no-repeat;background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");opacity:.3}}@media only screen and (min-width:640px){.home-page-reviews-carousel-item:before{display:none}}.home-page-reviews-carousel-item-helper{position:relative;display:block;height:100%;padding:24px 36px 105px 105px;background-color:var(--v-darkLight-base);border-radius:24px;color:#fff!important;text-decoration:none}@media only screen and (max-width:991px){.home-page-reviews-carousel-item-helper{padding:95px 32px 105px;box-shadow:0 4px 24px rgba(0,0,0,.1)}.home-page-reviews-carousel-item-helper:before{content:\"\";position:absolute;top:18px;right:32px;width:40px;height:36px;background-size:cover;background-repeat:no-repeat;background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");opacity:.3}}@media only screen and (max-width:479px){.home-page-reviews-carousel-item-helper{padding:90px 15px;box-shadow:none}.home-page-reviews-carousel-item-helper:before{right:18px}}.home-page-reviews-carousel-item-info{position:absolute;left:-15px;top:-15px}.home-page-reviews-carousel-item-info .flag{position:absolute;bottom:4px;right:-16px;border-radius:6px;overflow:hidden}.home-page-reviews-carousel-item-info .rating{position:absolute;left:30px;bottom:-18px;width:60px}@media only screen and (max-width:991px){.home-page-reviews-carousel-item-info .rating{left:102px;bottom:38px;width:88px}}.home-page-reviews-carousel-item-text{min-height:80px;font-size:16px;font-weight:300;line-height:1.6}.home-page-reviews-carousel-item-bottom{position:absolute;width:100%;left:0;bottom:40px;padding:0 36px 0 105px;font-size:20px}@media only screen and (max-width:991px){.home-page-reviews-carousel-item-bottom{padding:0 32px}}@media only screen and (max-width:479px){.home-page-reviews-carousel-item-bottom{padding:0 15px}}.home-page-reviews-carousel-item-bottom-helper{position:relative;padding:0 50px 0 60px}@media only screen and (max-width:479px){.home-page-reviews-carousel-item-bottom-helper{padding:0 0 0 60px;font-size:14px}}.home-page-reviews-carousel-item-bottom-helper:before{content:\"\";position:absolute;top:-16px;right:0;width:40px;height:36px;background-size:cover;background-repeat:no-repeat;background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ");opacity:.3}@media only screen and (max-width:991px){.home-page-reviews-carousel-item-bottom-helper:before{display:none}}.home-page-reviews-carousel-item-bottom-helper .v-image{position:absolute;left:0;top:50%;transform:translateY(-50%)}.home-page-reviews .slick-slide:nth-child(odd)>div{display:flex!important;justify-content:flex-end}.home-page-reviews .slick-arrow{position:absolute;top:27%;transform:translateY(-50%)}@media only screen and (max-width:991px){.home-page-reviews .slick-arrow{transform:translateX(-50%)}}.home-page-reviews .slick-next,.home-page-reviews .slick-prev{left:-696px}@media only screen and (max-width:1643px){.home-page-reviews .slick-next,.home-page-reviews .slick-prev{left:-192px}}@media only screen and (max-width:1439px){.home-page-reviews .slick-next,.home-page-reviews .slick-prev{left:-115px}}@media only screen and (max-width:991px){.home-page-reviews .slick-next,.home-page-reviews .slick-prev{top:auto;bottom:-100px}}@media only screen and (min-width:992px){.home-page-reviews .slick-prev{transform:translateY(calc(-50% - 45px))}}@media only screen and (max-width:991px){.home-page-reviews .slick-prev{left:calc(50% - 50px)}}.home-page-reviews .slick-prev:before{top:50%;left:calc(50% - 2px);transform:translate(-50%,-50%)}@media only screen and (min-width:992px){.home-page-reviews .slick-prev:before{top:calc(50% - 2px);left:50%;transform:translate(-50%,-50%) rotate(90deg)}}@media only screen and (min-width:992px){.home-page-reviews .slick-next{transform:translateY(calc(-50% + 45px))}}@media only screen and (max-width:991px){.home-page-reviews .slick-next{left:calc(50% + 50px)}}.home-page-reviews .slick-next:before{top:50%;left:calc(50% + 2px);transform:translate(-50%,-50%) rotate(180deg)}@media only screen and (min-width:992px){.home-page-reviews .slick-next:before{top:calc(50% + 2px);left:50%;transform:translate(-50%,-50%) rotate(270deg)}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1354:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FaqSection_vue_vue_type_style_index_0_id_03687606_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1261);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FaqSection_vue_vue_type_style_index_0_id_03687606_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FaqSection_vue_vue_type_style_index_0_id_03687606_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FaqSection_vue_vue_type_style_index_0_id_03687606_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FaqSection_vue_vue_type_style_index_0_id_03687606_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1355:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".questions[data-v-03687606]{position:relative;margin:138px 0 82px}@media only screen and (max-width:991px){.questions[data-v-03687606]{margin:95px 0 82px}}.questions .section-bg[data-v-03687606]{top:72px}.questions .section-head[data-v-03687606]{margin-bottom:118px}@media only screen and (max-width:991px){.questions .section-head[data-v-03687606]{margin-bottom:70px}}@media only screen and (max-width:767px){.questions .section-head[data-v-03687606]{margin-bottom:40px}}.questions-content[data-v-03687606]{max-width:920px;margin:0 auto}@media only screen and (max-width:479px){.questions-content .v-expansion-panel-content__wrap[data-v-03687606]{padding:0 16px 20px!important}}.questions-button[data-v-03687606]{display:flex;justify-content:center;margin-top:45px}.questions-button .v-btn[data-v-03687606]{min-width:202px!important}@media only screen and (max-width:479px){.questions-button .v-btn[data-v-03687606]{min-width:100%!important;width:100%!important}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1356:
/***/ (function(module, exports, __webpack_require__) {

var map = {
	"./teacher-1.jpeg": 436,
	"./teacher-2.jpeg": 437,
	"./teacher-3.jpeg": 438,
	"./teacher-4.jpeg": 439,
	"./teacher-5.jpeg": 440
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = 1356;

/***/ }),

/***/ 1357:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ThinkingSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1262);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ThinkingSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ThinkingSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ThinkingSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ThinkingSection_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1358:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_GET_URL_IMPORT___ = __webpack_require__(68);
var ___CSS_LOADER_URL_IMPORT_0___ = __webpack_require__(144);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
var ___CSS_LOADER_URL_REPLACEMENT_0___ = ___CSS_LOADER_GET_URL_IMPORT___(___CSS_LOADER_URL_IMPORT_0___);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".thinking{position:relative;padding:90px 0 48px;overflow:hidden}@media only screen and (max-width:991px){.thinking{padding:30px 0}}.thinking:before{content:\"\";position:absolute;top:50px;right:0;width:117px;height:256px;background-size:contain;background-repeat:no-repeat;background-position:bottom;background-image:url(" + ___CSS_LOADER_URL_REPLACEMENT_0___ + ")}@media only screen and (max-width:1643px){.thinking:before{top:112px;right:0;width:95px;height:210px}}@media only screen and (max-width:1439px){.thinking:before{top:auto;left:0;bottom:100px;right:auto;width:95px;height:210px;transform:scaleX(-1)}}.thinking .section-head{margin-bottom:105px;max-width:715px}@media only screen and (max-width:1643px){.thinking .section-head{max-width:660px;margin-bottom:42px}}@media only screen and (max-width:991px){.thinking .section-head{margin-bottom:80px}}@media only screen and (max-width:639px){.thinking .section-head{margin-bottom:30px}}.thinking-content{padding-bottom:80px;font-size:24px;line-height:1.4}@media only screen and (max-width:1439px){.thinking-content{padding-bottom:100px;font-size:22px}}@media only screen and (max-width:991px){.thinking-content{padding:30px 0 0}}@media only screen and (max-width:767px){.thinking-content{font-size:20px}}.thinking-images{position:relative;height:100%}@media only screen and (min-width:1644px){.thinking-images{left:-88px}}@media only screen and (max-width:991px){.thinking-images{min-height:320px}}.thinking-images-item,.thinking-images-item-decor{position:absolute}.thinking-images-item-t1{top:-122px;left:30%}@media only screen and (max-width:1643px){.thinking-images-item-t1{top:-110px;left:40%}}@media only screen and (max-width:1439px){.thinking-images-item-t1{top:-20px;left:22%}}@media only screen and (max-width:991px){.thinking-images-item-t1{top:0;left:65px}}@media only screen and (max-width:639px){.thinking-images-item-t1{top:0;left:0}}.thinking-images-item-t1 .v-avatar{width:99px!important;height:99px!important}@media only screen and (max-width:1643px){.thinking-images-item-t1 .v-avatar{width:80px!important;height:80px!important}}@media only screen and (max-width:639px){.thinking-images-item-t1 .v-avatar{width:89px!important;height:89px!important}}.thinking-images-item-t1 .thinking-images-item-decor{left:-4px;bottom:-13px;width:137px;height:107px}@media only screen and (max-width:1643px){.thinking-images-item-t1 .thinking-images-item-decor{left:-2px;bottom:-12px;width:105px;height:86px}}@media only screen and (max-width:639px){.thinking-images-item-t1 .thinking-images-item-decor{left:-2px;bottom:-12px;width:118px;height:97px;transform:scaleX(-1)}}.thinking-images-item-t2{right:11%;top:-127px}@media only screen and (max-width:1643px){.thinking-images-item-t2{top:-124px}}@media only screen and (max-width:1439px){.thinking-images-item-t2{top:-40px;right:0}}@media only screen and (max-width:991px){.thinking-images-item-t2{top:10px;right:45px}}@media only screen and (max-width:639px){.thinking-images-item-t2{right:-20px;top:20px}}.thinking-images-item-t2 .v-avatar{width:150px!important;height:150px!important}@media only screen and (max-width:1643px){.thinking-images-item-t2 .v-avatar{width:120px!important;height:120px!important}}@media only screen and (max-width:639px){.thinking-images-item-t2 .v-avatar{width:85px!important;height:85px!important}}.thinking-images-item-t2 .thinking-images-item-decor{left:-19px;bottom:20px;width:200px;height:163px}@media only screen and (max-width:1643px){.thinking-images-item-t2 .thinking-images-item-decor{left:-15px;bottom:16px;width:160px;height:130px}}@media only screen and (max-width:639px){.thinking-images-item-t2 .thinking-images-item-decor{left:-15px;bottom:0;width:110px;height:96px}}.thinking-images-item-t3{bottom:32px;left:8%}@media only screen and (max-width:1643px){.thinking-images-item-t3{bottom:53px;left:21%}}@media only screen and (max-width:1439px){.thinking-images-item-t3{bottom:0;left:0}}@media only screen and (max-width:991px){.thinking-images-item-t3{bottom:60px;left:80px}}@media only screen and (max-width:639px){.thinking-images-item-t3{bottom:60px;left:20px}}.thinking-images-item-t3 .v-avatar{width:144px!important;height:144px!important}@media only screen and (max-width:1643px){.thinking-images-item-t3 .v-avatar{width:115px!important;height:115px!important}}@media only screen and (max-width:639px){.thinking-images-item-t3 .v-avatar{width:80px!important;height:80px!important}}.thinking-images-item-t3 .thinking-images-item-decor{left:10px;bottom:-20px;width:191px;height:156px}@media only screen and (max-width:1643px){.thinking-images-item-t3 .thinking-images-item-decor{width:153px;height:125px}}@media only screen and (max-width:639px){.thinking-images-item-t3 .thinking-images-item-decor{left:-10px;bottom:10px;width:106px;height:87px}}.thinking-images-item-t4{left:50%;bottom:56px}@media only screen and (max-width:1643px){.thinking-images-item-t4{left:57%;bottom:107px}}@media only screen and (max-width:1439px){.thinking-images-item-t4{left:48%;bottom:50px}}@media only screen and (max-width:991px){.thinking-images-item-t4{left:45%;bottom:40%}}@media only screen and (max-width:639px){.thinking-images-item-t4{left:40%;bottom:44%}}.thinking-images-item-t4 .v-avatar{width:125px!important;height:125px!important}@media only screen and (max-width:1643px){.thinking-images-item-t4 .v-avatar{width:85px!important;height:85px!important}}@media only screen and (max-width:639px){.thinking-images-item-t4 .v-avatar{width:103px!important;height:103px!important}}.thinking-images-item-t4 .thinking-images-item-decor{left:-9px;bottom:12px;width:162px;height:141px}@media only screen and (max-width:1643px){.thinking-images-item-t4 .thinking-images-item-decor{left:-4px;bottom:8px;width:110px;height:96px}}@media only screen and (max-width:639px){.thinking-images-item-t4 .thinking-images-item-decor{left:0;bottom:-14px;width:136px;height:111px}}.thinking-images-item-t5{right:3%;bottom:-13px}@media only screen and (max-width:1643px){.thinking-images-item-t5{right:2%;bottom:47px}}@media only screen and (max-width:1439px){.thinking-images-item-t5{right:0;bottom:0}}@media only screen and (max-width:991px){.thinking-images-item-t5{right:115px;bottom:80px}}@media only screen and (max-width:639px){.thinking-images-item-t5{right:25px;bottom:15px}}.thinking-images-item-t5 .v-avatar{width:111px!important;height:111px!important}@media only screen and (max-width:1643px){.thinking-images-item-t5 .v-avatar{width:89px!important;height:89px!important}}.thinking-images-item-t5 .thinking-images-item-decor{left:0;bottom:-16px;width:147px;height:120px}@media only screen and (max-width:1643px){.thinking-images-item-t5 .thinking-images-item-decor{left:0;bottom:-16px;width:118px;height:96px}}@media only screen and (max-width:639px){.thinking-images-item-t5 .thinking-images-item-decor{left:-2px}}.thinking-bottom{margin-top:60px;font-weight:600}@media only screen and (max-width:1643px){.thinking-bottom{margin-top:40px}}.thinking-button{display:flex;justify-content:center;margin-top:90px}@media only screen and (max-width:1643px){.thinking-button{margin-top:75px}}@media only screen and (max-width:767px){.thinking-button{margin-top:60px}}.thinking-button .v-btn{min-width:150px!important}@media only screen and (max-width:479px){.thinking-button .v-btn .v-btn{min-width:100%!important;width:100%!important}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1380:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1460);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("28f1127f", content, true, context)
};

/***/ }),

/***/ 1410:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/IntroSection.vue?vue&type=template&id=0a29cf87&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"intro"},[_vm._ssrNode("<div class=\"intro-bg\" data-v-0a29cf87>","</div>",[_vm._ssrNode("<svg preserveAspectRatio=\"none\" viewBox=\"0 0 1919 196\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" data-v-0a29cf87>","</svg>",[_vm._ssrNode("<g opacity=\".1\" data-v-0a29cf87><path d=\"M1919-862H0v983.943s117.939 52.907 271.192 70.772c153.254 17.865 339.247-41.614 429.776-70.772C965.497 36.741 1162.06 21.625 1250.02 28.496c186.81 14.594 270.52 58.404 417.11 93.447 155.65 37.207 251.87 0 251.87 0V-862z\" fill=\"#C4C4C4\" data-v-0a29cf87></path> <path d=\"M1919-862H0v983.943s117.939 52.907 271.192 70.772c153.254 17.865 339.247-41.614 429.776-70.772C965.497 36.741 1162.06 21.625 1250.02 28.496c186.81 14.594 270.52 58.404 417.11 93.447 155.65 37.207 251.87 0 251.87 0V-862z\" fill=\"url(#b)\" data-v-0a29cf87></path></g> <g opacity=\".2\" data-v-0a29cf87><path d=\"M1-862h1919v984.873s-117.94 52.957-271.19 70.839c-153.26 17.882-339.25-41.653-429.78-70.839C954.503 37.591 757.939 22.46 669.985 29.337c-186.819 14.609-270.526 58.46-417.116 93.536-155.644 37.242-251.869 0-251.869 0V-862z\" fill=\"#C4C4C4\" data-v-0a29cf87></path> <path d=\"M1-862h1919v984.873s-117.94 52.957-271.19 70.839c-153.26 17.882-339.25-41.653-429.78-70.839C954.503 37.591 757.939 22.46 669.985 29.337c-186.819 14.609-270.526 58.46-417.116 93.536-155.644 37.242-251.869 0-251.869 0V-862z\" fill=\"url(#c)\" data-v-0a29cf87></path></g> <path d=\"M0-862h1919V92.183s-117.94 51.307-271.19 68.632c-153.26 17.324-339.25-40.356-429.78-68.632C953.503 9.558 756.939-5.101 668.985 1.563 482.166 15.714 398.459 58.2 251.869 92.182 96.225 128.264 0 92.183 0 92.183V-862z\" fill=\"#2D2D2D\" data-v-0a29cf87></path> <path d=\"M0-862h1919V92.183s-117.94 51.307-271.19 68.632c-153.26 17.324-339.25-40.356-429.78-68.632C953.503 9.558 756.939-5.101 668.985 1.563 482.166 15.714 398.459 58.2 251.869 92.182 96.225 128.264 0 92.183 0 92.183V-862z\" fill=\"#2D2D2D\" data-v-0a29cf87></path> "),_vm._ssrNode("<defs data-v-0a29cf87>","</defs>",[_c('linearGradient',{attrs:{"id":"b","x1":"1919","y1":"-862","x2":"678.457","y2":"781.577","gradientUnits":"userSpaceOnUse"}},[_c('stop',{attrs:{"stop-color":"#80B622"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"1","stop-color":"#3C87F8"}})],1),_vm._ssrNode(" "),_c('linearGradient',{attrs:{"id":"c","x1":"1","y1":"-862","x2":"1243.04","y2":"782.002","gradientUnits":"userSpaceOnUse"}},[_c('stop',{attrs:{"stop-color":"#80B622"}}),_vm._v(" "),_c('stop',{attrs:{"offset":"1","stop-color":"#3C87F8"}})],1)],2)],2)]),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"intro-wrap\" data-v-0a29cf87>","</div>",[_c('v-container',{staticClass:"pa-0"},[_c('v-row',[_c('v-col',{staticClass:"col-xl-10 offset-xl-1 d-flex align-center justify-space-between"},[_c('div',{staticClass:"intro-content"},[_c('h1',{staticClass:"text--gradient font-weight-bold"},[_vm._v("\n              "+_vm._s(_vm.$t('home_page.banner_title'))+"\n            ")]),_vm._v(" "),_c('h2',{staticClass:"font-weight-light"},[_vm._v("\n              "+_vm._s(_vm.$t('home_page.banner_subtitle'))+"\n            ")]),_vm._v(" "),(_vm.languageItems && _vm.languageItems.length)?_c('div',{staticClass:"intro-content-bottom d-none d-md-flex"},[_c('select-language',{attrs:{"items":_vm.languageItems}})],1):_vm._e()]),_vm._v(" "),_c('div',{staticClass:"intro-img"},[_c('IntroImage')],1)]),_vm._v(" "),(_vm.languageItems && _vm.languageItems.length)?_c('v-col',{staticClass:"col-12 d-md-none"},[_c('div',{staticClass:"intro-content-bottom"},[_c('select-language',{attrs:{"items":_vm.languageItems}})],1)]):_vm._e()],1)],1)],1)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/IntroSection.vue?vue&type=template&id=0a29cf87&scoped=true&

// EXTERNAL MODULE: ./components/images/HomePageIntroImage.vue
var HomePageIntroImage = __webpack_require__(1268);

// EXTERNAL MODULE: ./components/homepage/SelectLanguage.vue + 4 modules
var SelectLanguage = __webpack_require__(1277);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/IntroSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var IntroSectionvue_type_script_lang_js_ = ({
  name: 'IntroSection',
  components: {
    SelectLanguage: SelectLanguage["default"],
    IntroImage: HomePageIntroImage["default"]
  },
  props: {
    languageItems: {
      type: Array,
      required: true
    }
  }
});
// CONCATENATED MODULE: ./components/homepage/IntroSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_IntroSectionvue_type_script_lang_js_ = (IntroSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/IntroSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1340)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_IntroSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "0a29cf87",
  "00f04e42"
  
)

/* harmony default export */ var IntroSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */




installComponents_default()(component, {VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1411:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/StatSection.vue?vue&type=template&id=92e11f48&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"stat"},[_c('v-container',[_c('v-row',[_c('v-col',{staticClass:"col-xl-10 offset-xl-1"},[_c('v-row',_vm._l((_vm.items),function(item,idx){return _c('v-col',{key:idx,class:("col-12 col-sm-4 stat-item stat-item-i" + (idx + 1))},[_c('div',{staticClass:"stat-item-wrap"},[_c('div',{staticClass:"stat-item-value text--gradient"},[_vm._v("\n                "+_vm._s(item.value)+"\n              ")]),_vm._v(" "),_c('div',{staticClass:"stat-item-text",domProps:{"innerHTML":_vm._s(_vm.$t(item.text))}})])])}),1)],1)],1)],1)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/StatSection.vue?vue&type=template&id=92e11f48&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/StatSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var StatSectionvue_type_script_lang_js_ = ({
  name: 'StatSection',

  data() {
    return {
      items: [{
        value: '98%',
        text: 'home_page.stat_1'
      }, {
        value: this.$t('n_15000') + '+',
        text: 'home_page.stat_2'
      }, {
        value: '250+',
        text: 'home_page.stat_3'
      }]
    };
  }

});
// CONCATENATED MODULE: ./components/homepage/StatSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_StatSectionvue_type_script_lang_js_ = (StatSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/StatSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1342)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_StatSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "d1266b4a"
  
)

/* harmony default export */ var StatSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */




installComponents_default()(component, {VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1412:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/HowWorksSection.vue?vue&type=template&id=7dd35c7a&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"how-works"},[_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{staticClass:"section-head-title",staticStyle:{"color":"#262626","-webkit-text-fill-color":"#262626"}},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.details_section_title'))+"\n          ")])])])],1)],1),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"how-works-content\">","</div>",[_vm._ssrNode("<div class=\"section-bg\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(434),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('v-container',{staticClass:"py-0",attrs:{"fill-height":""}},[_c('v-row',[_c('v-col',{staticClass:"col-xl-10 offset-xl-1 py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-4 offset-md-4 py-0"},[_c('div',{staticClass:"how-works-item how-works-item-i1"},[_c('div',{staticClass:"how-works-item-bg d-none d-md-block"},[_c('v-img',{attrs:{"src":__webpack_require__(130),"options":{ rootMargin: '50%' },"contain":""}})],1),_vm._v(" "),_c('div',{staticClass:"how-works-item-bg d-md-none"},[_c('v-img',{attrs:{"src":__webpack_require__(129),"options":{ rootMargin: '50%' },"contain":""}})],1),_vm._v(" "),_c('div',{staticClass:"how-works-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.details_1_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"how-works-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.details_1_text'))+"\n                ")])])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-4 py-0"},[_c('div',{staticClass:"how-works-item how-works-item-i2"},[_c('div',{staticClass:"how-works-item-bg d-none d-md-block"},[_c('v-img',{attrs:{"src":__webpack_require__(132),"options":{ rootMargin: '50%' },"contain":""}})],1),_vm._v(" "),_c('div',{staticClass:"how-works-item-bg d-md-none"},[_c('v-img',{attrs:{"src":__webpack_require__(131),"options":{ rootMargin: '50%' },"contain":""}})],1),_vm._v(" "),_c('div',{staticClass:"how-works-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.details_2_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"how-works-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.details_2_text'))+"\n                ")])])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-4 offset-md-4 py-0"},[_c('div',{staticClass:"how-works-item how-works-item-i3"},[_c('div',{staticClass:"how-works-item-bg d-none d-md-block"},[_c('v-img',{attrs:{"src":__webpack_require__(134),"options":{ rootMargin: '50%' },"contain":""}})],1),_vm._v(" "),_c('div',{staticClass:"how-works-item-bg d-md-none"},[_c('v-img',{attrs:{"src":__webpack_require__(133),"options":{ rootMargin: '50%' },"contain":""}})],1),_vm._v(" "),_c('div',{staticClass:"how-works-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.details_3_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"how-works-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.details_3_text'))+"\n                ")])])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-4 offset-md-4 py-0"},[_c('div',{staticClass:"how-works-item how-works-item-i4"},[_c('div',{staticClass:"how-works-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.details_4_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"how-works-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.details_4_text'))+"\n                ")])])])],1)],1)],1)],1)],2)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/HowWorksSection.vue?vue&type=template&id=7dd35c7a&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/HowWorksSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var HowWorksSectionvue_type_script_lang_js_ = ({
  name: 'HowWorksSection'
});
// CONCATENATED MODULE: ./components/homepage/HowWorksSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_HowWorksSectionvue_type_script_lang_js_ = (HowWorksSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/HowWorksSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1344)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_HowWorksSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "eb63e846"
  
)

/* harmony default export */ var HowWorksSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */





installComponents_default()(component, {VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1413:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/AboutSection.vue?vue&type=template&id=9e893b70&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"about"},[_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{staticClass:"section-head-title",staticStyle:{"color":"#262626","-webkit-text-fill-color":"#262626"}},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.about_section_title'))+"\n          ")])])])],1)],1),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"about-content\">","</div>",[_c('v-container',{staticClass:"py-0"},[_c('v-row',{attrs:{"no-gutters":""}},[_c('v-col',{staticClass:"col-xl-10 offset-xl-1 about-wrap"},[_c('div',{staticClass:"about-bg d-none d-md-block"},[_c('v-img',{attrs:{"src":__webpack_require__(433),"options":{ rootMargin: '50%' },"contain":"","height":"100%"}})],1),_vm._v(" "),_c('div',{staticClass:"about-bg d-md-none"},[_c('v-img',{attrs:{"src":__webpack_require__(128),"options":{ rootMargin: '50%' },"contain":"","height":"100%"}})],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-4 py-0"},[_c('div',{staticClass:"about-item about-item-i1"},[_c('div',{staticClass:"about-item-image"},[_c('v-img',{attrs:{"src":__webpack_require__(122),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"about-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_1_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_1_text'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-more"},[_c('nuxt-link',{staticClass:"link-more",attrs:{"to":"/teacher-listing/1/motivation,2;speciality,22"}},[_vm._v("\n                    "+_vm._s(_vm.$t('learn_more'))+"\n                  ")])],1)])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-4 offset-md-4 py-0"},[_c('div',{staticClass:"about-item about-item-i2"},[_c('div',{staticClass:"about-item-image"},[_c('v-img',{attrs:{"src":__webpack_require__(123),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"about-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_2_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_2_text'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-more"},[_c('nuxt-link',{staticClass:"link-more",attrs:{"to":"/education"}},[_vm._v("\n                    "+_vm._s(_vm.$t('learn_more'))+"\n                  ")])],1)])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"order-md-2 col-12 col-md-4 offset-md-8 py-0"},[_c('div',{staticClass:"about-item about-item-i3"},[_c('div',{staticClass:"about-item-image"},[_c('v-img',{attrs:{"src":__webpack_require__(124),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"about-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_3_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_3_text'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-more"},[_c('nuxt-link',{staticClass:"link-more",attrs:{"to":"/teacher-listing/1/motivation,3"}},[_vm._v("\n                    "+_vm._s(_vm.$t('learn_more'))+"\n                  ")])],1)])]),_vm._v(" "),_c('v-col',{staticClass:"order-md-1 col-12 col-md-4 offset-md-1 offset-lg-4 py-0"},[_c('div',{staticClass:"about-item about-item-i4"},[_c('div',{staticClass:"about-item-image"},[_c('v-img',{attrs:{"src":__webpack_require__(125),"contain":"","options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"about-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_4_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_4_text'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-more"},[_c('nuxt-link',{staticClass:"link-more",attrs:{"to":"/teacher-listing/1/motivation,1"}},[_vm._v("\n                    "+_vm._s(_vm.$t('learn_more'))+"\n                  ")])],1)])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"about-item about-item-i5"},[_c('div',{staticClass:"about-item-image"},[_c('v-img',{staticClass:"type-1",attrs:{"src":__webpack_require__(127),"contain":"","options":{ rootMargin: '50%' }}}),_vm._v(" "),_c('v-img',{staticClass:"type-2",attrs:{"src":__webpack_require__(126),"contain":"","max-width":"371","options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"about-item-title"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_5_title'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-text"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.about_5_text'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"about-item-more"},[_c('nuxt-link',{staticClass:"link-more",attrs:{"to":"/business"}},[_vm._v("\n                    "+_vm._s(_vm.$t('learn_more'))+"\n                  ")])],1)])])],1)],1)],1)],1)],1)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/AboutSection.vue?vue&type=template&id=9e893b70&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/AboutSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var AboutSectionvue_type_script_lang_js_ = ({
  name: 'AboutSection'
});
// CONCATENATED MODULE: ./components/homepage/AboutSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_AboutSectionvue_type_script_lang_js_ = (AboutSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/AboutSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1346)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_AboutSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "455c4d1e"
  
)

/* harmony default export */ var AboutSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */





installComponents_default()(component, {VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1414:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/LanguagesSection.vue?vue&type=template&id=19955d52&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"languages"},[_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{staticClass:"section-head-title",staticStyle:{"color":"#262626","-webkit-text-fill-color":"#262626"}},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.languages_section_title'))+"\n          ")])])])],1)],1),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-content\">","</div>",[_vm._ssrNode("<div class=\"languages-content-image\">","</div>",[_c('v-img',{staticClass:"d-none d-sm-block",attrs:{"src":__webpack_require__(140),"contain":"","options":{ rootMargin: '50%' }}}),_vm._ssrNode(" "),_c('v-img',{staticClass:"d-sm-none",attrs:{"src":__webpack_require__(139),"contain":"","options":{ rootMargin: '50%' }}}),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-lines-mobile d-sm-none\">","</div>",[_vm._ssrNode("<div class=\"languages-line languages-line-r1\">","</div>",[_vm._ssrNode("<div class=\"languages-country languages-country-pl\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(113),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,32"}},[_vm._v("\n              "+_vm._s(_vm.$t('polish'))+"\n            ")])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-uk\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag languages-country-flag--double\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(118),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,12"}},[_c('span',{domProps:{"innerHTML":_vm._s(_vm.$t('uk_usa_english'))}})])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-fr\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(110),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,16"}},[_vm._v("\n              "+_vm._s(_vm.$t('french'))+"\n            ")])],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-line languages-line-r2\">","</div>",[_vm._ssrNode("<div class=\"languages-country languages-country-it\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(111),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,23"}},[_vm._v("\n              "+_vm._s(_vm.$t('italian'))+"\n            ")])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-ar\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(106),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,2"}},[_vm._v("\n              "+_vm._s(_vm.$t('arabic'))+"\n            ")])],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-line languages-line-r3\">","</div>",[_vm._ssrNode("<div class=\"languages-country languages-country-ge\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(108),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,17"}},[_vm._v("\n              "+_vm._s(_vm.$t('germany'))+"\n            ")])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-pr-br\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag languages-country-flag--double\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(114),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,34"}},[_c('span',{domProps:{"innerHTML":_vm._s(_vm.$t('portuguese_brazilian'))}})])],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-line languages-line-r4\">","</div>",[_vm._ssrNode("<div class=\"languages-country languages-country-ru\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(115),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,38"}},[_vm._v("\n              "+_vm._s(_vm.$t('russian'))+"\n            ")])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-jp\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(112),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,24"}},[_vm._v("\n              "+_vm._s(_vm.$t('japanese'))+"\n            ")])],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-line languages-line-r5\">","</div>",[_vm._ssrNode("<div class=\"languages-country languages-country-sp\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(116),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,44"}},[_vm._v("\n              "+_vm._s(_vm.$t('spanish'))+"\n            ")])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-ch\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(107),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,7"}},[_vm._v("\n              "+_vm._s(_vm.$t('chinese'))+"\n            ")])],2)],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-line languages-line-r6\">","</div>",[_vm._ssrNode("<div class=\"languages-country languages-country-du\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(109),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,11"}},[_vm._v("\n              "+_vm._s(_vm.$t('dutch'))+"\n            ")])],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"languages-country languages-country-sw\">","</div>",[_vm._ssrNode("<div class=\"languages-country-flag\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(117),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,45"}},[_vm._v("\n              "+_vm._s(_vm.$t('swedish'))+"\n            ")])],2)],2)],2)],2),_vm._ssrNode(" "),_c('v-container',{staticClass:"py-0 d-none d-sm-block"},[_c('v-row',[_c('v-col',{staticClass:"col-xl-10 offset-xl-1 py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"languages-line languages-line-r1"},[_c('div',{staticClass:"languages-country languages-country-pl"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(113),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,32"}},[_vm._v("\n                    "+_vm._s(_vm.$t('polish'))+"\n                  ")])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-uk"},[_c('div',{staticClass:"languages-country-flag languages-country-flag--double"},[_c('v-img',{attrs:{"src":__webpack_require__(118),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,12"}},[_c('span',{domProps:{"innerHTML":_vm._s(_vm.$t('uk_usa_english'))}})])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-fr"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(110),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,16"}},[_vm._v("\n                    "+_vm._s(_vm.$t('french'))+"\n                  ")])],1)]),_vm._v(" "),_c('div',{staticClass:"languages-line languages-line-r2"},[_c('div',{staticClass:"languages-country languages-country-it"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(111),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,23"}},[_vm._v("\n                    "+_vm._s(_vm.$t('italian'))+"\n                  ")])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-ar"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(106),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,2"}},[_vm._v("\n                    "+_vm._s(_vm.$t('arabic'))+"\n                  ")])],1)]),_vm._v(" "),_c('div',{staticClass:"languages-line languages-line-r3"},[_c('div',{staticClass:"languages-country languages-country-ge"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(108),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,17"}},[_vm._v("\n                    "+_vm._s(_vm.$t('germany'))+"\n                  ")])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-pr-br"},[_c('div',{staticClass:"languages-country-flag languages-country-flag--double"},[_c('v-img',{attrs:{"src":__webpack_require__(114),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,34"}},[_c('span',{domProps:{"innerHTML":_vm._s(_vm.$t('portuguese_brazilian'))}})])],1)]),_vm._v(" "),_c('div',{staticClass:"languages-line languages-line-r4"},[_c('div',{staticClass:"languages-country languages-country-ru"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(115),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,38"}},[_vm._v("\n                    "+_vm._s(_vm.$t('russian'))+"\n                  ")])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-jp"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(112),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,24"}},[_vm._v("\n                    "+_vm._s(_vm.$t('japanese'))+"\n                  ")])],1)]),_vm._v(" "),_c('div',{staticClass:"languages-line languages-line-r5"},[_c('div',{staticClass:"languages-country languages-country-sp"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(116),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,44"}},[_vm._v("\n                    "+_vm._s(_vm.$t('spanish'))+"\n                  ")])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-ch"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(107),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,7"}},[_vm._v("\n                    "+_vm._s(_vm.$t('chinese'))+"\n                  ")])],1)]),_vm._v(" "),_c('div',{staticClass:"languages-line languages-line-r6"},[_c('div',{staticClass:"languages-country languages-country-du"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(109),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,11"}},[_vm._v("\n                    "+_vm._s(_vm.$t('dutch'))+"\n                  ")])],1),_vm._v(" "),_c('div',{staticClass:"languages-country languages-country-sw"},[_c('div',{staticClass:"languages-country-flag"},[_c('v-img',{attrs:{"src":__webpack_require__(117),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('nuxt-link',{attrs:{"to":"/teacher-listing/language,45"}},[_vm._v("\n                    "+_vm._s(_vm.$t('swedish'))+"\n                  ")])],1)])])],1)],1)],1)],1)],2)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/LanguagesSection.vue?vue&type=template&id=19955d52&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/LanguagesSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var LanguagesSectionvue_type_script_lang_js_ = ({
  name: 'LanguagesSection'
});
// CONCATENATED MODULE: ./components/homepage/LanguagesSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_LanguagesSectionvue_type_script_lang_js_ = (LanguagesSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/LanguagesSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1348)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_LanguagesSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "6831a660"
  
)

/* harmony default export */ var LanguagesSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */





installComponents_default()(component, {VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1415:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/TutorsSection.vue?vue&type=template&id=0135b7d4&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"tutors"},[_vm._ssrNode("<div class=\"tutors-decoration--before\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(135),"contain":"","options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{staticClass:"section-head-title",staticStyle:{"color":"#262626","-webkit-text-fill-color":"#262626"}},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.tutors_section_title'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"section-head-subtitle text--gradient"},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.tutors_section_subtitle'))+"\n          ")])])])],1)],1),_vm._ssrNode(" "),_c('v-container',{staticClass:"py-0",attrs:{"fluid":""}},[_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"tutors-carousel"},[_c('client-only',[_c('VueSlickCarousel',_vm._b({},'VueSlickCarousel',_vm.tutorsCarouselSettings,false),_vm._l((_vm.teachers),function(t,idx){return _c('div',{key:idx,ref:"tutorsCarouselItem",refInFor:true,staticClass:"tutors-carousel-item"},[_c('div',{staticClass:"tutors-carousel-item-left"},[_c('div',[_c('nuxt-link',{attrs:{"to":t.profileLink}},[_c('div',{staticClass:"tutors-carousel-item-image"},[_c('v-avatar',{attrs:{"width":"163","height":"163"}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(
                                t.avatarsResized,
                                'user_thumb_163x163'
                              ),"srcset":_vm.getSrcSetAvatar(
                                t.avatarsResized,
                                'user_thumb_163x163',
                                'user_thumb_326x326'
                              ),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),(t.languagesTaught.length)?_c('div',{staticClass:"flags"},_vm._l((t.languagesTaught),function(languageTaught){return _c('div',{key:languageTaught.isoCode,staticClass:"flags-item"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (languageTaught.isoCode) + ".svg"),"width":"52","height":"36","contain":"","options":{ rootMargin: '50%' }}})],1)}),0):_vm._e()],1)]),_vm._v(" "),_c('div',[_c('div',{staticClass:"tutors-carousel-item-name text-uppercase"},[_c('nuxt-link',{attrs:{"to":t.profileLink}},[_vm._v("\n                          "+_vm._s(t.firstName)+"\n                        ")])],1),_vm._v(" "),_c('div',{staticClass:"tutors-carousel-item-rating"},[_c('div',[_c('v-img',{attrs:{"src":__webpack_require__(119),"width":"68","contain":"","options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('span',[_vm._v("("+_vm._s(_vm.$tc('review', t.countFeedbacks))+")")])])])],1),_vm._v(" "),(t.languagesTaught.length)?_c('ul',{staticClass:"tutors-carousel-item-list"},_vm._l((t.languagesTaught),function(lt,index){return _c('li',{key:index},[_vm._v("\n                      "+_vm._s(lt.name)+" "+_vm._s(_vm.$t('teacher'))+"\n                    ")])}),0):_vm._e()]),_vm._v(" "),_c('div',{staticClass:"tutors-carousel-item-right"},[_c('div',{staticClass:"tutors-carousel-item-text"},[_vm._v("\n                    “"+_vm._s(t.description)+"”\n                    "),(t.specialities && t.specialities.length)?[_c('span',[_vm._v(_vm._s(_vm.$t('top_specialities'))+":")]),_vm._v(" "),_c('div',{staticClass:"tutors-carousel-item-languages"},[_vm._v("\n                        "+_vm._s(_vm._f("specialitiesStr")(t.specialities))+"\n                      ")])]:_vm._e()],2),_vm._v(" "),(_vm.$vuetify.breakpoint.mdAndUp)?_c('div',{staticClass:"tutors-carousel-item-button text-center d-none d-md-block"},[_c('v-btn',{attrs:{"to":t.profileLink,"large":"","color":"greyDark"}},[(t.bookLesson.freeTrial)?[_vm._v("\n                        "+_vm._s(_vm.$t('free_trial'))+"\n                      ")]:[_vm._v("\n                        "+_vm._s(_vm.$t('trial'))+":  "+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.getPrice(t.bookLesson.price))+"\n                      ")]],2)],1):_vm._e()]),_vm._v(" "),(_vm.$vuetify.breakpoint.smAndDown)?_c('div',{staticClass:"tutors-carousel-item-button text-center d-md-none"},[_c('v-btn',{attrs:{"to":t.profileLink,"large":"","color":"greyDark"}},[(t.bookLesson.freeTrial)?[_vm._v("\n                      "+_vm._s(_vm.$t('free_trial'))+"\n                    ")]:[_vm._v("\n                      "+_vm._s(_vm.$t('trial'))+":  "+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.getPrice(t.bookLesson.price))+"\n                    ")]],2)],1):_vm._e()])}),0)],1)],1)])],1)],1),_vm._ssrNode(" "),_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"tutors-button"},[_c('v-btn',{attrs:{"to":_vm.localePath({
                name: 'teacher-listing',
                params: {
                  utm_source: 'homepage',
                  utm_medium: 'teacher-cards',
                },
              }),"large":"","color":"primary"}},[_vm._v("\n            "+_vm._s(_vm.$t('meet_all_our_teachers'))+"\n          ")])],1)])],1)],1),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"tutors-decoration--after\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(136),"contain":"","options":{ rootMargin: '50%' }}})],1)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/TutorsSection.vue?vue&type=template&id=0135b7d4&

// EXTERNAL MODULE: external "vue-slick-carousel"
var external_vue_slick_carousel_ = __webpack_require__(859);
var external_vue_slick_carousel_default = /*#__PURE__*/__webpack_require__.n(external_vue_slick_carousel_);

// EXTERNAL MODULE: ./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css
var vue_slick_carousel = __webpack_require__(1071);

// EXTERNAL MODULE: ./mixins/Avatars.vue + 2 modules
var Avatars = __webpack_require__(932);

// EXTERNAL MODULE: ./helpers/index.js
var helpers = __webpack_require__(501);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/TutorsSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var TutorsSectionvue_type_script_lang_js_ = ({
  name: 'TutorsSection',
  components: {
    VueSlickCarousel: external_vue_slick_carousel_default.a
  },
  filters: {
    specialitiesStr(arr) {
      let str = '';

      for (let i = 0; i < 3; i++) {
        var _arr$i$speciality$nam, _arr$i, _arr$i$speciality;

        str += (_arr$i$speciality$nam = (_arr$i = arr[i]) === null || _arr$i === void 0 ? void 0 : (_arr$i$speciality = _arr$i.speciality) === null || _arr$i$speciality === void 0 ? void 0 : _arr$i$speciality.name) !== null && _arr$i$speciality$nam !== void 0 ? _arr$i$speciality$nam : '';

        if (i < 3 - 1) {
          str += ', ';
        }
      }

      return str;
    }

  },
  mixins: [Avatars["a" /* default */]],
  props: {
    teachers: {
      type: Array,
      required: true
    }
  },

  data() {
    return {
      getPrice: helpers["getPrice"],
      tutorsCarouselSettings: {
        dots: false,
        focusOnSelect: true,
        infinite: true,
        speed: 800,
        slidesToShow: 2,
        slidesToScroll: 1,
        responsive: [{
          breakpoint: 992,
          settings: {
            slidesToShow: 1
          }
        }]
      }
    };
  },

  computed: {
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    }

  }
});
// CONCATENATED MODULE: ./components/homepage/TutorsSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_TutorsSectionvue_type_script_lang_js_ = (TutorsSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/VAvatar.js
var VAvatar = __webpack_require__(830);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/TutorsSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1350)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_TutorsSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "f3947edc"
  
)

/* harmony default export */ var TutorsSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */







installComponents_default()(component, {VAvatar: VAvatar["a" /* default */],VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1416:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/ReviewSection.vue?vue&type=template&id=7074f6fe&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"home-page-reviews"},[_vm._ssrNode("<div class=\"section-bg\">","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(435),"position":"center bottom","options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('v-container',[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{staticClass:"section-head-title",staticStyle:{"color":"#262626","-webkit-text-fill-color":"#262626"}},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.reviews_section_title'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"section-head-subtitle",staticStyle:{"max-width":"900px"},domProps:{"innerHTML":_vm._s(_vm.$t('home_page.reviews_section_subtitle'))}})])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 py-0 d-md-flex justify-end"},[_c('div',{staticClass:"home-page-reviews-carousel"},[_c('client-only',[_c('VueSlickCarousel',_vm._b({},'VueSlickCarousel',_vm.reviewsCarouselSettings,false),_vm._l((_vm.reviews),function(r,idx){return _c('div',{key:idx,staticClass:"home-page-reviews-carousel-item"},[_c('nuxt-link',{staticClass:"home-page-reviews-carousel-item-helper",attrs:{"to":r.teacherProfileUrl}},[_c('div',{staticClass:"home-page-reviews-carousel-item-info"},[_c('v-avatar',{attrs:{"width":"87","height":"87"}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(r.avatars, 'user_thumb_87x87'),"srcset":_vm.getSrcSetAvatar(
                            r.avatars,
                            'user_thumb_87x87',
                            'user_thumb_174x174'
                          ),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"flag"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (r.language.isoCode) + ".svg"),"width":"36","contain":"","options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"rating"},[_c('v-img',{attrs:{"src":__webpack_require__(119),"width":"100%","contain":"","options":{ rootMargin: '50%' }}})],1)],1),_vm._v(" "),_c('div',{staticClass:"home-page-reviews-carousel-item-text"},[_vm._v("\n                    “"+_vm._s(_vm._f("reviewText")(r.description))+"”\n                  ")]),_vm._v(" "),_c('div',{staticClass:"home-page-reviews-carousel-item-bottom"},[_c('div',{staticClass:"home-page-reviews-carousel-item-bottom-helper"},[_c('v-img',{attrs:{"src":__webpack_require__(120),"width":"45","options":{ rootMargin: '50%' }}}),_vm._v("\n                      "+_vm._s(r.studentFirstName)+",\n                      "+_vm._s(_vm.$tc('review_lessons_count', r.countFinishedLesson, {
                          language: r.language.name,
                        }))+"\n                    ")],1)])])],1)}),0)],1)],1)])],1)],1)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/ReviewSection.vue?vue&type=template&id=7074f6fe&

// EXTERNAL MODULE: external "vue-slick-carousel"
var external_vue_slick_carousel_ = __webpack_require__(859);
var external_vue_slick_carousel_default = /*#__PURE__*/__webpack_require__.n(external_vue_slick_carousel_);

// EXTERNAL MODULE: ./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css
var vue_slick_carousel = __webpack_require__(1071);

// EXTERNAL MODULE: ./mixins/Avatars.vue + 2 modules
var Avatars = __webpack_require__(932);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/ReviewSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



/* harmony default export */ var ReviewSectionvue_type_script_lang_js_ = ({
  name: 'ReviewSection',
  components: {
    VueSlickCarousel: external_vue_slick_carousel_default.a
  },
  filters: {
    reviewText(val) {
      let str = val;

      if (str.length > 322) {
        const arr = str.substring(0, 322).split(' ');
        arr.pop();
        str = arr.join(' ') + ' ...';
      }

      return str;
    }

  },
  mixins: [Avatars["a" /* default */]],
  props: {
    reviews: {
      type: Array,
      required: true
    }
  },

  data() {
    return {
      reviewsCarouselSettings: {
        dots: false,
        focusOnSelect: true,
        infinite: true,
        slidesToShow: 3,
        slidesToScroll: 1,
        vertical: true,
        verticalSwiping: true,
        speed: 800,
        responsive: [{
          breakpoint: 992,
          settings: {
            vertical: false,
            verticalSwiping: false,
            slidesToShow: 1
          }
        }]
      }
    };
  }

});
// CONCATENATED MODULE: ./components/homepage/ReviewSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_ReviewSectionvue_type_script_lang_js_ = (ReviewSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/VAvatar.js
var VAvatar = __webpack_require__(830);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/ReviewSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1352)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_ReviewSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "36678257"
  
)

/* harmony default export */ var ReviewSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */






installComponents_default()(component, {VAvatar: VAvatar["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1417:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/FaqSection.vue?vue&type=template&id=03687606&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.items.length)?_c('section',{ref:"questions",staticClass:"questions"},[(_vm.backgroundImage)?_vm._ssrNode("<div class=\"section-bg\" data-v-03687606>","</div>",[_c('v-img',{attrs:{"src":__webpack_require__(429),"position":"center top","options":{ rootMargin: '50%' }}})],1):_vm._e(),_vm._ssrNode(" "),_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{class:['section-head-title', _vm.titleClass],staticStyle:{"color":"#262626","-webkit-text-fill-color":"#262626"}},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.questions_section_title'))+"\n          ")])])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"questions-content"},[_c('l-expansion-panels',{attrs:{"items":_vm.items,"flat":""}}),_vm._v(" "),_c('div',{staticClass:"questions-button"},[_c('v-btn',{attrs:{"to":"/faq","large":"","color":"primary"}},[_vm._v("\n              "+_vm._s(_vm.$t('see_our_full_faqs'))+"\n            ")])],1)],1)])],1)],1)],2):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/FaqSection.vue?vue&type=template&id=03687606&scoped=true&

// EXTERNAL MODULE: ./components/LExpansionPanels.vue + 4 modules
var LExpansionPanels = __webpack_require__(1000);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/FaqSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var FaqSectionvue_type_script_lang_js_ = ({
  name: 'FaqSection',
  components: {
    LExpansionPanels: LExpansionPanels["default"]
  },
  props: {
    items: {
      type: Array,
      required: true
    },
    backgroundImage: {
      type: Boolean,
      default: false
    },
    titleClass: {
      type: String,
      default: null
    }
  },

  mounted() {// this.$nextTick(() => {
    //   if (this.items.length && this.backgroundImage) {
    //     const questionsSectionHeight = this.$refs.questions.offsetHeight
    //
    //     this.$refs.questionsSectionBg.style.height = `${Math.min(
    //       questionsSectionHeight,
    //       896
    //     )}px`
    //   }
    // })
  }

});
// CONCATENATED MODULE: ./components/homepage/FaqSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_FaqSectionvue_type_script_lang_js_ = (FaqSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/FaqSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1354)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_FaqSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "03687606",
  "23dd2975"
  
)

/* harmony default export */ var FaqSection = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LExpansionPanels: __webpack_require__(1000).default})


/* vuetify-loader */






installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1418:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/ThinkingSection.vue?vue&type=template&id=56a571c6&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:"thinking"},[_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{staticClass:"section-head-title",staticStyle:{"color":"#262626","-webkit-text-fill-color":"#262626"}},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.thinking_section_title'))+"\n          ")])])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-7 order-md-2 py-0"},[_c('div',{staticClass:"thinking-images"},_vm._l((5),function(i){return _c('div',{key:i,class:[("thinking-images-item thinking-images-item-t" + i)]},[_c('div',{staticClass:"thinking-images-item-decor"},[_c('v-img',{attrs:{"src":__webpack_require__(137),"options":{ rootMargin: '50%' },"contain":""}})],1),_vm._v(" "),_c('v-avatar',[_c('v-img',{attrs:{"src":__webpack_require__(1356)("./teacher-" + i + ".jpeg"),"options":{ rootMargin: '50%' }}})],1)],1)}),0)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-5 order-md-1 py-0"},[_c('div',{staticClass:"thinking-content"},[_c('div',{staticClass:"thinking-text"},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.thinking_text'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"thinking-bottom"},[_vm._v("\n            "+_vm._s(_vm.$t('home_page.thinking_highline'))+"\n          ")])])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"thinking-button"},[_c('v-btn',{attrs:{"href":"https://heylangu-teachers.com","target":"_blank","large":"","color":"primary"}},[_vm._v(_vm._s(_vm.$t('apply_now')))])],1)])],1)],1)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/homepage/ThinkingSection.vue?vue&type=template&id=56a571c6&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/homepage/ThinkingSection.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var ThinkingSectionvue_type_script_lang_js_ = ({
  name: 'ThinkingSection'
});
// CONCATENATED MODULE: ./components/homepage/ThinkingSection.vue?vue&type=script&lang=js&
 /* harmony default export */ var homepage_ThinkingSectionvue_type_script_lang_js_ = (ThinkingSectionvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/VAvatar.js
var VAvatar = __webpack_require__(830);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/homepage/ThinkingSection.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1357)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  homepage_ThinkingSectionvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "7fcc5f02"
  
)

/* harmony default export */ var ThinkingSection = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */







installComponents_default()(component, {VAvatar: VAvatar["a" /* default */],VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1459:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1380);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1460:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".features{padding-top:85px}@media only screen and (max-width:991px){.features{padding-top:60px}}@media only screen and (max-width:767px){.features-button{text-align:center}.features-button .v-btn{min-width:285px!important}}.features-item{margin-bottom:38px}@media only screen and (max-width:639px){.features-item{margin-bottom:15px}}.features-item-wrap{position:relative;padding-left:140px}@media only screen and (max-width:1643px){.features-item-wrap{padding-left:130px}}@media only screen and (max-width:1439px){.features-item-wrap{padding-left:100px}}@media only screen and (max-width:767px){.features-item-wrap{padding-left:120px}}@media only screen and (max-width:639px){.features-item-wrap{max-width:355px;margin-left:auto;margin-right:auto;padding-left:0;padding-top:86px;text-align:center}}.features-item-image{position:absolute;left:40px;top:8px}@media only screen and (max-width:1643px){.features-item-image{left:30px}}@media only screen and (max-width:1439px){.features-item-image{left:0}}@media only screen and (max-width:767px){.features-item-image{left:15px;top:50%;transform:translateY(-50%)}}@media only screen and (max-width:639px){.features-item-image{left:50%;top:0;transform:translate(-50%)}}.features-item-title{max-width:396px;font-size:24px;font-weight:700}@media only screen and (max-width:1439px){.features-item-title{left:0;margin-bottom:4px}}@media only screen and (max-width:991px){.features-item-title{font-size:20px}}@media only screen and (max-width:767px){.features-item-title{max-width:100%}}.features-item-text{max-width:358px;font-size:16px;color:var(--v-grey-base);line-height:1.5}@media only screen and (max-width:767px){.features-item-text{max-width:100%}}@media only screen and (min-width:992px){.features-item-i2 .features-item-wrap,.features-item-i4 .features-item-wrap{padding-left:112px}.features-item-i2 .features-item-image,.features-item-i4 .features-item-image{left:0}}@media only screen and (max-width:767px){.features-item-i4{margin-bottom:20px}}@media only screen and (max-width:479px){.features-item-i4{margin-bottom:5px}}.about-item-title,.how-works-item-title{margin-bottom:8px;font-size:24px;font-weight:700}@media only screen and (max-width:1439px){.about-item-title,.how-works-item-title{font-size:22px}}@media only screen and (max-width:767px){.about-item-title,.how-works-item-title{font-size:20px}}@media only screen and (max-width:639px){.about-item-title,.how-works-item-title{margin-bottom:4px}}.about-item-text,.how-works-item-text{line-height:1.55;opacity:.5}@media only screen and (max-width:639px){.about-item-text,.how-works-item-text{font-size:16px}}.find-teacher{position:relative;margin-top:-135px;padding:196px 0 146px}@media only screen and (min-width:992px){.find-teacher .section-bg .v-image__image{background-size:100% 100%!important}}@media only screen and (max-width:1439px){.find-teacher{padding:96px 0 46px}}@media only screen and (max-width:991px){.find-teacher{margin-top:0}}.find-teacher-images{display:flex;justify-content:center;margin-bottom:10px}@media only screen and (max-width:1439px){.find-teacher-images{margin-bottom:25px}}.find-teacher-images-item{max-width:145px;margin:0 15px 15px}@media only screen and (max-width:1439px){.find-teacher-images-item{max-width:125px}}@media only screen and (max-width:639px){.find-teacher-images-item{max-width:106px;margin:0 5px 10px}}@media only screen and (max-width:479px){.find-teacher-images-item{max-width:85px}}.find-teacher-content{position:relative;max-width:504px;margin:0 auto}@media only screen and (max-width:1439px){.find-teacher-content{margin:100px auto}}.find-teacher-title{margin-bottom:8px;font-size:24px;font-weight:700;color:var(--v-orangeLight-base)}@media only screen and (max-width:639px){.find-teacher-title{font-size:22px}}.find-teacher-text{color:#fff;line-height:1.77}@media only screen and (max-width:639px){.find-teacher-text{font-size:16px}}.find-teacher-button{margin-top:64px}@media only screen and (max-width:1439px){.find-teacher-button{margin-top:54px}}@media only screen and (max-width:639px){.find-teacher-button{margin-top:40px}}@media only screen and (max-width:479px){.find-teacher-button .v-btn{width:100%!important}}.virtual-classroom{position:relative;padding:165px 0;color:#fff}@media only screen and (max-width:1643px){.virtual-classroom{padding:195px 0}}@media only screen and (max-width:1439px){.virtual-classroom{padding:200px 0 195px}.virtual-classroom .section-bg .v-image__image{background-position:30%!important}}@media only screen and (max-width:991px){.virtual-classroom{padding:150px 0 160px}.virtual-classroom .section-bg .v-image__image{background-position:24%!important}}@media only screen and (max-width:479px){.virtual-classroom{padding-bottom:260px}}.virtual-classroom .section-head{max-width:550px;margin-bottom:60px}@media only screen and (max-width:1439px){.virtual-classroom .section-head{margin-bottom:25px}}.virtual-classroom-image{position:relative;display:flex;align-items:center;min-height:688px}@media only screen and (max-width:1643px){.virtual-classroom-image{min-height:580px}}@media only screen and (max-width:1439px){.virtual-classroom-image{min-height:auto;justify-content:center}}@media only screen and (max-width:991px){.virtual-classroom-image{display:block;max-width:870px}}.virtual-classroom-image .circle{position:absolute;top:50%;left:-215px;width:100%;height:100%;transform:translateY(-50%)}@media only screen and (max-width:1643px){.virtual-classroom-image .circle{left:-270px}}@media only screen and (max-width:991px){.virtual-classroom-image .circle{left:-100px}}.virtual-classroom-image>div{position:relative;left:-40px}@media only screen and (max-width:1643px){.virtual-classroom-image>div{left:-130px}}@media only screen and (max-width:1439px){.virtual-classroom-image>div{left:-50px}}@media only screen and (max-width:991px){.virtual-classroom-image>div{left:auto;margin:0 auto;padding-right:8%}}.virtual-classroom-image>div .laptop{max-width:802px}@media only screen and (max-width:1643px){.virtual-classroom-image>div .laptop{max-width:706px}}@media only screen and (max-width:1439px){.virtual-classroom-image>div .laptop{max-width:590px}}@media only screen and (max-width:991px){.virtual-classroom-image>div .laptop{max-width:802px}}.virtual-classroom-image>div .exercise,.virtual-classroom-image>div .stream,.virtual-classroom-image>div .whiteboard{position:absolute;width:100%;transition:transform .3s}@media only screen and (min-width:992px){.virtual-classroom-image>div .exercise:hover,.virtual-classroom-image>div .stream:hover,.virtual-classroom-image>div .whiteboard:hover{transform:scale(1.12)}}.virtual-classroom-image>div .whiteboard{max-width:476px;left:0;top:25px}@media only screen and (max-width:1643px){.virtual-classroom-image>div .whiteboard{max-width:420px}}@media only screen and (max-width:1439px){.virtual-classroom-image>div .whiteboard{max-width:345px}}@media only screen and (max-width:991px){.virtual-classroom-image>div .whiteboard{width:55%;top:7%}}.virtual-classroom-image>div .stream{max-width:185px;left:402px;top:215px}@media only screen and (max-width:1643px){.virtual-classroom-image>div .stream{max-width:158px;left:368px;top:200px}}@media only screen and (max-width:1439px){.virtual-classroom-image>div .stream{max-width:138px;left:328px;top:196px}}@media only screen and (max-width:991px){.virtual-classroom-image>div .stream{width:22%;left:46%;top:46%}}.virtual-classroom-image>div .exercise{max-width:81px;left:460px;top:80px}@media only screen and (max-width:1643px){.virtual-classroom-image>div .exercise{max-width:72px;left:405px;top:75px}}@media only screen and (max-width:1439px){.virtual-classroom-image>div .exercise{max-width:60px;left:55%;top:80px}}@media only screen and (max-width:991px){.virtual-classroom-image>div .exercise{left:50%;width:10%;max-width:81px;top:18%}}.virtual-classroom-image>div .mobile{position:absolute;right:-90px;bottom:-75px}@media only screen and (max-width:1439px){.virtual-classroom-image>div .mobile{width:28%;right:-25px;bottom:-62px}}@media only screen and (max-width:991px){.virtual-classroom-image>div .mobile{right:0;bottom:-15%}}.virtual-classroom-content{position:relative;height:100%;display:flex;justify-content:center;flex-direction:column;padding-left:90px}@media only screen and (max-width:1439px){.virtual-classroom-content{padding-left:0}}.virtual-classroom-title{margin-bottom:24px;font-size:32px;font-weight:600}@media only screen and (max-width:1439px){.virtual-classroom-title{margin-top:65px}}@media only screen and (max-width:991px){.virtual-classroom-title{margin-top:125px;text-align:center}}.virtual-classroom-title span{color:var(--v-orange-base);background:linear-gradient(-90deg,var(--v-orange-base),var(--v-green-base));background:-webkit-linear-gradient(-90deg,var(--v-orange-base),var(--v-green-base));-webkit-background-clip:text;-webkit-text-fill-color:transparent}.virtual-classroom-text{max-width:378px;font-weight:300;line-height:1.77}@media only screen and (max-width:1439px){.virtual-classroom-text{max-width:500px;margin:0 auto}}.virtual-classroom-text ul{max-width:326px;padding-left:20px!important}.virtual-classroom-video-1,.virtual-classroom-video-2{position:absolute}.virtual-classroom-video-1{left:60px;top:-135px}@media only screen and (max-width:1643px){.virtual-classroom-video-1{top:-165px;left:40px;max-width:320px}}@media only screen and (max-width:1439px){.virtual-classroom-video-1{top:-185px;left:70px}}@media only screen and (max-width:991px){.virtual-classroom-video-1{max-width:320px;left:auto;top:auto;right:0;bottom:-245px}}@media only screen and (max-width:479px){.virtual-classroom-video-1{max-width:285px}}.virtual-classroom-video-2{left:40px;bottom:-255px}@media only screen and (max-width:1643px){.virtual-classroom-video-2{left:80px;bottom:-255px;max-width:420px}}@media only screen and (max-width:1439px){.virtual-classroom-video-2{left:auto;right:-15px;bottom:-295px}}@media only screen and (max-width:991px){.virtual-classroom-video-2{max-width:240px;top:-50px;left:0;right:auto;bottom:auto}}@media only screen and (max-width:479px){.virtual-classroom-video-2{top:-30px;left:50%;transform:translateX(-50%)}}.company{padding:30px 0 190px}@media only screen and (max-width:1643px){.company{padding-bottom:75px}}@media only screen and (max-width:1439px){.company{padding-bottom:110px}}@media only screen and (max-width:991px){.company{padding:90px 0 60px}}@media only screen and (max-width:479px){.company{padding:90px 0 45px}}.company .section-head{margin-bottom:158px}@media only screen and (max-width:1643px){.company .section-head{margin-bottom:106px}}@media only screen and (max-width:1439px){.company .section-head{margin-bottom:85px}}.company-list{display:flex;flex-wrap:wrap}.company-item{display:flex;align-items:center;justify-content:center;width:25%;height:125px;padding:0 15px;opacity:.3}@media only screen and (max-width:1643px){.company-item{height:118px}}@media only screen and (max-width:991px){.company-item{width:50%;height:98px;margin-bottom:30px;opacity:.7}}@media only screen and (max-width:639px){.company-item{height:110px}}@media only screen and (max-width:479px){.company-item{height:70px}}.company-item:hover{opacity:1}.company-item .v-image{max-width:215px;width:auto;height:auto;max-height:100%}@media only screen and (max-width:1643px){.company-item .v-image{max-width:208px}}@media only screen and (max-width:479px){.company-item .v-image{max-width:130px}}.start{position:relative;height:1px;min-height:748px;color:#fff}@media only screen and (max-width:1439px){.start{min-height:660px}}@media only screen and (max-width:991px){.start{height:auto;min-height:auto;padding:80px 0;overflow:hidden}.start .section-bg .v-image__image{background-position:45%!important}}@media only screen and (min-width:768px){.start-title--mobile{display:none}}@media only screen and (max-width:767px){.start-title--mobile{position:relative;max-width:360px;margin:0 auto 48px;font-size:22px;line-height:1.45;text-align:center}.start-text{display:none}}.start-image .v-image{max-width:540px!important}@media only screen and (max-width:767px){.start-image{display:flex;justify-content:center}.start-image .v-image{max-width:392px!important}}@media only screen and (max-width:479px){.start-image{justify-content:flex-end}.start-image .v-image{position:relative;right:-95px;max-width:392px!important}}.start-content{position:relative;margin-left:120px;font-size:32px;line-height:1.3}@media only screen and (max-width:1439px){.start-content{margin-left:0;font-size:28px}}@media only screen and (max-width:991px){.start-content{font-size:24px}}.start-button{margin-top:54px}@media only screen and (max-width:767px){.start-button{display:flex;justify-content:center}}@media only screen and (max-width:479px){.start-button .v-btn{min-width:100%!important;width:100%!important}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1502:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/index.vue?vue&type=template&id=aa2b0066&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-row',{directives:[{name:"resize",rawName:"v-resize",value:(_vm.onResize),expression:"onResize"}]},[_c('v-col',{staticClass:"col-12 px-0"},[_c('intro-section',{attrs:{"language-items":_vm.languageItems}}),_vm._v(" "),_c('stat-section'),_vm._v(" "),_c('section',{staticClass:"features"},[_c('v-container',[_c('v-row',[_c('v-col',{staticClass:"col-xl-10 offset-xl-1 px-2 px-lg-4"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-sm-6 features-item features-item-i1"},[_c('div',{staticClass:"features-item-wrap"},[_c('div',{staticClass:"features-item-image"},[_c('v-img',{attrs:{"src":__webpack_require__(155),"width":"70","options":{ rootMargin: '30%' }}})],1),_vm._v(" "),_c('div',{staticClass:"features-item-title"},[_vm._v("\n                    "+_vm._s(_vm.$t('home_page.feature_1_title'))+"\n                  ")]),_vm._v(" "),_c('div',{staticClass:"features-item-text"},[_vm._v("\n                    "+_vm._s(_vm.$t('home_page.feature_1_text'))+"\n                  ")])])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 pr-md-0 features-item features-item-i2"},[_c('div',{staticClass:"features-item-wrap"},[_c('div',{staticClass:"features-item-image"},[_c('v-img',{attrs:{"src":__webpack_require__(148),"width":"80","options":{ rootMargin: '30%' }}})],1),_vm._v(" "),_c('div',{staticClass:"features-item-title"},[_vm._v("\n                    "+_vm._s(_vm.$t('home_page.feature_2_title'))+"\n                  ")]),_vm._v(" "),_c('div',{staticClass:"features-item-text"},[_vm._v("\n                    "+_vm._s(_vm.$t('home_page.feature_2_text'))+"\n                  ")])])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 features-item features-item-i3"},[_c('div',{staticClass:"features-item-wrap"},[_c('div',{staticClass:"features-item-image"},[_c('v-img',{attrs:{"src":__webpack_require__(146),"width":"80","options":{ rootMargin: '30%' }}})],1),_vm._v(" "),_c('div',{staticClass:"features-item-title"},[_vm._v("\n                    "+_vm._s(_vm.$t('home_page.feature_3_title'))+"\n                  ")]),_vm._v(" "),_c('div',{staticClass:"features-item-text"},[_vm._v("\n                    "+_vm._s(_vm.$t('home_page.feature_3_text'))+"\n                  ")])])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 pr-md-0 features-item features-item-i4"},[_c('div',{staticClass:"features-item-wrap"},[_c('div',{staticClass:"features-item-image"},[_c('v-img',{attrs:{"src":__webpack_require__(153),"width":"82","options":{ rootMargin: '30%' }}})],1),_vm._v(" "),_c('div',{staticClass:"features-item-title"},[_vm._v("\n                    "+_vm._s(_vm.$t('home_page.feature_4_title'))+"\n                  ")]),_vm._v(" "),_c('div',{staticClass:"features-item-text"},[_vm._v("\n                    "+_vm._s(_vm.$t('home_page.feature_4_text'))+"\n                  ")])])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 py-0 d-sm-none"},[_c('div',{staticClass:"features-button"},[_c('v-btn',{attrs:{"to":_vm.localePath({ name: 'teacher-listing' }),"large":"","color":"primary"}},[_vm._v("\n                    "+_vm._s(_vm.$t('get_started'))+"\n                  ")])],1)])],1)],1)],1)],1)],1),_vm._v(" "),_c('how-works-section'),_vm._v(" "),_c('about-section'),_vm._v(" "),_c('section',{staticClass:"find-teacher"},[_c('div',{staticClass:"section-bg"},[_c('v-img',{attrs:{"src":__webpack_require__(445),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('v-container',[_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"find-teacher-content text-center"},[_c('div',{staticClass:"find-teacher-images"},[_c('div',{staticClass:"find-teacher-images-item"},[_c('v-img',{attrs:{"src":__webpack_require__(156),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"find-teacher-images-item"},[_c('v-img',{attrs:{"src":__webpack_require__(157),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"find-teacher-images-item"},[_c('v-img',{attrs:{"src":__webpack_require__(158),"options":{ rootMargin: '50%' }}})],1)]),_vm._v(" "),_c('div',{staticClass:"find-teacher-title"},[_vm._v("\n                "+_vm._s(_vm.$t('home_page.find_teacher_title'))+"\n              ")]),_vm._v(" "),_c('div',{staticClass:"find-teacher-text"},[_vm._v("\n                "+_vm._s(_vm.$t('home_page.find_teacher_text'))+"\n              ")]),_vm._v(" "),_c('div',{staticClass:"find-teacher-button"},[_c('v-btn',{attrs:{"to":_vm.localePath({
                      name: 'teacher-listing',
                      params: { utm_source: 'homepage', utm_medium: 'intro' },
                    }),"large":"","color":"primary","outlined":""}},[_vm._v("\n                  "+_vm._s(_vm.$t('find_teacher'))+"\n                ")])],1)])])],1)],1)],1),_vm._v(" "),_c('languages-section'),_vm._v(" "),(_vm.teacherItems && _vm.teacherItems.length)?_c('tutors-section',{attrs:{"teachers":_vm.teacherItems}}):_vm._e(),_vm._v(" "),_c('section',{staticClass:"virtual-classroom"},[_c('div',{staticClass:"section-bg"},[_c('v-img',{attrs:{"src":__webpack_require__(450),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{staticClass:"section-head-title",staticStyle:{"color":"#ffffff","-webkit-text-fill-color":"#ffffff"}},[_vm._v("\n                "+_vm._s(_vm.$t('home_page.classroom_section_title'))+"\n              ")])])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-7"},[_c('div',{staticClass:"virtual-classroom-image"},[_c('v-img',{staticClass:"circle",attrs:{"src":__webpack_require__(147),"contain":"","position":"left center","options":{ rootMargin: '50%' }}}),_vm._v(" "),_c('div',[_c('div',{staticClass:"laptop"},[_c('v-img',{attrs:{"src":__webpack_require__(442),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"whiteboard"},[_c('v-img',{attrs:{"src":__webpack_require__(444),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"stream"},[_c('v-img',{attrs:{"src":__webpack_require__(443),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"exercise"},[_c('v-img',{attrs:{"src":__webpack_require__(441),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('v-img',{staticClass:"mobile",attrs:{"src":__webpack_require__(446),"max-width":"261","options":{ rootMargin: '50%' }}})],1)],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-5"},[_c('div',{staticClass:"virtual-classroom-content"},[_c('div',{staticClass:"virtual-classroom-video-1"},[_c('v-img',{attrs:{"src":__webpack_require__(448),"max-width":"404","options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"virtual-classroom-title",domProps:{"innerHTML":_vm._s(_vm.$t('home_page.classroom_title'))}}),_vm._v(" "),_c('div',{staticClass:"virtual-classroom-text",domProps:{"innerHTML":_vm._s(_vm.$t('home_page.classroom_text'))}}),_vm._v(" "),_c('div',{staticClass:"virtual-classroom-video-2"},[_c('v-img',{attrs:{"src":__webpack_require__(449),"max-width":"468","options":{ rootMargin: '50%' }}})],1)])])],1)],1)],1),_vm._v(" "),(_vm.reviewItems && _vm.reviewItems.length)?_c('review-section',{attrs:{"reviews":_vm.reviewItems}}):_vm._e(),_vm._v(" "),_c('section',{staticClass:"company"},[_c('v-container',{staticClass:"py-0"},[_c('v-row',[_c('v-col',{staticClass:"col-12 py-0"},[_c('div',{staticClass:"section-head section-head--decorated"},[_c('h3',{staticClass:"section-head-title",staticStyle:{"color":"#262626","-webkit-text-fill-color":"#262626"}},[_vm._v("\n                "+_vm._s(_vm.$t('home_page.company_section_title'))+"\n              ")])])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"company-list"},[_c('div',{staticClass:"company-item"},[_c('v-img',{attrs:{"src":__webpack_require__(151),"contain":"","options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"company-item"},[_c('v-img',{attrs:{"src":__webpack_require__(152),"contain":"","options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"company-item"},[_c('v-img',{attrs:{"src":__webpack_require__(150),"contain":"","options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"company-item"},[_c('v-img',{attrs:{"src":__webpack_require__(149),"contain":"","options":{ rootMargin: '50%' }}})],1)])])],1)],1)],1),_vm._v(" "),_c('section',{staticClass:"start"},[_c('div',{staticClass:"section-bg"},[_c('v-img',{attrs:{"src":__webpack_require__(447),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('v-container',{staticClass:"py-0 d-block d-sm-flex",attrs:{"fill-height":""}},[_c('v-row',[_c('v-col',{staticClass:"col-xl-10 offset-xl-1"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-sm-6"},[_c('div',{staticClass:"start-title--mobile"},[_vm._v("\n                  "+_vm._s(_vm.$t('home_page.start_text'))+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"start-image"},[_c('v-img',{attrs:{"src":__webpack_require__(154),"contain":"","options":{ rootMargin: '50%' }}})],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 d-sm-flex align-center"},[_c('div',{staticClass:"start-content"},[_c('div',{staticClass:"start-text"},[_vm._v("\n                    "+_vm._s(_vm.$t('home_page.start_text'))+"\n                  ")]),_vm._v(" "),_c('div',{staticClass:"start-button"},[_c('v-btn',{attrs:{"to":_vm.localePath({
                          name: 'teacher-listing',
                          params: {
                            utm_source: 'homepage',
                            utm_medium: 'bofu',
                          },
                        }),"large":"","color":"primary"}},[_vm._v("\n                      "+_vm._s(_vm.$t('lets_get_started'))+"\n                    ")])],1)])])],1)],1)],1)],1)],1),_vm._v(" "),(_vm.faqItems && _vm.faqItems.length)?_c('faq-section',{attrs:{"items":_vm.faqItems,"background-image":""}}):_vm._e(),_vm._v(" "),_c('thinking-section')],1)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/index.vue?vue&type=template&id=aa2b0066&

// EXTERNAL MODULE: ./components/homepage/IntroSection.vue + 4 modules
var IntroSection = __webpack_require__(1410);

// EXTERNAL MODULE: ./components/homepage/StatSection.vue + 4 modules
var StatSection = __webpack_require__(1411);

// EXTERNAL MODULE: ./components/homepage/HowWorksSection.vue + 4 modules
var HowWorksSection = __webpack_require__(1412);

// EXTERNAL MODULE: ./components/homepage/AboutSection.vue + 4 modules
var AboutSection = __webpack_require__(1413);

// EXTERNAL MODULE: ./components/homepage/LanguagesSection.vue + 4 modules
var LanguagesSection = __webpack_require__(1414);

// EXTERNAL MODULE: ./components/homepage/TutorsSection.vue + 4 modules
var TutorsSection = __webpack_require__(1415);

// EXTERNAL MODULE: ./components/homepage/ReviewSection.vue + 4 modules
var ReviewSection = __webpack_require__(1416);

// EXTERNAL MODULE: ./components/homepage/FaqSection.vue + 4 modules
var FaqSection = __webpack_require__(1417);

// EXTERNAL MODULE: ./components/homepage/ThinkingSection.vue + 4 modules
var ThinkingSection = __webpack_require__(1418);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/index.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//









/* harmony default export */ var lib_vue_loader_options_pagesvue_type_script_lang_js_ = ({
  name: 'HomePage',
  components: {
    IntroSection: IntroSection["default"],
    StatSection: StatSection["default"],
    HowWorksSection: HowWorksSection["default"],
    AboutSection: AboutSection["default"],
    LanguagesSection: LanguagesSection["default"],
    TutorsSection: TutorsSection["default"],
    ReviewSection: ReviewSection["default"],
    FaqSection: FaqSection["default"],
    ThinkingSection: ThinkingSection["default"]
  },

  async asyncData({
    store
  }) {
    let faqItems, reviewItems, languageItems, teacherItems;
    await Promise.allSettled([store.dispatch('faq/getHomePageFaqs'), store.dispatch('language/getHomePageLanguages'), store.dispatch('review/getHomePageReviews'), store.dispatch('teacher/getHomePageTeachers')]).then(res => {
      var _res$, _res$2, _res$3, _res$4;

      faqItems = (_res$ = res[0]) === null || _res$ === void 0 ? void 0 : _res$.value;
      languageItems = (_res$2 = res[1]) === null || _res$2 === void 0 ? void 0 : _res$2.value;
      reviewItems = (_res$3 = res[2]) === null || _res$3 === void 0 ? void 0 : _res$3.value;
      teacherItems = (_res$4 = res[3]) === null || _res$4 === void 0 ? void 0 : _res$4.value;
    });
    return {
      faqItems,
      reviewItems,
      teacherItems,
      languageItems
    };
  },

  data() {
    return {
      tutorEls: null,
      reviewEls: null
    };
  },

  head() {
    return {
      title: this.$t('home_page.seo_title'),
      meta: [{
        hid: 'description',
        name: 'description',
        content: this.$t('home_page.seo_description')
      }, {
        hid: 'og:title',
        name: 'og:title',
        property: 'og:title',
        content: this.$t('home_page.seo_title')
      }, {
        property: 'og:description',
        content: this.$t('home_page.seo_description')
      }],
      bodyAttrs: {
        class: `${this.locale} home-page`
      }
    };
  },

  computed: {
    isUserLogged() {
      return this.$store.getters['user/isUserLogged'];
    },

    // userCurrency() {
    //   return this.$store.getters['user/currency']
    // },
    locale() {
      return this.$i18n.locale;
    }

  },
  watch: {
    isUserLogged(newValue, oldValue) {
      this.$store.dispatch('language/getHomePageLanguages').then(data => this.languageItems = data);
    }

  },

  mounted() {
    this.$nextTick(() => {
      this.tutorEls = document.getElementsByClassName('tutors-carousel-item');
      this.reviewEls = document.getElementsByClassName('home-page-reviews-carousel-item');
      this.setCarouselItemHeight(this.tutorEls);
      this.setCarouselItemHeight(this.reviewEls);
    });
  },

  methods: {
    onResize() {
      if (this.tutorEls) {
        this.setCarouselItemHeight(this.tutorEls);
      }

      if (this.reviewEls) {
        this.setCarouselItemHeight(this.reviewEls);
      }
    },

    setCarouselItemHeight(els) {
      let maxHeight = 0;
      Array.prototype.forEach.call(els, el => {
        if (el.offsetHeight > maxHeight) {
          maxHeight = el.offsetHeight;
        }
      });
      Array.prototype.forEach.call(els, el => {
        el.style.height = '1px';
        el.style.minHeight = `${maxHeight}px`;
      });
    }

  }
});
// CONCATENATED MODULE: ./pages/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var pagesvue_type_script_lang_js_ = (lib_vue_loader_options_pagesvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installDirectives.js
var installDirectives = __webpack_require__(430);
var installDirectives_default = /*#__PURE__*/__webpack_require__.n(installDirectives);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/resize/index.js
var resize = __webpack_require__(32);

// CONCATENATED MODULE: ./pages/index.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1459)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  pagesvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "625c1dac"
  
)

/* harmony default export */ var pages = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */






installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/* vuetify-loader */


installDirectives_default()(component, {Resize: resize["a" /* default */]})


/***/ }),

/***/ 499:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXPORTS
__webpack_require__.d(__webpack_exports__, "a", function() { return /* binding */ VListItemContent; });
__webpack_require__.d(__webpack_exports__, "c", function() { return /* binding */ VListItemTitle; });
__webpack_require__.d(__webpack_exports__, "b", function() { return /* binding */ VListItemSubtitle; });

// UNUSED EXPORTS: VListItemActionText, VList, VListGroup, VListItem, VListItemAction, VListItemAvatar, VListItemIcon, VListItemGroup

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/helpers.js
var helpers = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VList.js
var VList = __webpack_require__(831);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VList/VListGroup.sass
var VListGroup = __webpack_require__(917);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/index.js
var VIcon = __webpack_require__(66);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItem.js
var VListItem = __webpack_require__(828);

// EXTERNAL MODULE: external "vue"
var external_vue_ = __webpack_require__(1);
var external_vue_default = /*#__PURE__*/__webpack_require__.n(external_vue_);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VList/VListItemIcon.js
// Types

/* @vue/component */

/* harmony default export */ var VListItemIcon = (external_vue_default.a.extend({
  name: 'v-list-item-icon',
  functional: true,

  render(h, {
    data,
    children
  }) {
    data.staticClass = `v-list-item__icon ${data.staticClass || ''}`.trim();
    return h('div', data, children);
  }

}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/binds-attrs/index.js
var binds_attrs = __webpack_require__(23);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/bootable/index.js
var bootable = __webpack_require__(103);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/colorable/index.js
var colorable = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/toggleable/index.js
var toggleable = __webpack_require__(10);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/registrable/index.js
var registrable = __webpack_require__(29);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/ripple/index.js
var ripple = __webpack_require__(22);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/transitions/index.js + 2 modules
var transitions = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/mixins.js
var mixins = __webpack_require__(2);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VList/VListGroup.js
// Styles
 // Components



 // Mixins





 // Directives

 // Transitions

 // Utils



const baseMixins = Object(mixins["a" /* default */])(binds_attrs["a" /* default */], bootable["a" /* default */], colorable["a" /* default */], Object(registrable["a" /* inject */])('list'), toggleable["a" /* default */]);
/* harmony default export */ var VList_VListGroup = (baseMixins.extend().extend({
  name: 'v-list-group',
  directives: {
    ripple: ripple["a" /* default */]
  },
  props: {
    activeClass: {
      type: String,
      default: ''
    },
    appendIcon: {
      type: String,
      default: '$expand'
    },
    color: {
      type: String,
      default: 'primary'
    },
    disabled: Boolean,
    group: String,
    noAction: Boolean,
    prependIcon: String,
    ripple: {
      type: [Boolean, Object],
      default: true
    },
    subGroup: Boolean
  },
  computed: {
    classes() {
      return {
        'v-list-group--active': this.isActive,
        'v-list-group--disabled': this.disabled,
        'v-list-group--no-action': this.noAction,
        'v-list-group--sub-group': this.subGroup
      };
    }

  },
  watch: {
    isActive(val) {
      /* istanbul ignore else */
      if (!this.subGroup && val) {
        this.list && this.list.listClick(this._uid);
      }
    },

    $route: 'onRouteChange'
  },

  created() {
    this.list && this.list.register(this);

    if (this.group && this.$route && this.value == null) {
      this.isActive = this.matchRoute(this.$route.path);
    }
  },

  beforeDestroy() {
    this.list && this.list.unregister(this);
  },

  methods: {
    click(e) {
      if (this.disabled) return;
      this.isBooted = true;
      this.$emit('click', e);
      this.$nextTick(() => this.isActive = !this.isActive);
    },

    genIcon(icon) {
      return this.$createElement(VIcon["a" /* default */], icon);
    },

    genAppendIcon() {
      const icon = !this.subGroup ? this.appendIcon : false;
      if (!icon && !this.$slots.appendIcon) return null;
      return this.$createElement(VListItemIcon, {
        staticClass: 'v-list-group__header__append-icon'
      }, [this.$slots.appendIcon || this.genIcon(icon)]);
    },

    genHeader() {
      return this.$createElement(VListItem["a" /* default */], {
        staticClass: 'v-list-group__header',
        attrs: {
          'aria-expanded': String(this.isActive),
          role: 'button'
        },
        class: {
          [this.activeClass]: this.isActive
        },
        props: {
          inputValue: this.isActive
        },
        directives: [{
          name: 'ripple',
          value: this.ripple
        }],
        on: { ...this.listeners$,
          click: this.click
        }
      }, [this.genPrependIcon(), this.$slots.activator, this.genAppendIcon()]);
    },

    genItems() {
      return this.showLazyContent(() => [this.$createElement('div', {
        staticClass: 'v-list-group__items',
        directives: [{
          name: 'show',
          value: this.isActive
        }]
      }, Object(helpers["n" /* getSlot */])(this))]);
    },

    genPrependIcon() {
      const icon = this.subGroup && this.prependIcon == null ? '$subgroup' : this.prependIcon;
      if (!icon && !this.$slots.prependIcon) return null;
      return this.$createElement(VListItemIcon, {
        staticClass: 'v-list-group__header__prepend-icon'
      }, [this.$slots.prependIcon || this.genIcon(icon)]);
    },

    onRouteChange(to) {
      /* istanbul ignore if */
      if (!this.group) return;
      const isActive = this.matchRoute(to.path);
      /* istanbul ignore else */

      if (isActive && this.isActive !== isActive) {
        this.list && this.list.listClick(this._uid);
      }

      this.isActive = isActive;
    },

    toggle(uid) {
      const isActive = this._uid === uid;
      if (isActive) this.isBooted = true;
      this.$nextTick(() => this.isActive = isActive);
    },

    matchRoute(to) {
      return to.match(this.group) !== null;
    }

  },

  render(h) {
    return h('div', this.setTextColor(this.isActive && this.color, {
      staticClass: 'v-list-group',
      class: this.classes
    }), [this.genHeader(), h(transitions["a" /* VExpandTransition */], this.genItems())]);
  }

}));
// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VList/VListItemGroup.sass
var VListItemGroup = __webpack_require__(919);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VItemGroup/VItemGroup.js
var VItemGroup = __webpack_require__(902);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VList/VListItemGroup.js
// Styles
 // Extensions

 // Mixins

 // Utilities


/* harmony default export */ var VList_VListItemGroup = (Object(mixins["a" /* default */])(VItemGroup["a" /* BaseItemGroup */], colorable["a" /* default */]).extend({
  name: 'v-list-item-group',

  provide() {
    return {
      isInGroup: true,
      listItemGroup: this
    };
  },

  computed: {
    classes() {
      return { ...VItemGroup["a" /* BaseItemGroup */].options.computed.classes.call(this),
        'v-list-item-group': true
      };
    }

  },
  methods: {
    genData() {
      return this.setTextColor(this.color, { ...VItemGroup["a" /* BaseItemGroup */].options.methods.genData.call(this),
        attrs: {
          role: 'listbox'
        }
      });
    }

  }
}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItemAction.js
var VListItemAction = __webpack_require__(904);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/index.js
var VAvatar = __webpack_require__(500);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VList/VListItemAvatar.js
// Components

/* @vue/component */

/* harmony default export */ var VListItemAvatar = (VAvatar["a" /* default */].extend({
  name: 'v-list-item-avatar',
  props: {
    horizontal: Boolean,
    size: {
      type: [Number, String],
      default: 40
    }
  },
  computed: {
    classes() {
      return {
        'v-list-item__avatar--horizontal': this.horizontal,
        ...VAvatar["a" /* default */].options.computed.classes.call(this),
        'v-avatar--tile': this.tile || this.horizontal
      };
    }

  },

  render(h) {
    const render = VAvatar["a" /* default */].options.render.call(this, h);
    render.data = render.data || {};
    render.data.staticClass += ' v-list-item__avatar';
    return render;
  }

}));
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VList/index.js








const VListItemActionText = Object(helpers["g" /* createSimpleFunctional */])('v-list-item__action-text', 'span');
const VListItemContent = Object(helpers["g" /* createSimpleFunctional */])('v-list-item__content', 'div');
const VListItemTitle = Object(helpers["g" /* createSimpleFunctional */])('v-list-item__title', 'div');
const VListItemSubtitle = Object(helpers["g" /* createSimpleFunctional */])('v-list-item__subtitle', 'div');

/* harmony default export */ var components_VList = ({
  $_vuetify_subcomponents: {
    VList: VList["a" /* default */],
    VListGroup: VList_VListGroup,
    VListItem: VListItem["a" /* default */],
    VListItemAction: VListItemAction["a" /* default */],
    VListItemActionText,
    VListItemAvatar: VListItemAvatar,
    VListItemContent,
    VListItemGroup: VList_VListItemGroup,
    VListItemIcon: VListItemIcon,
    VListItemSubtitle,
    VListItemTitle
  }
});

/***/ }),

/***/ 500:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _VAvatar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(830);


/* harmony default export */ __webpack_exports__["a"] = (_VAvatar__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]);

/***/ }),

/***/ 832:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _VMenu__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(833);


/* harmony default export */ __webpack_exports__["a"] = (_VMenu__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]);

/***/ }),

/***/ 901:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(909);
/* harmony import */ var _src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
/* harmony import */ var _transitions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(67);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(66);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9);
/* harmony import */ var _mixins_groupable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(47);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(7);
/* harmony import */ var _mixins_toggleable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(10);
/* harmony import */ var _mixins_routable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(18);
/* harmony import */ var _mixins_sizeable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(49);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(3);
// Styles

 // Components


 // Mixins






 // Utilities


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(_mixins_colorable__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"], _mixins_sizeable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"], _mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], _mixins_themeable__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"], Object(_mixins_groupable__WEBPACK_IMPORTED_MODULE_5__[/* factory */ "a"])('chipGroup'), Object(_mixins_toggleable__WEBPACK_IMPORTED_MODULE_7__[/* factory */ "b"])('inputValue')).extend({
  name: 'v-chip',
  props: {
    active: {
      type: Boolean,
      default: true
    },
    activeClass: {
      type: String,

      default() {
        if (!this.chipGroup) return '';
        return this.chipGroup.activeClass;
      }

    },
    close: Boolean,
    closeIcon: {
      type: String,
      default: '$delete'
    },
    closeLabel: {
      type: String,
      default: '$vuetify.close'
    },
    disabled: Boolean,
    draggable: Boolean,
    filter: Boolean,
    filterIcon: {
      type: String,
      default: '$complete'
    },
    label: Boolean,
    link: Boolean,
    outlined: Boolean,
    pill: Boolean,
    tag: {
      type: String,
      default: 'span'
    },
    textColor: String,
    value: null
  },
  data: () => ({
    proxyClass: 'v-chip--active'
  }),
  computed: {
    classes() {
      return {
        'v-chip': true,
        ..._mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].options.computed.classes.call(this),
        'v-chip--clickable': this.isClickable,
        'v-chip--disabled': this.disabled,
        'v-chip--draggable': this.draggable,
        'v-chip--label': this.label,
        'v-chip--link': this.isLink,
        'v-chip--no-color': !this.color,
        'v-chip--outlined': this.outlined,
        'v-chip--pill': this.pill,
        'v-chip--removable': this.hasClose,
        ...this.themeClasses,
        ...this.sizeableClasses,
        ...this.groupClasses
      };
    },

    hasClose() {
      return Boolean(this.close);
    },

    isClickable() {
      return Boolean(_mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].options.computed.isClickable.call(this) || this.chipGroup);
    }

  },

  created() {
    const breakingProps = [['outline', 'outlined'], ['selected', 'input-value'], ['value', 'active'], ['@input', '@active.sync']];
    /* istanbul ignore next */

    breakingProps.forEach(([original, replacement]) => {
      if (this.$attrs.hasOwnProperty(original)) Object(_util_console__WEBPACK_IMPORTED_MODULE_10__[/* breaking */ "a"])(original, replacement, this);
    });
  },

  methods: {
    click(e) {
      this.$emit('click', e);
      this.chipGroup && this.toggle();
    },

    genFilter() {
      const children = [];

      if (this.isActive) {
        children.push(this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"], {
          staticClass: 'v-chip__filter',
          props: {
            left: true
          }
        }, this.filterIcon));
      }

      return this.$createElement(_transitions__WEBPACK_IMPORTED_MODULE_2__[/* VExpandXTransition */ "b"], children);
    },

    genClose() {
      return this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"], {
        staticClass: 'v-chip__close',
        props: {
          right: true,
          size: 18
        },
        attrs: {
          'aria-label': this.$vuetify.lang.t(this.closeLabel)
        },
        on: {
          click: e => {
            e.stopPropagation();
            e.preventDefault();
            this.$emit('click:close');
            this.$emit('update:active', false);
          }
        }
      }, this.closeIcon);
    },

    genContent() {
      return this.$createElement('span', {
        staticClass: 'v-chip__content'
      }, [this.filter && this.genFilter(), this.$slots.default, this.hasClose && this.genClose()]);
    }

  },

  render(h) {
    const children = [this.genContent()];
    let {
      tag,
      data
    } = this.generateRouteLink();
    data.attrs = { ...data.attrs,
      draggable: this.draggable ? 'true' : undefined,
      tabindex: this.chipGroup && !this.disabled ? 0 : data.attrs.tabindex
    };
    data.directives.push({
      name: 'show',
      value: this.active
    });
    data = this.setBackgroundColor(this.color, data);
    const color = this.textColor || this.outlined && this.color;
    return h(tag, this.setTextColor(color, data), children);
  }

}));

/***/ }),

/***/ 902:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return BaseItemGroup; });
/* harmony import */ var _src_components_VItemGroup_VItemGroup_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(906);
/* harmony import */ var _src_components_VItemGroup_VItemGroup_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VItemGroup_VItemGroup_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _mixins_proxyable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(104);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3);
// Styles


 // Utilities



const BaseItemGroup = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(_mixins_proxyable__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], _mixins_themeable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"]).extend({
  name: 'base-item-group',
  props: {
    activeClass: {
      type: String,
      default: 'v-item--active'
    },
    mandatory: Boolean,
    max: {
      type: [Number, String],
      default: null
    },
    multiple: Boolean,
    tag: {
      type: String,
      default: 'div'
    }
  },

  data() {
    return {
      // As long as a value is defined, show it
      // Otherwise, check if multiple
      // to determine which default to provide
      internalLazyValue: this.value !== undefined ? this.value : this.multiple ? [] : undefined,
      items: []
    };
  },

  computed: {
    classes() {
      return {
        'v-item-group': true,
        ...this.themeClasses
      };
    },

    selectedIndex() {
      return this.selectedItem && this.items.indexOf(this.selectedItem) || -1;
    },

    selectedItem() {
      if (this.multiple) return undefined;
      return this.selectedItems[0];
    },

    selectedItems() {
      return this.items.filter((item, index) => {
        return this.toggleMethod(this.getValue(item, index));
      });
    },

    selectedValues() {
      if (this.internalValue == null) return [];
      return Array.isArray(this.internalValue) ? this.internalValue : [this.internalValue];
    },

    toggleMethod() {
      if (!this.multiple) {
        return v => this.internalValue === v;
      }

      const internalValue = this.internalValue;

      if (Array.isArray(internalValue)) {
        return v => internalValue.includes(v);
      }

      return () => false;
    }

  },
  watch: {
    internalValue: 'updateItemsState',
    items: 'updateItemsState'
  },

  created() {
    if (this.multiple && !Array.isArray(this.internalValue)) {
      Object(_util_console__WEBPACK_IMPORTED_MODULE_4__[/* consoleWarn */ "c"])('Model must be bound to an array if the multiple property is true.', this);
    }
  },

  methods: {
    genData() {
      return {
        class: this.classes
      };
    },

    getValue(item, i) {
      return item.value == null || item.value === '' ? i : item.value;
    },

    onClick(item) {
      this.updateInternalValue(this.getValue(item, this.items.indexOf(item)));
    },

    register(item) {
      const index = this.items.push(item) - 1;
      item.$on('change', () => this.onClick(item)); // If no value provided and mandatory,
      // assign first registered item

      if (this.mandatory && !this.selectedValues.length) {
        this.updateMandatory();
      }

      this.updateItem(item, index);
    },

    unregister(item) {
      if (this._isDestroyed) return;
      const index = this.items.indexOf(item);
      const value = this.getValue(item, index);
      this.items.splice(index, 1);
      const valueIndex = this.selectedValues.indexOf(value); // Items is not selected, do nothing

      if (valueIndex < 0) return; // If not mandatory, use regular update process

      if (!this.mandatory) {
        return this.updateInternalValue(value);
      } // Remove the value


      if (this.multiple && Array.isArray(this.internalValue)) {
        this.internalValue = this.internalValue.filter(v => v !== value);
      } else {
        this.internalValue = undefined;
      } // If mandatory and we have no selection
      // add the last item as value

      /* istanbul ignore else */


      if (!this.selectedItems.length) {
        this.updateMandatory(true);
      }
    },

    updateItem(item, index) {
      const value = this.getValue(item, index);
      item.isActive = this.toggleMethod(value);
    },

    // https://github.com/vuetifyjs/vuetify/issues/5352
    updateItemsState() {
      this.$nextTick(() => {
        if (this.mandatory && !this.selectedItems.length) {
          return this.updateMandatory();
        } // TODO: Make this smarter so it
        // doesn't have to iterate every
        // child in an update


        this.items.forEach(this.updateItem);
      });
    },

    updateInternalValue(value) {
      this.multiple ? this.updateMultiple(value) : this.updateSingle(value);
    },

    updateMandatory(last) {
      if (!this.items.length) return;
      const items = this.items.slice();
      if (last) items.reverse();
      const item = items.find(item => !item.disabled); // If no tabs are available
      // aborts mandatory value

      if (!item) return;
      const index = this.items.indexOf(item);
      this.updateInternalValue(this.getValue(item, index));
    },

    updateMultiple(value) {
      const defaultValue = Array.isArray(this.internalValue) ? this.internalValue : [];
      const internalValue = defaultValue.slice();
      const index = internalValue.findIndex(val => val === value);
      if (this.mandatory && // Item already exists
      index > -1 && // value would be reduced below min
      internalValue.length - 1 < 1) return;
      if ( // Max is set
      this.max != null && // Item doesn't exist
      index < 0 && // value would be increased above max
      internalValue.length + 1 > this.max) return;
      index > -1 ? internalValue.splice(index, 1) : internalValue.push(value);
      this.internalValue = internalValue;
    },

    updateSingle(value) {
      const isSame = value === this.internalValue;
      if (this.mandatory && isSame) return;
      this.internalValue = isSame ? undefined : value;
    }

  },

  render(h) {
    return h(this.tag, this.genData(), this.$slots.default);
  }

});
/* unused harmony default export */ var _unused_webpack_default_export = (BaseItemGroup.extend({
  name: 'v-item-group',

  provide() {
    return {
      itemGroup: this
    };
  }

}));

/***/ }),

/***/ 903:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(0);


/* harmony default export */ __webpack_exports__["a"] = (vue__WEBPACK_IMPORTED_MODULE_0___default.a.extend({
  name: 'comparable',
  props: {
    valueComparator: {
      type: Function,
      default: _util_helpers__WEBPACK_IMPORTED_MODULE_1__[/* deepEqual */ "h"]
    }
  }
}));

/***/ }),

/***/ 904:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
// Types

/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (vue__WEBPACK_IMPORTED_MODULE_0___default.a.extend({
  name: 'v-list-item-action',
  functional: true,

  render(h, {
    data,
    children = []
  }) {
    data.staticClass = data.staticClass ? `v-list-item__action ${data.staticClass}` : 'v-list-item__action';
    const filteredChild = children.filter(VNode => {
      return VNode.isComment === false && VNode.text !== ' ';
    });
    if (filteredChild.length > 1) data.staticClass += ' v-list-item__action--stack';
    return h('div', data, children);
  }

}));

/***/ }),

/***/ 905:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VDivider_VDivider_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(913);
/* harmony import */ var _src_components_VDivider_VDivider_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VDivider_VDivider_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(7);
// Styles
 // Mixins


/* harmony default export */ __webpack_exports__["a"] = (_mixins_themeable__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].extend({
  name: 'v-divider',
  props: {
    inset: Boolean,
    vertical: Boolean
  },

  render(h) {
    // WAI-ARIA attributes
    let orientation;

    if (!this.$attrs.role || this.$attrs.role === 'separator') {
      orientation = this.vertical ? 'vertical' : 'horizontal';
    }

    return h('hr', {
      class: {
        'v-divider': true,
        'v-divider--inset': this.inset,
        'v-divider--vertical': this.vertical,
        ...this.themeClasses
      },
      attrs: {
        role: 'separator',
        'aria-orientation': orientation,
        ...this.$attrs
      },
      on: this.$listeners
    });
  }

}));

/***/ }),

/***/ 906:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(907);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("73707fd0", content, true)

/***/ }),

/***/ 907:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-item-group{flex:0 1 auto;position:relative;max-width:100%;transition:.3s cubic-bezier(.25,.8,.5,1)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 909:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(910);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("197fcea4", content, true)

/***/ }),

/***/ 910:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-chip:not(.v-chip--outlined).accent,.v-chip:not(.v-chip--outlined).error,.v-chip:not(.v-chip--outlined).info,.v-chip:not(.v-chip--outlined).primary,.v-chip:not(.v-chip--outlined).secondary,.v-chip:not(.v-chip--outlined).success,.v-chip:not(.v-chip--outlined).warning{color:#fff}.theme--light.v-chip{border-color:rgba(0,0,0,.12);color:rgba(0,0,0,.87)}.theme--light.v-chip:not(.v-chip--active){background:#e0e0e0}.theme--light.v-chip:hover:before{opacity:.04}.theme--light.v-chip--active:before,.theme--light.v-chip--active:hover:before,.theme--light.v-chip:focus:before{opacity:.12}.theme--light.v-chip--active:focus:before{opacity:.16}.theme--dark.v-chip{border-color:hsla(0,0%,100%,.12);color:#fff}.theme--dark.v-chip:not(.v-chip--active){background:#555}.theme--dark.v-chip:hover:before{opacity:.08}.theme--dark.v-chip--active:before,.theme--dark.v-chip--active:hover:before,.theme--dark.v-chip:focus:before{opacity:.24}.theme--dark.v-chip--active:focus:before{opacity:.32}.v-chip{align-items:center;cursor:default;display:inline-flex;line-height:20px;max-width:100%;outline:none;overflow:hidden;padding:0 12px;position:relative;text-decoration:none;transition-duration:.28s;transition-property:box-shadow,opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);vertical-align:middle;white-space:nowrap}.v-chip:before{background-color:currentColor;bottom:0;border-radius:inherit;content:\"\";left:0;opacity:0;position:absolute;pointer-events:none;right:0;top:0}.v-chip .v-avatar{height:24px!important;min-width:24px!important;width:24px!important}.v-chip .v-icon{font-size:24px}.v-application--is-ltr .v-chip .v-avatar--left,.v-application--is-ltr .v-chip .v-icon--left{margin-left:-6px;margin-right:6px}.v-application--is-ltr .v-chip .v-avatar--right,.v-application--is-ltr .v-chip .v-icon--right,.v-application--is-rtl .v-chip .v-avatar--left,.v-application--is-rtl .v-chip .v-icon--left{margin-left:6px;margin-right:-6px}.v-application--is-rtl .v-chip .v-avatar--right,.v-application--is-rtl .v-chip .v-icon--right{margin-left:-6px;margin-right:6px}.v-chip:not(.v-chip--no-color) .v-icon{color:inherit}.v-chip .v-chip__close.v-icon{font-size:18px;max-height:18px;max-width:18px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right{margin-right:-4px}.v-application--is-rtl .v-chip .v-chip__close.v-icon.v-icon--right{margin-left:-4px}.v-chip .v-chip__close.v-icon:active,.v-chip .v-chip__close.v-icon:focus,.v-chip .v-chip__close.v-icon:hover{opacity:.72}.v-chip .v-chip__content{align-items:center;display:inline-flex;height:100%;max-width:100%}.v-chip--active .v-icon{color:inherit}.v-chip--link:before{transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-chip--link:focus:before{opacity:.32}.v-chip--clickable{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip--clickable:active{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-chip--disabled{opacity:.4;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip__filter{max-width:24px}.v-chip__filter.v-icon{color:inherit}.v-chip__filter.expand-x-transition-enter,.v-chip__filter.expand-x-transition-leave-active{margin:0}.v-chip--pill .v-chip__filter{margin-right:0 16px 0 0}.v-chip--pill .v-avatar{height:32px!important;width:32px!important}.v-application--is-ltr .v-chip--pill .v-avatar--left{margin-left:-12px}.v-application--is-ltr .v-chip--pill .v-avatar--right,.v-application--is-rtl .v-chip--pill .v-avatar--left{margin-right:-12px}.v-application--is-rtl .v-chip--pill .v-avatar--right{margin-left:-12px}.v-chip--label{border-radius:4px!important}.v-chip.v-chip--outlined{border-width:thin;border-style:solid}.v-chip.v-chip--outlined.v-chip--active:before{opacity:.08}.v-chip.v-chip--outlined .v-icon{color:inherit}.v-chip.v-chip--outlined.v-chip.v-chip{background-color:transparent!important}.v-chip.v-chip--selected{background:transparent}.v-chip.v-chip--selected:after{opacity:.28}.v-chip.v-size--x-small{border-radius:8px;font-size:10px;height:16px}.v-chip.v-size--small{border-radius:12px;font-size:12px;height:24px}.v-chip.v-size--default{border-radius:16px;font-size:14px;height:32px}.v-chip.v-size--large{border-radius:27px;font-size:16px;height:54px}.v-chip.v-size--x-large{border-radius:33px;font-size:18px;height:66px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 911:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _VChip__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(901);


/* harmony default export */ __webpack_exports__["a"] = (_VChip__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]);

/***/ }),

/***/ 913:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(914);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("7132a15d", content, true)

/***/ }),

/***/ 914:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-divider{border-color:rgba(0,0,0,.12)}.theme--dark.v-divider{border-color:hsla(0,0%,100%,.12)}.v-divider{display:block;flex:1 1 0px;max-width:100%;height:0;max-height:0;border:solid;border-width:thin 0 0;transition:inherit}.v-divider--inset:not(.v-divider--vertical){max-width:calc(100% - 72px)}.v-application--is-ltr .v-divider--inset:not(.v-divider--vertical){margin-left:72px}.v-application--is-rtl .v-divider--inset:not(.v-divider--vertical){margin-right:72px}.v-divider--vertical{align-self:stretch;border:solid;border-width:0 thin 0 0;display:inline-flex;height:inherit;min-height:100%;max-height:100%;max-width:0;width:0;vertical-align:text-bottom;margin:0 -1px}.v-divider--vertical.v-divider--inset{margin-top:8px;min-height:0;max-height:calc(100% - 16px)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 917:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(918);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("5e8d0e9e", content, true)

/***/ }),

/***/ 918:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-list-group .v-list-group__header .v-list-item__icon.v-list-group__header__append-icon{align-self:center;margin:0;min-width:48px;justify-content:flex-end}.v-list-group--sub-group{align-items:center;display:flex;flex-wrap:wrap}.v-list-group__header.v-list-item--active:not(:hover):not(:focus):before{opacity:0}.v-list-group__items{flex:1 1 auto}.v-list-group__items .v-list-group__items,.v-list-group__items .v-list-item{overflow:hidden}.v-list-group--active>.v-list-group__header.v-list-group__header--sub-group>.v-list-group__header__prepend-icon .v-icon,.v-list-group--active>.v-list-group__header>.v-list-group__header__append-icon .v-icon{transform:rotate(-180deg)}.v-list-group--active>.v-list-group__header .v-list-group__header__prepend-icon .v-icon,.v-list-group--active>.v-list-group__header .v-list-item,.v-list-group--active>.v-list-group__header .v-list-item__content{color:inherit}.v-application--is-ltr .v-list-group--sub-group .v-list-item__action:first-child,.v-application--is-ltr .v-list-group--sub-group .v-list-item__avatar:first-child,.v-application--is-ltr .v-list-group--sub-group .v-list-item__icon:first-child{margin-right:16px}.v-application--is-rtl .v-list-group--sub-group .v-list-item__action:first-child,.v-application--is-rtl .v-list-group--sub-group .v-list-item__avatar:first-child,.v-application--is-rtl .v-list-group--sub-group .v-list-item__icon:first-child{margin-left:16px}.v-application--is-ltr .v-list-group--sub-group .v-list-group__header{padding-left:32px}.v-application--is-rtl .v-list-group--sub-group .v-list-group__header{padding-right:32px}.v-application--is-ltr .v-list-group--sub-group .v-list-group__items .v-list-item{padding-left:40px}.v-application--is-rtl .v-list-group--sub-group .v-list-group__items .v-list-item{padding-right:40px}.v-list-group--sub-group.v-list-group--active .v-list-item__icon.v-list-group__header__prepend-icon .v-icon{transform:rotate(-180deg)}.v-application--is-ltr .v-list-group--no-action>.v-list-group__items>.v-list-item{padding-left:72px}.v-application--is-rtl .v-list-group--no-action>.v-list-group__items>.v-list-item{padding-right:72px}.v-application--is-ltr .v-list-group--no-action.v-list-group--sub-group>.v-list-group__items>.v-list-item{padding-left:88px}.v-application--is-rtl .v-list-group--no-action.v-list-group--sub-group>.v-list-group__items>.v-list-item{padding-right:88px}.v-application--is-ltr .v-list--dense .v-list-group--sub-group .v-list-group__header{padding-left:24px}.v-application--is-rtl .v-list--dense .v-list-group--sub-group .v-list-group__header{padding-right:24px}.v-application--is-ltr .v-list--dense.v-list--nav .v-list-group--no-action>.v-list-group__items>.v-list-item{padding-left:64px}.v-application--is-rtl .v-list--dense.v-list--nav .v-list-group--no-action>.v-list-group__items>.v-list-item{padding-right:64px}.v-application--is-ltr .v-list--dense.v-list--nav .v-list-group--no-action.v-list-group--sub-group>.v-list-group__items>.v-list-item{padding-left:80px}.v-application--is-rtl .v-list--dense.v-list--nav .v-list-group--no-action.v-list-group--sub-group>.v-list-group__items>.v-list-item{padding-right:80px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 919:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(920);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("516f87f8", content, true)

/***/ }),

/***/ 920:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-list-item-group .v-list-item--active{color:inherit}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 921:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _VDivider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(905);


/* harmony default export */ __webpack_exports__["a"] = (_VDivider__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]);

/***/ }),

/***/ 922:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(923);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("3f1da7f4", content, true)

/***/ }),

/***/ 923:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-select .v-select__selections{color:rgba(0,0,0,.87)}.theme--light.v-select.v-input--is-disabled .v-select__selections,.theme--light.v-select .v-select__selection--disabled{color:rgba(0,0,0,.38)}.theme--dark.v-select .v-select__selections,.theme--light.v-select.v-text-field--solo-inverted.v-input--is-focused .v-select__selections{color:#fff}.theme--dark.v-select.v-input--is-disabled .v-select__selections,.theme--dark.v-select .v-select__selection--disabled{color:hsla(0,0%,100%,.5)}.theme--dark.v-select.v-text-field--solo-inverted.v-input--is-focused .v-select__selections{color:rgba(0,0,0,.87)}.v-select{position:relative}.v-select:not(.v-select--is-multi).v-text-field--single-line .v-select__selections{flex-wrap:nowrap}.v-select>.v-input__control>.v-input__slot{cursor:pointer}.v-select .v-chip{flex:0 1 auto;margin:4px}.v-select .v-chip--selected:after{opacity:.22}.v-select .fade-transition-leave-active{position:absolute;left:0}.v-select.v-input--is-dirty ::-moz-placeholder{color:transparent!important}.v-select.v-input--is-dirty :-ms-input-placeholder{color:transparent!important}.v-select.v-input--is-dirty ::placeholder{color:transparent!important}.v-select:not(.v-input--is-dirty):not(.v-input--is-focused) .v-text-field__prefix{line-height:20px;top:7px;transition:.3s cubic-bezier(.25,.8,.5,1)}.v-select.v-text-field--enclosed:not(.v-text-field--single-line):not(.v-text-field--outlined) .v-select__selections{padding-top:20px}.v-select.v-text-field--outlined:not(.v-text-field--single-line) .v-select__selections{padding:8px 0}.v-select.v-text-field--outlined:not(.v-text-field--single-line).v-input--dense .v-select__selections{padding:4px 0}.v-select.v-text-field input{flex:1 1;margin-top:0;min-width:0;pointer-events:none;position:relative}.v-select.v-select--is-menu-active .v-input__icon--append .v-icon{transform:rotate(180deg)}.v-select.v-select--chips input{margin:0}.v-select.v-select--chips .v-select__selections{min-height:42px}.v-select.v-select--chips.v-input--dense .v-select__selections{min-height:40px}.v-select.v-select--chips .v-chip--select.v-chip--active:before{opacity:.2}.v-select.v-select--chips.v-select--chips--small .v-select__selections{min-height:26px}.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--box .v-select__selections,.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--enclosed .v-select__selections{min-height:68px}.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--box.v-input--dense .v-select__selections,.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--enclosed.v-input--dense .v-select__selections{min-height:40px}.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--box.v-select--chips--small .v-select__selections,.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--enclosed.v-select--chips--small .v-select__selections{min-height:26px}.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--box.v-select--chips--small.v-input--dense .v-select__selections,.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--enclosed.v-select--chips--small.v-input--dense .v-select__selections{min-height:38px}.v-select.v-text-field--reverse .v-select__selections,.v-select.v-text-field--reverse .v-select__slot{flex-direction:row-reverse}.v-select__selections{align-items:center;display:flex;flex:1 1;flex-wrap:wrap;line-height:18px;max-width:100%;min-width:0}.v-select__selection{max-width:90%}.v-select__selection--comma{margin:7px 4px 7px 0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.v-select.v-input--dense .v-select__selection--comma{margin:5px 4px 3px 0}.v-select.v-input--dense .v-chip{margin:0 4px}.v-select__slot{position:relative;align-items:center;display:flex;max-width:100%;min-width:0;width:100%}.v-select:not(.v-text-field--single-line):not(.v-text-field--outlined) .v-select__slot>input{align-self:flex-end}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 924:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(925);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("5c37caa6", content, true)

/***/ }),

/***/ 925:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-simple-checkbox{align-self:center;line-height:normal;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-simple-checkbox .v-icon{cursor:pointer}.v-simple-checkbox--disabled{cursor:default}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 926:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(927);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("e8b41e5e", content, true)

/***/ }),

/***/ 927:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-subheader{color:rgba(0,0,0,.6)}.theme--dark.v-subheader{color:hsla(0,0%,100%,.7)}.v-subheader{align-items:center;display:flex;height:48px;font-size:14px;font-weight:400;padding:0 16px}.v-subheader--inset{margin-left:56px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 932:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/Avatars.vue?vue&type=script&lang=js&
/* harmony default export */ var Avatarsvue_type_script_lang_js_ = ({
  methods: {
    getSrcAvatar(images, property, defaultImage = 'avatar.png') {
      return images !== null && images !== void 0 && images[property] ? images[property] : __webpack_require__(511)(`./${defaultImage}`);
    },

    getSrcSetAvatar(images, property1, property2) {
      return images !== null && images !== void 0 && images[property1] && images !== null && images !== void 0 && images[property2] ? `
            ${images[property1]} 1x,
            ${images[property2]} 2x,
          ` : '';
    }

  }
});
// CONCATENATED MODULE: ./mixins/Avatars.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_Avatarsvue_type_script_lang_js_ = (Avatarsvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/Avatars.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_Avatarsvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "0af9ff4e"
  
)

/* harmony default export */ var Avatars = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 941:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXPORTS
__webpack_require__.d(__webpack_exports__, "b", function() { return /* binding */ defaultMenuProps; });

// EXTERNAL MODULE: external "core-js/modules/esnext.array.last-item.js"
var esnext_array_last_item_js_ = __webpack_require__(835);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.delete-all.js"
var esnext_map_delete_all_js_ = __webpack_require__(71);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.every.js"
var esnext_map_every_js_ = __webpack_require__(72);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.filter.js"
var esnext_map_filter_js_ = __webpack_require__(73);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.find.js"
var esnext_map_find_js_ = __webpack_require__(74);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.find-key.js"
var esnext_map_find_key_js_ = __webpack_require__(75);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.includes.js"
var esnext_map_includes_js_ = __webpack_require__(76);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.key-of.js"
var esnext_map_key_of_js_ = __webpack_require__(77);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.map-keys.js"
var esnext_map_map_keys_js_ = __webpack_require__(78);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.map-values.js"
var esnext_map_map_values_js_ = __webpack_require__(79);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.merge.js"
var esnext_map_merge_js_ = __webpack_require__(80);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.reduce.js"
var esnext_map_reduce_js_ = __webpack_require__(81);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.some.js"
var esnext_map_some_js_ = __webpack_require__(82);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.update.js"
var esnext_map_update_js_ = __webpack_require__(83);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VTextField/VTextField.sass
var VTextField = __webpack_require__(512);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VSelect/VSelect.sass
var VSelect = __webpack_require__(922);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VChip/index.js
var VChip = __webpack_require__(911);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VMenu/index.js
var VMenu = __webpack_require__(832);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VCheckbox/VSimpleCheckbox.sass
var VSimpleCheckbox = __webpack_require__(924);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/ripple/index.js
var directives_ripple = __webpack_require__(22);

// EXTERNAL MODULE: external "vue"
var external_vue_ = __webpack_require__(1);
var external_vue_default = /*#__PURE__*/__webpack_require__.n(external_vue_);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(87);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/colorable/index.js
var colorable = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/themeable/index.js
var themeable = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/mergeData.js
var mergeData = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/helpers.js
var helpers = __webpack_require__(0);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCheckbox/VSimpleCheckbox.js



 // Mixins


 // Utilities



/* harmony default export */ var VCheckbox_VSimpleCheckbox = (external_vue_default.a.extend({
  name: 'v-simple-checkbox',
  functional: true,
  directives: {
    ripple: directives_ripple["a" /* default */]
  },
  props: { ...colorable["a" /* default */].options.props,
    ...themeable["a" /* default */].options.props,
    disabled: Boolean,
    ripple: {
      type: Boolean,
      default: true
    },
    value: Boolean,
    indeterminate: Boolean,
    indeterminateIcon: {
      type: String,
      default: '$checkboxIndeterminate'
    },
    onIcon: {
      type: String,
      default: '$checkboxOn'
    },
    offIcon: {
      type: String,
      default: '$checkboxOff'
    }
  },

  render(h, {
    props,
    data,
    listeners
  }) {
    const children = [];
    let icon = props.offIcon;
    if (props.indeterminate) icon = props.indeterminateIcon;else if (props.value) icon = props.onIcon;
    children.push(h(VIcon["a" /* default */], colorable["a" /* default */].options.methods.setTextColor(props.value && props.color, {
      props: {
        disabled: props.disabled,
        dark: props.dark,
        light: props.light
      }
    }), icon));

    if (props.ripple && !props.disabled) {
      const ripple = h('div', colorable["a" /* default */].options.methods.setTextColor(props.color, {
        staticClass: 'v-input--selection-controls__ripple',
        directives: [{
          name: 'ripple',
          value: {
            center: true
          }
        }]
      }));
      children.push(ripple);
    }

    return h('div', Object(mergeData["a" /* default */])(data, {
      class: {
        'v-simple-checkbox': true,
        'v-simple-checkbox--disabled': props.disabled
      },
      on: {
        click: e => {
          e.stopPropagation();

          if (data.on && data.on.input && !props.disabled) {
            Object(helpers["y" /* wrapInArray */])(data.on.input).forEach(f => f(!props.value));
          }
        }
      }
    }), [h('div', {
      staticClass: 'v-input--selection-controls__input'
    }, children)]);
  }

}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VDivider/index.js
var VDivider = __webpack_require__(921);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VSubheader/VSubheader.sass
var VSubheader = __webpack_require__(926);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/mixins.js
var mixins = __webpack_require__(2);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VSubheader/VSubheader.js
// Styles
 // Mixins



/* harmony default export */ var VSubheader_VSubheader = (Object(mixins["a" /* default */])(themeable["a" /* default */]
/* @vue/component */
).extend({
  name: 'v-subheader',
  props: {
    inset: Boolean
  },

  render(h) {
    return h('div', {
      staticClass: 'v-subheader',
      class: {
        'v-subheader--inset': this.inset,
        ...this.themeClasses
      },
      attrs: this.$attrs,
      on: this.$listeners
    }, this.$slots.default);
  }

}));
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VSubheader/index.js


/* harmony default export */ var components_VSubheader = (VSubheader_VSubheader);
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItem.js
var VListItem = __webpack_require__(828);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItemAction.js
var VListItemAction = __webpack_require__(904);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/index.js + 4 modules
var VList = __webpack_require__(499);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VList.js
var VList_VList = __webpack_require__(831);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VSelect/VSelectList.js
// Components



 // Directives

 // Mixins


 // Helpers

 // Types


/* @vue/component */

/* harmony default export */ var VSelectList = (Object(mixins["a" /* default */])(colorable["a" /* default */], themeable["a" /* default */]).extend({
  name: 'v-select-list',
  // https://github.com/vuejs/vue/issues/6872
  directives: {
    ripple: directives_ripple["a" /* default */]
  },
  props: {
    action: Boolean,
    dense: Boolean,
    hideSelected: Boolean,
    items: {
      type: Array,
      default: () => []
    },
    itemDisabled: {
      type: [String, Array, Function],
      default: 'disabled'
    },
    itemText: {
      type: [String, Array, Function],
      default: 'text'
    },
    itemValue: {
      type: [String, Array, Function],
      default: 'value'
    },
    noDataText: String,
    noFilter: Boolean,
    searchInput: null,
    selectedItems: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    parsedItems() {
      return this.selectedItems.map(item => this.getValue(item));
    },

    tileActiveClass() {
      return Object.keys(this.setTextColor(this.color).class || {}).join(' ');
    },

    staticNoDataTile() {
      const tile = {
        attrs: {
          role: undefined
        },
        on: {
          mousedown: e => e.preventDefault()
        }
      };
      return this.$createElement(VListItem["a" /* default */], tile, [this.genTileContent(this.noDataText)]);
    }

  },
  methods: {
    genAction(item, inputValue) {
      return this.$createElement(VListItemAction["a" /* default */], [this.$createElement(VCheckbox_VSimpleCheckbox, {
        props: {
          color: this.color,
          value: inputValue,
          ripple: false
        },
        on: {
          input: () => this.$emit('select', item)
        }
      })]);
    },

    genDivider(props) {
      return this.$createElement(VDivider["a" /* default */], {
        props
      });
    },

    genFilteredText(text) {
      text = text || '';
      if (!this.searchInput || this.noFilter) return Object(helpers["i" /* escapeHTML */])(text);
      const {
        start,
        middle,
        end
      } = this.getMaskedCharacters(text);
      return `${Object(helpers["i" /* escapeHTML */])(start)}${this.genHighlight(middle)}${Object(helpers["i" /* escapeHTML */])(end)}`;
    },

    genHeader(props) {
      return this.$createElement(components_VSubheader, {
        props
      }, props.header);
    },

    genHighlight(text) {
      return `<span class="v-list-item__mask">${Object(helpers["i" /* escapeHTML */])(text)}</span>`;
    },

    getMaskedCharacters(text) {
      const searchInput = (this.searchInput || '').toString().toLocaleLowerCase();
      const index = text.toLocaleLowerCase().indexOf(searchInput);
      if (index < 0) return {
        start: text,
        middle: '',
        end: ''
      };
      const start = text.slice(0, index);
      const middle = text.slice(index, index + searchInput.length);
      const end = text.slice(index + searchInput.length);
      return {
        start,
        middle,
        end
      };
    },

    genTile({
      item,
      index,
      disabled = null,
      value = false
    }) {
      if (!value) value = this.hasItem(item);

      if (item === Object(item)) {
        disabled = disabled !== null ? disabled : this.getDisabled(item);
      }

      const tile = {
        attrs: {
          // Default behavior in list does not
          // contain aria-selected by default
          'aria-selected': String(value),
          id: `list-item-${this._uid}-${index}`,
          role: 'option'
        },
        on: {
          mousedown: e => {
            // Prevent onBlur from being called
            e.preventDefault();
          },
          click: () => disabled || this.$emit('select', item)
        },
        props: {
          activeClass: this.tileActiveClass,
          disabled,
          ripple: true,
          inputValue: value
        }
      };

      if (!this.$scopedSlots.item) {
        return this.$createElement(VListItem["a" /* default */], tile, [this.action && !this.hideSelected && this.items.length > 0 ? this.genAction(item, value) : null, this.genTileContent(item, index)]);
      }

      const parent = this;
      const scopedSlot = this.$scopedSlots.item({
        parent,
        item,
        attrs: { ...tile.attrs,
          ...tile.props
        },
        on: tile.on
      });
      return this.needsTile(scopedSlot) ? this.$createElement(VListItem["a" /* default */], tile, scopedSlot) : scopedSlot;
    },

    genTileContent(item, index = 0) {
      const innerHTML = this.genFilteredText(this.getText(item));
      return this.$createElement(VList["a" /* VListItemContent */], [this.$createElement(VList["c" /* VListItemTitle */], {
        domProps: {
          innerHTML
        }
      })]);
    },

    hasItem(item) {
      return this.parsedItems.indexOf(this.getValue(item)) > -1;
    },

    needsTile(slot) {
      return slot.length !== 1 || slot[0].componentOptions == null || slot[0].componentOptions.Ctor.options.name !== 'v-list-item';
    },

    getDisabled(item) {
      return Boolean(Object(helpers["m" /* getPropertyFromItem */])(item, this.itemDisabled, false));
    },

    getText(item) {
      return String(Object(helpers["m" /* getPropertyFromItem */])(item, this.itemText, item));
    },

    getValue(item) {
      return Object(helpers["m" /* getPropertyFromItem */])(item, this.itemValue, this.getText(item));
    }

  },

  render() {
    const children = [];
    const itemsLength = this.items.length;

    for (let index = 0; index < itemsLength; index++) {
      const item = this.items[index];
      if (this.hideSelected && this.hasItem(item)) continue;
      if (item == null) children.push(this.genTile({
        item,
        index
      }));else if (item.header) children.push(this.genHeader(item));else if (item.divider) children.push(this.genDivider(item));else children.push(this.genTile({
        item,
        index
      }));
    }

    children.length || children.push(this.$slots['no-data'] || this.staticNoDataTile);
    this.$slots['prepend-item'] && children.unshift(this.$slots['prepend-item']);
    this.$slots['append-item'] && children.push(this.$slots['append-item']);
    return this.$createElement(VList_VList["a" /* default */], {
      staticClass: 'v-select-list',
      class: this.themeClasses,
      attrs: {
        role: 'listbox',
        tabindex: -1
      },
      props: {
        dense: this.dense
      }
    }, children);
  }

}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VInput/index.js + 3 modules
var VInput = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 4 modules
var VTextField_VTextField = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/comparable/index.js
var comparable = __webpack_require__(903);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/dependent/index.js
var dependent = __webpack_require__(30);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/mixins/filterable/index.js

/* @vue/component */

/* harmony default export */ var filterable = (external_vue_default.a.extend({
  name: 'filterable',
  props: {
    noDataText: {
      type: String,
      default: '$vuetify.noDataText'
    }
  }
}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/click-outside/index.js
var click_outside = __webpack_require__(31);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/console.js
var console = __webpack_require__(3);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VSelect/VSelect.js














// Styles

 // Components



 // Extensions


 // Mixins



 // Directives

 // Utilities



 // Types


const defaultMenuProps = {
  closeOnClick: false,
  closeOnContentClick: false,
  disableKeys: true,
  openOnClick: false,
  maxHeight: 304
}; // Types

const baseMixins = Object(mixins["a" /* default */])(VTextField_VTextField["a" /* default */], comparable["a" /* default */], dependent["a" /* default */], filterable);
/* @vue/component */

/* harmony default export */ var VSelect_VSelect = __webpack_exports__["a"] = (baseMixins.extend().extend({
  name: 'v-select',
  directives: {
    ClickOutside: click_outside["a" /* default */]
  },
  props: {
    appendIcon: {
      type: String,
      default: '$dropdown'
    },
    attach: {
      type: null,
      default: false
    },
    cacheItems: Boolean,
    chips: Boolean,
    clearable: Boolean,
    deletableChips: Boolean,
    disableLookup: Boolean,
    eager: Boolean,
    hideSelected: Boolean,
    items: {
      type: Array,
      default: () => []
    },
    itemColor: {
      type: String,
      default: 'primary'
    },
    itemDisabled: {
      type: [String, Array, Function],
      default: 'disabled'
    },
    itemText: {
      type: [String, Array, Function],
      default: 'text'
    },
    itemValue: {
      type: [String, Array, Function],
      default: 'value'
    },
    menuProps: {
      type: [String, Array, Object],
      default: () => defaultMenuProps
    },
    multiple: Boolean,
    openOnClear: Boolean,
    returnObject: Boolean,
    smallChips: Boolean
  },

  data() {
    return {
      cachedItems: this.cacheItems ? this.items : [],
      menuIsBooted: false,
      isMenuActive: false,
      lastItem: 20,
      // As long as a value is defined, show it
      // Otherwise, check if multiple
      // to determine which default to provide
      lazyValue: this.value !== undefined ? this.value : this.multiple ? [] : undefined,
      selectedIndex: -1,
      selectedItems: [],
      keyboardLookupPrefix: '',
      keyboardLookupLastTime: 0
    };
  },

  computed: {
    /* All items that the select has */
    allItems() {
      return this.filterDuplicates(this.cachedItems.concat(this.items));
    },

    classes() {
      return { ...VTextField_VTextField["a" /* default */].options.computed.classes.call(this),
        'v-select': true,
        'v-select--chips': this.hasChips,
        'v-select--chips--small': this.smallChips,
        'v-select--is-menu-active': this.isMenuActive,
        'v-select--is-multi': this.multiple
      };
    },

    /* Used by other components to overwrite */
    computedItems() {
      return this.allItems;
    },

    computedOwns() {
      return `list-${this._uid}`;
    },

    computedCounterValue() {
      const value = this.multiple ? this.selectedItems : (this.getText(this.selectedItems[0]) || '').toString();

      if (typeof this.counterValue === 'function') {
        return this.counterValue(value);
      }

      return value.length;
    },

    directives() {
      return this.isFocused ? [{
        name: 'click-outside',
        value: {
          handler: this.blur,
          closeConditional: this.closeConditional,
          include: () => this.getOpenDependentElements()
        }
      }] : undefined;
    },

    dynamicHeight() {
      return 'auto';
    },

    hasChips() {
      return this.chips || this.smallChips;
    },

    hasSlot() {
      return Boolean(this.hasChips || this.$scopedSlots.selection);
    },

    isDirty() {
      return this.selectedItems.length > 0;
    },

    listData() {
      const scopeId = this.$vnode && this.$vnode.context.$options._scopeId;
      const attrs = scopeId ? {
        [scopeId]: true
      } : {};
      return {
        attrs: { ...attrs,
          id: this.computedOwns
        },
        props: {
          action: this.multiple,
          color: this.itemColor,
          dense: this.dense,
          hideSelected: this.hideSelected,
          items: this.virtualizedItems,
          itemDisabled: this.itemDisabled,
          itemText: this.itemText,
          itemValue: this.itemValue,
          noDataText: this.$vuetify.lang.t(this.noDataText),
          selectedItems: this.selectedItems
        },
        on: {
          select: this.selectItem
        },
        scopedSlots: {
          item: this.$scopedSlots.item
        }
      };
    },

    staticList() {
      if (this.$slots['no-data'] || this.$slots['prepend-item'] || this.$slots['append-item']) {
        Object(console["b" /* consoleError */])('assert: staticList should not be called if slots are used');
      }

      return this.$createElement(VSelectList, this.listData);
    },

    virtualizedItems() {
      return this.$_menuProps.auto ? this.computedItems : this.computedItems.slice(0, this.lastItem);
    },

    menuCanShow: () => true,

    $_menuProps() {
      let normalisedProps = typeof this.menuProps === 'string' ? this.menuProps.split(',') : this.menuProps;

      if (Array.isArray(normalisedProps)) {
        normalisedProps = normalisedProps.reduce((acc, p) => {
          acc[p.trim()] = true;
          return acc;
        }, {});
      }

      return { ...defaultMenuProps,
        eager: this.eager,
        value: this.menuCanShow && this.isMenuActive,
        nudgeBottom: normalisedProps.offsetY ? 1 : 0,
        ...normalisedProps
      };
    }

  },
  watch: {
    internalValue(val) {
      this.initialValue = val;
      this.setSelectedItems();
    },

    isMenuActive(val) {
      window.setTimeout(() => this.onMenuActiveChange(val));
    },

    items: {
      immediate: true,

      handler(val) {
        if (this.cacheItems) {
          // Breaks vue-test-utils if
          // this isn't calculated
          // on the next tick
          this.$nextTick(() => {
            this.cachedItems = this.filterDuplicates(this.cachedItems.concat(val));
          });
        }

        this.setSelectedItems();
      }

    }
  },
  methods: {
    /** @public */
    blur(e) {
      VTextField_VTextField["a" /* default */].options.methods.blur.call(this, e);
      this.isMenuActive = false;
      this.isFocused = false;
      this.selectedIndex = -1;
      this.setMenuIndex(-1);
    },

    /** @public */
    activateMenu() {
      if (!this.isInteractive || this.isMenuActive) return;
      this.isMenuActive = true;
    },

    clearableCallback() {
      this.setValue(this.multiple ? [] : null);
      this.setMenuIndex(-1);
      this.$nextTick(() => this.$refs.input && this.$refs.input.focus());
      if (this.openOnClear) this.isMenuActive = true;
    },

    closeConditional(e) {
      if (!this.isMenuActive) return true;
      return !this._isDestroyed && ( // Click originates from outside the menu content
      // Multiple selects don't close when an item is clicked
      !this.getContent() || !this.getContent().contains(e.target)) && // Click originates from outside the element
      this.$el && !this.$el.contains(e.target) && e.target !== this.$el;
    },

    filterDuplicates(arr) {
      const uniqueValues = new Map();

      for (let index = 0; index < arr.length; ++index) {
        const item = arr[index]; // Do not deduplicate headers or dividers (#12517)

        if (item.header || item.divider) {
          uniqueValues.set(item, item);
          continue;
        }

        const val = this.getValue(item); // TODO: comparator

        !uniqueValues.has(val) && uniqueValues.set(val, item);
      }

      return Array.from(uniqueValues.values());
    },

    findExistingIndex(item) {
      const itemValue = this.getValue(item);
      return (this.internalValue || []).findIndex(i => this.valueComparator(this.getValue(i), itemValue));
    },

    getContent() {
      return this.$refs.menu && this.$refs.menu.$refs.content;
    },

    genChipSelection(item, index) {
      const isDisabled = this.isDisabled || this.getDisabled(item);
      const isInteractive = !isDisabled && this.isInteractive;
      return this.$createElement(VChip["a" /* default */], {
        staticClass: 'v-chip--select',
        attrs: {
          tabindex: -1
        },
        props: {
          close: this.deletableChips && isInteractive,
          disabled: isDisabled,
          inputValue: index === this.selectedIndex,
          small: this.smallChips
        },
        on: {
          click: e => {
            if (!isInteractive) return;
            e.stopPropagation();
            this.selectedIndex = index;
          },
          'click:close': () => this.onChipInput(item)
        },
        key: JSON.stringify(this.getValue(item))
      }, this.getText(item));
    },

    genCommaSelection(item, index, last) {
      const color = index === this.selectedIndex && this.computedColor;
      const isDisabled = this.isDisabled || this.getDisabled(item);
      return this.$createElement('div', this.setTextColor(color, {
        staticClass: 'v-select__selection v-select__selection--comma',
        class: {
          'v-select__selection--disabled': isDisabled
        },
        key: JSON.stringify(this.getValue(item))
      }), `${this.getText(item)}${last ? '' : ', '}`);
    },

    genDefaultSlot() {
      const selections = this.genSelections();
      const input = this.genInput(); // If the return is an empty array
      // push the input

      if (Array.isArray(selections)) {
        selections.push(input); // Otherwise push it into children
      } else {
        selections.children = selections.children || [];
        selections.children.push(input);
      }

      return [this.genFieldset(), this.$createElement('div', {
        staticClass: 'v-select__slot',
        directives: this.directives
      }, [this.genLabel(), this.prefix ? this.genAffix('prefix') : null, selections, this.suffix ? this.genAffix('suffix') : null, this.genClearIcon(), this.genIconSlot(), this.genHiddenInput()]), this.genMenu(), this.genProgress()];
    },

    genIcon(type, cb, extraData) {
      const icon = VInput["a" /* default */].options.methods.genIcon.call(this, type, cb, extraData);

      if (type === 'append') {
        // Don't allow the dropdown icon to be focused
        icon.children[0].data = Object(mergeData["a" /* default */])(icon.children[0].data, {
          attrs: {
            tabindex: icon.children[0].componentOptions.listeners && '-1',
            'aria-hidden': 'true',
            'aria-label': undefined
          }
        });
      }

      return icon;
    },

    genInput() {
      const input = VTextField_VTextField["a" /* default */].options.methods.genInput.call(this);
      delete input.data.attrs.name;
      input.data = Object(mergeData["a" /* default */])(input.data, {
        domProps: {
          value: null
        },
        attrs: {
          readonly: true,
          type: 'text',
          'aria-readonly': String(this.isReadonly),
          'aria-activedescendant': Object(helpers["l" /* getObjectValueByPath */])(this.$refs.menu, 'activeTile.id'),
          autocomplete: Object(helpers["l" /* getObjectValueByPath */])(input.data, 'attrs.autocomplete', 'off'),
          placeholder: !this.isDirty && (this.isFocused || !this.hasLabel) ? this.placeholder : undefined
        },
        on: {
          keypress: this.onKeyPress
        }
      });
      return input;
    },

    genHiddenInput() {
      return this.$createElement('input', {
        domProps: {
          value: this.lazyValue
        },
        attrs: {
          type: 'hidden',
          name: this.attrs$.name
        }
      });
    },

    genInputSlot() {
      const render = VTextField_VTextField["a" /* default */].options.methods.genInputSlot.call(this);
      render.data.attrs = { ...render.data.attrs,
        role: 'button',
        'aria-haspopup': 'listbox',
        'aria-expanded': String(this.isMenuActive),
        'aria-owns': this.computedOwns
      };
      return render;
    },

    genList() {
      // If there's no slots, we can use a cached VNode to improve performance
      if (this.$slots['no-data'] || this.$slots['prepend-item'] || this.$slots['append-item']) {
        return this.genListWithSlot();
      } else {
        return this.staticList;
      }
    },

    genListWithSlot() {
      const slots = ['prepend-item', 'no-data', 'append-item'].filter(slotName => this.$slots[slotName]).map(slotName => this.$createElement('template', {
        slot: slotName
      }, this.$slots[slotName])); // Requires destructuring due to Vue
      // modifying the `on` property when passed
      // as a referenced object

      return this.$createElement(VSelectList, { ...this.listData
      }, slots);
    },

    genMenu() {
      const props = this.$_menuProps;
      props.activator = this.$refs['input-slot']; // Attach to root el so that
      // menu covers prepend/append icons

      if ( // TODO: make this a computed property or helper or something
      this.attach === '' || // If used as a boolean prop (<v-menu attach>)
      this.attach === true || // If bound to a boolean (<v-menu :attach="true">)
      this.attach === 'attach' // If bound as boolean prop in pug (v-menu(attach))
      ) {
        props.attach = this.$el;
      } else {
        props.attach = this.attach;
      }

      return this.$createElement(VMenu["a" /* default */], {
        attrs: {
          role: undefined
        },
        props,
        on: {
          input: val => {
            this.isMenuActive = val;
            this.isFocused = val;
          },
          scroll: this.onScroll
        },
        ref: 'menu'
      }, [this.genList()]);
    },

    genSelections() {
      let length = this.selectedItems.length;
      const children = new Array(length);
      let genSelection;

      if (this.$scopedSlots.selection) {
        genSelection = this.genSlotSelection;
      } else if (this.hasChips) {
        genSelection = this.genChipSelection;
      } else {
        genSelection = this.genCommaSelection;
      }

      while (length--) {
        children[length] = genSelection(this.selectedItems[length], length, length === children.length - 1);
      }

      return this.$createElement('div', {
        staticClass: 'v-select__selections'
      }, children);
    },

    genSlotSelection(item, index) {
      return this.$scopedSlots.selection({
        attrs: {
          class: 'v-chip--select'
        },
        parent: this,
        item,
        index,
        select: e => {
          e.stopPropagation();
          this.selectedIndex = index;
        },
        selected: index === this.selectedIndex,
        disabled: !this.isInteractive
      });
    },

    getMenuIndex() {
      return this.$refs.menu ? this.$refs.menu.listIndex : -1;
    },

    getDisabled(item) {
      return Object(helpers["m" /* getPropertyFromItem */])(item, this.itemDisabled, false);
    },

    getText(item) {
      return Object(helpers["m" /* getPropertyFromItem */])(item, this.itemText, item);
    },

    getValue(item) {
      return Object(helpers["m" /* getPropertyFromItem */])(item, this.itemValue, this.getText(item));
    },

    onBlur(e) {
      e && this.$emit('blur', e);
    },

    onChipInput(item) {
      if (this.multiple) this.selectItem(item);else this.setValue(null); // If all items have been deleted,
      // open `v-menu`

      if (this.selectedItems.length === 0) {
        this.isMenuActive = true;
      } else {
        this.isMenuActive = false;
      }

      this.selectedIndex = -1;
    },

    onClick(e) {
      if (!this.isInteractive) return;

      if (!this.isAppendInner(e.target)) {
        this.isMenuActive = true;
      }

      if (!this.isFocused) {
        this.isFocused = true;
        this.$emit('focus');
      }

      this.$emit('click', e);
    },

    onEscDown(e) {
      e.preventDefault();

      if (this.isMenuActive) {
        e.stopPropagation();
        this.isMenuActive = false;
      }
    },

    onKeyPress(e) {
      if (this.multiple || !this.isInteractive || this.disableLookup) return;
      const KEYBOARD_LOOKUP_THRESHOLD = 1000; // milliseconds

      const now = performance.now();

      if (now - this.keyboardLookupLastTime > KEYBOARD_LOOKUP_THRESHOLD) {
        this.keyboardLookupPrefix = '';
      }

      this.keyboardLookupPrefix += e.key.toLowerCase();
      this.keyboardLookupLastTime = now;
      const index = this.allItems.findIndex(item => {
        const text = (this.getText(item) || '').toString();
        return text.toLowerCase().startsWith(this.keyboardLookupPrefix);
      });
      const item = this.allItems[index];

      if (index !== -1) {
        this.lastItem = Math.max(this.lastItem, index + 5);
        this.setValue(this.returnObject ? item : this.getValue(item));
        this.$nextTick(() => this.$refs.menu.getTiles());
        setTimeout(() => this.setMenuIndex(index));
      }
    },

    onKeyDown(e) {
      if (this.isReadonly && e.keyCode !== helpers["s" /* keyCodes */].tab) return;
      const keyCode = e.keyCode;
      const menu = this.$refs.menu; // If enter, space, open menu

      if ([helpers["s" /* keyCodes */].enter, helpers["s" /* keyCodes */].space].includes(keyCode)) this.activateMenu();
      this.$emit('keydown', e);
      if (!menu) return; // If menu is active, allow default
      // listIndex change from menu

      if (this.isMenuActive && keyCode !== helpers["s" /* keyCodes */].tab) {
        this.$nextTick(() => {
          menu.changeListIndex(e);
          this.$emit('update:list-index', menu.listIndex);
        });
      } // If menu is not active, up/down/home/<USER>
      // one of 2 things. If multiple, opens the
      // menu, if not, will cycle through all
      // available options


      if (!this.isMenuActive && [helpers["s" /* keyCodes */].up, helpers["s" /* keyCodes */].down, helpers["s" /* keyCodes */].home, helpers["s" /* keyCodes */].end].includes(keyCode)) return this.onUpDown(e); // If escape deactivate the menu

      if (keyCode === helpers["s" /* keyCodes */].esc) return this.onEscDown(e); // If tab - select item or close menu

      if (keyCode === helpers["s" /* keyCodes */].tab) return this.onTabDown(e); // If space preventDefault

      if (keyCode === helpers["s" /* keyCodes */].space) return this.onSpaceDown(e);
    },

    onMenuActiveChange(val) {
      // If menu is closing and mulitple
      // or menuIndex is already set
      // skip menu index recalculation
      if (this.multiple && !val || this.getMenuIndex() > -1) return;
      const menu = this.$refs.menu;
      if (!menu || !this.isDirty) return; // When menu opens, set index of first active item

      for (let i = 0; i < menu.tiles.length; i++) {
        if (menu.tiles[i].getAttribute('aria-selected') === 'true') {
          this.setMenuIndex(i);
          break;
        }
      }
    },

    onMouseUp(e) {
      // eslint-disable-next-line sonarjs/no-collapsible-if
      if (this.hasMouseDown && e.which !== 3 && this.isInteractive) {
        // If append inner is present
        // and the target is itself
        // or inside, toggle menu
        if (this.isAppendInner(e.target)) {
          this.$nextTick(() => this.isMenuActive = !this.isMenuActive);
        }
      }

      VTextField_VTextField["a" /* default */].options.methods.onMouseUp.call(this, e);
    },

    onScroll() {
      if (!this.isMenuActive) {
        requestAnimationFrame(() => this.getContent().scrollTop = 0);
      } else {
        if (this.lastItem > this.computedItems.length) return;
        const showMoreItems = this.getContent().scrollHeight - (this.getContent().scrollTop + this.getContent().clientHeight) < 200;

        if (showMoreItems) {
          this.lastItem += 20;
        }
      }
    },

    onSpaceDown(e) {
      e.preventDefault();
    },

    onTabDown(e) {
      const menu = this.$refs.menu;
      if (!menu) return;
      const activeTile = menu.activeTile; // An item that is selected by
      // menu-index should toggled

      if (!this.multiple && activeTile && this.isMenuActive) {
        e.preventDefault();
        e.stopPropagation();
        activeTile.click();
      } else {
        // If we make it here,
        // the user has no selected indexes
        // and is probably tabbing out
        this.blur(e);
      }
    },

    onUpDown(e) {
      const menu = this.$refs.menu;
      if (!menu) return;
      e.preventDefault(); // Multiple selects do not cycle their value
      // when pressing up or down, instead activate
      // the menu

      if (this.multiple) return this.activateMenu();
      const keyCode = e.keyCode; // Cycle through available values to achieve
      // select native behavior

      menu.isBooted = true;
      window.requestAnimationFrame(() => {
        menu.getTiles();
        if (!menu.hasClickableTiles) return this.activateMenu();

        switch (keyCode) {
          case helpers["s" /* keyCodes */].up:
            menu.prevTile();
            break;

          case helpers["s" /* keyCodes */].down:
            menu.nextTile();
            break;

          case helpers["s" /* keyCodes */].home:
            menu.firstTile();
            break;

          case helpers["s" /* keyCodes */].end:
            menu.lastTile();
            break;
        }

        this.selectItem(this.allItems[this.getMenuIndex()]);
      });
    },

    selectItem(item) {
      if (!this.multiple) {
        this.setValue(this.returnObject ? item : this.getValue(item));
        this.isMenuActive = false;
      } else {
        const internalValue = (this.internalValue || []).slice();
        const i = this.findExistingIndex(item);
        i !== -1 ? internalValue.splice(i, 1) : internalValue.push(item);
        this.setValue(internalValue.map(i => {
          return this.returnObject ? i : this.getValue(i);
        })); // When selecting multiple
        // adjust menu after each
        // selection

        this.$nextTick(() => {
          this.$refs.menu && this.$refs.menu.updateDimensions();
        }); // We only need to reset list index for multiple
        // to keep highlight when an item is toggled
        // on and off

        if (!this.multiple) return;
        const listIndex = this.getMenuIndex();
        this.setMenuIndex(-1); // There is no item to re-highlight
        // when selections are hidden

        if (this.hideSelected) return;
        this.$nextTick(() => this.setMenuIndex(listIndex));
      }
    },

    setMenuIndex(index) {
      this.$refs.menu && (this.$refs.menu.listIndex = index);
    },

    setSelectedItems() {
      const selectedItems = [];
      const values = !this.multiple || !Array.isArray(this.internalValue) ? [this.internalValue] : this.internalValue;

      for (const value of values) {
        const index = this.allItems.findIndex(v => this.valueComparator(this.getValue(v), this.getValue(value)));

        if (index > -1) {
          selectedItems.push(this.allItems[index]);
        }
      }

      this.selectedItems = selectedItems;
    },

    setValue(value) {
      const oldValue = this.internalValue;
      this.internalValue = value;
      value !== oldValue && this.$emit('change', value);
    },

    isAppendInner(target) {
      // return true if append inner is present
      // and the target is itself or inside
      const appendInner = this.$refs['append-inner'];
      return appendInner && (appendInner === target || appendInner.contains(target));
    }

  }
}));

/***/ }),

/***/ 969:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(970);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("48751daa", content, true)

/***/ }),

/***/ 970:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-expansion-panels .v-expansion-panel{background-color:#fff;color:rgba(0,0,0,.87)}.theme--light.v-expansion-panels .v-expansion-panel--disabled{color:rgba(0,0,0,.38)}.theme--light.v-expansion-panels .v-expansion-panel:not(:first-child):after{border-color:rgba(0,0,0,.12)}.theme--light.v-expansion-panels .v-expansion-panel-header .v-expansion-panel-header__icon .v-icon{color:rgba(0,0,0,.54)}.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:hover:before{opacity:.04}.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:before,.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:hover:before,.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:focus:before{opacity:.12}.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:focus:before{opacity:.16}.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:hover:before{opacity:.04}.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:before,.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:hover:before,.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:focus:before{opacity:.12}.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:focus:before{opacity:.16}.theme--dark.v-expansion-panels .v-expansion-panel{background-color:#1e1e1e;color:#fff}.theme--dark.v-expansion-panels .v-expansion-panel--disabled{color:hsla(0,0%,100%,.5)}.theme--dark.v-expansion-panels .v-expansion-panel:not(:first-child):after{border-color:hsla(0,0%,100%,.12)}.theme--dark.v-expansion-panels .v-expansion-panel-header .v-expansion-panel-header__icon .v-icon{color:#fff}.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:hover:before{opacity:.08}.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:before,.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:hover:before,.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:focus:before{opacity:.24}.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:focus:before{opacity:.32}.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:hover:before{opacity:.08}.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:before,.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:hover:before,.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:focus:before{opacity:.24}.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:focus:before{opacity:.32}.v-expansion-panels{border-radius:8px;display:flex;flex-wrap:wrap;justify-content:center;list-style-type:none;padding:0;width:100%;z-index:1}.v-expansion-panels>*{cursor:auto}.v-expansion-panels>:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.v-expansion-panels>:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--active{border-radius:8px}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--active+.v-expansion-panel{border-top-left-radius:8px;border-top-right-radius:8px}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--next-active{border-bottom-left-radius:8px;border-bottom-right-radius:8px}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--next-active .v-expansion-panel-header{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.v-expansion-panel{flex:1 0 100%;max-width:100%;position:relative;transition:.3s cubic-bezier(.25,.8,.5,1)}.v-expansion-panel:before{border-radius:inherit;bottom:0;content:\"\";left:0;position:absolute;right:0;top:0;z-index:-1;transition:box-shadow .28s cubic-bezier(.4,0,.2,1);will-change:box-shadow;box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-expansion-panel:not(:first-child):after{border-top:thin solid;content:\"\";left:0;position:absolute;right:0;top:0;transition:border-color .2s cubic-bezier(.4,0,.2,1),opacity .2s cubic-bezier(.4,0,.2,1)}.v-expansion-panel--disabled .v-expansion-panel-header{pointer-events:none}.v-expansion-panel--active+.v-expansion-panel,.v-expansion-panel--active:not(:first-child){margin-top:16px}.v-expansion-panel--active+.v-expansion-panel:after,.v-expansion-panel--active:not(:first-child):after{opacity:0}.v-expansion-panel--active>.v-expansion-panel-header{min-height:64px}.v-expansion-panel--active>.v-expansion-panel-header--active .v-expansion-panel-header__icon:not(.v-expansion-panel-header__icon--disable-rotate) .v-icon{transform:rotate(-180deg)}.v-expansion-panel-header__icon{display:inline-flex;margin-bottom:-4px;margin-top:-4px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-expansion-panel-header__icon{margin-left:auto}.v-application--is-rtl .v-expansion-panel-header__icon{margin-right:auto}.v-expansion-panel-header{align-items:center;border-top-left-radius:inherit;border-top-right-radius:inherit;display:flex;font-size:.9375rem;line-height:1;min-height:64px;outline:none;padding:20px 24px;position:relative;transition:min-height .3s cubic-bezier(.25,.8,.5,1);width:100%}.v-application--is-ltr .v-expansion-panel-header{text-align:left}.v-application--is-rtl .v-expansion-panel-header{text-align:right}.v-expansion-panel-header:not(.v-expansion-panel-header--mousedown):focus:before{opacity:.12}.v-expansion-panel-header:before{background-color:currentColor;border-radius:inherit;bottom:0;content:\"\";left:0;opacity:0;pointer-events:none;position:absolute;right:0;top:0;transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-expansion-panel-header>:not(.v-expansion-panel-header__icon){flex:1 1 auto}.v-expansion-panel-content{display:flex}.v-expansion-panel-content__wrap{padding:0 24px 20px;flex:1 1 auto;max-width:100%}.v-expansion-panels--accordion>.v-expansion-panel{margin-top:0}.v-expansion-panels--accordion>.v-expansion-panel:after{opacity:1}.v-expansion-panels--popout>.v-expansion-panel{max-width:calc(100% - 32px)}.v-expansion-panels--popout>.v-expansion-panel--active{max-width:calc(100% + 16px)}.v-expansion-panels--inset>.v-expansion-panel{max-width:100%}.v-expansion-panels--inset>.v-expansion-panel--active{max-width:calc(100% - 32px)}.v-expansion-panels--flat>.v-expansion-panel:after{border-top:none}.v-expansion-panels--flat>.v-expansion-panel:before{box-shadow:0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)}.v-expansion-panels--tile,.v-expansion-panels--tile>.v-expansion-panel:before{border-radius:0}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 993:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1049);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("1d04e335", content, true, context)
};

/***/ })

};;
//# sourceMappingURL=index.js.map