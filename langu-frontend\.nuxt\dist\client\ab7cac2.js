(window.webpackJsonp=window.webpackJsonp||[]).push([[4,58,84,140],{1413:function(t,e,n){"use strict";n.r(e);n(23),n(20),n(80),n(63);var o=n(859),l=n(1437),d=n(1522),r=n(1685),c={name:"LessonItem",components:{UserStatus:d.default,MessageDialog:r.default},mixins:[l.a],props:{item:{type:Object,required:!0},userStatuses:{type:Object,default:function(){return{}}}},data:function(){return{getPrice:o.getPrice,isLessonDialogShown:!1,dialogType:null,isShownMessageDialog:!1,studentInfo:null}},computed:{isTeacher:function(){return this.$store.getters["user/isTeacher"]},isStudent:function(){return this.$store.getters["user/isStudent"]},userId:function(){return this.item[this.isTeacher?"studentId":"teacherId"]},userIsDeleted:function(){return this.item.userIsDeleted},userName:function(){return this.userIsDeleted?this.$t("deleted_user"):"".concat(this.item.userFirstName," ").concat(this.item.userLastName)},currencyIsoCode:function(){return this.$store.state.currency.item.isoCode},currencySymbol:function(){return this.$store.getters["currency/currentCurrencySymbol"]},status:function(){var t,e="offline";return Object.prototype.hasOwnProperty.call(this.userStatuses,null===(t=this.userId)||void 0===t?void 0:t.toString())&&(e=this.userStatuses[this.userId]),e},isPast:function(){return"past"===this.item.type},isUnscheduled:function(){return"unscheduled"===this.item.type},timeZone:function(){return this.$store.getters["user/timeZone"]},startDate:function(){return this.$dayjs(this.item.startDate).tz(this.timeZone)},startTime:function(){return this.startDate.format("LT")},isUnscheduleButtonHidden:function(){return this.isUnscheduled||this.item.isFinished||this.isStudent&&(this.isPast||this.$dayjs().add(1,"day").isAfter(this.startDate,"minute"))},teacherLink:function(){return this.isStudent&&!this.item.userIsDeleted&&this.item.teacherUsername?"/teacher/".concat(this.item.teacherUsername):null}},methods:{showMessageDialog:function(){var t=this;this.$store.dispatch("message/checkConversation",this.userId).then((function(e){e.threadId?(t.$store.dispatch("loadingStart"),t.$router.push({path:"/user/messages/".concat(e.threadId,"/view")})):t.isShownMessageDialog=!0}))},cancelClickHandler:function(){var t=this;this.$store.dispatch("loadingStart"),this.$store.dispatch("lesson/cancelLesson",this.item.lessonId).then((function(){location.reload()})).catch((function(e){t.$store.dispatch("loadingStop"),t.$store.dispatch("snackbar/error"),console.info(e)}))},studentInfoClickHandler:function(){var t=this;this.$store.dispatch("lesson/getStudentInfo",this.item.studentId).then((function(e){t.studentInfo=e,t.dialogType="studentInfoDialog",t.showDialog()}))},showInitializeDialog:function(){this.dialogType="initializeDialog",this.showDialog()},showUnscheduledDialog:function(){this.dialogType="unscheduledDialog",this.showDialog()},showDialog:function(){this.isLessonDialogShown=!0},closeDialog:function(){var t=this;this.isLessonDialogShown=!1,setTimeout((function(){t.dialogType=null}),500)}}},h=(n(2037),n(22)),m=n(42),v=n.n(m),x=n(1343),f=n(1327),_=n(261),component=Object(h.a)(c,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"lesson d-sm-flex"},[o("div",{staticClass:"lesson-date d-flex text-center"},[o("div",[t.$slots.date?[t._t("date")]:[t.$vuetify.breakpoint.smAndUp?[o("div",{staticClass:"weekday d-none d-sm-block"},[t._v("\n            "+t._s(t.$dayjs(t.startDate).format("dddd"))+"\n          ")]),t._v(" "),o("div",{staticClass:"date d-none d-sm-block"},[t._v("\n            "+t._s(t.$dayjs(t.startDate).format("DD MMM"))+"\n          ")]),t._v(" "),o("div",{staticClass:"time d-none d-sm-block"},[t._v(t._s(t.startTime))])]:[o("div",{staticClass:"d-sm-none"},[t._v("\n            "+t._s(t.$dayjs(t.startDate).format("dddd"))+",\n            "+t._s(t.$dayjs(t.startDate).format("DD MMM"))+" - "+t._s(t.startTime)+"\n          ")])]]],2),t._v(" "),t.$vuetify.breakpoint.smAndUp?o("div",{staticClass:"duration d-none d-sm-block"},[o("div",{staticClass:"duration-icon"},[o("svg",{attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[o("use",{attrs:{"xlink:href":n(91)+"#clock-thin"}})])]),t._v("\n      "+t._s(t.$t("lessonLength_mins",{lessonLength:t.item.lessonLength}))+"\n    ")]):o("div",{staticClass:"duration d-sm-none"},[t._v("\n       ("+t._s(t.$t("lessonLength_mins",{lessonLength:t.item.lessonLength}))+")\n    ")])]),t._v(" "),o("div",{staticClass:"lesson-content"},[o("div",{staticClass:"lesson-details"},[o("div",{staticClass:"avatar mr-2"},[o("v-avatar",{attrs:{width:"110",height:"110"}},[o("v-img",{attrs:{src:t.getSrcAvatar(t.item.userAvatars,"user_thumb_110x110"),srcset:t.getSrcSetAvatar(t.item.userAvatars,"user_thumb_110x110","user_thumb_220x220"),options:{rootMargin:"50%"}}}),t._v(" "),t.teacherLink?o("nuxt-link",{attrs:{to:t.teacherLink}}):t._e()],1),t._v(" "),o("user-status",{attrs:{"user-id":t.userId,"user-statuses":t.userStatuses,large:""}})],1),t._v(" "),o("div",{staticClass:"details"},[o("div",{staticClass:"user-info"},[o("div",{staticClass:"user-info-name"},[t._v("\n            "+t._s(t.userName)+"\n            "),t.isTeacher?o("div",{on:{click:t.studentInfoClickHandler}},[o("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16"}},[o("use",{attrs:{"xlink:href":n(91)+"#info"}})])]):t._e()]),t._v(" "),o("div",{class:["user-info-status","user-info-status--"+t.status,{"text--red-gradient":"idle"===t.status}]},["online"===t.status?[t._v("\n              "+t._s(t.$t("online_now"))+"\n            ")]:"idle"===t.status?[t._v("\n              "+t._s(t.$t("online_but_idle"))+"\n            ")]:[t._v("\n              "+t._s(t.$t("offline_now"))+"\n            ")]],2)]),t._v(" "),o("div",{staticClass:"lesson-info"},[o("div",{staticClass:"avatar mr-2"},[o("v-avatar",{attrs:{width:"85",height:"85"}},[o("v-img",{attrs:{src:t.getSrcAvatar(t.item.userAvatars,"user_thumb_110x110"),srcset:t.getSrcSetAvatar(t.item.userAvatars,"user_thumb_110x110","user_thumb_220x220"),options:{rootMargin:"50%"}}}),t._v(" "),t.teacherLink?o("nuxt-link",{attrs:{to:t.teacherLink}}):t._e()],1),t._v(" "),o("user-status",{attrs:{"user-id":t.userId,"user-statuses":t.userStatuses,large:""}})],1),t._v(" "),o("div",[t._t("lessonInfo"),t._v(" "),t.isUnscheduled?t._e():o("div",[o("span",{staticClass:"text-capitalize"},[t._v(t._s(t.$t("lesson"))+":")]),t._v(" "),o("span",{staticClass:"text--gradient font-weight-bold text-no-wrap"},[t._v("\n                "+t._s(t.item.lessonType)+"\n              ")])]),t._v(" "),o("div",[o("span",{staticClass:"text-capitalize"},[t._v(t._s(t.$t("language"))+":")]),t._v(" "),o("span",{staticClass:"text--gradient font-weight-bold text-no-wrap"},[t._v("\n                "+t._s(t.item.language.name)+"\n              ")])]),t._v(" "),t.item.courseName&&t.item.courseName.length?o("div",[o("span",{staticClass:"text-capitalize"},[t._v(t._s(t.$t("course"))+":")]),t._v(" "),o("span",{staticClass:"text--gradient font-weight-bold"},[t._v("\n                "+t._s(t.item.courseName)+"\n              ")])]):t._e(),t._v(" "),o("div",{staticClass:"lesson-actions-additional"},[t._t("lessonAdditionalActionsTop"),t._v(" "),t.isUnscheduleButtonHidden?t._e():[o("div",[o("span",{staticClass:"action",on:{click:t.showUnscheduledDialog}},[o("v-img",{staticStyle:{left:"3px"},attrs:{src:n(970),width:"11",height:"11"}}),t._v("\n                    "+t._s(t.$t("unschedule_lesson"))+"\n                  ")],1)])],t._v(" "),t._t("lessonAdditionalActionsBottom",null,{showDialog:t.showDialog})],2)],2)])])]),t._v(" "),o("div",{staticClass:"lesson-actions"},[o("div",{staticClass:"lesson-actions-buttons"},[t.item.userIsDeleted?t._e():o("v-btn",{staticClass:"btn-add gradient font-weight-medium ml-1 mb-1",attrs:{width:"158"},on:{click:t.showMessageDialog}},[o("div",{staticClass:"mr-1"},[o("v-img",{attrs:{src:n(1010),width:"20",height:"20"}})],1),t._v(" "),o("div",{staticClass:"text--gradient"},[t._v("\n            "+t._s(t.$t("message"))+"\n          ")])]),t._v(" "),t._t("lessonActions"),t._v(" "),t.isUnscheduled?t._e():[t.isTeacher||t.isStudent&&!t.item.isCreated?[o("v-btn",{staticClass:"go-to-class-btn font-weight-medium ml-1 mb-1",attrs:{href:"/lesson/"+t.item.lessonId+"/classroom",color:"primary",width:"158"}},[o("svg",{staticClass:"mr-1",attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[o("use",{attrs:{"xlink:href":n(91)+"#save-icon"}})]),t._v("\n              "+t._s(t.$t("go_to_class"))+"\n            ")])]:[o("v-btn",{staticClass:"go-to-class-btn go-to-class-btn--disabled primary--light font-weight-medium ml-1 mb-1",attrs:{color:"primary",width:"158"},on:{click:t.showInitializeDialog}},[o("svg",{staticClass:"icon--rotated mr-1",attrs:{width:"18",height:"18",viewBox:"0 0 18 18"}},[o("use",{attrs:{"xlink:href":n(91)+"#save-icon"}})]),t._v("\n              "+t._s(t.$t("go_to_class"))+"\n              "),o("svg",{staticClass:"icon--right",attrs:{width:"18",height:"18",viewBox:"0 0 16 16"}},[o("use",{attrs:{"xlink:href":n(91)+"#info"}})])])]]],2),t._v(" "),o("div",{staticClass:"lesson-actions-additional"},[t._t("lessonAdditionalActionsTop"),t._v(" "),t.isUnscheduleButtonHidden?t._e():[o("div",[o("span",{staticClass:"action",on:{click:t.showUnscheduledDialog}},[o("v-img",{staticStyle:{left:"3px"},attrs:{src:n(970),width:"11",height:"11"}}),t._v("\n              "+t._s(t.$t("unschedule_lesson"))+"\n            ")],1)])],t._v(" "),t._t("lessonAdditionalActionsBottom",null,{showDialog:t.showDialog})],2)]),t._v(" "),o("div",{class:["lesson-dialog d-flex flex-column justify-center",{"lesson-dialog--shown":t.isLessonDialogShown},{"lesson-dialog--student-info":"studentInfoDialog"===t.dialogType}]},[t._t("dialog",null,{closeDialog:t.closeDialog}),t._v(" "),"unscheduledDialog"===t.dialogType?[o("div",{staticClass:"lesson-dialog-title font-weight-medium text--red-gradient"},[t._v("\n          "+t._s(t.$t("are_you_sure_you_want_to_unschedule_this_lesson_with",{name:t.item.userFirstName}))+"\n        ")]),t._v(" "),o("div",{staticClass:"lesson-dialog-content l-scroll l-scroll--grey"},[t.isStudent?[t.item.isFreeTrial?[t._v("\n              "+t._s(t.$t("you_will_be_able_to_reschedule_this_lesson_from_your_teachers_profile_page"))+"\n            ")]:[t._v("\n              "+t._s(t.$t("you_will_receive_credit_and_can_reschedule_lesson_for_anytime_your_teacher_is_available"))+"\n            ")]]:[t._v("\n            "+t._s(t.$t("student_will_be_given_credit_for_lesson"))+"\n          ")]],2),t._v(" "),o("div",{staticClass:"lesson-dialog-buttons"},[o("v-btn",{staticClass:"font-weight-medium",attrs:{color:"greyDark",outlined:""},on:{click:t.closeDialog}},[t._v("\n            "+t._s(t.$t("do_not_cancel_lesson"))+"\n          ")]),t._v(" "),o("v-btn",{staticClass:"font-weight-medium",attrs:{color:"error"},on:{click:t.cancelClickHandler}},[t._v("\n            "+t._s(t.$t("cancel_lesson"))+"\n          ")])],1)]:t._e(),t._v(" "),"initializeDialog"===t.dialogType?[o("div",{staticClass:"lesson-dialog-title font-weight-medium text--gradient"},[o("div",{staticClass:"lesson-dialog-title-icon"},[o("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 12 12"}},[o("use",{attrs:{"xlink:href":n(91)+"#attention"}})])]),t._v("\n          "+t._s(t.$t("your_teacher_will_enter_classroom_first"))+"\n        ")]),t._v(" "),o("div",{staticClass:"lesson-dialog-content l-scroll l-scroll--grey",domProps:{innerHTML:t._s(t.$t("after_your_teacher_enters_go_to_class_button_will_become_clickable_so_you_can_enter_as_well"))}}),t._v(" "),o("div",{staticClass:"lesson-dialog-buttons"},[o("v-btn",{staticClass:"font-weight-medium",attrs:{color:"primary","max-width":"158"},on:{click:t.closeDialog}},[t._v("\n            OK!\n          ")])],1)]:t._e(),t._v(" "),"studentInfoDialog"===t.dialogType?[o("div",{staticClass:"lesson-dialog-title font-weight-medium text--gradient"},[o("div",{staticClass:"lesson-dialog-title-icon"},[o("svg",{attrs:{width:"20",height:"20",viewBox:"0 0 16 16"}},[o("use",{attrs:{"xlink:href":n(91)+"#info"}})])]),t._v("\n          "+t._s(t.$t("student_info"))+"\n        ")]),t._v(" "),o("div",{staticClass:"lesson-dialog-content l-scroll l-scroll--grey"},[o("div",[o("div",[o("ul",[o("li",[o("span",{staticClass:"font-weight-medium"},[t._v(t._s(t.$t("name"))+":")]),t._v("\n                  "+t._s(t.studentInfo.name)+"\n                ")]),t._v(" "),o("li",[o("span",{staticClass:"font-weight-medium"},[t._v(t._s(t.$t("lifetime_free_trials_scheduled"))+":")]),t._v("\n                  "+t._s(t.studentInfo.freeTrialScheduled)+"\n                ")]),t._v(" "),o("li",[o("span",{staticClass:"font-weight-medium"},[t._v(t._s(t.$t("lifetime_lessons_purchased"))+":")]),t._v("\n                  "+t._s(t.studentInfo.lessonsPurchased)+"\n                ")]),t._v(" "),o("li",[o("span",{staticClass:"font-weight-medium"},[t._v(t._s(t.$t("lifetime_teachers_booked_with"))+":")]),t._v("\n                  "+t._s(t.studentInfo.teachersBookedWith)+"\n                ")])])]),t._v(" "),o("div",{staticClass:"pl-1"},[o("ul",[o("li",[o("span",{staticClass:"font-weight-medium"},[t._v(t._s(t.$t("current_time"))+":")]),t._v("\n                  "+t._s(t.$dayjs().tz(t.studentInfo.timezone).format("LT"))+"\n                  ("+t._s(t.$dayjs().tz(t.studentInfo.timezone).format("z"))+",\n                  "+t._s(t.studentInfo.timezone)+")\n                ")]),t._v(" "),o("li",[o("span",{staticClass:"font-weight-medium"},[t._v(t._s(t.$t("total_spent_with_you"))+" ("+t._s(t.currencyIsoCode)+"):")]),t._v("\n                  "+t._s(t.currencySymbol)+t._s(t.getPrice(t.studentInfo.totalSpendWithTeacher))+"\n                ")]),t._v(" "),o("li",[o("span",{staticClass:"font-weight-medium"},[t._v(t._s(t.$t("date_registered_on_langu"))+":")]),t._v("\n                  "+t._s(t.$dayjs(t.studentInfo.dateRegistered).tz(t.timeZone).format("ll, LT"))+"\n                ")]),t._v(" "),o("li",[o("span",{staticClass:"font-weight-medium"},[t._v(t._s(t.$t("last_online"))+":")]),t._v(" "),"online"===t.status?[t._v("\n                    "+t._s(t.$t("online_now"))+"\n                  ")]:"idle"===t.status?[t._v("\n                    "+t._s(t.$t("online_but_idle"))+"\n                  ")]:[t._v("\n                    "+t._s(t.$dayjs(t.studentInfo.lastLoginDate).tz(t.timeZone).format("ll, LT"))+"\n                  ")]],2)])])])]),t._v(" "),o("div",{staticClass:"lesson-dialog-buttons"},[o("v-btn",{staticClass:"font-weight-medium",attrs:{color:"primary","max-width":"158"},on:{click:t.closeDialog}},[t._v("\n            OK\n          ")])],1)]:t._e()],2)]),t._v(" "),t.isShownMessageDialog?o("message-dialog",{attrs:{"recipient-id":t.userId,"recipient-name":t.item.userFirstName+" "+t.item.userLastName,"is-shown-message-dialog":t.isShownMessageDialog},on:{"close-dialog":function(e){t.isShownMessageDialog=!1}}}):t._e(),t._v(" "),t._t("default")],2)}),[],!1,null,null,null);e.default=component.exports;v()(component,{UserStatus:n(1522).default,MessageDialog:n(1685).default}),v()(component,{VAvatar:x.a,VBtn:f.a,VImg:_.a})},1434:function(t,e,n){var content=n(1491);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("a98bb618",content,!0,{sourceMap:!1})},1437:function(t,e,n){"use strict";n(23);var o={methods:{getSrcAvatar:function(t,e){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"avatar.png";return null!=t&&t[e]?t[e]:n(881)("./".concat(o))},getSrcSetAvatar:function(t,e,n){return null!=t&&t[e]&&null!=t&&t[n]?"\n            ".concat(t[e]," 1x,\n            ").concat(t[n]," 2x,\n          "):""}}},l=n(22),component=Object(l.a)(o,undefined,undefined,!1,null,null,null);e.a=component.exports},1490:function(t,e,n){"use strict";n(1434)},1491:function(t,e,n){var o=n(18)(!1);o.push([t.i,'.text-editor{position:relative}.text-editor-buttons{position:absolute;right:18px;top:8px;z-index:2}.text-editor-buttons button{display:inline-flex;justify-content:center;align-items:center;width:24px;height:24px;margin-left:8px;border-radius:2px;border:1px solid transparent}.text-editor-buttons button.is-active{background-color:var(--v-greyBg-base);border-color:var(--v-greyLight-base)}.text-editor .ProseMirror{min-height:280px;margin-bottom:4px;padding:40px 12px 12px;border:1px solid #bebebe;font-size:13px;border-radius:16px;line-height:1.23}.text-editor .ProseMirror>*{position:relative}.text-editor .ProseMirror p{margin-bottom:0}.text-editor .ProseMirror ul{padding-left:28px}.text-editor .ProseMirror ul>li p{margin-bottom:0}.text-editor .ProseMirror strong{font-weight:700!important}.text-editor .ProseMirror.focus-visible,.text-editor .ProseMirror:focus,.text-editor .ProseMirror:focus-visible{outline:none!important}.text-editor .ProseMirror-focused:before{content:"";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:16px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}.text-editor .v-text-field__details{padding:0 14px}',""]),t.exports=o},1503:function(t,e,n){"use strict";n.r(e);n(31),n(96);var o=n(1586),l=n(1587),d=n(1577),r=n(1589),c={name:"Editor",components:{EditorContent:o.b},props:{value:{type:String,required:!0},counter:{type:Boolean,default:!1},autoLink:{type:Boolean,default:!1},limit:{type:Number,default:null}},data:function(){return{editor:null,text:"",isValid:!0,editorEl:null,keysPressed:{},isDirty:!1}},watch:{value:function(t){this.editor.getHTML()===t||this.editor.commands.setContent(t,!1)}},mounted:function(){var t=this;this.editor=new o.a({content:this.value,extensions:[l.a,d.a.configure({limit:this.limit}),r.a.configure({autolink:!0})]}),this.editor.on("create",(function(e){var n=e.editor;t.text=n.getText(),t.$nextTick((function(){t.editorEl=document.getElementsByClassName("ProseMirror")[0],t.editorEl&&(t.editorEl.addEventListener("keydown",t.keydownHandler),t.editorEl.addEventListener("keyup",t.keyupHandler))})),t.validation()})),this.editor.on("update",(function(e){var n=e.editor;t.isDirty=!0,t.text=n.getText(),t.validation(),t.$emit("update",t.text?n.getHTML():"")}))},beforeDestroy:function(){this.editorEl&&(this.editorEl.removeEventListener("keydown",this.keydownHandler),this.editorEl.removeEventListener("keyup",this.keyupHandler)),this.editor.destroy()},methods:{keydownHandler:function(t){this.keysPressed[t.keyCode]=!0,(t.ctrlKey||this.keysPressed[17]||this.keysPressed[91]||this.keysPressed[93]||this.keysPressed[224])&&this.keysPressed[13]?(t.preventDefault(),this.$emit("submit"),this.keysPressed={}):13!==t.keyCode||t.shiftKey||(t.preventDefault(),this.editor.commands.enter())},keyupHandler:function(t){delete this.keysPressed[t.keyCode]},validation:function(){var t=this.text.trim().length;this.isValid=!!t,t&&this.limit&&(this.isValid=t<=this.limit),this.$emit("validation",this.isValid)}}},h=(n(1490),n(22)),component=Object(h.a)(c,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"text-editor"},[t.editor?o("div",{staticClass:"text-editor-buttons"},[o("button",{class:{"is-active":t.editor.isActive("bold")},on:{click:function(e){e.stopPropagation(),e.preventDefault(),t.editor.chain().focus().toggleBold().run()}}},[o("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16"}},[o("use",{attrs:{"xlink:href":n(91)+"#editor-bold-icon"}})])]),t._v(" "),o("button",{class:{"is-active":t.editor.isActive("bulletList")},on:{click:function(e){e.stopPropagation(),e.preventDefault(),t.editor.chain().focus().toggleBulletList().run()}}},[o("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16"}},[o("use",{attrs:{"xlink:href":n(91)+"#editor-list-icon"}})])])]):t._e(),t._v(" "),o("editor-content",{attrs:{editor:t.editor}}),t._v(" "),t.counter?o("div",{staticClass:"v-text-field__details"},[t._m(0),t._v(" "),o("div",{class:["v-counter theme--light",{"error--text":!t.isValid&&t.isDirty}]},[t._v("\n      "+t._s(t.text.length)+" / "+t._s(t.limit)+"\n    ")])]):t._e()],1)}),[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"v-messages theme--light"},[e("div",{staticClass:"v-messages__wrapper"})])}],!1,null,null,null);e.default=component.exports},1522:function(t,e,n){"use strict";n.r(e);n(31),n(20),n(80);var o={name:"UserStatus",props:{userId:{type:Number,default:0},large:{type:Boolean,default:!1},userStatuses:{type:Object,default:function(){return{}}}},computed:{status:function(){var t,e="offline";return Object.prototype.hasOwnProperty.call(this.userStatuses,null===(t=this.userId)||void 0===t?void 0:t.toString())&&(e=this.userStatuses[this.userId]),e}}},l=(n(1597),n(22)),component=Object(l.a)(o,(function(){var t=this,e=t.$createElement;return(t._self._c||e)("div",{class:["user-status","user-status--"+t.status,{"user-status--large":t.large}]})}),[],!1,null,"652352c7",null);e.default=component.exports},1545:function(t,e,n){var content=n(1598);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("006007e9",content,!0,{sourceMap:!1})},1578:function(t,e,n){var content=n(1629);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("0f94d031",content,!0,{sourceMap:!1})},1597:function(t,e,n){"use strict";n(1545)},1598:function(t,e,n){var o=n(18)(!1);o.push([t.i,".user-status[data-v-652352c7]{width:16px;height:16px;border-radius:50%;border:2px solid #fff;background:#636363;z-index:2}.user-status--idle[data-v-652352c7]{background:linear-gradient(122.42deg,var(--v-redLight-base),var(--v-orangeLight2-base))}.user-status--online[data-v-652352c7]{background:var(--v-success-base)}.user-status--large[data-v-652352c7]{width:25px;height:25px}@media only screen and (max-width:991px){.user-status--large[data-v-652352c7]{width:23px;height:23px}}@media only screen and (max-width:639px){.user-status--large[data-v-652352c7]{width:21px;height:21px}}@media only screen and (max-width:479px){.user-status--large[data-v-652352c7]{width:19px;height:19px}}",""]),t.exports=o},1628:function(t,e,n){"use strict";n(1578)},1629:function(t,e,n){var o=n(18)(!1);o.push([t.i,".v-application .v-dialog.message-dialog>.v-card{padding:32px 40px!important}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog>.v-card{padding:50px 18px 74px!important}.v-application .v-dialog.message-dialog>.v-card .dialog-content,.v-application .v-dialog.message-dialog>.v-card .message-dialog-body,.v-application .v-dialog.message-dialog>.v-card .v-form{height:100%}.v-application .v-dialog.message-dialog>.v-card .message-dialog-body{overflow-y:auto}}.v-application .v-dialog.message-dialog .message-dialog-header{display:inline-block;padding-right:60px;font-size:20px;font-weight:700;line-height:1.1}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-header{position:absolute;top:0;left:0;width:100%;height:50px;display:flex;align-items:center;padding-left:18px;font-size:18px}}.v-application .v-dialog.message-dialog .message-dialog-body .row .col:first-child{padding-right:20px}.v-application .v-dialog.message-dialog .message-dialog-body .row .col:last-child{padding-left:20px}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-text div:first-child{font-size:16px;font-weight:600}}.v-application .v-dialog.message-dialog .message-dialog-text ul{padding-left:20px}@media only screen and (min-width:992px){.v-application .v-dialog.message-dialog .message-dialog-footer{margin-top:28px}}@media only screen and (max-width:991px){.v-application .v-dialog.message-dialog .message-dialog-footer{position:absolute;bottom:0;left:0;width:100%;height:74px;padding:0 18px}}.v-application .v-dialog.message-dialog .message-dialog-footer .prev-button{color:var(--v-orange-base);cursor:pointer}.v-application .v-dialog.message-dialog .text-editor .ProseMirror{min-height:248px}.v-application .v-dialog.message-dialog .text-editor-buttons{left:8px;right:auto}",""]),t.exports=o},1685:function(t,e,n){"use strict";n.r(e);n(31),n(20);var o=n(149),l=n(1503),d={name:"MessageDialog",components:{LDialog:o.default,Editor:l.default},props:{recipientId:{type:Number,required:!0},recipientName:{type:String,required:!0},isShownMessageDialog:{type:Boolean,required:!0}},data:function(){return{newMessage:"",isMessageValid:!1}},beforeDestroy:function(){this.newMessage="",this.isMessageValid=!1},methods:{submitHandler:function(){var t=this;this.isMessageValid&&this.$store.dispatch("message/sendNewMessage",{recipientId:this.recipientId,message:this.newMessage}).then((function(e){t.newMessage="",t.$router.push({path:"/user/messages/".concat(e.id,"/view")})})).finally((function(){return t.$emit("close-dialog")}))}}},r=(n(1628),n(22)),c=n(42),h=n.n(c),m=n(1327),v=n(1360),x=n(1363),f=n(1361),component=Object(r.a)(d,(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("l-dialog",t._g({attrs:{dialog:t.isShownMessageDialog,"max-width":"725","custom-class":"message-dialog",persistent:"",fullscreen:t.$vuetify.breakpoint.xsOnly}},t.$listeners),[o("v-form",{on:{submit:function(e){return e.preventDefault(),t.submitHandler.apply(null,arguments)}}},[o("div",{staticClass:"message-dialog-header text--gradient"},[t._v("\n      "+t._s(t.$t(t.$vuetify.breakpoint.xsOnly?"ask_question":"questions_ask_teacher_directly"))+"\n    ")]),t._v(" "),o("div",{class:["message-dialog-body pt-0 pt-sm-3",{"l-scroll l-scroll--grey l-scroll--large":t.$vuetify.breakpoint.xsOnly}]},[o("v-row",{attrs:{"no-gutters":""}},[o("v-col",{staticClass:"col-12 col-sm-5 col-md-6"},[o("div",{staticClass:"message-dialog-text pr-0 pr-sm-2"},[o("div",{directives:[{name:"show",rawName:"v-show",value:t.$vuetify.breakpoint.xsOnly,expression:"$vuetify.breakpoint.xsOnly"}]},[o("span",{staticClass:"text-capitalize"},[t._v(t._s(t.$t("teacher")))]),t._v(": "+t._s(t.recipientName)),o("br"),t._v(" "),o("br")]),t._v(" "),o("div",{domProps:{innerHTML:t._s(t.$t("message_dialog_text",{recipientName:t.recipientName}))}})])]),t._v(" "),o("v-col",{staticClass:"col-12 col-sm-7 col-md-6 mt-3 mt-sm-0"},[o("editor",{attrs:{value:t.newMessage,limit:6e3},on:{update:function(e){t.newMessage=e},validation:function(e){t.isMessageValid=e}}})],1)],1)],1),t._v(" "),o("div",{staticClass:"message-dialog-footer d-flex justify-space-between align-center"},[o("div",{staticClass:"prev-button body-1",on:{click:function(e){return t.$emit("close-dialog")}}},[o("svg",{staticClass:"mr-1",attrs:{width:"17",height:"12",viewBox:"0 0 17 12"}},[o("use",{attrs:{"xlink:href":n(91)+"#arrow-prev"}})]),t._v(t._s(t.$t("go_back"))+"\n      ")]),t._v(" "),o("div",[o("v-btn",{attrs:{small:"",color:"primary",type:"submit"}},[t._v("\n          "+t._s(t.$t("send_message"))+"\n        ")])],1)])])],1)}),[],!1,null,null,null);e.default=component.exports;h()(component,{LDialog:n(149).default}),h()(component,{VBtn:m.a,VCol:v.a,VForm:x.a,VRow:f.a})},1841:function(t,e,n){var content=n(2038);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[t.i,content,""]]),content.locals&&(t.exports=content.locals);(0,n(19).default)("a8b919c2",content,!0,{sourceMap:!1})},2037:function(t,e,n){"use strict";n(1841)},2038:function(t,e,n){var o=n(18)(!1);o.push([t.i,".lesson{position:relative;min-height:128px;padding-left:142px}@media only screen and (max-width:1215px){.lesson{min-height:130px;padding-left:85px}}@media only screen and (min-width:768px){.lesson{background:#fff;border-radius:16px;box-shadow:0 6px 10px rgba(17,46,90,.08)}}@media only screen and (max-width:991px){.lesson{padding-left:112px}}@media only screen and (max-width:767px){.lesson{padding-left:0}}.lesson-date{justify-content:center;align-items:center;width:142px;padding:11px;font-size:16px;font-weight:600;background:linear-gradient(126.15deg,rgba(128,182,34,.74),rgba(60,135,248,.74) 102.93%);border-radius:16px;color:#fff}@media only screen and (min-width:768px){.lesson-date{position:absolute;left:0;top:0;height:100%;flex-direction:column;justify-content:space-between;box-shadow:4px 5px 8px hsla(0,0%,40%,.25);z-index:3}}@media only screen and (max-width:1215px){.lesson-date{width:85px;padding:14px 6px;font-size:15px}}@media only screen and (max-width:991px){.lesson-date{width:112px;padding:10px 4px;font-size:13px}}@media only screen and (max-width:767px){.lesson-date{width:100%;padding:7px 10px 32px;font-size:16px!important;font-weight:600!important;border-radius:10px 10px 0 0}.lesson-date>div{display:flex;align-items:center}}@media only screen and (max-width:480px){.lesson-date{font-size:14px!important;font-weight:400!important}}.lesson-date .time,.lesson-date .weekday{font-size:13px;font-weight:700;line-height:1}.lesson-date .date{font-weight:700;font-size:24px;line-height:1.2;white-space:nowrap}@media only screen and (max-width:1215px){.lesson-date .date{margin-bottom:4px;font-size:20px}}@media only screen and (max-width:767px){.lesson-date .date{margin-bottom:0;font-weight:inherit;font-size:inherit}}.lesson-date .remaining{line-height:1.23}@media only screen and (min-width:768px){.lesson-date .remaining{font-size:13px}}@media only screen and (max-width:767px){.lesson-date .remaining{padding:0 3px}}.lesson-date .duration{white-space:nowrap}@media only screen and (min-width:1216px){.lesson-date .duration{padding-left:22px}}@media only screen and (min-width:768px){.lesson-date .duration{position:relative;font-weight:400;color:#e8f1f7}}.lesson-date .duration-icon{position:absolute;left:0;top:50%;width:18px;height:18px;margin-top:-9px;color:var(--v-dark-lighten3)}@media only screen and (max-width:1215px){.lesson-date .duration-icon{display:none}}.lesson-content{position:relative;display:flex;justify-content:space-between;width:100%;border-radius:0 10px 10px 0;overflow:hidden}@media only screen and (max-width:767px){.lesson-content{min-height:128px;margin-top:-25px;border-radius:10px;background:#fff;box-shadow:0 1px 5px hsla(0,0%,51%,.3);z-index:2}}@media only screen and (max-width:639px){.lesson-content{flex-direction:column;padding:12px 12px 4px}}.lesson-details{display:flex;padding:8px 10px 8px 18px}@media only screen and (max-width:1215px){.lesson-details{padding:12px 4px 8px 12px}.lesson-details>.avatar{display:none}}@media only screen and (max-width:767px){.lesson-details{padding-top:10px}}@media only screen and (max-width:639px){.lesson-details{position:relative;padding:0 0 0 100px}}.lesson-details .avatar{position:relative}@media only screen and (max-width:639px){.lesson-details .avatar{position:absolute;left:0;top:0}}.lesson-details .avatar .user-status{position:absolute;top:0;right:0}@media only screen and (max-width:479px){.lesson-details .avatar .user-status{top:1px;right:1px}}.lesson-details .avatar a{position:absolute;top:0;left:0;width:100%;height:100%}.lesson-details .details{display:flex;flex-direction:column;justify-content:space-between}.lesson-details .details .user-info{line-height:1.1}@media only screen and (max-width:1439px){.lesson-details .details .user-info{margin-bottom:8px}}@media only screen and (min-width:1216px){.lesson-details .details .user-info{padding-top:2px}}.lesson-details .details .user-info-name{display:inline;font-size:24px}@media only screen and (min-width:1440px){.lesson-details .details .user-info-name{margin-right:6px}}@media only screen and (max-width:1439px){.lesson-details .details .user-info-name{font-size:22px}}@media only screen and (max-width:1215px){.lesson-details .details .user-info-name{font-size:20px}}@media only screen and (max-width:479px){.lesson-details .details .user-info-name{font-size:18px}}.lesson-details .details .user-info-name div{position:relative;top:-2px;display:inline-block;height:16px;color:var(--v-dark-lighten3);cursor:pointer}.lesson-details .details .user-info-status{display:inline;font-size:13px;color:var(--v-dark-lighten3);text-transform:lowercase}.lesson-details .details .user-info-status--online{color:var(--v-green-lighten1)}.lesson-details .details .lesson-info{font-size:16px;color:var(--v-dark-lighten3);line-height:1.1}@media only screen and (min-width:768px){.lesson-details .details .lesson-info{padding-bottom:4px}}@media only screen and (min-width:1216px){.lesson-details .details .lesson-info .avatar{display:none!important}}@media only screen and (max-width:1215px){.lesson-details .details .lesson-info{display:flex;align-items:flex-end;font-size:14px}}.lesson-details .details .lesson-info>div:not(.avatar)>div:not(:last-child){margin-bottom:3px}@media only screen and (max-width:639px){.lesson-details .details .lesson-info>div:not(.avatar)>div:not(:last-child){margin-bottom:5px}}@media only screen and (min-width:640px){.lesson-details .details .lesson-info .lesson-actions-additional{display:none!important}}.lesson-actions .v-btn:not(.v-btn--icon).v-size--default,.lesson-dialog .v-btn:not(.v-btn--icon).v-size--default{min-width:158px!important;padding:0 10px!important}.lesson-actions{display:flex;width:350px;padding:18px 18px 10px 0}@media only screen and (max-width:1215px){.lesson-actions{padding:12px 12px 8px 0}}@media only screen and (max-width:767px){.lesson-actions{padding-top:10px}}@media only screen and (min-width:640px){.lesson-actions{flex-direction:column;align-items:flex-end;justify-content:space-between;flex-grow:1}}@media only screen and (max-width:639px){.lesson-actions{width:100%;margin-top:8px;padding:8px 0 0;border-top:1px solid rgba(0,0,0,.08)}}.lesson-actions-buttons{display:flex;justify-content:flex-end}@media only screen and (max-width:639px){.lesson-actions-buttons{justify-content:space-between;width:100%}.lesson-actions-buttons .v-btn{width:158px!important;margin-left:0!important;margin-right:0!important}}.lesson-actions-buttons .go-to-class-btn .icon--right{margin-left:3px}.lesson-actions-buttons .go-to-class-btn .icon--rotated{transform:rotate(-90deg)}.lesson-actions-additional{min-width:158px;font-size:13px;line-height:1.333}@media only screen and (max-width:639px){.lesson-actions-additional{font-size:inherit;line-height:inherit}}@media only screen and (max-width:639px){.lesson-actions-additional>div{margin-top:5px}}.lesson-actions-additional>div>div,.lesson-actions-additional a,.lesson-actions-additional span.action{position:relative;display:inline-block;padding-left:20px;color:var(--v-darkLight-lighten3)!important;text-decoration:none;transition:color .3s}.lesson-actions-additional>div>div .v-image,.lesson-actions-additional a .v-image,.lesson-actions-additional span.action .v-image{position:absolute;left:0;top:50%;transform:translateY(-50%)}.lesson-actions-additional a,.lesson-actions-additional span.action{cursor:pointer}.lesson-actions-additional a:hover,.lesson-actions-additional span.action:hover{color:var(--v-success-base)!important}@media only screen and (max-width:639px){.lesson-actions .lesson-actions-additional{display:none!important}}.lesson-dialog{position:absolute;top:0;left:100%;width:100%;height:100%;padding:10px 18px 12px;font-size:13px;line-height:1.2;background-color:#fff;border-radius:0 16px 16px 0;z-index:2;transition:all .3s ease-in}@media only screen and (max-width:1215px){.lesson-dialog{padding:8px 12px}}@media only screen and (max-width:991px){.lesson-dialog{left:0;top:100%}}@media only screen and (max-width:639px){.lesson-dialog.justify-center{justify-content:space-between!important}}.lesson-dialog-title{display:flex;margin-bottom:5px;font-size:20px}@media only screen and (max-width:639px){.lesson-dialog-title{margin-bottom:12px;font-size:18px}}.lesson-dialog-title-icon{display:flex;align-items:center;padding-right:4px}.lesson-dialog-content{color:var(--v-dark-lighten3);overflow-y:auto}@media only screen and (max-width:639px){.lesson-dialog-buttons{display:flex;justify-content:space-between;margin-top:8px;border-top:1px solid rgba(0,0,0,.08)}}.lesson-dialog-buttons .v-btn{max-width:246px;margin-top:8px}@media only screen and (min-width:640px){.lesson-dialog-buttons .v-btn{width:100%}}.lesson-dialog-buttons>:first-child{margin-right:16px}@media only screen and (max-width:639px){.lesson-dialog-buttons>:first-child{margin-left:4px}}.lesson-dialog--student-info .lesson-dialog-content{display:flex}@media only screen and (min-width:640px){.lesson-dialog--student-info .lesson-dialog-content{margin-right:168px}}.lesson-dialog--student-info .lesson-dialog-content>div{display:flex;flex-grow:1}@media only screen and (max-width:639px){.lesson-dialog--student-info .lesson-dialog-content>div{width:100%;max-width:100%}}@media only screen and (max-width:479px){.lesson-dialog--student-info .lesson-dialog-content>div{flex-wrap:wrap}}@media only screen and (max-width:479px){.lesson-dialog--student-info .lesson-dialog-content>div>div{padding:0!important}}@media only screen and (min-width:480px){.lesson-dialog--student-info .lesson-dialog-content>div>div:first-child{padding-right:45px;border-right:1px solid var(--v-dark-lighten3)}}.lesson-dialog--student-info .lesson-dialog-content>div>div ul{padding:0;list-style-type:none;line-height:1.2}@media only screen and (min-width:1216px){.lesson-dialog--student-info .lesson-dialog-buttons{right:18px;bottom:12px}}@media only screen and (min-width:640px){.lesson-dialog--student-info .lesson-dialog-buttons{position:absolute;right:12px;bottom:8px}}@media only screen and (max-width:639px){.lesson-dialog--student-info .lesson-dialog-buttons{display:flex;justify-content:flex-end;align-items:flex-end}}.lesson-dialog--student-info .lesson-dialog-buttons .v-btn{margin-right:0!important}.lesson-dialog--shown{left:0;transition:all .3s ease-out}@media only screen and (max-width:991px){.lesson-dialog--shown{top:0}}",""]),t.exports=o}}]);