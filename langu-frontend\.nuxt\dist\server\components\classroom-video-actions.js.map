{"version": 3, "file": "components/classroom-video-actions.js", "sources": ["webpack:///./components/classroom/video/VideoActions.vue?5b55", "webpack:///./components/classroom/video/VideoActions.vue?846b", "webpack:///./components/classroom/video/VideoActions.vue?524c", "webpack:///./components/classroom/video/VideoActions.vue?09a3", "webpack:///./components/classroom/video/VideoActions.vue", "webpack:///./components/classroom/video/VideoActions.vue?9f92", "webpack:///./components/classroom/video/VideoActions.vue?9bdf"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./VideoActions.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"77e2f3f6\", content, true, context)\n};", "export * from \"-!../../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./VideoActions.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".stream-controls{position:absolute;left:50%;bottom:-38px;z-index:10;transform:translateX(-50%)}.stream-controls .toolbar-button-wrapper{width:40px;height:40px}.stream-controls .toolbar-button-wrapper button svg{margin:0 auto}.stream-controls .toolbar-button-wrapper button:focus{outline:none}.stream-controls .toolbar-button-wrapper-full-screen button{padding:9px}.stream-controls .hover-btn-info{left:50%;right:auto;top:auto;bottom:-36px;transform:translateX(-50%)}.stream-controls .hover-btn-info:after{left:50%;top:-9px;right:auto;border:5px solid transparent;border-bottom-color:#444;transform:translateX(-50%)}.stream-controls .stream-controls-switch{display:flex;justify-content:center;align-items:center;margin-top:-6px;padding:10px 6px 3px;background:#fff;border:none;border-radius:0 0 6px 6px;font-size:13px;line-height:1;outline:none;box-shadow:0 2px 10px rgba(0,0,0,.25)}.stream-controls .stream-controls-switch .toolbar-button-wrapper{width:18px;height:18px}.stream-controls .stream-controls-switch .toolbar-button-wrapper:not(:last-child){margin-right:4px}.stream-controls .stream-controls-switch .toolbar-button-item{min-width:18px!important;font-size:13px!important;border-radius:50%!important;overflow:hidden}.stream-controls .stream-controls-switch .toolbar-button-item--selected{color:#fff;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%)!important}.toolbar-button-icon.hand-raised{fill:#ffc107!important}.toolbar-button-icon.chat-enabled,.toolbar-button-icon.participants-enabled{fill:#2196f3!important}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"stream-controls\"},[_vm._ssrNode(\"<div id=\\\"video-window-buttons\\\" class=\\\"video-window-buttons-wrap\\\"><div class=\\\"stream-controls-wrapper cursor-auto\\\"><div class=\\\"toolbar-button-wrapper\\\"><button data-stream-toggle-video type=\\\"button\\\"\"+(_vm._ssrAttr(\"disabled\",!_vm.isJoined))+\" class=\\\"toolbar-button-item cursor-pointer\\\">\"+((_vm.settings.isVideoEnabled)?(\"<svg width=\\\"51\\\" height=\\\"33\\\" viewBox=\\\"0 0 51 33\\\" class=\\\"toolbar-button-icon\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/videocam.svg')) + \"#videocam\")))+\"></use></svg>\"):(!_vm.settings.isVideoEnabled)?(\"<svg width=\\\"39\\\" height=\\\"33\\\" viewBox=\\\"0 0 39 33\\\" class=\\\"toolbar-button-icon\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/videocam.svg')) + \"#videocam_off\")))+\"></use></svg>\"):\"<!---->\")+\"</button> <div class=\\\"hover-btn-info\\\">\"+((_vm.settings.isVideoEnabled)?(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('turn_off_camera'))+\"\\n          \")):(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('turn_on_camera'))+\"\\n          \")))+\"</div></div> <div class=\\\"toolbar-button-wrapper\\\"><button data-stream-toggle-audio type=\\\"button\\\"\"+(_vm._ssrAttr(\"disabled\",!_vm.isJoined))+\" class=\\\"toolbar-button-item cursor-pointer\\\">\"+((!_vm.settings.isMuted)?(\"<svg width=\\\"23\\\" height=\\\"33\\\" viewBox=\\\"0 0 23 33\\\" class=\\\"toolbar-button-icon\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/microphone.svg')) + \"#microphone\")))+\"></use></svg>\"):\"<!---->\")+\" \"+((_vm.settings.isMuted)?(\"<svg width=\\\"31\\\" height=\\\"34\\\" viewBox=\\\"0 0 31 34\\\" class=\\\"toolbar-button-icon\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/microphone.svg')) + \"#microphone-off\")))+\"></use></svg>\"):\"<!---->\")+\"</button> <div class=\\\"hover-btn-info\\\">\"+((!_vm.settings.isMuted)?(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('mute_microphone'))+\"\\n          \")):(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('unmute_microphone'))+\"\\n          \")))+\"</div></div> <div class=\\\"toolbar-button-wrapper toolbar-button-wrapper-full-screen\\\"><button data-stream-toggle-full-screen type=\\\"button\\\"\"+(_vm._ssrAttr(\"disabled\",!_vm.isJoined))+\" class=\\\"toolbar-button-item cursor-pointer\\\">\"+((!_vm.settings.isFullscreenEnabled)?(\"<svg width=\\\"31\\\" height=\\\"31\\\" viewBox=\\\"0 0 31 31\\\" class=\\\"toolbar-button-icon\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/full_screen.svg')) + \"#full_screen\")))+\"></use></svg>\"):\"<!---->\")+\" \"+((_vm.settings.isFullscreenEnabled)?(\"<svg width=\\\"31\\\" height=\\\"31\\\" viewBox=\\\"0 0 31 31\\\" class=\\\"toolbar-button-icon\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/full_screen.svg')) + \"#window_screen\")))+\"></use></svg>\"):\"<!---->\")+\"</button> <div class=\\\"hover-btn-info\\\">\"+((!_vm.settings.isFullscreenEnabled)?(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('full_screen_video'))+\"\\n          \")):(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('leave_full_screen_video'))+\"\\n          \")))+\"</div></div> <div class=\\\"toolbar-button-wrapper\\\"><button data-stream-toggle-screen-share type=\\\"button\\\"\"+(_vm._ssrAttr(\"disabled\",_vm.isScreenShareDisabled || (!_vm.isJoined && _vm.type === 'twilio')))+\" class=\\\"toolbar-button-item cursor-pointer\\\">\"+((_vm.settings.isScreenShareEnabled)?(\"<svg width=\\\"38\\\" height=\\\"35\\\" viewBox=\\\"0 0 38 35\\\" class=\\\"toolbar-button-icon\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/not_share.svg')) + \"#not_share\")))+\"></use></svg>\"):\"<!---->\")+\" \"+((!_vm.settings.isScreenShareEnabled)?(\"<svg width=\\\"37\\\" height=\\\"28\\\" viewBox=\\\"0 0 37 28\\\" class=\\\"toolbar-button-icon\\\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/not_share.svg')) + \"#share\")))+\"></use></svg>\"):\"<!---->\")+\"</button> <div class=\\\"hover-btn-info\\\">\"+((_vm.isScreenShareDisabled)?(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('share_is_disabled'))+\"\\n          \")):(((!_vm.settings.isScreenShareEnabled)?(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('share_my_screen'))+\"\\n            \")):(_vm._ssrEscape(\"\\n              \"+_vm._s(_vm.$t('stop_screenshare'))+\"\\n            \")))))+\"</div></div> \"+((_vm.type === 'whereby')?(\"<div class=\\\"toolbar-button-wrapper\\\"><button data-stream-toggle-hand-raise type=\\\"button\\\"\"+(_vm._ssrAttr(\"disabled\",!_vm.isJoined))+\" class=\\\"toolbar-button-item cursor-pointer\\\"><svg width=\\\"24\\\" height=\\\"33\\\" viewBox=\\\"0 0 24 33\\\"\"+(_vm._ssrClass(null,[\n              'toolbar-button-icon',\n              { 'hand-raised': _vm.settings.isHandRaised } ]))+\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/hand.svg')) + \"#hand\")))+\"></use></svg></button> <div class=\\\"hover-btn-info\\\">\"+((_vm.settings.isHandRaised)?(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('lower_hand'))+\"\\n          \")):(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('raise_hand'))+\"\\n          \")))+\"</div></div>\"):\"<!---->\")+\" \"+((_vm.type === 'whereby')?(\"<div class=\\\"toolbar-button-wrapper\\\"><button data-stream-toggle-chat type=\\\"button\\\"\"+(_vm._ssrAttr(\"disabled\",!_vm.isJoined))+\" class=\\\"toolbar-button-item cursor-pointer\\\"><svg width=\\\"28\\\" height=\\\"28\\\" viewBox=\\\"0 0 28 28\\\"\"+(_vm._ssrClass(null,[\n              'toolbar-button-icon',\n              { 'chat-enabled': _vm.settings.isChatEnabled } ]))+\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/chat.svg')) + \"#chat\")))+\"></use></svg></button> <div class=\\\"hover-btn-info\\\">\"+((_vm.settings.isChatEnabled)?(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('hide_chat'))+\"\\n          \")):(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('show_chat'))+\"\\n          \")))+\"</div></div>\"):\"<!---->\")+\" \"+((_vm.type === 'whereby')?(\"<div class=\\\"toolbar-button-wrapper\\\"><button data-stream-toggle-participants type=\\\"button\\\"\"+(_vm._ssrAttr(\"disabled\",!_vm.isJoined))+\" class=\\\"toolbar-button-item cursor-pointer\\\"><svg width=\\\"32\\\" height=\\\"28\\\" viewBox=\\\"0 0 32 28\\\"\"+(_vm._ssrClass(null,[\n              'toolbar-button-icon',\n              { 'participants-enabled': _vm.settings.isParticipantsEnabled } ]))+\"><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/classroom/participants.svg')) + \"#participants\")))+\"></use></svg></button> <div class=\\\"hover-btn-info\\\">\"+((_vm.settings.isParticipantsEnabled)?(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('hide_participants'))+\"\\n          \")):(_vm._ssrEscape(\"\\n            \"+_vm._s(_vm.$t('show_participants'))+\"\\n          \")))+\"</div></div>\"):\"<!---->\")+\"</div></div>\")])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'VideoActions',\n  props: {\n    settings: {\n      type: Object,\n      required: true,\n    },\n    isJoined: {\n      type: Boolean,\n      required: true,\n    },\n    isScreenShareDisabled: {\n      type: Boolean,\n      default: false,\n    },\n    type: {\n      type: String,\n      required: true,\n    },\n  },\n  computed: {\n    role() {\n      return this.$store.getters['classroom/role']\n    },\n  },\n}\n", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./VideoActions.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./VideoActions.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./VideoActions.vue?vue&type=template&id=bff1bf3c&\"\nimport script from \"./VideoActions.vue?vue&type=script&lang=js&\"\nexport * from \"./VideoActions.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./VideoActions.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"168b8df8\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;ACRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAIA;AACA;AACA;AAFA;AAbA;AAkBA;AACA;AACA;AACA;AACA;AAJA;AApBA;;ACpUA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}