{"version": 3, "file": "components/homepage-about-section.js", "sources": ["webpack:///./components/homepage/AboutSection.vue?e43b", "webpack:///./components/homepage/AboutSection.vue?388c", "webpack:///./components/homepage/AboutSection.vue?42eb", "webpack:///./components/homepage/AboutSection.vue?9577", "webpack:///./components/homepage/AboutSection.vue", "webpack:///./components/homepage/AboutSection.vue?5f38", "webpack:///./components/homepage/AboutSection.vue?3ad4"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AboutSection.vue?vue&type=style&index=0&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"7fb752ea\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AboutSection.vue?vue&type=style&index=0&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".about{padding-top:80px}@media only screen and (min-width:992px){.about{overflow:hidden}}.about .section-head{margin-bottom:130px}@media only screen and (max-width:991px){.about .section-head{margin-bottom:80px}}.about-wrap{position:relative}@media only screen and (max-width:991px){.about-wrap{padding-bottom:62px!important}}.about-wrap .about-bg{position:absolute;left:58%;top:25px;width:90%;height:100%;transform:translateX(-50%)}@media only screen and (max-width:1439px){.about-wrap .about-bg{top:12px;left:68%;height:104%}}@media only screen and (max-width:991px){.about-wrap .about-bg{width:100%;height:100%;top:135px;left:0;transform:translateX(0)}}.about-item{position:relative;max-width:330px;padding-bottom:70px}@media only screen and (max-width:991px){.about-item{max-width:380px;margin:0 auto}}.about-item-image{margin-bottom:5px}@media only screen and (max-width:991px){.about-item-image{display:flex;justify-content:center;margin-bottom:18px;margin-left:auto;margin-right:auto}}.about-item-more{margin-top:12px}.about-item-i1{margin-top:25px}@media only screen and (max-width:991px){.about-item-i1{margin-top:0}}.about-item-i1 .about-item-image{max-width:280px}@media only screen and (max-width:991px){.about-item-i1 .about-item-image{max-width:304px}}.about-item-i2 .about-item-image{max-width:242px}@media only screen and (max-width:991px){.about-item-i2 .about-item-image{max-width:293px}}.about-item-i3{top:-240px;left:30px}@media only screen and (max-width:1439px){.about-item-i3{top:0;left:20px;margin-top:-590px}}@media only screen and (max-width:991px){.about-item-i3{top:auto;left:auto;margin-top:0}}.about-item-i3 .about-item-image{max-width:248px}@media only screen and (max-width:991px){.about-item-i3 .about-item-image{max-width:280px}}.about-item-i4{top:-30px}@media only screen and (max-width:1439px){.about-item-i4{top:-140px;margin-top:127px}}@media only screen and (max-width:991px){.about-item-i4{top:auto;margin-top:0}}.about-item-i4 .about-item-image{max-width:255px}.about-item-i5{top:-250px;max-width:620px;padding-bottom:0}@media only screen and (max-width:1439px){.about-item-i5{top:-160px;max-width:575px}}@media only screen and (max-width:991px){.about-item-i5{top:15px;margin-left:30px}}@media only screen and (max-width:479px){.about-item-i5{max-width:371px;top:-50px;margin-left:0;margin-top:80px;padding-top:130px}}.about-item-i5 .about-item-image{position:absolute;top:50%;left:0;width:100%;transform:translateY(-50%)}@media only screen and (max-width:479px){.about-item-i5 .about-item-image{top:0;left:-30px;transform:none}.about-item-i5 .about-item-image .type-1{display:none}}.about-item-i5 .about-item-image .type-2{display:none}@media only screen and (max-width:479px){.about-item-i5 .about-item-image .type-2{display:block}}.about-item-i5 .about-item-title{font-size:20px}@media only screen and (max-width:639px){.about-item-i5 .about-item-title{font-size:18px}}.about-item-i5 .about-item-text{font-size:16px}.about-item-i5 .about-item-more,.about-item-i5 .about-item-text,.about-item-i5 .about-item-title{padding-left:200px}@media only screen and (max-width:639px){.about-item-i5 .about-item-more,.about-item-i5 .about-item-text,.about-item-i5 .about-item-title{padding-left:32%}}@media only screen and (max-width:479px){.about-item-i5 .about-item-more,.about-item-i5 .about-item-text,.about-item-i5 .about-item-title{padding-left:0;padding-right:30px}}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('section',{staticClass:\"about\"},[_c('v-container',{staticClass:\"py-0\"},[_c('v-row',[_c('v-col',{staticClass:\"col-12 py-0\"},[_c('div',{staticClass:\"section-head section-head--decorated\"},[_c('h3',{staticClass:\"section-head-title\",staticStyle:{\"color\":\"#262626\",\"-webkit-text-fill-color\":\"#262626\"}},[_vm._v(\"\\n            \"+_vm._s(_vm.$t('home_page.about_section_title'))+\"\\n          \")])])])],1)],1),_vm._ssrNode(\" \"),_vm._ssrNode(\"<div class=\\\"about-content\\\">\",\"</div>\",[_c('v-container',{staticClass:\"py-0\"},[_c('v-row',{attrs:{\"no-gutters\":\"\"}},[_c('v-col',{staticClass:\"col-xl-10 offset-xl-1 about-wrap\"},[_c('div',{staticClass:\"about-bg d-none d-md-block\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/about-bg.png'),\"options\":{ rootMargin: '50%' },\"contain\":\"\",\"height\":\"100%\"}})],1),_vm._v(\" \"),_c('div',{staticClass:\"about-bg d-md-none\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/about-m-bg.svg'),\"options\":{ rootMargin: '50%' },\"contain\":\"\",\"height\":\"100%\"}})],1),_vm._v(\" \"),_c('v-row',[_c('v-col',{staticClass:\"col-12 col-md-4 py-0\"},[_c('div',{staticClass:\"about-item about-item-i1\"},[_c('div',{staticClass:\"about-item-image\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/about-1.svg'),\"options\":{ rootMargin: '50%' }}})],1),_vm._v(\" \"),_c('div',{staticClass:\"about-item-title\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.about_1_title'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"about-item-text\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.about_1_text'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"about-item-more\"},[_c('nuxt-link',{staticClass:\"link-more\",attrs:{\"to\":\"/teacher-listing/1/motivation,2;speciality,22\"}},[_vm._v(\"\\n                    \"+_vm._s(_vm.$t('learn_more'))+\"\\n                  \")])],1)])]),_vm._v(\" \"),_c('v-col',{staticClass:\"col-12 col-md-4 offset-md-4 py-0\"},[_c('div',{staticClass:\"about-item about-item-i2\"},[_c('div',{staticClass:\"about-item-image\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/about-2.svg'),\"options\":{ rootMargin: '50%' }}})],1),_vm._v(\" \"),_c('div',{staticClass:\"about-item-title\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.about_2_title'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"about-item-text\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.about_2_text'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"about-item-more\"},[_c('nuxt-link',{staticClass:\"link-more\",attrs:{\"to\":\"/education\"}},[_vm._v(\"\\n                    \"+_vm._s(_vm.$t('learn_more'))+\"\\n                  \")])],1)])])],1),_vm._v(\" \"),_c('v-row',[_c('v-col',{staticClass:\"order-md-2 col-12 col-md-4 offset-md-8 py-0\"},[_c('div',{staticClass:\"about-item about-item-i3\"},[_c('div',{staticClass:\"about-item-image\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/about-3.svg'),\"options\":{ rootMargin: '50%' }}})],1),_vm._v(\" \"),_c('div',{staticClass:\"about-item-title\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.about_3_title'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"about-item-text\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.about_3_text'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"about-item-more\"},[_c('nuxt-link',{staticClass:\"link-more\",attrs:{\"to\":\"/teacher-listing/1/motivation,3\"}},[_vm._v(\"\\n                    \"+_vm._s(_vm.$t('learn_more'))+\"\\n                  \")])],1)])]),_vm._v(\" \"),_c('v-col',{staticClass:\"order-md-1 col-12 col-md-4 offset-md-1 offset-lg-4 py-0\"},[_c('div',{staticClass:\"about-item about-item-i4\"},[_c('div',{staticClass:\"about-item-image\"},[_c('v-img',{attrs:{\"src\":require('~/assets/images/homepage/about-4.svg'),\"contain\":\"\",\"options\":{ rootMargin: '50%' }}})],1),_vm._v(\" \"),_c('div',{staticClass:\"about-item-title\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.about_4_title'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"about-item-text\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.about_4_text'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"about-item-more\"},[_c('nuxt-link',{staticClass:\"link-more\",attrs:{\"to\":\"/teacher-listing/1/motivation,1\"}},[_vm._v(\"\\n                    \"+_vm._s(_vm.$t('learn_more'))+\"\\n                  \")])],1)])])],1),_vm._v(\" \"),_c('v-row',[_c('v-col',{staticClass:\"col-12 py-0\"},[_c('div',{staticClass:\"about-item about-item-i5\"},[_c('div',{staticClass:\"about-item-image\"},[_c('v-img',{staticClass:\"type-1\",attrs:{\"src\":require('~/assets/images/homepage/about-5.svg'),\"contain\":\"\",\"options\":{ rootMargin: '50%' }}}),_vm._v(\" \"),_c('v-img',{staticClass:\"type-2\",attrs:{\"src\":require('~/assets/images/homepage/about-5-m.svg'),\"contain\":\"\",\"max-width\":\"371\",\"options\":{ rootMargin: '50%' }}})],1),_vm._v(\" \"),_c('div',{staticClass:\"about-item-title\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.about_5_title'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"about-item-text\"},[_vm._v(\"\\n                  \"+_vm._s(_vm.$t('home_page.about_5_text'))+\"\\n                \")]),_vm._v(\" \"),_c('div',{staticClass:\"about-item-more\"},[_c('nuxt-link',{staticClass:\"link-more\",attrs:{\"to\":\"/business\"}},[_vm._v(\"\\n                    \"+_vm._s(_vm.$t('learn_more'))+\"\\n                  \")])],1)])])],1)],1)],1)],1)],1)],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'AboutSection',\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AboutSection.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./AboutSection.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./AboutSection.vue?vue&type=template&id=9e893b70&\"\nimport script from \"./AboutSection.vue?vue&type=script&lang=js&\"\nexport * from \"./AboutSection.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./AboutSection.vue?vue&type=style&index=0&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  null,\n  \"455c4d1e\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VCol } from 'vuetify/lib/components/VGrid';\nimport { VContainer } from 'vuetify/lib/components/VGrid';\nimport { VImg } from 'vuetify/lib/components/VImg';\nimport { VRow } from 'vuetify/lib/components/VGrid';\ninstallComponents(component, {VCol,VContainer,VImg,VRow})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AADA;;ACjLA;;;;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}