exports.ids = [160,33,34,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123];
exports.modules = {

/***/ 1003:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1004);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("12a190a6", content, true)

/***/ }),

/***/ 1004:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-input--checkbox.v-input--indeterminate.v-input--is-disabled{opacity:.6}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1006:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1007);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("50788f08", content, true)

/***/ }),

/***/ 1007:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-autocomplete.v-input>.v-input__control>.v-input__slot{cursor:text}.v-autocomplete input{align-self:center}.v-autocomplete.v-select.v-input--is-focused input{min-width:64px}.v-autocomplete:not(.v-input--is-focused).v-select--chips input{max-height:0;padding:0}.v-autocomplete--is-selecting-index input{opacity:0}.v-autocomplete.v-text-field--enclosed:not(.v-text-field--solo):not(.v-text-field--single-line):not(.v-text-field--outlined) .v-select__slot>input{margin-top:24px}.v-autocomplete.v-text-field--enclosed:not(.v-text-field--solo):not(.v-text-field--single-line):not(.v-text-field--outlined).v-input--dense .v-select__slot>input{margin-top:20px}.v-autocomplete:not(.v-input--is-disabled).v-select.v-text-field input{pointer-events:inherit}.v-autocomplete__content.v-menu__content,.v-autocomplete__content.v-menu__content .v-card{border-radius:0}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1013:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/UserSettingSelect.vue?vue&type=template&id=322ff0d6&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"user-setting-select",attrs:{"id":_vm.attachId}},[_c('v-select',_vm._g({attrs:{"value":_vm.value,"items":_vm.items,"height":"44","full-width":"","outlined":"","dense":"","hide-selected":_vm.hideSelected,"hide-details":_vm.hideDetails,"return-object":"","rules":_vm.rules,"placeholder":_vm._placeholder,"item-value":_vm.itemValue,"item-text":_vm.itemText,"attach":("#" + _vm.attachId),"validate-on-blur":_vm.validateOnBlur,"menu-props":{
      bottom: true,
      offsetY: true,
      minWidth: 200,
      maxHeight: _vm.maxHeight,
      nudgeBottom: 8,
      contentClass: 'select-list l-scroll',
    }},scopedSlots:_vm._u([{key:"append",fn:function(){return [_c('svg',{attrs:{"width":"12","height":"12","viewBox":"0 0 12 12"}},[_c('use',{attrs:{"xlink:href":_vm.chevronIcon}})])]},proxy:true},{key:"item",fn:function(ref){
    var item = ref.item;
return [(item.isoCode)?_c('div',{staticClass:"icon"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (item.isoCode) + ".svg"),"height":"24","width":"24","eager":""}})],1):_vm._e(),_vm._v(" "),_c('div',{staticClass:"text",style:({ color: item.id === 'all' ? '#888' : 'inherit' })},[_vm._v("\n        "+_vm._s(item[_vm.itemText])+"\n      ")])]}}])},_vm.$listeners))],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/UserSettingSelect.vue?vue&type=template&id=322ff0d6&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/UserSettingSelect.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var UserSettingSelectvue_type_script_lang_js_ = ({
  name: 'UserSettingSelect',
  props: {
    value: {
      type: Object,
      required: true
    },
    items: {
      type: Array,
      required: true
    },
    attachId: {
      type: String,
      required: true
    },
    itemValue: {
      type: String,
      default: 'id'
    },
    itemText: {
      type: String,
      default: 'name'
    },
    hideDetails: {
      type: Boolean,
      default: true
    },
    hideSelected: {
      type: Boolean,
      default: true
    },
    rules: {
      type: Array,
      default: () => []
    },
    validateOnBlur: {
      type: Boolean,
      default: false
    },
    maxHeight: {
      type: Number,
      default: 162
    },
    // eslint-disable-next-line vue/require-default-prop
    placeholder: [Boolean, String]
  },
  data: () => ({
    chevronIcon: `${__webpack_require__(14)}#chevron-down`
  }),
  computed: {
    _placeholder() {
      return !this.placeholder ? '' : this.$t(this.placeholder);
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/UserSettingSelect.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_UserSettingSelectvue_type_script_lang_js_ = (UserSettingSelectvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VSelect/VSelect.js + 5 modules
var VSelect = __webpack_require__(941);

// CONCATENATED MODULE: ./components/user-settings/UserSettingSelect.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1112)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_UserSettingSelectvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "322ff0d6",
  "9e3d1f7c"
  
)

/* harmony default export */ var UserSettingSelect = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */



installComponents_default()(component, {VImg: VImg["a" /* default */],VSelect: VSelect["a" /* default */]})


/***/ }),

/***/ 1025:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1113);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("5bc00c5d", content, true, context)
};

/***/ }),

/***/ 1041:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConfirmDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(982);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConfirmDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConfirmDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConfirmDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConfirmDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1042:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".remove-illustration-title{font-size:20px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1043:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/UserSettingAutocomplete.vue?vue&type=template&id=53fdd87c&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{key:_vm.key,staticClass:"user-setting-autocomplete",attrs:{"id":_vm.attachId}},[_c('v-autocomplete',_vm._g({ref:"autocomplete",attrs:{"value":_vm.value,"items":_vm._items,"dense":"","filled":"","outlined":"","hide-selected":"","hide-no-data":"","return-object":"","hide-details":_vm.hideDetails,"rules":_vm.rules,"item-text":_vm.itemText,"placeholder":_vm._placeholder,"attach":("#" + _vm.attachId),"menu-props":{
      bottom: true,
      offsetY: true,
      nudgeBottom: 8,
      contentClass: 'select-list l-scroll',
      maxHeight: 205,
    }},on:{"focus":_vm.clearSelection,"change":function($event){_vm.key++}},scopedSlots:_vm._u([{key:"append",fn:function(){return [_c('svg',{attrs:{"width":"12","height":"12","viewBox":"0 0 12 12"}},[_c('use',{attrs:{"xlink:href":_vm.chevronIcon}})])]},proxy:true},{key:"item",fn:function(ref){
    var item = ref.item;
return [(item.isoCode)?_c('div',{staticClass:"icon"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (item.isoCode) + ".svg"),"height":"24","width":"24","eager":""}})],1):_vm._e(),_vm._v(" "),_c('div',{staticClass:"text"},[_vm._v(_vm._s(item[_vm.itemText]))])]}}])},_vm.$listeners))],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/UserSettingAutocomplete.vue?vue&type=template&id=53fdd87c&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/UserSettingAutocomplete.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var UserSettingAutocompletevue_type_script_lang_js_ = ({
  name: 'UserSettingAutocomplete',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    items: {
      type: Array,
      required: true
    },
    selectedItems: {
      type: Array,
      default: () => []
    },
    attachId: {
      type: String,
      required: true
    },
    itemText: {
      type: String,
      default: 'name'
    },
    rules: {
      type: Array,
      default: () => []
    },
    hideDetails: {
      type: Boolean,
      default: true
    },
    // eslint-disable-next-line vue/require-default-prop
    placeholder: [Boolean, String]
  },
  data: () => ({
    key: 1,
    chevronIcon: `${__webpack_require__(14)}#chevron-down`
  }),
  computed: {
    _placeholder() {
      return !this.placeholder ? '' : this.$t(this.placeholder || 'choose_language');
    },

    _items() {
      if (!this.selectedItems.length) {
        return this.items;
      }

      return this.items.filter(item => {
        var _this$selectedItems$m, _this$selectedItems;

        return !((_this$selectedItems$m = (_this$selectedItems = this.selectedItems) === null || _this$selectedItems === void 0 ? void 0 : _this$selectedItems.map(item => item.id)) !== null && _this$selectedItems$m !== void 0 ? _this$selectedItems$m : []).includes(item.id);
      });
    }

  },
  methods: {
    clearSelection() {
      this.$nextTick(() => {
        var _this$$refs$autocompl, _this$$refs$autocompl2;

        const input = (_this$$refs$autocompl = this.$refs.autocomplete) === null || _this$$refs$autocompl === void 0 ? void 0 : (_this$$refs$autocompl2 = _this$$refs$autocompl.$el) === null || _this$$refs$autocompl2 === void 0 ? void 0 : _this$$refs$autocompl2.querySelector('input');

        if (input) {
          input.setSelectionRange(0, 0); // deselect the text

          input.blur(); // optional: blur to prevent flashing selection
        }
      });
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/UserSettingAutocomplete.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_UserSettingAutocompletevue_type_script_lang_js_ = (UserSettingAutocompletevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAutocomplete/VAutocomplete.js
var VAutocomplete = __webpack_require__(1137);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-settings/UserSettingAutocomplete.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1142)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_UserSettingAutocompletevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "53fdd87c",
  "23989aae"
  
)

/* harmony default export */ var UserSettingAutocomplete = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */



installComponents_default()(component, {VAutocomplete: VAutocomplete["a" /* default */],VImg: VImg["a" /* default */]})


/***/ }),

/***/ 1059:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1060);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("83ff91dc", content, true)

/***/ }),

/***/ 1060:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-file-input .v-file-input__text{color:rgba(0,0,0,.87)}.theme--light.v-file-input .v-file-input__text--placeholder{color:rgba(0,0,0,.6)}.theme--light.v-file-input.v-input--is-disabled .v-file-input__text,.theme--light.v-file-input.v-input--is-disabled .v-file-input__text .v-file-input__text--placeholder{color:rgba(0,0,0,.38)}.theme--dark.v-file-input .v-file-input__text{color:#fff}.theme--dark.v-file-input .v-file-input__text--placeholder{color:hsla(0,0%,100%,.7)}.theme--dark.v-file-input.v-input--is-disabled .v-file-input__text,.theme--dark.v-file-input.v-input--is-disabled .v-file-input__text .v-file-input__text--placeholder{color:hsla(0,0%,100%,.5)}.v-file-input input[type=file]{left:0;opacity:0;pointer-events:none;position:absolute;max-width:0;width:0}.v-file-input .v-file-input__text{align-items:center;align-self:stretch;display:flex;flex-wrap:wrap;width:100%}.v-file-input .v-file-input__text.v-file-input__text--chips{flex-wrap:wrap}.v-file-input .v-file-input__text .v-chip{margin:4px}.v-file-input .v-text-field__slot{min-height:32px}.v-file-input.v-input--dense .v-text-field__slot{min-height:26px}.v-file-input.v-text-field--filled:not(.v-text-field--single-line) .v-file-input__text{padding-top:22px}.v-file-input.v-text-field--outlined .v-text-field__slot{padding:6px 0}.v-file-input.v-text-field--outlined.v-input--dense .v-text-field__slot{padding:3px 0}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1062:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1143);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("61200eaa", content, true, context)
};

/***/ }),

/***/ 1065:
/***/ (function(module, exports, __webpack_require__) {

var map = {
	"./illustration-1.svg": 550,
	"./illustration-10.svg": 551,
	"./illustration-11.svg": 552,
	"./illustration-12.svg": 553,
	"./illustration-13.svg": 554,
	"./illustration-14.svg": 555,
	"./illustration-15.svg": 556,
	"./illustration-16.svg": 557,
	"./illustration-17.svg": 558,
	"./illustration-18.svg": 559,
	"./illustration-19.svg": 560,
	"./illustration-2.svg": 561,
	"./illustration-20.svg": 562,
	"./illustration-21.svg": 563,
	"./illustration-22.svg": 564,
	"./illustration-3.svg": 565,
	"./illustration-4.svg": 566,
	"./illustration-5.svg": 567,
	"./illustration-6.svg": 568,
	"./illustration-7.svg": 569,
	"./illustration-8.svg": 570,
	"./illustration-9.svg": 571
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = 1065;

/***/ }),

/***/ 1072:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _mixins_groupable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(47);
/* harmony import */ var _mixins_registrable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(0);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2);
// Mixins

 // Utilities



/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(Object(_mixins_groupable__WEBPACK_IMPORTED_MODULE_0__[/* factory */ "a"])('expansionPanels', 'v-expansion-panel', 'v-expansion-panels'), Object(_mixins_registrable__WEBPACK_IMPORTED_MODULE_1__[/* provide */ "b"])('expansionPanel', true)
/* @vue/component */
).extend({
  name: 'v-expansion-panel',
  props: {
    disabled: Boolean,
    readonly: Boolean
  },

  data() {
    return {
      content: null,
      header: null,
      nextIsActive: false
    };
  },

  computed: {
    classes() {
      return {
        'v-expansion-panel--active': this.isActive,
        'v-expansion-panel--next-active': this.nextIsActive,
        'v-expansion-panel--disabled': this.isDisabled,
        ...this.groupClasses
      };
    },

    isDisabled() {
      return this.expansionPanels.disabled || this.disabled;
    },

    isReadonly() {
      return this.expansionPanels.readonly || this.readonly;
    }

  },
  methods: {
    registerContent(vm) {
      this.content = vm;
    },

    unregisterContent() {
      this.content = null;
    },

    registerHeader(vm) {
      this.header = vm;
      vm.$on('click', this.onClick);
    },

    unregisterHeader() {
      this.header = null;
    },

    onClick(e) {
      if (e.detail) this.header.$el.blur();
      this.$emit('click', e);
      this.isReadonly || this.isDisabled || this.toggle();
    },

    toggle() {
      /* istanbul ignore else */
      if (this.content) this.content.isBooted = true;
      this.$nextTick(() => this.$emit('change'));
    }

  },

  render(h) {
    return h('div', {
      staticClass: 'v-expansion-panel',
      class: this.classes,
      attrs: {
        'aria-expanded': String(this.isActive)
      }
    }, Object(_util_helpers__WEBPACK_IMPORTED_MODULE_2__[/* getSlot */ "n"])(this));
  }

}));

/***/ }),

/***/ 1073:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _transitions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67);
/* harmony import */ var _mixins_bootable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(103);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(9);
/* harmony import */ var _mixins_registrable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(29);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(0);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(2);
 // Mixins



 // Utilities



const baseMixins = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(_mixins_bootable__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], _mixins_colorable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"], Object(_mixins_registrable__WEBPACK_IMPORTED_MODULE_3__[/* inject */ "a"])('expansionPanel', 'v-expansion-panel-content', 'v-expansion-panel'));
/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (baseMixins.extend().extend({
  name: 'v-expansion-panel-content',
  computed: {
    isActive() {
      return this.expansionPanel.isActive;
    }

  },

  created() {
    this.expansionPanel.registerContent(this);
  },

  beforeDestroy() {
    this.expansionPanel.unregisterContent();
  },

  render(h) {
    return h(_transitions__WEBPACK_IMPORTED_MODULE_0__[/* VExpandTransition */ "a"], this.showLazyContent(() => [h('div', this.setBackgroundColor(this.color, {
      staticClass: 'v-expansion-panel-content',
      directives: [{
        name: 'show',
        value: this.isActive
      }]
    }), [h('div', {
      class: 'v-expansion-panel-content__wrap'
    }, Object(_util_helpers__WEBPACK_IMPORTED_MODULE_4__[/* getSlot */ "n"])(this))])]));
  }

}));

/***/ }),

/***/ 1074:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _transitions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(66);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(9);
/* harmony import */ var _mixins_registrable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(29);
/* harmony import */ var _directives_ripple__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(22);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(0);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(2);
// Components

 // Mixins


 // Directives

 // Utilities



const baseMixins = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"])(_mixins_colorable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"], Object(_mixins_registrable__WEBPACK_IMPORTED_MODULE_3__[/* inject */ "a"])('expansionPanel', 'v-expansion-panel-header', 'v-expansion-panel'));
/* harmony default export */ __webpack_exports__["a"] = (baseMixins.extend().extend({
  name: 'v-expansion-panel-header',
  directives: {
    ripple: _directives_ripple__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"]
  },
  props: {
    disableIconRotate: Boolean,
    expandIcon: {
      type: String,
      default: '$expand'
    },
    hideActions: Boolean,
    ripple: {
      type: [Boolean, Object],
      default: false
    }
  },
  data: () => ({
    hasMousedown: false
  }),
  computed: {
    classes() {
      return {
        'v-expansion-panel-header--active': this.isActive,
        'v-expansion-panel-header--mousedown': this.hasMousedown
      };
    },

    isActive() {
      return this.expansionPanel.isActive;
    },

    isDisabled() {
      return this.expansionPanel.isDisabled;
    },

    isReadonly() {
      return this.expansionPanel.isReadonly;
    }

  },

  created() {
    this.expansionPanel.registerHeader(this);
  },

  beforeDestroy() {
    this.expansionPanel.unregisterHeader();
  },

  methods: {
    onClick(e) {
      this.$emit('click', e);
    },

    genIcon() {
      const icon = Object(_util_helpers__WEBPACK_IMPORTED_MODULE_5__[/* getSlot */ "n"])(this, 'actions') || [this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], this.expandIcon)];
      return this.$createElement(_transitions__WEBPACK_IMPORTED_MODULE_0__[/* VFadeTransition */ "d"], [this.$createElement('div', {
        staticClass: 'v-expansion-panel-header__icon',
        class: {
          'v-expansion-panel-header__icon--disable-rotate': this.disableIconRotate
        },
        directives: [{
          name: 'show',
          value: !this.isDisabled
        }]
      }, icon)]);
    }

  },

  render(h) {
    return h('button', this.setBackgroundColor(this.color, {
      staticClass: 'v-expansion-panel-header',
      class: this.classes,
      attrs: {
        tabindex: this.isDisabled ? -1 : null,
        type: 'button'
      },
      directives: [{
        name: 'ripple',
        value: this.ripple
      }],
      on: { ...this.$listeners,
        click: this.onClick,
        mousedown: () => this.hasMousedown = true,
        mouseup: () => this.hasMousedown = false
      }
    }), [Object(_util_helpers__WEBPACK_IMPORTED_MODULE_5__[/* getSlot */ "n"])(this, 'default', {
      open: this.isActive
    }, true), this.hideActions || this.genIcon()]);
  }

}));

/***/ }),

/***/ 1092:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VExpansionPanel_VExpansionPanel_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(969);
/* harmony import */ var _src_components_VExpansionPanel_VExpansionPanel_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VExpansionPanel_VExpansionPanel_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(902);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3);
// Styles
 // Components

 // Utilities


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (_VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_1__[/* BaseItemGroup */ "a"].extend({
  name: 'v-expansion-panels',

  provide() {
    return {
      expansionPanels: this
    };
  },

  props: {
    accordion: Boolean,
    disabled: Boolean,
    flat: Boolean,
    hover: Boolean,
    focusable: Boolean,
    inset: Boolean,
    popout: Boolean,
    readonly: Boolean,
    tile: Boolean
  },
  computed: {
    classes() {
      return { ..._VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_1__[/* BaseItemGroup */ "a"].options.computed.classes.call(this),
        'v-expansion-panels': true,
        'v-expansion-panels--accordion': this.accordion,
        'v-expansion-panels--flat': this.flat,
        'v-expansion-panels--hover': this.hover,
        'v-expansion-panels--focusable': this.focusable,
        'v-expansion-panels--inset': this.inset,
        'v-expansion-panels--popout': this.popout,
        'v-expansion-panels--tile': this.tile
      };
    }

  },

  created() {
    /* istanbul ignore next */
    if (this.$attrs.hasOwnProperty('expand')) {
      Object(_util_console__WEBPACK_IMPORTED_MODULE_2__[/* breaking */ "a"])('expand', 'multiple', this);
    }
    /* istanbul ignore next */


    if (Array.isArray(this.value) && this.value.length > 0 && typeof this.value[0] === 'boolean') {
      Object(_util_console__WEBPACK_IMPORTED_MODULE_2__[/* breaking */ "a"])(':value="[true, false, true]"', ':value="[0, 2]"', this);
    }
  },

  methods: {
    updateItem(item, index) {
      const value = this.getValue(item, index);
      const nextValue = this.getValue(item, index + 1);
      item.isActive = this.toggleMethod(value);
      item.nextIsActive = this.toggleMethod(nextValue);
    }

  }
}));

/***/ }),

/***/ 1093:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VRadioGroup_VRadio_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(971);
/* harmony import */ var _src_components_VRadioGroup_VRadio_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VRadioGroup_VRadio_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _VLabel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(50);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(66);
/* harmony import */ var _VInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(20);
/* harmony import */ var _mixins_binds_attrs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(23);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(9);
/* harmony import */ var _mixins_groupable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(47);
/* harmony import */ var _mixins_rippleable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(934);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(7);
/* harmony import */ var _mixins_selectable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(936);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(0);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(2);
/* harmony import */ var _util_mergeData__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(15);
// Styles



 // Mixins






 // Utilities




const baseMixins = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"])(_mixins_binds_attrs__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"], _mixins_colorable__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"], _mixins_rippleable__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"], Object(_mixins_groupable__WEBPACK_IMPORTED_MODULE_6__[/* factory */ "a"])('radioGroup'), _mixins_themeable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"]);
/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (baseMixins.extend().extend({
  name: 'v-radio',
  inheritAttrs: false,
  props: {
    disabled: Boolean,
    id: String,
    label: String,
    name: String,
    offIcon: {
      type: String,
      default: '$radioOff'
    },
    onIcon: {
      type: String,
      default: '$radioOn'
    },
    readonly: Boolean,
    value: {
      default: null
    }
  },
  data: () => ({
    isFocused: false
  }),
  computed: {
    classes() {
      return {
        'v-radio--is-disabled': this.isDisabled,
        'v-radio--is-focused': this.isFocused,
        ...this.themeClasses,
        ...this.groupClasses
      };
    },

    computedColor() {
      return _mixins_selectable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].options.computed.computedColor.call(this);
    },

    computedIcon() {
      return this.isActive ? this.onIcon : this.offIcon;
    },

    computedId() {
      return _VInput__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].options.computed.computedId.call(this);
    },

    hasLabel: _VInput__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].options.computed.hasLabel,

    hasState() {
      return (this.radioGroup || {}).hasState;
    },

    isDisabled() {
      return this.disabled || !!this.radioGroup && this.radioGroup.isDisabled;
    },

    isReadonly() {
      return this.readonly || !!this.radioGroup && this.radioGroup.isReadonly;
    },

    computedName() {
      if (this.name || !this.radioGroup) {
        return this.name;
      }

      return this.radioGroup.name || `radio-${this.radioGroup._uid}`;
    },

    rippleState() {
      return _mixins_selectable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].options.computed.rippleState.call(this);
    },

    validationState() {
      return (this.radioGroup || {}).validationState || this.computedColor;
    }

  },
  methods: {
    genInput(args) {
      // We can't actually use the mixin directly because
      // it's made for standalone components, but its
      // genInput method is exactly what we need
      return _mixins_selectable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"].options.methods.genInput.call(this, 'radio', args);
    },

    genLabel() {
      if (!this.hasLabel) return null;
      return this.$createElement(_VLabel__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], {
        on: {
          // Label shouldn't cause the input to focus
          click: _mixins_selectable__WEBPACK_IMPORTED_MODULE_9__[/* prevent */ "b"]
        },
        attrs: {
          for: this.computedId
        },
        props: {
          color: this.validationState,
          focused: this.hasState
        }
      }, Object(_util_helpers__WEBPACK_IMPORTED_MODULE_10__[/* getSlot */ "n"])(this, 'label') || this.label);
    },

    genRadio() {
      return this.$createElement('div', {
        staticClass: 'v-input--selection-controls__input'
      }, [this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"], this.setTextColor(this.validationState, {
        props: {
          dense: this.radioGroup && this.radioGroup.dense
        }
      }), this.computedIcon), this.genInput({
        name: this.computedName,
        value: this.value,
        ...this.attrs$
      }), this.genRipple(this.setTextColor(this.rippleState))]);
    },

    onFocus(e) {
      this.isFocused = true;
      this.$emit('focus', e);
    },

    onBlur(e) {
      this.isFocused = false;
      this.$emit('blur', e);
    },

    onChange() {
      if (this.isDisabled || this.isReadonly || this.isActive) return;
      this.toggle();
    },

    onKeydown: () => {}
  },

  render(h) {
    const data = {
      staticClass: 'v-radio',
      class: this.classes,
      on: Object(_util_mergeData__WEBPACK_IMPORTED_MODULE_12__[/* mergeListeners */ "b"])({
        click: this.onChange
      }, this.listeners$)
    };
    return h('div', data, [this.genRadio(), this.genLabel()]);
  }

}));

/***/ }),

/***/ 1094:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(935);
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _src_components_VRadioGroup_VRadioGroup_sass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(973);
/* harmony import */ var _src_components_VRadioGroup_VRadioGroup_sass__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_src_components_VRadioGroup_VRadioGroup_sass__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _VInput__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(20);
/* harmony import */ var _VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(902);
/* harmony import */ var _mixins_comparable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(903);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(2);
// Styles

 // Extensions


 // Mixins

 // Types


const baseMixins = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(_mixins_comparable__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"], _VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_3__[/* BaseItemGroup */ "a"], _VInput__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"]);
/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (baseMixins.extend({
  name: 'v-radio-group',

  provide() {
    return {
      radioGroup: this
    };
  },

  props: {
    column: {
      type: Boolean,
      default: true
    },
    height: {
      type: [Number, String],
      default: 'auto'
    },
    name: String,
    row: Boolean,
    // If no value set on VRadio
    // will match valueComparator
    // force default to null
    value: null
  },
  computed: {
    classes() {
      return { ..._VInput__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"].options.computed.classes.call(this),
        'v-input--selection-controls v-input--radio-group': true,
        'v-input--radio-group--column': this.column && !this.row,
        'v-input--radio-group--row': this.row
      };
    }

  },
  methods: {
    genDefaultSlot() {
      return this.$createElement('div', {
        staticClass: 'v-input--radio-group__input',
        attrs: {
          id: this.id,
          role: 'radiogroup',
          'aria-labelledby': this.computedId
        }
      }, _VInput__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"].options.methods.genDefaultSlot.call(this));
    },

    genInputSlot() {
      const render = _VInput__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"].options.methods.genInputSlot.call(this);
      delete render.data.on.click;
      return render;
    },

    genLabel() {
      const label = _VInput__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"].options.methods.genLabel.call(this);
      if (!label) return null;
      label.data.attrs.id = this.computedId; // WAI considers this an orphaned label

      delete label.data.attrs.for;
      label.tag = 'legend';
      return label;
    },

    onClick: _VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_3__[/* BaseItemGroup */ "a"].options.methods.onClick
  }
}));

/***/ }),

/***/ 1112:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingSelect_vue_vue_type_style_index_0_id_322ff0d6_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1025);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingSelect_vue_vue_type_style_index_0_id_322ff0d6_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingSelect_vue_vue_type_style_index_0_id_322ff0d6_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingSelect_vue_vue_type_style_index_0_id_322ff0d6_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingSelect_vue_vue_type_style_index_0_id_322ff0d6_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1113:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-setting-select[data-v-322ff0d6]{position:relative}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1114:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/LessonPrice.vue?vue&type=template&id=1ecf865e&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('text-input',{key:_vm.key,ref:"priceInput",attrs:{"value":_vm.value_,"type-class":"border-gradient","height":"32","hide-details":"","placeholder":"0.00","rules":_vm.rules.concat( [_vm.validatePrice]),"prefix":_vm.currentCurrencySymbol},on:{"keydown":function($event){_vm.keyCode = $event.code},"input":function($event){return _vm.updateValue($event)}}})}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/LessonPrice.vue?vue&type=template&id=1ecf865e&

// EXTERNAL MODULE: ./components/form/TextInput.vue + 4 modules
var TextInput = __webpack_require__(102);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/LessonPrice.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

const regex = /^\d+\.?\d{0,2}$/;
/* harmony default export */ var LessonPricevue_type_script_lang_js_ = ({
  name: 'LessonPrice',
  components: {
    TextInput: TextInput["default"]
  },
  props: {
    value: {
      type: [String, Number],
      required: true
    },
    rules: {
      type: Array,
      default: () => []
    },
    length: {
      type: Number,
      required: true,
      default: 30
    },
    freeTrial: {
      type: Boolean,
      required: false,
      default: false
    }
  },

  data() {
    return {
      key: 1,
      keyCode: null
    };
  },

  computed: {
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    },

    currencyCode() {
      const currencyMap = {
        $: 'USD',
        '€': 'EUR',
        '£': 'GBP',
        zł: 'PLN',
        A$: 'AUD',
        C$: 'CAD'
      };
      return currencyMap[this.currentCurrencySymbol] || 'USD';
    },

    value_() {
      return this.value || null;
    },

    minimumPrice() {
      var _pricingMap$this$curr;

      const pricingMap = {
        EUR: {
          30: 7,
          60: 11,
          90: 16,
          120: 21
        },
        GBP: {
          30: 6,
          60: 10,
          90: 15,
          120: 20
        },
        PLN: {
          30: 30,
          60: 50,
          90: 70,
          120: 85
        },
        USD: {
          30: 8,
          60: 12,
          90: 17,
          120: 22
        },
        AUD: {
          30: 12,
          60: 20,
          90: 28,
          120: 36
        },
        CAD: {
          30: 11,
          60: 18,
          90: 25,
          120: 32
        }
      };
      return ((_pricingMap$this$curr = pricingMap[this.currencyCode]) === null || _pricingMap$this$curr === void 0 ? void 0 : _pricingMap$this$curr[this.length]) || 10;
    },

    minimumValidation(value) {
      if (Number(length) === 60 || Number(value) > 0) {
        return this.minimumPrice;
      } else {
        return 0;
      }
    }

  },

  mounted() {
    var _this$value;

    this.validation((_this$value = this.value) !== null && _this$value !== void 0 ? _this$value : 0, this.freeTrial);
  },

  methods: {
    updateValue(event) {
      let value;

      if (regex.test(event) || this.keyCode === 'Backspace' || this.keyCode === 'Delete') {
        value = event;
      } else {
        value = this.value;
        this.key++;
        this.$nextTick(() => {
          this.$refs.priceInput.focus();
        });
      }

      this.keyCode = null;
      this.validation(value);
      this.$emit('input', value);
    },

    validation(value, isFreeTrial = false) {
      const minPrice = this.minimumPrice;
      this.$emit('validation', function () {
        if (isFreeTrial) {
          return true;
        } else if (Number(length) === 60 && Number(value) < minPrice) {
          return false;
        } else if (Number(value) > 0 && Number(value) < minPrice) {
          return false;
        }

        return true;
      }());
    },

    validatePrice(value) {
      const minPrice = this.minimumPrice;

      if (this.$props.freeTrial) {
        return true;
      } else if (Number(length) === 60 && Number(value) < minPrice) {
        return `Error: Minimum price is ${minPrice}`;
      } else if (Number(value) > 0 && Number(value) < minPrice) {
        return `Error: Minimum price is ${minPrice}`;
      }

      return true;
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/LessonPrice.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_LessonPricevue_type_script_lang_js_ = (LessonPricevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/user-settings/LessonPrice.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_LessonPricevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "fa29dedc"
  
)

/* harmony default export */ var LessonPrice = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1128:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VCheckbox_VCheckbox_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1003);
/* harmony import */ var _src_components_VCheckbox_VCheckbox_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VCheckbox_VCheckbox_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(935);
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(66);
/* harmony import */ var _VInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(20);
/* harmony import */ var _mixins_selectable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(936);
// Styles

 // Components


 // Mixins


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (_mixins_selectable__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"].extend({
  name: 'v-checkbox',
  props: {
    indeterminate: Boolean,
    indeterminateIcon: {
      type: String,
      default: '$checkboxIndeterminate'
    },
    offIcon: {
      type: String,
      default: '$checkboxOff'
    },
    onIcon: {
      type: String,
      default: '$checkboxOn'
    }
  },

  data() {
    return {
      inputIndeterminate: this.indeterminate
    };
  },

  computed: {
    classes() {
      return { ..._VInput__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].options.computed.classes.call(this),
        'v-input--selection-controls': true,
        'v-input--checkbox': true,
        'v-input--indeterminate': this.inputIndeterminate
      };
    },

    computedIcon() {
      if (this.inputIndeterminate) {
        return this.indeterminateIcon;
      } else if (this.isActive) {
        return this.onIcon;
      } else {
        return this.offIcon;
      }
    },

    // Do not return undefined if disabled,
    // according to spec, should still show
    // a color when disabled and active
    validationState() {
      if (this.isDisabled && !this.inputIndeterminate) return undefined;
      if (this.hasError && this.shouldValidate) return 'error';
      if (this.hasSuccess) return 'success';
      if (this.hasColor !== null) return this.computedColor;
      return undefined;
    }

  },
  watch: {
    indeterminate(val) {
      // https://github.com/vuetifyjs/vuetify/issues/8270
      this.$nextTick(() => this.inputIndeterminate = val);
    },

    inputIndeterminate(val) {
      this.$emit('update:indeterminate', val);
    },

    isActive() {
      if (!this.indeterminate) return;
      this.inputIndeterminate = false;
    }

  },
  methods: {
    genCheckbox() {
      return this.$createElement('div', {
        staticClass: 'v-input--selection-controls__input'
      }, [this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"], this.setTextColor(this.validationState, {
        props: {
          dense: this.dense,
          dark: this.dark,
          light: this.light
        }
      }), this.computedIcon), this.genInput('checkbox', { ...this.attrs$,
        'aria-checked': this.inputIndeterminate ? 'mixed' : this.isActive.toString()
      }), this.genRipple(this.setTextColor(this.rippleState))]);
    },

    genDefaultSlot() {
      return [this.genCheckbox(), this.genLabel()];
    }

  }
}));

/***/ }),

/***/ 1137:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VAutocomplete_VAutocomplete_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1006);
/* harmony import */ var _src_components_VAutocomplete_VAutocomplete_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VAutocomplete_VAutocomplete_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(941);
/* harmony import */ var _VTextField_VTextField__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(39);
/* harmony import */ var _util_mergeData__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(15);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(0);
// Styles
 // Extensions


 // Utilities



const defaultMenuProps = { ..._VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* defaultMenuProps */ "b"],
  offsetY: true,
  offsetOverflow: true,
  transition: false
};
/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (_VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].extend({
  name: 'v-autocomplete',
  props: {
    allowOverflow: {
      type: Boolean,
      default: true
    },
    autoSelectFirst: {
      type: Boolean,
      default: false
    },
    filter: {
      type: Function,
      default: (item, queryText, itemText) => {
        return itemText.toLocaleLowerCase().indexOf(queryText.toLocaleLowerCase()) > -1;
      }
    },
    hideNoData: Boolean,
    menuProps: {
      type: _VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.props.menuProps.type,
      default: () => defaultMenuProps
    },
    noFilter: Boolean,
    searchInput: {
      type: String
    }
  },

  data() {
    return {
      lazySearch: this.searchInput
    };
  },

  computed: {
    classes() {
      return { ..._VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.computed.classes.call(this),
        'v-autocomplete': true,
        'v-autocomplete--is-selecting-index': this.selectedIndex > -1
      };
    },

    computedItems() {
      return this.filteredItems;
    },

    selectedValues() {
      return this.selectedItems.map(item => this.getValue(item));
    },

    hasDisplayedItems() {
      return this.hideSelected ? this.filteredItems.some(item => !this.hasItem(item)) : this.filteredItems.length > 0;
    },

    currentRange() {
      if (this.selectedItem == null) return 0;
      return String(this.getText(this.selectedItem)).length;
    },

    filteredItems() {
      if (!this.isSearching || this.noFilter || this.internalSearch == null) return this.allItems;
      return this.allItems.filter(item => {
        const value = Object(_util_helpers__WEBPACK_IMPORTED_MODULE_4__[/* getPropertyFromItem */ "m"])(item, this.itemText);
        const text = value != null ? String(value) : '';
        return this.filter(item, String(this.internalSearch), text);
      });
    },

    internalSearch: {
      get() {
        return this.lazySearch;
      },

      set(val) {
        this.lazySearch = val;
        this.$emit('update:search-input', val);
      }

    },

    isAnyValueAllowed() {
      return false;
    },

    isDirty() {
      return this.searchIsDirty || this.selectedItems.length > 0;
    },

    isSearching() {
      return this.multiple && this.searchIsDirty || this.searchIsDirty && this.internalSearch !== this.getText(this.selectedItem);
    },

    menuCanShow() {
      if (!this.isFocused) return false;
      return this.hasDisplayedItems || !this.hideNoData;
    },

    $_menuProps() {
      const props = _VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.computed.$_menuProps.call(this);
      props.contentClass = `v-autocomplete__content ${props.contentClass || ''}`.trim();
      return { ...defaultMenuProps,
        ...props
      };
    },

    searchIsDirty() {
      return this.internalSearch != null && this.internalSearch !== '';
    },

    selectedItem() {
      if (this.multiple) return null;
      return this.selectedItems.find(i => {
        return this.valueComparator(this.getValue(i), this.getValue(this.internalValue));
      });
    },

    listData() {
      const data = _VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.computed.listData.call(this);
      data.props = { ...data.props,
        items: this.virtualizedItems,
        noFilter: this.noFilter || !this.isSearching || !this.filteredItems.length,
        searchInput: this.internalSearch
      };
      return data;
    }

  },
  watch: {
    filteredItems: 'onFilteredItemsChanged',
    internalValue: 'setSearch',

    isFocused(val) {
      if (val) {
        document.addEventListener('copy', this.onCopy);
        this.$refs.input && this.$refs.input.select();
      } else {
        document.removeEventListener('copy', this.onCopy);
        this.$refs.input && this.$refs.input.blur();
        this.updateSelf();
      }
    },

    isMenuActive(val) {
      if (val || !this.hasSlot) return;
      this.lazySearch = null;
    },

    items(val, oldVal) {
      // If we are focused, the menu
      // is not active, hide no data is enabled,
      // and items change
      // User is probably async loading
      // items, try to activate the menu
      if (!(oldVal && oldVal.length) && this.hideNoData && this.isFocused && !this.isMenuActive && val.length) this.activateMenu();
    },

    searchInput(val) {
      this.lazySearch = val;
    },

    internalSearch: 'onInternalSearchChanged',
    itemText: 'updateSelf'
  },

  created() {
    this.setSearch();
  },

  destroyed() {
    document.removeEventListener('copy', this.onCopy);
  },

  methods: {
    onFilteredItemsChanged(val, oldVal) {
      // TODO: How is the watcher triggered
      // for duplicate items? no idea
      if (val === oldVal) return;
      this.setMenuIndex(-1);
      this.$nextTick(() => {
        if (!this.internalSearch || val.length !== 1 && !this.autoSelectFirst) return;
        this.$refs.menu.getTiles();
        this.setMenuIndex(0);
      });
    },

    onInternalSearchChanged() {
      this.updateMenuDimensions();
    },

    updateMenuDimensions() {
      // Type from menuable is not making it through
      this.isMenuActive && this.$refs.menu && this.$refs.menu.updateDimensions();
    },

    changeSelectedIndex(keyCode) {
      // Do not allow changing of selectedIndex
      // when search is dirty
      if (this.searchIsDirty) return;

      if (this.multiple && keyCode === _util_helpers__WEBPACK_IMPORTED_MODULE_4__[/* keyCodes */ "s"].left) {
        if (this.selectedIndex === -1) {
          this.selectedIndex = this.selectedItems.length - 1;
        } else {
          this.selectedIndex--;
        }
      } else if (this.multiple && keyCode === _util_helpers__WEBPACK_IMPORTED_MODULE_4__[/* keyCodes */ "s"].right) {
        if (this.selectedIndex >= this.selectedItems.length - 1) {
          this.selectedIndex = -1;
        } else {
          this.selectedIndex++;
        }
      } else if (keyCode === _util_helpers__WEBPACK_IMPORTED_MODULE_4__[/* keyCodes */ "s"].backspace || keyCode === _util_helpers__WEBPACK_IMPORTED_MODULE_4__[/* keyCodes */ "s"].delete) {
        this.deleteCurrentItem();
      }
    },

    deleteCurrentItem() {
      const curIndex = this.selectedIndex;
      const curItem = this.selectedItems[curIndex]; // Do nothing if input or item is disabled

      if (!this.isInteractive || this.getDisabled(curItem)) return;
      const lastIndex = this.selectedItems.length - 1; // Select the last item if
      // there is no selection

      if (this.selectedIndex === -1 && lastIndex !== 0) {
        this.selectedIndex = lastIndex;
        return;
      }

      const length = this.selectedItems.length;
      const nextIndex = curIndex !== length - 1 ? curIndex : curIndex - 1;
      const nextItem = this.selectedItems[nextIndex];

      if (!nextItem) {
        this.setValue(this.multiple ? [] : null);
      } else {
        this.selectItem(curItem);
      }

      this.selectedIndex = nextIndex;
    },

    clearableCallback() {
      this.internalSearch = null;
      _VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.clearableCallback.call(this);
    },

    genInput() {
      const input = _VTextField_VTextField__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"].options.methods.genInput.call(this);
      input.data = Object(_util_mergeData__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(input.data, {
        attrs: {
          'aria-activedescendant': Object(_util_helpers__WEBPACK_IMPORTED_MODULE_4__[/* getObjectValueByPath */ "l"])(this.$refs.menu, 'activeTile.id'),
          autocomplete: Object(_util_helpers__WEBPACK_IMPORTED_MODULE_4__[/* getObjectValueByPath */ "l"])(input.data, 'attrs.autocomplete', 'off')
        },
        domProps: {
          value: this.internalSearch
        }
      });
      return input;
    },

    genInputSlot() {
      const slot = _VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.genInputSlot.call(this);
      slot.data.attrs.role = 'combobox';
      return slot;
    },

    genSelections() {
      return this.hasSlot || this.multiple ? _VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.genSelections.call(this) : [];
    },

    onClick(e) {
      if (!this.isInteractive) return;
      this.selectedIndex > -1 ? this.selectedIndex = -1 : this.onFocus();
      if (!this.isAppendInner(e.target)) this.activateMenu();
    },

    onInput(e) {
      if (this.selectedIndex > -1 || !e.target) return;
      const target = e.target;
      const value = target.value; // If typing and menu is not currently active

      if (target.value) this.activateMenu();
      this.internalSearch = value;
      this.badInput = target.validity && target.validity.badInput;
    },

    onKeyDown(e) {
      const keyCode = e.keyCode;

      if (e.ctrlKey || ![_util_helpers__WEBPACK_IMPORTED_MODULE_4__[/* keyCodes */ "s"].home, _util_helpers__WEBPACK_IMPORTED_MODULE_4__[/* keyCodes */ "s"].end].includes(keyCode)) {
        _VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.onKeyDown.call(this, e);
      } // The ordering is important here
      // allows new value to be updated
      // and then moves the index to the
      // proper location


      this.changeSelectedIndex(keyCode);
    },

    onSpaceDown(e) {},

    onTabDown(e) {
      _VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.onTabDown.call(this, e);
      this.updateSelf();
    },

    onUpDown(e) {
      // Prevent screen from scrolling
      e.preventDefault(); // For autocomplete / combobox, cycling
      // interfers with native up/down behavior
      // instead activate the menu

      this.activateMenu();
    },

    selectItem(item) {
      _VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.selectItem.call(this, item);
      this.setSearch();
    },

    setSelectedItems() {
      _VSelect_VSelect__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.setSelectedItems.call(this); // #4273 Don't replace if searching
      // #4403 Don't replace if focused

      if (!this.isFocused) this.setSearch();
    },

    setSearch() {
      // Wait for nextTick so selectedItem
      // has had time to update
      this.$nextTick(() => {
        if (!this.multiple || !this.internalSearch || !this.isMenuActive) {
          this.internalSearch = !this.selectedItems.length || this.multiple || this.hasSlot ? null : this.getText(this.selectedItem);
        }
      });
    },

    updateSelf() {
      if (!this.searchIsDirty && !this.internalValue) return;

      if (!this.valueComparator(this.internalSearch, this.getValue(this.internalValue))) {
        this.setSearch();
      }
    },

    hasItem(item) {
      return this.selectedValues.indexOf(this.getValue(item)) > -1;
    },

    onCopy(event) {
      var _event$clipboardData, _event$clipboardData2;

      if (this.selectedIndex === -1) return;
      const currentItem = this.selectedItems[this.selectedIndex];
      const currentItemText = this.getText(currentItem);
      (_event$clipboardData = event.clipboardData) == null ? void 0 : _event$clipboardData.setData('text/plain', currentItemText);
      (_event$clipboardData2 = event.clipboardData) == null ? void 0 : _event$clipboardData2.setData('text/vnd.vuetify.autocomplete.item+plain', currentItemText);
      event.preventDefault();
    }

  }
}));

/***/ }),

/***/ 1142:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingAutocomplete_vue_vue_type_style_index_0_id_53fdd87c_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1062);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingAutocomplete_vue_vue_type_style_index_0_id_53fdd87c_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingAutocomplete_vue_vue_type_style_index_0_id_53fdd87c_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingAutocomplete_vue_vue_type_style_index_0_id_53fdd87c_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingAutocomplete_vue_vue_type_style_index_0_id_53fdd87c_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1143:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-setting-autocomplete[data-v-53fdd87c]{position:relative}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1144:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1185);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("65b3defe", content, true, context)
};

/***/ }),

/***/ 1163:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VFileInput_VFileInput_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1059);
/* harmony import */ var _src_components_VFileInput_VFileInput_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VFileInput_VFileInput_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _VTextField__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(853);
/* harmony import */ var _VChip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(901);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(0);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3);
/* harmony import */ var _util_mergeData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(15);
// Styles
 // Extensions

 // Components

 // Utilities




/* harmony default export */ __webpack_exports__["a"] = (_VTextField__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].extend({
  name: 'v-file-input',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    chips: Boolean,
    clearable: {
      type: Boolean,
      default: true
    },
    counterSizeString: {
      type: String,
      default: '$vuetify.fileInput.counterSize'
    },
    counterString: {
      type: String,
      default: '$vuetify.fileInput.counter'
    },
    hideInput: Boolean,
    placeholder: String,
    prependIcon: {
      type: String,
      default: '$file'
    },
    readonly: {
      type: Boolean,
      default: false
    },
    showSize: {
      type: [Boolean, Number],
      default: false,
      validator: v => {
        return typeof v === 'boolean' || [1000, 1024].includes(v);
      }
    },
    smallChips: Boolean,
    truncateLength: {
      type: [Number, String],
      default: 22
    },
    type: {
      type: String,
      default: 'file'
    },
    value: {
      default: undefined,
      validator: val => {
        return Object(_util_helpers__WEBPACK_IMPORTED_MODULE_3__[/* wrapInArray */ "y"])(val).every(v => v != null && typeof v === 'object');
      }
    }
  },
  computed: {
    classes() {
      return { ..._VTextField__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.computed.classes.call(this),
        'v-file-input': true
      };
    },

    computedCounterValue() {
      const fileCount = this.isMultiple && this.lazyValue ? this.lazyValue.length : this.lazyValue instanceof File ? 1 : 0;
      if (!this.showSize) return this.$vuetify.lang.t(this.counterString, fileCount);
      const bytes = this.internalArrayValue.reduce((bytes, {
        size = 0
      }) => {
        return bytes + size;
      }, 0);
      return this.$vuetify.lang.t(this.counterSizeString, fileCount, Object(_util_helpers__WEBPACK_IMPORTED_MODULE_3__[/* humanReadableFileSize */ "q"])(bytes, this.base === 1024));
    },

    internalArrayValue() {
      return Object(_util_helpers__WEBPACK_IMPORTED_MODULE_3__[/* wrapInArray */ "y"])(this.internalValue);
    },

    internalValue: {
      get() {
        return this.lazyValue;
      },

      set(val) {
        this.lazyValue = val;
        this.$emit('change', this.lazyValue);
      }

    },

    isDirty() {
      return this.internalArrayValue.length > 0;
    },

    isLabelActive() {
      return this.isDirty;
    },

    isMultiple() {
      return this.$attrs.hasOwnProperty('multiple');
    },

    text() {
      if (!this.isDirty && (this.isFocused || !this.hasLabel)) return [this.placeholder];
      return this.internalArrayValue.map(file => {
        const {
          name = '',
          size = 0
        } = file;
        const truncatedText = this.truncateText(name);
        return !this.showSize ? truncatedText : `${truncatedText} (${Object(_util_helpers__WEBPACK_IMPORTED_MODULE_3__[/* humanReadableFileSize */ "q"])(size, this.base === 1024)})`;
      });
    },

    base() {
      return typeof this.showSize !== 'boolean' ? this.showSize : undefined;
    },

    hasChips() {
      return this.chips || this.smallChips;
    }

  },
  watch: {
    readonly: {
      handler(v) {
        if (v === true) Object(_util_console__WEBPACK_IMPORTED_MODULE_4__[/* consoleError */ "b"])('readonly is not supported on <v-file-input>', this);
      },

      immediate: true
    },

    value(v) {
      const value = this.isMultiple ? v : v ? [v] : [];

      if (!Object(_util_helpers__WEBPACK_IMPORTED_MODULE_3__[/* deepEqual */ "h"])(value, this.$refs.input.files)) {
        // When the input value is changed programatically, clear the
        // internal input's value so that the `onInput` handler
        // can be triggered again if the user re-selects the exact
        // same file(s). Ideally, `input.files` should be
        // manipulated directly but that property is readonly.
        this.$refs.input.value = '';
      }
    }

  },
  methods: {
    clearableCallback() {
      this.internalValue = this.isMultiple ? [] : null;
      this.$refs.input.value = '';
    },

    genChips() {
      if (!this.isDirty) return [];
      return this.text.map((text, index) => this.$createElement(_VChip__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"], {
        props: {
          small: this.smallChips
        },
        on: {
          'click:close': () => {
            const internalValue = this.internalValue;
            internalValue.splice(index, 1);
            this.internalValue = internalValue; // Trigger the watcher
          }
        }
      }, [text]));
    },

    genControl() {
      const render = _VTextField__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.genControl.call(this);

      if (this.hideInput) {
        render.data.style = Object(_util_mergeData__WEBPACK_IMPORTED_MODULE_5__[/* mergeStyles */ "c"])(render.data.style, {
          display: 'none'
        });
      }

      return render;
    },

    genInput() {
      const input = _VTextField__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.genInput.call(this); // We should not be setting value
      // programmatically on the input
      // when it is using type="file"

      delete input.data.domProps.value; // This solves an issue in Safari where
      // nothing happens when adding a file
      // do to the input event not firing
      // https://github.com/vuetifyjs/vuetify/issues/7941

      delete input.data.on.input;
      input.data.on.change = this.onInput;
      return [this.genSelections(), input];
    },

    genPrependSlot() {
      if (!this.prependIcon) return null;
      const icon = this.genIcon('prepend', () => {
        this.$refs.input.click();
      });
      return this.genSlot('prepend', 'outer', [icon]);
    },

    genSelectionText() {
      const length = this.text.length;
      if (length < 2) return this.text;
      if (this.showSize && !this.counter) return [this.computedCounterValue];
      return [this.$vuetify.lang.t(this.counterString, length)];
    },

    genSelections() {
      const children = [];

      if (this.isDirty && this.$scopedSlots.selection) {
        this.internalArrayValue.forEach((file, index) => {
          if (!this.$scopedSlots.selection) return;
          children.push(this.$scopedSlots.selection({
            text: this.text[index],
            file,
            index
          }));
        });
      } else {
        children.push(this.hasChips && this.isDirty ? this.genChips() : this.genSelectionText());
      }

      return this.$createElement('div', {
        staticClass: 'v-file-input__text',
        class: {
          'v-file-input__text--placeholder': this.placeholder && !this.isDirty,
          'v-file-input__text--chips': this.hasChips && !this.$scopedSlots.selection
        }
      }, children);
    },

    genTextFieldSlot() {
      const node = _VTextField__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.genTextFieldSlot.call(this);
      node.data.on = { ...(node.data.on || {}),
        click: () => this.$refs.input.click()
      };
      return node;
    },

    onInput(e) {
      const files = [...(e.target.files || [])];
      this.internalValue = this.isMultiple ? files : files[0]; // Set initialValue here otherwise isFocused
      // watcher in VTextField will emit a change
      // event whenever the component is blurred

      this.initialValue = this.internalValue;
    },

    onKeyDown(e) {
      this.$emit('keydown', e);
    },

    truncateText(str) {
      if (str.length < Number(this.truncateLength)) return str;
      const charsKeepOneSide = Math.floor((Number(this.truncateLength) - 1) / 2);
      return `${str.slice(0, charsKeepOneSide)}…${str.slice(str.length - charsKeepOneSide)}`;
    }

  }
}));

/***/ }),

/***/ 1165:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1223);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("26b71cad", content, true, context)
};

/***/ }),

/***/ 1166:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1226);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("16b2336a", content, true, context)
};

/***/ }),

/***/ 1167:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1230);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("0a9f2106", content, true, context)
};

/***/ }),

/***/ 1184:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_IllustrationDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1144);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_IllustrationDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_IllustrationDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_IllustrationDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_IllustrationDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1185:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-application .v-dialog.illustration-picker{height:100%}@media only screen and (min-height:1080px){.v-application .v-dialog.illustration-picker{max-height:975px!important}}.v-application .v-dialog.illustration-picker .dialog-content{display:flex;flex-direction:column;height:100%}.v-application .v-dialog.illustration-picker .dialog-footer{position:absolute;bottom:0;left:0;display:flex;justify-content:flex-end;align-items:center;width:100%;height:104px;margin-top:0;padding:0 28px}@media only screen and (max-width:991px){.v-application .v-dialog.illustration-picker .dialog-footer{height:74px}}.v-application .v-dialog.illustration-picker>.v-card{height:100%;padding:88px 28px 104px!important}@media only screen and (max-width:991px){.v-application .v-dialog.illustration-picker>.v-card{padding:50px 18px 74px!important}}.v-application .v-dialog.illustration-picker .illustration-picker-header{position:absolute;top:0;left:0;width:100%;height:88px;display:flex;align-items:flex-end;padding:0 60px 24px 28px;font-size:20px;font-weight:700;line-height:1.1}@media only screen and (max-width:991px){.v-application .v-dialog.illustration-picker .illustration-picker-header{align-items:center;height:50px;padding-left:18px;padding-bottom:0;font-size:18px}}.v-application .v-dialog.illustration-picker .illustration-picker-body{height:100%;flex-grow:1;overflow-y:auto;overflow-x:hidden;padding:0 2px}.v-application .v-dialog.illustration-picker .illustration-picker-list{flex-wrap:wrap;width:calc(100% + 16px)}@media only screen and (max-width:991px){.v-application .v-dialog.illustration-picker .illustration-picker-list{width:calc(100% + 12px)}}.v-application .v-dialog.illustration-picker .illustration-picker-list .item{width:20%;flex:0 0 20%;padding:8px 16px 8px 0}@media only screen and (max-width:767px){.v-application .v-dialog.illustration-picker .illustration-picker-list .item{width:25%;flex:0 0 25%;padding:6px 12px 6px 0}}.v-application .v-dialog.illustration-picker .illustration-picker-list .item-helper{position:relative;border-radius:16px;background-clip:padding-box}.v-application .v-dialog.illustration-picker .illustration-picker-list .item-helper:not(.item-helper--selected){cursor:pointer}.v-application .v-dialog.illustration-picker .illustration-picker-list .item-helper:not(.item-helper--selected):hover:after{content:\"\";position:absolute;border-radius:inherit;top:0;left:0;bottom:0;right:0;border:1px solid rgba(45,45,45,.31)}.v-application .v-dialog.illustration-picker .illustration-picker-list .item-helper .v-image{border-radius:inherit;background:linear-gradient(95.18deg,#f2f8e9 15.34%,#ebf3fe 66.75%),#c4c4c4}.v-application .v-dialog.illustration-picker .illustration-picker-list .item-helper--selected:before{content:\"\";position:absolute;border-radius:18px;top:0;left:0;bottom:0;right:0;margin:-2px;background:linear-gradient(95.18deg,var(--v-green-base) 15.34%,var(--v-primary-base) 66.75%),#c4c4c4}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1186:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1187);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("beda1088", content, true)

/***/ }),

/***/ 1187:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-input--switch .v-input--switch__thumb{color:#fff}.theme--light.v-input--switch .v-input--switch__track{color:rgba(0,0,0,.38)}.theme--light.v-input--switch.v-input--is-disabled:not(.v-input--is-dirty) .v-input--switch__thumb{color:#fafafa!important}.theme--light.v-input--switch.v-input--is-disabled:not(.v-input--is-dirty) .v-input--switch__track{color:rgba(0,0,0,.12)!important}.theme--dark.v-input--switch .v-input--switch__thumb{color:#bdbdbd}.theme--dark.v-input--switch .v-input--switch__track{color:hsla(0,0%,100%,.3)}.theme--dark.v-input--switch.v-input--is-disabled:not(.v-input--is-dirty) .v-input--switch__thumb{color:#424242!important}.theme--dark.v-input--switch.v-input--is-disabled:not(.v-input--is-dirty) .v-input--switch__track{color:hsla(0,0%,100%,.1)!important}.v-input--switch__thumb,.v-input--switch__track{background-color:currentColor;pointer-events:none;transition:inherit}.v-input--switch__track{border-radius:8px;width:36px;height:14px;left:2px;position:absolute;opacity:.6;right:2px;top:calc(50% - 7px)}.v-input--switch__thumb{border-radius:50%;top:calc(50% - 10px);height:20px;position:relative;width:20px;display:flex;justify-content:center;align-items:center;transition:.3s cubic-bezier(.25,.8,.5,1)}.v-input--switch .v-input--selection-controls__input{width:38px}.v-input--switch .v-input--selection-controls__ripple{top:calc(50% - 24px)}.v-input--switch.v-input--dense .v-input--switch__thumb{width:18px;height:18px}.v-input--switch.v-input--dense .v-input--switch__track{height:12px;width:32px}.v-input--switch.v-input--dense.v-input--switch--inset .v-input--switch__track{height:22px;width:44px;top:calc(50% - 12px);left:-3px}.v-input--switch.v-input--dense .v-input--selection-controls__ripple{top:calc(50% - 22px)}.v-input--switch.v-input--is-dirty.v-input--is-disabled{opacity:.6}.v-application--is-ltr .v-input--switch .v-input--selection-controls__ripple{left:-14px}.v-application--is-ltr .v-input--switch.v-input--dense .v-input--selection-controls__ripple{left:-12px}.v-application--is-ltr .v-input--switch.v-input--is-dirty .v-input--selection-controls__ripple,.v-application--is-ltr .v-input--switch.v-input--is-dirty .v-input--switch__thumb{transform:translate(20px)}.v-application--is-rtl .v-input--switch .v-input--selection-controls__ripple{right:-14px}.v-application--is-rtl .v-input--switch.v-input--dense .v-input--selection-controls__ripple{right:-12px}.v-application--is-rtl .v-input--switch.v-input--is-dirty .v-input--selection-controls__ripple,.v-application--is-rtl .v-input--switch.v-input--is-dirty .v-input--switch__thumb{transform:translate(-20px)}.v-input--switch:not(.v-input--switch--flat):not(.v-input--switch--inset) .v-input--switch__thumb{box-shadow:0 2px 4px -1px rgba(0,0,0,.2),0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12)}.v-input--switch--inset .v-input--selection-controls__input,.v-input--switch--inset .v-input--switch__track{width:48px}.v-input--switch--inset .v-input--switch__track{border-radius:14px;height:28px;left:-4px;opacity:.32;top:calc(50% - 14px)}.v-application--is-ltr .v-input--switch--inset .v-input--selection-controls__ripple,.v-application--is-ltr .v-input--switch--inset .v-input--switch__thumb{transform:translate(0)!important}.v-application--is-rtl .v-input--switch--inset .v-input--selection-controls__ripple,.v-application--is-rtl .v-input--switch--inset .v-input--switch__thumb{transform:translate(-6px)!important}.v-application--is-ltr .v-input--switch--inset.v-input--is-dirty .v-input--selection-controls__ripple,.v-application--is-ltr .v-input--switch--inset.v-input--is-dirty .v-input--switch__thumb{transform:translate(20px)!important}.v-application--is-rtl .v-input--switch--inset.v-input--is-dirty .v-input--selection-controls__ripple,.v-application--is-rtl .v-input--switch--inset.v-input--is-dirty .v-input--switch__thumb{transform:translate(-26px)!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1199:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/IllustrationDialog.vue?vue&type=template&id=3e355e63&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{"dialog":_vm.isShownIllustrationDialog,"max-width":"844","custom-class":"illustration-picker","fullscreen":_vm.$vuetify.breakpoint.smAndDown},scopedSlots:_vm._u([{key:"footer",fn:function(){return [_c('div',{staticClass:"d-flex justify-end"},[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"primary"},on:{"click":function($event){return _vm.$emit('update-image', _vm.selectedImage)}}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"18","height":"18","viewBox":"0 0 18 18"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#save-icon")}})]),_vm._v("\n        "+_vm._s(_vm.$t('save_changes'))+"\n      ")])],1)]},proxy:true}])},_vm.$listeners),[_c('div',{staticClass:"illustration-picker-header"},[_vm._v("\n    "+_vm._s(_vm.$t('choose_illustration'))+":\n  ")]),_vm._v(" "),_c('div',{staticClass:"illustration-picker-body l-scroll l-scroll--grey l-scroll--large"},[_c('div',{staticClass:"illustration-picker-list d-flex"},_vm._l((_vm.items),function(item){return _c('div',{key:item.id,staticClass:"item"},[_c('div',{class:[
            'item-helper',
            { 'item-helper--selected': _vm.selectedImage === item.image } ],on:{"click":function($event){_vm.newSelectedImage = item.image}}},[_c('v-img',{attrs:{"src":__webpack_require__(1065)("./" + (item.image) + ".svg")}})],1)])}),0)])])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/IllustrationDialog.vue?vue&type=template&id=3e355e63&

// EXTERNAL MODULE: ./components/LDialog.vue + 5 modules
var LDialog = __webpack_require__(28);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/IllustrationDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var IllustrationDialogvue_type_script_lang_js_ = ({
  name: 'IllustrationDialog',
  components: {
    LDialog: LDialog["default"]
  },
  props: {
    isShownIllustrationDialog: {
      type: Boolean,
      required: true
    },
    currentIllustration: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      newSelectedImage: null
    };
  },

  computed: {
    items() {
      return this.$store.state.settings.illustrationItems;
    },

    selectedImage() {
      var _this$currentIllustra;

      return this.newSelectedImage || ((_this$currentIllustra = this.currentIllustration) === null || _this$currentIllustra === void 0 ? void 0 : _this$currentIllustra.image);
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/IllustrationDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_IllustrationDialogvue_type_script_lang_js_ = (IllustrationDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-settings/IllustrationDialog.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1184)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_IllustrationDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "5b7625a7"
  
)

/* harmony default export */ var IllustrationDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LDialog: __webpack_require__(28).default})


/* vuetify-loader */



installComponents_default()(component, {VBtn: VBtn["a" /* default */],VImg: VImg["a" /* default */]})


/***/ }),

/***/ 1218:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1291);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("a97915c6", content, true, context)
};

/***/ }),

/***/ 1219:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1293);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("1a361b51", content, true, context)
};

/***/ }),

/***/ 1220:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1295);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("36e4c324", content, true, context)
};

/***/ }),

/***/ 1221:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1297);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("3ab5135c", content, true, context)
};

/***/ }),

/***/ 1222:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CourseItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1165);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CourseItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CourseItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CourseItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CourseItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1223:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".course-panel .v-expansion-panel-header{min-height:75px;padding:24px 0;font-size:20px;font-weight:600}@media only screen and (max-width:1439px){.course-panel .v-expansion-panel-header{padding:20px 0}}@media only screen and (max-width:767px){.course-panel .v-expansion-panel-header{min-height:48px;padding:10px 0;font-size:16px}}.course-panel .v-expansion-panel-content__wrap{padding:16px 0 40px}@media only screen and (max-width:1439px){.course-panel .v-expansion-panel-content__wrap{padding:16px 0 32px}}@media only screen and (max-width:767px){.course-panel .v-expansion-panel-content__wrap{padding:16px 0 24px}}.course-panel .input-wrap--image .image-select{color:var(--v-grey-base)}.course-panel .input-wrap--image .image-select-preview{width:80px;height:80px;border-radius:16px;background:linear-gradient(95.18deg,#f2f8e9 15.34%,#ebf3fe 66.75%),#c4c4c4;overflow:hidden}.course-panel .input-wrap--image .image-select-name{position:relative;margin-left:12px;padding-right:32px;font-size:14px}.course-panel .input-wrap--image .image-select-name .v-btn{position:absolute;right:0;top:1px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1224:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1300);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("f170caf6", content, true, context)
};

/***/ }),

/***/ 1225:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SpecialityDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1166);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SpecialityDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SpecialityDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SpecialityDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SpecialityDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1226:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".speciality-picker{height:calc(100vh - 10%)}.speciality-picker .dialog-content,.speciality-picker .v-card{height:100%}@media only screen and (max-width:1439px){.speciality-picker .v-card{padding:32px 28px}}.speciality-picker .dialog-content{position:relative}.speciality-picker-content{padding-bottom:88px}.speciality-picker-content>.row{height:100%}.speciality-picker-content>.row>.col,.speciality-picker-content>.row>.col>.column{height:inherit}.speciality-picker-title{font-size:20px}.speciality-picker-text{color:var(--v-grey-base);letter-spacing:.3px}.speciality-picker-text ul{padding-left:0;list-style-type:none}.speciality-picker-text ul>li:not(:last-child){margin-bottom:4px}.speciality-picker-bottom{position:absolute;left:0;bottom:0;width:100%}.speciality-picker .column{padding:20px 15px;border:1px solid #00a500;border-radius:24px}.speciality-picker .column-helper{height:100%;overflow-y:auto;overflow-x:hidden}.speciality-picker .column-helper>div,.speciality-picker .column-helper>div>div{height:100%}.speciality-picker .column .list-group-item{cursor:move}.speciality-picker .column .list-group-item:not(:last-child){margin-bottom:8px}.speciality-picker .column .list-group-item.highest-priority{color:var(--v-success-base)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1227:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1302);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("a3fe467e", content, true, context)
};

/***/ }),

/***/ 1228:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1304);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("7e351ed2", content, true, context)
};

/***/ }),

/***/ 1229:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AddQualificationDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1167);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AddQualificationDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AddQualificationDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AddQualificationDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AddQualificationDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1230:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, "@media only screen and (max-width:1439px){.qualification-dialog .v-card{padding:48px 28px 32px}}.qualification-dialog .upload-file{position:relative}.qualification-dialog .upload-file-name{position:relative;padding-right:32px}.qualification-dialog .upload-file-name .file-remove-btn{position:absolute;right:0;top:2px}.qualification-dialog-input{position:absolute;left:0;bottom:-26px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1231:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1306);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("32ea8be0", content, true, context)
};

/***/ }),

/***/ 1232:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1310);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("09966082", content, true, context)
};

/***/ }),

/***/ 1271:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/PerLessonPrice.vue?vue&type=template&id=b047eada&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('lesson-price',{attrs:{"value":_vm.value,"rules":_vm.rules,"length":_vm.length},on:{"input":_vm.updateValue}})}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/PerLessonPrice.vue?vue&type=template&id=b047eada&

// EXTERNAL MODULE: ./components/user-settings/LessonPrice.vue + 4 modules
var LessonPrice = __webpack_require__(1114);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/PerLessonPrice.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//

/* harmony default export */ var PerLessonPricevue_type_script_lang_js_ = ({
  name: 'PerLessonPrice',
  components: {
    LessonPrice: LessonPrice["default"]
  },
  props: {
    items: {
      type: Array,
      required: true
    },
    length: {
      type: Number,
      required: true
    },
    lessons: {
      type: Number,
      required: true
    },
    rules: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      key: this.length + this.lessons,
      keyCode: null
    };
  },

  computed: {
    value() {
      var _this$items$find;

      return (_this$items$find = this.items.find(item => item.length === this.length && item.lessons === this.lessons)) === null || _this$items$find === void 0 ? void 0 : _this$items$find.price;
    }

  },
  methods: {
    updateValue(value) {
      this.$store.commit('settings/UPDATE_LESSON_PRICE', {
        value,
        length: this.length,
        lessons: this.lessons
      });
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/PerLessonPrice.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_PerLessonPricevue_type_script_lang_js_ = (PerLessonPricevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/user-settings/PerLessonPrice.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_PerLessonPricevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "50089bfa"
  
)

/* harmony default export */ var PerLessonPrice = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1272:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/CourseItem.vue?vue&type=template&id=7415d14b&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-expansion-panel',{staticClass:"course-panel"},[_c('v-expansion-panel-header',{attrs:{"disable-icon-rotate":""},scopedSlots:_vm._u([{key:"actions",fn:function(){return [(_vm.isActive)?[_c('v-icon',{attrs:{"color":"darkLight"}},[_vm._v("\n          "+_vm._s(_vm.mdiMinus)+"\n        ")])]:[_c('v-img',{attrs:{"src":__webpack_require__(515),"width":"24","height":"24"}})]]},proxy:true}])},[_vm._v("\n    "+_vm._s(_vm.title)+"\n    ")]),_vm._v(" "),_c('v-expansion-panel-content',{attrs:{"eager":""}},[_c('v-form',{ref:("form-" + _vm.index),attrs:{"value":_vm.valid},on:{"input":function($event){_vm.formValid = $event},"submit":function($event){$event.preventDefault();return _vm.saveCourse.apply(null, arguments)}}},[_c('div',{staticClass:"mb-md-2"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-sm-6 d-flex align-end"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n                "+_vm._s(_vm.$t('please_enter_name_of_course'))+"\n              ")]),_vm._v(" "),_c('text-input',{attrs:{"value":_vm.item.name,"type-class":"border-gradient","height":"44","counter":"50","hide-details":false,"rules":_vm.rules.name,"placeholder":_vm.$t('name_of_course')},on:{"input":function($event){return _vm.updateValue($event, 'name')}}})],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 d-flex align-end"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n                "+_vm._s(_vm.$t('number_of_lessons_in_course'))+"\n              ")]),_vm._v(" "),_c('user-setting-select',{attrs:{"value":{ name: _vm.item.lessons },"items":_vm.lessonCountItems,"hide-details":false,"item-value":"name","attach-id":("course-lessons-" + (_vm.item.id || _vm.item.uid))},on:{"change":function($event){return _vm.updateValue($event.name, 'lessons')}}})],1)])],1)],1),_vm._v(" "),_c('div',{staticClass:"mb-sm-2"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-sm-6 d-flex mb-2 mb-sm-0"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n                "+_vm._s(_vm.$t('duration_of_lessons'))+"\n              ")]),_vm._v(" "),_c('user-setting-select',{attrs:{"value":{ name: _vm.item.length },"items":_vm.lessonLengthItems,"item-value":"name","attach-id":("course-length-" + (_vm.item.id || _vm.item.uid))},on:{"change":function ($event) {
                    _vm.updateValue($event.name, 'length')
                    _vm.currentSelectedDuration = $event.name
                  }}})],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 d-flex mb-2 mb-sm-0"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n                "+_vm._s(_vm.$t('price_per_lesson'))+"\n              ")]),_vm._v(" "),_c('lesson-price',{attrs:{"value":_vm.item.price,"length":_vm.currentSelectedDuration},on:{"validation":function($event){_vm.priceValid = $event},"input":_vm.updatePriceTrialLesson}}),_vm._v(" "),(_vm.totalPackagePrice)?_c('div',{staticClass:"input-wrap-label mt-1 mb-0"},[_vm._v("\n                "+_vm._s(_vm.$t('Total package price'))+": "+_vm._s(_vm.currentCurrencySymbol)+_vm._s(_vm.totalPackagePrice.toFixed(2))+"\n              ")]):_vm._e()],1)])],1)],1),_vm._v(" "),_c('div',{staticClass:"mb-2 mb-md-4"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-sm-6 d-flex"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n                "+_vm._s(_vm.$t('language_of_course'))+"\n              ")]),_vm._v(" "),_c('user-setting-select',{attrs:{"value":{ id: _vm.item.languageId },"items":_vm.languages,"hide-selected":false,"attach-id":("course-language-" + (_vm.item.id || _vm.item.uid))},on:{"change":function($event){return _vm.updateValue($event.id, 'languageId')}}})],1)])],1)],1),_vm._v(" "),_c('div',{staticClass:"mb-md-2"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-sm-6 d-flex"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n                "+_vm._s(_vm.$t('please_enter_short_description_of_course'))+":\n              ")]),_vm._v(" "),_c('div',[_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.item.shortDescription,"no-resize":"","height":"234","solo":"","dense":"","counter":"250","rules":_vm.rules.shortDescription},on:{"input":function($event){return _vm.updateValue($event, 'shortDescription')}}})],1)])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 d-flex"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n                "+_vm._s(_vm.$t('please_enter_intro_to_course'))+":\n              ")]),_vm._v(" "),_c('div',[_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.item.introductionToCourse,"no-resize":"","height":"234","solo":"","dense":"","counter":"500","rules":_vm.rules.description},on:{"input":function($event){return _vm.updateValue($event, 'introductionToCourse')}}})],1)])])],1)],1),_vm._v(" "),_c('div',{staticClass:"mb-2 mb-md-3"},[_c('v-row',[_c('v-col',{staticClass:"col-12 d-flex"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n                "+_vm._s(_vm.$t('please_enter_structure_of_course'))+":\n              ")]),_vm._v(" "),_c('div',[_c('editor',{attrs:{"value":_vm.item.courseStructure,"limit":1500,"counter":""},on:{"validation":function($event){_vm.courseStructureValid = $event},"update":_vm.updateCourseStructure}})],1)])])],1)],1),_vm._v(" "),_c('div',[_c('v-row',[_c('v-col',{staticClass:"col-12 col-sm-6 d-flex mb-2 mb-sm-0"},[_c('div',{staticClass:"input-wrap input-wrap--image"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n                "+_vm._s(_vm.$t('please_choose_illustration_that_best_suits_to_course'))+":\n              ")]),_vm._v(" "),_c('div',{staticClass:"image-select"},[(!_vm.item.image)?[_c('div',{staticClass:"image-select--no-data body-2"},[_vm._v("\n                    "+_vm._s(_vm.$t('no_illustration_chosen'))+"\n                  ")])]:[_c('div',{staticClass:"d-flex align-center"},[_c('div',{staticClass:"image-select-preview"},[_c('v-img',{attrs:{"eager":"","src":__webpack_require__(1065)("./" + (_vm.item.image) + ".svg")}})],1),_vm._v(" "),_c('div',{staticClass:"image-select-name"},[_vm._v("\n                      "+_vm._s(_vm.currentIllustration.name)+"\n                      "),_c('v-btn',{attrs:{"width":"18","height":"18","icon":""},on:{"click":function($event){_vm.isShownIllustrationConfirmDialog = true}}},[_c('v-img',{attrs:{"src":__webpack_require__(105),"width":"15","height":"15"}})],1)],1)])]],2),_vm._v(" "),_c('v-btn',{staticClass:"gradient font-weight-medium mt-3",on:{"click":function($event){_vm.isShownIllustrationDialog = true}}},[_c('div',{staticClass:"text--gradient"},[_vm._v("\n                  "+_vm._s(_vm.$t('choose_illustration'))+"\n                ")])])],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n                "+_vm._s(_vm.$t('course_intro_video_please_enter_youtube_link'))+"\n              ")]),_vm._v(" "),_c('text-input',{attrs:{"value":_vm.item.youtube,"type-class":"border-gradient","height":"44","hide-details":"","placeholder":_vm.$t('youtube_link')},on:{"input":function($event){return _vm.updateValue($event, 'youtube')}}})],1),_vm._v(" "),_c('div',{staticClass:"mt-3"},[_c('div',{staticClass:"input-wrap-label mb-0"},[_vm._v("\n                "+_vm._s(_vm.$t('are_you_ready_to_publish_this_course'))+" "),_c('br'),_vm._v("\n                "+_vm._s(_vm.$t(
                    'select_this_option_when_you_are_ready_to_begin_selling_this_course'
                  ))+"\n              ")]),_vm._v(" "),_c('div',{staticClass:"d-flex justify-end"},[_c('v-switch',{attrs:{"inset":"","ripple":false,"color":"success","dense":"","hide-details":""},model:{value:(_vm.isPublish),callback:function ($$v) {_vm.isPublish=$$v},expression:"isPublish"}})],1)])])],1)],1),_vm._v(" "),_c('div',[_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"d-flex justify-space-between justify-sm-end mt-3"},[_c('v-btn',{staticClass:"font-weight-medium mt-1 mx-1",attrs:{"color":"error"},on:{"click":function($event){_vm.isShownCourseConfirmDialog = true}}},[_vm._v("\n                "+_vm._s(_vm.$t('delete_course'))+"\n              ")]),_vm._v(" "),_c('v-btn',{staticClass:"font-weight-medium mt-1 mx-1",attrs:{"color":"primary","disabled":!_vm.valid,"type":"submit"}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"18","height":"18","viewBox":"0 0 18 18"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#save-icon")}})]),_vm._v("\n                "+_vm._s(_vm.$t('save_changes'))+"\n              ")])],1)])],1)],1)]),_vm._v(" "),_c('illustration-dialog',{key:_vm.key,attrs:{"is-shown-illustration-dialog":_vm.isShownIllustrationDialog,"current-illustration":_vm.currentIllustration},on:{"update-image":_vm.updateImage,"close-dialog":function($event){_vm.isShownIllustrationDialog = false}}}),_vm._v(" "),_c('confirm-dialog',{attrs:{"is-shown-confirm-dialog":_vm.isShownIllustrationConfirmDialog},on:{"confirm":_vm.removeImage,"close-dialog":function($event){_vm.isShownIllustrationConfirmDialog = false}}},[_vm._v("\n      "+_vm._s(_vm.$t(
          'illustration_will_be_deleted_from_course_you_will_have_to_choose_another_one'
        ))+"\n      "),_c('br'),_vm._v("\n      "+_vm._s(_vm.$t('do_you_confirm_that'))+"\n    ")]),_vm._v(" "),_c('confirm-dialog',{attrs:{"is-shown-confirm-dialog":_vm.isShownCourseConfirmDialog,"cancel-text-button":"no","confirm-text-button":"yes"},on:{"confirm":_vm.removeCourse,"close-dialog":function($event){_vm.isShownCourseConfirmDialog = false}}},[_vm._v("\n      "+_vm._s(_vm.$t('are_you_sure_you_want_to_delete_this_course_permanently'))+"\n    ")])],1)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/CourseItem.vue?vue&type=template&id=7415d14b&

// EXTERNAL MODULE: ./components/form/TextInput.vue + 4 modules
var TextInput = __webpack_require__(102);

// EXTERNAL MODULE: ./components/user-settings/LessonPrice.vue + 4 modules
var LessonPrice = __webpack_require__(1114);

// EXTERNAL MODULE: ./components/user-settings/UserSettingSelect.vue + 4 modules
var UserSettingSelect = __webpack_require__(1013);

// EXTERNAL MODULE: ./components/form/Editor.vue + 4 modules
var Editor = __webpack_require__(942);

// EXTERNAL MODULE: ./components/user-settings/IllustrationDialog.vue + 4 modules
var IllustrationDialog = __webpack_require__(1199);

// EXTERNAL MODULE: ./components/ConfirmDialog.vue + 4 modules
var ConfirmDialog = __webpack_require__(985);

// EXTERNAL MODULE: external "@mdi/js"
var js_ = __webpack_require__(48);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/CourseItem.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//







/* harmony default export */ var CourseItemvue_type_script_lang_js_ = ({
  name: 'CourseItem',
  components: {
    TextInput: TextInput["default"],
    LessonPrice: LessonPrice["default"],
    UserSettingSelect: UserSettingSelect["default"],
    Editor: Editor["default"],
    IllustrationDialog: IllustrationDialog["default"],
    ConfirmDialog: ConfirmDialog["default"]
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    languages: {
      type: Array,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    isActive: {
      type: Boolean,
      required: true
    }
  },

  data() {
    return {
      mdiMinus: js_["mdiMinus"],
      title: '',
      key: 1,
      formValid: true,
      priceValid: true,
      courseStructureValid: true,
      rules: {
        name: [v => v && v.length <= 50, () => this.titleIsUnique || this.$t('name_should_be_unique')],
        shortDescription: [v => v && v.length <= 250],
        description: [v => v && v.length <= 500]
      },
      isShownIllustrationDialog: false,
      isShownIllustrationConfirmDialog: false,
      isShownCourseConfirmDialog: false,
      currentSelectedDuration: 30
    };
  },

  computed: {
    lessonCountItems() {
      return this.$store.state.settings.lessonCountItems;
    },

    lessonLengthItems() {
      return this.$store.state.settings.lessonLengthItems;
    },

    totalPackagePrice() {
      return this.item.price * this.item.lessons;
    },

    illustrationItems() {
      return this.$store.state.settings.illustrationItems;
    },

    currentIllustration() {
      if (!this.item.image) return {};
      const [item] = this.illustrationItems.filter(el => el.image === this.item.image);
      return item;
    },

    isPublish: {
      get() {
        return this.item.isPublish;
      },

      set(value) {
        this.updateValue(value, 'isPublish');
      }

    },

    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol'];
    },

    courses() {
      return this.$store.state.settings.courseItems;
    },

    titleIsUnique() {
      return !this.courses.filter(item => item.id && item.id !== this.item.id || item.uid && item.uid !== this.item.uid).map(item => item.name).includes(this.item.name);
    },

    valid() {
      var _this$currentIllustra;

      return this.titleIsUnique && this.formValid && this.priceValid && !!((_this$currentIllustra = this.currentIllustration) !== null && _this$currentIllustra !== void 0 && _this$currentIllustra.id) && this.courseStructureValid;
    }

  },
  watch: {
    titleIsUnique(newValue, oldValue) {
      var _this$$refs$;

      (_this$$refs$ = this.$refs[`form-${this.index}`]) === null || _this$$refs$ === void 0 ? void 0 : _this$$refs$.validate();
    }

  },

  mounted() {
    this.setTitle();
  },

  methods: {
    setTitle() {
      this.title = this.item.name || `${this.$t('course')} ${this.index + 1}`;
    },

    updateImage(value) {
      this.isShownIllustrationDialog = false;
      this.updateValue(value, 'image');
    },

    updatePriceTrialLesson(value) {
      this.updateValue(value ? Number.parseFloat(value) : 0.0, 'price');
    },

    removeImage() {
      this.isShownIllustrationConfirmDialog = false;
      this.updateValue(null, 'image');
      this.key++;
    },

    updateCourseStructure(value) {
      this.updateValue(value, 'courseStructure');
    },

    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_COURSE_ITEM', { ...this.item,
        [property]: value
      });
    },

    async removeCourse() {
      if (this.item.id) {
        await this.$store.dispatch('settings/removeCourse', this.item.id);
      }

      this.$store.commit('settings/REMOVE_COURSE_ITEM', this.item);
      this.isShownCourseConfirmDialog = false;
      this.$emit('scroll-to-top');
    },

    saveCourse() {
      const item = { ...this.item
      };
      delete item.slug;
      this.$store.dispatch(`settings/${this.item.uid ? 'addCourse' : 'updateCourse'}`, item).then(() => this.setTitle());
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/CourseItem.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_CourseItemvue_type_script_lang_js_ = (CourseItemvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanel.js
var VExpansionPanel = __webpack_require__(1072);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelContent.js
var VExpansionPanelContent = __webpack_require__(1073);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelHeader.js
var VExpansionPanelHeader = __webpack_require__(1074);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(896);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(87);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VSwitch/VSwitch.js
var VSwitch = __webpack_require__(1298);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(897);

// CONCATENATED MODULE: ./components/user-settings/CourseItem.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1222)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_CourseItemvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "506028bd"
  
)

/* harmony default export */ var CourseItem = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingSelect: __webpack_require__(1013).default,ConfirmDialog: __webpack_require__(985).default})


/* vuetify-loader */












installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VExpansionPanel: VExpansionPanel["a" /* default */],VExpansionPanelContent: VExpansionPanelContent["a" /* default */],VExpansionPanelHeader: VExpansionPanelHeader["a" /* default */],VForm: VForm["a" /* default */],VIcon: VIcon["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */],VSwitch: VSwitch["a" /* default */],VTextarea: VTextarea["a" /* default */]})


/***/ }),

/***/ 1273:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/SpecialityDialog.vue?vue&type=template&id=437fbe4b&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({directives:[{name:"resize",rawName:"v-resize",value:(_vm.onResize),expression:"onResize"}],attrs:{"dialog":_vm.isShownSpecialitiesDialog,"max-width":"820","custom-class":"speciality-picker"}},_vm.$listeners),[_c('div',{ref:"header",staticClass:"header"},[_c('div',{staticClass:"speciality-picker-title font-weight-medium"},[_vm._v("\n      "+_vm._s(_vm.$t('manage_specialties'))+":\n    ")]),_vm._v(" "),_c('div',{staticClass:"speciality-picker-text body-2 mt-2"},[_c('ul',[_c('li',[_vm._v("1. "+_vm._s(_vm.$t('drag_your_teaching_specialities')))]),_vm._v(" "),_c('li',[_vm._v("\n          2.\n          "+_vm._s(_vm.$t(
              'top_specialities_will_be_featured_on_teacher_search_results_page'
            ))+"\n        ")])])]),_vm._v(" "),_c('v-row',{staticClass:"my-0"},[_c('v-col',{staticClass:"col-6 py-0"},[_c('div',{staticClass:"text--gradient text-center subtitle-2 font-weight-medium mt-3 mb-1"},[_vm._v("\n          "+_vm._s(_vm.$t('available_specialties'))+":\n        ")])]),_vm._v(" "),_c('v-col',{staticClass:"col-6 py-0"},[_c('div',{staticClass:"text--gradient text-center subtitle-2 font-weight-medium mt-3 mb-1"},[_vm._v("\n          "+_vm._s(_vm.$t('selected_specialties'))+":\n        ")])])],1)],1),_vm._v(" "),_c('div',{staticClass:"speciality-picker-content",style:({ height: _vm.contentElHeight })},[_c('v-row',{staticClass:"my-0"},[_c('v-col',{staticClass:"col-6 py-0"},[_c('div',{staticClass:"column"},[_c('div',{staticClass:"column-helper l-scroll l-scroll--dark-grey l-scroll--large"},[_c('div',{staticClass:"column-content"},[_c('draggable',{staticClass:"list-group",attrs:{"group":"specialities"},on:{"end":_vm.onEnd},model:{value:(_vm.availableSpecializations),callback:function ($$v) {_vm.availableSpecializations=$$v},expression:"availableSpecializations"}},_vm._l((_vm.availableSpecializations),function(element){return _c('div',{key:element.name,staticClass:"list-group-item body-1"},[_vm._v("\n                  "+_vm._s(element.name)+"\n                ")])}),0)],1)])])]),_vm._v(" "),_c('v-col',{staticClass:"col-6 py-0"},[_c('div',{staticClass:"column l-scroll"},[_c('div',{staticClass:"column-helper l-scroll--dark-grey l-scroll--large"},[_c('div',{staticClass:"column-content"},[_c('draggable',{staticClass:"list-group",attrs:{"group":"specialities"},model:{value:(_vm.teacherSpecialities),callback:function ($$v) {_vm.teacherSpecialities=$$v},expression:"teacherSpecialities"}},_vm._l((_vm.teacherSpecialities),function(element,idx){return _c('div',{key:element.name,class:[
                    'list-group-item body-1',
                    { 'highest-priority': idx < 3 } ]},[_vm._v("\n                  "+_vm._s(element.name)+"\n                ")])}),0)],1)])])])],1),_vm._v(" "),_c('div',{staticClass:"speciality-picker-bottom d-flex justify-end"},[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"primary"},on:{"click":_vm.submitData}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"18","height":"18","viewBox":"0 0 18 18"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#save-icon")}})]),_vm._v("\n        "+_vm._s(_vm.$t('save_changes'))+"\n      ")])],1)],1)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/SpecialityDialog.vue?vue&type=template&id=437fbe4b&

// EXTERNAL MODULE: external "vuedraggable"
var external_vuedraggable_ = __webpack_require__(865);
var external_vuedraggable_default = /*#__PURE__*/__webpack_require__.n(external_vuedraggable_);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/SpecialityDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var SpecialityDialogvue_type_script_lang_js_ = ({
  name: 'SpecialityDialog',
  components: {
    draggable: external_vuedraggable_default.a
  },
  props: {
    isShownSpecialitiesDialog: {
      type: Boolean,
      required: true
    }
  },

  data() {
    return {
      contentElHeight: 'auto'
    };
  },

  computed: {
    availableSpecializations: {
      get() {
        return this.$store.getters['settings/availableSpecializations'];
      },

      set(value) {
        this.$store.commit('settings/UPDATE_AVAILABLE_SPECIALIZATIONS', value);
      }

    },
    teacherSpecialities: {
      get() {
        return this.$store.getters['settings/teacherSpecialities'];
      },

      set(value) {
        this.$store.commit('settings/UPDATE_TEACHER_SPECIALITIES', value);
      }

    }
  },
  watch: {
    isShownSpecialitiesDialog(newValue, oldValue) {
      if (newValue) {
        this.$nextTick(() => {
          window.setTimeout(() => this.setContentElHeight());
        });
      }
    }

  },
  methods: {
    onEnd(e) {
      if (this.teacherSpecialities.length > 8) {
        const _availableSpecializations = [...this.availableSpecializations];
        const _teacherSpecialities = [...this.teacherSpecialities];

        _availableSpecializations.splice(e.oldIndex, 0, _teacherSpecialities[e.newIndex]);

        _teacherSpecialities.splice(e.newIndex, 1);

        this.$store.commit('settings/UPDATE_AVAILABLE_SPECIALIZATIONS', _availableSpecializations);
        this.$store.commit('settings/UPDATE_TEACHER_SPECIALITIES', _teacherSpecialities);
      }
    },

    setContentElHeight() {
      var _this$$refs$header$cl, _this$$refs$header;

      this.contentElHeight = `calc(100% - ${(_this$$refs$header$cl = (_this$$refs$header = this.$refs.header) === null || _this$$refs$header === void 0 ? void 0 : _this$$refs$header.clientHeight) !== null && _this$$refs$header$cl !== void 0 ? _this$$refs$header$cl : 0}px)`;
    },

    onResize() {
      this.setContentElHeight();
    },

    submitData() {
      this.$store.dispatch('settings/updateSpecialities').then(() => this.$emit('close-dialog'));
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/SpecialityDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_SpecialityDialogvue_type_script_lang_js_ = (SpecialityDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installDirectives.js
var installDirectives = __webpack_require__(430);
var installDirectives_default = /*#__PURE__*/__webpack_require__.n(installDirectives);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/resize/index.js
var resize = __webpack_require__(32);

// CONCATENATED MODULE: ./components/user-settings/SpecialityDialog.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1225)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_SpecialityDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "3bb9d92c"
  
)

/* harmony default export */ var SpecialityDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LDialog: __webpack_require__(28).default})


/* vuetify-loader */




installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VRow: VRow["a" /* default */]})


/* vuetify-loader */


installDirectives_default()(component, {Resize: resize["a" /* default */]})


/***/ }),

/***/ 1274:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/AddQualificationDialog.vue?vue&type=template&id=3d08a31a&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{"dialog":_vm.isShownQualificationDialog,"max-width":"844","custom-class":"qualification-dialog"},scopedSlots:_vm._u([{key:"footer",fn:function(){return [_c('div',{staticClass:"d-flex justify-end"},[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"primary","disabled":!_vm.valid},on:{"click":_vm.add}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"18","height":"18","viewBox":"0 0 18 18"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#save-icon")}})]),_vm._v("\n        "+_vm._s(_vm.$t('save_changes'))+"\n      ")])],1)]},proxy:true}])},_vm.$listeners),[_c('v-form',{model:{value:(_vm.valid),callback:function ($$v) {_vm.valid=$$v},expression:"valid"}},[_c('div',{staticClass:"mt-3 mt-sm-4"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-sm-6 pt-0 d-sm-flex align-end"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label font-weight-medium mb-1 mb-sm-2 mb-md-3"},[_vm._v("\n              "+_vm._s(_vm.$t('what_is_name_of_teaching_qualification'))+"\n            ")]),_vm._v(" "),_c('text-input',{attrs:{"type-class":"border-gradient","height":"44","hide-details":"","rules":_vm.rules.name},model:{value:(_vm.item.name),callback:function ($$v) {_vm.$set(_vm.item, "name", $$v)},expression:"item.name"}})],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 pt-0 d-flex align-end"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label font-weight-medium mt-1 mt-sm-0 mb-1 mb-sm-2 mb-md-3"},[_vm._v("\n              "+_vm._s(_vm.$t('please_upload_copy_of_qualification_certificate'))+"\n            ")]),_vm._v(" "),_c('div',{staticClass:"upload-file"},[_c('div',{staticClass:"d-flex"},[_c('v-btn',{staticClass:"gradient font-weight-medium",on:{"click":function($event){_vm.$refs.fileQualification.$el.querySelector('input').click()}}},[_c('div',[_c('v-img',{staticClass:"mr-1",attrs:{"src":__webpack_require__(574),"width":"20","height":"20"}})],1),_vm._v(" "),_c('div',{staticClass:"text--gradient"},[_vm._v("\n                    "+_vm._s(_vm.$t('choose_file'))+"\n                  ")])]),_vm._v(" "),_c('div',{staticClass:"d-flex align-center ml-2 body-2"},[(!_vm.item.file)?[_vm._v("\n                    "+_vm._s(_vm.$t('no_file_chosen'))+"\n                  ")]:[_c('div',{staticClass:"upload-file-name"},[_vm._v("\n                      "+_vm._s(_vm.item.file.name)+"\n                      "),_c('v-btn',{staticClass:"file-remove-btn",attrs:{"width":"18","height":"18","icon":""},on:{"click":function($event){_vm.item.file = null}}},[_c('v-img',{attrs:{"src":__webpack_require__(105),"width":"15","height":"15"}})],1)],1)]],2)],1),_vm._v(" "),_c('div',{staticClass:"qualification-dialog-input"},[_c('v-file-input',{ref:"fileQualification",staticClass:"l-file-input l-file-input--input-hidden mt-0",attrs:{"rules":_vm.rules.file,"prepend-icon":"","accept":"image/png, image/jpeg, image/bmp, application/pdf"},model:{value:(_vm.item.file),callback:function ($$v) {_vm.$set(_vm.item, "file", $$v)},expression:"item.file"}})],1)])])])],1)],1)])],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/AddQualificationDialog.vue?vue&type=template&id=3d08a31a&

// EXTERNAL MODULE: ./components/form/TextInput.vue + 4 modules
var TextInput = __webpack_require__(102);

// EXTERNAL MODULE: ./components/LDialog.vue + 5 modules
var LDialog = __webpack_require__(28);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/AddQualificationDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



const defaultItem = () => ({
  name: '',
  file: null,
  verified: null
});

/* harmony default export */ var AddQualificationDialogvue_type_script_lang_js_ = ({
  name: 'AddQualificationDialog',
  components: {
    LDialog: LDialog["default"],
    TextInput: TextInput["default"]
  },
  props: {
    isShownQualificationDialog: {
      type: Boolean,
      required: true
    }
  },

  data() {
    return {
      valid: true,
      rules: {
        name: [v => !!v && v.length > 1],
        file: [v => !!v, v => !v || v.size < 6000000 || this.$t('file_size_should_be_less_than', {
          value: '6 MB'
        })]
      },
      item: defaultItem()
    };
  },

  methods: {
    add() {
      this.$store.dispatch('settings/addTeachingQualification', this.item).then(data => {
        this.$store.commit('settings/ADD_TEACHING_QUALIFICATION_ITEM', { ...this.item,
          ...data
        });
        this.item = defaultItem();
        this.$emit('qualification-added');
      });
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/AddQualificationDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_AddQualificationDialogvue_type_script_lang_js_ = (AddQualificationDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VFileInput/VFileInput.js
var VFileInput = __webpack_require__(1163);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(896);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/user-settings/AddQualificationDialog.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1229)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_AddQualificationDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "241a9a25"
  
)

/* harmony default export */ var AddQualificationDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LDialog: __webpack_require__(28).default})


/* vuetify-loader */







installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VFileInput: VFileInput["a" /* default */],VForm: VForm["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1275:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/QualificationSuccessDialog.vue?vue&type=template&id=1fa2a67e&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('l-dialog',_vm._g({attrs:{"dialog":_vm.isShownDialog,"max-width":"418","custom-class":"qualification-added text-center"}},_vm.$listeners),[_c('div',[_c('v-img',{staticClass:"mx-auto mb-3",attrs:{"src":__webpack_require__(621),"width":"56","height":"56"}}),_vm._v(" "),_c('div',{staticClass:"qualification-added-text"},[_vm._v("\n      "+_vm._s(_vm.$t(
          'thank_you_for_saving_your_qualification_it_will_shortly_be_verified_by_langu_admin'
        ))+"\n    ")])],1)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/QualificationSuccessDialog.vue?vue&type=template&id=1fa2a67e&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/QualificationSuccessDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var QualificationSuccessDialogvue_type_script_lang_js_ = ({
  name: 'QualificationSuccessDialog',
  props: {
    isShownDialog: {
      type: Boolean,
      required: true
    }
  }
});
// CONCATENATED MODULE: ./components/user-settings/QualificationSuccessDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_QualificationSuccessDialogvue_type_script_lang_js_ = (QualificationSuccessDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-settings/QualificationSuccessDialog.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_QualificationSuccessDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "40ac936b"
  
)

/* harmony default export */ var QualificationSuccessDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LDialog: __webpack_require__(28).default})


/* vuetify-loader */


installComponents_default()(component, {VImg: VImg["a" /* default */]})


/***/ }),

/***/ 1290:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_BasicInfo_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1218);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_BasicInfo_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_BasicInfo_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_BasicInfo_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_BasicInfo_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1291:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-settings-avatar .v-avatar{cursor:pointer}.user-settings-email,.user-settings-nickname{position:relative;padding-left:32px}.user-settings-email svg,.user-settings-nickname svg{position:absolute;left:0;top:2px}.user-settings-email a,.user-settings-nickname a{color:inherit!important;text-decoration:none;transition:color .3s}.user-settings-email a:hover,.user-settings-nickname a:hover{color:var(--v-orange-base)!important}@media only screen and (min-width:768px){.user-settings-email{margin-bottom:12px}}.user-settings-nickname .input-wrap-notice{font-size:12px!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1292:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutMeInfo_vue_vue_type_style_index_0_id_d9792938_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1219);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutMeInfo_vue_vue_type_style_index_0_id_d9792938_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutMeInfo_vue_vue_type_style_index_0_id_d9792938_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutMeInfo_vue_vue_type_style_index_0_id_d9792938_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_AboutMeInfo_vue_vue_type_style_index_0_id_d9792938_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1293:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".download-cv[data-v-d9792938],.upload-cv[data-v-d9792938]{position:relative;display:inline-block}.download-cv .v-btn[data-v-d9792938],.upload-cv .v-btn[data-v-d9792938]{min-width:154px!important}.download-cv .v-btn[data-v-d9792938]{margin-left:32px;font-size:16px;cursor:pointer}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1294:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PricingTableInfo_vue_vue_type_style_index_0_id_b41b2052_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1220);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PricingTableInfo_vue_vue_type_style_index_0_id_b41b2052_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PricingTableInfo_vue_vue_type_style_index_0_id_b41b2052_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PricingTableInfo_vue_vue_type_style_index_0_id_b41b2052_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PricingTableInfo_vue_vue_type_style_index_0_id_b41b2052_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1295:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".current-currency[data-v-b41b2052]{font-size:16px}.currency-input[data-v-b41b2052]{width:84px}.lesson-pricing[data-v-b41b2052]{display:inline-block}@media only screen and (max-width:479px){.lesson-pricing[data-v-b41b2052]{width:calc(100% + 20px);margin-left:-10px}}.lesson-pricing-row[data-v-b41b2052]{position:relative;display:flex;align-items:center}.lesson-pricing-row[data-v-b41b2052]:before{content:\"\";position:absolute;bottom:0;right:0;width:calc(100% - 67px);height:1px;background-color:rgba(45,45,45,.06)}@media only screen and (max-width:991px){.lesson-pricing-row[data-v-b41b2052]:before{width:calc(100% - 35px)}}@media only screen and (max-width:479px){.lesson-pricing-row[data-v-b41b2052]:before{width:100%;left:0}}.lesson-pricing-row[data-v-b41b2052]:first-child{font-size:14px;font-weight:500}.lesson-pricing-row[data-v-b41b2052]:first-child:before,.lesson-pricing-row[data-v-b41b2052]:last-child:before{display:none}.lesson-pricing .item[data-v-b41b2052]{text-align:center}.lesson-pricing .item[data-v-b41b2052]:first-child{width:35px;font-size:14px;font-weight:500}.lesson-pricing .item[data-v-b41b2052]:not(:first-child){width:84px}.lesson-pricing .item_text[data-v-b41b2052]{display:flex;justify-content:flex-start;align-items:center;padding-left:20px;font-size:14px;min-width:196px;font-weight:400}@media(max-width:768px){.lesson-pricing .item_text[data-v-b41b2052]{justify-content:center;padding-left:0;padding-top:8px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1296:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PricingTableInfo_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1221);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PricingTableInfo_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PricingTableInfo_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PricingTableInfo_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_PricingTableInfo_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1297:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".price-input .v-input__slot{padding:0 10px 0 8px!important}.price-input .v-text-field__prefix{letter-spacing:-.2px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1298:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(935);
/* harmony import */ var _src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_styles_components_selection_controls_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _src_components_VSwitch_VSwitch_sass__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(1186);
/* harmony import */ var _src_components_VSwitch_VSwitch_sass__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_src_components_VSwitch_VSwitch_sass__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _mixins_selectable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(936);
/* harmony import */ var _VInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(20);
/* harmony import */ var _directives_touch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(432);
/* harmony import */ var _transitions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(67);
/* harmony import */ var _VProgressCircular_VProgressCircular__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(85);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(0);
// Styles

 // Mixins


 // Directives

 // Components


 // Helpers


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (_mixins_selectable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"].extend({
  name: 'v-switch',
  directives: {
    Touch: _directives_touch__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"]
  },
  props: {
    inset: Boolean,
    loading: {
      type: [Boolean, String],
      default: false
    },
    flat: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    classes() {
      return { ..._VInput__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"].options.computed.classes.call(this),
        'v-input--selection-controls v-input--switch': true,
        'v-input--switch--flat': this.flat,
        'v-input--switch--inset': this.inset
      };
    },

    attrs() {
      return {
        'aria-checked': String(this.isActive),
        'aria-disabled': String(this.isDisabled),
        role: 'switch'
      };
    },

    // Do not return undefined if disabled,
    // according to spec, should still show
    // a color when disabled and active
    validationState() {
      if (this.hasError && this.shouldValidate) return 'error';
      if (this.hasSuccess) return 'success';
      if (this.hasColor !== null) return this.computedColor;
      return undefined;
    },

    switchData() {
      return this.setTextColor(this.loading ? undefined : this.validationState, {
        class: this.themeClasses
      });
    }

  },
  methods: {
    genDefaultSlot() {
      return [this.genSwitch(), this.genLabel()];
    },

    genSwitch() {
      return this.$createElement('div', {
        staticClass: 'v-input--selection-controls__input'
      }, [this.genInput('checkbox', { ...this.attrs,
        ...this.attrs$
      }), this.genRipple(this.setTextColor(this.validationState, {
        directives: [{
          name: 'touch',
          value: {
            left: this.onSwipeLeft,
            right: this.onSwipeRight
          }
        }]
      })), this.$createElement('div', {
        staticClass: 'v-input--switch__track',
        ...this.switchData
      }), this.$createElement('div', {
        staticClass: 'v-input--switch__thumb',
        ...this.switchData
      }, [this.genProgress()])]);
    },

    genProgress() {
      return this.$createElement(_transitions__WEBPACK_IMPORTED_MODULE_5__[/* VFabTransition */ "c"], {}, [this.loading === false ? null : this.$slots.progress || this.$createElement(_VProgressCircular_VProgressCircular__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"], {
        props: {
          color: this.loading === true || this.loading === '' ? this.color || 'primary' : this.loading,
          size: 16,
          width: 2,
          indeterminate: true
        }
      })]);
    },

    onSwipeLeft() {
      if (this.isActive) this.onChange();
    },

    onSwipeRight() {
      if (!this.isActive) this.onChange();
    },

    onKeydown(e) {
      if (e.keyCode === _util_helpers__WEBPACK_IMPORTED_MODULE_7__[/* keyCodes */ "s"].left && this.isActive || e.keyCode === _util_helpers__WEBPACK_IMPORTED_MODULE_7__[/* keyCodes */ "s"].right && !this.isActive) this.onChange();
    }

  }
}));

/***/ }),

/***/ 1299:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CoursesInfo_vue_vue_type_style_index_0_id_e39f7fc0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1224);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CoursesInfo_vue_vue_type_style_index_0_id_e39f7fc0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CoursesInfo_vue_vue_type_style_index_0_id_e39f7fc0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CoursesInfo_vue_vue_type_style_index_0_id_e39f7fc0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CoursesInfo_vue_vue_type_style_index_0_id_e39f7fc0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1300:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".border-top[data-v-e39f7fc0]{border-top:1px solid #ccc}.v-expansion-panel[data-v-e39f7fc0]:before{box-shadow:none}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1301:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachingPreferencesInfo_vue_vue_type_style_index_0_id_22c932db_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1227);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachingPreferencesInfo_vue_vue_type_style_index_0_id_22c932db_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachingPreferencesInfo_vue_vue_type_style_index_0_id_22c932db_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachingPreferencesInfo_vue_vue_type_style_index_0_id_22c932db_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachingPreferencesInfo_vue_vue_type_style_index_0_id_22c932db_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1302:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".checkbox[data-v-22c932db]:not(:last-child){margin-bottom:20px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1303:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LearningPreferencesInfo_vue_vue_type_style_index_0_id_c405d942_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1228);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LearningPreferencesInfo_vue_vue_type_style_index_0_id_c405d942_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LearningPreferencesInfo_vue_vue_type_style_index_0_id_c405d942_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LearningPreferencesInfo_vue_vue_type_style_index_0_id_c405d942_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LearningPreferencesInfo_vue_vue_type_style_index_0_id_c405d942_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1304:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".checkbox[data-v-c405d942]:not(:last-child){margin-bottom:20px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1305:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachingQualificationsInfo_vue_vue_type_style_index_0_id_614b28e4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1231);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachingQualificationsInfo_vue_vue_type_style_index_0_id_614b28e4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachingQualificationsInfo_vue_vue_type_style_index_0_id_614b28e4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachingQualificationsInfo_vue_vue_type_style_index_0_id_614b28e4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachingQualificationsInfo_vue_vue_type_style_index_0_id_614b28e4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1306:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".qualifications-wrap[data-v-614b28e4]{max-width:400px}.qualifications-wrap .item--verified[data-v-614b28e4]{position:relative;padding-left:22px}.qualifications-wrap .item--verified svg[data-v-614b28e4]{position:absolute;left:0;top:50%;transform:translateY(-50%)}.qualifications .btn-add[data-v-614b28e4]{min-width:106px!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1307:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1308);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("7c06aa28", content, true)

/***/ }),

/***/ 1308:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-data-table{background-color:#fff;color:rgba(0,0,0,.87)}.theme--light.v-data-table .v-data-table__divider{border-right:thin solid rgba(0,0,0,.12)}.theme--light.v-data-table.v-data-table--fixed-header thead th{background:#fff;box-shadow:inset 0 -1px 0 rgba(0,0,0,.12)}.theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr>th{color:rgba(0,0,0,.6)}.theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:last-child,.theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:not(.v-data-table__mobile-row),.theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:last-child,.theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:not(.v-data-table__mobile-row),.theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr:last-child>th{border-bottom:thin solid rgba(0,0,0,.12)}.theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr.active{background:#f5f5f5}.theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:hover:not(.v-data-table__expanded__content):not(.v-data-table__empty-wrapper){background:#eee}.theme--dark.v-data-table{background-color:#1e1e1e;color:#fff}.theme--dark.v-data-table .v-data-table__divider{border-right:thin solid hsla(0,0%,100%,.12)}.theme--dark.v-data-table.v-data-table--fixed-header thead th{background:#1e1e1e;box-shadow:inset 0 -1px 0 hsla(0,0%,100%,.12)}.theme--dark.v-data-table>.v-data-table__wrapper>table>thead>tr>th{color:hsla(0,0%,100%,.7)}.theme--dark.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:last-child,.theme--dark.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:not(.v-data-table__mobile-row),.theme--dark.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:last-child,.theme--dark.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:not(.v-data-table__mobile-row),.theme--dark.v-data-table>.v-data-table__wrapper>table>thead>tr:last-child>th{border-bottom:thin solid hsla(0,0%,100%,.12)}.theme--dark.v-data-table>.v-data-table__wrapper>table>tbody>tr.active{background:#505050}.theme--dark.v-data-table>.v-data-table__wrapper>table>tbody>tr:hover:not(.v-data-table__expanded__content):not(.v-data-table__empty-wrapper){background:#616161}.v-data-table{line-height:1.5;max-width:100%}.v-data-table>.v-data-table__wrapper>table{width:100%;border-spacing:0}.v-data-table>.v-data-table__wrapper>table>tbody>tr>td,.v-data-table>.v-data-table__wrapper>table>tbody>tr>th,.v-data-table>.v-data-table__wrapper>table>tfoot>tr>td,.v-data-table>.v-data-table__wrapper>table>tfoot>tr>th,.v-data-table>.v-data-table__wrapper>table>thead>tr>td,.v-data-table>.v-data-table__wrapper>table>thead>tr>th{padding:0 16px;transition:height .2s cubic-bezier(.4,0,.6,1)}.v-data-table>.v-data-table__wrapper>table>tbody>tr>th,.v-data-table>.v-data-table__wrapper>table>tfoot>tr>th,.v-data-table>.v-data-table__wrapper>table>thead>tr>th{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;font-size:12px;height:48px}.v-application--is-ltr .v-data-table>.v-data-table__wrapper>table>tbody>tr>th,.v-application--is-ltr .v-data-table>.v-data-table__wrapper>table>tfoot>tr>th,.v-application--is-ltr .v-data-table>.v-data-table__wrapper>table>thead>tr>th{text-align:left}.v-application--is-rtl .v-data-table>.v-data-table__wrapper>table>tbody>tr>th,.v-application--is-rtl .v-data-table>.v-data-table__wrapper>table>tfoot>tr>th,.v-application--is-rtl .v-data-table>.v-data-table__wrapper>table>thead>tr>th{text-align:right}.v-data-table>.v-data-table__wrapper>table>tbody>tr>td,.v-data-table>.v-data-table__wrapper>table>tfoot>tr>td,.v-data-table>.v-data-table__wrapper>table>thead>tr>td{font-size:16px;height:48px}.v-data-table__wrapper{overflow-x:auto;overflow-y:hidden}.v-data-table__progress{height:auto!important}.v-data-table__progress th{height:auto!important;border:none!important;padding:0;position:relative}.v-data-table--dense>.v-data-table__wrapper>table>tbody>tr>td,.v-data-table--dense>.v-data-table__wrapper>table>tbody>tr>th,.v-data-table--dense>.v-data-table__wrapper>table>tfoot>tr>td,.v-data-table--dense>.v-data-table__wrapper>table>tfoot>tr>th,.v-data-table--dense>.v-data-table__wrapper>table>thead>tr>td,.v-data-table--dense>.v-data-table__wrapper>table>thead>tr>th{height:32px}.v-data-table--has-top>.v-data-table__wrapper>table>tbody>tr:first-child:hover>td:first-child{border-top-left-radius:0}.v-data-table--has-top>.v-data-table__wrapper>table>tbody>tr:first-child:hover>td:last-child{border-top-right-radius:0}.v-data-table--has-bottom>.v-data-table__wrapper>table>tbody>tr:last-child:hover>td:first-child{border-bottom-left-radius:0}.v-data-table--has-bottom>.v-data-table__wrapper>table>tbody>tr:last-child:hover>td:last-child{border-bottom-right-radius:0}.v-data-table--fixed-header>.v-data-table__wrapper,.v-data-table--fixed-height .v-data-table__wrapper{overflow-y:auto}.v-data-table--fixed-header>.v-data-table__wrapper>table>thead>tr>th{border-bottom:0!important;position:sticky;top:0;z-index:2}.v-data-table--fixed-header>.v-data-table__wrapper>table>thead>tr:nth-child(2)>th{top:48px}.v-application--is-ltr .v-data-table--fixed-header .v-data-footer{margin-right:17px}.v-application--is-rtl .v-data-table--fixed-header .v-data-footer{margin-left:17px}.v-data-table--fixed-header.v-data-table--dense>.v-data-table__wrapper>table>thead>tr:nth-child(2)>th{top:32px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1309:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReceiptInfo_vue_vue_type_style_index_0_id_af7ad3a4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1232);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReceiptInfo_vue_vue_type_style_index_0_id_af7ad3a4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReceiptInfo_vue_vue_type_style_index_0_id_af7ad3a4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReceiptInfo_vue_vue_type_style_index_0_id_af7ad3a4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ReceiptInfo_vue_vue_type_style_index_0_id_af7ad3a4_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1310:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".checkbox[data-v-af7ad3a4]:not(:last-child){margin-bottom:20px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1372:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1444);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("47068e17", content, true, context)
};

/***/ }),

/***/ 1383:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/TeachingQualificationsInfo.vue?vue&type=template&id=614b28e4&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('user-setting-template',{staticClass:"qualifications",attrs:{"title":_vm.$t('teaching_qualifications'),"hide-footer":""}},[(_vm.items.length)?_c('div',{staticClass:"mb-3 mb-md-5"},[_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"qualifications-wrap"},[_c('v-simple-table',{scopedSlots:_vm._u([{key:"default",fn:function(){return [_c('thead',[_c('tr',[_c('th',{staticClass:"text-left subtitle-2 pb-2"},[_vm._v("\n                    "+_vm._s(_vm.$t('name'))+"\n                  ")]),_vm._v(" "),_c('th',{staticClass:"text-left subtitle-2 pb-2"},[_vm._v("\n                    "+_vm._s(_vm.$t('verified'))+"\n                  ")]),_vm._v(" "),_c('th',{staticClass:"text-left subtitle-2 pb-2"})])]),_vm._v(" "),_c('tbody',_vm._l((_vm.items),function(item,idx){return _c('tr',{key:idx},[_c('td',{staticClass:"body-1"},[_vm._v(_vm._s(item.name))]),_vm._v(" "),_c('td',[_c('div',{staticClass:"item--verified body-1"},[_c('svg',{attrs:{"width":"16","height":"16","viewBox":"0 0 16 16"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#" + (item.verified ? 'check' : 'close') + "-circle-icon")}})]),_vm._v("\n                      "+_vm._s(_vm.$t(item.verified ? 'yes' : 'no'))+"\n                    ")])]),_vm._v(" "),_c('td',[_c('v-btn',{attrs:{"icon":"","x-small":"","color":item.verified ? 'success' : 'error'},on:{"click":function($event){return _vm.removeClickHandler(item)}}},[_c('svg',{attrs:{"width":"20","height":"20","viewBox":"0 0 34 34"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#close-big")}})])])],1)])}),0)]},proxy:true}],null,false,363784176)})],1)])],1)],1):_vm._e(),_vm._v(" "),_c('div',[_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('v-btn',{staticClass:"btn-add gradient font-weight-medium",on:{"click":function($event){_vm.isShownQualificationDialog = true}}},[_c('div',{staticClass:"mr-1"},[_c('v-img',{attrs:{"src":__webpack_require__(515),"width":"20","height":"20"}})],1),_vm._v(" "),_c('div',{staticClass:"text--gradient"},[_vm._v("\n            "+_vm._s(_vm.$t('add'))+"\n          ")])])],1)],1)],1),_vm._v(" "),_c('add-qualification-dialog',{attrs:{"is-shown-qualification-dialog":_vm.isShownQualificationDialog},on:{"close-dialog":function($event){_vm.isShownQualificationDialog = false},"qualification-added":_vm.qualificationAdded}}),_vm._v(" "),_c('confirm-dialog',{attrs:{"is-shown-confirm-dialog":_vm.isShownConfirmDialog},on:{"confirm":_vm.removeQualification,"close-dialog":function($event){_vm.isShownConfirmDialog = false}}},[_vm._v("\n    "+_vm._s(_vm.$t('teaching_qualification_will_be_deleted_do_you_confirm_that'))+"\n  ")]),_vm._v(" "),_c('qualification-success-dialog',{attrs:{"is-shown-dialog":_vm.isShownQualificationAddedDialog},on:{"close-dialog":function($event){_vm.isShownQualificationAddedDialog = false}}})],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/TeachingQualificationsInfo.vue?vue&type=template&id=614b28e4&scoped=true&

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// EXTERNAL MODULE: ./components/user-settings/AddQualificationDialog.vue + 4 modules
var AddQualificationDialog = __webpack_require__(1274);

// EXTERNAL MODULE: ./components/ConfirmDialog.vue + 4 modules
var ConfirmDialog = __webpack_require__(985);

// EXTERNAL MODULE: ./components/user-settings/QualificationSuccessDialog.vue + 4 modules
var QualificationSuccessDialog = __webpack_require__(1275);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/TeachingQualificationsInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var TeachingQualificationsInfovue_type_script_lang_js_ = ({
  name: 'TeachingQualificationsInfo',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"],
    AddQualificationDialog: AddQualificationDialog["default"],
    ConfirmDialog: ConfirmDialog["default"],
    QualificationSuccessDialog: QualificationSuccessDialog["default"]
  },

  data() {
    return {
      isShownQualificationDialog: false,
      isShownConfirmDialog: false,
      isShownQualificationAddedDialog: false,
      selectedItem: null
    };
  },

  computed: {
    items() {
      return this.$store.state.settings.teachingQualificationItems;
    }

  },

  beforeCreate() {
    this.$store.dispatch('settings/getTeachingQualifications');
  },

  methods: {
    removeClickHandler(item) {
      this.isShownConfirmDialog = true;
      this.selectedItem = item;
    },

    async removeQualification() {
      if (this.selectedItem) {
        if (this.selectedItem.id) {
          await this.$store.dispatch('settings/removeTeachingQualification', this.selectedItem.id);
        }

        this.$store.commit('settings/REMOVE_TEACHING_QUALIFICATION_ITEM', this.selectedItem);
      }

      this.isShownConfirmDialog = false;
      this.selectedItem = null;
    },

    qualificationAdded() {
      this.isShownQualificationDialog = false;
      this.isShownQualificationAddedDialog = true;
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/TeachingQualificationsInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_TeachingQualificationsInfovue_type_script_lang_js_ = (TeachingQualificationsInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VDataTable/VSimpleTable.sass
var VSimpleTable = __webpack_require__(1307);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/helpers.js
var helpers = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/themeable/index.js
var themeable = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/mixins.js
var mixins = __webpack_require__(2);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VDataTable/VSimpleTable.js




/* harmony default export */ var VDataTable_VSimpleTable = (Object(mixins["a" /* default */])(themeable["a" /* default */]).extend({
  name: 'v-simple-table',
  props: {
    dense: Boolean,
    fixedHeader: Boolean,
    height: [Number, String]
  },
  computed: {
    classes() {
      return {
        'v-data-table--dense': this.dense,
        'v-data-table--fixed-height': !!this.height && !this.fixedHeader,
        'v-data-table--fixed-header': this.fixedHeader,
        'v-data-table--has-top': !!this.$slots.top,
        'v-data-table--has-bottom': !!this.$slots.bottom,
        ...this.themeClasses
      };
    }

  },
  methods: {
    genWrapper() {
      return this.$slots.wrapper || this.$createElement('div', {
        staticClass: 'v-data-table__wrapper',
        style: {
          height: Object(helpers["f" /* convertToUnit */])(this.height)
        }
      }, [this.$createElement('table', this.$slots.default)]);
    }

  },

  render(h) {
    return h('div', {
      staticClass: 'v-data-table',
      class: this.classes
    }, [this.$slots.top, this.genWrapper(), this.$slots.bottom]);
  }

}));
// CONCATENATED MODULE: ./components/user-settings/TeachingQualificationsInfo.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1305)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_TeachingQualificationsInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "614b28e4",
  "272f2c64"
  
)

/* harmony default export */ var TeachingQualificationsInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {ConfirmDialog: __webpack_require__(985).default,UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */






installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */],VSimpleTable: VDataTable_VSimpleTable})


/***/ }),

/***/ 1386:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/BasicInfo.vue?vue&type=template&id=40f9f4e9&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('user-setting-template',{attrs:{"title":_vm.$t('basic_info'),"submit-func":_vm.submitData}},[_c('div',{staticClass:"mb-3"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-7 mb-3 mb-sm-2"},[_c('div',{staticClass:"user-settings-avatar"},[_c('div',{staticClass:"d-flex flex-column flex-sm-row align-center justify-center justify-sm-start"},[_c('v-avatar',{staticClass:"mb-1 mb-sm-0 mr-sm-2",attrs:{"size":"124px"},on:{"click":function($event){_vm.$refs.fileAvatar.$el.querySelector('input').click()}}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(_vm.basicInfoItem.avatars, 'user_thumb_124x124'),"srcset":_vm.getSrcSetAvatar(
                    _vm.basicInfoItem.avatars,
                    'user_thumb_124x124',
                    'user_thumb_248x248'
                  ),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),_c('div',{staticClass:"text-center text-sm-left body-2"},[_vm._v("\n              "+_vm._s(_vm.$t('click_photo_to_upload_new_one'))+"\n            ")])],1),_vm._v(" "),_c('div',{staticClass:"input-wrap"},[_c('v-file-input',{ref:"fileAvatar",staticClass:"l-file-input pt-0",attrs:{"accept":"image/png, image/jpeg, image/bmp","prepend-icon":"","hide-input":""},on:{"change":_vm.updateAvatar},model:{value:(_vm.file),callback:function ($$v) {_vm.file=$$v},expression:"file"}}),_vm._v(" "),(!_vm.fileValid)?_c('div',{staticClass:"v-text-field__details"},[_c('div',{staticClass:"input-wrap-error"},[_c('div',{staticClass:"v-messages theme--light error--text",attrs:{"role":"alert"}},[_c('div',{staticClass:"v-messages__wrapper"},[_c('div',{staticClass:"v-messages__message"},[_vm._v("\n                      "+_vm._s(_vm.$t('file_size_should_be_less_than', { value: '6 MB' }))+"\n                    ")])])])])]):_vm._e()],1)])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 d-flex flex-column-reverse flex-sm-column justify-center align-start"},[_c('div',{staticClass:"user-settings-email body-1"},[_c('svg',{attrs:{"width":"20","height":"20","viewBox":"0 0 20 20"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#email-icon")}})]),_vm._v("\n          "+_vm._s(_vm.basicInfoItem.email)+"\n        ")]),_vm._v(" "),(_vm.isTeacher)?_c('div',{staticClass:"user-settings-nickname body-1 mb-1 mb-sm-0"},[_c('svg',{attrs:{"width":"20","height":"20","viewBox":"0 0 20 20"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#user-icon")}})]),_vm._v(" "),(_vm.$vuetify.breakpoint.smAndUp)?[_c('nuxt-link',{attrs:{"to":{ path: ("/teacher/" + (_vm.basicInfoItem.username)) },"target":"_blank"}},[_vm._v("\n              "+_vm._s(_vm.userLink)+"\n            ")]),_vm._v(" "),_c('div',{staticClass:"input-wrap-notice text--gradient"},[_vm._v("\n              "+_vm._s(_vm.$t('here_is_link_to_your_profile_feel_free_to_share'))+"\n            ")])]:[_c('div',{staticClass:"d-flex align-center"},[_c('div',[_vm._v(_vm._s(_vm.basicInfoItem.username))]),_vm._v(" "),_c('div',{staticClass:"d-flex align-center text--gradient ml-2"},[_c('v-img',{staticClass:"mr-1",attrs:{"src":__webpack_require__(549),"width":"16","height":"16"}}),_vm._v(" "),_c('input',{ref:"profileLink",staticClass:"d-none",attrs:{"type":"text"}}),_vm._v(" "),_c('div',{on:{"click":_vm.copyLink}},[_vm._v("\n                  "+_vm._s(_vm.$t('copy_link'))+"\n                ")])],1)])]],2):_vm._e()])],1)],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 col-sm-6 mb-2 mb-md-4"},[_c('text-input',{attrs:{"value":_vm.basicInfoItem.firstName,"type-class":"border-gradient","height":"44","hide-details":"","rules":[_vm.rules.required],"label":_vm.$t('first_name'),"autocomplete":"given-name"},on:{"input":function($event){return _vm.updateValue($event, 'firstName')}}})],1),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 mb-2 mb-md-4"},[_c('text-input',{attrs:{"value":_vm.basicInfoItem.lastName,"type-class":"border-gradient","height":"44","hide-details":"","rules":[_vm.rules.required],"label":_vm.$t('last_name'),"autocomplete":"family-name"},on:{"input":function($event){return _vm.updateValue($event, 'lastName')}}})],1)],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 col-sm-6 d-flex align-end mb-2 mb-md-4"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n          "+_vm._s(_vm.$t('please_choose_your_website_language'))+"\n        ")]),_vm._v(" "),_c('user-setting-select',{attrs:{"value":_vm.selectedUiLocal,"items":_vm.locales,"attach-id":"website-language"},on:{"change":function($event){return _vm.updateValue($event.code, 'uiLanguage')}}})],1)])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 col-sm-6 mb-2 mb-sm-0 d-flex align-end"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n          "+_vm._s(_vm.$t('select_desired_local_time_zone'))+"\n        ")]),_vm._v(" "),_c('user-setting-autocomplete',{attrs:{"value":_vm.selectedTimeZone,"items":_vm.timeZones,"attach-id":"time-zone","item-text":"gmt","placeholder":false},on:{"change":_vm.updateTimezone}})],1)]),_vm._v(" "),(_vm.isTeacher)?_c('v-col',{staticClass:"col-12 col-sm-6 d-flex align-end"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n          "+_vm._s(_vm.$t('in_which_country_do_you_normally_pay_taxes'))+"\n        ")]),_vm._v(" "),_c('user-setting-autocomplete',{attrs:{"value":_vm.basicInfoItem.taxCountry,"items":_vm.taxCountries,"attach-id":"country-pay-taxes"},on:{"change":function($event){return _vm.updateValue($event, 'taxCountry')}}})],1)]):_vm._e()],1)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/BasicInfo.vue?vue&type=template&id=40f9f4e9&

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// EXTERNAL MODULE: ./components/user-settings/UserSettingSelect.vue + 4 modules
var UserSettingSelect = __webpack_require__(1013);

// EXTERNAL MODULE: ./components/user-settings/UserSettingAutocomplete.vue + 4 modules
var UserSettingAutocomplete = __webpack_require__(1043);

// EXTERNAL MODULE: ./components/form/TextInput.vue + 4 modules
var TextInput = __webpack_require__(102);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/BasicInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var BasicInfovue_type_script_lang_js_ = ({
  name: 'UserSettingBasic',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"],
    UserSettingSelect: UserSettingSelect["default"],
    UserSettingAutocomplete: UserSettingAutocomplete["default"],
    TextInput: TextInput["default"]
  },
  props: {
    basicInfoItem: {
      type: Object,
      required: true
    }
  },
  data: () => ({
    domain: "'http://localhost:3000'",
    file: null,
    fileValid: true,
    fileSizeLimit: 6000000,
    rules: {
      required: v => !!v && v.length > 1
    }
  }),
  computed: {
    taxCountries() {
      return this.basicInfoItem.taxCountries.map(item => item.taxCountry);
    },

    locales() {
      return this.$store.state.locales.map(item => ({ ...item,
        name: this.$t(item.name)
      }));
    },

    timeZones() {
      return this.basicInfoItem.timezones;
    },

    selectedUiLocal() {
      return this.locales.find(item => item.code === this.basicInfoItem.uiLanguage);
    },

    selectedTimeZone() {
      return this.timeZones.find(item => item.name === this.basicInfoItem.timezone);
    },

    isTeacher() {
      return this.$store.getters['user/isTeacher'];
    },

    userLink() {
      return `${this.domain}/teacher/${this.basicInfoItem.username}`;
    }

  },
  watch: {
    $route() {
      this.file = null;
      this.fileValid = true;
    }

  },
  methods: {
    getSrcAvatar(images, property, defaultImage = 'avatar.png') {
      return images !== null && images !== void 0 && images[property] ? images[property] : __webpack_require__(511)(`./${defaultImage}`);
    },

    getSrcSetAvatar(images, property1, property2) {
      return images !== null && images !== void 0 && images[property1] && images !== null && images !== void 0 && images[property2] ? `
            ${images[property1]} 1x,
            ${images[property2]} 2x,
          ` : '';
    },

    updateTimezone(value) {
      if (value !== null && value !== void 0 && value.name) {
        this.updateValue(value.name, 'timezone');
      }
    },

    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_BASIC_INFO_ITEM', {
        [property]: value
      });
    },

    updateAvatar(file) {
      this.fileValid = true;

      if (file.size > this.fileSizeLimit) {
        this.fileValid = false;
        return;
      }

      this.$store.dispatch('settings/updateAvatar', file);
    },

    copyLink() {
      try {
        const el = this.$refs.profileLink;
        el.setAttribute('value', this.userLink);
        el.select();
        el.setSelectionRange(0, 99999);
        navigator.clipboard.writeText(el.value);
        this.$store.dispatch('snackbar/success', {
          successMessage: 'link_copied',
          timeout: 1500
        });
      } catch (e) {
        console.log(e);
      }
    },

    submitData() {
      this.$store.dispatch('settings/updateBasicInfo').then(() => {
        this.$store.dispatch('loadingStart');
        this.$emit('reload-page');
      });
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/BasicInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_BasicInfovue_type_script_lang_js_ = (BasicInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/VAvatar.js
var VAvatar = __webpack_require__(830);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VFileInput/VFileInput.js
var VFileInput = __webpack_require__(1163);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/user-settings/BasicInfo.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1290)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_BasicInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "a8bd9f46"
  
)

/* harmony default export */ var BasicInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingSelect: __webpack_require__(1013).default,UserSettingAutocomplete: __webpack_require__(1043).default,UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */






installComponents_default()(component, {VAvatar: VAvatar["a" /* default */],VCol: VCol["a" /* default */],VFileInput: VFileInput["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1387:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/SummaryInfo.vue?vue&type=template&id=0a38b2cd&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.item)?_c('user-setting-template',{attrs:{"title":_vm.$t('summary'),"submit-func":_vm.submitData}},[_c('div',{staticClass:"mb-md-2"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('short_summary'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n            "+_vm._s(_vm.$t(
                'provide_short_and_snappy_description_of_what_you_specialise_in_teaching'
              ))+"\n          ")]),_vm._v(" "),_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.item.shortSummary,"no-resize":"","height":"78","counter":"120","solo":"","dense":"","rules":_vm.shortSummaryRules},on:{"input":function($event){return _vm.updateValue($event, 'shortSummary')}}})],1)])],1)],1),_vm._v(" "),_c('div',{staticClass:"mb-md-2"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('longer_summary'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n            "+_vm._s(_vm.$t('provide_longer_introduction_of_yourself_and_your_teaching'))+"\n          ")]),_vm._v(" "),_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.item.longSummary,"no-resize":"","height":"140","solo":"","dense":"","counter":"500","rules":_vm.longSummaryRules},on:{"input":function($event){return _vm.updateValue($event, 'longSummary')}}})],1)])],1)],1),_vm._v(" "),_c('div',[_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n          "+_vm._s(_vm.$t('interesting_facts'))+"\n        ")]),_vm._v(" "),_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n          "+_vm._s(_vm.$t(
              'what_are_unique_facts_about_you_that_students_might_find_interesting'
            ))+"\n        ")])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 mb-2"},[_c('div',{staticClass:"input-wrap"},[_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.factsAbout[0],"no-resize":"","height":"80","solo":"","dense":"","hide-details":"","placeholder":_vm.$t('Interesting fact number 1')},on:{"input":function($event){return _vm.updateFact($event, 0)}}})],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6 mb-2"},[_c('div',{staticClass:"input-wrap"},[_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.factsAbout[1],"no-resize":"","height":"80","solo":"","dense":"","hide-details":"","placeholder":_vm.$t('Interesting fact number 2')},on:{"input":function($event){return _vm.updateFact($event, 1)}}})],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-6"},[_c('div',{staticClass:"input-wrap"},[_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.factsAbout[2],"no-resize":"","height":"80","solo":"","dense":"","hide-details":"","placeholder":_vm.$t('Interesting fact number 3')},on:{"input":function($event){return _vm.updateFact($event, 2)}}})],1)])],1)],1)]):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/SummaryInfo.vue?vue&type=template&id=0a38b2cd&

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/SummaryInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var SummaryInfovue_type_script_lang_js_ = ({
  name: 'SummaryInfo',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"]
  },

  data() {
    return {
      shortSummaryRules: [v => v === null || (v === null || v === void 0 ? void 0 : v.length) <= 120],
      longSummaryRules: [v => v === null || (v === null || v === void 0 ? void 0 : v.length) <= 500]
    };
  },

  computed: {
    item() {
      return this.$store.state.settings.summaryItem;
    },

    factsAbout() {
      return this.item.factsAbout || [];
    }

  },

  beforeCreate() {
    this.$store.dispatch('settings/getSummary');
  },

  methods: {
    updateFact(value, index) {
      const facts = [...this.factsAbout];
      facts[index] = value;
      this.updateValue(facts, 'factsAbout');
    },

    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_SUMMARY_ITEM', {
        [property]: value
      });
    },

    submitData() {
      this.$store.dispatch('settings/updateSummary');
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/SummaryInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_SummaryInfovue_type_script_lang_js_ = (SummaryInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(897);

// CONCATENATED MODULE: ./components/user-settings/SummaryInfo.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_SummaryInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "273fbd95"
  
)

/* harmony default export */ var SummaryInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */




installComponents_default()(component, {VCol: VCol["a" /* default */],VRow: VRow["a" /* default */],VTextarea: VTextarea["a" /* default */]})


/***/ }),

/***/ 1388:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/LanguagesInfo.vue?vue&type=template&id=2defad89&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.item)?_c('user-setting-template',{attrs:{"title":_vm.$t('languages'),"custom-valid":_vm.valid,"submit-func":_vm.submitData}},[_c('div',{staticClass:"mb-4 mb-md-7"},[_c('v-row',[(_vm.isTeacher)?[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-2"},[_vm._v("\n            "+_vm._s(_vm.$t('what_is_your_native_language'))+"\n          ")])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-10 col-md-6"},[_c('div',{staticClass:"input-wrap"},[_c('user-setting-autocomplete',{attrs:{"items":_vm.item.languages,"selected-items":_vm.item.nativeLanguages,"attach-id":"native-language","placeholder":_vm.$t('choose_language')},on:{"change":function($event){return _vm.addLanguage($event, 'nativeLanguages')}}})],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-6"},[(_vm.item.nativeLanguages && _vm.item.nativeLanguages.length)?_c('div',{staticClass:"chips"},_vm._l((_vm.item.nativeLanguages),function(nativeLanguage){return _c('l-chip',{key:nativeLanguage.id,staticClass:"mr-1",attrs:{"label":nativeLanguage.name,"light":""},on:{"click:close":function($event){return _vm.resetLanguage(nativeLanguage, 'nativeLanguages')}}})}),1):_vm._e()])]:_vm._e(),_vm._v(" "),(_vm.isStudent)?[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-2"},[_vm._v("\n            "+_vm._s(_vm.$t('what_language_would_you_like_to_learn_on_langu'))+"\n          ")])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-10 col-md-6"},[_c('div',{staticClass:"input-wrap"},[_c('user-setting-select',{attrs:{"value":_vm.selectedLanguageToLearn,"items":_vm.languagesToLearn,"attach-id":"learn-language","hide-details":false,"placeholder":_vm.$t('choose_language')},on:{"change":function($event){return _vm.updateValue($event.id, 'languageToLearn')}}})],1)])]:_vm._e()],2)],1),_vm._v(" "),(_vm.isTeacher)?_c('div',{staticClass:"mb-4 mb-md-7"},[_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-2"},[_vm._v("\n          "+_vm._s(_vm.$t('what_languages_would_you_like_to_teach_on_langu'))+"\n        ")])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-10 col-md-6"},[_c('div',{staticClass:"input-wrap"},[_c('user-setting-autocomplete',{attrs:{"items":_vm.item.languages,"selected-items":_vm.item.languagesTaught,"attach-id":"teach-language","placeholder":_vm.$t('choose_language')},on:{"change":_vm.addTeachLanguage}})],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-6"},[(_vm.item.languagesTaught && _vm.item.languagesTaught.length)?_c('div',{staticClass:"chips"},_vm._l((_vm.item.languagesTaught),function(taughtLanguage){return _c('l-chip',{key:taughtLanguage.id,staticClass:"mr-1",attrs:{"label":taughtLanguage.name,"light":""},on:{"click:close":function($event){return _vm.resetLanguage(taughtLanguage, 'languagesTaught')}}})}),1):_vm._e()])],1)],1):_vm._e(),_vm._v(" "),_c('div',[_c('v-row',[(_vm.isTeacher)?[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-2"},[_vm._v("\n            "+_vm._s(_vm.$t('what_other_languages_do_you_speak'))+"\n          ")])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-10 col-md-6"},[_c('div',{staticClass:"input-wrap"},[_c('user-setting-autocomplete',{attrs:{"items":_vm.item.languages,"selected-items":_vm.item.languagesSpoken,"attach-id":"other-speak-language","placeholder":_vm.$t('choose_language')},on:{"change":function($event){return _vm.addLanguage($event, 'languagesSpoken')}}})],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-6"},[(_vm.item.languagesSpoken && _vm.item.languagesSpoken.length)?_c('div',{staticClass:"chips"},_vm._l((_vm.item.languagesSpoken),function(spokenLanguage){return _c('l-chip',{key:spokenLanguage.id,staticClass:"mr-1",attrs:{"label":spokenLanguage.name,"light":""},on:{"click:close":function($event){return _vm.resetLanguage(spokenLanguage, 'languagesSpoken')}}})}),1):_vm._e()])]:_vm._e(),_vm._v(" "),(_vm.isStudent)?[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-2"},[_vm._v("\n            "+_vm._s(_vm.$t('what_is_your_native_language'))+"\n          ")])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-sm-10 col-md-6"},[_c('div',{staticClass:"input-wrap"},[_c('user-setting-autocomplete',{attrs:{"items":_vm.item.languages,"selected-items":_vm.item.nativeLanguages,"attach-id":"other-speak-language","placeholder":_vm.$t('choose_language')},on:{"change":function($event){return _vm.addLanguage($event, 'nativeLanguages')}}})],1)]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-6"},[(_vm.item.nativeLanguages && _vm.item.nativeLanguages.length)?_c('div',{staticClass:"chips"},_vm._l((_vm.item.nativeLanguages),function(nativeLanguage){return _c('l-chip',{key:nativeLanguage.id,staticClass:"mr-1",attrs:{"label":nativeLanguage.name,"light":""},on:{"click:close":function($event){return _vm.resetLanguage(nativeLanguage, 'nativeLanguages')}}})}),1):_vm._e()])]:_vm._e()],2)],1)]):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/LanguagesInfo.vue?vue&type=template&id=2defad89&

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// EXTERNAL MODULE: ./components/user-settings/UserSettingSelect.vue + 4 modules
var UserSettingSelect = __webpack_require__(1013);

// EXTERNAL MODULE: ./components/user-settings/UserSettingAutocomplete.vue + 4 modules
var UserSettingAutocomplete = __webpack_require__(1043);

// EXTERNAL MODULE: ./components/LChip.vue + 4 modules
var LChip = __webpack_require__(70);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/LanguagesInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var LanguagesInfovue_type_script_lang_js_ = ({
  name: 'LanguagesInfo',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"],
    UserSettingSelect: UserSettingSelect["default"],
    UserSettingAutocomplete: UserSettingAutocomplete["default"],
    LChip: LChip["default"]
  },

  data() {
    return {
      key: 1,
      languagesToLearn: [{
        id: null,
        name: this.$t('all_languages')
      }]
    };
  },

  computed: {
    item() {
      return this.$store.state.settings.languagesItem;
    },

    selectedLanguageToLearn() {
      return this.languagesToLearn.find(item => item.id === this.item.languageToLearn) || {
        id: null,
        name: this.$t('all_languages')
      };
    },

    isTeacher() {
      return this.$store.getters['user/isTeacher'];
    },

    isStudent() {
      return this.$store.getters['user/isStudent'];
    },

    valid() {
      return !!(this.isStudent || this.item.nativeLanguages.length && this.item.languagesTaught.length);
    }

  },

  async mounted() {
    var _this$item;

    await this.$store.dispatch('settings/getLanguages');
    this.languagesToLearn = [...this.languagesToLearn, ...((_this$item = this.item) === null || _this$item === void 0 ? void 0 : _this$item.languages)];
  },

  methods: {
    addTeachLanguage(item) {
      const currentItems = [...this.item.languagesTaught];

      if (currentItems.length < 2) {
        currentItems.push(item);
      } else {
        currentItems.splice(1, 1, item);
      }

      this.$store.commit('settings/SET_LANGUAGES_ITEM', { ...this.item,
        languagesTaught: currentItems
      });
    },

    addLanguage(item, property) {
      const currentItems = [...this.item[property]];
      currentItems.push(item);
      this.$store.commit('settings/SET_LANGUAGES_ITEM', { ...this.item,
        [property]: currentItems
      });
    },

    resetLanguage(item, property) {
      this.$store.commit('settings/SET_LANGUAGES_ITEM', { ...this.item,
        [property]: this.item[property].filter(el => el.id !== item.id)
      });
    },

    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_LANGUAGES_ITEM', {
        [property]: value
      });
    },

    submitData() {
      this.$store.dispatch('settings/updateLanguages');
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/LanguagesInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_LanguagesInfovue_type_script_lang_js_ = (LanguagesInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/user-settings/LanguagesInfo.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_LanguagesInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "3b43eaca"
  
)

/* harmony default export */ var LanguagesInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingAutocomplete: __webpack_require__(1043).default,LChip: __webpack_require__(70).default,UserSettingSelect: __webpack_require__(1013).default,UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */



installComponents_default()(component, {VCol: VCol["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1389:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/BackgroundInfo.vue?vue&type=template&id=db72c68c&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.item)?_c('user-setting-template',{attrs:{"title":_vm.$t('background'),"submit-func":_vm.submitData}},[_c('div',{staticClass:"mb-md-2"},[_c('v-row',{staticClass:"mb-2"},[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('your_teaching_background'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n            "+_vm._s(_vm.$t('describe_your_language_teaching_background'))+"\n          ")]),_vm._v(" "),_c('div',[_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.item.teachingBackground,"no-resize":"","height":"120","solo":"","dense":"","counter":_vm.lengthLimit,"rules":_vm.rules.maxLength},on:{"input":function($event){return _vm.updateValue($event, 'teachingBackground')}}})],1)])])],1)],1),_vm._v(" "),_c('div',[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('your_general_background'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n            "+_vm._s(_vm.$t('describe_any_aspects_of_your_background'))+"\n          ")]),_vm._v(" "),_c('div',[_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.item.generalBackground,"no-resize":"","height":"120","solo":"","dense":"","counter":_vm.lengthLimit,"rules":_vm.rules.maxLength},on:{"input":function($event){return _vm.updateValue($event, 'generalBackground')}}})],1)])])],1)],1)]):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/BackgroundInfo.vue?vue&type=template&id=db72c68c&

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/BackgroundInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

const lengthLimit = 1000;
/* harmony default export */ var BackgroundInfovue_type_script_lang_js_ = ({
  name: 'BackgroundInfo',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"]
  },

  data() {
    return {
      lengthLimit,
      rules: {
        maxLength: [v => v === null || (v === null || v === void 0 ? void 0 : v.length) <= lengthLimit]
      }
    };
  },

  computed: {
    item() {
      return this.$store.state.settings.backgroundItem;
    }

  },

  beforeCreate() {
    this.$store.dispatch('settings/getBackground');
  },

  methods: {
    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_BACKGROUND_ITEM', {
        [property]: value
      });
    },

    submitData() {
      this.$store.dispatch('settings/updateBackground');
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/BackgroundInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_BackgroundInfovue_type_script_lang_js_ = (BackgroundInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(897);

// CONCATENATED MODULE: ./components/user-settings/BackgroundInfo.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_BackgroundInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "98be092a"
  
)

/* harmony default export */ var BackgroundInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */




installComponents_default()(component, {VCol: VCol["a" /* default */],VRow: VRow["a" /* default */],VTextarea: VTextarea["a" /* default */]})


/***/ }),

/***/ 1390:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/AboutMeInfo.vue?vue&type=template&id=d9792938&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.item)?_c('user-setting-template',{attrs:{"title":_vm.$t('about_me'),"submit-func":_vm.submitData}},[_c('div',{staticClass:"mb-2 mb-md-4"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('what_should_students_expect_from_your_classes'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n            "+_vm._s(_vm.$t(
                'describe_any_materials_evaluations_activities_you_like_to_use'
              ))+"\n          ")]),_vm._v(" "),_c('div',[_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.item.whatToExpect,"no-resize":"","height":"120","solo":"","dense":"","hide-details":""},on:{"input":function($event){return _vm.updateValue($event, 'whatToExpect')}}})],1),_vm._v(" "),_c('div',{staticClass:"input-wrap-notice text--gradient"},[_vm._v("\n            "+_vm._s(_vm.$t(
                'formatting_tip_use_asterisk_at_beginning_of_line_to_add_bullet_point'
              ))+"\n          ")])])])],1)],1),_vm._v(" "),_c('div',{staticClass:"mb-2 mb-md-4"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('your_cv'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n            "+_vm._s(_vm.$t('upload_cv_or_provide_link_to_your_linkedIn_profile'))+"\n          ")]),_vm._v(" "),_c('div',[_c('text-input',{attrs:{"value":_vm.item.linkedinUrl,"type-class":"border-gradient","height":"44","hide-details":"","placeholder":_vm.$t('Linkedin.com')},on:{"input":function($event){return _vm.updateValue($event, 'linkedinUrl')}}})],1)])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-10 mt-3"},[_c('div',{staticClass:"d-flex"},[_c('div',{staticClass:"upload-cv"},[_c('v-btn',{staticClass:"gradient font-weight-medium mt-1",on:{"click":function($event){_vm.$refs.fileCV.$el.querySelector('input').click()}}},[_c('div',[_c('v-img',{staticClass:"mr-1",attrs:{"src":__webpack_require__(574),"width":"20","height":"20"}})],1),_vm._v(" "),_c('div',{staticClass:"text--gradient"},[_vm._v("\n                "+_vm._s(_vm.$t('upload_cv'))+"\n              ")])]),_vm._v(" "),_c('div',{staticClass:"input-wrap"},[_c('v-file-input',{ref:"fileCV",staticClass:"l-file-input pt-0",attrs:{"prepend-icon":"","hide-input":"","accept":"image/png, image/jpeg, image/bmp, application/pdf"},on:{"change":_vm.uploadCV},model:{value:(_vm.file),callback:function ($$v) {_vm.file=$$v},expression:"file"}}),_vm._v(" "),(!_vm.fileValid)?_c('div',{staticClass:"v-text-field__details"},[_c('div',{staticClass:"input-wrap-error"},[_c('div',{staticClass:"v-messages theme--light error--text",attrs:{"role":"alert"}},[_c('div',{staticClass:"v-messages__wrapper"},[_c('div',{staticClass:"v-messages__message"},[_vm._v("\n                        "+_vm._s(_vm.$t('file_size_should_be_less_than', {
                            value: '6 MB',
                          }))+"\n                      ")])])])])]):_vm._e()],1)],1),_vm._v(" "),(_vm.item.cvUrl)?_c('div',{staticClass:"download-cv"},[_c('v-btn',{staticClass:"font-weight-medium mt-1",attrs:{"text":"","href":_vm.cvUrl,"target":"_blank","download":""},on:{"click":function($event){$event.preventDefault();return _vm.downloadClickHandler.apply(null, arguments)}}},[_c('div',[_c('v-img',{staticClass:"mr-1",attrs:{"src":__webpack_require__(508),"width":"24","height":"24"}})],1),_vm._v(" "),_c('span',{staticClass:"text--gradient"},[_vm._v("\n                "+_vm._s(_vm.$t('download_cv'))+"\n              ")])])],1):_vm._e()])])],1)],1),_vm._v(" "),_c('div',[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('provide_link_to_your_introduction_video_on_youtube'))+"\n          ")]),_vm._v(" "),_c('div',[_c('text-input',{attrs:{"value":_vm.item.youtubeUrl,"type-class":"border-gradient","height":"44","hide-details":"","placeholder":_vm.$t('youtube_link')},on:{"input":function($event){return _vm.updateValue($event, 'youtubeUrl')}}})],1)])])],1)],1)]):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/AboutMeInfo.vue?vue&type=template&id=d9792938&scoped=true&

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// EXTERNAL MODULE: ./components/form/TextInput.vue + 4 modules
var TextInput = __webpack_require__(102);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/AboutMeInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var AboutMeInfovue_type_script_lang_js_ = ({
  name: 'AboutMeInfo',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"],
    TextInput: TextInput["default"]
  },

  data() {
    return {
      file: null,
      fileValid: true,
      fileSizeLimit: 6000000
    };
  },

  computed: {
    item() {
      return this.$store.state.settings.aboutMeItem;
    },

    cvUrl() {
      return this.item.cvUrl;
    },

    fileName() {
      const arrPath = this.cvUrl.split('/');
      return arrPath[arrPath.length - 1];
    }

  },
  watch: {
    $route() {
      this.resetFile();
    }

  },

  beforeCreate() {
    this.$store.dispatch('settings/getAboutMe');
  },

  methods: {
    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_ABOUT_ME_ITEM', {
        [property]: value
      });
    },

    async downloadClickHandler() {
      await this.$axios({
        url: this.cvUrl,
        method: 'GET',
        responseType: 'blob'
      }).then(response => {
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', this.fileName);
        document.body.appendChild(link);
        link.click();
      }).catch(() => console.info('Download error'));
    },

    resetFile() {
      this.file = null;
      this.fileValid = true;
    },

    uploadCV(file) {
      this.fileValid = true;

      if (file.size > this.fileSizeLimit) {
        this.fileValid = false;
        return;
      }

      this.$store.dispatch('settings/uploadCV', file);
    },

    submitData() {
      this.$store.dispatch('settings/updateAboutMe').then(() => {
        this.resetFile();
      });
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/AboutMeInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_AboutMeInfovue_type_script_lang_js_ = (AboutMeInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VFileInput/VFileInput.js
var VFileInput = __webpack_require__(1163);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(897);

// CONCATENATED MODULE: ./components/user-settings/AboutMeInfo.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1292)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_AboutMeInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "d9792938",
  "55f2c4f4"
  
)

/* harmony default export */ var AboutMeInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */







installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VFileInput: VFileInput["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */],VTextarea: VTextarea["a" /* default */]})


/***/ }),

/***/ 1391:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/PricingTableInfo.vue?vue&type=template&id=b41b2052&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.item)?_c('user-setting-template',{attrs:{"title":_vm.$t('pricing_table'),"submit-func":_vm.submitData}},[_c('div',{staticClass:"mb-3 mb-md-4"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('currency'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"input-wrap-label"},[_vm._v("\n            "+_vm._s(_vm.$t(
                'once_saved_you_cannot_change_your_currency_if_you_need_to_change_your_payment_currency'
              ))+"\n            "),_c('a',{staticClass:"text--gradient",attrs:{"href":"mailto:<EMAIL>"}},[_vm._v("<EMAIL>")]),_vm._v(".\n          ")]),_vm._v(" "),_c('div',{staticClass:"current-currency d-flex align-center"},[_c('svg',{staticClass:"mr-1",attrs:{"width":"20","height":"20","viewBox":"0 0 20 20"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#dollar-coin")}})]),_vm._v("\n            "+_vm._s(_vm.currentCurrency.isoCode)+"\n          ")])])])],1)],1),_vm._v(" "),_c('div',{staticClass:"mb-3 mb-md-4"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('trial_lesson_pricing'))+"\n          ")]),_vm._v(" "),_c('div',[_c('v-radio-group',{staticClass:"mt-0 pt-0",attrs:{"value":_vm.selectedTrialLessonPricing,"hide-details":""},on:{"change":_vm.updateTrial}},_vm._l((_vm.trialLessonPricingItems),function(tl,idx){return _c('div',{key:tl.id,class:{ 'mb-2': idx < _vm.trialLessonPricingItems.length - 1 }},[_c('div',{staticClass:"radiobutton d-flex align-center"},[_c('v-radio',{staticClass:"d-flex align-center l-radio-button l-radio-button--type-2 mb-0",attrs:{"color":"success","ripple":false,"value":tl.id},scopedSlots:_vm._u([{key:"label",fn:function(){return [_vm._v("\n                      "+_vm._s(_vm.$t(tl.name))+"\n                    ")]},proxy:true}],null,true)}),_vm._v(" "),(tl.id === 2)?[_c('div',{staticClass:"currency-input price-input ml-2"},[_c('lesson-price',{attrs:{"value":_vm.item.priceTrialLesson,"rules":_vm.selectedTrialLessonPricing === 2
                            ? _vm.trialPriceRules
                            : [],"length":30,"free-trial":true},on:{"input":function($event){return _vm.updateValue($event, 'priceTrialLesson')}}})],1)]:_vm._e()],2)])}),0)],1)])])],1)],1),_vm._v(" "),_c('div',{staticClass:"mb-3 mb-md-4"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('new_students'))+"\n          ")]),_vm._v(" "),_c('div',[_c('v-radio-group',{staticClass:"mt-0 pt-0",attrs:{"value":_vm.item.acceptNewStudents,"hide-details":""},on:{"change":function($event){return _vm.updateValue($event, 'acceptNewStudents')}}},_vm._l((_vm.newStudentsItems),function(ns,idx){return _c('div',{key:ns.id,class:{ 'mb-2': idx < _vm.newStudentsItems.length - 1 }},[_c('div',{staticClass:"radiobutton"},[_c('v-radio',{staticClass:"d-flex align-center l-radio-button l-radio-button--type-2",attrs:{"color":"success","ripple":false,"value":ns.value},scopedSlots:_vm._u([{key:"label",fn:function(){return [_vm._v("\n                      "+_vm._s(_vm.$t(ns.name))+"\n                    ")]},proxy:true}],null,true)})],1)])}),0)],1)])])],1)],1),_vm._v(" "),_c('div',[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('lesson_pricing'))+"\n            "),_c('span',[_vm._v("("+_vm._s(_vm.$t('enter_prices_per_lesson'))+")")])]),_vm._v(" "),_c('div',{staticClass:"input-wrap-label mb-3"},[_vm._v("\n            "+_vm._s(_vm.$t('all_60_pricing_options_are_required'))+"\n          ")]),_vm._v(" "),_c('div',{staticClass:"lesson-pricing"},[_c('div',{staticClass:"lesson-pricing-row"},[_c('div',{staticClass:"d-flex align-center justify-center item mr-md-4"},[_c('svg',{attrs:{"width":"20","height":"20","viewBox":"0 0 15 15"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#clock")}})])]),_vm._v(" "),_c('div',{staticClass:"item mr-1 mr-lg-2"},[_vm._v("\n                "+_vm._s(_vm.$tc('lessons_count', 1))+"\n              ")]),_vm._v(" "),_c('div',{staticClass:"item mr-1 mr-lg-2"},[_vm._v("\n                "+_vm._s(_vm.$tc('lessons_count', 5))+"\n              ")]),_vm._v(" "),_c('div',{staticClass:"item mr-1 mr-lg-2"},[_vm._v("\n                "+_vm._s(_vm.$tc('lessons_count', 10))+"\n              ")]),_vm._v(" "),_c('div',{staticClass:"item"},[_vm._v(_vm._s(_vm.$tc('lessons_count', 20)))])]),_vm._v(" "),_c('div',{staticClass:"lesson-pricing-row"},[_c('div',{staticClass:"item mr-md-4"},[_vm._v("30’")]),_vm._v(" "),_c('div',{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":30,"lessons":1}})],1),_vm._v(" "),_c('div',{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":30,"lessons":5}})],1),_vm._v(" "),_c('div',{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":30,"lessons":10}})],1),_vm._v(" "),_c('div',{staticClass:"item price-input py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":30,"lessons":20}})],1),_vm._v(" "),(_vm.getMinimumTextVisibility)?_c('div',{staticClass:"item_text py-2"},[_c('v-icon',{attrs:{"color":"greyDark"}},[_vm._v(_vm._s(_vm.mdiInformationOutline))]),_vm._v(" "),_c('span',{staticClass:"pl-1"},[_vm._v("\n                  "+_vm._s(_vm.$t('minimum_Text'))+_vm._s(((_vm.pricingMap[_vm.currentCurrencyIsoCode]['30']) + " " + _vm.currentCurrencyIsoCode))+"\n                ")])],1):_vm._e()]),_vm._v(" "),(!_vm.getMinimumTextVisibility)?_c('div',{staticClass:"item_text"},[_c('v-icon',{attrs:{"color":"greyDark"}},[_vm._v(_vm._s(_vm.mdiInformationOutline))]),_vm._v(" "),_c('span',{staticClass:"pl-1"},[_vm._v("\n                "+_vm._s(_vm.$t('minimum_Text'))+_vm._s(((_vm.pricingMap[_vm.currentCurrencyIsoCode]['30']) + " " + _vm.currentCurrencyIsoCode))+"\n              ")])],1):_vm._e(),_vm._v(" "),_c('div',{staticClass:"lesson-pricing-row"},[_c('div',{staticClass:"item mr-md-4"},[_vm._v("60’")]),_vm._v(" "),_c('div',{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":60,"lessons":1,"rules":_vm.priceRules}})],1),_vm._v(" "),_c('div',{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":60,"lessons":5,"rules":_vm.priceRules}})],1),_vm._v(" "),_c('div',{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":60,"lessons":10,"rules":_vm.priceRules}})],1),_vm._v(" "),_c('div',{staticClass:"item price-input py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":60,"lessons":20,"rules":_vm.priceRules}})],1),_vm._v(" "),(_vm.getMinimumTextVisibility)?_c('div',{staticClass:"item_text py-2"},[_c('v-icon',{attrs:{"color":"greyDark"}},[_vm._v(_vm._s(_vm.mdiInformationOutline))]),_vm._v(" "),_c('span',{staticClass:"pl-1"},[_vm._v("\n                  "+_vm._s(_vm.$t('minimum_Text'))+_vm._s(((_vm.pricingMap[_vm.currentCurrencyIsoCode]['60']) + " " + _vm.currentCurrencyIsoCode))+"\n                ")])],1):_vm._e()]),_vm._v(" "),(!_vm.getMinimumTextVisibility)?_c('div',{staticClass:"item_text"},[_c('v-icon',{attrs:{"color":"greyDark"}},[_vm._v(_vm._s(_vm.mdiInformationOutline))]),_vm._v(" "),_c('span',{staticClass:"pl-1"},[_vm._v("\n                "+_vm._s(_vm.$t('minimum_Text'))+_vm._s(((_vm.pricingMap[_vm.currentCurrencyIsoCode]['60']) + " " + _vm.currentCurrencyIsoCode))+"\n              ")])],1):_vm._e(),_vm._v(" "),_c('div',{staticClass:"lesson-pricing-row"},[_c('div',{staticClass:"item mr-md-4"},[_vm._v("90’")]),_vm._v(" "),_c('div',{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":90,"lessons":1}})],1),_vm._v(" "),_c('div',{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":90,"lessons":5}})],1),_vm._v(" "),_c('div',{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":90,"lessons":10}})],1),_vm._v(" "),_c('div',{staticClass:"item price-input py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":90,"lessons":20}})],1),_vm._v(" "),(_vm.getMinimumTextVisibility)?_c('div',{staticClass:"item_text py-2"},[_c('v-icon',{attrs:{"color":"greyDark"}},[_vm._v(_vm._s(_vm.mdiInformationOutline))]),_vm._v(" "),_c('span',{staticClass:"pl-1"},[_vm._v("\n                  "+_vm._s(_vm.$t('minimum_Text'))+_vm._s(((_vm.pricingMap[_vm.currentCurrencyIsoCode]['90']) + " " + _vm.currentCurrencyIsoCode))+"\n                ")])],1):_vm._e()]),_vm._v(" "),(!_vm.getMinimumTextVisibility)?_c('div',{staticClass:"item_text"},[_c('v-icon',{attrs:{"color":"greyDark"}},[_vm._v(_vm._s(_vm.mdiInformationOutline))]),_vm._v(" "),_c('span',{staticClass:"pl-1"},[_vm._v("\n                "+_vm._s(_vm.$t('minimum_Text'))+_vm._s(((_vm.pricingMap[_vm.currentCurrencyIsoCode]['90']) + " " + _vm.currentCurrencyIsoCode))+"\n              ")])],1):_vm._e(),_vm._v(" "),_c('div',{staticClass:"lesson-pricing-row"},[_c('div',{staticClass:"item mr-md-4"},[_vm._v("120’")]),_vm._v(" "),_c('div',{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":120,"lessons":1}})],1),_vm._v(" "),_c('div',{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":120,"lessons":5}})],1),_vm._v(" "),_c('div',{staticClass:"item price-input mr-1 mr-lg-2 py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":120,"lessons":10}})],1),_vm._v(" "),_c('div',{staticClass:"item price-input py-2"},[_c('per-lesson-price',{attrs:{"items":_vm.lessonPrices,"length":120,"lessons":20}})],1),_vm._v(" "),(_vm.getMinimumTextVisibility)?_c('div',{staticClass:"item_text py-2"},[_c('v-icon',{attrs:{"color":"greyDark"}},[_vm._v(_vm._s(_vm.mdiInformationOutline))]),_vm._v(" "),_c('span',{staticClass:"pl-1"},[_vm._v("\n                  "+_vm._s(_vm.$t('minimum_Text'))+_vm._s(((_vm.pricingMap[_vm.currentCurrencyIsoCode]['120']) + " " + _vm.currentCurrencyIsoCode))+"\n                ")])],1):_vm._e()]),_vm._v(" "),(!_vm.getMinimumTextVisibility)?_c('div',{staticClass:"item_text"},[_c('v-icon',{attrs:{"color":"greyDark"}},[_vm._v(_vm._s(_vm.mdiInformationOutline))]),_vm._v(" "),_c('span',{staticClass:"pl-1"},[_vm._v("\n                "+_vm._s(_vm.$t('minimum_Text'))+_vm._s(((_vm.pricingMap[_vm.currentCurrencyIsoCode]['120']) + " " + _vm.currentCurrencyIsoCode))+"\n              ")])],1):_vm._e()])])])],1)],1)]):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/PricingTableInfo.vue?vue&type=template&id=b41b2052&scoped=true&

// EXTERNAL MODULE: external "@mdi/js"
var js_ = __webpack_require__(48);

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// EXTERNAL MODULE: ./components/user-settings/LessonPrice.vue + 4 modules
var LessonPrice = __webpack_require__(1114);

// EXTERNAL MODULE: ./components/user-settings/PerLessonPrice.vue + 4 modules
var PerLessonPrice = __webpack_require__(1271);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/PricingTableInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var PricingTableInfovue_type_script_lang_js_ = ({
  name: 'PricingTableInfo',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"],
    LessonPrice: LessonPrice["default"],
    PerLessonPrice: PerLessonPrice["default"]
  },

  data() {
    return {
      mdiInformationOutline: js_["mdiInformationOutline"],
      trialPriceRules: [v => !!v],
      priceRules: [v => !!v],
      trialLessonPricingItems: [{
        id: 1,
        name: 'i_offer_free_30_minute_trial_lesson_recommended',
        property: 'freeTrialLesson'
      }, {
        id: 2,
        name: 'i_offer_30_minute_trial_lesson_for',
        property: 'trialLesson'
      } // {
      //   id: 3,
      //   name: 'i_do_not_offer_trial_option',
      //   property: 'trialNotProvided',
      // },
      ],
      newStudentsItems: [{
        id: 1,
        name: 'i_am_currently_accepting_new_students',
        value: true
      }, {
        id: 2,
        name: 'i_am_only_accepting_bookings_from_past_current_students',
        value: false
      }],
      pricingMap: {
        EUR: {
          30: 7,
          60: 11,
          90: 16,
          120: 21
        },
        GBP: {
          30: 6,
          60: 10,
          90: 15,
          120: 20
        },
        PLN: {
          30: 30,
          60: 50,
          90: 70,
          120: 85
        },
        USD: {
          30: 8,
          60: 12,
          90: 17,
          120: 22
        },
        AUD: {
          30: 12,
          60: 20,
          90: 28,
          120: 36
        },
        CAD: {
          30: 11,
          60: 18,
          90: 25,
          120: 32
        }
      }
    };
  },

  computed: {
    item() {
      return this.$store.state.settings.pricingTableItem;
    },

    lessonPrices() {
      return this.$store.state.settings.lessonPrices;
    },

    selectedTrialLessonPricing() {
      var _this$item, _this$item2;

      let value = 3;

      if ((_this$item = this.item) !== null && _this$item !== void 0 && _this$item.freeTrialLesson) {
        value = 1;
      } else if ((_this$item2 = this.item) !== null && _this$item2 !== void 0 && _this$item2.trialLesson) {
        value = 2;
      }

      return value;
    },

    currentCurrency() {
      return this.$store.state.currency.item;
    },

    currentCurrencyIsoCode() {
      var _this$$store$state$cu, _this$$store$state$cu2;

      return (_this$$store$state$cu = this.$store.state.currency.item) !== null && _this$$store$state$cu !== void 0 && _this$$store$state$cu.isoCode ? (_this$$store$state$cu2 = this.$store.state.currency.item) === null || _this$$store$state$cu2 === void 0 ? void 0 : _this$$store$state$cu2.isoCode.toUpperCase() : 'USD';
    },

    getMinimumTextVisibility() {
      return this.$vuetify.breakpoint.mdAndUp;
    }

  },
  watch: {
    selectedTrialLessonPricing() {
      this.$emit('validate');
    }

  },

  beforeCreate() {
    this.$store.dispatch('settings/getPricingTable');
  },

  methods: {
    updateTrial(id) {
      const [trialPackage] = this.trialLessonPricingItems.filter(item => item.id === id);

      if (trialPackage.property === 'freeTrialLesson') {
        this.updateValue(true, 'freeTrialLesson');
        this.updateValue(false, 'trialLesson');
      }

      if (trialPackage.property === 'trialLesson') {
        this.updateValue(false, 'freeTrialLesson');
        this.updateValue(true, 'trialLesson');
      } // if (trialPackage.property === 'trialNotProvided') {
      //   this.updateValue(false, 'freeTrialLesson')
      //   this.updateValue(false, 'trialLesson')
      // }

    },

    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_PRICING_TABLE_ITEM', {
        [property]: value
      });
    },

    submitData() {
      this.$store.dispatch('settings/updatePricingTable');
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/PricingTableInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_PricingTableInfovue_type_script_lang_js_ = (PricingTableInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(87);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VRadioGroup/VRadio.js
var VRadio = __webpack_require__(1093);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VRadioGroup/VRadioGroup.js
var VRadioGroup = __webpack_require__(1094);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/user-settings/PricingTableInfo.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1294)
if (style0.__inject__) style0.__inject__(context)
var style1 = __webpack_require__(1296)
if (style1.__inject__) style1.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_PricingTableInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "b41b2052",
  "4df28a25"
  
)

/* harmony default export */ var PricingTableInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */






installComponents_default()(component, {VCol: VCol["a" /* default */],VIcon: VIcon["a" /* default */],VRadio: VRadio["a" /* default */],VRadioGroup: VRadioGroup["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1392:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/CoursesInfo.vue?vue&type=template&id=e39f7fc0&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('user-setting-template',{attrs:{"title":_vm.$t('courses'),"hide-footer":""}},[(_vm.items.length)?_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('v-expansion-panels',{attrs:{"accordion":""},model:{value:(_vm.panel),callback:function ($$v) {_vm.panel=$$v},expression:"panel"}},_vm._l((_vm.items),function(item,idx){return _c('course-item',{key:idx,attrs:{"id":item.slug,"item":item,"index":idx,"is-active":idx === _vm.panel,"languages":_vm.languagesTaught},on:{"scroll-to-top":_vm.scrollToTop}})}),1)],1)],1):_vm._e(),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{class:{ 'border-top': _vm.items.length }},[_c('v-btn',{staticClass:"gradient font-weight-medium mt-4",on:{"click":_vm.addCourse}},[_c('div',{staticClass:"mr-1"},[_c('v-img',{attrs:{"src":__webpack_require__(515),"width":"20","height":"20"}})],1),_vm._v(" "),_c('div',{staticClass:"text--gradient"},[_vm._v("\n            "+_vm._s(_vm.$t('add_course'))+"\n          ")])])],1)])],1)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/CoursesInfo.vue?vue&type=template&id=e39f7fc0&scoped=true&

// EXTERNAL MODULE: external "uniqid"
var external_uniqid_ = __webpack_require__(452);
var external_uniqid_default = /*#__PURE__*/__webpack_require__.n(external_uniqid_);

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// EXTERNAL MODULE: ./components/user-settings/CourseItem.vue + 4 modules
var CourseItem = __webpack_require__(1272);

// EXTERNAL MODULE: ./helpers/index.js
var helpers = __webpack_require__(501);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/CoursesInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var CoursesInfovue_type_script_lang_js_ = ({
  name: 'CoursesInfo',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"],
    CourseItem: CourseItem["default"]
  },

  data() {
    return {
      getSlugByString: helpers["getSlugByString"],
      panel: undefined
    };
  },

  computed: {
    items() {
      return this.$store.state.settings.courseItems.map(item => ({ ...item,
        slug: item.name ? this.getSlugByString(item.name) : item.uid
      }));
    },

    languagesTaught() {
      var _this$$store$state$se, _this$$store$state$se2;

      return (_this$$store$state$se = (_this$$store$state$se2 = this.$store.state.settings.languagesItem) === null || _this$$store$state$se2 === void 0 ? void 0 : _this$$store$state$se2.languagesTaught) !== null && _this$$store$state$se !== void 0 ? _this$$store$state$se : [];
    }

  },
  watch: {
    panel() {
      var _this$items$this$pane;

      const el = document.getElementById((_this$items$this$pane = this.items[this.panel]) === null || _this$items$this$pane === void 0 ? void 0 : _this$items$this$pane.slug);

      if (el) {
        window.setTimeout(() => {
          this.goTo(el);
        }, 300);
      }
    }

  },

  beforeCreate() {
    this.$store.dispatch('settings/getCourses');
    this.$store.dispatch('settings/getLanguages');
  },

  methods: {
    addCourse() {
      const uid = external_uniqid_default()();
      this.$store.commit('settings/ADD_COURSE_ITEM', uid);
      this.$nextTick(() => {
        this.panel = this.items.length - 1;
      });
    },

    scrollToTop() {
      this.panel = undefined;
      this.goTo();
    },

    goTo(target = 0) {
      this.$vuetify.goTo(target, {
        duration: 0,
        offset: 0,
        easing: 'linear'
      });
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/CoursesInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_CoursesInfovue_type_script_lang_js_ = (CoursesInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanels.js
var VExpansionPanels = __webpack_require__(1092);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/user-settings/CoursesInfo.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1299)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_CoursesInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "e39f7fc0",
  "6fa607a7"
  
)

/* harmony default export */ var CoursesInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */






installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VExpansionPanels: VExpansionPanels["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1393:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/TeachingPreferencesInfo.vue?vue&type=template&id=22c932db&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.preferences.length)?_c('user-setting-template',{attrs:{"title":_vm.$t('teaching_preferences'),"submit-func":_vm.submitData}},[_c('div',{staticClass:"mb-4 mb-md-7"},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-1"},[_vm._v("\n            "+_vm._s(_vm.$t('specialities'))+"\n          ")]),_vm._v(" "),_c('div',[_c('v-btn',{staticClass:"gradient font-weight-medium mt-2",on:{"click":function($event){_vm.isShownSpecialitiesDialog = true}}},[_c('div',{staticClass:"mr-1"},[_c('v-img',{attrs:{"src":__webpack_require__(625),"width":"24","height":"24"}})],1),_vm._v(" "),_c('div',{staticClass:"text--gradient"},[_vm._v("\n                "+_vm._s(_vm.$t('manage_specialties'))+"\n              ")])])],1)])])],1)],1),_vm._v(" "),_c('div',[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-2"},[_vm._v("\n            "+_vm._s(_vm.$t('which_levels_would_you_like_to_teach'))+"\n          ")]),_vm._v(" "),_c('div',_vm._l((_vm.preferences),function(item,idx){return _c('div',{key:idx,staticClass:"checkbox"},[_c('v-checkbox',{staticClass:"l-checkbox",attrs:{"value":item,"label":item.name,"hide-details":"","ripple":false},model:{value:(_vm.selectedPreferences),callback:function ($$v) {_vm.selectedPreferences=$$v},expression:"selectedPreferences"}})],1)}),0)])])],1)],1),_vm._v(" "),_c('speciality-dialog',{attrs:{"is-shown-specialities-dialog":_vm.isShownSpecialitiesDialog},on:{"close-dialog":function($event){_vm.isShownSpecialitiesDialog = false}}})],1):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/TeachingPreferencesInfo.vue?vue&type=template&id=22c932db&scoped=true&

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// EXTERNAL MODULE: ./components/user-settings/SpecialityDialog.vue + 4 modules
var SpecialityDialog = __webpack_require__(1273);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/TeachingPreferencesInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var TeachingPreferencesInfovue_type_script_lang_js_ = ({
  name: 'TeachingPreferencesInfo',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"],
    SpecialityDialog: SpecialityDialog["default"]
  },

  data() {
    return {
      isShownSpecialitiesDialog: false
    };
  },

  computed: {
    preferences() {
      return this.$store.state.settings.preferenceItems;
    },

    selectedPreferences: {
      get() {
        return this.preferences.filter(item => item.isSelected);
      },

      set(value) {
        return this.$store.commit('settings/UPDATE_TEACHING_PREFERENCE_ITEMS', value);
      }

    }
  },

  async beforeCreate() {
    await Promise.all([this.$store.dispatch('settings/getTeachingPreferences'), this.$store.dispatch('settings/getSpecialities')]);
  },

  methods: {
    submitData() {
      this.$store.dispatch('settings/updateTeachingPreferences');
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/TeachingPreferencesInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_TeachingPreferencesInfovue_type_script_lang_js_ = (TeachingPreferencesInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCheckbox/VCheckbox.js
var VCheckbox = __webpack_require__(1128);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/user-settings/TeachingPreferencesInfo.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1301)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_TeachingPreferencesInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "22c932db",
  "651fa0da"
  
)

/* harmony default export */ var TeachingPreferencesInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */






installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCheckbox: VCheckbox["a" /* default */],VCol: VCol["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1394:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/LearningPreferencesInfo.vue?vue&type=template&id=c405d942&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('user-setting-template',{attrs:{"title":_vm.$t('learning_preferences'),"submit-func":_vm.submitData}},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium"},[_vm._v("\n          "+_vm._s(_vm.$t('you_prefer_teacher_who'))+":\n        ")]),_vm._v(" "),_c('div',[_c('v-radio-group',{staticClass:"mt-0 pt-0",attrs:{"hide-details":""},model:{value:(_vm.selectedPreferences),callback:function ($$v) {_vm.selectedPreferences=$$v},expression:"selectedPreferences"}},_vm._l((_vm.preferences),function(item){return _c('div',{key:item.id,staticClass:"d-flex justify-space-between"},[_c('div',[_c('div',{staticClass:"radiobutton"},[_c('v-radio',{staticClass:"l-radio-button l-radio-button--type-2 mt-2",attrs:{"color":"success","ripple":false,"value":item.id,"label":item.name}})],1)])])}),0)],1)])])],1),_vm._v(" "),_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-7"},[_c('div',{staticClass:"mt-2"},[_c('div',{staticClass:"input-wrap"},[_c('user-setting-autocomplete',{attrs:{"value":{},"items":_vm.languages,"attach-id":"language","placeholder":_vm.$t('choose_language')},on:{"change":function($event){return _vm.updateLanguage($event, 'language')}}})],1)])])],1)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/LearningPreferencesInfo.vue?vue&type=template&id=c405d942&scoped=true&

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// EXTERNAL MODULE: ./components/user-settings/UserSettingAutocomplete.vue + 4 modules
var UserSettingAutocomplete = __webpack_require__(1043);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/LearningPreferencesInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var LearningPreferencesInfovue_type_script_lang_js_ = ({
  name: 'LearningPreferencesInfo',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"],
    UserSettingAutocomplete: UserSettingAutocomplete["default"]
  },

  data() {
    return {
      isShownSpecialitiesDialog: false
    };
  },

  computed: {
    preferences() {
      return this.$store.state.settings.preferenceItems;
    },

    selectedPreferences: {
      get() {
        var _this$preferences$fin, _this$preferences$fin2;

        return (_this$preferences$fin = (_this$preferences$fin2 = this.preferences.find(item => item.isSelected)) === null || _this$preferences$fin2 === void 0 ? void 0 : _this$preferences$fin2.id) !== null && _this$preferences$fin !== void 0 ? _this$preferences$fin : 0;
      },

      set(id) {
        return this.$store.commit('settings/UPDATE_LEARNING_PREFERENCE_ITEMS', id);
      }

    },

    languages() {
      var _this$$store$state$se, _this$$store$state$se2;

      return (_this$$store$state$se = (_this$$store$state$se2 = this.$store.state.settings.languagesItem) === null || _this$$store$state$se2 === void 0 ? void 0 : _this$$store$state$se2.languages) !== null && _this$$store$state$se !== void 0 ? _this$$store$state$se : [];
    }

  },

  beforeCreate() {
    this.$store.dispatch('settings/getLearningPreferences');
    this.$store.dispatch('settings/getLanguages');
  },

  methods: {
    updateLanguage(item, property) {// const currentItems = [...this.item[property]]
      //
      // currentItems.push(item)
      //
      // this.$store.commit('settings/SET_LANGUAGES_ITEM', {
      //   ...this.item,
      //   [property]: currentItems,
      // })
    },

    submitData() {
      this.$store.dispatch('settings/updateLearningPreferences');
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/LearningPreferencesInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_LearningPreferencesInfovue_type_script_lang_js_ = (LearningPreferencesInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VRadioGroup/VRadio.js
var VRadio = __webpack_require__(1093);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VRadioGroup/VRadioGroup.js
var VRadioGroup = __webpack_require__(1094);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/user-settings/LearningPreferencesInfo.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1303)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_LearningPreferencesInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "c405d942",
  "1bd81169"
  
)

/* harmony default export */ var LearningPreferencesInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingAutocomplete: __webpack_require__(1043).default,UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */





installComponents_default()(component, {VCol: VCol["a" /* default */],VRadio: VRadio["a" /* default */],VRadioGroup: VRadioGroup["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1395:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/CalendarNotificationInfo.vue?vue&type=template&id=0780ed20&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.item)?_c('user-setting-template',{attrs:{"title":_vm.$t('calendar'),"submit-func":_vm.submitData}},[_c('v-row',[_c('v-col',{staticClass:"col-12 col-md-8"},[_c('div',{staticClass:"input-wrap mb-3 mb-md-0"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium"},[_vm._v("\n          "+_vm._s(_vm.$t(
              'do_you_want_to_automatically_add_lesson_bookings_to_your_calendar'
            ))+"\n        ")]),_vm._v(" "),(_vm.$vuetify.breakpoint.mdAndUp)?_c('div',{staticClass:"mt-4 d-none d-md-block",style:(_vm.styles)},[_c('text-input',{attrs:{"type-class":"border-gradient","height":"44","rules":_vm.emailRules,"hide-details":"","placeholder":_vm.$t('calendar_email_address')},model:{value:(_vm.email),callback:function ($$v) {_vm.email=$$v},expression:"email"}})],1):_vm._e()])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-auto",attrs:{"offset-md":"1"}},[_c('v-row',{attrs:{"align":"center","justify":"space-between"}},[(_vm.$vuetify.breakpoint.smAndDown)?_c('v-col',{staticClass:"col-9 col-md-12 d-md-none"},[_c('div',{style:(_vm.styles)},[_c('text-input',{attrs:{"type-class":"border-gradient","height":"44","rules":_vm.emailRules,"hide-details":"","placeholder":_vm.$t('calendar_email_address')},model:{value:(_vm.email),callback:function ($$v) {_vm.email=$$v},expression:"email"}})],1)]):_vm._e(),_vm._v(" "),_c('v-col',{staticClass:"col-auto"},[_c('v-switch',{staticClass:"pt-0 mt-0 mt-md-3",attrs:{"inset":"","ripple":false,"color":"success","dense":"","hide-details":""},model:{value:(_vm.enabled),callback:function ($$v) {_vm.enabled=$$v},expression:"enabled"}})],1)],1)],1)],1),_vm._v(" "),(_vm.isTeacher)?_c('v-row',{staticClass:"pt-3 pt-md-4"},[_c('v-col',{staticClass:"col-12 col-md-8"},[_c('div',{staticClass:"input-wrap mb-3 mb-md-0"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium d-flex align-center"},[_c('img',{staticStyle:{"height":"24px","width":"24px","margin-right":"8px"},attrs:{"src":"https://www.gstatic.com/marketing-cms/assets/images/cf/3c/0d56042f479fac9ad22d06855578/calender.webp=s96-fcrop64=1,00000000ffffffff-rw","alt":"Google Calendar"}}),_vm._v("\n          Connect to Google Calendar\n        ")]),_vm._v(" "),_c('span',{staticStyle:{"font-size":"14px"},domProps:{"innerHTML":_vm._s(_vm.$t('advanced_integration_google_calendar_only'))}})])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-auto d-flex justify-end",attrs:{"offset-md":"1"}},[_c('v-switch',{staticClass:"pt-0 mt-0 mt-md-3",attrs:{"inset":"","ripple":false,"color":"success","dense":"","hide-details":""},on:{"change":_vm.toggleAdvancedIntegration},model:{value:(_vm.syncCalendarWithSlots),callback:function ($$v) {_vm.syncCalendarWithSlots=$$v},expression:"syncCalendarWithSlots"}})],1)],1):_vm._e(),_vm._v(" "),(_vm.isTeacher)?_c('v-row',{staticClass:"pt-0 pb-2"},[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"mt-2",staticStyle:{"font-size":"13px","color":"#757575"}},[_vm._v("\n        This app's use and transfer to any other app of information received\n        from Google APIs will adhere to the Google API Services User Data\n        Policy, including the Limited Use requirements.\n        "),_c('a',{staticStyle:{"color":"#1a73e8","text-decoration":"underline"},attrs:{"href":"https://developers.google.com/terms/api-services-user-data-policy","target":"_blank","rel":"noopener"}},[_vm._v("\n          Read more\n        ")])])])],1):_vm._e()],1):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/CalendarNotificationInfo.vue?vue&type=template&id=0780ed20&scoped=true&

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// EXTERNAL MODULE: ./components/form/TextInput.vue + 4 modules
var TextInput = __webpack_require__(102);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/CalendarNotificationInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//


/* harmony default export */ var CalendarNotificationInfovue_type_script_lang_js_ = ({
  name: 'CalendarNotificationInfo',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"],
    TextInput: TextInput["default"]
  },

  data() {
    return {
      syncCalendarWithSlots: undefined
    };
  },

  computed: {
    isTeacher() {
      return this.$store.getters['user/isTeacher'];
    },

    item() {
      return this.$store.state.settings.notificationCalendarItem;
    },

    emailRules() {
      return this.enabled ? [v => !!v, v => /.+@.+\..+/.test(v)] : [];
    },

    email: {
      get() {
        return this.item.email;
      },

      set(value) {
        this.$store.commit('settings/UPDATE_NOTIFICATION_CALENDAR_ITEM', {
          email: value
        });
      }

    },
    enabled: {
      get() {
        return this.item.enabled;
      },

      set(value) {
        this.$store.commit('settings/UPDATE_NOTIFICATION_CALENDAR_ITEM', {
          enabled: value
        });
      }

    },

    // syncCalendarWithSlots() {
    //   console.log(this.$store.state.user.item.syncCalendarWithSlots)
    //   return this.$store.state.user.item.syncCalendarWithSlots
    // },
    styles() {
      let obj = {};

      if (!this.enabled) {
        obj = {
          opacity: 0,
          visibility: 'hidden',
          zIndex: -1
        };
      }

      return obj;
    }

  },

  beforeCreate() {
    this.$store.dispatch('settings/getNotificationCalendar');
  },

  beforeMount() {
    this.syncCalendarWithSlots = this.$store.state.user.item.syncCalendarWithSlots;
  },

  methods: {
    submitData() {
      this.$store.dispatch('settings/updateNotificationCalendar');
    },

    toggleAdvancedIntegration(event) {
      window.location = event ? '/user/login/google-calendar' : '/google/calendar-notification-webhook-deactivate';
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/CalendarNotificationInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_CalendarNotificationInfovue_type_script_lang_js_ = (CalendarNotificationInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VSwitch/VSwitch.js
var VSwitch = __webpack_require__(1298);

// CONCATENATED MODULE: ./components/user-settings/CalendarNotificationInfo.vue



function injectStyles (context) {
  
  
}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_CalendarNotificationInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "0780ed20",
  "764cb474"
  
)

/* harmony default export */ var CalendarNotificationInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */




installComponents_default()(component, {VCol: VCol["a" /* default */],VRow: VRow["a" /* default */],VSwitch: VSwitch["a" /* default */]})


/***/ }),

/***/ 1396:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/ReceiptInfo.vue?vue&type=template&id=af7ad3a4&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('user-setting-template',{attrs:{"title":_vm.$t('receipt_information'),"submit-func":_vm.submitData}},[_c('v-row',[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"input-wrap-title body-1 font-weight-medium mb-2"},[_vm._v("\n        "+_vm._s(_vm.$t(
            'provide_purchaser_information_you_would_like_to_appear_in_receipt'
          ))+":\n      ")])]),_vm._v(" "),_c('v-col',{staticClass:"col-12 col-md-10"},[_c('div',{staticClass:"input-wrap"},[_c('div',[_c('v-textarea',{staticClass:"l-textarea",attrs:{"value":_vm.itemText,"no-resize":"","height":"186","solo":"","dense":""},on:{"input":function($event){return _vm.updateValue($event, 'text')}}})],1)])])],1)],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/ReceiptInfo.vue?vue&type=template&id=af7ad3a4&scoped=true&

// EXTERNAL MODULE: ./components/user-settings/UserSettingTemplate.vue + 4 modules
var UserSettingTemplate = __webpack_require__(929);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/ReceiptInfo.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var ReceiptInfovue_type_script_lang_js_ = ({
  name: 'ReceiptInfo',
  components: {
    UserSettingTemplate: UserSettingTemplate["default"]
  },

  data() {
    return {
      isShownSpecialitiesDialog: false
    };
  },

  computed: {
    itemText() {
      var _this$$store$state$se, _this$$store$state$se2;

      return (_this$$store$state$se = (_this$$store$state$se2 = this.$store.state.settings.invoiceItem) === null || _this$$store$state$se2 === void 0 ? void 0 : _this$$store$state$se2.text) !== null && _this$$store$state$se !== void 0 ? _this$$store$state$se : '';
    }

  },

  beforeCreate() {
    this.$store.dispatch('settings/getInvoice');
  },

  methods: {
    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_INVOICE_ITEM', {
        [property]: value
      });
    },

    submitData() {
      this.$store.dispatch('settings/updateInvoice');
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/ReceiptInfo.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_ReceiptInfovue_type_script_lang_js_ = (ReceiptInfovue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(897);

// CONCATENATED MODULE: ./components/user-settings/ReceiptInfo.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1309)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_ReceiptInfovue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "af7ad3a4",
  "0bce53b2"
  
)

/* harmony default export */ var ReceiptInfo = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserSettingTemplate: __webpack_require__(929).default})


/* vuetify-loader */




installComponents_default()(component, {VCol: VCol["a" /* default */],VRow: VRow["a" /* default */],VTextarea: VTextarea["a" /* default */]})


/***/ }),

/***/ 1443:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1372);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1444:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-settings{--sidebar-width:295px;margin-top:10px}@media only screen and (max-width:1439px){.user-settings{--sidebar-width:235px}}.user-settings-wrap{max-width:1030px}.user-settings-wrap>div{width:100%}.user-settings-title{font-size:24px;line-height:1.333}@media only screen and (max-width:479px){.user-settings-title{font-size:20px}}.user-settings-content{width:calc(100% - var(--sidebar-width));padding-left:20px}.user-settings-sidebar{width:var(--sidebar-width)}.user-settings-sidebar-sticky{position:sticky;top:80px}.user-settings .nav-list{padding-left:0;list-style-type:none}.user-settings .nav-list>li{margin-bottom:12px}.user-settings .nav-list .v-btn{padding:0 10px 0 40px;border-radius:20px;font-size:18px;background-color:transparent!important}@media only screen and (max-width:1439px){.user-settings .nav-list .v-btn{padding:0 10px 0 20px;font-size:16px}}.user-settings .nav-list .v-btn__content{justify-content:flex-start;color:var(--v-greyDark-base);text-align:left}.user-settings .nav-list .v-btn:before{transition:none!important}.user-settings .nav-list .v-btn.active{background:linear-gradient(126.15deg,rgba(128,182,34,.1),rgba(60,135,248,.1) 102.93%)}.user-settings .nav-list .v-btn.active .v-btn__content{color:var(--v-dark-base);font-weight:600!important}.user-settings .nav-list .v-btn.active:focus:before,.user-settings .nav-list .v-btn.active:hover:before{display:none!important}.user-settings .nav-list .v-btn:focus:before,.user-settings .nav-list .v-btn:hover:before{background:linear-gradient(126.15deg,rgba(128,182,34,.1),rgba(60,135,248,.1) 102.93%);opacity:.6}.user-settings .tabs-mobile.v-expansion-panels{border-radius:0}.user-settings .tabs-mobile.v-expansion-panels>.v-expansion-panel{margin-bottom:16px;background-color:transparent!important}.user-settings .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-header{min-height:48px;padding:12px 40px}@media only screen and (max-width:479px){.user-settings .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-header{padding:12px 20px}}.user-settings .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-header--active{border-radius:20px;background:linear-gradient(126.15deg,rgba(128,182,34,.1),rgba(60,135,248,.1) 102.93%)}.user-settings .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-content{margin-top:12px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1);border-radius:20px}.user-settings .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-content>.v-expansion-panel-content__wrap{padding:24px 16px 32px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1482:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/settings/index.vue?vue&type=template&id=01b2dd3c&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-col',{staticClass:"col-12 px-0"},[_c('div',{staticClass:"user-settings"},[_c('v-container',{staticClass:"pa-0",attrs:{"fluid":""}},[_c('v-row',{attrs:{"no-gutters":""}},[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"user-settings-wrap mx-auto"},[(_vm.fullName)?_c('div',{staticClass:"user-settings-title mb-3 mb-md-2"},[_vm._v("\n              "+_vm._s(_vm.$t('welcome'))+",\n              "),_c('span',{staticClass:"font-weight-medium"},[_vm._v("\n                "+_vm._s(_vm.fullName)+"\n              ")]),_vm._v("\n              👋\n            ")]):_vm._e(),_vm._v(" "),(_vm.$vuetify.breakpoint.smAndUp)?_c('div',{staticClass:"d-none d-sm-flex"},[_c('aside',{staticClass:"user-settings-sidebar"},[_c('div',{staticClass:"user-settings-sidebar-sticky"},[_c('div',{staticClass:"user-settings-tabs-nav"},[_c('ul',{staticClass:"nav-list"},_vm._l((_vm.tabs),function(tab,idx){return _c('li',{key:idx,staticClass:"nav-item"},[_c('v-btn',{class:[
                            'font-weight-regular',
                            { active: _vm.tabActive === idx } ],attrs:{"dark":tab.value === _vm.tabActive,"width":"100%","height":"48"},on:{"click":function($event){return _vm.tabClickHandler(tab.value)}}},[_vm._v("\n                          "+_vm._s(tab.name)+"\n                        ")])],1)}),0)])])]),_vm._v(" "),_c('div',{ref:"userSettingsContent",staticClass:"user-settings-content"},_vm._l((_vm.tabs),function(tab,idx){return _c('keep-alive',{key:idx},[(_vm.tabActive === idx)?_c(tab.component,_vm._b({tag:"component",on:{"reload-page":_vm.reloadPage}},'component',{ basicInfoItem: _vm.basicInfoItem },false)):_vm._e()],1)}),1)]):_c('div',{staticClass:"d-sm-none"},[_c('client-only',[_c('v-expansion-panels',{staticClass:"tabs-mobile",attrs:{"accordion":"","flat":""},model:{value:(_vm.tabActive),callback:function ($$v) {_vm.tabActive=$$v},expression:"tabActive"}},_vm._l((_vm.tabs),function(tab,idx){return _c('v-expansion-panel',{key:idx},[_c('v-expansion-panel-header',{ref:("panel-" + idx),refInFor:true,attrs:{"disable-icon-rotate":""},scopedSlots:_vm._u([{key:"actions",fn:function(){return [(_vm.tabActive === idx)?[_c('v-icon',{attrs:{"color":"dark"}},[_vm._v("\n                            "+_vm._s(_vm.mdiMinus)+"\n                          ")])]:[_c('v-img',{attrs:{"src":__webpack_require__(515),"width":"24","height":"24"}})]]},proxy:true}],null,true)},[_vm._v("\n                      "+_vm._s(tab.name)+"\n                      ")]),_vm._v(" "),_c('v-expansion-panel-content',[_c('keep-alive',[_c(tab.component,_vm._b({tag:"component",on:{"reload-page":_vm.reloadPage}},'component',{ basicInfoItem: _vm.basicInfoItem },false))],1)],1)],1)}),1)],1)],1)])])],1)],1)],1)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/user/settings/index.vue?vue&type=template&id=01b2dd3c&

// EXTERNAL MODULE: external "@mdi/js"
var js_ = __webpack_require__(48);

// EXTERNAL MODULE: ./components/user-settings/BasicInfo.vue + 4 modules
var BasicInfo = __webpack_require__(1386);

// EXTERNAL MODULE: ./components/user-settings/SummaryInfo.vue + 4 modules
var SummaryInfo = __webpack_require__(1387);

// EXTERNAL MODULE: ./components/user-settings/LanguagesInfo.vue + 4 modules
var LanguagesInfo = __webpack_require__(1388);

// EXTERNAL MODULE: ./components/user-settings/BackgroundInfo.vue + 4 modules
var BackgroundInfo = __webpack_require__(1389);

// EXTERNAL MODULE: ./components/user-settings/AboutMeInfo.vue + 4 modules
var AboutMeInfo = __webpack_require__(1390);

// EXTERNAL MODULE: ./components/user-settings/PricingTableInfo.vue + 4 modules
var PricingTableInfo = __webpack_require__(1391);

// EXTERNAL MODULE: ./components/user-settings/CoursesInfo.vue + 4 modules
var CoursesInfo = __webpack_require__(1392);

// EXTERNAL MODULE: ./components/user-settings/TeachingPreferencesInfo.vue + 4 modules
var TeachingPreferencesInfo = __webpack_require__(1393);

// EXTERNAL MODULE: ./components/user-settings/LearningPreferencesInfo.vue + 4 modules
var LearningPreferencesInfo = __webpack_require__(1394);

// EXTERNAL MODULE: ./components/user-settings/TeachingQualificationsInfo.vue + 5 modules
var TeachingQualificationsInfo = __webpack_require__(1383);

// EXTERNAL MODULE: ./components/user-settings/CalendarNotificationInfo.vue + 4 modules
var CalendarNotificationInfo = __webpack_require__(1395);

// EXTERNAL MODULE: ./components/user-settings/ReceiptInfo.vue + 4 modules
var ReceiptInfo = __webpack_require__(1396);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/settings/index.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//













/* harmony default export */ var settingsvue_type_script_lang_js_ = ({
  name: 'UserSettingsPage',
  components: {
    UserSettingBasicInfo: BasicInfo["default"],
    SummaryInfo: SummaryInfo["default"],
    LanguagesInfo: LanguagesInfo["default"],
    BackgroundInfo: BackgroundInfo["default"],
    AboutMeInfo: AboutMeInfo["default"],
    PricingTableInfo: PricingTableInfo["default"],
    CoursesInfo: CoursesInfo["default"],
    TeachingPreferencesInfo: TeachingPreferencesInfo["default"],
    LearningPreferencesInfo: LearningPreferencesInfo["default"],
    TeachingQualificationsInfo: TeachingQualificationsInfo["default"],
    CalendarNotificationInfo: CalendarNotificationInfo["default"],
    ReceiptInfo: ReceiptInfo["default"]
  },
  middleware: 'authenticated',

  async asyncData(ctx) {
    await ctx.store.dispatch('settings/getBasicInfo');
  },

  data() {
    return {
      mdiMinus: js_["mdiMinus"],
      fullName: null,
      tabActive: null,
      snackbar: true,
      tabs: [{
        id: 0,
        name: this.$t('basic_info'),
        value: 'basic-info',
        component: BasicInfo["default"]
      }, {
        id: 1,
        name: this.$t('languages'),
        value: 'languages',
        component: LanguagesInfo["default"]
      }, {
        id: 11,
        name: this.$t('calendar'),
        value: 'calendar',
        component: CalendarNotificationInfo["default"]
      }]
    };
  },

  head() {
    return {
      title: this.$t('user_settings_page.seo_title'),
      meta: [{
        hid: 'description',
        name: 'description',
        content: this.$t('user_settings_page.seo_description')
      }, {
        hid: 'og:title',
        name: 'og:title',
        property: 'og:title',
        content: this.$t('user_settings_page.seo_title')
      }, {
        property: 'og:description',
        content: this.$t('user_settings_page.seo_description')
      }],
      bodyAttrs: {
        class: `${this.locale} user-settings-page`
      }
    };
  },

  computed: {
    locale() {
      return this.$i18n.locale;
    },

    basicInfoItem() {
      return this.$store.state.settings.basicInfoItem;
    },

    isTeacher() {
      return this.$store.getters['user/isTeacher'];
    },

    isStudent() {
      return this.$store.getters['user/isStudent'];
    }

  },
  watch: {
    $route() {
      this.getRouteHash();
    },

    tabActive(newValue, oldValue) {
      var _this$tabs$newValue$v, _this$tabs$newValue;

      window.location.hash = (_this$tabs$newValue$v = (_this$tabs$newValue = this.tabs[newValue]) === null || _this$tabs$newValue === void 0 ? void 0 : _this$tabs$newValue.value) !== null && _this$tabs$newValue$v !== void 0 ? _this$tabs$newValue$v : 'basic-info';
    }

  },

  mounted() {
    this.fullName = `${this.basicInfoItem.firstName} ${this.basicInfoItem.lastName}`;

    if (this.isTeacher) {
      this.tabs = [...this.tabs, ...[{
        id: 2,
        name: this.$t('summary'),
        value: 'summary',
        component: SummaryInfo["default"]
      }, {
        id: 3,
        name: this.$t('background'),
        value: 'background',
        component: BackgroundInfo["default"]
      }, {
        id: 4,
        name: this.$t('about_me'),
        value: 'about-me',
        component: AboutMeInfo["default"]
      }, {
        id: 5,
        name: this.$t('pricing_table'),
        value: 'pricing-table',
        component: PricingTableInfo["default"]
      }, {
        id: 6,
        name: this.$t('courses'),
        value: 'courses',
        component: CoursesInfo["default"]
      }, {
        id: 7,
        name: this.$t('teaching_preferences'),
        value: 'teaching-preferences',
        component: TeachingPreferencesInfo["default"]
      }, {
        id: 9,
        name: this.$t('teaching_qualifications'),
        value: 'teaching-qualifications',
        component: TeachingQualificationsInfo["default"]
      }]];
    }

    if (this.isStudent) {
      this.tabs = [...this.tabs, ...[// {
      //   id: 8,
      //   name: this.$t('learning_preferences'),
      //   value: 'learning-preferences',
      //   component: LearningPreferencesInfo,
      // },
      {
        id: 10,
        name: this.$t('receipt_information'),
        value: 'receipt-information',
        component: ReceiptInfo["default"]
      }]];
    }

    this.tabs = this.tabs.sort((a, b) => a.id - b.id);
    this.getRouteHash();
  },

  methods: {
    getRouteHash() {
      const hash = this.$route.hash.replace('#', '');
      this.tabActive = this.tabs.map(item => item.value).indexOf(hash) || 0;

      if (window.scrollY > 110) {
        this.$nextTick(function () {
          const el = this.$vuetify.breakpoint.smAndUp ? this.$refs.userSettingsContent : this.$refs[`panel-${this.tabActive}`][0].$el;

          if (el) {
            window.setTimeout(() => {
              this.$vuetify.goTo(el, {
                duration: 0,
                offset: 10,
                easing: 'linear'
              });
            }, 400);
          }
        });
      }
    },

    tabClickHandler(value) {
      window.location.hash = value;
    },

    reloadPage() {
      window.location.reload();
    }

  }
});
// CONCATENATED MODULE: ./pages/user/settings/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settingsvue_type_script_lang_js_ = (settingsvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanel.js
var VExpansionPanel = __webpack_require__(1072);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelContent.js
var VExpansionPanelContent = __webpack_require__(1073);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelHeader.js
var VExpansionPanelHeader = __webpack_require__(1074);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanels.js
var VExpansionPanels = __webpack_require__(1092);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(87);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./pages/user/settings/index.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1443)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settingsvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "6fee6964"
  
)

/* harmony default export */ var settings = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */











installComponents_default()(component, {VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VExpansionPanel: VExpansionPanel["a" /* default */],VExpansionPanelContent: VExpansionPanelContent["a" /* default */],VExpansionPanelHeader: VExpansionPanelHeader["a" /* default */],VExpansionPanels: VExpansionPanels["a" /* default */],VIcon: VIcon["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 499:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXPORTS
__webpack_require__.d(__webpack_exports__, "a", function() { return /* binding */ VListItemContent; });
__webpack_require__.d(__webpack_exports__, "c", function() { return /* binding */ VListItemTitle; });
__webpack_require__.d(__webpack_exports__, "b", function() { return /* binding */ VListItemSubtitle; });

// UNUSED EXPORTS: VListItemActionText, VList, VListGroup, VListItem, VListItemAction, VListItemAvatar, VListItemIcon, VListItemGroup

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/helpers.js
var helpers = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VList.js
var VList = __webpack_require__(831);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VList/VListGroup.sass
var VListGroup = __webpack_require__(917);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/index.js
var VIcon = __webpack_require__(66);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItem.js
var VListItem = __webpack_require__(828);

// EXTERNAL MODULE: external "vue"
var external_vue_ = __webpack_require__(1);
var external_vue_default = /*#__PURE__*/__webpack_require__.n(external_vue_);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VList/VListItemIcon.js
// Types

/* @vue/component */

/* harmony default export */ var VListItemIcon = (external_vue_default.a.extend({
  name: 'v-list-item-icon',
  functional: true,

  render(h, {
    data,
    children
  }) {
    data.staticClass = `v-list-item__icon ${data.staticClass || ''}`.trim();
    return h('div', data, children);
  }

}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/binds-attrs/index.js
var binds_attrs = __webpack_require__(23);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/bootable/index.js
var bootable = __webpack_require__(103);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/colorable/index.js
var colorable = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/toggleable/index.js
var toggleable = __webpack_require__(10);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/registrable/index.js
var registrable = __webpack_require__(29);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/ripple/index.js
var ripple = __webpack_require__(22);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/transitions/index.js + 2 modules
var transitions = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/mixins.js
var mixins = __webpack_require__(2);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VList/VListGroup.js
// Styles
 // Components



 // Mixins





 // Directives

 // Transitions

 // Utils



const baseMixins = Object(mixins["a" /* default */])(binds_attrs["a" /* default */], bootable["a" /* default */], colorable["a" /* default */], Object(registrable["a" /* inject */])('list'), toggleable["a" /* default */]);
/* harmony default export */ var VList_VListGroup = (baseMixins.extend().extend({
  name: 'v-list-group',
  directives: {
    ripple: ripple["a" /* default */]
  },
  props: {
    activeClass: {
      type: String,
      default: ''
    },
    appendIcon: {
      type: String,
      default: '$expand'
    },
    color: {
      type: String,
      default: 'primary'
    },
    disabled: Boolean,
    group: String,
    noAction: Boolean,
    prependIcon: String,
    ripple: {
      type: [Boolean, Object],
      default: true
    },
    subGroup: Boolean
  },
  computed: {
    classes() {
      return {
        'v-list-group--active': this.isActive,
        'v-list-group--disabled': this.disabled,
        'v-list-group--no-action': this.noAction,
        'v-list-group--sub-group': this.subGroup
      };
    }

  },
  watch: {
    isActive(val) {
      /* istanbul ignore else */
      if (!this.subGroup && val) {
        this.list && this.list.listClick(this._uid);
      }
    },

    $route: 'onRouteChange'
  },

  created() {
    this.list && this.list.register(this);

    if (this.group && this.$route && this.value == null) {
      this.isActive = this.matchRoute(this.$route.path);
    }
  },

  beforeDestroy() {
    this.list && this.list.unregister(this);
  },

  methods: {
    click(e) {
      if (this.disabled) return;
      this.isBooted = true;
      this.$emit('click', e);
      this.$nextTick(() => this.isActive = !this.isActive);
    },

    genIcon(icon) {
      return this.$createElement(VIcon["a" /* default */], icon);
    },

    genAppendIcon() {
      const icon = !this.subGroup ? this.appendIcon : false;
      if (!icon && !this.$slots.appendIcon) return null;
      return this.$createElement(VListItemIcon, {
        staticClass: 'v-list-group__header__append-icon'
      }, [this.$slots.appendIcon || this.genIcon(icon)]);
    },

    genHeader() {
      return this.$createElement(VListItem["a" /* default */], {
        staticClass: 'v-list-group__header',
        attrs: {
          'aria-expanded': String(this.isActive),
          role: 'button'
        },
        class: {
          [this.activeClass]: this.isActive
        },
        props: {
          inputValue: this.isActive
        },
        directives: [{
          name: 'ripple',
          value: this.ripple
        }],
        on: { ...this.listeners$,
          click: this.click
        }
      }, [this.genPrependIcon(), this.$slots.activator, this.genAppendIcon()]);
    },

    genItems() {
      return this.showLazyContent(() => [this.$createElement('div', {
        staticClass: 'v-list-group__items',
        directives: [{
          name: 'show',
          value: this.isActive
        }]
      }, Object(helpers["n" /* getSlot */])(this))]);
    },

    genPrependIcon() {
      const icon = this.subGroup && this.prependIcon == null ? '$subgroup' : this.prependIcon;
      if (!icon && !this.$slots.prependIcon) return null;
      return this.$createElement(VListItemIcon, {
        staticClass: 'v-list-group__header__prepend-icon'
      }, [this.$slots.prependIcon || this.genIcon(icon)]);
    },

    onRouteChange(to) {
      /* istanbul ignore if */
      if (!this.group) return;
      const isActive = this.matchRoute(to.path);
      /* istanbul ignore else */

      if (isActive && this.isActive !== isActive) {
        this.list && this.list.listClick(this._uid);
      }

      this.isActive = isActive;
    },

    toggle(uid) {
      const isActive = this._uid === uid;
      if (isActive) this.isBooted = true;
      this.$nextTick(() => this.isActive = isActive);
    },

    matchRoute(to) {
      return to.match(this.group) !== null;
    }

  },

  render(h) {
    return h('div', this.setTextColor(this.isActive && this.color, {
      staticClass: 'v-list-group',
      class: this.classes
    }), [this.genHeader(), h(transitions["a" /* VExpandTransition */], this.genItems())]);
  }

}));
// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VList/VListItemGroup.sass
var VListItemGroup = __webpack_require__(919);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VItemGroup/VItemGroup.js
var VItemGroup = __webpack_require__(902);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VList/VListItemGroup.js
// Styles
 // Extensions

 // Mixins

 // Utilities


/* harmony default export */ var VList_VListItemGroup = (Object(mixins["a" /* default */])(VItemGroup["a" /* BaseItemGroup */], colorable["a" /* default */]).extend({
  name: 'v-list-item-group',

  provide() {
    return {
      isInGroup: true,
      listItemGroup: this
    };
  },

  computed: {
    classes() {
      return { ...VItemGroup["a" /* BaseItemGroup */].options.computed.classes.call(this),
        'v-list-item-group': true
      };
    }

  },
  methods: {
    genData() {
      return this.setTextColor(this.color, { ...VItemGroup["a" /* BaseItemGroup */].options.methods.genData.call(this),
        attrs: {
          role: 'listbox'
        }
      });
    }

  }
}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItemAction.js
var VListItemAction = __webpack_require__(904);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/index.js
var VAvatar = __webpack_require__(500);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VList/VListItemAvatar.js
// Components

/* @vue/component */

/* harmony default export */ var VListItemAvatar = (VAvatar["a" /* default */].extend({
  name: 'v-list-item-avatar',
  props: {
    horizontal: Boolean,
    size: {
      type: [Number, String],
      default: 40
    }
  },
  computed: {
    classes() {
      return {
        'v-list-item__avatar--horizontal': this.horizontal,
        ...VAvatar["a" /* default */].options.computed.classes.call(this),
        'v-avatar--tile': this.tile || this.horizontal
      };
    }

  },

  render(h) {
    const render = VAvatar["a" /* default */].options.render.call(this, h);
    render.data = render.data || {};
    render.data.staticClass += ' v-list-item__avatar';
    return render;
  }

}));
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VList/index.js








const VListItemActionText = Object(helpers["g" /* createSimpleFunctional */])('v-list-item__action-text', 'span');
const VListItemContent = Object(helpers["g" /* createSimpleFunctional */])('v-list-item__content', 'div');
const VListItemTitle = Object(helpers["g" /* createSimpleFunctional */])('v-list-item__title', 'div');
const VListItemSubtitle = Object(helpers["g" /* createSimpleFunctional */])('v-list-item__subtitle', 'div');

/* harmony default export */ var components_VList = ({
  $_vuetify_subcomponents: {
    VList: VList["a" /* default */],
    VListGroup: VList_VListGroup,
    VListItem: VListItem["a" /* default */],
    VListItemAction: VListItemAction["a" /* default */],
    VListItemActionText,
    VListItemAvatar: VListItemAvatar,
    VListItemContent,
    VListItemGroup: VList_VListItemGroup,
    VListItemIcon: VListItemIcon,
    VListItemSubtitle,
    VListItemTitle
  }
});

/***/ }),

/***/ 500:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _VAvatar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(830);


/* harmony default export */ __webpack_exports__["a"] = (_VAvatar__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]);

/***/ }),

/***/ 832:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _VMenu__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(833);


/* harmony default export */ __webpack_exports__["a"] = (_VMenu__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]);

/***/ }),

/***/ 853:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _VTextField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(39);


/* harmony default export */ __webpack_exports__["a"] = (_VTextField__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]);

/***/ }),

/***/ 901:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(909);
/* harmony import */ var _src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
/* harmony import */ var _transitions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(67);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(66);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9);
/* harmony import */ var _mixins_groupable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(47);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(7);
/* harmony import */ var _mixins_toggleable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(10);
/* harmony import */ var _mixins_routable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(18);
/* harmony import */ var _mixins_sizeable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(49);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(3);
// Styles

 // Components


 // Mixins






 // Utilities


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(_mixins_colorable__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"], _mixins_sizeable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"], _mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], _mixins_themeable__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"], Object(_mixins_groupable__WEBPACK_IMPORTED_MODULE_5__[/* factory */ "a"])('chipGroup'), Object(_mixins_toggleable__WEBPACK_IMPORTED_MODULE_7__[/* factory */ "b"])('inputValue')).extend({
  name: 'v-chip',
  props: {
    active: {
      type: Boolean,
      default: true
    },
    activeClass: {
      type: String,

      default() {
        if (!this.chipGroup) return '';
        return this.chipGroup.activeClass;
      }

    },
    close: Boolean,
    closeIcon: {
      type: String,
      default: '$delete'
    },
    closeLabel: {
      type: String,
      default: '$vuetify.close'
    },
    disabled: Boolean,
    draggable: Boolean,
    filter: Boolean,
    filterIcon: {
      type: String,
      default: '$complete'
    },
    label: Boolean,
    link: Boolean,
    outlined: Boolean,
    pill: Boolean,
    tag: {
      type: String,
      default: 'span'
    },
    textColor: String,
    value: null
  },
  data: () => ({
    proxyClass: 'v-chip--active'
  }),
  computed: {
    classes() {
      return {
        'v-chip': true,
        ..._mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].options.computed.classes.call(this),
        'v-chip--clickable': this.isClickable,
        'v-chip--disabled': this.disabled,
        'v-chip--draggable': this.draggable,
        'v-chip--label': this.label,
        'v-chip--link': this.isLink,
        'v-chip--no-color': !this.color,
        'v-chip--outlined': this.outlined,
        'v-chip--pill': this.pill,
        'v-chip--removable': this.hasClose,
        ...this.themeClasses,
        ...this.sizeableClasses,
        ...this.groupClasses
      };
    },

    hasClose() {
      return Boolean(this.close);
    },

    isClickable() {
      return Boolean(_mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].options.computed.isClickable.call(this) || this.chipGroup);
    }

  },

  created() {
    const breakingProps = [['outline', 'outlined'], ['selected', 'input-value'], ['value', 'active'], ['@input', '@active.sync']];
    /* istanbul ignore next */

    breakingProps.forEach(([original, replacement]) => {
      if (this.$attrs.hasOwnProperty(original)) Object(_util_console__WEBPACK_IMPORTED_MODULE_10__[/* breaking */ "a"])(original, replacement, this);
    });
  },

  methods: {
    click(e) {
      this.$emit('click', e);
      this.chipGroup && this.toggle();
    },

    genFilter() {
      const children = [];

      if (this.isActive) {
        children.push(this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"], {
          staticClass: 'v-chip__filter',
          props: {
            left: true
          }
        }, this.filterIcon));
      }

      return this.$createElement(_transitions__WEBPACK_IMPORTED_MODULE_2__[/* VExpandXTransition */ "b"], children);
    },

    genClose() {
      return this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"], {
        staticClass: 'v-chip__close',
        props: {
          right: true,
          size: 18
        },
        attrs: {
          'aria-label': this.$vuetify.lang.t(this.closeLabel)
        },
        on: {
          click: e => {
            e.stopPropagation();
            e.preventDefault();
            this.$emit('click:close');
            this.$emit('update:active', false);
          }
        }
      }, this.closeIcon);
    },

    genContent() {
      return this.$createElement('span', {
        staticClass: 'v-chip__content'
      }, [this.filter && this.genFilter(), this.$slots.default, this.hasClose && this.genClose()]);
    }

  },

  render(h) {
    const children = [this.genContent()];
    let {
      tag,
      data
    } = this.generateRouteLink();
    data.attrs = { ...data.attrs,
      draggable: this.draggable ? 'true' : undefined,
      tabindex: this.chipGroup && !this.disabled ? 0 : data.attrs.tabindex
    };
    data.directives.push({
      name: 'show',
      value: this.active
    });
    data = this.setBackgroundColor(this.color, data);
    const color = this.textColor || this.outlined && this.color;
    return h(tag, this.setTextColor(color, data), children);
  }

}));

/***/ }),

/***/ 902:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return BaseItemGroup; });
/* harmony import */ var _src_components_VItemGroup_VItemGroup_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(906);
/* harmony import */ var _src_components_VItemGroup_VItemGroup_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VItemGroup_VItemGroup_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _mixins_proxyable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(104);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3);
// Styles


 // Utilities



const BaseItemGroup = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(_mixins_proxyable__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], _mixins_themeable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"]).extend({
  name: 'base-item-group',
  props: {
    activeClass: {
      type: String,
      default: 'v-item--active'
    },
    mandatory: Boolean,
    max: {
      type: [Number, String],
      default: null
    },
    multiple: Boolean,
    tag: {
      type: String,
      default: 'div'
    }
  },

  data() {
    return {
      // As long as a value is defined, show it
      // Otherwise, check if multiple
      // to determine which default to provide
      internalLazyValue: this.value !== undefined ? this.value : this.multiple ? [] : undefined,
      items: []
    };
  },

  computed: {
    classes() {
      return {
        'v-item-group': true,
        ...this.themeClasses
      };
    },

    selectedIndex() {
      return this.selectedItem && this.items.indexOf(this.selectedItem) || -1;
    },

    selectedItem() {
      if (this.multiple) return undefined;
      return this.selectedItems[0];
    },

    selectedItems() {
      return this.items.filter((item, index) => {
        return this.toggleMethod(this.getValue(item, index));
      });
    },

    selectedValues() {
      if (this.internalValue == null) return [];
      return Array.isArray(this.internalValue) ? this.internalValue : [this.internalValue];
    },

    toggleMethod() {
      if (!this.multiple) {
        return v => this.internalValue === v;
      }

      const internalValue = this.internalValue;

      if (Array.isArray(internalValue)) {
        return v => internalValue.includes(v);
      }

      return () => false;
    }

  },
  watch: {
    internalValue: 'updateItemsState',
    items: 'updateItemsState'
  },

  created() {
    if (this.multiple && !Array.isArray(this.internalValue)) {
      Object(_util_console__WEBPACK_IMPORTED_MODULE_4__[/* consoleWarn */ "c"])('Model must be bound to an array if the multiple property is true.', this);
    }
  },

  methods: {
    genData() {
      return {
        class: this.classes
      };
    },

    getValue(item, i) {
      return item.value == null || item.value === '' ? i : item.value;
    },

    onClick(item) {
      this.updateInternalValue(this.getValue(item, this.items.indexOf(item)));
    },

    register(item) {
      const index = this.items.push(item) - 1;
      item.$on('change', () => this.onClick(item)); // If no value provided and mandatory,
      // assign first registered item

      if (this.mandatory && !this.selectedValues.length) {
        this.updateMandatory();
      }

      this.updateItem(item, index);
    },

    unregister(item) {
      if (this._isDestroyed) return;
      const index = this.items.indexOf(item);
      const value = this.getValue(item, index);
      this.items.splice(index, 1);
      const valueIndex = this.selectedValues.indexOf(value); // Items is not selected, do nothing

      if (valueIndex < 0) return; // If not mandatory, use regular update process

      if (!this.mandatory) {
        return this.updateInternalValue(value);
      } // Remove the value


      if (this.multiple && Array.isArray(this.internalValue)) {
        this.internalValue = this.internalValue.filter(v => v !== value);
      } else {
        this.internalValue = undefined;
      } // If mandatory and we have no selection
      // add the last item as value

      /* istanbul ignore else */


      if (!this.selectedItems.length) {
        this.updateMandatory(true);
      }
    },

    updateItem(item, index) {
      const value = this.getValue(item, index);
      item.isActive = this.toggleMethod(value);
    },

    // https://github.com/vuetifyjs/vuetify/issues/5352
    updateItemsState() {
      this.$nextTick(() => {
        if (this.mandatory && !this.selectedItems.length) {
          return this.updateMandatory();
        } // TODO: Make this smarter so it
        // doesn't have to iterate every
        // child in an update


        this.items.forEach(this.updateItem);
      });
    },

    updateInternalValue(value) {
      this.multiple ? this.updateMultiple(value) : this.updateSingle(value);
    },

    updateMandatory(last) {
      if (!this.items.length) return;
      const items = this.items.slice();
      if (last) items.reverse();
      const item = items.find(item => !item.disabled); // If no tabs are available
      // aborts mandatory value

      if (!item) return;
      const index = this.items.indexOf(item);
      this.updateInternalValue(this.getValue(item, index));
    },

    updateMultiple(value) {
      const defaultValue = Array.isArray(this.internalValue) ? this.internalValue : [];
      const internalValue = defaultValue.slice();
      const index = internalValue.findIndex(val => val === value);
      if (this.mandatory && // Item already exists
      index > -1 && // value would be reduced below min
      internalValue.length - 1 < 1) return;
      if ( // Max is set
      this.max != null && // Item doesn't exist
      index < 0 && // value would be increased above max
      internalValue.length + 1 > this.max) return;
      index > -1 ? internalValue.splice(index, 1) : internalValue.push(value);
      this.internalValue = internalValue;
    },

    updateSingle(value) {
      const isSame = value === this.internalValue;
      if (this.mandatory && isSame) return;
      this.internalValue = isSame ? undefined : value;
    }

  },

  render(h) {
    return h(this.tag, this.genData(), this.$slots.default);
  }

});
/* unused harmony default export */ var _unused_webpack_default_export = (BaseItemGroup.extend({
  name: 'v-item-group',

  provide() {
    return {
      itemGroup: this
    };
  }

}));

/***/ }),

/***/ 903:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(0);


/* harmony default export */ __webpack_exports__["a"] = (vue__WEBPACK_IMPORTED_MODULE_0___default.a.extend({
  name: 'comparable',
  props: {
    valueComparator: {
      type: Function,
      default: _util_helpers__WEBPACK_IMPORTED_MODULE_1__[/* deepEqual */ "h"]
    }
  }
}));

/***/ }),

/***/ 904:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1);
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_0__);
// Types

/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (vue__WEBPACK_IMPORTED_MODULE_0___default.a.extend({
  name: 'v-list-item-action',
  functional: true,

  render(h, {
    data,
    children = []
  }) {
    data.staticClass = data.staticClass ? `v-list-item__action ${data.staticClass}` : 'v-list-item__action';
    const filteredChild = children.filter(VNode => {
      return VNode.isComment === false && VNode.text !== ' ';
    });
    if (filteredChild.length > 1) data.staticClass += ' v-list-item__action--stack';
    return h('div', data, children);
  }

}));

/***/ }),

/***/ 905:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VDivider_VDivider_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(913);
/* harmony import */ var _src_components_VDivider_VDivider_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VDivider_VDivider_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(7);
// Styles
 // Mixins


/* harmony default export */ __webpack_exports__["a"] = (_mixins_themeable__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].extend({
  name: 'v-divider',
  props: {
    inset: Boolean,
    vertical: Boolean
  },

  render(h) {
    // WAI-ARIA attributes
    let orientation;

    if (!this.$attrs.role || this.$attrs.role === 'separator') {
      orientation = this.vertical ? 'vertical' : 'horizontal';
    }

    return h('hr', {
      class: {
        'v-divider': true,
        'v-divider--inset': this.inset,
        'v-divider--vertical': this.vertical,
        ...this.themeClasses
      },
      attrs: {
        role: 'separator',
        'aria-orientation': orientation,
        ...this.$attrs
      },
      on: this.$listeners
    });
  }

}));

/***/ }),

/***/ 906:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(907);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("73707fd0", content, true)

/***/ }),

/***/ 907:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-item-group{flex:0 1 auto;position:relative;max-width:100%;transition:.3s cubic-bezier(.25,.8,.5,1)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 909:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(910);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("197fcea4", content, true)

/***/ }),

/***/ 910:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-chip:not(.v-chip--outlined).accent,.v-chip:not(.v-chip--outlined).error,.v-chip:not(.v-chip--outlined).info,.v-chip:not(.v-chip--outlined).primary,.v-chip:not(.v-chip--outlined).secondary,.v-chip:not(.v-chip--outlined).success,.v-chip:not(.v-chip--outlined).warning{color:#fff}.theme--light.v-chip{border-color:rgba(0,0,0,.12);color:rgba(0,0,0,.87)}.theme--light.v-chip:not(.v-chip--active){background:#e0e0e0}.theme--light.v-chip:hover:before{opacity:.04}.theme--light.v-chip--active:before,.theme--light.v-chip--active:hover:before,.theme--light.v-chip:focus:before{opacity:.12}.theme--light.v-chip--active:focus:before{opacity:.16}.theme--dark.v-chip{border-color:hsla(0,0%,100%,.12);color:#fff}.theme--dark.v-chip:not(.v-chip--active){background:#555}.theme--dark.v-chip:hover:before{opacity:.08}.theme--dark.v-chip--active:before,.theme--dark.v-chip--active:hover:before,.theme--dark.v-chip:focus:before{opacity:.24}.theme--dark.v-chip--active:focus:before{opacity:.32}.v-chip{align-items:center;cursor:default;display:inline-flex;line-height:20px;max-width:100%;outline:none;overflow:hidden;padding:0 12px;position:relative;text-decoration:none;transition-duration:.28s;transition-property:box-shadow,opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);vertical-align:middle;white-space:nowrap}.v-chip:before{background-color:currentColor;bottom:0;border-radius:inherit;content:\"\";left:0;opacity:0;position:absolute;pointer-events:none;right:0;top:0}.v-chip .v-avatar{height:24px!important;min-width:24px!important;width:24px!important}.v-chip .v-icon{font-size:24px}.v-application--is-ltr .v-chip .v-avatar--left,.v-application--is-ltr .v-chip .v-icon--left{margin-left:-6px;margin-right:6px}.v-application--is-ltr .v-chip .v-avatar--right,.v-application--is-ltr .v-chip .v-icon--right,.v-application--is-rtl .v-chip .v-avatar--left,.v-application--is-rtl .v-chip .v-icon--left{margin-left:6px;margin-right:-6px}.v-application--is-rtl .v-chip .v-avatar--right,.v-application--is-rtl .v-chip .v-icon--right{margin-left:-6px;margin-right:6px}.v-chip:not(.v-chip--no-color) .v-icon{color:inherit}.v-chip .v-chip__close.v-icon{font-size:18px;max-height:18px;max-width:18px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right{margin-right:-4px}.v-application--is-rtl .v-chip .v-chip__close.v-icon.v-icon--right{margin-left:-4px}.v-chip .v-chip__close.v-icon:active,.v-chip .v-chip__close.v-icon:focus,.v-chip .v-chip__close.v-icon:hover{opacity:.72}.v-chip .v-chip__content{align-items:center;display:inline-flex;height:100%;max-width:100%}.v-chip--active .v-icon{color:inherit}.v-chip--link:before{transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-chip--link:focus:before{opacity:.32}.v-chip--clickable{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip--clickable:active{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-chip--disabled{opacity:.4;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip__filter{max-width:24px}.v-chip__filter.v-icon{color:inherit}.v-chip__filter.expand-x-transition-enter,.v-chip__filter.expand-x-transition-leave-active{margin:0}.v-chip--pill .v-chip__filter{margin-right:0 16px 0 0}.v-chip--pill .v-avatar{height:32px!important;width:32px!important}.v-application--is-ltr .v-chip--pill .v-avatar--left{margin-left:-12px}.v-application--is-ltr .v-chip--pill .v-avatar--right,.v-application--is-rtl .v-chip--pill .v-avatar--left{margin-right:-12px}.v-application--is-rtl .v-chip--pill .v-avatar--right{margin-left:-12px}.v-chip--label{border-radius:4px!important}.v-chip.v-chip--outlined{border-width:thin;border-style:solid}.v-chip.v-chip--outlined.v-chip--active:before{opacity:.08}.v-chip.v-chip--outlined .v-icon{color:inherit}.v-chip.v-chip--outlined.v-chip.v-chip{background-color:transparent!important}.v-chip.v-chip--selected{background:transparent}.v-chip.v-chip--selected:after{opacity:.28}.v-chip.v-size--x-small{border-radius:8px;font-size:10px;height:16px}.v-chip.v-size--small{border-radius:12px;font-size:12px;height:24px}.v-chip.v-size--default{border-radius:16px;font-size:14px;height:32px}.v-chip.v-size--large{border-radius:27px;font-size:16px;height:54px}.v-chip.v-size--x-large{border-radius:33px;font-size:18px;height:66px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 911:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _VChip__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(901);


/* harmony default export */ __webpack_exports__["a"] = (_VChip__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]);

/***/ }),

/***/ 913:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(914);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("7132a15d", content, true)

/***/ }),

/***/ 914:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-divider{border-color:rgba(0,0,0,.12)}.theme--dark.v-divider{border-color:hsla(0,0%,100%,.12)}.v-divider{display:block;flex:1 1 0px;max-width:100%;height:0;max-height:0;border:solid;border-width:thin 0 0;transition:inherit}.v-divider--inset:not(.v-divider--vertical){max-width:calc(100% - 72px)}.v-application--is-ltr .v-divider--inset:not(.v-divider--vertical){margin-left:72px}.v-application--is-rtl .v-divider--inset:not(.v-divider--vertical){margin-right:72px}.v-divider--vertical{align-self:stretch;border:solid;border-width:0 thin 0 0;display:inline-flex;height:inherit;min-height:100%;max-height:100%;max-width:0;width:0;vertical-align:text-bottom;margin:0 -1px}.v-divider--vertical.v-divider--inset{margin-top:8px;min-height:0;max-height:calc(100% - 16px)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 915:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(938);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("a98bb618", content, true, context)
};

/***/ }),

/***/ 917:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(918);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("5e8d0e9e", content, true)

/***/ }),

/***/ 918:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-list-group .v-list-group__header .v-list-item__icon.v-list-group__header__append-icon{align-self:center;margin:0;min-width:48px;justify-content:flex-end}.v-list-group--sub-group{align-items:center;display:flex;flex-wrap:wrap}.v-list-group__header.v-list-item--active:not(:hover):not(:focus):before{opacity:0}.v-list-group__items{flex:1 1 auto}.v-list-group__items .v-list-group__items,.v-list-group__items .v-list-item{overflow:hidden}.v-list-group--active>.v-list-group__header.v-list-group__header--sub-group>.v-list-group__header__prepend-icon .v-icon,.v-list-group--active>.v-list-group__header>.v-list-group__header__append-icon .v-icon{transform:rotate(-180deg)}.v-list-group--active>.v-list-group__header .v-list-group__header__prepend-icon .v-icon,.v-list-group--active>.v-list-group__header .v-list-item,.v-list-group--active>.v-list-group__header .v-list-item__content{color:inherit}.v-application--is-ltr .v-list-group--sub-group .v-list-item__action:first-child,.v-application--is-ltr .v-list-group--sub-group .v-list-item__avatar:first-child,.v-application--is-ltr .v-list-group--sub-group .v-list-item__icon:first-child{margin-right:16px}.v-application--is-rtl .v-list-group--sub-group .v-list-item__action:first-child,.v-application--is-rtl .v-list-group--sub-group .v-list-item__avatar:first-child,.v-application--is-rtl .v-list-group--sub-group .v-list-item__icon:first-child{margin-left:16px}.v-application--is-ltr .v-list-group--sub-group .v-list-group__header{padding-left:32px}.v-application--is-rtl .v-list-group--sub-group .v-list-group__header{padding-right:32px}.v-application--is-ltr .v-list-group--sub-group .v-list-group__items .v-list-item{padding-left:40px}.v-application--is-rtl .v-list-group--sub-group .v-list-group__items .v-list-item{padding-right:40px}.v-list-group--sub-group.v-list-group--active .v-list-item__icon.v-list-group__header__prepend-icon .v-icon{transform:rotate(-180deg)}.v-application--is-ltr .v-list-group--no-action>.v-list-group__items>.v-list-item{padding-left:72px}.v-application--is-rtl .v-list-group--no-action>.v-list-group__items>.v-list-item{padding-right:72px}.v-application--is-ltr .v-list-group--no-action.v-list-group--sub-group>.v-list-group__items>.v-list-item{padding-left:88px}.v-application--is-rtl .v-list-group--no-action.v-list-group--sub-group>.v-list-group__items>.v-list-item{padding-right:88px}.v-application--is-ltr .v-list--dense .v-list-group--sub-group .v-list-group__header{padding-left:24px}.v-application--is-rtl .v-list--dense .v-list-group--sub-group .v-list-group__header{padding-right:24px}.v-application--is-ltr .v-list--dense.v-list--nav .v-list-group--no-action>.v-list-group__items>.v-list-item{padding-left:64px}.v-application--is-rtl .v-list--dense.v-list--nav .v-list-group--no-action>.v-list-group__items>.v-list-item{padding-right:64px}.v-application--is-ltr .v-list--dense.v-list--nav .v-list-group--no-action.v-list-group--sub-group>.v-list-group__items>.v-list-item{padding-left:80px}.v-application--is-rtl .v-list--dense.v-list--nav .v-list-group--no-action.v-list-group--sub-group>.v-list-group__items>.v-list-item{padding-right:80px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 919:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(920);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("516f87f8", content, true)

/***/ }),

/***/ 920:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-list-item-group .v-list-item--active{color:inherit}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 921:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _VDivider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(905);


/* harmony default export */ __webpack_exports__["a"] = (_VDivider__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]);

/***/ }),

/***/ 922:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(923);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("3f1da7f4", content, true)

/***/ }),

/***/ 923:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-select .v-select__selections{color:rgba(0,0,0,.87)}.theme--light.v-select.v-input--is-disabled .v-select__selections,.theme--light.v-select .v-select__selection--disabled{color:rgba(0,0,0,.38)}.theme--dark.v-select .v-select__selections,.theme--light.v-select.v-text-field--solo-inverted.v-input--is-focused .v-select__selections{color:#fff}.theme--dark.v-select.v-input--is-disabled .v-select__selections,.theme--dark.v-select .v-select__selection--disabled{color:hsla(0,0%,100%,.5)}.theme--dark.v-select.v-text-field--solo-inverted.v-input--is-focused .v-select__selections{color:rgba(0,0,0,.87)}.v-select{position:relative}.v-select:not(.v-select--is-multi).v-text-field--single-line .v-select__selections{flex-wrap:nowrap}.v-select>.v-input__control>.v-input__slot{cursor:pointer}.v-select .v-chip{flex:0 1 auto;margin:4px}.v-select .v-chip--selected:after{opacity:.22}.v-select .fade-transition-leave-active{position:absolute;left:0}.v-select.v-input--is-dirty ::-moz-placeholder{color:transparent!important}.v-select.v-input--is-dirty :-ms-input-placeholder{color:transparent!important}.v-select.v-input--is-dirty ::placeholder{color:transparent!important}.v-select:not(.v-input--is-dirty):not(.v-input--is-focused) .v-text-field__prefix{line-height:20px;top:7px;transition:.3s cubic-bezier(.25,.8,.5,1)}.v-select.v-text-field--enclosed:not(.v-text-field--single-line):not(.v-text-field--outlined) .v-select__selections{padding-top:20px}.v-select.v-text-field--outlined:not(.v-text-field--single-line) .v-select__selections{padding:8px 0}.v-select.v-text-field--outlined:not(.v-text-field--single-line).v-input--dense .v-select__selections{padding:4px 0}.v-select.v-text-field input{flex:1 1;margin-top:0;min-width:0;pointer-events:none;position:relative}.v-select.v-select--is-menu-active .v-input__icon--append .v-icon{transform:rotate(180deg)}.v-select.v-select--chips input{margin:0}.v-select.v-select--chips .v-select__selections{min-height:42px}.v-select.v-select--chips.v-input--dense .v-select__selections{min-height:40px}.v-select.v-select--chips .v-chip--select.v-chip--active:before{opacity:.2}.v-select.v-select--chips.v-select--chips--small .v-select__selections{min-height:26px}.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--box .v-select__selections,.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--enclosed .v-select__selections{min-height:68px}.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--box.v-input--dense .v-select__selections,.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--enclosed.v-input--dense .v-select__selections{min-height:40px}.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--box.v-select--chips--small .v-select__selections,.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--enclosed.v-select--chips--small .v-select__selections{min-height:26px}.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--box.v-select--chips--small.v-input--dense .v-select__selections,.v-select.v-select--chips:not(.v-text-field--single-line).v-text-field--enclosed.v-select--chips--small.v-input--dense .v-select__selections{min-height:38px}.v-select.v-text-field--reverse .v-select__selections,.v-select.v-text-field--reverse .v-select__slot{flex-direction:row-reverse}.v-select__selections{align-items:center;display:flex;flex:1 1;flex-wrap:wrap;line-height:18px;max-width:100%;min-width:0}.v-select__selection{max-width:90%}.v-select__selection--comma{margin:7px 4px 7px 0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.v-select.v-input--dense .v-select__selection--comma{margin:5px 4px 3px 0}.v-select.v-input--dense .v-chip{margin:0 4px}.v-select__slot{position:relative;align-items:center;display:flex;max-width:100%;min-width:0;width:100%}.v-select:not(.v-text-field--single-line):not(.v-text-field--outlined) .v-select__slot>input{align-self:flex-end}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 924:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(925);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("5c37caa6", content, true)

/***/ }),

/***/ 925:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-simple-checkbox{align-self:center;line-height:normal;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-simple-checkbox .v-icon{cursor:pointer}.v-simple-checkbox--disabled{cursor:default}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 926:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(927);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("e8b41e5e", content, true)

/***/ }),

/***/ 927:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-subheader{color:rgba(0,0,0,.6)}.theme--dark.v-subheader{color:hsla(0,0%,100%,.7)}.v-subheader{align-items:center;display:flex;height:48px;font-size:14px;font-weight:400;padding:0 16px}.v-subheader--inset{margin-left:56px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 929:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/UserSettingTemplate.vue?vue&type=template&id=6326778e&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-form',{ref:"form",attrs:{"value":_vm.formValid},on:{"validate":_vm.validate,"submit":function($event){$event.preventDefault();return _vm.submit.apply(null, arguments)},"input":function($event){_vm.formValid = $event}}},[_c('div',{staticClass:"user-settings-panel"},[_c('div',{staticClass:"panel"},[(_vm.$vuetify.breakpoint.smAndUp)?_c('div',{staticClass:"panel-head d-none d-sm-block"},[_c('div',{staticClass:"panel-head-title font-weight-medium mb-2 mb-sm-3 mb-md-5"},[_vm._v("\n          "+_vm._s(_vm.title)+"\n        ")])]):_vm._e(),_vm._v(" "),_c('div',{staticClass:"panel-body"},[_vm._t("default")],2),_vm._v(" "),(!_vm.hideFooter)?_c('div',{staticClass:"panel-footer d-flex justify-center justify-sm-end"},[_c('v-btn',{staticClass:"font-weight-medium",attrs:{"color":"primary","type":"submit","disabled":!_vm.valid}},[_c('svg',{staticClass:"mr-1",attrs:{"width":"18","height":"18","viewBox":"0 0 18 18"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#save-icon")}})]),_vm._v("\n          "+_vm._s(_vm.$t('save_changes'))+"\n        ")])],1):_vm._e()])])])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-settings/UserSettingTemplate.vue?vue&type=template&id=6326778e&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-settings/UserSettingTemplate.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var UserSettingTemplatevue_type_script_lang_js_ = ({
  name: 'UserSettingTemplate',
  props: {
    title: {
      type: String,
      required: true
    },
    hideFooter: {
      type: Boolean,
      default: false
    },
    customValid: {
      type: Boolean,
      default: true
    },
    submitFunc: {
      type: Function,
      default: () => {}
    }
  },

  data() {
    return {
      formValid: true
    };
  },

  computed: {
    valid() {
      return this.formValid && this.customValid;
    }

  },

  mounted() {
    this.validate();
  },

  methods: {
    validate() {
      this.$refs.form.validate();
    },

    submit() {
      if (!this.valid) return;
      this.submitFunc();
    }

  }
});
// CONCATENATED MODULE: ./components/user-settings/UserSettingTemplate.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_settings_UserSettingTemplatevue_type_script_lang_js_ = (UserSettingTemplatevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(896);

// CONCATENATED MODULE: ./components/user-settings/UserSettingTemplate.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(988)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_settings_UserSettingTemplatevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "ed2bb580"
  
)

/* harmony default export */ var UserSettingTemplate = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */



installComponents_default()(component, {VBtn: VBtn["a" /* default */],VForm: VForm["a" /* default */]})


/***/ }),

/***/ 934:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _directives_ripple__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(22);
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(1);
/* harmony import */ var vue__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(vue__WEBPACK_IMPORTED_MODULE_1__);
// Directives
 // Types


/* harmony default export */ __webpack_exports__["a"] = (vue__WEBPACK_IMPORTED_MODULE_1___default.a.extend({
  name: 'rippleable',
  directives: {
    ripple: _directives_ripple__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]
  },
  props: {
    ripple: {
      type: [Boolean, Object],
      default: true
    }
  },
  methods: {
    genRipple(data = {}) {
      if (!this.ripple) return null;
      data.staticClass = 'v-input--selection-controls__ripple';
      data.directives = data.directives || [];
      data.directives.push({
        name: 'ripple',
        value: {
          center: true
        }
      });
      return this.$createElement('div', data);
    }

  }
}));

/***/ }),

/***/ 935:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(957);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("2e2bc7da", content, true)

/***/ }),

/***/ 936:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "b", function() { return prevent; });
/* harmony import */ var _components_VInput__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(20);
/* harmony import */ var _rippleable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(934);
/* harmony import */ var _comparable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(903);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2);
// Components
 // Mixins


 // Utilities


function prevent(e) {
  e.preventDefault();
}
/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(_components_VInput__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"], _rippleable__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], _comparable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"]).extend({
  name: 'selectable',
  model: {
    prop: 'inputValue',
    event: 'change'
  },
  props: {
    id: String,
    inputValue: null,
    falseValue: null,
    trueValue: null,
    multiple: {
      type: Boolean,
      default: null
    },
    label: String
  },

  data() {
    return {
      hasColor: this.inputValue,
      lazyValue: this.inputValue
    };
  },

  computed: {
    computedColor() {
      if (!this.isActive) return undefined;
      if (this.color) return this.color;
      if (this.isDark && !this.appIsDark) return 'white';
      return 'primary';
    },

    isMultiple() {
      return this.multiple === true || this.multiple === null && Array.isArray(this.internalValue);
    },

    isActive() {
      const value = this.value;
      const input = this.internalValue;

      if (this.isMultiple) {
        if (!Array.isArray(input)) return false;
        return input.some(item => this.valueComparator(item, value));
      }

      if (this.trueValue === undefined || this.falseValue === undefined) {
        return value ? this.valueComparator(value, input) : Boolean(input);
      }

      return this.valueComparator(input, this.trueValue);
    },

    isDirty() {
      return this.isActive;
    },

    rippleState() {
      return !this.isDisabled && !this.validationState ? undefined : this.validationState;
    }

  },
  watch: {
    inputValue(val) {
      this.lazyValue = val;
      this.hasColor = val;
    }

  },
  methods: {
    genLabel() {
      const label = _components_VInput__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"].options.methods.genLabel.call(this);
      if (!label) return label;
      label.data.on = {
        // Label shouldn't cause the input to focus
        click: prevent
      };
      return label;
    },

    genInput(type, attrs) {
      return this.$createElement('input', {
        attrs: Object.assign({
          'aria-checked': this.isActive.toString(),
          disabled: this.isDisabled,
          id: this.computedId,
          role: type,
          type
        }, attrs),
        domProps: {
          value: this.value,
          checked: this.isActive
        },
        on: {
          blur: this.onBlur,
          change: this.onChange,
          focus: this.onFocus,
          keydown: this.onKeydown,
          click: prevent
        },
        ref: 'input'
      });
    },

    onBlur() {
      this.isFocused = false;
    },

    onClick(e) {
      this.onChange();
      this.$emit('click', e);
    },

    onChange() {
      if (!this.isInteractive) return;
      const value = this.value;
      let input = this.internalValue;

      if (this.isMultiple) {
        if (!Array.isArray(input)) {
          input = [];
        }

        const length = input.length;
        input = input.filter(item => !this.valueComparator(item, value));

        if (input.length === length) {
          input.push(value);
        }
      } else if (this.trueValue !== undefined && this.falseValue !== undefined) {
        input = this.valueComparator(input, this.trueValue) ? this.falseValue : this.trueValue;
      } else if (value) {
        input = this.valueComparator(input, value) ? null : value;
      } else {
        input = !input;
      }

      this.validate(true, input);
      this.internalValue = input;
      this.hasColor = input;
    },

    onFocus() {
      this.isFocused = true;
    },

    /** @abstract */
    onKeydown(e) {}

  }
}));

/***/ }),

/***/ 937:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(915);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 938:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".text-editor{position:relative}.text-editor-buttons{position:absolute;right:18px;top:8px;z-index:2}.text-editor-buttons button{display:inline-flex;justify-content:center;align-items:center;width:24px;height:24px;margin-left:8px;border-radius:2px;border:1px solid transparent}.text-editor-buttons button.is-active{background-color:var(--v-greyBg-base);border-color:var(--v-greyLight-base)}.text-editor .ProseMirror{min-height:280px;margin-bottom:4px;padding:40px 12px 12px;border:1px solid #bebebe;font-size:13px;border-radius:16px;line-height:1.23}.text-editor .ProseMirror>*{position:relative}.text-editor .ProseMirror p{margin-bottom:0}.text-editor .ProseMirror ul{padding-left:28px}.text-editor .ProseMirror ul>li p{margin-bottom:0}.text-editor .ProseMirror strong{font-weight:700!important}.text-editor .ProseMirror.focus-visible,.text-editor .ProseMirror:focus,.text-editor .ProseMirror:focus-visible{outline:none!important}.text-editor .ProseMirror-focused:before{content:\"\";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:16px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}.text-editor .v-text-field__details{padding:0 14px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 941:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXPORTS
__webpack_require__.d(__webpack_exports__, "b", function() { return /* binding */ defaultMenuProps; });

// EXTERNAL MODULE: external "core-js/modules/esnext.array.last-item.js"
var esnext_array_last_item_js_ = __webpack_require__(835);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.delete-all.js"
var esnext_map_delete_all_js_ = __webpack_require__(71);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.every.js"
var esnext_map_every_js_ = __webpack_require__(72);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.filter.js"
var esnext_map_filter_js_ = __webpack_require__(73);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.find.js"
var esnext_map_find_js_ = __webpack_require__(74);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.find-key.js"
var esnext_map_find_key_js_ = __webpack_require__(75);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.includes.js"
var esnext_map_includes_js_ = __webpack_require__(76);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.key-of.js"
var esnext_map_key_of_js_ = __webpack_require__(77);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.map-keys.js"
var esnext_map_map_keys_js_ = __webpack_require__(78);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.map-values.js"
var esnext_map_map_values_js_ = __webpack_require__(79);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.merge.js"
var esnext_map_merge_js_ = __webpack_require__(80);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.reduce.js"
var esnext_map_reduce_js_ = __webpack_require__(81);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.some.js"
var esnext_map_some_js_ = __webpack_require__(82);

// EXTERNAL MODULE: external "core-js/modules/esnext.map.update.js"
var esnext_map_update_js_ = __webpack_require__(83);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VTextField/VTextField.sass
var VTextField = __webpack_require__(512);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VSelect/VSelect.sass
var VSelect = __webpack_require__(922);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VChip/index.js
var VChip = __webpack_require__(911);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VMenu/index.js
var VMenu = __webpack_require__(832);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VCheckbox/VSimpleCheckbox.sass
var VSimpleCheckbox = __webpack_require__(924);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/ripple/index.js
var directives_ripple = __webpack_require__(22);

// EXTERNAL MODULE: external "vue"
var external_vue_ = __webpack_require__(1);
var external_vue_default = /*#__PURE__*/__webpack_require__.n(external_vue_);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(87);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/colorable/index.js
var colorable = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/themeable/index.js
var themeable = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/mergeData.js
var mergeData = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/helpers.js
var helpers = __webpack_require__(0);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VCheckbox/VSimpleCheckbox.js



 // Mixins


 // Utilities



/* harmony default export */ var VCheckbox_VSimpleCheckbox = (external_vue_default.a.extend({
  name: 'v-simple-checkbox',
  functional: true,
  directives: {
    ripple: directives_ripple["a" /* default */]
  },
  props: { ...colorable["a" /* default */].options.props,
    ...themeable["a" /* default */].options.props,
    disabled: Boolean,
    ripple: {
      type: Boolean,
      default: true
    },
    value: Boolean,
    indeterminate: Boolean,
    indeterminateIcon: {
      type: String,
      default: '$checkboxIndeterminate'
    },
    onIcon: {
      type: String,
      default: '$checkboxOn'
    },
    offIcon: {
      type: String,
      default: '$checkboxOff'
    }
  },

  render(h, {
    props,
    data,
    listeners
  }) {
    const children = [];
    let icon = props.offIcon;
    if (props.indeterminate) icon = props.indeterminateIcon;else if (props.value) icon = props.onIcon;
    children.push(h(VIcon["a" /* default */], colorable["a" /* default */].options.methods.setTextColor(props.value && props.color, {
      props: {
        disabled: props.disabled,
        dark: props.dark,
        light: props.light
      }
    }), icon));

    if (props.ripple && !props.disabled) {
      const ripple = h('div', colorable["a" /* default */].options.methods.setTextColor(props.color, {
        staticClass: 'v-input--selection-controls__ripple',
        directives: [{
          name: 'ripple',
          value: {
            center: true
          }
        }]
      }));
      children.push(ripple);
    }

    return h('div', Object(mergeData["a" /* default */])(data, {
      class: {
        'v-simple-checkbox': true,
        'v-simple-checkbox--disabled': props.disabled
      },
      on: {
        click: e => {
          e.stopPropagation();

          if (data.on && data.on.input && !props.disabled) {
            Object(helpers["y" /* wrapInArray */])(data.on.input).forEach(f => f(!props.value));
          }
        }
      }
    }), [h('div', {
      staticClass: 'v-input--selection-controls__input'
    }, children)]);
  }

}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VDivider/index.js
var VDivider = __webpack_require__(921);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VSubheader/VSubheader.sass
var VSubheader = __webpack_require__(926);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/mixins.js
var mixins = __webpack_require__(2);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VSubheader/VSubheader.js
// Styles
 // Mixins



/* harmony default export */ var VSubheader_VSubheader = (Object(mixins["a" /* default */])(themeable["a" /* default */]
/* @vue/component */
).extend({
  name: 'v-subheader',
  props: {
    inset: Boolean
  },

  render(h) {
    return h('div', {
      staticClass: 'v-subheader',
      class: {
        'v-subheader--inset': this.inset,
        ...this.themeClasses
      },
      attrs: this.$attrs,
      on: this.$listeners
    }, this.$slots.default);
  }

}));
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VSubheader/index.js


/* harmony default export */ var components_VSubheader = (VSubheader_VSubheader);
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItem.js
var VListItem = __webpack_require__(828);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItemAction.js
var VListItemAction = __webpack_require__(904);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/index.js + 4 modules
var VList = __webpack_require__(499);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VList.js
var VList_VList = __webpack_require__(831);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VSelect/VSelectList.js
// Components



 // Directives

 // Mixins


 // Helpers

 // Types


/* @vue/component */

/* harmony default export */ var VSelectList = (Object(mixins["a" /* default */])(colorable["a" /* default */], themeable["a" /* default */]).extend({
  name: 'v-select-list',
  // https://github.com/vuejs/vue/issues/6872
  directives: {
    ripple: directives_ripple["a" /* default */]
  },
  props: {
    action: Boolean,
    dense: Boolean,
    hideSelected: Boolean,
    items: {
      type: Array,
      default: () => []
    },
    itemDisabled: {
      type: [String, Array, Function],
      default: 'disabled'
    },
    itemText: {
      type: [String, Array, Function],
      default: 'text'
    },
    itemValue: {
      type: [String, Array, Function],
      default: 'value'
    },
    noDataText: String,
    noFilter: Boolean,
    searchInput: null,
    selectedItems: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    parsedItems() {
      return this.selectedItems.map(item => this.getValue(item));
    },

    tileActiveClass() {
      return Object.keys(this.setTextColor(this.color).class || {}).join(' ');
    },

    staticNoDataTile() {
      const tile = {
        attrs: {
          role: undefined
        },
        on: {
          mousedown: e => e.preventDefault()
        }
      };
      return this.$createElement(VListItem["a" /* default */], tile, [this.genTileContent(this.noDataText)]);
    }

  },
  methods: {
    genAction(item, inputValue) {
      return this.$createElement(VListItemAction["a" /* default */], [this.$createElement(VCheckbox_VSimpleCheckbox, {
        props: {
          color: this.color,
          value: inputValue,
          ripple: false
        },
        on: {
          input: () => this.$emit('select', item)
        }
      })]);
    },

    genDivider(props) {
      return this.$createElement(VDivider["a" /* default */], {
        props
      });
    },

    genFilteredText(text) {
      text = text || '';
      if (!this.searchInput || this.noFilter) return Object(helpers["i" /* escapeHTML */])(text);
      const {
        start,
        middle,
        end
      } = this.getMaskedCharacters(text);
      return `${Object(helpers["i" /* escapeHTML */])(start)}${this.genHighlight(middle)}${Object(helpers["i" /* escapeHTML */])(end)}`;
    },

    genHeader(props) {
      return this.$createElement(components_VSubheader, {
        props
      }, props.header);
    },

    genHighlight(text) {
      return `<span class="v-list-item__mask">${Object(helpers["i" /* escapeHTML */])(text)}</span>`;
    },

    getMaskedCharacters(text) {
      const searchInput = (this.searchInput || '').toString().toLocaleLowerCase();
      const index = text.toLocaleLowerCase().indexOf(searchInput);
      if (index < 0) return {
        start: text,
        middle: '',
        end: ''
      };
      const start = text.slice(0, index);
      const middle = text.slice(index, index + searchInput.length);
      const end = text.slice(index + searchInput.length);
      return {
        start,
        middle,
        end
      };
    },

    genTile({
      item,
      index,
      disabled = null,
      value = false
    }) {
      if (!value) value = this.hasItem(item);

      if (item === Object(item)) {
        disabled = disabled !== null ? disabled : this.getDisabled(item);
      }

      const tile = {
        attrs: {
          // Default behavior in list does not
          // contain aria-selected by default
          'aria-selected': String(value),
          id: `list-item-${this._uid}-${index}`,
          role: 'option'
        },
        on: {
          mousedown: e => {
            // Prevent onBlur from being called
            e.preventDefault();
          },
          click: () => disabled || this.$emit('select', item)
        },
        props: {
          activeClass: this.tileActiveClass,
          disabled,
          ripple: true,
          inputValue: value
        }
      };

      if (!this.$scopedSlots.item) {
        return this.$createElement(VListItem["a" /* default */], tile, [this.action && !this.hideSelected && this.items.length > 0 ? this.genAction(item, value) : null, this.genTileContent(item, index)]);
      }

      const parent = this;
      const scopedSlot = this.$scopedSlots.item({
        parent,
        item,
        attrs: { ...tile.attrs,
          ...tile.props
        },
        on: tile.on
      });
      return this.needsTile(scopedSlot) ? this.$createElement(VListItem["a" /* default */], tile, scopedSlot) : scopedSlot;
    },

    genTileContent(item, index = 0) {
      const innerHTML = this.genFilteredText(this.getText(item));
      return this.$createElement(VList["a" /* VListItemContent */], [this.$createElement(VList["c" /* VListItemTitle */], {
        domProps: {
          innerHTML
        }
      })]);
    },

    hasItem(item) {
      return this.parsedItems.indexOf(this.getValue(item)) > -1;
    },

    needsTile(slot) {
      return slot.length !== 1 || slot[0].componentOptions == null || slot[0].componentOptions.Ctor.options.name !== 'v-list-item';
    },

    getDisabled(item) {
      return Boolean(Object(helpers["m" /* getPropertyFromItem */])(item, this.itemDisabled, false));
    },

    getText(item) {
      return String(Object(helpers["m" /* getPropertyFromItem */])(item, this.itemText, item));
    },

    getValue(item) {
      return Object(helpers["m" /* getPropertyFromItem */])(item, this.itemValue, this.getText(item));
    }

  },

  render() {
    const children = [];
    const itemsLength = this.items.length;

    for (let index = 0; index < itemsLength; index++) {
      const item = this.items[index];
      if (this.hideSelected && this.hasItem(item)) continue;
      if (item == null) children.push(this.genTile({
        item,
        index
      }));else if (item.header) children.push(this.genHeader(item));else if (item.divider) children.push(this.genDivider(item));else children.push(this.genTile({
        item,
        index
      }));
    }

    children.length || children.push(this.$slots['no-data'] || this.staticNoDataTile);
    this.$slots['prepend-item'] && children.unshift(this.$slots['prepend-item']);
    this.$slots['append-item'] && children.push(this.$slots['append-item']);
    return this.$createElement(VList_VList["a" /* default */], {
      staticClass: 'v-select-list',
      class: this.themeClasses,
      attrs: {
        role: 'listbox',
        tabindex: -1
      },
      props: {
        dense: this.dense
      }
    }, children);
  }

}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VInput/index.js + 3 modules
var VInput = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 4 modules
var VTextField_VTextField = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/comparable/index.js
var comparable = __webpack_require__(903);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/dependent/index.js
var dependent = __webpack_require__(30);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/mixins/filterable/index.js

/* @vue/component */

/* harmony default export */ var filterable = (external_vue_default.a.extend({
  name: 'filterable',
  props: {
    noDataText: {
      type: String,
      default: '$vuetify.noDataText'
    }
  }
}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/click-outside/index.js
var click_outside = __webpack_require__(31);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/console.js
var console = __webpack_require__(3);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VSelect/VSelect.js














// Styles

 // Components



 // Extensions


 // Mixins



 // Directives

 // Utilities



 // Types


const defaultMenuProps = {
  closeOnClick: false,
  closeOnContentClick: false,
  disableKeys: true,
  openOnClick: false,
  maxHeight: 304
}; // Types

const baseMixins = Object(mixins["a" /* default */])(VTextField_VTextField["a" /* default */], comparable["a" /* default */], dependent["a" /* default */], filterable);
/* @vue/component */

/* harmony default export */ var VSelect_VSelect = __webpack_exports__["a"] = (baseMixins.extend().extend({
  name: 'v-select',
  directives: {
    ClickOutside: click_outside["a" /* default */]
  },
  props: {
    appendIcon: {
      type: String,
      default: '$dropdown'
    },
    attach: {
      type: null,
      default: false
    },
    cacheItems: Boolean,
    chips: Boolean,
    clearable: Boolean,
    deletableChips: Boolean,
    disableLookup: Boolean,
    eager: Boolean,
    hideSelected: Boolean,
    items: {
      type: Array,
      default: () => []
    },
    itemColor: {
      type: String,
      default: 'primary'
    },
    itemDisabled: {
      type: [String, Array, Function],
      default: 'disabled'
    },
    itemText: {
      type: [String, Array, Function],
      default: 'text'
    },
    itemValue: {
      type: [String, Array, Function],
      default: 'value'
    },
    menuProps: {
      type: [String, Array, Object],
      default: () => defaultMenuProps
    },
    multiple: Boolean,
    openOnClear: Boolean,
    returnObject: Boolean,
    smallChips: Boolean
  },

  data() {
    return {
      cachedItems: this.cacheItems ? this.items : [],
      menuIsBooted: false,
      isMenuActive: false,
      lastItem: 20,
      // As long as a value is defined, show it
      // Otherwise, check if multiple
      // to determine which default to provide
      lazyValue: this.value !== undefined ? this.value : this.multiple ? [] : undefined,
      selectedIndex: -1,
      selectedItems: [],
      keyboardLookupPrefix: '',
      keyboardLookupLastTime: 0
    };
  },

  computed: {
    /* All items that the select has */
    allItems() {
      return this.filterDuplicates(this.cachedItems.concat(this.items));
    },

    classes() {
      return { ...VTextField_VTextField["a" /* default */].options.computed.classes.call(this),
        'v-select': true,
        'v-select--chips': this.hasChips,
        'v-select--chips--small': this.smallChips,
        'v-select--is-menu-active': this.isMenuActive,
        'v-select--is-multi': this.multiple
      };
    },

    /* Used by other components to overwrite */
    computedItems() {
      return this.allItems;
    },

    computedOwns() {
      return `list-${this._uid}`;
    },

    computedCounterValue() {
      const value = this.multiple ? this.selectedItems : (this.getText(this.selectedItems[0]) || '').toString();

      if (typeof this.counterValue === 'function') {
        return this.counterValue(value);
      }

      return value.length;
    },

    directives() {
      return this.isFocused ? [{
        name: 'click-outside',
        value: {
          handler: this.blur,
          closeConditional: this.closeConditional,
          include: () => this.getOpenDependentElements()
        }
      }] : undefined;
    },

    dynamicHeight() {
      return 'auto';
    },

    hasChips() {
      return this.chips || this.smallChips;
    },

    hasSlot() {
      return Boolean(this.hasChips || this.$scopedSlots.selection);
    },

    isDirty() {
      return this.selectedItems.length > 0;
    },

    listData() {
      const scopeId = this.$vnode && this.$vnode.context.$options._scopeId;
      const attrs = scopeId ? {
        [scopeId]: true
      } : {};
      return {
        attrs: { ...attrs,
          id: this.computedOwns
        },
        props: {
          action: this.multiple,
          color: this.itemColor,
          dense: this.dense,
          hideSelected: this.hideSelected,
          items: this.virtualizedItems,
          itemDisabled: this.itemDisabled,
          itemText: this.itemText,
          itemValue: this.itemValue,
          noDataText: this.$vuetify.lang.t(this.noDataText),
          selectedItems: this.selectedItems
        },
        on: {
          select: this.selectItem
        },
        scopedSlots: {
          item: this.$scopedSlots.item
        }
      };
    },

    staticList() {
      if (this.$slots['no-data'] || this.$slots['prepend-item'] || this.$slots['append-item']) {
        Object(console["b" /* consoleError */])('assert: staticList should not be called if slots are used');
      }

      return this.$createElement(VSelectList, this.listData);
    },

    virtualizedItems() {
      return this.$_menuProps.auto ? this.computedItems : this.computedItems.slice(0, this.lastItem);
    },

    menuCanShow: () => true,

    $_menuProps() {
      let normalisedProps = typeof this.menuProps === 'string' ? this.menuProps.split(',') : this.menuProps;

      if (Array.isArray(normalisedProps)) {
        normalisedProps = normalisedProps.reduce((acc, p) => {
          acc[p.trim()] = true;
          return acc;
        }, {});
      }

      return { ...defaultMenuProps,
        eager: this.eager,
        value: this.menuCanShow && this.isMenuActive,
        nudgeBottom: normalisedProps.offsetY ? 1 : 0,
        ...normalisedProps
      };
    }

  },
  watch: {
    internalValue(val) {
      this.initialValue = val;
      this.setSelectedItems();
    },

    isMenuActive(val) {
      window.setTimeout(() => this.onMenuActiveChange(val));
    },

    items: {
      immediate: true,

      handler(val) {
        if (this.cacheItems) {
          // Breaks vue-test-utils if
          // this isn't calculated
          // on the next tick
          this.$nextTick(() => {
            this.cachedItems = this.filterDuplicates(this.cachedItems.concat(val));
          });
        }

        this.setSelectedItems();
      }

    }
  },
  methods: {
    /** @public */
    blur(e) {
      VTextField_VTextField["a" /* default */].options.methods.blur.call(this, e);
      this.isMenuActive = false;
      this.isFocused = false;
      this.selectedIndex = -1;
      this.setMenuIndex(-1);
    },

    /** @public */
    activateMenu() {
      if (!this.isInteractive || this.isMenuActive) return;
      this.isMenuActive = true;
    },

    clearableCallback() {
      this.setValue(this.multiple ? [] : null);
      this.setMenuIndex(-1);
      this.$nextTick(() => this.$refs.input && this.$refs.input.focus());
      if (this.openOnClear) this.isMenuActive = true;
    },

    closeConditional(e) {
      if (!this.isMenuActive) return true;
      return !this._isDestroyed && ( // Click originates from outside the menu content
      // Multiple selects don't close when an item is clicked
      !this.getContent() || !this.getContent().contains(e.target)) && // Click originates from outside the element
      this.$el && !this.$el.contains(e.target) && e.target !== this.$el;
    },

    filterDuplicates(arr) {
      const uniqueValues = new Map();

      for (let index = 0; index < arr.length; ++index) {
        const item = arr[index]; // Do not deduplicate headers or dividers (#12517)

        if (item.header || item.divider) {
          uniqueValues.set(item, item);
          continue;
        }

        const val = this.getValue(item); // TODO: comparator

        !uniqueValues.has(val) && uniqueValues.set(val, item);
      }

      return Array.from(uniqueValues.values());
    },

    findExistingIndex(item) {
      const itemValue = this.getValue(item);
      return (this.internalValue || []).findIndex(i => this.valueComparator(this.getValue(i), itemValue));
    },

    getContent() {
      return this.$refs.menu && this.$refs.menu.$refs.content;
    },

    genChipSelection(item, index) {
      const isDisabled = this.isDisabled || this.getDisabled(item);
      const isInteractive = !isDisabled && this.isInteractive;
      return this.$createElement(VChip["a" /* default */], {
        staticClass: 'v-chip--select',
        attrs: {
          tabindex: -1
        },
        props: {
          close: this.deletableChips && isInteractive,
          disabled: isDisabled,
          inputValue: index === this.selectedIndex,
          small: this.smallChips
        },
        on: {
          click: e => {
            if (!isInteractive) return;
            e.stopPropagation();
            this.selectedIndex = index;
          },
          'click:close': () => this.onChipInput(item)
        },
        key: JSON.stringify(this.getValue(item))
      }, this.getText(item));
    },

    genCommaSelection(item, index, last) {
      const color = index === this.selectedIndex && this.computedColor;
      const isDisabled = this.isDisabled || this.getDisabled(item);
      return this.$createElement('div', this.setTextColor(color, {
        staticClass: 'v-select__selection v-select__selection--comma',
        class: {
          'v-select__selection--disabled': isDisabled
        },
        key: JSON.stringify(this.getValue(item))
      }), `${this.getText(item)}${last ? '' : ', '}`);
    },

    genDefaultSlot() {
      const selections = this.genSelections();
      const input = this.genInput(); // If the return is an empty array
      // push the input

      if (Array.isArray(selections)) {
        selections.push(input); // Otherwise push it into children
      } else {
        selections.children = selections.children || [];
        selections.children.push(input);
      }

      return [this.genFieldset(), this.$createElement('div', {
        staticClass: 'v-select__slot',
        directives: this.directives
      }, [this.genLabel(), this.prefix ? this.genAffix('prefix') : null, selections, this.suffix ? this.genAffix('suffix') : null, this.genClearIcon(), this.genIconSlot(), this.genHiddenInput()]), this.genMenu(), this.genProgress()];
    },

    genIcon(type, cb, extraData) {
      const icon = VInput["a" /* default */].options.methods.genIcon.call(this, type, cb, extraData);

      if (type === 'append') {
        // Don't allow the dropdown icon to be focused
        icon.children[0].data = Object(mergeData["a" /* default */])(icon.children[0].data, {
          attrs: {
            tabindex: icon.children[0].componentOptions.listeners && '-1',
            'aria-hidden': 'true',
            'aria-label': undefined
          }
        });
      }

      return icon;
    },

    genInput() {
      const input = VTextField_VTextField["a" /* default */].options.methods.genInput.call(this);
      delete input.data.attrs.name;
      input.data = Object(mergeData["a" /* default */])(input.data, {
        domProps: {
          value: null
        },
        attrs: {
          readonly: true,
          type: 'text',
          'aria-readonly': String(this.isReadonly),
          'aria-activedescendant': Object(helpers["l" /* getObjectValueByPath */])(this.$refs.menu, 'activeTile.id'),
          autocomplete: Object(helpers["l" /* getObjectValueByPath */])(input.data, 'attrs.autocomplete', 'off'),
          placeholder: !this.isDirty && (this.isFocused || !this.hasLabel) ? this.placeholder : undefined
        },
        on: {
          keypress: this.onKeyPress
        }
      });
      return input;
    },

    genHiddenInput() {
      return this.$createElement('input', {
        domProps: {
          value: this.lazyValue
        },
        attrs: {
          type: 'hidden',
          name: this.attrs$.name
        }
      });
    },

    genInputSlot() {
      const render = VTextField_VTextField["a" /* default */].options.methods.genInputSlot.call(this);
      render.data.attrs = { ...render.data.attrs,
        role: 'button',
        'aria-haspopup': 'listbox',
        'aria-expanded': String(this.isMenuActive),
        'aria-owns': this.computedOwns
      };
      return render;
    },

    genList() {
      // If there's no slots, we can use a cached VNode to improve performance
      if (this.$slots['no-data'] || this.$slots['prepend-item'] || this.$slots['append-item']) {
        return this.genListWithSlot();
      } else {
        return this.staticList;
      }
    },

    genListWithSlot() {
      const slots = ['prepend-item', 'no-data', 'append-item'].filter(slotName => this.$slots[slotName]).map(slotName => this.$createElement('template', {
        slot: slotName
      }, this.$slots[slotName])); // Requires destructuring due to Vue
      // modifying the `on` property when passed
      // as a referenced object

      return this.$createElement(VSelectList, { ...this.listData
      }, slots);
    },

    genMenu() {
      const props = this.$_menuProps;
      props.activator = this.$refs['input-slot']; // Attach to root el so that
      // menu covers prepend/append icons

      if ( // TODO: make this a computed property or helper or something
      this.attach === '' || // If used as a boolean prop (<v-menu attach>)
      this.attach === true || // If bound to a boolean (<v-menu :attach="true">)
      this.attach === 'attach' // If bound as boolean prop in pug (v-menu(attach))
      ) {
        props.attach = this.$el;
      } else {
        props.attach = this.attach;
      }

      return this.$createElement(VMenu["a" /* default */], {
        attrs: {
          role: undefined
        },
        props,
        on: {
          input: val => {
            this.isMenuActive = val;
            this.isFocused = val;
          },
          scroll: this.onScroll
        },
        ref: 'menu'
      }, [this.genList()]);
    },

    genSelections() {
      let length = this.selectedItems.length;
      const children = new Array(length);
      let genSelection;

      if (this.$scopedSlots.selection) {
        genSelection = this.genSlotSelection;
      } else if (this.hasChips) {
        genSelection = this.genChipSelection;
      } else {
        genSelection = this.genCommaSelection;
      }

      while (length--) {
        children[length] = genSelection(this.selectedItems[length], length, length === children.length - 1);
      }

      return this.$createElement('div', {
        staticClass: 'v-select__selections'
      }, children);
    },

    genSlotSelection(item, index) {
      return this.$scopedSlots.selection({
        attrs: {
          class: 'v-chip--select'
        },
        parent: this,
        item,
        index,
        select: e => {
          e.stopPropagation();
          this.selectedIndex = index;
        },
        selected: index === this.selectedIndex,
        disabled: !this.isInteractive
      });
    },

    getMenuIndex() {
      return this.$refs.menu ? this.$refs.menu.listIndex : -1;
    },

    getDisabled(item) {
      return Object(helpers["m" /* getPropertyFromItem */])(item, this.itemDisabled, false);
    },

    getText(item) {
      return Object(helpers["m" /* getPropertyFromItem */])(item, this.itemText, item);
    },

    getValue(item) {
      return Object(helpers["m" /* getPropertyFromItem */])(item, this.itemValue, this.getText(item));
    },

    onBlur(e) {
      e && this.$emit('blur', e);
    },

    onChipInput(item) {
      if (this.multiple) this.selectItem(item);else this.setValue(null); // If all items have been deleted,
      // open `v-menu`

      if (this.selectedItems.length === 0) {
        this.isMenuActive = true;
      } else {
        this.isMenuActive = false;
      }

      this.selectedIndex = -1;
    },

    onClick(e) {
      if (!this.isInteractive) return;

      if (!this.isAppendInner(e.target)) {
        this.isMenuActive = true;
      }

      if (!this.isFocused) {
        this.isFocused = true;
        this.$emit('focus');
      }

      this.$emit('click', e);
    },

    onEscDown(e) {
      e.preventDefault();

      if (this.isMenuActive) {
        e.stopPropagation();
        this.isMenuActive = false;
      }
    },

    onKeyPress(e) {
      if (this.multiple || !this.isInteractive || this.disableLookup) return;
      const KEYBOARD_LOOKUP_THRESHOLD = 1000; // milliseconds

      const now = performance.now();

      if (now - this.keyboardLookupLastTime > KEYBOARD_LOOKUP_THRESHOLD) {
        this.keyboardLookupPrefix = '';
      }

      this.keyboardLookupPrefix += e.key.toLowerCase();
      this.keyboardLookupLastTime = now;
      const index = this.allItems.findIndex(item => {
        const text = (this.getText(item) || '').toString();
        return text.toLowerCase().startsWith(this.keyboardLookupPrefix);
      });
      const item = this.allItems[index];

      if (index !== -1) {
        this.lastItem = Math.max(this.lastItem, index + 5);
        this.setValue(this.returnObject ? item : this.getValue(item));
        this.$nextTick(() => this.$refs.menu.getTiles());
        setTimeout(() => this.setMenuIndex(index));
      }
    },

    onKeyDown(e) {
      if (this.isReadonly && e.keyCode !== helpers["s" /* keyCodes */].tab) return;
      const keyCode = e.keyCode;
      const menu = this.$refs.menu; // If enter, space, open menu

      if ([helpers["s" /* keyCodes */].enter, helpers["s" /* keyCodes */].space].includes(keyCode)) this.activateMenu();
      this.$emit('keydown', e);
      if (!menu) return; // If menu is active, allow default
      // listIndex change from menu

      if (this.isMenuActive && keyCode !== helpers["s" /* keyCodes */].tab) {
        this.$nextTick(() => {
          menu.changeListIndex(e);
          this.$emit('update:list-index', menu.listIndex);
        });
      } // If menu is not active, up/down/home/<USER>
      // one of 2 things. If multiple, opens the
      // menu, if not, will cycle through all
      // available options


      if (!this.isMenuActive && [helpers["s" /* keyCodes */].up, helpers["s" /* keyCodes */].down, helpers["s" /* keyCodes */].home, helpers["s" /* keyCodes */].end].includes(keyCode)) return this.onUpDown(e); // If escape deactivate the menu

      if (keyCode === helpers["s" /* keyCodes */].esc) return this.onEscDown(e); // If tab - select item or close menu

      if (keyCode === helpers["s" /* keyCodes */].tab) return this.onTabDown(e); // If space preventDefault

      if (keyCode === helpers["s" /* keyCodes */].space) return this.onSpaceDown(e);
    },

    onMenuActiveChange(val) {
      // If menu is closing and mulitple
      // or menuIndex is already set
      // skip menu index recalculation
      if (this.multiple && !val || this.getMenuIndex() > -1) return;
      const menu = this.$refs.menu;
      if (!menu || !this.isDirty) return; // When menu opens, set index of first active item

      for (let i = 0; i < menu.tiles.length; i++) {
        if (menu.tiles[i].getAttribute('aria-selected') === 'true') {
          this.setMenuIndex(i);
          break;
        }
      }
    },

    onMouseUp(e) {
      // eslint-disable-next-line sonarjs/no-collapsible-if
      if (this.hasMouseDown && e.which !== 3 && this.isInteractive) {
        // If append inner is present
        // and the target is itself
        // or inside, toggle menu
        if (this.isAppendInner(e.target)) {
          this.$nextTick(() => this.isMenuActive = !this.isMenuActive);
        }
      }

      VTextField_VTextField["a" /* default */].options.methods.onMouseUp.call(this, e);
    },

    onScroll() {
      if (!this.isMenuActive) {
        requestAnimationFrame(() => this.getContent().scrollTop = 0);
      } else {
        if (this.lastItem > this.computedItems.length) return;
        const showMoreItems = this.getContent().scrollHeight - (this.getContent().scrollTop + this.getContent().clientHeight) < 200;

        if (showMoreItems) {
          this.lastItem += 20;
        }
      }
    },

    onSpaceDown(e) {
      e.preventDefault();
    },

    onTabDown(e) {
      const menu = this.$refs.menu;
      if (!menu) return;
      const activeTile = menu.activeTile; // An item that is selected by
      // menu-index should toggled

      if (!this.multiple && activeTile && this.isMenuActive) {
        e.preventDefault();
        e.stopPropagation();
        activeTile.click();
      } else {
        // If we make it here,
        // the user has no selected indexes
        // and is probably tabbing out
        this.blur(e);
      }
    },

    onUpDown(e) {
      const menu = this.$refs.menu;
      if (!menu) return;
      e.preventDefault(); // Multiple selects do not cycle their value
      // when pressing up or down, instead activate
      // the menu

      if (this.multiple) return this.activateMenu();
      const keyCode = e.keyCode; // Cycle through available values to achieve
      // select native behavior

      menu.isBooted = true;
      window.requestAnimationFrame(() => {
        menu.getTiles();
        if (!menu.hasClickableTiles) return this.activateMenu();

        switch (keyCode) {
          case helpers["s" /* keyCodes */].up:
            menu.prevTile();
            break;

          case helpers["s" /* keyCodes */].down:
            menu.nextTile();
            break;

          case helpers["s" /* keyCodes */].home:
            menu.firstTile();
            break;

          case helpers["s" /* keyCodes */].end:
            menu.lastTile();
            break;
        }

        this.selectItem(this.allItems[this.getMenuIndex()]);
      });
    },

    selectItem(item) {
      if (!this.multiple) {
        this.setValue(this.returnObject ? item : this.getValue(item));
        this.isMenuActive = false;
      } else {
        const internalValue = (this.internalValue || []).slice();
        const i = this.findExistingIndex(item);
        i !== -1 ? internalValue.splice(i, 1) : internalValue.push(item);
        this.setValue(internalValue.map(i => {
          return this.returnObject ? i : this.getValue(i);
        })); // When selecting multiple
        // adjust menu after each
        // selection

        this.$nextTick(() => {
          this.$refs.menu && this.$refs.menu.updateDimensions();
        }); // We only need to reset list index for multiple
        // to keep highlight when an item is toggled
        // on and off

        if (!this.multiple) return;
        const listIndex = this.getMenuIndex();
        this.setMenuIndex(-1); // There is no item to re-highlight
        // when selections are hidden

        if (this.hideSelected) return;
        this.$nextTick(() => this.setMenuIndex(listIndex));
      }
    },

    setMenuIndex(index) {
      this.$refs.menu && (this.$refs.menu.listIndex = index);
    },

    setSelectedItems() {
      const selectedItems = [];
      const values = !this.multiple || !Array.isArray(this.internalValue) ? [this.internalValue] : this.internalValue;

      for (const value of values) {
        const index = this.allItems.findIndex(v => this.valueComparator(this.getValue(v), this.getValue(value)));

        if (index > -1) {
          selectedItems.push(this.allItems[index]);
        }
      }

      this.selectedItems = selectedItems;
    },

    setValue(value) {
      const oldValue = this.internalValue;
      this.internalValue = value;
      value !== oldValue && this.$emit('change', value);
    },

    isAppendInner(target) {
      // return true if append inner is present
      // and the target is itself or inside
      const appendInner = this.$refs['append-inner'];
      return appendInner && (appendInner === target || appendInner.contains(target));
    }

  }
}));

/***/ }),

/***/ 942:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/form/Editor.vue?vue&type=template&id=23b137ee&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"text-editor"},[_vm._ssrNode(((_vm.editor)?("<div class=\"text-editor-buttons\"><button"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bold') }))+"><svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#editor-bold-icon")))+"></use></svg></button> <button"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bulletList') }))+"><svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#editor-list-icon")))+"></use></svg></button></div>"):"<!---->")+" "),_c('editor-content',{attrs:{"editor":_vm.editor}}),_vm._ssrNode(" "+((_vm.counter)?("<div class=\"v-text-field__details\"><div class=\"v-messages theme--light\"><div class=\"v-messages__wrapper\"></div></div> <div"+(_vm._ssrClass(null,[
        'v-counter theme--light',
        { 'error--text': !_vm.isValid && _vm.isDirty } ]))+">"+_vm._ssrEscape("\n      "+_vm._s(_vm.text.length)+" / "+_vm._s(_vm.limit)+"\n    ")+"</div></div>"):"<!---->"))],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/form/Editor.vue?vue&type=template&id=23b137ee&

// EXTERNAL MODULE: external "@tiptap/vue-2"
var vue_2_ = __webpack_require__(854);

// EXTERNAL MODULE: external "@tiptap/starter-kit"
var starter_kit_ = __webpack_require__(855);
var starter_kit_default = /*#__PURE__*/__webpack_require__.n(starter_kit_);

// EXTERNAL MODULE: external "@tiptap/extension-character-count"
var extension_character_count_ = __webpack_require__(856);
var extension_character_count_default = /*#__PURE__*/__webpack_require__.n(extension_character_count_);

// EXTERNAL MODULE: external "@tiptap/extension-link"
var extension_link_ = __webpack_require__(857);
var extension_link_default = /*#__PURE__*/__webpack_require__.n(extension_link_);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/form/Editor.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var Editorvue_type_script_lang_js_ = ({
  name: 'Editor',
  components: {
    EditorContent: vue_2_["EditorContent"]
  },
  props: {
    value: {
      type: String,
      required: true
    },
    counter: {
      type: Boolean,
      default: false
    },
    autoLink: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: null
    }
  },

  data() {
    return {
      editor: null,
      text: '',
      isValid: true,
      editorEl: null,
      keysPressed: {},
      isDirty: false
    };
  },

  watch: {
    value(value) {
      const isSame = this.editor.getHTML() === value;

      if (isSame) {
        return;
      }

      this.editor.commands.setContent(value, false);
    }

  },

  mounted() {
    this.editor = new vue_2_["Editor"]({
      content: this.value,
      extensions: [starter_kit_default.a, extension_character_count_default.a.configure({
        limit: this.limit
      }), extension_link_default.a.configure({
        autolink: true
      })]
    });
    this.editor.on('create', ({
      editor
    }) => {
      this.text = editor.getText();
      this.$nextTick(() => {
        this.editorEl = document.getElementsByClassName('ProseMirror')[0];

        if (this.editorEl) {
          this.editorEl.addEventListener('keydown', this.keydownHandler);
          this.editorEl.addEventListener('keyup', this.keyupHandler);
        }
      });
      this.validation();
    });
    this.editor.on('update', ({
      editor
    }) => {
      this.isDirty = true;
      this.text = editor.getText();
      this.validation();
      this.$emit('update', this.text ? editor.getHTML() : '');
    });
  },

  beforeDestroy() {
    if (this.editorEl) {
      this.editorEl.removeEventListener('keydown', this.keydownHandler);
      this.editorEl.removeEventListener('keyup', this.keyupHandler);
    }

    this.editor.destroy();
  },

  methods: {
    keydownHandler(e) {
      this.keysPressed[e.keyCode] = true;

      if ((e.ctrlKey || this.keysPressed[17] || this.keysPressed[91] || this.keysPressed[93] || this.keysPressed[224]) && this.keysPressed[13]) {
        e.preventDefault();
        this.$emit('submit');
        this.keysPressed = {};
      } else if (e.keyCode === 13 && !e.shiftKey) {
        e.preventDefault();
        this.editor.commands.enter();
      }
    },

    keyupHandler(e) {
      delete this.keysPressed[e.keyCode];
    },

    validation() {
      const strLength = this.text.trim().length;
      this.isValid = !!strLength;

      if (!!strLength && this.limit) {
        this.isValid = strLength <= this.limit;
      }

      this.$emit('validation', this.isValid);
    }

  }
});
// CONCATENATED MODULE: ./components/form/Editor.vue?vue&type=script&lang=js&
 /* harmony default export */ var form_Editorvue_type_script_lang_js_ = (Editorvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/form/Editor.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(937)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  form_Editorvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "0bb70d5d"
  
)

/* harmony default export */ var Editor = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 951:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(989);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("419d3f06", content, true, context)
};

/***/ }),

/***/ 957:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-input--selection-controls.v-input--is-disabled:not(.v-input--indeterminate) .v-icon{color:rgba(0,0,0,.26)!important}.theme--dark.v-input--selection-controls.v-input--is-disabled:not(.v-input--indeterminate) .v-icon{color:hsla(0,0%,100%,.3)!important}.v-input--selection-controls{margin-top:16px;padding-top:4px}.v-input--selection-controls>.v-input__append-outer,.v-input--selection-controls>.v-input__prepend-outer{margin-top:0;margin-bottom:0}.v-input--selection-controls:not(.v-input--hide-details)>.v-input__slot{margin-bottom:12px}.v-input--selection-controls .v-input__slot,.v-input--selection-controls .v-radio{cursor:pointer}.v-input--selection-controls .v-input__slot>.v-label,.v-input--selection-controls .v-radio>.v-label{align-items:center;display:inline-flex;flex:1 1 auto;height:auto}.v-input--selection-controls__input{color:inherit;display:inline-flex;flex:0 0 auto;height:24px;position:relative;transition:.3s cubic-bezier(.25,.8,.5,1);transition-property:transform;width:24px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__input .v-icon{width:100%}.v-application--is-ltr .v-input--selection-controls__input{margin-right:8px}.v-application--is-rtl .v-input--selection-controls__input{margin-left:8px}.v-input--selection-controls__input input[role=checkbox],.v-input--selection-controls__input input[role=radio],.v-input--selection-controls__input input[role=switch]{position:absolute;opacity:0;width:100%;height:100%;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__input+.v-label{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-input--selection-controls__ripple{border-radius:50%;cursor:pointer;height:34px;position:absolute;transition:inherit;width:34px;left:-12px;top:calc(50% - 24px);margin:7px}.v-input--selection-controls__ripple:before{border-radius:inherit;bottom:0;content:\"\";position:absolute;opacity:.2;left:0;right:0;top:0;transform-origin:center center;transform:scale(.2);transition:inherit}.v-input--selection-controls__ripple>.v-ripple__container{transform:scale(1.2)}.v-input--selection-controls.v-input--dense .v-input--selection-controls__ripple{width:28px;height:28px;left:-9px}.v-input--selection-controls.v-input--dense:not(.v-input--switch) .v-input--selection-controls__ripple{top:calc(50% - 21px)}.v-input--selection-controls.v-input{flex:0 1 auto}.v-input--selection-controls.v-input--is-focused .v-input--selection-controls__ripple:before,.v-input--selection-controls .v-radio--is-focused .v-input--selection-controls__ripple:before{background:currentColor;transform:scale(1.2)}.v-input--selection-controls__input:hover .v-input--selection-controls__ripple:before{background:currentColor;transform:scale(1.2);transition:none}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 969:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(970);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("48751daa", content, true)

/***/ }),

/***/ 970:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-expansion-panels .v-expansion-panel{background-color:#fff;color:rgba(0,0,0,.87)}.theme--light.v-expansion-panels .v-expansion-panel--disabled{color:rgba(0,0,0,.38)}.theme--light.v-expansion-panels .v-expansion-panel:not(:first-child):after{border-color:rgba(0,0,0,.12)}.theme--light.v-expansion-panels .v-expansion-panel-header .v-expansion-panel-header__icon .v-icon{color:rgba(0,0,0,.54)}.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:hover:before{opacity:.04}.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:before,.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:hover:before,.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:focus:before{opacity:.12}.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:focus:before{opacity:.16}.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:hover:before{opacity:.04}.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:before,.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:hover:before,.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:focus:before{opacity:.12}.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:focus:before{opacity:.16}.theme--dark.v-expansion-panels .v-expansion-panel{background-color:#1e1e1e;color:#fff}.theme--dark.v-expansion-panels .v-expansion-panel--disabled{color:hsla(0,0%,100%,.5)}.theme--dark.v-expansion-panels .v-expansion-panel:not(:first-child):after{border-color:hsla(0,0%,100%,.12)}.theme--dark.v-expansion-panels .v-expansion-panel-header .v-expansion-panel-header__icon .v-icon{color:#fff}.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:hover:before{opacity:.08}.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:before,.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:hover:before,.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:focus:before{opacity:.24}.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:focus:before{opacity:.32}.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:hover:before{opacity:.08}.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:before,.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:hover:before,.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:focus:before{opacity:.24}.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:focus:before{opacity:.32}.v-expansion-panels{border-radius:8px;display:flex;flex-wrap:wrap;justify-content:center;list-style-type:none;padding:0;width:100%;z-index:1}.v-expansion-panels>*{cursor:auto}.v-expansion-panels>:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.v-expansion-panels>:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--active{border-radius:8px}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--active+.v-expansion-panel{border-top-left-radius:8px;border-top-right-radius:8px}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--next-active{border-bottom-left-radius:8px;border-bottom-right-radius:8px}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--next-active .v-expansion-panel-header{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.v-expansion-panel{flex:1 0 100%;max-width:100%;position:relative;transition:.3s cubic-bezier(.25,.8,.5,1)}.v-expansion-panel:before{border-radius:inherit;bottom:0;content:\"\";left:0;position:absolute;right:0;top:0;z-index:-1;transition:box-shadow .28s cubic-bezier(.4,0,.2,1);will-change:box-shadow;box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-expansion-panel:not(:first-child):after{border-top:thin solid;content:\"\";left:0;position:absolute;right:0;top:0;transition:border-color .2s cubic-bezier(.4,0,.2,1),opacity .2s cubic-bezier(.4,0,.2,1)}.v-expansion-panel--disabled .v-expansion-panel-header{pointer-events:none}.v-expansion-panel--active+.v-expansion-panel,.v-expansion-panel--active:not(:first-child){margin-top:16px}.v-expansion-panel--active+.v-expansion-panel:after,.v-expansion-panel--active:not(:first-child):after{opacity:0}.v-expansion-panel--active>.v-expansion-panel-header{min-height:64px}.v-expansion-panel--active>.v-expansion-panel-header--active .v-expansion-panel-header__icon:not(.v-expansion-panel-header__icon--disable-rotate) .v-icon{transform:rotate(-180deg)}.v-expansion-panel-header__icon{display:inline-flex;margin-bottom:-4px;margin-top:-4px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-expansion-panel-header__icon{margin-left:auto}.v-application--is-rtl .v-expansion-panel-header__icon{margin-right:auto}.v-expansion-panel-header{align-items:center;border-top-left-radius:inherit;border-top-right-radius:inherit;display:flex;font-size:.9375rem;line-height:1;min-height:64px;outline:none;padding:20px 24px;position:relative;transition:min-height .3s cubic-bezier(.25,.8,.5,1);width:100%}.v-application--is-ltr .v-expansion-panel-header{text-align:left}.v-application--is-rtl .v-expansion-panel-header{text-align:right}.v-expansion-panel-header:not(.v-expansion-panel-header--mousedown):focus:before{opacity:.12}.v-expansion-panel-header:before{background-color:currentColor;border-radius:inherit;bottom:0;content:\"\";left:0;opacity:0;pointer-events:none;position:absolute;right:0;top:0;transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-expansion-panel-header>:not(.v-expansion-panel-header__icon){flex:1 1 auto}.v-expansion-panel-content{display:flex}.v-expansion-panel-content__wrap{padding:0 24px 20px;flex:1 1 auto;max-width:100%}.v-expansion-panels--accordion>.v-expansion-panel{margin-top:0}.v-expansion-panels--accordion>.v-expansion-panel:after{opacity:1}.v-expansion-panels--popout>.v-expansion-panel{max-width:calc(100% - 32px)}.v-expansion-panels--popout>.v-expansion-panel--active{max-width:calc(100% + 16px)}.v-expansion-panels--inset>.v-expansion-panel{max-width:100%}.v-expansion-panels--inset>.v-expansion-panel--active{max-width:calc(100% - 32px)}.v-expansion-panels--flat>.v-expansion-panel:after{border-top:none}.v-expansion-panels--flat>.v-expansion-panel:before{box-shadow:0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)}.v-expansion-panels--tile,.v-expansion-panels--tile>.v-expansion-panel:before{border-radius:0}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 971:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(972);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("5e62c9d0", content, true)

/***/ }),

/***/ 972:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-radio--is-disabled label{color:rgba(0,0,0,.38)}.theme--light.v-radio--is-disabled .v-icon{color:rgba(0,0,0,.26)!important}.theme--dark.v-radio--is-disabled label{color:hsla(0,0%,100%,.5)}.theme--dark.v-radio--is-disabled .v-icon{color:hsla(0,0%,100%,.3)!important}.v-radio{align-items:center;display:flex;height:auto;outline:none}.v-radio--is-disabled{pointer-events:none;cursor:default}.v-input--radio-group.v-input--radio-group--row .v-radio{margin-right:16px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 973:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(974);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("999cb8a8", content, true)

/***/ }),

/***/ 974:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-input--radio-group legend.v-label{cursor:text;font-size:14px;height:auto}.v-input--radio-group__input{border:none;cursor:default;display:flex;width:100%}.v-input--radio-group--column .v-input--radio-group__input>.v-label{padding-bottom:8px}.v-input--radio-group--row .v-input--radio-group__input>.v-label{padding-right:8px}.v-input--radio-group--row legend{align-self:center;display:inline-block}.v-input--radio-group--row .v-input--radio-group__input{flex-direction:row;flex-wrap:wrap}.v-input--radio-group--column legend{padding-bottom:8px}.v-input--radio-group--column .v-radio:not(:last-child):not(:only-child){margin-bottom:8px}.v-input--radio-group--column .v-input--radio-group__input{flex-direction:column}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 982:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1042);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("f203485e", content, true, context)
};

/***/ }),

/***/ 985:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/ConfirmDialog.vue?vue&type=template&id=2a649283&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.isShownConfirmDialog)?_c('l-dialog',_vm._g({attrs:{"dialog":_vm.isShownConfirmDialog,"hide-close-button":"","max-width":"418","custom-class":"remove-illustration text-center"}},_vm.$listeners),[_c('div',[_c('div',{staticClass:"remove-illustration-title font-weight-medium"},[_vm._v("\n      "+_vm._s(_vm.$t('are_you_sure'))+"\n    ")]),_vm._v(" "),_c('div',{staticClass:"mt-2"},[_vm._t("default")],2),_vm._v(" "),_c('div',{staticClass:"d-flex justify-space-around justify-sm-space-between flex-wrap mt-2"},[_c('v-btn',{staticClass:"gradient font-weight-medium my-1",on:{"click":function($event){return _vm.$emit('close-dialog')}}},[_c('div',{staticClass:"text--gradient"},[_vm._v("\n          "+_vm._s(_vm.$t(_vm.cancelTextButton))+"\n        ")])]),_vm._v(" "),_c('v-btn',{staticClass:"font-weight-medium my-1",attrs:{"color":"primary"},on:{"click":function($event){return _vm.$emit('confirm')}}},[_vm._v("\n        "+_vm._s(_vm.$t(_vm.confirmTextButton))+"\n      ")])],1)])]):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/ConfirmDialog.vue?vue&type=template&id=2a649283&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/ConfirmDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var ConfirmDialogvue_type_script_lang_js_ = ({
  name: 'ConfirmDialog',
  props: {
    isShownConfirmDialog: {
      type: Boolean,
      required: true
    },
    cancelTextButton: {
      type: String,
      default: 'close'
    },
    confirmTextButton: {
      type: String,
      default: 'confirm'
    }
  }
});
// CONCATENATED MODULE: ./components/ConfirmDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_ConfirmDialogvue_type_script_lang_js_ = (ConfirmDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// CONCATENATED MODULE: ./components/ConfirmDialog.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1041)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_ConfirmDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "33ddf780"
  
)

/* harmony default export */ var ConfirmDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LDialog: __webpack_require__(28).default})


/* vuetify-loader */


installComponents_default()(component, {VBtn: VBtn["a" /* default */]})


/***/ }),

/***/ 988:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingTemplate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(951);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingTemplate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingTemplate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingTemplate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserSettingTemplate_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 989:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-settings-panel{padding:44px;border-radius:20px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1)}@media only screen and (max-width:1439px){.user-settings-panel{padding:24px}}@media only screen and (max-width:767px){.user-settings-panel{padding:0;box-shadow:none}}.user-settings-panel .row{margin:0 -14px!important}.user-settings-panel .col{padding:0 14px!important}.user-settings-panel .panel{color:var(--v-greyDark-base)}.user-settings-panel .panel-head-title{font-size:24px;line-height:1.333;color:var(--v-darkLight-base)}@media only screen and (max-width:1439px){.user-settings-panel .panel-head-title{font-size:20px}}.user-settings-panel .panel-body .chips>div{margin-top:6px}.user-settings-panel .panel-body .price-input .v-text-field--outlined.v-input--dense.v-text-field--outlined>.v-input__control>.v-input__slot{min-height:32px!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr>th{color:var(--v-dark-base)}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>td:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:not(:last-child)>th:not(.v-data-table__mobile-row),.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>thead>tr:last-child>th{border:none!important}.user-settings-panel .panel-body .theme--light.v-data-table>.v-data-table__wrapper>table>tbody>tr:hover:not(.v-data-table__expanded__content):not(.v-data-table__empty-wrapper){background:none}.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tbody>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>tfoot>tr>td,.user-settings-panel .panel-body .v-data-table>.v-data-table__wrapper>table>thead>tr>td{height:38px;color:var(--v-greyDark-base)}.user-settings-panel .panel-footer{margin-top:115px}@media only screen and (max-width:1439px){.user-settings-panel .panel-footer{margin-top:56px}}.user-settings-panel .panel-footer .v-btn{letter-spacing:.1px}@media only screen and (max-width:479px){.user-settings-panel .panel-footer .v-btn{width:100%!important}}.user-settings-panel .l-checkbox .v-input--selection-controls__input{width:16px;height:16px;margin-top:2px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

};;
//# sourceMappingURL=index.js.map