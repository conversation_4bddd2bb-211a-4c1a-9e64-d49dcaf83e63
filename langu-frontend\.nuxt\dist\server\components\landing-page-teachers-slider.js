exports.ids = [59,76];
exports.modules = {

/***/ 1028:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StarRating_vue_vue_type_style_index_0_id_1645fb89_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(968);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StarRating_vue_vue_type_style_index_0_id_1645fb89_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StarRating_vue_vue_type_style_index_0_id_1645fb89_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StarRating_vue_vue_type_style_index_0_id_1645fb89_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_StarRating_vue_vue_type_style_index_0_id_1645fb89_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1029:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".score[data-v-1645fb89]{display:flex;align-items:center;height:18px;font-size:12px;line-height:.8;font-weight:700;letter-spacing:.1px;color:var(--v-orange-base)}@media only screen and (max-width:1215px){.score[data-v-1645fb89]{justify-content:flex-end}}.score>div[data-v-1645fb89]{width:65px;display:flex;margin-left:2px}@media only screen and (max-width:1215px){.score>div[data-v-1645fb89]{width:auto}}.score svg[data-v-1645fb89]:not(:first-child){margin-left:1px}.score--large[data-v-1645fb89]{font-size:18px}@media only screen and (max-width:1215px){.score--large[data-v-1645fb89]{font-size:16px}}.score--large>div[data-v-1645fb89]{width:112px;margin-left:8px}@media only screen and (max-width:1215px){.score--large>div[data-v-1645fb89]{width:84px}}.score--large svg[data-v-1645fb89]:not(:first-child){margin-left:3px}@media only screen and (max-width:1215px){.score--large svg[data-v-1645fb89]:not(:first-child){margin-left:1px}}@media only screen and (max-width:1215px){.score--large svg[data-v-1645fb89]{width:16px!important;height:16px!important}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1071:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1105);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("20c2c1c7", content, true)

/***/ }),

/***/ 1105:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".slick-track[data-v-e4caeaf8]{position:relative;top:0;left:0;display:block;transform:translateZ(0)}.slick-track.slick-center[data-v-e4caeaf8]{margin-left:auto;margin-right:auto}.slick-track[data-v-e4caeaf8]:after,.slick-track[data-v-e4caeaf8]:before{display:table;content:\"\"}.slick-track[data-v-e4caeaf8]:after{clear:both}.slick-loading .slick-track[data-v-e4caeaf8]{visibility:hidden}.slick-slide[data-v-e4caeaf8]{display:none;float:left;height:100%;min-height:1px}[dir=rtl] .slick-slide[data-v-e4caeaf8]{float:right}.slick-slide img[data-v-e4caeaf8]{display:block}.slick-slide.slick-loading img[data-v-e4caeaf8]{display:none}.slick-slide.dragging img[data-v-e4caeaf8]{pointer-events:none}.slick-initialized .slick-slide[data-v-e4caeaf8]{display:block}.slick-loading .slick-slide[data-v-e4caeaf8]{visibility:hidden}.slick-vertical .slick-slide[data-v-e4caeaf8]{display:block;height:auto;border:1px solid transparent}.slick-arrow.slick-hidden[data-v-21137603]{display:none}.slick-slider[data-v-3d1a4f76]{position:relative;display:block;box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;touch-action:pan-y;-webkit-tap-highlight-color:transparent}.slick-list[data-v-3d1a4f76]{position:relative;display:block;overflow:hidden;margin:0;padding:0;transform:translateZ(0)}.slick-list[data-v-3d1a4f76]:focus{outline:none}.slick-list.dragging[data-v-3d1a4f76]{cursor:pointer;cursor:hand}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1212:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1282);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("66d4ff73", content, true, context)
};

/***/ }),

/***/ 1281:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachersSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1212);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachersSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachersSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachersSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TeachersSlider_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1282:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, "@media(max-width:850px){.lp-teachers-slider .slick-slide{padding:0 15px;transition:all .5s ease-in}}@media only screen and (max-width:850px)and (max-width:639px){.lp-teachers-slider .slick-slide{padding:0}}@media(max-width:850px){.lp-teachers-slider .slick-slide:not(.slick-center){transform:translateZ(0) scale(.8)!important;opacity:.7}}@media only screen and (max-width:850px)and (max-width:991px){.lp-teachers-slider .slick-slide:not(.slick-center){transform:translateZ(0) scale(.9)!important;opacity:.6}}.lp-teachers-slider .slider{padding:15px 30px;margin-top:50px;display:flex;align-items:center;justify-content:space-between}.lp-teachers-slider .slider-card{width:100%;max-width:450px;margin:0 auto;position:relative;box-shadow:0 0 20px rgba(0,0,0,.15);border-radius:20px;display:flex;flex-direction:column;overflow:hidden}.lp-teachers-slider .slider-card__image{width:100%;height:430px;max-height:330px}.lp-teachers-slider .slider-card__img-point{position:absolute;top:20px;right:20px}.lp-teachers-slider .slider-card__content{color:#fff;padding:20px;background:linear-gradient(180.39deg,rgba(171,19,92,.8) -80.41%,rgba(247,173,72,.8) 86.01%)}.lp-teachers-slider .slider-card__content-star{color:#fff}.lp-teachers-slider .slider-card__content-star p{font-size:14px}.lp-teachers-slider .slider-card__content-star #text{display:inline}.lp-teachers-slider .slider-card__content-name{font-size:20px;font-weight:700;line-height:1.3;color:#fff!important;text-decoration:none}.lp-teachers-slider .slider-card__content-text{font-size:18px;line-height:20px;font-weight:300;margin:15px 0}@media only screen and (max-width:639px){.lp-teachers-slider .slider-card__content-text{font-size:16px}}.lp-teachers-slider .slider-card__content-tlabel{color:#fff;font-size:18px}@media only screen and (max-width:639px){.lp-teachers-slider .slider-card__content-tlabel{font-size:16px}}@media only screen and (max-width:479px){.lp-teachers-slider .slider-card__content-tlabel{font-size:14px}}.lp-teachers-slider .slider-card__content-ttext{color:#fff;font-size:18px;font-weight:300;padding-left:10px}@media only screen and (max-width:639px){.lp-teachers-slider .slider-card__content-ttext{font-size:16px}}@media only screen and (max-width:479px){.lp-teachers-slider .slider-card__content-ttext{font-size:14px}}.lp-teachers-slider .slider-card td{padding-top:10px;vertical-align:baseline}.lp-teachers-slider .slider-card .flags-area{position:absolute;right:0;text-align:right;top:0;width:100%}.lp-teachers-slider .slider-card .flag-icon{display:inline-block;font-size:30px;margin:10px}@media(max-width:1439px){.lp-teachers-slider .slider{display:flex;justify-content:center;padding:15px}.lp-teachers-slider .slider-card{max-width:480px;margin:0 auto}.lp-teachers-slider .slider-card__content-text{line-height:20px}}@media(max-width:1170px){.lp-teachers-slider .slider-card{max-width:400px}.lp-teachers-slider .slider-card__image{width:inherit;background-position:0 15%}}@media(max-width:900px){.lp-teachers-slider .slider-card{max-width:480px}}@media only screen and (max-width:639px){.lp-teachers-slider .slider{flex-direction:column;padding:0}}@media(max-width:480px){.lp-teachers-slider .slider-card__image{background-position:50%}}.lp-teachers-slider .slick-arrow{position:absolute;top:50%;background-color:#000;transform:translateY(-50%)}.lp-teachers-slider .slick-arrow.slick-next{right:30px}.lp-teachers-slider .slick-arrow.slick-prev{left:30px}.lp-teachers-slider .slick-dots{margin-top:10px!important}.lp-teachers-slider--dark .slider-card__content{color:#fff;background:var(--v-darkLight-base)}.lp-teachers-slider--dark .slider-card__content-star{color:var(--v-orangeLight-base)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1385:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/landing-page/TeachersSlider.vue?vue&type=template&id=29ad2fba&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.data.length)?_c('div',{class:['lp-teachers-slider', { 'lp-teachers-slider--dark': _vm.dark }]},[_c('client-only',[_c('VueSlickCarousel',_vm._b({},'VueSlickCarousel',_vm.sliderSettings,false),_vm._l((_vm.data),function(item,index){return _c('div',{key:index},[_c('div',{staticClass:"slider"},[_c('div',{staticClass:"slider-card"},[_c('nuxt-link',{attrs:{"to":{ path: ("/teacher/" + (item.username)) },"target":"_blank"}},[_c('v-img',{staticClass:"slider-card__image",attrs:{"src":_vm.getSrcAvatar(item.picture),"position":"50% 30%"}})],1),_vm._v(" "),(item.languagesTaught.length)?_c('div',{staticClass:"flags-area d-flex justify-end"},_vm._l((item.languagesTaught),function(languageTaught){return _c('div',{key:languageTaught.isoCode,staticClass:"flags-item ma-1 elevation-2 rounded overflow-hidden"},[_c('v-img',{attrs:{"src":__webpack_require__(101)("./" + (languageTaught.isoCode) + ".svg"),"width":"40","height":"30","contain":"","options":{ rootMargin: '50%' }}})],1)}),0):_vm._e(),_vm._v(" "),_c('div',{staticClass:"slider-card__content"},[_c('div',{staticClass:"d-flex justify-space-between align-center"},[_c('nuxt-link',{staticClass:"slider-card__content-name text-uppercase",attrs:{"to":{ path: ("/teacher/" + (item.username)) },"target":"_blank"}},[_vm._v("\n                  "+_vm._s(item.firstName)+" "+_vm._s(item.lastName)+"\n                ")]),_vm._v(" "),_c('div',{staticClass:"slider-card__content-star"},[_c('star-rating',{staticClass:"mr-1 mr-sm-2 mr-md-0",attrs:{"value":item.averageRatings}}),_vm._v(" "),_c('p',{staticClass:"mb-0"},[_vm._v("\n                    ("+_vm._s(_vm.$tc('review', item.countFeedbacks))+")\n                  ")])],1)],1),_vm._v(" "),_c('div',{staticClass:"slider-card__content-text"},[_vm._v("\n                "+_vm._s(item.shortSummary)+"\n              ")]),_vm._v(" "),_c('table',[_c('tr',[_c('td',{class:[
                      'slider-card__content-tlabel',
                      { 'dark--text': !_vm.dark } ]},[_vm._v("\n                    "+_vm._s(_vm.$t('teaches'))+":\n                  ")]),_vm._v(" "),_c('td',{class:[
                      'slider-card__content-ttext',
                      { 'dark--text': !_vm.dark } ]},[_vm._v("\n                    "+_vm._s(item.languagesTaught.map(function (i) { return i.name; }).join(', '))+"\n                  ")])]),_vm._v(" "),_c('tr',[_c('td',{class:[
                      'slider-card__content-tlabel',
                      { 'dark--text': !_vm.dark } ]},[_vm._v("\n                    "+_vm._s(_vm.$t('specialities'))+":\n                  ")]),_vm._v(" "),_c('td',{class:[
                      'slider-card__content-ttext',
                      { 'dark--text': !_vm.dark } ]},[_vm._v("\n                    "+_vm._s(item.specialities)+"\n                  ")])])])])],1)])])}),0)],1)],1):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/landing-page/TeachersSlider.vue?vue&type=template&id=29ad2fba&

// EXTERNAL MODULE: external "vue-slick-carousel"
var external_vue_slick_carousel_ = __webpack_require__(859);
var external_vue_slick_carousel_default = /*#__PURE__*/__webpack_require__.n(external_vue_slick_carousel_);

// EXTERNAL MODULE: ./node_modules/vue-slick-carousel/dist/vue-slick-carousel.css
var vue_slick_carousel = __webpack_require__(1071);

// EXTERNAL MODULE: ./components/StarRating.vue + 4 modules
var StarRating = __webpack_require__(996);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/landing-page/TeachersSlider.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



/* harmony default export */ var TeachersSlidervue_type_script_lang_js_ = ({
  name: 'TeachersSlider',
  components: {
    VueSlickCarousel: external_vue_slick_carousel_default.a,
    StarRating: StarRating["default"]
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    dark: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      slider: null,
      sliderSettings: {
        dots: false,
        focusOnSelect: true,
        infinite: true,
        speed: 800,
        slidesToShow: 1,
        slidesToScroll: 1,
        responsive: [{
          breakpoint: 900,
          settings: {
            arrows: false,
            dots: true
          }
        }, {
          breakpoint: 850,
          settings: {
            arrows: false,
            dots: true,
            centerMode: true,
            centerPadding: '100px'
          }
        }, {
          breakpoint: 639,
          settings: {
            arrows: false,
            dots: true,
            centerMode: true,
            centerPadding: '45px'
          }
        }, {
          breakpoint: 479,
          settings: {
            arrows: false,
            dots: true,
            centerMode: true,
            centerPadding: '30px'
          }
        }]
      }
    };
  },

  methods: {
    getSrcAvatar(image, defaultImage = 'avatar.png') {
      return image || __webpack_require__(511)(`./${defaultImage}`);
    }

  }
});
// CONCATENATED MODULE: ./components/landing-page/TeachersSlider.vue?vue&type=script&lang=js&
 /* harmony default export */ var landing_page_TeachersSlidervue_type_script_lang_js_ = (TeachersSlidervue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/landing-page/TeachersSlider.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1281)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  landing_page_TeachersSlidervue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "4baed921"
  
)

/* harmony default export */ var TeachersSlider = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {StarRating: __webpack_require__(996).default})


/* vuetify-loader */


installComponents_default()(component, {VImg: VImg["a" /* default */]})


/***/ }),

/***/ 968:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1029);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("1f907d7b", content, true, context)
};

/***/ }),

/***/ 996:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/StarRating.vue?vue&type=template&id=1645fb89&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:['score', { 'score--large': _vm.large }]},[_vm._ssrNode("<span data-v-1645fb89>"+_vm._ssrEscape(_vm._s(_vm.value_.toFixed(1)))+"</span> <div data-v-1645fb89>"+(_vm._ssrList((_vm.stars),function(i){return ("<svg"+(_vm._ssrAttr("width",_vm.width))+(_vm._ssrAttr("height",_vm.height))+" viewBox=\"0 0 12 12\" data-v-1645fb89><use"+(_vm._ssrAttr("xlink:href",_vm.iconFilledStar))+" data-v-1645fb89></use></svg>")}))+" "+((_vm.isHasHalf)?("<svg"+(_vm._ssrAttr("width",_vm.width))+(_vm._ssrAttr("height",_vm.height))+" viewBox=\"0 0 12 12\" data-v-1645fb89><use"+(_vm._ssrAttr("xlink:href",_vm.iconFilledHalfStar))+" data-v-1645fb89></use></svg>"):"<!---->")+"</div>")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/StarRating.vue?vue&type=template&id=1645fb89&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/StarRating.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var StarRatingvue_type_script_lang_js_ = ({
  name: 'StarRating',
  props: {
    value: {
      type: Number,
      required: true
    },
    large: {
      type: Boolean,
      required: false
    }
  },

  data() {
    return {
      iconFilledStar: `${__webpack_require__(14)}#filledStar`,
      iconFilledHalfStar: `${__webpack_require__(14)}#filledHalfStar`
    };
  },

  computed: {
    width() {
      return this.large ? 20 : 12;
    },

    height() {
      return this.large ? 20 : 12;
    },

    value_() {
      return Math.round(this.value * 10) / 10;
    },

    isRoundToLess() {
      const rest = Math.round(this.value_ % 1 * 10);
      return rest <= 5 && rest !== 0;
    },

    roundToLessHalf() {
      return this.isRoundToLess ? Math.floor(this.value_ * 2) / 2 : Math.ceil(this.value_ * 2) / 2;
    },

    stars() {
      return this.isRoundToLess ? Math.floor(this.roundToLessHalf) : Math.ceil(this.roundToLessHalf);
    },

    isHasHalf() {
      return this.isRoundToLess && this.value_ !== 5 || this.value_ < 0.5;
    }

  }
});
// CONCATENATED MODULE: ./components/StarRating.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_StarRatingvue_type_script_lang_js_ = (StarRatingvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/StarRating.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1028)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_StarRatingvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "1645fb89",
  "743e07b2"
  
)

/* harmony default export */ var StarRating = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=landing-page-teachers-slider.js.map