exports.ids = [127];
exports.modules = {

/***/ 1364:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1426);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("3daf26fe", content, true, context)
};

/***/ }),

/***/ 1425:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1364);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1426:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".faq-page{--sidebar-width:100%}@media only screen and (min-width:992px){.faq-page{--sidebar-width:260px}}.faq-page-wrap{max-width:1030px}@media only screen and (max-width:991px){.faq-page-wrap{max-width:720px}}.faq-page-title{font-size:32px;line-height:1.333}@media only screen and (max-width:991px){.faq-page-title{font-size:26px}}@media only screen and (max-width:639px){.faq-page-title{font-size:24px}}.faq-page-content{width:100%}@media only screen and (min-width:992px){.faq-page-content{width:calc(100% - var(--sidebar-width));padding-right:36px}}.faq-page-content h2{font-size:26px}@media only screen and (max-width:991px){.faq-page-content h2{font-size:22px}}@media only screen and (max-width:639px){.faq-page-content h2{font-size:20px}}.faq-page-content .v-expansion-panel-content__wrap p{margin-bottom:16px!important}.faq-page-content .v-expansion-panel-content__wrap a{color:var(--v-orange-base);text-decoration:none}.faq-page-content .v-expansion-panel-content__wrap img{max-width:100%;margin-top:20px}.faq-page-content .v-expansion-panel-content__wrap .iframe-wrapper{position:relative;max-width:560px;margin-left:auto;margin-right:auto}.faq-page-content .v-expansion-panel-content__wrap .iframe-wrapper:before{content:\"\";position:relative;display:block;width:100%;padding-bottom:56.25%}.faq-page-content .v-expansion-panel-content__wrap .iframe-wrapper iframe{position:absolute;top:0;left:0;width:100%;height:100%}.faq-page-sidebar{width:var(--sidebar-width)}@media only screen and (min-width:992px){.faq-page-sidebar-sticky{position:sticky;top:70px}}.faq-page-sidebar-item:after{content:\"\";display:table;clear:both}.faq-page-sidebar-item .item-title{font-size:24px}@media only screen and (min-width:992px){.faq-page-sidebar-item .item-title{font-size:22px}}@media only screen and (max-width:639px){.faq-page-sidebar-item .item-title{font-size:20px}}.faq-page-sidebar-item .item-content a{color:var(--v-darkLight-base);text-decoration:none}.faq-page-sidebar-item .item-content a:hover{color:var(--v-orange-base);transition:color .2s}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1469:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/blog/index.vue?vue&type=template&id=58e3d554&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{attrs:{"id":"dib-posts"}},[])}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/blog/index.vue?vue&type=template&id=58e3d554&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/blog/index.vue?vue&type=script&lang=js&
//
//
//
//
/* harmony default export */ var blogvue_type_script_lang_js_ = ({
  head() {
    return {
      title: this.$i18n.locale === 'pl' ? 'Langu | Blog: Artykuły i zasoby do efektywnej nauki języków online' : 'Langu | Blog: Articles and Resources for Effective Online Language Learning',
      script: [{
        src: 'https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js',
        async: true,
        defer: true
      }]
    };
  },

  mounted() {
    this.loadDropInBlogScript();
  },

  methods: {
    loadDropInBlogScript() {
      if (typeof window.main === 'function') {
        // Call the main function directly if it exists
        window.main();
      } else {
        const existingScript = document.querySelector('script[src="https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js"]');

        if (!existingScript) {
          const script = document.createElement('script');
          script.src = 'https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js';
          script.async = true;
          script.defer = true;

          script.onload = () => {
            // Ensure the main function is called after the script is loaded
            if (typeof window.main === 'function') {
              window.main();
            }
          };

          document.head.appendChild(script);
        }
      }
    }

  }
});
// CONCATENATED MODULE: ./pages/blog/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var pages_blogvue_type_script_lang_js_ = (blogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./pages/blog/index.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1425)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  pages_blogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "f4bfd6a4"
  
)

/* harmony default export */ var blog = __webpack_exports__["default"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=index.js.map