(window.webpackJsonp=window.webpackJsonp||[]).push([[116,57,140],{1437:function(e,t,n){"use strict";n(23);var r={methods:{getSrcAvatar:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"avatar.png";return null!=e&&e[t]?e[t]:n(881)("./".concat(r))},getSrcSetAvatar:function(e,t,n){return null!=e&&e[t]&&null!=e&&e[n]?"\n            ".concat(e[t]," 1x,\n            ").concat(e[n]," 2x,\n          "):""}}},o=n(22),component=Object(o.a)(r,undefined,undefined,!1,null,null,null);t.a=component.exports},1445:function(e,t,n){var content=n(1512);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("f203485e",content,!0,{sourceMap:!1})},1450:function(e,t,n){"use strict";n.r(t);var r={name:"ConfirmDialog",props:{isShownConfirmDialog:{type:Boolean,required:!0},cancelTextButton:{type:String,default:"close"},confirmTextButton:{type:String,default:"confirm"}}},o=(n(1511),n(22)),c=n(42),d=n.n(c),l=n(1327),component=Object(o.a)(r,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.isShownConfirmDialog?n("l-dialog",e._g({attrs:{dialog:e.isShownConfirmDialog,"hide-close-button":"","max-width":"418","custom-class":"remove-illustration text-center"}},e.$listeners),[n("div",[n("div",{staticClass:"remove-illustration-title font-weight-medium"},[e._v("\n      "+e._s(e.$t("are_you_sure"))+"\n    ")]),e._v(" "),n("div",{staticClass:"mt-2"},[e._t("default")],2),e._v(" "),n("div",{staticClass:"d-flex justify-space-around justify-sm-space-between flex-wrap mt-2"},[n("v-btn",{staticClass:"gradient font-weight-medium my-1",on:{click:function(t){return e.$emit("close-dialog")}}},[n("div",{staticClass:"text--gradient"},[e._v("\n          "+e._s(e.$t(e.cancelTextButton))+"\n        ")])]),e._v(" "),n("v-btn",{staticClass:"font-weight-medium my-1",attrs:{color:"primary"},on:{click:function(t){return e.$emit("confirm")}}},[e._v("\n        "+e._s(e.$t(e.confirmTextButton))+"\n      ")])],1)])]):e._e()}),[],!1,null,null,null);t.default=component.exports;d()(component,{LDialog:n(149).default}),d()(component,{VBtn:l.a})},1511:function(e,t,n){"use strict";n(1445)},1512:function(e,t,n){var r=n(18)(!1);r.push([e.i,".remove-illustration-title{font-size:20px}",""]),e.exports=r},1522:function(e,t,n){"use strict";n.r(t);n(31),n(20),n(80);var r={name:"UserStatus",props:{userId:{type:Number,default:0},large:{type:Boolean,default:!1},userStatuses:{type:Object,default:function(){return{}}}},computed:{status:function(){var e,t="offline";return Object.prototype.hasOwnProperty.call(this.userStatuses,null===(e=this.userId)||void 0===e?void 0:e.toString())&&(t=this.userStatuses[this.userId]),t}}},o=(n(1597),n(22)),component=Object(o.a)(r,(function(){var e=this,t=e.$createElement;return(e._self._c||t)("div",{class:["user-status","user-status--"+e.status,{"user-status--large":e.large}]})}),[],!1,null,"652352c7",null);t.default=component.exports},1545:function(e,t,n){var content=n(1598);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("006007e9",content,!0,{sourceMap:!1})},1597:function(e,t,n){"use strict";n(1545)},1598:function(e,t,n){var r=n(18)(!1);r.push([e.i,".user-status[data-v-652352c7]{width:16px;height:16px;border-radius:50%;border:2px solid #fff;background:#636363;z-index:2}.user-status--idle[data-v-652352c7]{background:linear-gradient(122.42deg,var(--v-redLight-base),var(--v-orangeLight2-base))}.user-status--online[data-v-652352c7]{background:var(--v-success-base)}.user-status--large[data-v-652352c7]{width:25px;height:25px}@media only screen and (max-width:991px){.user-status--large[data-v-652352c7]{width:23px;height:23px}}@media only screen and (max-width:639px){.user-status--large[data-v-652352c7]{width:21px;height:21px}}@media only screen and (max-width:479px){.user-status--large[data-v-652352c7]{width:19px;height:19px}}",""]),e.exports=r},1633:function(e,t,n){var content=n(1732);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,n(19).default)("7337ad18",content,!0,{sourceMap:!1})},1731:function(e,t,n){"use strict";n(1633)},1732:function(e,t,n){var r=n(18)(!1);r.push([e.i,".conversation-item{display:flex;width:100%}.conversation-item>div{width:calc(100% - 60px);max-width:462px}@media only screen and (max-width:639px){.conversation-item>div{width:calc(100% - 45px)}}@media only screen and (max-width:479px){.conversation-item>div{width:calc(100% - 20px)}}.conversation-item-header{display:flex;align-items:flex-end;margin-bottom:2px;font-size:13px;color:var(--v-greyLight-darken2);line-height:1.23}@media only screen and (max-width:479px){.conversation-item-header{font-size:12px}}.conversation-item-header-avatar{position:relative;margin-bottom:2px;filter:drop-shadow(0 4px 5px rgba(0,0,0,.2))}@media only screen and (max-width:479px){.conversation-item-header-avatar .v-avatar{width:42px!important;min-width:42px!important;height:42px!important}}.conversation-item-header>div:not(.conversation-item-header-avatar){margin-left:10px}.conversation-item-body{padding:16px 12px;font-size:14px;line-height:1.4;border-radius:16px;color:var(--v-greyLight-darken4);background:linear-gradient(126.15deg,rgba(128,182,34,.18),rgba(60,135,248,.18) 102.93%)}@media only screen and (max-width:1439px){.conversation-item-body{padding:14px 12px}}.conversation-item-body a{color:var(--v-greyLight-darken4)!important;transition:color .3s}.conversation-item-body a span{display:inline-block;font-size:20px}.conversation-item-body a:hover{color:var(--v-success-base)!important}.conversation-item-body.conversation-item-body--file a{text-decoration:none}.conversation-item-body ul{padding-left:20px}.conversation-item-body ul li{margin-bottom:0}.conversation-item-body ul li p{min-height:16px;margin-bottom:0}.conversation-item-body>div{word-wrap:break-word}.conversation-item-body>div>*{min-height:16px;margin-bottom:0}.conversation-item-body>div>:last-child{min-height:0}.conversation-item-footer{display:flex;justify-content:flex-end;align-items:center;margin-top:2px;padding-right:10px;font-size:13px;color:var(--v-greyLight-darken2);line-height:1.23}@media only screen and (max-width:479px){.conversation-item-footer{font-size:12px}}.conversation-item--mine{justify-content:flex-end}.conversation-item--mine .conversation-item-header{flex-direction:row-reverse}.conversation-item--mine .conversation-item-header-avatar{margin-right:0;margin-left:12px}.conversation-item--mine .conversation-item-footer{padding-right:0}.conversation-item--mine+.conversation-item--mine{margin-top:10px}@media only screen and (max-width:479px){.conversation-item--mine+.conversation-item--mine{margin-top:6px}}.conversation-item--mine+.conversation-item--mine .conversation-item-header-avatar{display:none}.conversation-item--other+.conversation-item--other{margin-top:10px}@media only screen and (max-width:479px){.conversation-item--other+.conversation-item--other{margin-top:6px}}.conversation-item--other+.conversation-item--other .conversation-item-header-avatar{display:none}",""]),e.exports=r},1919:function(e,t,n){"use strict";n.r(t);var r=n(10),o=(n(62),n(31),n(23),n(20),n(37),n(44),n(151),n(1450)),c=n(1437),d={name:"ConversationItem",components:{UserStatus:n(1522).default,ConfirmDialog:o.default},mixins:[c.a],props:{item:{type:Object,required:!0},threadId:{type:Number,required:!0},recipientName:{type:String,required:!0},recipientAvatars:{type:Object,required:!0},userAvatars:{type:Object,required:!0},userStatuses:{type:Object,default:function(){return{}}}},data:function(){return{isShownMessageConfirmDialog:!1}},computed:{userId:function(){var e;return null===(e=this.$store.state.user.item)||void 0===e?void 0:e.id},isCurrentUser:function(){return this.userId===this.item.authorId},avatars:function(){return this.isCurrentUser?this.userAvatars:this.recipientAvatars},fileUrl:function(){return"".concat("'http://localhost:3000'","/messages/file/").concat(this.item.id)},timeZone:function(){return this.$store.getters["user/timeZone"]}},methods:{downloadClickHandler:function(){var e=this;return Object(r.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$axios({url:e.fileUrl,method:"GET",responseType:"blob"}).then((function(t){var n=window.URL.createObjectURL(new Blob([t.data])),link=document.createElement("a");link.href=n,link.setAttribute("download",e.item.text),document.body.appendChild(link),link.click()})).catch((function(){return console.info("Download error")}));case 2:case"end":return t.stop()}}),t)})))()},removeMessage:function(){this.isShownMessageConfirmDialog=!1,this.$store.dispatch("message/removeMessage",this.item.id)}}},l=(n(1731),n(22)),m=n(42),v=n.n(m),h=n(1343),f=n(1327),x=n(261),component=Object(l.a)(d,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:["conversation-item",{"conversation-item--mine":e.isCurrentUser},{"conversation-item--other":!e.isCurrentUser}]},[n("div",[n("div",{staticClass:"conversation-item-header"},[n("div",{staticClass:"conversation-item-header-avatar"},[n("v-avatar",{attrs:{width:"52",height:"52"}},[n("v-img",{attrs:{src:e.getSrcAvatar(e.avatars,"user_thumb_52x52"),srcset:e.getSrcSetAvatar(e.avatars,"user_thumb_52x52","user_thumb_104x104"),options:{rootMargin:"50%"}}})],1),e._v(" "),n("user-status",{attrs:{"user-id":e.item.authorId,"user-statuses":e.userStatuses}})],1),e._v(" "),n("div",[e.isCurrentUser?[e._v("\n          "+e._s(e.$t("sent_by_me_on"))+"\n        ")]:[e._v("\n          "+e._s(e.$t("sent_by_somebody_on",{username:e.recipientName}))+"\n        ")],e._v("\n        "+e._s(e.$dayjs(e.item.createDate).tz(e.timeZone).format("ll, LT"))+"\n      ")],2)]),e._v(" "),n("div",{class:["conversation-item-body",{"conversation-item-body--file":e.item.isFile}]},[e.item.isFile?[n("a",{attrs:{href:e.fileUrl,download:""},on:{click:function(t){return t.preventDefault(),e.downloadClickHandler.apply(null,arguments)}}},[n("span",{staticClass:"mr-1"},[e._v("📎")]),e._v(e._s(e.item.text)+"\n        ")])]:[n("div",{domProps:{innerHTML:e._s(e.item.text)}})]],2),e._v(" "),n("div",{staticClass:"conversation-item-footer"},[e.item.readDate?[e._v(e._s(e.$t("seen"))+"\n        "+e._s(e.$dayjs(e.item.readDate).tz(e.timeZone).format("ll, LT")))]:[e._v(e._s(e.$t("not_yet_seen")))],e._v(" "),e.isCurrentUser?n("v-btn",{attrs:{text:"","x-small":"",height:"16",color:"grey"},on:{click:function(t){e.isShownMessageConfirmDialog=!0}}},[e._v("\n        ("),n("span",{staticClass:"text-decoration-underline"},[e._v(e._s(e.$t("delete_message")))]),e._v(")")]):e._e()],2)]),e._v(" "),n("confirm-dialog",{attrs:{"is-shown-confirm-dialog":e.isShownMessageConfirmDialog,"cancel-text-button":"no","confirm-text-button":"yes"},on:{confirm:e.removeMessage,"close-dialog":function(t){e.isShownMessageConfirmDialog=!1}}},[e._v("\n    "+e._s(e.$t("are_you_sure_you_want_to_delete_this_message_permanently"))+"\n  ")])],1)}),[],!1,null,null,null);t.default=component.exports;v()(component,{UserStatus:n(1522).default,ConfirmDialog:n(1450).default}),v()(component,{VAvatar:h.a,VBtn:f.a,VImg:x.a})}}]);