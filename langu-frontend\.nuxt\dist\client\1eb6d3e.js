(window.webpackJsonp=window.webpackJsonp||[]).push([[134],{1700:function(t,e,l){"use strict";l.r(e);var n={name:"QualificationSuccessDialog",props:{isShownDialog:{type:Boolean,required:!0}}},o=l(22),c=l(42),r=l.n(c),d=l(261),component=Object(o.a)(n,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("l-dialog",t._g({attrs:{dialog:t.isShownDialog,"max-width":"418","custom-class":"qualification-added text-center"}},t.$listeners),[n("div",[n("v-img",{staticClass:"mx-auto mb-3",attrs:{src:l(963),width:"56",height:"56"}}),t._v(" "),n("div",{staticClass:"qualification-added-text"},[t._v("\n      "+t._s(t.$t("thank_you_for_saving_your_qualification_it_will_shortly_be_verified_by_langu_admin"))+"\n    ")])],1)])}),[],!1,null,null,null);e.default=component.exports;r()(component,{LDialog:l(149).default}),r()(component,{VImg:d.a})}}]);