{"version": 3, "file": "components/user-messages-empty-content.js", "sources": ["webpack:///./components/user-messages/EmptyContent.vue?3b81", "webpack:///./components/user-messages/EmptyContent.vue?8667", "webpack:///./components/user-messages/EmptyContent.vue?d5b5", "webpack:///./components/user-messages/EmptyContent.vue?975e", "webpack:///./components/user-messages/EmptyContent.vue", "webpack:///./components/user-messages/EmptyContent.vue?b742", "webpack:///./components/user-messages/EmptyContent.vue?a8e9"], "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./EmptyContent.vue?vue&type=style&index=0&id=58e2b6d0&scoped=true&lang=scss&\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add CSS to SSR context\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesServer.js\").default\nmodule.exports.__inject__ = function (context) {\n  add(\"79c53e1f\", content, true, context)\n};", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--7-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--7-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--7-oneOf-1-2!../../node_modules/sass-loader/dist/cjs.js??ref--7-oneOf-1-3!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./EmptyContent.vue?vue&type=style&index=0&id=58e2b6d0&scoped=true&lang=scss&\"", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".messages-empty-content[data-v-58e2b6d0]{padding:30px 44px 138px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1);border-radius:20px;line-height:1.4}@media only screen and (max-width:1439px){.messages-empty-content[data-v-58e2b6d0]{padding:24px 24px 60px}}@media only screen and (min-width:768px){.messages-empty-content[data-v-58e2b6d0]{min-height:620px}}@media only screen and (max-width:479px){.messages-empty-content[data-v-58e2b6d0]{padding:24px 15px 30px}}.messages-empty-content-title[data-v-58e2b6d0]{font-size:24px}@media only screen and (max-width:991px){.messages-empty-content-title[data-v-58e2b6d0]{font-size:20px}}.messages-empty-content-text[data-v-58e2b6d0]{font-size:18px}@media only screen and (max-width:991px){.messages-empty-content-text[data-v-58e2b6d0]{font-size:16px}}.messages-empty-content-text a[data-v-58e2b6d0]{color:var(--v-success-base);background:linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));background:-webkit-linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));-webkit-background-clip:text;-webkit-text-fill-color:transparent}.messages-empty-content-text ul[data-v-58e2b6d0]{padding-left:32px}\", \"\"]);\n// Exports\nmodule.exports = ___CSS_LOADER_EXPORT___;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"messages-empty-content\"},[_vm._ssrNode(\"<div class=\\\"messages-empty-content-title font-weight-medium mb-3 mb-sm-4\\\" data-v-58e2b6d0>\"+_vm._ssrEscape(\"\\n    \"+_vm._s(_vm.$t(_vm.isTeacher ? 'no_messages_teacher_yet' : 'no_messages_student_yet'))+\"\\n    💬\\n  \")+\"</div> \"),_vm._ssrNode(\"<div class=\\\"messages-empty-content-text\\\" data-v-58e2b6d0>\",\"</div>\",[(_vm.isTeacher)?[_vm._ssrNode(((_vm.locale === 'pl')?(\"\\n        Kiedy uczeń rezerwuje lekcję próbną, jest poproszony o napisanie\\n        krótkiej wiadom<PERSON>, która pojawi się tutaj.\\n        <br data-v-58e2b6d0><br data-v-58e2b6d0>\\n        Zanim to się stanie, upewnij się, że Twoja strona profilowa wzbudza\\n        jak największe zainteresowanie.\\n        <br data-v-58e2b6d0><br data-v-58e2b6d0> <ul class=\\\"mb-0\\\" data-v-58e2b6d0><li data-v-58e2b6d0>Dodaj wysokiej jakości wideo na YouTube</li> <li data-v-58e2b6d0>\\n            Zwróć uwagę na dodanie przyjaznego zdjęcia profilowego (powinno\\n            być jasne i przejrzyste)\\n          </li> <li data-v-58e2b6d0>\\n            Dodaj Kursy do swojego profilu, aby zaprezentować swoją\\n            specjalistyczną wiedzę\\n          </li> <li data-v-58e2b6d0>Spraw, żeby Twój profil się wyróżniał!</li></ul> <br data-v-58e2b6d0>\\n        Możesz także udostępnić link do swojego profilu nauczyciela. Wyślij go\\n        do poprzednich uczniów, opublikuj w mediach społecznościowych lub\\n        reklamuj w kanałach lokalnych.\\n      \"):(_vm.locale === 'es')?(\"\\n        Cuando un estudiante reserva una lección de prueba, debe escribir un\\n        mensaje de introducción, que aparecerá aquí.\\n        <br data-v-58e2b6d0><br data-v-58e2b6d0>\\n        Hasta entonces, asegúrese de que su perfil docente sea lo más\\n        atractivo posible.\\n        <br data-v-58e2b6d0><br data-v-58e2b6d0> <ul class=\\\"mb-0\\\" data-v-58e2b6d0><li data-v-58e2b6d0>Agregar un video de YouTube de alta calidad</li> <li data-v-58e2b6d0>\\n            Agregue una foto de perfil de bienvenida (brillante y clara)\\n          </li> <li data-v-58e2b6d0>\\n            Agregue uno o más cursos a su perfil para mostrar su experiencia\\n          </li> <li data-v-58e2b6d0>¡Haz que tu personalidad destaque!</li></ul> <br data-v-58e2b6d0>\\n        También puede compartir un enlace a su perfil de maestro. Envíelo a\\n        los alumnos anteriores, publíquelo en las redes sociales o publíquelo\\n        en los canales locales.\\n      \"):(\"\\n        When a student books a trial lesson, they must write an intro message,\\n        which will appear here. You may also receive questions without a trial\\n        booking.\\n        <br data-v-58e2b6d0><br data-v-58e2b6d0>\\n        Until then, make sure to make your teaching profile as engaging as\\n        possible.\\n        <br data-v-58e2b6d0><br data-v-58e2b6d0> <ul class=\\\"mb-0\\\" data-v-58e2b6d0><li data-v-58e2b6d0>Add a high-quality YouTube video</li> <li data-v-58e2b6d0>Add a welcoming profile photo (bright and clear)</li> <li data-v-58e2b6d0>\\n            Add one or more Courses to your profile to showcase your expertise\\n          </li> <li data-v-58e2b6d0>Make your personality stand out!</li></ul> <br data-v-58e2b6d0>\\n        You can also share a link to your teacher profile. Send it to previous\\n        students, post it on social media, or advertise it in local channels.\\n      \"))+\" \"),(_vm.teacherSlug)?[_vm._ssrNode(\"<br data-v-58e2b6d0><br data-v-58e2b6d0> \"),(_vm.$vuetify.breakpoint.smAndUp)?[_c('nuxt-link',{attrs:{\"to\":{ path: (\"/teacher/\" + _vm.teacherSlug) }}},[_vm._v(\"\\n            \"+_vm._s(_vm.profileLink)+\"\\n          \")])]:[_vm._ssrNode(\"<div class=\\\"d-flex align-center\\\" data-v-58e2b6d0>\",\"</div>\",[_vm._ssrNode(\"<svg width=\\\"20\\\" height=\\\"20\\\" viewBox=\\\"0 0 20 20\\\" class=\\\"mr-1\\\" data-v-58e2b6d0><use\"+(_vm._ssrAttr(\"xlink:href\",((require('~/assets/images/icon-sprite.svg')) + \"#user-icon\")))+\" data-v-58e2b6d0></use></svg> <div data-v-58e2b6d0>\"+_vm._ssrEscape(_vm._s(_vm.teacherSlug))+\"</div> \"),_vm._ssrNode(\"<div class=\\\"d-flex align-center text--gradient ml-2\\\" data-v-58e2b6d0>\",\"</div>\",[_c('v-img',{staticClass:\"mr-1\",attrs:{\"src\":require('~/assets/images/copy-icon-gradient.svg'),\"width\":\"16\",\"height\":\"16\"}}),_vm._ssrNode(\" <input type=\\\"text\\\" class=\\\"d-none\\\" data-v-58e2b6d0> <div data-v-58e2b6d0>\"+_vm._ssrEscape(\"\\n                \"+_vm._s(_vm.$t('copy_link'))+\"\\n              \")+\"</div>\")],2)],2)]]:_vm._e()]:[(_vm.locale === 'pl')?[_vm._ssrNode(\"\\n        Wejdź na stronę\\n        \"),_c('nuxt-link',{attrs:{\"to\":\"/teacher-listing\"}},[_vm._v(\"\\\"Znajdź nauczyciela\\\"\")]),_vm._ssrNode(\", aby\\n        wybrać swojego korepetytora, zarezerwować lekcję próbną lub zapytać\\n        nauczyciela jak może pomóc Ci z osiągnięciem Twoich celów\\n        językowych.<br data-v-58e2b6d0><br data-v-58e2b6d0>\\n        Nie zapomnij odwiedzić naszej strony\\n        \"),_c('nuxt-link',{attrs:{\"to\":\"/faq\"}},[_vm._v(\"FAQ\")]),_vm._ssrNode(\", jeśli nie masz pewności, jak\\n        korzystać z Langu!\\n      \")]:(_vm.locale === 'es')?[_vm._ssrNode(\"\\n        Consulte la página\\n        \"),_c('nuxt-link',{attrs:{\"to\":\"/teacher-listing\"}},[_vm._v(\"Encontrar un Profesor\")]),_vm._ssrNode(\"\\n        para elegir un maestro, reservar una lección de prueba o hacer una\\n        pregunta sobre cómo un maestro puede ayudarlo a alcanzar sus\\n        objetivos. <br data-v-58e2b6d0><br data-v-58e2b6d0>\\n        ¡Y asegúrese de visitar nuestra página\\n        \"),_c('nuxt-link',{attrs:{\"to\":\"/faq\"}},[_vm._v(\"de preguntas frecuentes\")]),_vm._ssrNode(\" si no está\\n        seguro de cómo funciona Langu!\\n      \")]:[_vm._ssrNode(\"\\n        Check out the\\n        \"),_c('nuxt-link',{attrs:{\"to\":\"/teacher-listing\"}},[_vm._v(\"Find a Teacher\")]),_vm._ssrNode(\" page to\\n        pick a teacher, book a trial lesson, or ask a question about how a\\n        teacher can help you achieve your goals. <br data-v-58e2b6d0><br data-v-58e2b6d0>\\n        And be sure to visit our \"),_c('nuxt-link',{attrs:{\"href\":\"/faq\"}},[_vm._v(\"FAQ\")]),_vm._ssrNode(\" page\\n        if you’re not sure how Langu works!\\n      \")]]],2)],2)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nexport default {\n  name: 'UserMessagesEmptyContent',\n  computed: {\n    locale() {\n      return this.$i18n.locale\n    },\n    isTeacher() {\n      return this.$store.getters['user/isTeacher']\n    },\n    teacherSlug() {\n      return this.$store.getters['user/teacherSlug']\n    },\n    profileLink() {\n      return this.teacherSlug\n        ? `${process.env.NUXT_ENV_URL}/teacher/${this.teacherSlug}`\n        : null\n    },\n  },\n  methods: {\n    copyLink() {\n      try {\n        const el = this.$refs.profileLink\n\n        el.setAttribute('value', this.profileLink)\n        el.select()\n        el.setSelectionRange(0, 99999)\n\n        navigator.clipboard.writeText(el.value)\n\n        this.$store.dispatch('snackbar/success', {\n          successMessage: 'link_copied',\n          timeout: 1500,\n        })\n      } catch (e) {\n        console.log(e)\n      }\n    },\n  },\n}\n", "import mod from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./EmptyContent.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/babel-loader/lib/index.js??ref--2-0!../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./EmptyContent.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./EmptyContent.vue?vue&type=template&id=58e2b6d0&scoped=true&\"\nimport script from \"./EmptyContent.vue?vue&type=script&lang=js&\"\nexport * from \"./EmptyContent.vue?vue&type=script&lang=js&\"\nfunction injectStyles (context) {\n  \n  var style0 = require(\"./EmptyContent.vue?vue&type=style&index=0&id=58e2b6d0&scoped=true&lang=scss&\")\nif (style0.__inject__) style0.__inject__(context)\n\n}\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  injectStyles,\n  \"58e2b6d0\",\n  \"09c49e92\"\n  \n)\n\nexport default component.exports\n\n/* vuetify-loader */\nimport installComponents from \"!../../node_modules/vuetify-loader/lib/runtime/installComponents.js\"\nimport { VImg } from 'vuetify/lib/components/VImg';\ninstallComponents(component, {VImg})\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACXA;AAAA;AAAA;AAAA;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACNA;AACA;AACA;;;;;ACFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAGA;AACA;AAfA;AAgBA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAFA;AAIA;AACA;AACA;AACA;AACA;AAnBA;AAlBA;;AC/IA;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;A", "sourceRoot": ""}