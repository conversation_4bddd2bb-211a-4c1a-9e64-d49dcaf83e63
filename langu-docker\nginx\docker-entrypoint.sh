#!/bin/bash
set -eux  # Added 'x' to show commands being executed

echo "Starting entrypoint script..."
envsubst '${DOMAIN_NAME} ${DOMAINS_NAME} ${NODE_PORT} ${EMAIL_FOR_CERTBOT} ${ADMIN_DOMAINS_NAME} ${DOMAINS_NAME_FOR_CERTBOT} ${ADMIN_DOMAINS_NAME_FOR_CERTBOT}' < nginx.conf > /etc/nginx/conf.d/default.conf

echo "Generated nginx config:"
cat /etc/nginx/conf.d/default.conf

if [[ ${APP_ENV} == "prod" ]] ; then
    echo "Running in production mode - setting up SSL..."
    apt-get update
    apt-get install -y certbot python-certbot-nginx bash wget
    
    echo "Attempting to get certificates..."
    certbot certonly --standalone --agree-tos -m ${EMAIL_FOR_CERTBOT} -n liveserver.langu.io \
        --dry-run  # First try with --dry-run to test
    certbot certonly --standalone --agree-tos -m ${EMAIL_FOR_CERTBOT} -n liveserver.langu.io
    
    rm -rf /var/lib/apt/lists/*
    echo "@monthly certbot renew --nginx >> /var/log/cron.log 2>&1" >/etc/cron.d/certbot-renew
    crontab /etc/cron.d/certbot-renew
    
    echo "Certificates obtained and renewal set up."
fi

echo "Starting nginx..."
nginx -t  # Test configuration before starting
service nginx stop
nginx -g 'daemon off;'
exec "$@"
