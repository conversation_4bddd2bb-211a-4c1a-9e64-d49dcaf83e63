(window.webpackJsonp=window.webpackJsonp||[]).push([[26],{1862:function(e,t,c){var content=c(2072);content.__esModule&&(content=content.default),"string"==typeof content&&(content=[[e.i,content,""]]),content.locals&&(e.exports=content.locals);(0,c(19).default)("b9052ed2",content,!0,{sourceMap:!1})},2071:function(e,t,c){"use strict";c(1862)},2072:function(e,t,c){var n=c(18)(!1);n.push([e.i,".teacher-profile-feedback-tags[data-v-1c41cb86]{margin-bottom:14px}@media only screen and (max-width:991px){.teacher-profile-feedback-tags[data-v-1c41cb86]{margin-bottom:10px}}.teacher-profile-feedback-tags>*[data-v-1c41cb86]{margin-right:10px}.teacher-profile-feedback-tags-label[data-v-1c41cb86]{font-size:16px;font-weight:700}@media only screen and (max-width:991px){.teacher-profile-feedback-tags-label[data-v-1c41cb86]{font-size:14px;font-weight:600}}.teacher-profile-feedback-tags>.chip[data-v-1c41cb86]{margin-bottom:7px}",""]),e.exports=n},2232:function(e,t,c){"use strict";c.r(t);var n={name:"FeedbackTags",props:{items:{type:Array,default:function(){return[]}}}},r=(c(2071),c(22)),component=Object(r.a)(n,(function(){var e=this,t=e.$createElement,c=e._self._c||t;return e.items.length?c("div",{staticClass:"teacher-profile-feedback-tags"},[c("span",{staticClass:"teacher-profile-feedback-tags-label text--gradient"},[e._v(e._s(e.$t("students_say"))+":")]),e._v(" "),e._l([].concat(e.items).reverse().slice(0,5),(function(e){return c("l-chip",{key:e.tag.id,attrs:{label:e.tag.name+" ("+e.count+")","close-btn":!1,light:""}})}))],2):e._e()}),[],!1,null,"1c41cb86",null);t.default=component.exports;installComponents(component,{LChip:c(264).default})}}]);