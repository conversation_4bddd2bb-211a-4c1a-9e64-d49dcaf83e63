{"version": 3, "file": "pages/user/messages/_recipientId/new/index.js", "sources": ["webpack:///./pages/user/messages/_recipientId/new/index.vue", "webpack:///./pages/user/messages/_recipientId/new/index.vue?addb", "webpack:///./pages/user/messages/_recipientId/new/index.vue?e2b2"], "sourcesContent": ["\nexport default {\n  middleware: ['authenticated', 'threadExisted'],\n}\n", "import mod from \"-!../../../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../node_modules/babel-loader/lib/index.js??ref--2-0!../../../../../node_modules/@nuxt/components/dist/loader.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "var render, staticRenderFns\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  \"646f3464\"\n  \n)\n\nexport default component.exports"], "mappings": ";;;;;;;;;;;AACA;AACA;AADA;;ACDA;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;A", "sourceRoot": ""}