exports.ids = [148,33,34,35,62,98,99,100,101,124];
exports.modules = {

/***/ 1041:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConfirmDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(982);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConfirmDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConfirmDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConfirmDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConfirmDialog_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1042:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".remove-illustration-title{font-size:20px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1047:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/LoadMoreBtn.vue?vue&type=template&id=34f0bc91&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-btn',{class:['load-more-btn', { 'load-more-btn--large': _vm.large }],attrs:{"text":"","width":"100%"},on:{"click":_vm.fetchFunc}},[_c('div',{staticClass:"load-more-btn-icon mr-1"},[_c('svg',{attrs:{"viewBox":"0 0 17 12"}},[_c('use',{attrs:{"xlink:href":((__webpack_require__(14)) + "#arrow-prev")}})])]),_vm._v("\n  "+_vm._s(_vm.textBtn)+"\n")])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/LoadMoreBtn.vue?vue&type=template&id=34f0bc91&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/LoadMoreBtn.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var LoadMoreBtnvue_type_script_lang_js_ = ({
  name: 'LoadMoreBtn',
  props: {
    large: {
      type: Boolean,
      default: false
    },
    textBtn: {
      type: String,
      required: true
    },
    fetchFunc: {
      type: Function,
      required: true
    }
  }
});
// CONCATENATED MODULE: ./components/LoadMoreBtn.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_LoadMoreBtnvue_type_script_lang_js_ = (LoadMoreBtnvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// CONCATENATED MODULE: ./components/LoadMoreBtn.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1157)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_LoadMoreBtnvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "34f0bc91",
  "59b3c299"
  
)

/* harmony default export */ var LoadMoreBtn = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */


installComponents_default()(component, {VBtn: VBtn["a" /* default */]})


/***/ }),

/***/ 1059:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1060);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("83ff91dc", content, true)

/***/ }),

/***/ 1060:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-file-input .v-file-input__text{color:rgba(0,0,0,.87)}.theme--light.v-file-input .v-file-input__text--placeholder{color:rgba(0,0,0,.6)}.theme--light.v-file-input.v-input--is-disabled .v-file-input__text,.theme--light.v-file-input.v-input--is-disabled .v-file-input__text .v-file-input__text--placeholder{color:rgba(0,0,0,.38)}.theme--dark.v-file-input .v-file-input__text{color:#fff}.theme--dark.v-file-input .v-file-input__text--placeholder{color:hsla(0,0%,100%,.7)}.theme--dark.v-file-input.v-input--is-disabled .v-file-input__text,.theme--dark.v-file-input.v-input--is-disabled .v-file-input__text .v-file-input__text--placeholder{color:hsla(0,0%,100%,.5)}.v-file-input input[type=file]{left:0;opacity:0;pointer-events:none;position:absolute;max-width:0;width:0}.v-file-input .v-file-input__text{align-items:center;align-self:stretch;display:flex;flex-wrap:wrap;width:100%}.v-file-input .v-file-input__text.v-file-input__text--chips{flex-wrap:wrap}.v-file-input .v-file-input__text .v-chip{margin:4px}.v-file-input .v-text-field__slot{min-height:32px}.v-file-input.v-input--dense .v-text-field__slot{min-height:26px}.v-file-input.v-text-field--filled:not(.v-text-field--single-line) .v-file-input__text{padding-top:22px}.v-file-input.v-text-field--outlined .v-text-field__slot{padding:6px 0}.v-file-input.v-text-field--outlined.v-input--dense .v-text-field__slot{padding:3px 0}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1072:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _mixins_groupable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(47);
/* harmony import */ var _mixins_registrable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(0);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2);
// Mixins

 // Utilities



/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(Object(_mixins_groupable__WEBPACK_IMPORTED_MODULE_0__[/* factory */ "a"])('expansionPanels', 'v-expansion-panel', 'v-expansion-panels'), Object(_mixins_registrable__WEBPACK_IMPORTED_MODULE_1__[/* provide */ "b"])('expansionPanel', true)
/* @vue/component */
).extend({
  name: 'v-expansion-panel',
  props: {
    disabled: Boolean,
    readonly: Boolean
  },

  data() {
    return {
      content: null,
      header: null,
      nextIsActive: false
    };
  },

  computed: {
    classes() {
      return {
        'v-expansion-panel--active': this.isActive,
        'v-expansion-panel--next-active': this.nextIsActive,
        'v-expansion-panel--disabled': this.isDisabled,
        ...this.groupClasses
      };
    },

    isDisabled() {
      return this.expansionPanels.disabled || this.disabled;
    },

    isReadonly() {
      return this.expansionPanels.readonly || this.readonly;
    }

  },
  methods: {
    registerContent(vm) {
      this.content = vm;
    },

    unregisterContent() {
      this.content = null;
    },

    registerHeader(vm) {
      this.header = vm;
      vm.$on('click', this.onClick);
    },

    unregisterHeader() {
      this.header = null;
    },

    onClick(e) {
      if (e.detail) this.header.$el.blur();
      this.$emit('click', e);
      this.isReadonly || this.isDisabled || this.toggle();
    },

    toggle() {
      /* istanbul ignore else */
      if (this.content) this.content.isBooted = true;
      this.$nextTick(() => this.$emit('change'));
    }

  },

  render(h) {
    return h('div', {
      staticClass: 'v-expansion-panel',
      class: this.classes,
      attrs: {
        'aria-expanded': String(this.isActive)
      }
    }, Object(_util_helpers__WEBPACK_IMPORTED_MODULE_2__[/* getSlot */ "n"])(this));
  }

}));

/***/ }),

/***/ 1073:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _transitions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67);
/* harmony import */ var _mixins_bootable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(103);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(9);
/* harmony import */ var _mixins_registrable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(29);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(0);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(2);
 // Mixins



 // Utilities



const baseMixins = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_5__[/* default */ "a"])(_mixins_bootable__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], _mixins_colorable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"], Object(_mixins_registrable__WEBPACK_IMPORTED_MODULE_3__[/* inject */ "a"])('expansionPanel', 'v-expansion-panel-content', 'v-expansion-panel'));
/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (baseMixins.extend().extend({
  name: 'v-expansion-panel-content',
  computed: {
    isActive() {
      return this.expansionPanel.isActive;
    }

  },

  created() {
    this.expansionPanel.registerContent(this);
  },

  beforeDestroy() {
    this.expansionPanel.unregisterContent();
  },

  render(h) {
    return h(_transitions__WEBPACK_IMPORTED_MODULE_0__[/* VExpandTransition */ "a"], this.showLazyContent(() => [h('div', this.setBackgroundColor(this.color, {
      staticClass: 'v-expansion-panel-content',
      directives: [{
        name: 'show',
        value: this.isActive
      }]
    }), [h('div', {
      class: 'v-expansion-panel-content__wrap'
    }, Object(_util_helpers__WEBPACK_IMPORTED_MODULE_4__[/* getSlot */ "n"])(this))])]));
  }

}));

/***/ }),

/***/ 1074:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _transitions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(67);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(66);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(9);
/* harmony import */ var _mixins_registrable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(29);
/* harmony import */ var _directives_ripple__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(22);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(0);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(2);
// Components

 // Mixins


 // Directives

 // Utilities



const baseMixins = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"])(_mixins_colorable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"], Object(_mixins_registrable__WEBPACK_IMPORTED_MODULE_3__[/* inject */ "a"])('expansionPanel', 'v-expansion-panel-header', 'v-expansion-panel'));
/* harmony default export */ __webpack_exports__["a"] = (baseMixins.extend().extend({
  name: 'v-expansion-panel-header',
  directives: {
    ripple: _directives_ripple__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"]
  },
  props: {
    disableIconRotate: Boolean,
    expandIcon: {
      type: String,
      default: '$expand'
    },
    hideActions: Boolean,
    ripple: {
      type: [Boolean, Object],
      default: false
    }
  },
  data: () => ({
    hasMousedown: false
  }),
  computed: {
    classes() {
      return {
        'v-expansion-panel-header--active': this.isActive,
        'v-expansion-panel-header--mousedown': this.hasMousedown
      };
    },

    isActive() {
      return this.expansionPanel.isActive;
    },

    isDisabled() {
      return this.expansionPanel.isDisabled;
    },

    isReadonly() {
      return this.expansionPanel.isReadonly;
    }

  },

  created() {
    this.expansionPanel.registerHeader(this);
  },

  beforeDestroy() {
    this.expansionPanel.unregisterHeader();
  },

  methods: {
    onClick(e) {
      this.$emit('click', e);
    },

    genIcon() {
      const icon = Object(_util_helpers__WEBPACK_IMPORTED_MODULE_5__[/* getSlot */ "n"])(this, 'actions') || [this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], this.expandIcon)];
      return this.$createElement(_transitions__WEBPACK_IMPORTED_MODULE_0__[/* VFadeTransition */ "d"], [this.$createElement('div', {
        staticClass: 'v-expansion-panel-header__icon',
        class: {
          'v-expansion-panel-header__icon--disable-rotate': this.disableIconRotate
        },
        directives: [{
          name: 'show',
          value: !this.isDisabled
        }]
      }, icon)]);
    }

  },

  render(h) {
    return h('button', this.setBackgroundColor(this.color, {
      staticClass: 'v-expansion-panel-header',
      class: this.classes,
      attrs: {
        tabindex: this.isDisabled ? -1 : null,
        type: 'button'
      },
      directives: [{
        name: 'ripple',
        value: this.ripple
      }],
      on: { ...this.$listeners,
        click: this.onClick,
        mousedown: () => this.hasMousedown = true,
        mouseup: () => this.hasMousedown = false
      }
    }), [Object(_util_helpers__WEBPACK_IMPORTED_MODULE_5__[/* getSlot */ "n"])(this, 'default', {
      open: this.isActive
    }, true), this.hideActions || this.genIcon()]);
  }

}));

/***/ }),

/***/ 1092:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VExpansionPanel_VExpansionPanel_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(969);
/* harmony import */ var _src_components_VExpansionPanel_VExpansionPanel_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VExpansionPanel_VExpansionPanel_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(902);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(3);
// Styles
 // Components

 // Utilities


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (_VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_1__[/* BaseItemGroup */ "a"].extend({
  name: 'v-expansion-panels',

  provide() {
    return {
      expansionPanels: this
    };
  },

  props: {
    accordion: Boolean,
    disabled: Boolean,
    flat: Boolean,
    hover: Boolean,
    focusable: Boolean,
    inset: Boolean,
    popout: Boolean,
    readonly: Boolean,
    tile: Boolean
  },
  computed: {
    classes() {
      return { ..._VItemGroup_VItemGroup__WEBPACK_IMPORTED_MODULE_1__[/* BaseItemGroup */ "a"].options.computed.classes.call(this),
        'v-expansion-panels': true,
        'v-expansion-panels--accordion': this.accordion,
        'v-expansion-panels--flat': this.flat,
        'v-expansion-panels--hover': this.hover,
        'v-expansion-panels--focusable': this.focusable,
        'v-expansion-panels--inset': this.inset,
        'v-expansion-panels--popout': this.popout,
        'v-expansion-panels--tile': this.tile
      };
    }

  },

  created() {
    /* istanbul ignore next */
    if (this.$attrs.hasOwnProperty('expand')) {
      Object(_util_console__WEBPACK_IMPORTED_MODULE_2__[/* breaking */ "a"])('expand', 'multiple', this);
    }
    /* istanbul ignore next */


    if (Array.isArray(this.value) && this.value.length > 0 && typeof this.value[0] === 'boolean') {
      Object(_util_console__WEBPACK_IMPORTED_MODULE_2__[/* breaking */ "a"])(':value="[true, false, true]"', ':value="[0, 2]"', this);
    }
  },

  methods: {
    updateItem(item, index) {
      const value = this.getValue(item, index);
      const nextValue = this.getValue(item, index + 1);
      item.isActive = this.toggleMethod(value);
      item.nextIsActive = this.toggleMethod(nextValue);
    }

  }
}));

/***/ }),

/***/ 1097:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1158);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("45e472a6", content, true, context)
};

/***/ }),

/***/ 1098:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1160);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("7337ad18", content, true, context)
};

/***/ }),

/***/ 1138:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1181);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("c2cacb64", content, true, context)
};

/***/ }),

/***/ 1139:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1183);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("79c53e1f", content, true, context)
};

/***/ }),

/***/ 1157:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LoadMoreBtn_vue_vue_type_style_index_0_id_34f0bc91_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1097);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LoadMoreBtn_vue_vue_type_style_index_0_id_34f0bc91_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LoadMoreBtn_vue_vue_type_style_index_0_id_34f0bc91_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LoadMoreBtn_vue_vue_type_style_index_0_id_34f0bc91_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LoadMoreBtn_vue_vue_type_style_index_0_id_34f0bc91_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1158:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".load-more-btn[data-v-34f0bc91]{color:var(--v-greyDark-base)}.load-more-btn-icon[data-v-34f0bc91]{display:flex;align-items:center;justify-content:center;width:38px;height:38px;color:#fff;border-radius:50%;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%)}.load-more-btn-icon svg[data-v-34f0bc91]{width:19px;height:14px;transform:rotate(-90deg)}.load-more-btn--large[data-v-34f0bc91]{height:52px!important}.load-more-btn--large .load-more-btn-icon[data-v-34f0bc91]{width:52px;height:52px}.load-more-btn--large .load-more-btn-icon svg[data-v-34f0bc91]{width:26px;height:16px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1159:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConversationItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1098);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConversationItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConversationItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConversationItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ConversationItem_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1160:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".conversation-item{display:flex;width:100%}.conversation-item>div{width:calc(100% - 60px);max-width:462px}@media only screen and (max-width:639px){.conversation-item>div{width:calc(100% - 45px)}}@media only screen and (max-width:479px){.conversation-item>div{width:calc(100% - 20px)}}.conversation-item-header{display:flex;align-items:flex-end;margin-bottom:2px;font-size:13px;color:var(--v-greyLight-darken2);line-height:1.23}@media only screen and (max-width:479px){.conversation-item-header{font-size:12px}}.conversation-item-header-avatar{position:relative;margin-bottom:2px;filter:drop-shadow(0 4px 5px rgba(0,0,0,.2))}@media only screen and (max-width:479px){.conversation-item-header-avatar .v-avatar{width:42px!important;min-width:42px!important;height:42px!important}}.conversation-item-header>div:not(.conversation-item-header-avatar){margin-left:10px}.conversation-item-body{padding:16px 12px;font-size:14px;line-height:1.4;border-radius:16px;color:var(--v-greyLight-darken4);background:linear-gradient(126.15deg,rgba(128,182,34,.18),rgba(60,135,248,.18) 102.93%)}@media only screen and (max-width:1439px){.conversation-item-body{padding:14px 12px}}.conversation-item-body a{color:var(--v-greyLight-darken4)!important;transition:color .3s}.conversation-item-body a span{display:inline-block;font-size:20px}.conversation-item-body a:hover{color:var(--v-success-base)!important}.conversation-item-body.conversation-item-body--file a{text-decoration:none}.conversation-item-body ul{padding-left:20px}.conversation-item-body ul li{margin-bottom:0}.conversation-item-body ul li p{min-height:16px;margin-bottom:0}.conversation-item-body>div{word-wrap:break-word}.conversation-item-body>div>*{min-height:16px;margin-bottom:0}.conversation-item-body>div>:last-child{min-height:0}.conversation-item-footer{display:flex;justify-content:flex-end;align-items:center;margin-top:2px;padding-right:10px;font-size:13px;color:var(--v-greyLight-darken2);line-height:1.23}@media only screen and (max-width:479px){.conversation-item-footer{font-size:12px}}.conversation-item--mine{justify-content:flex-end}.conversation-item--mine .conversation-item-header{flex-direction:row-reverse}.conversation-item--mine .conversation-item-header-avatar{margin-right:0;margin-left:12px}.conversation-item--mine .conversation-item-footer{padding-right:0}.conversation-item--mine+.conversation-item--mine{margin-top:10px}@media only screen and (max-width:479px){.conversation-item--mine+.conversation-item--mine{margin-top:6px}}.conversation-item--mine+.conversation-item--mine .conversation-item-header-avatar{display:none}.conversation-item--other+.conversation-item--other{margin-top:10px}@media only screen and (max-width:479px){.conversation-item--other+.conversation-item--other{margin-top:6px}}.conversation-item--other+.conversation-item--other .conversation-item-header-avatar{display:none}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1163:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VFileInput_VFileInput_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1059);
/* harmony import */ var _src_components_VFileInput_VFileInput_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VFileInput_VFileInput_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _VTextField__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(853);
/* harmony import */ var _VChip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(901);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(0);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3);
/* harmony import */ var _util_mergeData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(15);
// Styles
 // Extensions

 // Components

 // Utilities




/* harmony default export */ __webpack_exports__["a"] = (_VTextField__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].extend({
  name: 'v-file-input',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    chips: Boolean,
    clearable: {
      type: Boolean,
      default: true
    },
    counterSizeString: {
      type: String,
      default: '$vuetify.fileInput.counterSize'
    },
    counterString: {
      type: String,
      default: '$vuetify.fileInput.counter'
    },
    hideInput: Boolean,
    placeholder: String,
    prependIcon: {
      type: String,
      default: '$file'
    },
    readonly: {
      type: Boolean,
      default: false
    },
    showSize: {
      type: [Boolean, Number],
      default: false,
      validator: v => {
        return typeof v === 'boolean' || [1000, 1024].includes(v);
      }
    },
    smallChips: Boolean,
    truncateLength: {
      type: [Number, String],
      default: 22
    },
    type: {
      type: String,
      default: 'file'
    },
    value: {
      default: undefined,
      validator: val => {
        return Object(_util_helpers__WEBPACK_IMPORTED_MODULE_3__[/* wrapInArray */ "y"])(val).every(v => v != null && typeof v === 'object');
      }
    }
  },
  computed: {
    classes() {
      return { ..._VTextField__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.computed.classes.call(this),
        'v-file-input': true
      };
    },

    computedCounterValue() {
      const fileCount = this.isMultiple && this.lazyValue ? this.lazyValue.length : this.lazyValue instanceof File ? 1 : 0;
      if (!this.showSize) return this.$vuetify.lang.t(this.counterString, fileCount);
      const bytes = this.internalArrayValue.reduce((bytes, {
        size = 0
      }) => {
        return bytes + size;
      }, 0);
      return this.$vuetify.lang.t(this.counterSizeString, fileCount, Object(_util_helpers__WEBPACK_IMPORTED_MODULE_3__[/* humanReadableFileSize */ "q"])(bytes, this.base === 1024));
    },

    internalArrayValue() {
      return Object(_util_helpers__WEBPACK_IMPORTED_MODULE_3__[/* wrapInArray */ "y"])(this.internalValue);
    },

    internalValue: {
      get() {
        return this.lazyValue;
      },

      set(val) {
        this.lazyValue = val;
        this.$emit('change', this.lazyValue);
      }

    },

    isDirty() {
      return this.internalArrayValue.length > 0;
    },

    isLabelActive() {
      return this.isDirty;
    },

    isMultiple() {
      return this.$attrs.hasOwnProperty('multiple');
    },

    text() {
      if (!this.isDirty && (this.isFocused || !this.hasLabel)) return [this.placeholder];
      return this.internalArrayValue.map(file => {
        const {
          name = '',
          size = 0
        } = file;
        const truncatedText = this.truncateText(name);
        return !this.showSize ? truncatedText : `${truncatedText} (${Object(_util_helpers__WEBPACK_IMPORTED_MODULE_3__[/* humanReadableFileSize */ "q"])(size, this.base === 1024)})`;
      });
    },

    base() {
      return typeof this.showSize !== 'boolean' ? this.showSize : undefined;
    },

    hasChips() {
      return this.chips || this.smallChips;
    }

  },
  watch: {
    readonly: {
      handler(v) {
        if (v === true) Object(_util_console__WEBPACK_IMPORTED_MODULE_4__[/* consoleError */ "b"])('readonly is not supported on <v-file-input>', this);
      },

      immediate: true
    },

    value(v) {
      const value = this.isMultiple ? v : v ? [v] : [];

      if (!Object(_util_helpers__WEBPACK_IMPORTED_MODULE_3__[/* deepEqual */ "h"])(value, this.$refs.input.files)) {
        // When the input value is changed programatically, clear the
        // internal input's value so that the `onInput` handler
        // can be triggered again if the user re-selects the exact
        // same file(s). Ideally, `input.files` should be
        // manipulated directly but that property is readonly.
        this.$refs.input.value = '';
      }
    }

  },
  methods: {
    clearableCallback() {
      this.internalValue = this.isMultiple ? [] : null;
      this.$refs.input.value = '';
    },

    genChips() {
      if (!this.isDirty) return [];
      return this.text.map((text, index) => this.$createElement(_VChip__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"], {
        props: {
          small: this.smallChips
        },
        on: {
          'click:close': () => {
            const internalValue = this.internalValue;
            internalValue.splice(index, 1);
            this.internalValue = internalValue; // Trigger the watcher
          }
        }
      }, [text]));
    },

    genControl() {
      const render = _VTextField__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.genControl.call(this);

      if (this.hideInput) {
        render.data.style = Object(_util_mergeData__WEBPACK_IMPORTED_MODULE_5__[/* mergeStyles */ "c"])(render.data.style, {
          display: 'none'
        });
      }

      return render;
    },

    genInput() {
      const input = _VTextField__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.genInput.call(this); // We should not be setting value
      // programmatically on the input
      // when it is using type="file"

      delete input.data.domProps.value; // This solves an issue in Safari where
      // nothing happens when adding a file
      // do to the input event not firing
      // https://github.com/vuetifyjs/vuetify/issues/7941

      delete input.data.on.input;
      input.data.on.change = this.onInput;
      return [this.genSelections(), input];
    },

    genPrependSlot() {
      if (!this.prependIcon) return null;
      const icon = this.genIcon('prepend', () => {
        this.$refs.input.click();
      });
      return this.genSlot('prepend', 'outer', [icon]);
    },

    genSelectionText() {
      const length = this.text.length;
      if (length < 2) return this.text;
      if (this.showSize && !this.counter) return [this.computedCounterValue];
      return [this.$vuetify.lang.t(this.counterString, length)];
    },

    genSelections() {
      const children = [];

      if (this.isDirty && this.$scopedSlots.selection) {
        this.internalArrayValue.forEach((file, index) => {
          if (!this.$scopedSlots.selection) return;
          children.push(this.$scopedSlots.selection({
            text: this.text[index],
            file,
            index
          }));
        });
      } else {
        children.push(this.hasChips && this.isDirty ? this.genChips() : this.genSelectionText());
      }

      return this.$createElement('div', {
        staticClass: 'v-file-input__text',
        class: {
          'v-file-input__text--placeholder': this.placeholder && !this.isDirty,
          'v-file-input__text--chips': this.hasChips && !this.$scopedSlots.selection
        }
      }, children);
    },

    genTextFieldSlot() {
      const node = _VTextField__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"].options.methods.genTextFieldSlot.call(this);
      node.data.on = { ...(node.data.on || {}),
        click: () => this.$refs.input.click()
      };
      return node;
    },

    onInput(e) {
      const files = [...(e.target.files || [])];
      this.internalValue = this.isMultiple ? files : files[0]; // Set initialValue here otherwise isFocused
      // watcher in VTextField will emit a change
      // event whenever the component is blurred

      this.initialValue = this.internalValue;
    },

    onKeyDown(e) {
      this.$emit('keydown', e);
    },

    truncateText(str) {
      if (str.length < Number(this.truncateLength)) return str;
      const charsKeepOneSide = Math.floor((Number(this.truncateLength) - 1) / 2);
      return `${str.slice(0, charsKeepOneSide)}…${str.slice(str.length - charsKeepOneSide)}`;
    }

  }
}));

/***/ }),

/***/ 1164:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1217);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("5aec5f3e", content, true, context)
};

/***/ }),

/***/ 1172:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-messages/ConversationItem.vue?vue&type=template&id=1e96ee73&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[
    'conversation-item',
    { 'conversation-item--mine': _vm.isCurrentUser },
    { 'conversation-item--other': !_vm.isCurrentUser } ]},[_vm._ssrNode("<div>","</div>",[_vm._ssrNode("<div class=\"conversation-item-header\">","</div>",[_vm._ssrNode("<div class=\"conversation-item-header-avatar\">","</div>",[_c('v-avatar',{attrs:{"width":"52","height":"52"}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(_vm.avatars, 'user_thumb_52x52'),"srcset":_vm.getSrcSetAvatar(
                _vm.avatars,
                'user_thumb_52x52',
                'user_thumb_104x104'
              ),"options":{ rootMargin: '50%' }}})],1),_vm._ssrNode(" "),_c('user-status',{attrs:{"user-id":_vm.item.authorId,"user-statuses":_vm.userStatuses}})],2),_vm._ssrNode(" <div>"+((_vm.isCurrentUser)?(_vm._ssrEscape("\n          "+_vm._s(_vm.$t('sent_by_me_on'))+"\n        ")):(_vm._ssrEscape("\n          "+_vm._s(_vm.$t('sent_by_somebody_on', {
              username: _vm.recipientName,
            }))+"\n        ")))+_vm._ssrEscape("\n        "+_vm._s(_vm.$dayjs(_vm.item.createDate).tz(_vm.timeZone).format('ll, LT'))+"\n      ")+"</div>")],2),_vm._ssrNode(" <div"+(_vm._ssrClass(null,[
        'conversation-item-body',
        { 'conversation-item-body--file': _vm.item.isFile } ]))+">"+((!_vm.item.isFile)?("<div>"+(_vm._s(_vm.item.text))+"</div>"):("<a"+(_vm._ssrAttr("href",_vm.fileUrl))+" download><span class=\"mr-1\">📎</span>"+_vm._ssrEscape(_vm._s(_vm.item.text)+"\n        ")+"</a>"))+"</div> "),_vm._ssrNode("<div class=\"conversation-item-footer\">","</div>",[_vm._ssrNode(((_vm.item.readDate)?(_vm._ssrEscape(_vm._s(_vm.$t('seen'))+"\n        "+_vm._s(_vm.$dayjs(_vm.item.readDate).tz(_vm.timeZone).format('ll, LT')))):(_vm._ssrEscape(_vm._s(_vm.$t('not_yet_seen')))))+" "),(_vm.isCurrentUser)?_c('v-btn',{attrs:{"text":"","x-small":"","height":"16","color":"grey"},on:{"click":function($event){_vm.isShownMessageConfirmDialog = true}}},[_vm._v("\n        ("),_c('span',{staticClass:"text-decoration-underline"},[_vm._v(_vm._s(_vm.$t('delete_message')))]),_vm._v(")")]):_vm._e()],2)],2),_vm._ssrNode(" "),_c('confirm-dialog',{attrs:{"is-shown-confirm-dialog":_vm.isShownMessageConfirmDialog,"cancel-text-button":"no","confirm-text-button":"yes"},on:{"confirm":_vm.removeMessage,"close-dialog":function($event){_vm.isShownMessageConfirmDialog = false}}},[_vm._v("\n    "+_vm._s(_vm.$t('are_you_sure_you_want_to_delete_this_message_permanently'))+"\n  ")])],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-messages/ConversationItem.vue?vue&type=template&id=1e96ee73&

// EXTERNAL MODULE: ./components/ConfirmDialog.vue + 4 modules
var ConfirmDialog = __webpack_require__(985);

// EXTERNAL MODULE: ./mixins/Avatars.vue + 2 modules
var Avatars = __webpack_require__(932);

// EXTERNAL MODULE: ./components/UserStatus.vue + 4 modules
var UserStatus = __webpack_require__(916);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-messages/ConversationItem.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//



/* harmony default export */ var ConversationItemvue_type_script_lang_js_ = ({
  name: 'ConversationItem',
  components: {
    UserStatus: UserStatus["default"],
    ConfirmDialog: ConfirmDialog["default"]
  },
  mixins: [Avatars["a" /* default */]],
  props: {
    item: {
      type: Object,
      required: true
    },
    threadId: {
      type: Number,
      required: true
    },
    recipientName: {
      type: String,
      required: true
    },
    recipientAvatars: {
      type: Object,
      required: true
    },
    userAvatars: {
      type: Object,
      required: true
    },
    userStatuses: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      isShownMessageConfirmDialog: false
    };
  },

  computed: {
    userId() {
      var _this$$store$state$us;

      return (_this$$store$state$us = this.$store.state.user.item) === null || _this$$store$state$us === void 0 ? void 0 : _this$$store$state$us.id;
    },

    isCurrentUser() {
      return this.userId === this.item.authorId;
    },

    avatars() {
      return this.isCurrentUser ? this.userAvatars : this.recipientAvatars;
    },

    fileUrl() {
      return `${"'http://localhost:3000'"}/messages/file/${this.item.id}`;
    },

    timeZone() {
      return this.$store.getters['user/timeZone'];
    }

  },
  methods: {
    async downloadClickHandler() {
      await this.$axios({
        url: this.fileUrl,
        method: 'GET',
        responseType: 'blob'
      }).then(response => {
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', this.item.text);
        document.body.appendChild(link);
        link.click();
      }).catch(() => console.info('Download error'));
    },

    removeMessage() {
      this.isShownMessageConfirmDialog = false;
      this.$store.dispatch('message/removeMessage', this.item.id);
    }

  }
});
// CONCATENATED MODULE: ./components/user-messages/ConversationItem.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_messages_ConversationItemvue_type_script_lang_js_ = (ConversationItemvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/VAvatar.js
var VAvatar = __webpack_require__(830);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-messages/ConversationItem.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1159)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_messages_ConversationItemvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "a09c4348"
  
)

/* harmony default export */ var ConversationItem = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserStatus: __webpack_require__(916).default,ConfirmDialog: __webpack_require__(985).default})


/* vuetify-loader */




installComponents_default()(component, {VAvatar: VAvatar["a" /* default */],VBtn: VBtn["a" /* default */],VImg: VImg["a" /* default */]})


/***/ }),

/***/ 1180:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Conversation_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1138);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Conversation_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Conversation_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Conversation_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Conversation_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1181:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".conversation{padding:30px 44px 138px;background-color:#fff;border-radius:20px}@media only screen and (max-width:1439px){.conversation{padding:24px 24px 60px}}@media only screen and (min-width:768px){.conversation{box-shadow:0 8px 17px rgba(17,46,90,.1)}}@media only screen and (max-width:767px){.conversation{padding:0}}.conversation-header{display:flex;justify-content:space-between}.conversation-header .conversation-avatar{filter:drop-shadow(0 4px 5px rgba(0,0,0,.2))}.conversation-header .conversation-avatar>div,.conversation-header .conversation-avatar>div .v-avatar{position:relative}@media only screen and (max-width:991px){.conversation-header .conversation-avatar>div .v-avatar{width:90px!important;height:90px!important}}@media only screen and (max-width:639px){.conversation-header .conversation-avatar>div .v-avatar{width:80px!important;height:80px!important}}@media only screen and (max-width:479px){.conversation-header .conversation-avatar>div .v-avatar{width:70px!important;height:70px!important}}.conversation-header .conversation-avatar>div .v-avatar a{display:block;position:absolute;top:0;left:0;width:100%;height:100%;z-index:2}.conversation-header .conversation-avatar .user-status{right:3px;bottom:3px}@media only screen and (max-width:639px){.conversation-header .conversation-avatar .user-status{right:1px;bottom:1px}}.conversation-header .conversation-title{font-size:24px;line-height:1.333}@media only screen and (max-width:1439px){.conversation-header .conversation-title{font-size:20px}}@media only screen and (max-width:767px){.conversation-header .conversation-title{font-size:18px}}.conversation-header .conversation-details{font-size:13px;line-height:1.23;color:var(--v-greyLight-darken2)}@media only screen and (max-width:767px){.conversation-header .conversation-details{font-size:12px}}@media only screen and (max-width:1439px){.conversation-header .conversation-details>div:not(:last-child){margin-bottom:4px}}.conversation-header .conversation-details a{color:var(--v-grey-base)}.conversation-header .conversation-details a:hover{color:var(--v-green-base);transition:all .3s}.conversation-body .new-message-label{font-size:16px}.conversation-body .new-message .text-editor .ProseMirror{min-height:118px}.conversation-body .new-message .text-editor .text-editor-buttons{left:8px;right:auto}.conversation-body .new-message-notice{margin-bottom:4px;font-size:12px;color:var(--v-greyLight-darken2);line-height:1.2}@media only screen and (max-width:1439px){.conversation-body .new-message-notice{margin-bottom:8px}}.conversation-body .new-message-bottom{display:flex;flex-wrap:wrap;justify-content:space-between;align-items:center}.conversation-body .new-message-bottom .upload-file-name{display:flex;align-items:center}.conversation-body .new-message-bottom-buttons{width:100%;display:flex;flex-wrap:wrap;justify-content:flex-end}@media only screen and (max-width:479px){.conversation-body .new-message-bottom-buttons{justify-content:space-around}}.conversation-body .new-message-bottom-buttons .v-btn .v-btn__content{white-space:nowrap}.conversation-body .messages-list{display:flex;flex-direction:column}.conversation-body .conversation-show-more-btn{max-width:240px;margin-left:auto;margin-right:auto}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1182:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_EmptyContent_vue_vue_type_style_index_0_id_58e2b6d0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1139);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_EmptyContent_vue_vue_type_style_index_0_id_58e2b6d0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_EmptyContent_vue_vue_type_style_index_0_id_58e2b6d0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_EmptyContent_vue_vue_type_style_index_0_id_58e2b6d0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_EmptyContent_vue_vue_type_style_index_0_id_58e2b6d0_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1183:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".messages-empty-content[data-v-58e2b6d0]{padding:30px 44px 138px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1);border-radius:20px;line-height:1.4}@media only screen and (max-width:1439px){.messages-empty-content[data-v-58e2b6d0]{padding:24px 24px 60px}}@media only screen and (min-width:768px){.messages-empty-content[data-v-58e2b6d0]{min-height:620px}}@media only screen and (max-width:479px){.messages-empty-content[data-v-58e2b6d0]{padding:24px 15px 30px}}.messages-empty-content-title[data-v-58e2b6d0]{font-size:24px}@media only screen and (max-width:991px){.messages-empty-content-title[data-v-58e2b6d0]{font-size:20px}}.messages-empty-content-text[data-v-58e2b6d0]{font-size:18px}@media only screen and (max-width:991px){.messages-empty-content-text[data-v-58e2b6d0]{font-size:16px}}.messages-empty-content-text a[data-v-58e2b6d0]{color:var(--v-success-base);background:linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));background:-webkit-linear-gradient(-75deg,var(--v-success-base),var(--v-primary-base));-webkit-background-clip:text;-webkit-text-fill-color:transparent}.messages-empty-content-text ul[data-v-58e2b6d0]{padding-left:32px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1197:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-messages/Conversation.vue?vue&type=template&id=21d9c4c8&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"conversation"},[_vm._ssrNode("<div class=\"conversation-header mb-1\">","</div>",[_vm._ssrNode("<div class=\"mr-1 mr-sm-2\">","</div>",[_vm._ssrNode("<div class=\"conversation-title font-weight-medium\">"+_vm._ssrEscape("\n        "+_vm._s(_vm.$t('messages_with'))+"\n        ")+((!_vm.item.userIsDeleted)?(_vm._ssrEscape("\n          "+_vm._s(_vm.item.firstName)+"\n          "+_vm._s(_vm.item.lastName)+"\n        ")):(_vm._ssrEscape("\n          "+_vm._s(_vm.$t('deleted_user'))+"\n        ")))+"</div> "),(!_vm.item.userIsDeleted)?_vm._ssrNode("<div class=\"conversation-details mt-2\">","</div>",[_vm._ssrNode("<div>","</div>",[_vm._ssrNode(_vm._ssrEscape("\n          "+_vm._s(_vm.$t('next_lesson'))+":\n          ")),_vm._ssrNode("<span class=\"text-no-wrap\">","</span>",[(_vm.hasNextLesson)?[_vm._ssrNode("<span class=\"font-weight-medium\">"+_vm._ssrEscape("\n                "+_vm._s(_vm.$dayjs(_vm.item.nextLessonDate)
                    .tz(_vm.userTimezone)
                    .format('ll, LT'))+"\n              ")+"</span>\n              ("),_c('nuxt-link',{attrs:{"to":_vm.localePath({ path: '/user/lessons' })}},[_vm._v(_vm._s(_vm.$t('see_lessons')))]),_vm._ssrNode(")\n            ")]:_vm._ssrNode("<span class=\"font-weight-medium\">"+_vm._ssrEscape("\n                "+_vm._s(_vm.$t('none_scheduled'))+"\n              ")+"</span>")],2)],2),_vm._ssrNode(" <div>"+_vm._ssrEscape("\n          "+_vm._s(_vm.$t('current_local_time_for'))+"\n          "+_vm._s(_vm.item.firstName)+":\n          ")+"<span class=\"text-no-wrap\"><span class=\"font-weight-medium\">"+_vm._ssrEscape("\n              "+_vm._s(_vm.$dayjs().tz(_vm.item.recipientTimeZone).format('LT'))+"\n            ")+"</span>"+_vm._ssrEscape("\n            ("+_vm._s(_vm.$dayjs().tz(_vm.item.recipientTimeZone).format('z'))+")\n          ")+"</span></div> <div>"+_vm._ssrEscape("\n          "+_vm._s(_vm.$t('last_online'))+":\n          ")+"<span class=\"font-weight-medium text-no-wrap\">"+((_vm.status === 'online')?(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('online_now'))+"\n            ")):(_vm.status === 'idle')?(_vm._ssrEscape("\n              "+_vm._s(_vm.$t('online_but_idle'))+"\n            ")):(_vm._ssrEscape("\n              "+_vm._s(_vm.$dayjs(_vm.item.lastLoginDate).tz(_vm.userTimezone).format('ll, LT'))+"\n            ")))+"</span></div>")],2):_vm._e()],2),_vm._ssrNode(" "),(!_vm.item.userIsDeleted)?_vm._ssrNode("<div class=\"conversation-avatar\">","</div>",[_vm._ssrNode("<div>","</div>",[_c('v-avatar',{attrs:{"width":"118","height":"118"}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(_vm.item.recipientAvatars, 'user_thumb_118x118'),"srcset":_vm.getSrcSetAvatar(
                _vm.item.recipientAvatars,
                'user_thumb_118x118',
                'user_thumb_236x236'
              ),"options":{ rootMargin: '50%' }}}),_vm._v(" "),(_vm.recipientLink)?_c('nuxt-link',{attrs:{"to":_vm.recipientLink}}):_vm._e()],1),_vm._ssrNode(" "),_c('user-status',{attrs:{"user-id":_vm.item.userId,"user-statuses":_vm.userStatuses,"large":""}})],2)]):_vm._e()],2),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"conversation-body\">","</div>",[(!_vm.item.userIsDeleted)?_vm._ssrNode("<div class=\"new-message\">","</div>",[_vm._ssrNode("<div class=\"new-message-label font-weight-medium mb-1\">"+_vm._ssrEscape("\n        "+_vm._s(_vm.$t('write_new_message'))+" 🖋️\n      ")+"</div> "),_c('editor',{attrs:{"value":_vm.newMessage,"limit":6000,"auto-link":""},on:{"update":function($event){_vm.newMessage = $event},"validation":function($event){_vm.isMessageValid = $event},"submit":_vm.submitNewMessageHandler,"keydown":_vm.handleKeydown}}),_vm._ssrNode(" <div class=\"new-message-notice pl-2\">"+_vm._ssrEscape("\n        "+_vm._s(_vm.$t('press_ctrl_enter_to_send'))+"\n      ")+"</div> "),_vm._ssrNode("<div class=\"new-message-bottom\">","</div>",[_vm._ssrNode("<div class=\"new-message-attached-file\">","</div>",[_c('v-file-input',{ref:"fileMessage",staticClass:"d-none",attrs:{"rules":_vm.rules.file,"prepend-icon":""},on:{"change":_vm.uploadFile}})],1),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"new-message-bottom-buttons\">","</div>",[_c('v-btn',{staticClass:"gradient font-weight-medium my-1 ml-1",on:{"click":function($event){_vm.$refs.fileMessage.$el.querySelector('input').click()}}},[_c('div',{staticClass:"text--gradient"},[_vm._v(_vm._s(_vm.$t('attach_document'))+" 📎")])]),_vm._ssrNode(" "),_c('v-btn',{staticClass:"font-weight-medium my-1 ml-1",attrs:{"color":"primary"},on:{"click":_vm.submitNewMessageHandler}},[_vm._v("\n            "+_vm._s(_vm.$t('send'))+" 📬\n          ")])],2)],2)],2):_vm._e(),_vm._ssrNode(" "),_vm._ssrNode("<div class=\"messages-list mt-2 mt-md-3\">","</div>",_vm._l((_vm.messages),function(message){return _c('conversation-item',{key:message.id,attrs:{"item":message,"recipient-name":_vm.recipientName,"recipient-avatars":!_vm.item.userIsDeleted ? _vm.item.recipientAvatars : {},"user-avatars":_vm.item.userAvatars,"thread-id":_vm.threadId,"user-statuses":_vm.userStatuses}})}),1),_vm._ssrNode(" "),(_vm.isMoreButtonShown)?_vm._ssrNode("<div class=\"conversation-show-more-btn mt-2 mt-sm-5\">","</div>",[_c('load-more-btn',{attrs:{"text-btn":_vm.$t('load_more_messages'),"fetch-func":_vm.fetchMessages}})],1):_vm._e()],2)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-messages/Conversation.vue?vue&type=template&id=21d9c4c8&

// EXTERNAL MODULE: ./components/form/Editor.vue + 4 modules
var Editor = __webpack_require__(942);

// EXTERNAL MODULE: ./components/LoadMoreBtn.vue + 4 modules
var LoadMoreBtn = __webpack_require__(1047);

// EXTERNAL MODULE: ./components/user-messages/ConversationItem.vue + 4 modules
var ConversationItem = __webpack_require__(1172);

// EXTERNAL MODULE: ./mixins/Avatars.vue + 2 modules
var Avatars = __webpack_require__(932);

// EXTERNAL MODULE: ./components/UserStatus.vue + 4 modules
var UserStatus = __webpack_require__(916);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-messages/Conversation.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//





/* harmony default export */ var Conversationvue_type_script_lang_js_ = ({
  name: 'Conversation',
  components: {
    Editor: Editor["default"],
    LoadMoreBtn: LoadMoreBtn["default"],
    ConversationItem: ConversationItem["default"],
    UserStatus: UserStatus["default"]
  },
  mixins: [Avatars["a" /* default */]],
  props: {
    item: {
      type: Object,
      required: true
    },
    userStatuses: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      newMessage: '',
      messagesPage: 1,
      isMessageValid: false,
      rules: {
        file: [v => !!v, v => !v || v.size < 10485760 || this.$t('file_size_should_be_less_than', {
          value: '10 MB'
        })]
      }
    };
  },

  computed: {
    threadId() {
      return this.item.threadId;
    },

    userTimezone() {
      return this.$store.getters['user/timeZone'];
    },

    recipientName() {
      return !this.item.userIsDeleted ? this.item.firstName : this.$t('deleted_user');
    },

    recipientLink() {
      return this.item.username ? `/teacher/${this.item.username}` : null;
    },

    hasNextLesson() {
      var _this$item$nextLesson;

      return (_this$item$nextLesson = this.item.nextLessonDate) === null || _this$item$nextLesson === void 0 ? void 0 : _this$item$nextLesson.length;
    },

    status() {
      var _this$item$userId;

      let status = 'offline';

      if (Object.prototype.hasOwnProperty.call(this.userStatuses, (_this$item$userId = this.item.userId) === null || _this$item$userId === void 0 ? void 0 : _this$item$userId.toString())) {
        status = this.userStatuses[this.item.userId];
      }

      return status;
    },

    totalPages() {
      return Math.ceil(this.item.countMessages / "20");
    },

    messages() {
      var _this$item$messages;

      return (_this$item$messages = this.item.messages) !== null && _this$item$messages !== void 0 ? _this$item$messages : [];
    },

    isMoreButtonShown() {
      return this.totalPages > 1 && this.messagesPage < this.totalPages;
    }

  },
  watch: {
    threadId() {
      this.messagesPage = 1;
    }

  },

  beforeDestroy() {
    this.newMessage = '';
    this.file = null;
  },

  methods: {
    uploadFile(file) {
      this.$store.dispatch('message/uploadFile', {
        threadId: this.threadId,
        file
      });
    },

    submitNewMessageHandler() {
      if (this.isMessageValid) {
        this.$store.dispatch('message/sendMessage', {
          threadId: this.threadId,
          message: this.newMessage
        }).then(() => {
          this.newMessage = '';
        });
      }
    },

    async fetchMessages() {
      this.messagesPage++;
      await this.$store.dispatch('loadingAllow', false);
      await this.$store.dispatch('message/getConversation', {
        threadId: this.threadId,
        page: this.messagesPage
      });
      await this.$store.dispatch('loadingAllow', true);
    },

    handleKeydown(event) {
      if (event.key === 'Enter' && !event.ctrlKey) {
        event.preventDefault();
        this.newMessage += '\n';
      } else if (event.key === 'Enter' && event.ctrlKey) {
        event.preventDefault();
        this.submitNewMessageHandler();
      }
    }

  }
});
// CONCATENATED MODULE: ./components/user-messages/Conversation.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_messages_Conversationvue_type_script_lang_js_ = (Conversationvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/VAvatar.js
var VAvatar = __webpack_require__(830);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VFileInput/VFileInput.js
var VFileInput = __webpack_require__(1163);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-messages/Conversation.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1180)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_messages_Conversationvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "23391dae"
  
)

/* harmony default export */ var Conversation = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserStatus: __webpack_require__(916).default,LoadMoreBtn: __webpack_require__(1047).default})


/* vuetify-loader */





installComponents_default()(component, {VAvatar: VAvatar["a" /* default */],VBtn: VBtn["a" /* default */],VFileInput: VFileInput["a" /* default */],VImg: VImg["a" /* default */]})


/***/ }),

/***/ 1198:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-messages/EmptyContent.vue?vue&type=template&id=58e2b6d0&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"messages-empty-content"},[_vm._ssrNode("<div class=\"messages-empty-content-title font-weight-medium mb-3 mb-sm-4\" data-v-58e2b6d0>"+_vm._ssrEscape("\n    "+_vm._s(_vm.$t(_vm.isTeacher ? 'no_messages_teacher_yet' : 'no_messages_student_yet'))+"\n    💬\n  ")+"</div> "),_vm._ssrNode("<div class=\"messages-empty-content-text\" data-v-58e2b6d0>","</div>",[(_vm.isTeacher)?[_vm._ssrNode(((_vm.locale === 'pl')?("\n        Kiedy uczeń rezerwuje lekcję próbną, jest poproszony o napisanie\n        krótkiej wiadomości, która pojawi się tutaj.\n        <br data-v-58e2b6d0><br data-v-58e2b6d0>\n        Zanim to się stanie, upewnij się, że Twoja strona profilowa wzbudza\n        jak największe zainteresowanie.\n        <br data-v-58e2b6d0><br data-v-58e2b6d0> <ul class=\"mb-0\" data-v-58e2b6d0><li data-v-58e2b6d0>Dodaj wysokiej jakości wideo na YouTube</li> <li data-v-58e2b6d0>\n            Zwróć uwagę na dodanie przyjaznego zdjęcia profilowego (powinno\n            być jasne i przejrzyste)\n          </li> <li data-v-58e2b6d0>\n            Dodaj Kursy do swojego profilu, aby zaprezentować swoją\n            specjalistyczną wiedzę\n          </li> <li data-v-58e2b6d0>Spraw, żeby Twój profil się wyróżniał!</li></ul> <br data-v-58e2b6d0>\n        Możesz także udostępnić link do swojego profilu nauczyciela. Wyślij go\n        do poprzednich uczniów, opublikuj w mediach społecznościowych lub\n        reklamuj w kanałach lokalnych.\n      "):(_vm.locale === 'es')?("\n        Cuando un estudiante reserva una lección de prueba, debe escribir un\n        mensaje de introducción, que aparecerá aquí.\n        <br data-v-58e2b6d0><br data-v-58e2b6d0>\n        Hasta entonces, asegúrese de que su perfil docente sea lo más\n        atractivo posible.\n        <br data-v-58e2b6d0><br data-v-58e2b6d0> <ul class=\"mb-0\" data-v-58e2b6d0><li data-v-58e2b6d0>Agregar un video de YouTube de alta calidad</li> <li data-v-58e2b6d0>\n            Agregue una foto de perfil de bienvenida (brillante y clara)\n          </li> <li data-v-58e2b6d0>\n            Agregue uno o más cursos a su perfil para mostrar su experiencia\n          </li> <li data-v-58e2b6d0>¡Haz que tu personalidad destaque!</li></ul> <br data-v-58e2b6d0>\n        También puede compartir un enlace a su perfil de maestro. Envíelo a\n        los alumnos anteriores, publíquelo en las redes sociales o publíquelo\n        en los canales locales.\n      "):("\n        When a student books a trial lesson, they must write an intro message,\n        which will appear here. You may also receive questions without a trial\n        booking.\n        <br data-v-58e2b6d0><br data-v-58e2b6d0>\n        Until then, make sure to make your teaching profile as engaging as\n        possible.\n        <br data-v-58e2b6d0><br data-v-58e2b6d0> <ul class=\"mb-0\" data-v-58e2b6d0><li data-v-58e2b6d0>Add a high-quality YouTube video</li> <li data-v-58e2b6d0>Add a welcoming profile photo (bright and clear)</li> <li data-v-58e2b6d0>\n            Add one or more Courses to your profile to showcase your expertise\n          </li> <li data-v-58e2b6d0>Make your personality stand out!</li></ul> <br data-v-58e2b6d0>\n        You can also share a link to your teacher profile. Send it to previous\n        students, post it on social media, or advertise it in local channels.\n      "))+" "),(_vm.teacherSlug)?[_vm._ssrNode("<br data-v-58e2b6d0><br data-v-58e2b6d0> "),(_vm.$vuetify.breakpoint.smAndUp)?[_c('nuxt-link',{attrs:{"to":{ path: ("/teacher/" + _vm.teacherSlug) }}},[_vm._v("\n            "+_vm._s(_vm.profileLink)+"\n          ")])]:[_vm._ssrNode("<div class=\"d-flex align-center\" data-v-58e2b6d0>","</div>",[_vm._ssrNode("<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" class=\"mr-1\" data-v-58e2b6d0><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#user-icon")))+" data-v-58e2b6d0></use></svg> <div data-v-58e2b6d0>"+_vm._ssrEscape(_vm._s(_vm.teacherSlug))+"</div> "),_vm._ssrNode("<div class=\"d-flex align-center text--gradient ml-2\" data-v-58e2b6d0>","</div>",[_c('v-img',{staticClass:"mr-1",attrs:{"src":__webpack_require__(549),"width":"16","height":"16"}}),_vm._ssrNode(" <input type=\"text\" class=\"d-none\" data-v-58e2b6d0> <div data-v-58e2b6d0>"+_vm._ssrEscape("\n                "+_vm._s(_vm.$t('copy_link'))+"\n              ")+"</div>")],2)],2)]]:_vm._e()]:[(_vm.locale === 'pl')?[_vm._ssrNode("\n        Wejdź na stronę\n        "),_c('nuxt-link',{attrs:{"to":"/teacher-listing"}},[_vm._v("\"Znajdź nauczyciela\"")]),_vm._ssrNode(", aby\n        wybrać swojego korepetytora, zarezerwować lekcję próbną lub zapytać\n        nauczyciela jak może pomóc Ci z osiągnięciem Twoich celów\n        językowych.<br data-v-58e2b6d0><br data-v-58e2b6d0>\n        Nie zapomnij odwiedzić naszej strony\n        "),_c('nuxt-link',{attrs:{"to":"/faq"}},[_vm._v("FAQ")]),_vm._ssrNode(", jeśli nie masz pewności, jak\n        korzystać z Langu!\n      ")]:(_vm.locale === 'es')?[_vm._ssrNode("\n        Consulte la página\n        "),_c('nuxt-link',{attrs:{"to":"/teacher-listing"}},[_vm._v("Encontrar un Profesor")]),_vm._ssrNode("\n        para elegir un maestro, reservar una lección de prueba o hacer una\n        pregunta sobre cómo un maestro puede ayudarlo a alcanzar sus\n        objetivos. <br data-v-58e2b6d0><br data-v-58e2b6d0>\n        ¡Y asegúrese de visitar nuestra página\n        "),_c('nuxt-link',{attrs:{"to":"/faq"}},[_vm._v("de preguntas frecuentes")]),_vm._ssrNode(" si no está\n        seguro de cómo funciona Langu!\n      ")]:[_vm._ssrNode("\n        Check out the\n        "),_c('nuxt-link',{attrs:{"to":"/teacher-listing"}},[_vm._v("Find a Teacher")]),_vm._ssrNode(" page to\n        pick a teacher, book a trial lesson, or ask a question about how a\n        teacher can help you achieve your goals. <br data-v-58e2b6d0><br data-v-58e2b6d0>\n        And be sure to visit our "),_c('nuxt-link',{attrs:{"href":"/faq"}},[_vm._v("FAQ")]),_vm._ssrNode(" page\n        if you’re not sure how Langu works!\n      ")]]],2)],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-messages/EmptyContent.vue?vue&type=template&id=58e2b6d0&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-messages/EmptyContent.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var EmptyContentvue_type_script_lang_js_ = ({
  name: 'UserMessagesEmptyContent',
  computed: {
    locale() {
      return this.$i18n.locale;
    },

    isTeacher() {
      return this.$store.getters['user/isTeacher'];
    },

    teacherSlug() {
      return this.$store.getters['user/teacherSlug'];
    },

    profileLink() {
      return this.teacherSlug ? `${"'http://localhost:3000'"}/teacher/${this.teacherSlug}` : null;
    }

  },
  methods: {
    copyLink() {
      try {
        const el = this.$refs.profileLink;
        el.setAttribute('value', this.profileLink);
        el.select();
        el.setSelectionRange(0, 99999);
        navigator.clipboard.writeText(el.value);
        this.$store.dispatch('snackbar/success', {
          successMessage: 'link_copied',
          timeout: 1500
        });
      } catch (e) {
        console.log(e);
      }
    }

  }
});
// CONCATENATED MODULE: ./components/user-messages/EmptyContent.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_messages_EmptyContentvue_type_script_lang_js_ = (EmptyContentvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/user-messages/EmptyContent.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1182)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_messages_EmptyContentvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "58e2b6d0",
  "09c49e92"
  
)

/* harmony default export */ var EmptyContent = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */


installComponents_default()(component, {VImg: VImg["a" /* default */]})


/***/ }),

/***/ 1216:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessagesPage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1164);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessagesPage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessagesPage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessagesPage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_MessagesPage_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 1217:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-messages{--sidebar-width:280px;margin-top:10px}@media only screen and (max-width:991px){.user-messages{--sidebar-width:255px}}.user-messages-wrap{max-width:1030px}@media only screen and (min-width:768px){.user-messages-wrap{display:flex}}.user-messages-wrap>div{width:100%}.user-messages-title{font-size:24px;line-height:1.333}@media only screen and (max-width:479px){.user-messages-title{font-size:20px}}.user-messages .user-status{position:absolute;right:0;bottom:0}.user-messages-content{width:calc(100% - var(--sidebar-width));padding-left:36px}@media only screen and (max-width:991px){.user-messages-content{padding-left:20px}}@media only screen and (max-width:767px){.user-messages-content{width:100%;padding-left:0}}.user-messages-sidebar{width:var(--sidebar-width)}.user-messages-sidebar-sticky{position:sticky;top:80px}.user-messages-sidebar .user-messages-tabs-nav{overflow:hidden}.user-messages-sidebar .user-messages-tabs-nav>div{width:calc(100% + 15px);height:calc(100vh - 198px);padding-right:15px;overflow-y:scroll}.user-messages-sidebar .user-messages-tabs-nav>div .nav-item{margin-bottom:28px}@media only screen and (max-width:991px){.user-messages-sidebar .user-messages-tabs-nav>div .nav-item{margin-bottom:16px}}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn{padding:0 10px 0 26px;border-radius:20px;font-size:18px;background-color:transparent!important}@media only screen and (max-width:991px){.user-messages-sidebar .user-messages-tabs-nav>div .v-btn{padding:0 10px;font-size:16px}}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn__content{justify-content:flex-start;color:var(--v-greyDark-base);text-align:left}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn:before{transition:none!important}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn.active{background:linear-gradient(126.15deg,rgba(128,182,34,.18),rgba(60,135,248,.18) 102.93%)}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn.active .v-btn__content{color:var(--v-dark-base);font-weight:600!important}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn.active:focus:before,.user-messages-sidebar .user-messages-tabs-nav>div .v-btn.active:hover:before{display:none!important}.user-messages-sidebar .user-messages-tabs-nav>div .v-btn:focus:before,.user-messages-sidebar .user-messages-tabs-nav>div .v-btn:hover:before{background:linear-gradient(126.15deg,rgba(128,182,34,.18),rgba(60,135,248,.18) 102.93%);opacity:.6}.user-messages-sidebar-search .v-input,.user-messages-sidebar-search .v-input.v-input--is-focused .v-input__control .v-input__slot:before{border-radius:8px!important}.user-messages-sidebar-show-more-btn{margin:14px 0}.user-messages-sidebar-show-more-btn .v-btn{padding:0 10px 0 26px!important}@media only screen and (max-width:991px){.user-messages-sidebar-show-more-btn .v-btn{padding:0 10px!important}}@media only screen and (min-width:768px){.user-messages-sidebar-show-more-btn .v-btn__content{justify-content:flex-start!important}}@media only screen and (max-width:767px){.user-messages-sidebar-show-more-btn{max-width:280px;margin-left:auto;margin-right:auto}}.user-messages .nav-item-avatar{position:relative;filter:drop-shadow(0 4px 5px rgba(0,0,0,.2));z-index:2}.user-messages .nav-item-avatar .envelop{position:absolute;top:-3px;left:-14px}@media only screen and (max-width:991px){.user-messages .nav-item-avatar .envelop{top:-2px;left:-10px}}.user-messages .tabs-mobile.v-expansion-panels{border-radius:0}.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel{margin-bottom:16px;background-color:transparent!important}.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-header{min-height:48px;padding:0 15px}@media only screen and (max-width:479px){.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-header{padding:0 10px}}.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-header>:not(.v-expansion-panel-header__icon){flex-grow:0}.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-header--active{border-radius:20px;background:linear-gradient(126.15deg,rgba(128,182,34,.18),rgba(60,135,248,.18) 102.93%)}.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-content{margin-top:12px;background-color:#fff;box-shadow:0 8px 17px rgba(17,46,90,.1);border-radius:20px}.user-messages .tabs-mobile.v-expansion-panels>.v-expansion-panel>.v-expansion-panel-content>.v-expansion-panel-content__wrap{padding:24px 16px 32px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1270:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-messages/MessagesPage.vue?vue&type=template&id=a1a71ef0&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-col',{staticClass:"col-12 px-0"},[_c('div',{staticClass:"user-messages"},[_c('v-container',{staticClass:"pa-0",attrs:{"fluid":""}},[_c('v-row',{attrs:{"no-gutters":""}},[_c('v-col',{staticClass:"col-12"},[_c('div',{staticClass:"user-messages-wrap mx-auto"},[_c('div',{staticClass:"user-messages-title font-weight-medium mb-3 d-sm-none"},[_vm._v("\n              "+_vm._s(_vm.$t('my_messages'))+" 📬\n            ")]),_vm._v(" "),(_vm.$vuetify.breakpoint.smAndUp)?_c('div',{staticClass:"d-none d-sm-flex"},[(_vm.$vuetify.breakpoint.smAndUp)?_c('aside',{staticClass:"user-messages-sidebar d-none d-sm-block"},[_c('div',{staticClass:"user-messages-title font-weight-medium mb-2"},[_vm._v("\n                  "+_vm._s(_vm.$t('my_messages'))+" 📬\n                ")]),_vm._v(" "),_c('div',{staticClass:"user-messages-sidebar-sticky"},[_c('div',{staticClass:"user-messages-sidebar-search mb-2"},[_c('search-input',{attrs:{"disabled":!_vm.threadsNotEmpty && !_vm.searchQueryParam,"placeholder":"search_for_recipient","small":""},on:{"submit":_vm.searchSubmitHandler},model:{value:(_vm.searchQuery),callback:function ($$v) {_vm.searchQuery=(typeof $$v === 'string'? $$v.trim(): $$v)},expression:"searchQuery"}})],1),_vm._v(" "),(_vm.threadsNotEmpty)?_c('div',{staticClass:"user-messages-tabs-nav"},[_c('div',{ref:"threadsList",staticClass:"py-1"},[_vm._l((_vm.threads),function(thread,idx){return _c('div',{key:idx,staticClass:"nav-item",attrs:{"id":("thread-" + (thread.id))}},[_c('v-btn',{class:[
                            'font-weight-regular',
                            { active: _vm.selectedThread.id === thread.id } ],attrs:{"dark":_vm.selectedThread.id === thread.id,"width":"100%","height":"48"},on:{"click":function($event){return _vm.threadClickHandler(thread)}}},[_c('div',{staticClass:"nav-item-avatar mr-1"},[(!thread.isRead)?_c('v-img',{staticClass:"envelop",attrs:{"src":__webpack_require__(572),"width":"22","height":"22"}}):_vm._e(),_vm._v(" "),_c('v-avatar',{attrs:{"width":"52","height":"52"}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(
                                    !thread.userIsDeleted
                                      ? thread.avatars
                                      : {},
                                    'user_thumb_52x52'
                                  ),"srcset":_vm.getSrcSetAvatar(
                                    !thread.userIsDeleted
                                      ? thread.avatars
                                      : {},
                                    'user_thumb_52x52',
                                    'user_thumb_104x104'
                                  ),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),(!thread.userIsDeleted)?_c('user-status',{attrs:{"user-id":thread.userId,"user-statuses":_vm.userStatuses}}):_vm._e()],1),_vm._v(" "),(!thread.userIsDeleted)?[_vm._v("\n                            "+_vm._s(thread.firstName)+" "+_vm._s(thread.lastName)+"\n                          ")]:[_vm._v("\n                            "+_vm._s(_vm.$t('deleted_user'))+"\n                          ")]],2)],1)}),_vm._v(" "),(_vm.isMoreButtonShown)?_c('div',{staticClass:"user-messages-sidebar-show-more-btn"},[_c('load-more-btn',{attrs:{"large":"","text-btn":_vm.$t('load_more_threads'),"fetch-func":_vm.fetchThreads}})],1):_vm._e()],2)]):_vm._e()])]):_vm._e(),_vm._v(" "),_c('div',{staticClass:"user-messages-content"},[(_vm.threadsNotEmpty)?[_c('conversation',{attrs:{"item":_vm.conversation,"user-statuses":_vm.userStatuses}})]:[_c('empty-content')]],2)]):_c('div',{staticClass:"d-sm-none"},[(_vm.threadsNotEmpty)?[_c('div',{staticClass:"user-messages-sidebar-search mb-2 mb-sm-3"},[_c('search-input',{attrs:{"disabled":!_vm.threadsNotEmpty && !_vm.searchQueryParam,"placeholder":"search_for_recipient","small":""},on:{"submit":_vm.searchSubmitHandler},model:{value:(_vm.searchQuery),callback:function ($$v) {_vm.searchQuery=(typeof $$v === 'string'? $$v.trim(): $$v)},expression:"searchQuery"}})],1),_vm._v(" "),_c('client-only',[_c('v-expansion-panels',{staticClass:"tabs-mobile",attrs:{"accordion":"","flat":""},model:{value:(_vm.tabActive),callback:function ($$v) {_vm.tabActive=$$v},expression:"tabActive"}},_vm._l((_vm.threads),function(thread,idx){return _c('v-expansion-panel',{key:idx},[_c('v-expansion-panel-header',{ref:("panel-" + idx),refInFor:true,attrs:{"disable-icon-rotate":""},on:{"click":function($event){return _vm.threadClickHandler(thread)}},scopedSlots:_vm._u([{key:"actions",fn:function(){return [(_vm.tabActive === idx)?[_c('v-icon',{attrs:{"color":"dark"}},[_vm._v("\n                              "+_vm._s(_vm.mdiMinus)+"\n                            ")])]:[_c('v-img',{attrs:{"src":__webpack_require__(515),"width":"24","height":"24"}})]]},proxy:true}],null,true)},[_c('div',{staticClass:"nav-item-avatar mr-1"},[(!thread.isRead)?_c('v-img',{staticClass:"envelop",attrs:{"src":__webpack_require__(572),"width":"22","height":"22"}}):_vm._e(),_vm._v(" "),_c('v-avatar',{attrs:{"width":"48","height":"48"}},[_c('v-img',{attrs:{"src":_vm.getSrcAvatar(
                                  !thread.userIsDeleted ? thread.avatars : {},
                                  'user_thumb_52x52'
                                ),"srcset":_vm.getSrcSetAvatar(
                                  !thread.userIsDeleted ? thread.avatars : {},
                                  'user_thumb_52x52',
                                  'user_thumb_104x104'
                                ),"options":{ rootMargin: '50%' }}})],1),_vm._v(" "),(!thread.userIsDeleted)?_c('user-status',{attrs:{"user-id":thread.userId,"user-statuses":_vm.userStatuses}}):_vm._e()],1),_vm._v(" "),(!thread.userIsDeleted)?[_vm._v("\n                          "+_vm._s(thread.firstName)+" "+_vm._s(thread.lastName)+"\n                        ")]:[_vm._v("\n                          "+_vm._s(_vm.$t('deleted_user'))+"\n                        ")]],2),_vm._v(" "),_c('v-expansion-panel-content',[_c('conversation',{attrs:{"item":_vm.conversation,"user-statuses":_vm.userStatuses}})],1)],1)}),1)],1),_vm._v(" "),(_vm.isMoreButtonShown)?_c('div',{staticClass:"user-messages-sidebar-show-more-btn mt-1"},[_c('load-more-btn',{attrs:{"large":"","text-btn":_vm.$t('load_more_threads'),"fetch-func":_vm.fetchThreads}})],1):_vm._e()]:[_c('empty-content')]],2)])])],1)],1)],1)])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/user-messages/MessagesPage.vue?vue&type=template&id=a1a71ef0&

// EXTERNAL MODULE: external "@mdi/js"
var js_ = __webpack_require__(48);

// EXTERNAL MODULE: ./mixins/Avatars.vue + 2 modules
var Avatars = __webpack_require__(932);

// EXTERNAL MODULE: ./mixins/StatusOnline.vue + 2 modules
var StatusOnline = __webpack_require__(992);

// EXTERNAL MODULE: ./components/form/SearchInput.vue + 4 modules
var SearchInput = __webpack_require__(945);

// EXTERNAL MODULE: ./components/UserStatus.vue + 4 modules
var UserStatus = __webpack_require__(916);

// EXTERNAL MODULE: ./components/LoadMoreBtn.vue + 4 modules
var LoadMoreBtn = __webpack_require__(1047);

// EXTERNAL MODULE: ./components/user-messages/Conversation.vue + 4 modules
var Conversation = __webpack_require__(1197);

// EXTERNAL MODULE: ./components/user-messages/EmptyContent.vue + 4 modules
var EmptyContent = __webpack_require__(1198);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/user-messages/MessagesPage.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//








/* harmony default export */ var MessagesPagevue_type_script_lang_js_ = ({
  name: 'MessagesPage',
  components: {
    SearchInput: SearchInput["default"],
    UserStatus: UserStatus["default"],
    LoadMoreBtn: LoadMoreBtn["default"],
    Conversation: Conversation["default"],
    EmptyContent: EmptyContent["default"]
  },
  mixins: [Avatars["a" /* default */], StatusOnline["a" /* default */]],
  props: {
    totalQuantity: {
      type: Number,
      required: true
    },
    additionalUser: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      mdiMinus: js_["mdiMinus"],
      tabActive: null,
      searchQuery: '',
      threadsPage: 1
    };
  },

  computed: {
    threads() {
      return this.$store.state.message.items;
    },

    threadsNotEmpty() {
      return !!this.threads.length;
    },

    selectedThread() {
      return this.$store.state.message.item;
    },

    conversation() {
      return this.$store.state.message.conversation;
    },

    totalPages() {
      return Math.ceil(this.totalQuantity / "15");
    },

    isMoreButtonShown() {
      return this.totalPages > 1 && this.threadsPage < this.totalPages;
    },

    searchQueryParam() {
      var _this$$route$query$se, _this$$route$query;

      return (_this$$route$query$se = (_this$$route$query = this.$route.query) === null || _this$$route$query === void 0 ? void 0 : _this$$route$query.search) !== null && _this$$route$query$se !== void 0 ? _this$$route$query$se : '';
    },

    userId() {
      var _this$$store$state$us;

      return (_this$$store$state$us = this.$store.state.user.item) === null || _this$$store$state$us === void 0 ? void 0 : _this$$store$state$us.id;
    }

  },
  watch: {
    '$route.params.search': {
      handler() {
        this.threadsPage = 1;
        this.searchQuery = this.searchQueryParam;
        this.setArrStatusId();
      },

      deep: true
    },

    'threads.length'() {
      this.setArrStatusId();
      this.tabActive = null;
    },

    tabActive(newValue, oldValue) {
      if (newValue != null && this.$vuetify.breakpoint.xsOnly) {
        const el = this.$refs[`panel-${this.tabActive}`][0].$el;

        if (el) {
          this.$nextTick(() => {
            window.setTimeout(() => {
              this.$vuetify.goTo(el, {
                duration: 0,
                offset: 10,
                easing: 'linear'
              });
            }, 400);
          });
        }
      }
    }

  },

  beforeMount() {
    this.searchQuery = this.searchQueryParam;
    this.setArrStatusId();
    this.refreshStatusOnline();
  },

  methods: {
    setArrStatusId() {
      this.arrStatusId = this.threads.filter(item => !item.userIsDeleted).map(item => item.userId);

      if (this.userId) {
        this.arrStatusId.push(this.userId);
      }

      if (this.additionalUser) {
        this.arrStatusId.push(this.conversation.userId);
      }
    },

    searchSubmitHandler() {
      this.$router.push({
        name: this.$route.name,
        query: this.searchQuery ? {
          search: this.searchQuery
        } : {}
      });
    },

    threadClickHandler(thread) {
      this.$store.dispatch('message/getConversation', {
        threadId: thread.id
      }).then(() => {
        this.$store.commit('message/SET_ITEM', thread);
      });
    },

    async fetchThreads() {
      this.threadsPage++;
      await this.$store.dispatch('loadingAllow', false);
      await this.$store.dispatch('message/getItems', {
        page: this.threadsPage
      }).then(() => {
        this.$nextTick(() => {
          const lastItem = this.threads[(this.threadsPage - 1) * "15"];
          const lastEl = document.getElementById(`thread-${lastItem.id}`);

          if (lastEl) {
            this.$refs.threadsList.scrollTo({
              top: lastEl.offsetTop - 76,
              behavior: 'smooth'
            });
          }
        });
      });
      await this.$store.dispatch('loadingAllow', true);
    }

  }
});
// CONCATENATED MODULE: ./components/user-messages/MessagesPage.vue?vue&type=script&lang=js&
 /* harmony default export */ var user_messages_MessagesPagevue_type_script_lang_js_ = (MessagesPagevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/VAvatar.js
var VAvatar = __webpack_require__(830);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VContainer.js + 1 modules
var VContainer = __webpack_require__(899);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanel.js
var VExpansionPanel = __webpack_require__(1072);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelContent.js
var VExpansionPanelContent = __webpack_require__(1073);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelHeader.js
var VExpansionPanelHeader = __webpack_require__(1074);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanels.js
var VExpansionPanels = __webpack_require__(1092);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(87);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(893);

// CONCATENATED MODULE: ./components/user-messages/MessagesPage.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1216)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  user_messages_MessagesPagevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "147f6aa1"
  
)

/* harmony default export */ var MessagesPage = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {UserStatus: __webpack_require__(916).default,LoadMoreBtn: __webpack_require__(1047).default})


/* vuetify-loader */












installComponents_default()(component, {VAvatar: VAvatar["a" /* default */],VBtn: VBtn["a" /* default */],VCol: VCol["a" /* default */],VContainer: VContainer["a" /* default */],VExpansionPanel: VExpansionPanel["a" /* default */],VExpansionPanelContent: VExpansionPanelContent["a" /* default */],VExpansionPanelHeader: VExpansionPanelHeader["a" /* default */],VExpansionPanels: VExpansionPanels["a" /* default */],VIcon: VIcon["a" /* default */],VImg: VImg["a" /* default */],VRow: VRow["a" /* default */]})


/***/ }),

/***/ 1497:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/messages/_threadId/view/index.vue?vue&type=template&id=60e9fb32&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('messages-page',{attrs:{"total-quantity":_vm.count,"additional-user":""}})}
var staticRenderFns = []


// CONCATENATED MODULE: ./pages/user/messages/_threadId/view/index.vue?vue&type=template&id=60e9fb32&

// EXTERNAL MODULE: ./components/user-messages/MessagesPage.vue + 4 modules
var MessagesPage = __webpack_require__(1270);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/user/messages/_threadId/view/index.vue?vue&type=script&lang=js&
//
//
//
//

/* harmony default export */ var viewvue_type_script_lang_js_ = ({
  name: 'UserMessagesView',
  components: {
    MessagesPage: MessagesPage["default"]
  },
  middleware: 'authenticated',

  async asyncData({
    store,
    route,
    query
  }) {
    const searchQuery = query === null || query === void 0 ? void 0 : query.search;
    const threadId = +route.params.threadId;
    let count;
    await store.dispatch('message/getItems', {
      page: 1,
      searchQuery
    }).then(async data => {
      var _data$count, _data$threads;

      count = (_data$count = data === null || data === void 0 ? void 0 : data.count) !== null && _data$count !== void 0 ? _data$count : 0;

      if (data !== null && data !== void 0 && (_data$threads = data.threads) !== null && _data$threads !== void 0 && _data$threads.length) {
        const thread = data.threads.find(item => item.id === threadId);
        store.commit('message/SET_ITEM', thread || {
          id: threadId
        });
        await store.dispatch('message/getConversation', {
          threadId
        });
      }
    });
    return {
      count
    };
  },

  head() {
    return {
      title: this.$t('user_messages_page.seo_title'),
      meta: [{
        hid: 'description',
        name: 'description',
        content: this.$t('user_messages_page.seo_description')
      }, {
        hid: 'og:title',
        name: 'og:title',
        property: 'og:title',
        content: this.$t('user_messages_page.seo_title')
      }, {
        property: 'og:description',
        content: this.$t('user_messages_page.seo_description')
      }],
      bodyAttrs: {
        class: `${this.locale} user-messages-page`
      }
    };
  },

  computed: {
    locale() {
      return this.$i18n.locale;
    }

  },
  watchQuery: true
});
// CONCATENATED MODULE: ./pages/user/messages/_threadId/view/index.vue?vue&type=script&lang=js&
 /* harmony default export */ var _threadId_viewvue_type_script_lang_js_ = (viewvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./pages/user/messages/_threadId/view/index.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  _threadId_viewvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "7a18bf68"
  
)

/* harmony default export */ var view = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 853:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _VTextField__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(39);


/* harmony default export */ __webpack_exports__["a"] = (_VTextField__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"]);

/***/ }),

/***/ 901:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(909);
/* harmony import */ var _src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VChip_VChip_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(2);
/* harmony import */ var _transitions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(67);
/* harmony import */ var _VIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(66);
/* harmony import */ var _mixins_colorable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(9);
/* harmony import */ var _mixins_groupable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(47);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(7);
/* harmony import */ var _mixins_toggleable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(10);
/* harmony import */ var _mixins_routable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(18);
/* harmony import */ var _mixins_sizeable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(49);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(3);
// Styles

 // Components


 // Mixins






 // Utilities


/* @vue/component */

/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(_mixins_colorable__WEBPACK_IMPORTED_MODULE_4__[/* default */ "a"], _mixins_sizeable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"], _mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], _mixins_themeable__WEBPACK_IMPORTED_MODULE_6__[/* default */ "a"], Object(_mixins_groupable__WEBPACK_IMPORTED_MODULE_5__[/* factory */ "a"])('chipGroup'), Object(_mixins_toggleable__WEBPACK_IMPORTED_MODULE_7__[/* factory */ "b"])('inputValue')).extend({
  name: 'v-chip',
  props: {
    active: {
      type: Boolean,
      default: true
    },
    activeClass: {
      type: String,

      default() {
        if (!this.chipGroup) return '';
        return this.chipGroup.activeClass;
      }

    },
    close: Boolean,
    closeIcon: {
      type: String,
      default: '$delete'
    },
    closeLabel: {
      type: String,
      default: '$vuetify.close'
    },
    disabled: Boolean,
    draggable: Boolean,
    filter: Boolean,
    filterIcon: {
      type: String,
      default: '$complete'
    },
    label: Boolean,
    link: Boolean,
    outlined: Boolean,
    pill: Boolean,
    tag: {
      type: String,
      default: 'span'
    },
    textColor: String,
    value: null
  },
  data: () => ({
    proxyClass: 'v-chip--active'
  }),
  computed: {
    classes() {
      return {
        'v-chip': true,
        ..._mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].options.computed.classes.call(this),
        'v-chip--clickable': this.isClickable,
        'v-chip--disabled': this.disabled,
        'v-chip--draggable': this.draggable,
        'v-chip--label': this.label,
        'v-chip--link': this.isLink,
        'v-chip--no-color': !this.color,
        'v-chip--outlined': this.outlined,
        'v-chip--pill': this.pill,
        'v-chip--removable': this.hasClose,
        ...this.themeClasses,
        ...this.sizeableClasses,
        ...this.groupClasses
      };
    },

    hasClose() {
      return Boolean(this.close);
    },

    isClickable() {
      return Boolean(_mixins_routable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"].options.computed.isClickable.call(this) || this.chipGroup);
    }

  },

  created() {
    const breakingProps = [['outline', 'outlined'], ['selected', 'input-value'], ['value', 'active'], ['@input', '@active.sync']];
    /* istanbul ignore next */

    breakingProps.forEach(([original, replacement]) => {
      if (this.$attrs.hasOwnProperty(original)) Object(_util_console__WEBPACK_IMPORTED_MODULE_10__[/* breaking */ "a"])(original, replacement, this);
    });
  },

  methods: {
    click(e) {
      this.$emit('click', e);
      this.chipGroup && this.toggle();
    },

    genFilter() {
      const children = [];

      if (this.isActive) {
        children.push(this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"], {
          staticClass: 'v-chip__filter',
          props: {
            left: true
          }
        }, this.filterIcon));
      }

      return this.$createElement(_transitions__WEBPACK_IMPORTED_MODULE_2__[/* VExpandXTransition */ "b"], children);
    },

    genClose() {
      return this.$createElement(_VIcon__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"], {
        staticClass: 'v-chip__close',
        props: {
          right: true,
          size: 18
        },
        attrs: {
          'aria-label': this.$vuetify.lang.t(this.closeLabel)
        },
        on: {
          click: e => {
            e.stopPropagation();
            e.preventDefault();
            this.$emit('click:close');
            this.$emit('update:active', false);
          }
        }
      }, this.closeIcon);
    },

    genContent() {
      return this.$createElement('span', {
        staticClass: 'v-chip__content'
      }, [this.filter && this.genFilter(), this.$slots.default, this.hasClose && this.genClose()]);
    }

  },

  render(h) {
    const children = [this.genContent()];
    let {
      tag,
      data
    } = this.generateRouteLink();
    data.attrs = { ...data.attrs,
      draggable: this.draggable ? 'true' : undefined,
      tabindex: this.chipGroup && !this.disabled ? 0 : data.attrs.tabindex
    };
    data.directives.push({
      name: 'show',
      value: this.active
    });
    data = this.setBackgroundColor(this.color, data);
    const color = this.textColor || this.outlined && this.color;
    return h(tag, this.setTextColor(color, data), children);
  }

}));

/***/ }),

/***/ 902:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return BaseItemGroup; });
/* harmony import */ var _src_components_VItemGroup_VItemGroup_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(906);
/* harmony import */ var _src_components_VItemGroup_VItemGroup_sass__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_src_components_VItemGroup_VItemGroup_sass__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _mixins_proxyable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(104);
/* harmony import */ var _mixins_themeable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2);
/* harmony import */ var _util_console__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(3);
// Styles


 // Utilities



const BaseItemGroup = Object(_util_mixins__WEBPACK_IMPORTED_MODULE_3__[/* default */ "a"])(_mixins_proxyable__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"], _mixins_themeable__WEBPACK_IMPORTED_MODULE_2__[/* default */ "a"]).extend({
  name: 'base-item-group',
  props: {
    activeClass: {
      type: String,
      default: 'v-item--active'
    },
    mandatory: Boolean,
    max: {
      type: [Number, String],
      default: null
    },
    multiple: Boolean,
    tag: {
      type: String,
      default: 'div'
    }
  },

  data() {
    return {
      // As long as a value is defined, show it
      // Otherwise, check if multiple
      // to determine which default to provide
      internalLazyValue: this.value !== undefined ? this.value : this.multiple ? [] : undefined,
      items: []
    };
  },

  computed: {
    classes() {
      return {
        'v-item-group': true,
        ...this.themeClasses
      };
    },

    selectedIndex() {
      return this.selectedItem && this.items.indexOf(this.selectedItem) || -1;
    },

    selectedItem() {
      if (this.multiple) return undefined;
      return this.selectedItems[0];
    },

    selectedItems() {
      return this.items.filter((item, index) => {
        return this.toggleMethod(this.getValue(item, index));
      });
    },

    selectedValues() {
      if (this.internalValue == null) return [];
      return Array.isArray(this.internalValue) ? this.internalValue : [this.internalValue];
    },

    toggleMethod() {
      if (!this.multiple) {
        return v => this.internalValue === v;
      }

      const internalValue = this.internalValue;

      if (Array.isArray(internalValue)) {
        return v => internalValue.includes(v);
      }

      return () => false;
    }

  },
  watch: {
    internalValue: 'updateItemsState',
    items: 'updateItemsState'
  },

  created() {
    if (this.multiple && !Array.isArray(this.internalValue)) {
      Object(_util_console__WEBPACK_IMPORTED_MODULE_4__[/* consoleWarn */ "c"])('Model must be bound to an array if the multiple property is true.', this);
    }
  },

  methods: {
    genData() {
      return {
        class: this.classes
      };
    },

    getValue(item, i) {
      return item.value == null || item.value === '' ? i : item.value;
    },

    onClick(item) {
      this.updateInternalValue(this.getValue(item, this.items.indexOf(item)));
    },

    register(item) {
      const index = this.items.push(item) - 1;
      item.$on('change', () => this.onClick(item)); // If no value provided and mandatory,
      // assign first registered item

      if (this.mandatory && !this.selectedValues.length) {
        this.updateMandatory();
      }

      this.updateItem(item, index);
    },

    unregister(item) {
      if (this._isDestroyed) return;
      const index = this.items.indexOf(item);
      const value = this.getValue(item, index);
      this.items.splice(index, 1);
      const valueIndex = this.selectedValues.indexOf(value); // Items is not selected, do nothing

      if (valueIndex < 0) return; // If not mandatory, use regular update process

      if (!this.mandatory) {
        return this.updateInternalValue(value);
      } // Remove the value


      if (this.multiple && Array.isArray(this.internalValue)) {
        this.internalValue = this.internalValue.filter(v => v !== value);
      } else {
        this.internalValue = undefined;
      } // If mandatory and we have no selection
      // add the last item as value

      /* istanbul ignore else */


      if (!this.selectedItems.length) {
        this.updateMandatory(true);
      }
    },

    updateItem(item, index) {
      const value = this.getValue(item, index);
      item.isActive = this.toggleMethod(value);
    },

    // https://github.com/vuetifyjs/vuetify/issues/5352
    updateItemsState() {
      this.$nextTick(() => {
        if (this.mandatory && !this.selectedItems.length) {
          return this.updateMandatory();
        } // TODO: Make this smarter so it
        // doesn't have to iterate every
        // child in an update


        this.items.forEach(this.updateItem);
      });
    },

    updateInternalValue(value) {
      this.multiple ? this.updateMultiple(value) : this.updateSingle(value);
    },

    updateMandatory(last) {
      if (!this.items.length) return;
      const items = this.items.slice();
      if (last) items.reverse();
      const item = items.find(item => !item.disabled); // If no tabs are available
      // aborts mandatory value

      if (!item) return;
      const index = this.items.indexOf(item);
      this.updateInternalValue(this.getValue(item, index));
    },

    updateMultiple(value) {
      const defaultValue = Array.isArray(this.internalValue) ? this.internalValue : [];
      const internalValue = defaultValue.slice();
      const index = internalValue.findIndex(val => val === value);
      if (this.mandatory && // Item already exists
      index > -1 && // value would be reduced below min
      internalValue.length - 1 < 1) return;
      if ( // Max is set
      this.max != null && // Item doesn't exist
      index < 0 && // value would be increased above max
      internalValue.length + 1 > this.max) return;
      index > -1 ? internalValue.splice(index, 1) : internalValue.push(value);
      this.internalValue = internalValue;
    },

    updateSingle(value) {
      const isSame = value === this.internalValue;
      if (this.mandatory && isSame) return;
      this.internalValue = isSame ? undefined : value;
    }

  },

  render(h) {
    return h(this.tag, this.genData(), this.$slots.default);
  }

});
/* unused harmony default export */ var _unused_webpack_default_export = (BaseItemGroup.extend({
  name: 'v-item-group',

  provide() {
    return {
      itemGroup: this
    };
  }

}));

/***/ }),

/***/ 906:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(907);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("73707fd0", content, true)

/***/ }),

/***/ 907:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-item-group{flex:0 1 auto;position:relative;max-width:100%;transition:.3s cubic-bezier(.25,.8,.5,1)}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 909:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(910);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("197fcea4", content, true)

/***/ }),

/***/ 910:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-chip:not(.v-chip--outlined).accent,.v-chip:not(.v-chip--outlined).error,.v-chip:not(.v-chip--outlined).info,.v-chip:not(.v-chip--outlined).primary,.v-chip:not(.v-chip--outlined).secondary,.v-chip:not(.v-chip--outlined).success,.v-chip:not(.v-chip--outlined).warning{color:#fff}.theme--light.v-chip{border-color:rgba(0,0,0,.12);color:rgba(0,0,0,.87)}.theme--light.v-chip:not(.v-chip--active){background:#e0e0e0}.theme--light.v-chip:hover:before{opacity:.04}.theme--light.v-chip--active:before,.theme--light.v-chip--active:hover:before,.theme--light.v-chip:focus:before{opacity:.12}.theme--light.v-chip--active:focus:before{opacity:.16}.theme--dark.v-chip{border-color:hsla(0,0%,100%,.12);color:#fff}.theme--dark.v-chip:not(.v-chip--active){background:#555}.theme--dark.v-chip:hover:before{opacity:.08}.theme--dark.v-chip--active:before,.theme--dark.v-chip--active:hover:before,.theme--dark.v-chip:focus:before{opacity:.24}.theme--dark.v-chip--active:focus:before{opacity:.32}.v-chip{align-items:center;cursor:default;display:inline-flex;line-height:20px;max-width:100%;outline:none;overflow:hidden;padding:0 12px;position:relative;text-decoration:none;transition-duration:.28s;transition-property:box-shadow,opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);vertical-align:middle;white-space:nowrap}.v-chip:before{background-color:currentColor;bottom:0;border-radius:inherit;content:\"\";left:0;opacity:0;position:absolute;pointer-events:none;right:0;top:0}.v-chip .v-avatar{height:24px!important;min-width:24px!important;width:24px!important}.v-chip .v-icon{font-size:24px}.v-application--is-ltr .v-chip .v-avatar--left,.v-application--is-ltr .v-chip .v-icon--left{margin-left:-6px;margin-right:6px}.v-application--is-ltr .v-chip .v-avatar--right,.v-application--is-ltr .v-chip .v-icon--right,.v-application--is-rtl .v-chip .v-avatar--left,.v-application--is-rtl .v-chip .v-icon--left{margin-left:6px;margin-right:-6px}.v-application--is-rtl .v-chip .v-avatar--right,.v-application--is-rtl .v-chip .v-icon--right{margin-left:-6px;margin-right:6px}.v-chip:not(.v-chip--no-color) .v-icon{color:inherit}.v-chip .v-chip__close.v-icon{font-size:18px;max-height:18px;max-width:18px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-chip .v-chip__close.v-icon.v-icon--right{margin-right:-4px}.v-application--is-rtl .v-chip .v-chip__close.v-icon.v-icon--right{margin-left:-4px}.v-chip .v-chip__close.v-icon:active,.v-chip .v-chip__close.v-icon:focus,.v-chip .v-chip__close.v-icon:hover{opacity:.72}.v-chip .v-chip__content{align-items:center;display:inline-flex;height:100%;max-width:100%}.v-chip--active .v-icon{color:inherit}.v-chip--link:before{transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-chip--link:focus:before{opacity:.32}.v-chip--clickable{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip--clickable:active{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-chip--disabled{opacity:.4;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-chip__filter{max-width:24px}.v-chip__filter.v-icon{color:inherit}.v-chip__filter.expand-x-transition-enter,.v-chip__filter.expand-x-transition-leave-active{margin:0}.v-chip--pill .v-chip__filter{margin-right:0 16px 0 0}.v-chip--pill .v-avatar{height:32px!important;width:32px!important}.v-application--is-ltr .v-chip--pill .v-avatar--left{margin-left:-12px}.v-application--is-ltr .v-chip--pill .v-avatar--right,.v-application--is-rtl .v-chip--pill .v-avatar--left{margin-right:-12px}.v-application--is-rtl .v-chip--pill .v-avatar--right{margin-left:-12px}.v-chip--label{border-radius:4px!important}.v-chip.v-chip--outlined{border-width:thin;border-style:solid}.v-chip.v-chip--outlined.v-chip--active:before{opacity:.08}.v-chip.v-chip--outlined .v-icon{color:inherit}.v-chip.v-chip--outlined.v-chip.v-chip{background-color:transparent!important}.v-chip.v-chip--selected{background:transparent}.v-chip.v-chip--selected:after{opacity:.28}.v-chip.v-size--x-small{border-radius:8px;font-size:10px;height:16px}.v-chip.v-size--small{border-radius:12px;font-size:12px;height:24px}.v-chip.v-size--default{border-radius:16px;font-size:14px;height:32px}.v-chip.v-size--large{border-radius:27px;font-size:16px;height:54px}.v-chip.v-size--x-large{border-radius:33px;font-size:18px;height:66px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 915:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(938);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("a98bb618", content, true, context)
};

/***/ }),

/***/ 916:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/UserStatus.vue?vue&type=template&id=652352c7&scoped=true&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{class:[
    'user-status',
    ("user-status--" + _vm.status),
    { 'user-status--large': _vm.large } ]},[])}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/UserStatus.vue?vue&type=template&id=652352c7&scoped=true&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/UserStatus.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var UserStatusvue_type_script_lang_js_ = ({
  name: 'UserStatus',
  props: {
    userId: {
      type: Number,
      default: 0
    },
    large: {
      type: Boolean,
      default: false
    },
    userStatuses: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    status() {
      var _this$userId;

      let status = 'offline';

      if (Object.prototype.hasOwnProperty.call(this.userStatuses, (_this$userId = this.userId) === null || _this$userId === void 0 ? void 0 : _this$userId.toString())) {
        status = this.userStatuses[this.userId];
      }

      return status;
    }

  }
});
// CONCATENATED MODULE: ./components/UserStatus.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_UserStatusvue_type_script_lang_js_ = (UserStatusvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/UserStatus.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(958)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_UserStatusvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  "652352c7",
  "4c070a35"
  
)

/* harmony default export */ var UserStatus = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 931:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(954);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("637a1dfc", content, true, context)
};

/***/ }),

/***/ 932:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/Avatars.vue?vue&type=script&lang=js&
/* harmony default export */ var Avatarsvue_type_script_lang_js_ = ({
  methods: {
    getSrcAvatar(images, property, defaultImage = 'avatar.png') {
      return images !== null && images !== void 0 && images[property] ? images[property] : __webpack_require__(511)(`./${defaultImage}`);
    },

    getSrcSetAvatar(images, property1, property2) {
      return images !== null && images !== void 0 && images[property1] && images !== null && images !== void 0 && images[property2] ? `
            ${images[property1]} 1x,
            ${images[property2]} 2x,
          ` : '';
    }

  }
});
// CONCATENATED MODULE: ./mixins/Avatars.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_Avatarsvue_type_script_lang_js_ = (Avatarsvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/Avatars.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_Avatarsvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "0af9ff4e"
  
)

/* harmony default export */ var Avatars = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 933:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(959);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("006007e9", content, true, context)
};

/***/ }),

/***/ 937:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(915);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Editor_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 938:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".text-editor{position:relative}.text-editor-buttons{position:absolute;right:18px;top:8px;z-index:2}.text-editor-buttons button{display:inline-flex;justify-content:center;align-items:center;width:24px;height:24px;margin-left:8px;border-radius:2px;border:1px solid transparent}.text-editor-buttons button.is-active{background-color:var(--v-greyBg-base);border-color:var(--v-greyLight-base)}.text-editor .ProseMirror{min-height:280px;margin-bottom:4px;padding:40px 12px 12px;border:1px solid #bebebe;font-size:13px;border-radius:16px;line-height:1.23}.text-editor .ProseMirror>*{position:relative}.text-editor .ProseMirror p{margin-bottom:0}.text-editor .ProseMirror ul{padding-left:28px}.text-editor .ProseMirror ul>li p{margin-bottom:0}.text-editor .ProseMirror strong{font-weight:700!important}.text-editor .ProseMirror.focus-visible,.text-editor .ProseMirror:focus,.text-editor .ProseMirror:focus-visible{outline:none!important}.text-editor .ProseMirror-focused:before{content:\"\";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:16px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}.text-editor .v-text-field__details{padding:0 14px}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 942:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/form/Editor.vue?vue&type=template&id=23b137ee&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"text-editor"},[_vm._ssrNode(((_vm.editor)?("<div class=\"text-editor-buttons\"><button"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bold') }))+"><svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#editor-bold-icon")))+"></use></svg></button> <button"+(_vm._ssrClass(null,{ 'is-active': _vm.editor.isActive('bulletList') }))+"><svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\"><use"+(_vm._ssrAttr("xlink:href",((__webpack_require__(14)) + "#editor-list-icon")))+"></use></svg></button></div>"):"<!---->")+" "),_c('editor-content',{attrs:{"editor":_vm.editor}}),_vm._ssrNode(" "+((_vm.counter)?("<div class=\"v-text-field__details\"><div class=\"v-messages theme--light\"><div class=\"v-messages__wrapper\"></div></div> <div"+(_vm._ssrClass(null,[
        'v-counter theme--light',
        { 'error--text': !_vm.isValid && _vm.isDirty } ]))+">"+_vm._ssrEscape("\n      "+_vm._s(_vm.text.length)+" / "+_vm._s(_vm.limit)+"\n    ")+"</div></div>"):"<!---->"))],2)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/form/Editor.vue?vue&type=template&id=23b137ee&

// EXTERNAL MODULE: external "@tiptap/vue-2"
var vue_2_ = __webpack_require__(854);

// EXTERNAL MODULE: external "@tiptap/starter-kit"
var starter_kit_ = __webpack_require__(855);
var starter_kit_default = /*#__PURE__*/__webpack_require__.n(starter_kit_);

// EXTERNAL MODULE: external "@tiptap/extension-character-count"
var extension_character_count_ = __webpack_require__(856);
var extension_character_count_default = /*#__PURE__*/__webpack_require__.n(extension_character_count_);

// EXTERNAL MODULE: external "@tiptap/extension-link"
var extension_link_ = __webpack_require__(857);
var extension_link_default = /*#__PURE__*/__webpack_require__.n(extension_link_);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/form/Editor.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//




/* harmony default export */ var Editorvue_type_script_lang_js_ = ({
  name: 'Editor',
  components: {
    EditorContent: vue_2_["EditorContent"]
  },
  props: {
    value: {
      type: String,
      required: true
    },
    counter: {
      type: Boolean,
      default: false
    },
    autoLink: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: null
    }
  },

  data() {
    return {
      editor: null,
      text: '',
      isValid: true,
      editorEl: null,
      keysPressed: {},
      isDirty: false
    };
  },

  watch: {
    value(value) {
      const isSame = this.editor.getHTML() === value;

      if (isSame) {
        return;
      }

      this.editor.commands.setContent(value, false);
    }

  },

  mounted() {
    this.editor = new vue_2_["Editor"]({
      content: this.value,
      extensions: [starter_kit_default.a, extension_character_count_default.a.configure({
        limit: this.limit
      }), extension_link_default.a.configure({
        autolink: true
      })]
    });
    this.editor.on('create', ({
      editor
    }) => {
      this.text = editor.getText();
      this.$nextTick(() => {
        this.editorEl = document.getElementsByClassName('ProseMirror')[0];

        if (this.editorEl) {
          this.editorEl.addEventListener('keydown', this.keydownHandler);
          this.editorEl.addEventListener('keyup', this.keyupHandler);
        }
      });
      this.validation();
    });
    this.editor.on('update', ({
      editor
    }) => {
      this.isDirty = true;
      this.text = editor.getText();
      this.validation();
      this.$emit('update', this.text ? editor.getHTML() : '');
    });
  },

  beforeDestroy() {
    if (this.editorEl) {
      this.editorEl.removeEventListener('keydown', this.keydownHandler);
      this.editorEl.removeEventListener('keyup', this.keyupHandler);
    }

    this.editor.destroy();
  },

  methods: {
    keydownHandler(e) {
      this.keysPressed[e.keyCode] = true;

      if ((e.ctrlKey || this.keysPressed[17] || this.keysPressed[91] || this.keysPressed[93] || this.keysPressed[224]) && this.keysPressed[13]) {
        e.preventDefault();
        this.$emit('submit');
        this.keysPressed = {};
      } else if (e.keyCode === 13 && !e.shiftKey) {
        e.preventDefault();
        this.editor.commands.enter();
      }
    },

    keyupHandler(e) {
      delete this.keysPressed[e.keyCode];
    },

    validation() {
      const strLength = this.text.trim().length;
      this.isValid = !!strLength;

      if (!!strLength && this.limit) {
        this.isValid = strLength <= this.limit;
      }

      this.$emit('validation', this.isValid);
    }

  }
});
// CONCATENATED MODULE: ./components/form/Editor.vue?vue&type=script&lang=js&
 /* harmony default export */ var form_Editorvue_type_script_lang_js_ = (Editorvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./components/form/Editor.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(937)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  form_Editorvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "0bb70d5d"
  
)

/* harmony default export */ var Editor = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 945:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/form/SearchInput.vue?vue&type=template&id=8bfec74e&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('v-form',{on:{"submit":function($event){$event.preventDefault();return _vm.submit.apply(null, arguments)}}},[_c('text-input',{class:['search-input', { 'search-input--small': _vm.small }],attrs:{"value":_vm.value,"type-class":"border-gradient","hide-details":"","disabled":_vm.disabled,"placeholder":_vm.$t(_vm.placeholder)},on:{"input":function($event){return _vm.$emit('input', $event)}},scopedSlots:_vm._u([{key:"append",fn:function(){return [_c('div',{staticStyle:{"margin-top":"6px","cursor":"pointer"},on:{"click":_vm.submit}},[_c('v-img',{attrs:{"src":__webpack_require__(506)}})],1)]},proxy:true}])})],1)}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/form/SearchInput.vue?vue&type=template&id=8bfec74e&

// EXTERNAL MODULE: ./components/form/TextInput.vue + 4 modules
var TextInput = __webpack_require__(102);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/form/SearchInput.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

/* harmony default export */ var SearchInputvue_type_script_lang_js_ = ({
  name: 'SearchInput',
  components: {
    TextInput: TextInput["default"]
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      required: true
    },
    small: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    submit() {
      this.$emit('submit');
    }

  }
});
// CONCATENATED MODULE: ./components/form/SearchInput.vue?vue&type=script&lang=js&
 /* harmony default export */ var form_SearchInputvue_type_script_lang_js_ = (SearchInputvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(896);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(64);

// CONCATENATED MODULE: ./components/form/SearchInput.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(953)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  form_SearchInputvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "86c5c87c"
  
)

/* harmony default export */ var SearchInput = __webpack_exports__["default"] = (component.exports);

/* vuetify-loader */



installComponents_default()(component, {VForm: VForm["a" /* default */],VImg: VImg["a" /* default */]})


/***/ }),

/***/ 953:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(931);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchInput_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 954:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".search-input .v-input{background-color:#fff;border-radius:50px!important;box-shadow:0 4px 14px rgba(217,225,236,.47)}@media only screen and (max-width:767px){.search-input .v-input{border-radius:10px!important}}.search-input .v-input input::-moz-placeholder{color:var(--v-greyLight-darken2)}.search-input .v-input input:-ms-input-placeholder{color:var(--v-greyLight-darken2)}.search-input .v-input input::placeholder{color:var(--v-greyLight-darken2)}.search-input .v-input .v-input__control>.v-input__slot{height:56px!important}@media only screen and (max-width:767px){.search-input .v-input .v-input__control>.v-input__slot{height:40px!important}}.search-input .v-input .v-input__append-inner{margin-top:9px!important}@media only screen and (max-width:767px){.search-input .v-input .v-input__append-inner{margin-top:6px!important}}.search-input .v-input .v-input__append-inner .v-image{width:26px!important;height:26px!important}@media only screen and (max-width:767px){.search-input .v-input .v-input__append-inner .v-image{width:20px!important;height:20px!important}}.search-input .v-input.v-input--is-focused .v-input__control>.v-input__slot:before{border-radius:16px!important}.search-input .v-input.v-input.v-text-field--outlined fieldset{border-color:transparent!important;box-shadow:0 4px 14px rgba(217,225,236,.47)}.search-input--inner-border .v-input .v-input__control>.v-input__slot{padding-top:5px!important;padding-left:5px!important;padding-bottom:5px!important}.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot{position:relative;padding:0 16px;background-color:transparent!important}@media only screen and (max-width:1215px){.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot{padding:0 12px}}@media only screen and (max-width:767px){.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot{padding:0 10px}}.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot:before{display:none!important;content:\"\";position:absolute;top:-1px;left:-1px;width:calc(100% + 2px);height:calc(100% + 2px);border-style:none;border-radius:15px;padding:1px;background:linear-gradient(126.15deg,var(--v-success-base),var(--v-primary-base) 102.93%);-webkit-mask:linear-gradient(#fff,#fff 0) content-box,linear-gradient(#fff,#fff 0);-webkit-mask:linear-gradient(#fff 0 0) content-box,linear-gradient(#fff 0 0);-webkit-mask-composite:destination-out;mask-composite:exclude;transform:none}@media only screen and (max-width:767px){.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot:before{border-radius:9px}}.search-input--inner-border .v-input .v-input__control>.v-input__slot>.v-text-field__slot input{position:relative;z-index:2}.search-input--inner-border .v-input .v-input__append-inner{margin-top:4px!important;padding-left:15px}@media only screen and (max-width:767px){.search-input--inner-border .v-input .v-input__append-inner{margin-top:0!important}}.search-input--inner-border .v-input.v-input--is-focused .v-input__control>.v-input__slot:before{display:none!important}.search-input--inner-border .v-input.v-input--is-focused .v-input__control>.v-input__slot>.v-text-field__slot:before{display:block!important}.search-input--small .v-input .v-input__control>.v-input__slot{height:44px!important}.search-input--small .v-input .v-input__append-inner{margin-top:6px!important}.search-input--small .v-input .v-input__append-inner .v-image{width:20px!important;height:20px!important}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 958:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(933);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_UserStatus_vue_vue_type_style_index_0_id_652352c7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ 959:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".user-status[data-v-652352c7]{width:16px;height:16px;border-radius:50%;border:2px solid #fff;background:#636363;z-index:2}.user-status--idle[data-v-652352c7]{background:linear-gradient(122.42deg,var(--v-redLight-base),var(--v-orangeLight2-base))}.user-status--online[data-v-652352c7]{background:var(--v-success-base)}.user-status--large[data-v-652352c7]{width:25px;height:25px}@media only screen and (max-width:991px){.user-status--large[data-v-652352c7]{width:23px;height:23px}}@media only screen and (max-width:639px){.user-status--large[data-v-652352c7]{width:21px;height:21px}}@media only screen and (max-width:479px){.user-status--large[data-v-652352c7]{width:19px;height:19px}}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 969:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(970);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
__webpack_require__(5).default("48751daa", content, true)

/***/ }),

/***/ 970:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(4);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(false);
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-expansion-panels .v-expansion-panel{background-color:#fff;color:rgba(0,0,0,.87)}.theme--light.v-expansion-panels .v-expansion-panel--disabled{color:rgba(0,0,0,.38)}.theme--light.v-expansion-panels .v-expansion-panel:not(:first-child):after{border-color:rgba(0,0,0,.12)}.theme--light.v-expansion-panels .v-expansion-panel-header .v-expansion-panel-header__icon .v-icon{color:rgba(0,0,0,.54)}.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:hover:before{opacity:.04}.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:before,.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:hover:before,.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:focus:before{opacity:.12}.theme--light.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:focus:before{opacity:.16}.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:hover:before{opacity:.04}.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:before,.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:hover:before,.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:focus:before{opacity:.12}.theme--light.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:focus:before{opacity:.16}.theme--dark.v-expansion-panels .v-expansion-panel{background-color:#1e1e1e;color:#fff}.theme--dark.v-expansion-panels .v-expansion-panel--disabled{color:hsla(0,0%,100%,.5)}.theme--dark.v-expansion-panels .v-expansion-panel:not(:first-child):after{border-color:hsla(0,0%,100%,.12)}.theme--dark.v-expansion-panels .v-expansion-panel-header .v-expansion-panel-header__icon .v-icon{color:#fff}.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:hover:before{opacity:.08}.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:before,.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:hover:before,.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header:focus:before{opacity:.24}.theme--dark.v-expansion-panels.v-expansion-panels--focusable .v-expansion-panel-header--active:focus:before{opacity:.32}.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:hover:before{opacity:.08}.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:before,.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:hover:before,.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover:focus:before{opacity:.24}.theme--dark.v-expansion-panels.v-expansion-panels--hover>.v-expansion-panel>.v-expansion-panel-header:hover--active:focus:before{opacity:.32}.v-expansion-panels{border-radius:8px;display:flex;flex-wrap:wrap;justify-content:center;list-style-type:none;padding:0;width:100%;z-index:1}.v-expansion-panels>*{cursor:auto}.v-expansion-panels>:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.v-expansion-panels>:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--active{border-radius:8px}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--active+.v-expansion-panel{border-top-left-radius:8px;border-top-right-radius:8px}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--next-active{border-bottom-left-radius:8px;border-bottom-right-radius:8px}.v-expansion-panels:not(.v-expansion-panels--accordion):not(.v-expansion-panels--tile)>.v-expansion-panel--next-active .v-expansion-panel-header{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.v-expansion-panel{flex:1 0 100%;max-width:100%;position:relative;transition:.3s cubic-bezier(.25,.8,.5,1)}.v-expansion-panel:before{border-radius:inherit;bottom:0;content:\"\";left:0;position:absolute;right:0;top:0;z-index:-1;transition:box-shadow .28s cubic-bezier(.4,0,.2,1);will-change:box-shadow;box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-expansion-panel:not(:first-child):after{border-top:thin solid;content:\"\";left:0;position:absolute;right:0;top:0;transition:border-color .2s cubic-bezier(.4,0,.2,1),opacity .2s cubic-bezier(.4,0,.2,1)}.v-expansion-panel--disabled .v-expansion-panel-header{pointer-events:none}.v-expansion-panel--active+.v-expansion-panel,.v-expansion-panel--active:not(:first-child){margin-top:16px}.v-expansion-panel--active+.v-expansion-panel:after,.v-expansion-panel--active:not(:first-child):after{opacity:0}.v-expansion-panel--active>.v-expansion-panel-header{min-height:64px}.v-expansion-panel--active>.v-expansion-panel-header--active .v-expansion-panel-header__icon:not(.v-expansion-panel-header__icon--disable-rotate) .v-icon{transform:rotate(-180deg)}.v-expansion-panel-header__icon{display:inline-flex;margin-bottom:-4px;margin-top:-4px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.v-application--is-ltr .v-expansion-panel-header__icon{margin-left:auto}.v-application--is-rtl .v-expansion-panel-header__icon{margin-right:auto}.v-expansion-panel-header{align-items:center;border-top-left-radius:inherit;border-top-right-radius:inherit;display:flex;font-size:.9375rem;line-height:1;min-height:64px;outline:none;padding:20px 24px;position:relative;transition:min-height .3s cubic-bezier(.25,.8,.5,1);width:100%}.v-application--is-ltr .v-expansion-panel-header{text-align:left}.v-application--is-rtl .v-expansion-panel-header{text-align:right}.v-expansion-panel-header:not(.v-expansion-panel-header--mousedown):focus:before{opacity:.12}.v-expansion-panel-header:before{background-color:currentColor;border-radius:inherit;bottom:0;content:\"\";left:0;opacity:0;pointer-events:none;position:absolute;right:0;top:0;transition:opacity .3s cubic-bezier(.25,.8,.5,1)}.v-expansion-panel-header>:not(.v-expansion-panel-header__icon){flex:1 1 auto}.v-expansion-panel-content{display:flex}.v-expansion-panel-content__wrap{padding:0 24px 20px;flex:1 1 auto;max-width:100%}.v-expansion-panels--accordion>.v-expansion-panel{margin-top:0}.v-expansion-panels--accordion>.v-expansion-panel:after{opacity:1}.v-expansion-panels--popout>.v-expansion-panel{max-width:calc(100% - 32px)}.v-expansion-panels--popout>.v-expansion-panel--active{max-width:calc(100% + 16px)}.v-expansion-panels--inset>.v-expansion-panel{max-width:100%}.v-expansion-panels--inset>.v-expansion-panel--active{max-width:calc(100% - 32px)}.v-expansion-panels--flat>.v-expansion-panel:after{border-top:none}.v-expansion-panels--flat>.v-expansion-panel:before{box-shadow:0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)}.v-expansion-panels--tile,.v-expansion-panels--tile>.v-expansion-panel:before{border-radius:0}", ""]);
// Exports
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 982:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1042);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add CSS to SSR context
var add = __webpack_require__(5).default
module.exports.__inject__ = function (context) {
  add("f203485e", content, true, context)
};

/***/ }),

/***/ 985:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/ConfirmDialog.vue?vue&type=template&id=2a649283&
var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.isShownConfirmDialog)?_c('l-dialog',_vm._g({attrs:{"dialog":_vm.isShownConfirmDialog,"hide-close-button":"","max-width":"418","custom-class":"remove-illustration text-center"}},_vm.$listeners),[_c('div',[_c('div',{staticClass:"remove-illustration-title font-weight-medium"},[_vm._v("\n      "+_vm._s(_vm.$t('are_you_sure'))+"\n    ")]),_vm._v(" "),_c('div',{staticClass:"mt-2"},[_vm._t("default")],2),_vm._v(" "),_c('div',{staticClass:"d-flex justify-space-around justify-sm-space-between flex-wrap mt-2"},[_c('v-btn',{staticClass:"gradient font-weight-medium my-1",on:{"click":function($event){return _vm.$emit('close-dialog')}}},[_c('div',{staticClass:"text--gradient"},[_vm._v("\n          "+_vm._s(_vm.$t(_vm.cancelTextButton))+"\n        ")])]),_vm._v(" "),_c('v-btn',{staticClass:"font-weight-medium my-1",attrs:{"color":"primary"},on:{"click":function($event){return _vm.$emit('confirm')}}},[_vm._v("\n        "+_vm._s(_vm.$t(_vm.confirmTextButton))+"\n      ")])],1)])]):_vm._e()}
var staticRenderFns = []


// CONCATENATED MODULE: ./components/ConfirmDialog.vue?vue&type=template&id=2a649283&

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/ConfirmDialog.vue?vue&type=script&lang=js&
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
/* harmony default export */ var ConfirmDialogvue_type_script_lang_js_ = ({
  name: 'ConfirmDialog',
  props: {
    isShownConfirmDialog: {
      type: Boolean,
      required: true
    },
    cancelTextButton: {
      type: String,
      default: 'close'
    },
    confirmTextButton: {
      type: String,
      default: 'confirm'
    }
  }
});
// CONCATENATED MODULE: ./components/ConfirmDialog.vue?vue&type=script&lang=js&
 /* harmony default export */ var components_ConfirmDialogvue_type_script_lang_js_ = (ConfirmDialogvue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify-loader/lib/runtime/installComponents.js
var installComponents = __webpack_require__(8);
var installComponents_default = /*#__PURE__*/__webpack_require__.n(installComponents);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(852);

// CONCATENATED MODULE: ./components/ConfirmDialog.vue



function injectStyles (context) {
  
  var style0 = __webpack_require__(1041)
if (style0.__inject__) style0.__inject__(context)

}

/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  components_ConfirmDialogvue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  injectStyles,
  null,
  "33ddf780"
  
)

/* harmony default export */ var ConfirmDialog = __webpack_exports__["default"] = (component.exports);

/* nuxt-component-imports */
installComponents_default()(component, {LDialog: __webpack_require__(28).default})


/* vuetify-loader */


installComponents_default()(component, {VBtn: VBtn["a" /* default */]})


/***/ }),

/***/ 992:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: external "core-js/modules/esnext.set.add-all.js"
var esnext_set_add_all_js_ = __webpack_require__(836);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.delete-all.js"
var esnext_set_delete_all_js_ = __webpack_require__(837);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.difference.js"
var esnext_set_difference_js_ = __webpack_require__(838);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.every.js"
var esnext_set_every_js_ = __webpack_require__(839);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.filter.js"
var esnext_set_filter_js_ = __webpack_require__(840);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.find.js"
var esnext_set_find_js_ = __webpack_require__(841);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.intersection.js"
var esnext_set_intersection_js_ = __webpack_require__(842);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.is-disjoint-from.js"
var esnext_set_is_disjoint_from_js_ = __webpack_require__(843);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.is-subset-of.js"
var esnext_set_is_subset_of_js_ = __webpack_require__(844);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.is-superset-of.js"
var esnext_set_is_superset_of_js_ = __webpack_require__(845);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.join.js"
var esnext_set_join_js_ = __webpack_require__(846);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.map.js"
var esnext_set_map_js_ = __webpack_require__(847);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.reduce.js"
var esnext_set_reduce_js_ = __webpack_require__(848);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.some.js"
var esnext_set_some_js_ = __webpack_require__(849);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.symmetric-difference.js"
var esnext_set_symmetric_difference_js_ = __webpack_require__(850);

// EXTERNAL MODULE: external "core-js/modules/esnext.set.union.js"
var esnext_set_union_js_ = __webpack_require__(851);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./mixins/StatusOnline.vue?vue&type=script&lang=js&
















/* harmony default export */ var StatusOnlinevue_type_script_lang_js_ = ({
  data() {
    return {
      timeoutId: null,
      userStatuses: {},
      arrStatusId: []
    };
  },

  computed: {
    preparedArr() {
      return [...new Set(this.arrStatusId)];
    }

  },

  mounted() {
    this.timeoutId = window.setInterval(() => {
      this.refreshStatusOnline();

      if (!this.arrStatusId.length) {
        this.clearInterval();
      }
    }, 10000);
  },

  beforeDestroy() {
    if (this.timeoutId) {
      this.clearInterval();
    }
  },

  methods: {
    refreshStatusOnline() {
      if (this.arrStatusId.length) {
        this.$store.dispatch('user/refreshStatusOnline', this.preparedArr).then(res => this.userStatuses = res);
      }
    },

    clearInterval() {
      window.clearInterval(this.timeoutId);
      this.timeoutId = null;
    }

  }
});
// CONCATENATED MODULE: ./mixins/StatusOnline.vue?vue&type=script&lang=js&
 /* harmony default export */ var mixins_StatusOnlinevue_type_script_lang_js_ = (StatusOnlinevue_type_script_lang_js_); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(6);

// CONCATENATED MODULE: ./mixins/StatusOnline.vue
var render, staticRenderFns




/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  mixins_StatusOnlinevue_type_script_lang_js_,
  render,
  staticRenderFns,
  false,
  null,
  null,
  "2b0aab01"
  
)

/* harmony default export */ var StatusOnline = __webpack_exports__["a"] = (component.exports);

/***/ })

};;
//# sourceMappingURL=index.js.map